# Modern AI Pro - Lead Management System


## Business Context
**Modern AI Pro** is a proven AI education business scaling from **1,500 to 10,000 students/year**. After 2+ years of operations and **2,500+ graduates**, we're scaling lead processing from **50-60 leads/day to 400 leads/day** while maintaining our proven **6-7% conversion rate**. The system manages Facebook Lead Ads → Workshop Enrollment → Alumni Network pipeline with $320 USD/₹15k INR workshops.

📁 **Business Details**: `new/docs/1. overview/bigger_vision.md`  
📁 **Product Requirements**: `new/docs/2. product_specs/dashboard_prd.md`

## Technology Stack
NOTE: To run the python scripts, make sure you use the venv found in new/scripts/

### Database (V2 Schema - Recently Migrated)
- **Database**: SQLite (production-ready, upgradeable to PostgreSQL)
- **Location**: `new/dashboard/database/leads.db`
- **Schema**: V2 customer-centric with multi-email support
- **Key Tables**: `leads`, `customers`, `payments`, `workshops`, `workshop_enrollments`, `customer_emails`
- **Migration Scripts**: `new/scripts/` (use only python for scripts)

### Server Application (Next.js)
- **Framework**: Next.js with React
- **Location**: `new/dashboard/` 
- **API Routes**: Next.js API routes for backend logic
- **Styling**: Tailwind CSS
- **Deployment**: Azure Virtual Machine

### Automation Scripts (Python)
- **Location**: `new/scripts/facebook/`, `new/scripts/email/`, `new/scripts/certificates/`
- **Purpose**: Facebook Lead Ads integration, email automation, certificate generation
- **Language**: Python (NOT Node.js - keep separate from server)
- **Scheduling**: Cron jobs for automated lead pulling and processing

## Key Business Logic

### Lead Conversion Funnel (6-7% Target)
1. **New** → Facebook Lead Ads capture
2. **Auto-Responded** → Instant email + SMS (<5 minutes)
3. **Contacted** → Sales call (3-5 minutes)
4. **Qualified** → Meets senior tech professional criteria
5. **Demo Scheduled** → Workshop demo call
6. **Demo Completed** → Workshop explained
7. **Enrolled** → Payment processed ($320 USD/₹15k INR)
8. **Workshop Complete** → 2.5-day weekend workshop
9. **Alumni Network** → Certificate + community access
10. **Subscription Prospect** → Future $500/3-month upsell

### Workshop Types
- AI Essentials, AI Practitioner, Agentic AI, Vibe Coding, AI for PMs, AI for UX
- 2.5 days over weekends, 2 workshops/month each

## Database Schema (V2)

### Core Relationships
```
LEADS (1,441 total) → CUSTOMERS (702 with purchases) → PAYMENTS (32 completed)
CUSTOMERS ←→ CUSTOMER_EMAILS (multi-email support)
CUSTOMERS → WORKSHOP_ENROLLMENTS → WORKSHOPS
```

### Key V2 Features
- **Multi-email handling**: Customers use different emails for leads, payments, communications
- **Payment reconciliation**: Links Stripe payments to correct customer records  
- **Workshop operations**: Bulk email management, certificate tracking
- **Subscription ready**: Infrastructure for upcoming subscription model

## Critical File Locations

### Documentation
- `new/docs/1. overview/` - Business strategy and vision
- `new/docs/2. product_specs/` - Technical requirements
- `new/docs/3. technical_implementation/` - Database UML, architecture

### Server Code (Next.js)
- `new/dashboard/` - Main Next.js application
- `new/dashboard/database/` - Database files and schema

### Python Automation Scripts
- `new/scripts/facebook/` - Facebook Lead Ads integration
- `new/scripts/email/` - Email automation sequences  
- `new/scripts/certificates/` - Certificate generation
- `new/scripts/data_migration/` - Database migration tools (Node.js)

## Development Guidelines

### Technology Separation
- **Python Scripts**: Data processing, Facebook API, email automation, certificate generation
- **Next.js Server**: Dashboard UI, API routes, user management, real-time analytics
- **Database**: SQLite with V2 schema, direct SQL queries for complex analytics

### Key Metrics to Track
- **Lead Volume**: Current 50-60/day → Target 400/day
- **Conversion Rate**: Maintain 6-7% (proven rate)
- **Response Time**: <5 minutes for first contact
- **Customer LTV**: Average $500 per customer
- **Geographic Split**: US ($320) vs India (₹15k) vs Middle East

## Recent Changes (2025-08-07)
- ✅ **Database Migration Complete**: Migrated to V2 customer-centric schema
- ✅ **Multi-email Support**: Added `customer_emails` table for payment reconciliation
- ✅ **Data Integrity**: All 1,441 leads and 32 payments properly migrated
- ✅ **Business Intelligence**: Added V2 views for customer analytics

## Common Tasks

### Database Operations
```bash
# Access database
sqlite3 new/dashboard/database/leads.db

# Run validation
node new/scripts/migration_validator.js
```

### Development Commands
```bash
# Start Next.js dashboard
cd new/dashboard && npm run dev

# Run Python scripts
cd new/scripts/facebook && python fetch_leads.py
```

## Business Priorities

### Immediate (Q3 2024)
- Scale lead processing to 400/day
- Maintain 6-7% conversion rate
- Implement <5 minute response automation
- Launch subscription model ($500/3-month)

### Strategic Decision Point (October 2025)
- If reaching 250+ learners/month → Focus on B2C platform scaling
- If below 250 → Focus on B2B enterprise Kapi opportunity
- Leverage 2,500+ alumni network for growth

## Security & Compliance
- No secrets in code repositories
- Environment variables for API keys
- HTTPS encryption for all communications
- Daily database backups
- Azure VM deployment with proper security groups

The scripts are in python and the frontend is in nextjs (/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard). It is very likely that the user is already running npm run dev and if you want to test the node directly test against the 3000 port.

---

**Last Updated**: 2025-08-07  
**Database Schema Version**: V2.0  
**Migration Status**: Complete ✅