{"permissions": {"allow": ["<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python3:*)", "mcp__ide__executeCode", "<PERSON><PERSON>(python:*)", "Bash(ls:*)", "Bash(pip3 install:*)", "Bash(find:*)", "Bash(cp:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "<PERSON><PERSON>(touch:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(cat:*)", "Bash(grep:*)", "<PERSON><PERSON>(timeout:*)", "Bash(./setup_cron_jobs.sh:*)", "<PERSON><PERSON>(mv:*)", "Bash(./venv/bin/python:*)", "WebFetch(domain:colab.research.google.com)", "WebFetch(domain:drive.google.com)", "Bash(gdown:*)", "WebFetch(domain:docs.google.com)", "Bash(node:*)", "Bash(../venv/bin/python:*)", "Bash(tree:*)", "<PERSON><PERSON>(pkill:*)", "Bash(wc:*)", "<PERSON><PERSON>(tail:*)", "Bash(npm install:*)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:learn.microsoft.com)", "WebFetch(domain:localhost)", "Bash(ssh:*)", "<PERSON><PERSON>(scp:*)", "Bash(pm2 logs:*)", "Bash(/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/venv/bin/python --version)", "Bash(git rm:*)", "Bash(git add:*)", "Bash(git add:*)", "<PERSON><PERSON>(git mv:*)", "Bash(npm run build:*)", "Bash(for:*)", "Bash(do sed -i.bak 's|import Layout from.*components/Layout.*;|import Layout from \"\"../../components/layout/Layout\"\";|g' \"$file\")", "<PERSON><PERSON>(echo:*)", "Bash(done)", "<PERSON><PERSON>(sed:*)", "Bash(do sed -i '' 's|const User = require.*User.*|// &  // TODO: Create User model|' \"$file\")", "WebFetch(domain:mitrarobot.com)", "<PERSON><PERSON>(crontab:*)", "Bash(/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/integrations/facebook/cron_rate_limited_leads.sh)", "<PERSON><PERSON>(launchctl:*)", "<PERSON><PERSON>(sudo launchctl:*)", "Bash(npm run dev:*)", "Bash(./scripts/venv/bin/python:*)", "Bash(npm test:*)", "Bash(npm run test:coverage:*)", "Bash(../venv/bin/pip install:*)", "Bash(git push:*)", "Bash(git pull:*)", "Bash(git init:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(git commit:*)", "WebSearch", "Bash(PYTHONPATH=/Users/<USER>/Code/modernaipro/mai-administrative/new/docs/8-courses/Practitioner/Pod1/backend python3 backend/app.py)", "Bash(npm start)", "Bash(git init:*)", "Bash(git remote add:*)", "Bash(git commit:*)", "Bash(gh repo create:*)", "Bash(git remote add:*)", "WebFetch(domain:github.com)"], "deny": [], "defaultMode": "acceptEdits"}}