# Environment variables
.env
*.env.*
.env.local
.env.development
.env.production
.envrc
*scratchpad.md

# Dependencies
node_modules/
__pycache__/
*.py[cod]
*$py.class
*.pyc
venv/
ENV/
env/
.venv
env.bak/
venv.bak/
fb_env/

# Database files - NEVER commit production data
*.sqlite
*.sqlite3
*.db
*.db-shm
*.db-wal
*.db-journal
db.sqlite3
db.sqlite3-journal
leads.db
database/*.db
*/database/*.db
**/database/*.db

# Logs
*.log
logs/
pip-log.txt
pip-delete-this-directory.txt

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
.spyderproject
.spyproject
.ropeproject

# Build outputs
build/
dist/
.next/
target/
develop-eggs/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing and coverage
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py.cover
.hypothesis/
.pytest_cache/
cover/

# Temporary files
temp/
tmp/
*.tmp
.scrapy

# CSV exports and data files
leads_*.json
consolidated_*.csv

# Certificates and sensitive assets
modernaipro-*.json

# Python virtual environments
bin/
include/
pyvenv.cfg
__pypackages__/

# Package management
#Pipfile.lock
#uv.lock
#poetry.lock
#poetry.toml
#pdm.lock
#pdm.toml
.pdm-python
.pdm-build/
#pixi.lock
.pixi

# Documentation
docs/_build/
/site

# Type checking and analysis
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/
.ruff_cache/

# Other Python tools
cython_debug/
celerybeat-schedule
celerybeat.pid
*.sage.py
profile_default/
ipython_config.py
.ipynb_checkpoints

# Specific tools and services
.abstra/
.cursorignore
.cursorindexingignore
marimo/_static/
marimo/_lsp/
__marimo__/
.pypirc

# PyInstaller
*.manifest
*.spec

# Translations
*.mo
*.pot

# Django/Flask
local_settings.py
instance/
.webassets-cache

# C extensions
*.so

# OS specific
.Python
