"""adding workshop

Revision ID: 12690fb1f33a
Revises: 60e03f32ec28
Create Date: 2025-03-09 13:11:37.549597

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '12690fb1f33a'
down_revision: Union[str, None] = '60e03f32ec28'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # First create the workshops table
    op.create_table('workshops',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=False),
        sa.Column('end_date', sa.DateTime(), nullable=False),
        sa.Column('geography', sa.String(), nullable=True),
        sa.Column('payment_link', sa.String(), nullable=True),
        sa.Column('cost', sa.Float(), nullable=True),
        sa.Column('max_participants', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_workshops_id'), 'workshops', ['id'], unique=False)
    
    # Create the workshop_participants association table
    op.create_table('workshop_participants',
        sa.Column('workshop_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['workshop_id'], ['workshops.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('workshop_id', 'user_id')
    )
    
    # Create the workshop_payment_history table
    op.create_table('workshop_payment_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('workshop_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('amount', sa.Float(), nullable=False),
        sa.Column('payment_date', sa.DateTime(), nullable=True),
        sa.Column('payment_method', sa.String(), nullable=False),
        sa.Column('transaction_id', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=False),
        sa.ForeignKeyConstraint(['workshop_id'], ['workshops.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('transaction_id')
    )
    op.create_index(op.f('ix_workshop_payment_history_id'), 'workshop_payment_history', ['id'], unique=False)
    
    # Now add the syllabus_link column
    op.add_column('workshops', sa.Column('syllabus_link', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('workshops', 'syllabus_link')
    op.drop_index(op.f('ix_workshop_payment_history_id'), table_name='workshop_payment_history')
    op.drop_table('workshop_payment_history')
    op.drop_table('workshop_participants')
    op.drop_index(op.f('ix_workshops_id'), table_name='workshops')
    op.drop_table('workshops')
    # ### end Alembic commands ###