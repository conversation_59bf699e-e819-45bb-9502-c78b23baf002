"""Initial tables

Revision ID: 1c9438160b46
Revises: 
Create Date: 2025-03-08 12:30:29.195735

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '1c9438160b46'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('learning_pods',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('active', sa.<PERSON>(), nullable=True),
    sa.Column('achievement_points', sa.Integer(), nullable=True),
    sa.Column('challenges_completed', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_learning_pods_id'), 'learning_pods', ['id'], unique=False)
    op.create_table('lesson_categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('level', sa.Integer(), nullable=False),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['lesson_categories.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_lesson_categories_id'), 'lesson_categories', ['id'], unique=False)
    op.create_table('onboarding_states',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('explanation', sa.Text(), nullable=False),
    sa.Column('video_link', sa.String(length=255), nullable=True),
    sa.Column('prompt_to_llm', sa.Text(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.Column('estimated_time', sa.Integer(), nullable=True),
    sa.Column('resources', sa.JSON(), nullable=True),
    sa.Column('subscription_type', sa.Enum('BASIC', 'PREMIUM', 'REQUIRED', name='onboardingtype'), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('unlock_feature', sa.String(length=255), nullable=True),
    sa.Column('skill_category', sa.String(length=100), nullable=True),
    sa.Column('elo_points', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_onboarding_states_id'), 'onboarding_states', ['id'], unique=False)
    op.create_table('programming_challenges',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('difficulty', sa.Enum('BEGINNER', 'INTERMEDIATE', 'ADVANCED', name='difficultylevel'), nullable=True),
    sa.Column('requirements', sa.Text(), nullable=False),
    sa.Column('starter_code', sa.Text(), nullable=True),
    sa.Column('solution_template', sa.Text(), nullable=True),
    sa.Column('test_cases', sa.JSON(), nullable=False),
    sa.Column('elo_points', sa.Integer(), nullable=True),
    sa.Column('category', sa.String(), nullable=False),
    sa.Column('estimated_time', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_programming_challenges_id'), 'programming_challenges', ['id'], unique=False)
    op.create_table('quiz_questions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('category', sa.String(), nullable=True),
    sa.Column('difficulty', sa.Integer(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('question', sa.String(), nullable=True),
    sa.Column('choices', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('correct_answer', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('question')
    )
    op.create_index(op.f('ix_quiz_questions_category'), 'quiz_questions', ['category'], unique=False)
    op.create_index(op.f('ix_quiz_questions_id'), 'quiz_questions', ['id'], unique=False)
    op.create_index(op.f('ix_quiz_questions_type'), 'quiz_questions', ['type'], unique=False)
    op.create_table('surveys',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('surveyName', sa.String(), nullable=True),
    sa.Column('surveyType', sa.String(), nullable=True),
    sa.Column('question', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('responses', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_surveys_id'), 'surveys', ['id'], unique=False)
    op.create_index(op.f('ix_surveys_surveyName'), 'surveys', ['surveyName'], unique=False)
    op.create_table('lessons',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.Column('llm_prompt', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['lesson_categories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_lessons_id'), 'lessons', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('hashed_password', sa.String(), nullable=True),
    sa.Column('role', sa.Enum('student', 'teacher', 'admin', 'bd', name='userroles'), nullable=True),
    sa.Column('subscription_tier', sa.Enum('free', 'basic', 'premium', name='subscriptiontier'), nullable=True),
    sa.Column('subscription_start_date', sa.DateTime(), nullable=True),
    sa.Column('subscription_end_date', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('elo_rating', sa.Integer(), nullable=True),
    sa.Column('skills_graph', sa.JSON(), nullable=True),
    sa.Column('total_challenges_completed', sa.Integer(), nullable=True),
    sa.Column('streak_count', sa.Integer(), nullable=True),
    sa.Column('last_active_date', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('pod_id', sa.Integer(), nullable=True),
    sa.Column('referred_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['pod_id'], ['learning_pods.id'], ),
    sa.ForeignKeyConstraint(['referred_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('learning_pod_members',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('pod_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('joined_at', sa.DateTime(), nullable=True),
    sa.Column('is_admin', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['pod_id'], ['learning_pods.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_learning_pod_members_id'), 'learning_pod_members', ['id'], unique=False)
    op.create_table('lesson_contents',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('lesson_id', sa.Integer(), nullable=False),
    sa.Column('content_type', sa.Enum('text', 'image', 'video', 'code', 'audio', name='contenttype'), nullable=False),
    sa.Column('text_content', sa.Text(), nullable=True),
    sa.Column('content_url', sa.String(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['lesson_id'], ['lessons.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_lesson_contents_id'), 'lesson_contents', ['id'], unique=False)
    op.create_table('payment_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('payment_date', sa.DateTime(), nullable=True),
    sa.Column('subscription_tier', sa.Enum('free', 'basic', 'premium', name='subscriptiontier'), nullable=False),
    sa.Column('payment_method', sa.String(), nullable=False),
    sa.Column('transaction_id', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('transaction_id')
    )
    op.create_index(op.f('ix_payment_history_id'), 'payment_history', ['id'], unique=False)
    op.create_table('programming_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('challenge_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('partner_id', sa.Integer(), nullable=True),
    sa.Column('is_ai_partner', sa.Boolean(), nullable=True),
    sa.Column('start_time', sa.DateTime(), nullable=True),
    sa.Column('end_time', sa.DateTime(), nullable=True),
    sa.Column('completed', sa.Boolean(), nullable=True),
    sa.Column('code_solution', sa.Text(), nullable=True),
    sa.Column('performance_score', sa.Float(), nullable=True),
    sa.Column('time_spent', sa.Integer(), nullable=True),
    sa.Column('lines_of_code', sa.Integer(), nullable=True),
    sa.Column('test_cases_passed', sa.Integer(), nullable=True),
    sa.Column('scheduled_time', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.ForeignKeyConstraint(['challenge_id'], ['programming_challenges.id'], ),
    sa.ForeignKeyConstraint(['partner_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_programming_sessions_id'), 'programming_sessions', ['id'], unique=False)
    op.create_table('quiz_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('current_question_index', sa.Integer(), nullable=True),
    sa.Column('total_score', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('time_taken', sa.Interval(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_quiz_sessions_id'), 'quiz_sessions', ['id'], unique=False)
    op.create_table('referrals',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('referrer_id', sa.Integer(), nullable=False),
    sa.Column('referred_id', sa.Integer(), nullable=False),
    sa.Column('referral_code', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('referrer_reward_given', sa.Boolean(), nullable=True),
    sa.Column('referred_reward_given', sa.Boolean(), nullable=True),
    sa.Column('reward_type', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['referred_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['referrer_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_referrals_id'), 'referrals', ['id'], unique=False)
    op.create_index(op.f('ix_referrals_referral_code'), 'referrals', ['referral_code'], unique=False)
    op.create_table('user_lessons',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('lesson_id', sa.Integer(), nullable=False),
    sa.Column('completed', sa.Boolean(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['lesson_id'], ['lessons.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_lessons_id'), 'user_lessons', ['id'], unique=False)
    op.create_table('user_onboarding_states',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('onboarding_state_id', sa.Integer(), nullable=False),
    sa.Column('completion_status', sa.Boolean(), nullable=True),
    sa.Column('start_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('completion_date', sa.DateTime(), nullable=True),
    sa.Column('attempts', sa.Integer(), nullable=True),
    sa.Column('last_activity', sa.DateTime(), nullable=True),
    sa.Column('completed_with_partner_id', sa.Integer(), nullable=True),
    sa.Column('achievement_unlocked', sa.Boolean(), nullable=True),
    sa.Column('feedback_rating', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['completed_with_partner_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['onboarding_state_id'], ['onboarding_states.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_onboarding_states_id'), 'user_onboarding_states', ['id'], unique=False)
    op.create_table('elo_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('previous_rating', sa.Integer(), nullable=False),
    sa.Column('new_rating', sa.Integer(), nullable=False),
    sa.Column('change', sa.Integer(), nullable=False),
    sa.Column('reason', sa.String(), nullable=False),
    sa.Column('programming_session_id', sa.Integer(), nullable=True),
    sa.Column('opponent_id', sa.Integer(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['opponent_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['programming_session_id'], ['programming_sessions.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_elo_history_id'), 'elo_history', ['id'], unique=False)
    op.create_table('user_answers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('quiz_session_id', sa.Integer(), nullable=False),
    sa.Column('question_id', sa.Integer(), nullable=False),
    sa.Column('user_response', sa.Text(), nullable=True),
    sa.Column('correct', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['question_id'], ['quiz_questions.id'], ),
    sa.ForeignKeyConstraint(['quiz_session_id'], ['quiz_sessions.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_answers_id'), 'user_answers', ['id'], unique=False)
    op.create_table('user_lesson_issues',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_lesson_id', sa.Integer(), nullable=False),
    sa.Column('issue_description', sa.Text(), nullable=False),
    sa.Column('resolved', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('resolved_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_lesson_id'], ['user_lessons.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_lesson_issues_id'), 'user_lesson_issues', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_lesson_issues_id'), table_name='user_lesson_issues')
    op.drop_table('user_lesson_issues')
    op.drop_index(op.f('ix_user_answers_id'), table_name='user_answers')
    op.drop_table('user_answers')
    op.drop_index(op.f('ix_elo_history_id'), table_name='elo_history')
    op.drop_table('elo_history')
    op.drop_index(op.f('ix_user_onboarding_states_id'), table_name='user_onboarding_states')
    op.drop_table('user_onboarding_states')
    op.drop_index(op.f('ix_user_lessons_id'), table_name='user_lessons')
    op.drop_table('user_lessons')
    op.drop_index(op.f('ix_referrals_referral_code'), table_name='referrals')
    op.drop_index(op.f('ix_referrals_id'), table_name='referrals')
    op.drop_table('referrals')
    op.drop_index(op.f('ix_quiz_sessions_id'), table_name='quiz_sessions')
    op.drop_table('quiz_sessions')
    op.drop_index(op.f('ix_programming_sessions_id'), table_name='programming_sessions')
    op.drop_table('programming_sessions')
    op.drop_index(op.f('ix_payment_history_id'), table_name='payment_history')
    op.drop_table('payment_history')
    op.drop_index(op.f('ix_lesson_contents_id'), table_name='lesson_contents')
    op.drop_table('lesson_contents')
    op.drop_index(op.f('ix_learning_pod_members_id'), table_name='learning_pod_members')
    op.drop_table('learning_pod_members')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_lessons_id'), table_name='lessons')
    op.drop_table('lessons')
    op.drop_index(op.f('ix_surveys_surveyName'), table_name='surveys')
    op.drop_index(op.f('ix_surveys_id'), table_name='surveys')
    op.drop_table('surveys')
    op.drop_index(op.f('ix_quiz_questions_type'), table_name='quiz_questions')
    op.drop_index(op.f('ix_quiz_questions_id'), table_name='quiz_questions')
    op.drop_index(op.f('ix_quiz_questions_category'), table_name='quiz_questions')
    op.drop_table('quiz_questions')
    op.drop_index(op.f('ix_programming_challenges_id'), table_name='programming_challenges')
    op.drop_table('programming_challenges')
    op.drop_index(op.f('ix_onboarding_states_id'), table_name='onboarding_states')
    op.drop_table('onboarding_states')
    op.drop_index(op.f('ix_lesson_categories_id'), table_name='lesson_categories')
    op.drop_table('lesson_categories')
    op.drop_index(op.f('ix_learning_pods_id'), table_name='learning_pods')
    op.drop_table('learning_pods')
    # ### end Alembic commands ###
