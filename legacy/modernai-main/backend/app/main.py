import os
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import text
from starlette.middleware.sessions import SessionMiddleware

from app.database import get_db, Base, engine
from app.routes import auth, login
from app.admin import setup_admin

# Create database tables
Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(title="Modern AI Pro API")

# Add session middleware for admin authentication
app.add_middleware(
    SessionMiddleware, 
    secret_key=os.getenv("SECRET_KEY", "your-secret-key")
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3001"],  # React frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup SQLAdmin
admin = setup_admin(app)

# Include routers
app.include_router(auth.router, prefix="/auth")
app.include_router(login.router)

@app.get("/")
async def root():
    return {"message": "Welcome to Modern AI Pro API"}

@app.get("/health")
async def health_check(db: Session = Depends(get_db)):
    try:
        db.execute(text("SELECT 1")) 
        return {"status": "healthy"}
    except:
        raise HTTPException(status_code=500, detail="Database connection error")

# Run with: uvicorn main:app --reload
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)