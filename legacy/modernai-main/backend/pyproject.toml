[tool.poetry]
name = "modern-ai-pro"
version = "0.1.0"
description = "Modern AI Pro learning platform backend"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.109.2"
sqlalchemy = "^2.0.27"
pydantic = "^2.6.1"
jinja2 = "^3.1.3"
python-jose = {extras = ["cryptography"], version = "^3.4.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
bcrypt = "4.0.1"
python-multipart = "^0.0.7"
alembic = "^1.13.1"
email-validator = "^2.1.0.post1"
python-dotenv = "^1.0.1"
psycopg2 = "^2.9.10"
uvicorn = "^0.34.0"
sqladmin = "^0.20.1"
itsdangerous = "^2.2.0"
requests = "^2.32.3"
pyjwt = "^2.10.1"
langchain-openai = "^0.3.8"
langchain-groq = "^0.2.5"
numpy = "^2.2.3"
chromadb = "^0.6.3"

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"