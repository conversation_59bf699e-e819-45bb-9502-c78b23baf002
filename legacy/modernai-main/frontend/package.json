{"name": "modern-ai-pro", "version": "0.1.0", "private": true, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tsparticles/preset-confetti": "^3.2.0", "axios": "^1.6.7", "chart.js": "^4.4.1", "chartjs-chart-wordcloud": "^4.3.3", "framer-motion": "^10.16.5", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-player": "^2.14.1", "react-router-dom": "^6.20.0", "react-scripts": "5.0.1", "react-tsparticles": "^2.12.2", "tsparticles": "^3.8.1", "tsparticles-preset-confetti": "^2.12.0", "tsparticles-slim": "^2.12.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}