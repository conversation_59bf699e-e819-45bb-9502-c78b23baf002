from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
import os
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv
from typing import Optional, List

# Add the scripts directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'scripts', 'crm'))

from fb_retrieve_leads import fetch_leads
from fb_campaign_stats import fetch_metrics, get_time_range

load_dotenv()

app = FastAPI(title="MAI CRM API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Authentication
security = HTTPBearer()
API_KEY = os.getenv("INTERNAL_API_KEY", "your-secret-api-key")

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if credentials.credentials != API_KEY:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return credentials.credentials

@app.get("/health")
def health_check():
    return {"status": "healthy"}

@app.get("/api/leads/recent")
def get_recent_leads(token: str = Depends(verify_token)):
    try:
        leads = fetch_leads()
        return {
            "status": "success",
            "count": len(leads),
            "leads": leads
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/campaigns/stats")
def get_campaign_stats(
    days: int = Query(7, description="Number of days to look back"),
    detailed: bool = Query(False, description="Include detailed campaign breakdown"),
    token: str = Depends(verify_token)
):
    try:
        time_range = get_time_range(days)
        stats = fetch_metrics(time_range, detailed)
        
        return {
            "status": "success",
            "time_range": f"Last {days} days",
            "stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/leads/filtered")
def get_filtered_leads(
    city: Optional[str] = Query(None, description="Filter by city"),
    ai_experience: Optional[str] = Query(None, description="Filter by AI experience level"),
    token: str = Depends(verify_token)
):
    try:
        leads = fetch_leads()
        
        # Apply filters
        if city:
            leads = [lead for lead in leads if city.lower() in lead.get('city', '').lower()]
        
        if ai_experience:
            leads = [lead for lead in leads if ai_experience.lower() in lead.get('ai_experience', '').lower()]
        
        return {
            "status": "success",
            "count": len(leads),
            "filters_applied": {
                "city": city,
                "ai_experience": ai_experience
            },
            "leads": leads
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv('PORT', 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)