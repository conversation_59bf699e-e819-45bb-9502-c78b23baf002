import re

def is_valid_phone_number(phone):
    """Validates if the given string is a valid phone number.
    
    Args:
        phone (str): The phone number string to validate
        
    Returns:
        bool: True if valid phone number, False otherwise
    """
    if not phone:  # Handle None or empty string
        return False
        
    # First clean the phone number
    cleaned = re.sub(r'\D', '', str(phone))  # Remove all non-digits
    
    # Valid formats:
    # 1. 10 digits (Indian mobile without country code)
    # 2. 12 digits starting with '91' (Indian mobile with country code)
    # 3. 12 digits starting with '971' (UAE mobile with country code)
    # 4. Other international formats (11-15 digits)
    
    # Check if it's exactly 10 digits (Indian mobile without country code)
    if len(cleaned) == 10:
        # Validate first digit is 6-9 for Indian mobile numbers
        if cleaned[0] in ['6', '7', '8', '9']:
            return True
    
    # Check if it's exactly 12 digits and starts with '91' (India country code)
    elif len(cleaned) == 12 and cleaned.startswith('91'):
        # Validate the digit after '91' is 6-9 for Indian mobile numbers
        if cleaned[2] in ['6', '7', '8', '9']:
            return True
    
    # Check if it's exactly 12 digits and starts with '971' (UAE country code)
    elif len(cleaned) == 12 and cleaned.startswith('971'):
        # UAE mobile numbers typically start with 5 after country code
        if cleaned[3] in ['5']:
            return True
    
    # Check for other international formats (11-15 digits)
    elif 11 <= len(cleaned) <= 15:
        # Basic validation for international numbers
        # Must start with a valid country code (1-999)
        if cleaned[0] != '0':  # Country codes don't start with 0
            return True
    
    return False

def clean_phone_number(phone):
    """Cleans a phone number by removing non-digit characters and standardizes to 12-digit format with country code.
    
    Args:
        phone (str): The phone number string to clean
        
    Returns:
        str: Cleaned and standardized phone number with country code
    """
    if not phone:  # Handle None or empty string
        return ""
        
    # Remove all non-digits
    cleaned = re.sub(r'\D', '', str(phone))
    
    # If it's a 10-digit number, add the '91' prefix (India country code)
    if len(cleaned) == 10 and cleaned[0] in ['6', '7', '8', '9']:
        return f"91{cleaned}"
    
    # If it's already a 12-digit number with '91' prefix, return as is
    if len(cleaned) == 12 and cleaned.startswith('91') and cleaned[2] in ['6', '7', '8', '9']:
        return cleaned
    
    # Return the cleaned number as is for other cases
    return cleaned