from datetime import datetime
from dateutil import parser
import pytz


# Convert the difference to a human-friendly format
def human_friendly_time_difference(created_time):
    created_datetime = parser.parse(created_time)
    now = datetime.now(pytz.utc)
    time_diff = now - created_datetime
    seconds = time_diff.total_seconds()
    if seconds < 60:
        return f"{int(seconds)} seconds ago"
    elif seconds < 3600:
        minutes = seconds // 60
        return f"{int(minutes)} minutes ago"
    elif seconds < 86400:
        hours = seconds // 3600
        return f"{int(hours)} hours ago"
    else:
        days = seconds // 86400
        return f"{int(days)} days ago"

#print(human_friendly_time_difference("2024-08-01T14:41:41+0000"))

def human_friendly_time(created_time_str):
    created_time = datetime.strptime(created_time_str, "%Y-%m-%dT%H:%M:%S%z")
    ist_timezone = pytz.timezone('Asia/Kolkata')
    created_time_ist = created_time.astimezone(ist_timezone)
    
    return created_time_ist.strftime("%a %-I %p")