from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON>Auth2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from jose import jwt, JWTError
from datetime import timed<PERSON><PERSON>

from database import get_db
from models import User as DBUser
from auth_utils import (create_access_token, verify_password, get_password_hash,
                        UserCreate, Token, TokenData, generate_password, SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES)


oauth_2_scheme = OAuth2PasswordBearer(tokenUrl="/token")
router = APIRouter(tags=[__file__.split('/')[-1].split(".")[0]])

# Utility


async def get_user_by_username(db: Session, username: str):
    user = db.query(DBUser).filter(DBUser.username == username).first()
    return user


async def authenticate_user(db: Session, username: str, password: str):
    user = await get_user_by_username(db, username)
    if not user or not verify_password(password, user.hashed_password):
        return False
    return user

# Dependency


async def get_current_user_role(db: Session = Depends(get_db), token: str = Depends(oauth_2_scheme)) -> DBUser:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        user = await get_user_by_username(db, username)
        if user is None:
            raise credentials_exception
        return user
    except JWTError:
        raise credentials_exception


async def get_admin_user(current_user: DBUser = Depends(get_current_user_role), db: Session = Depends(get_db)):
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Access forbidden")
    return current_user


async def get_current_user(db: Session = Depends(get_db), token: str = Depends(oauth_2_scheme)) -> DBUser:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        user = await get_user_by_username(db, username)
        if user is None:
            raise credentials_exception
        return user
    except JWTError:
        raise credentials_exception


async def get_current_active_user(current_user: DBUser = Depends(get_current_user), db: Session = Depends(get_db)):
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# Endpoints


@router.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = await authenticate_user(db, form_data.username, form_data.password)

    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,
                            detail="Incorrect username or password")
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    # Include the user's role in the token payload
    access_token = create_access_token(
        data={"sub": user.username, "role": user.role}, expires_delta=access_token_expires)
    print("In login for access token: role", user.role)
    return {"access_token": access_token, "token_type": "bearer", "role": user.role,  "first_name": user.first_name, "last_name": user.last_name}


@router.post("/users/", response_model=Token)
async def create_user(user: UserCreate, db: Session = Depends(get_db)):
    username = f"{user.first_name}.{user.last_name}".lower()
    user_exists = db.query(DBUser).filter(
        (DBUser.username == username) | (DBUser.email == user.email)).first()
    if user_exists:
        raise HTTPException(
            status_code=400, detail="Username or email already exists")
    hashed_password = get_password_hash(generate_password())
    new_user = DBUser(first_name=user.first_name, last_name=user.last_name,
                      email=user.email, username=username, hashed_password=hashed_password)
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    # Consider security implications of returning sensitive information
    return {"username": new_user.username, "password": hashed_password}
