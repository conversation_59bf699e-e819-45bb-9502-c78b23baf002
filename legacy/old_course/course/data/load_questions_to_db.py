# Run this file from one folder level above:
# python3 -m data.load_questions_to_db

import json
from sqlalchemy.orm import Session
from database import SessionLocal, engine
from models import QuizQuestion, Base


def load_questions(filepath: str):
    db: Session = SessionLocal()

    with open(filepath, 'r') as file:
        questions = json.load(file)

    # Iterate over the questions and add them to the database
    for question in questions:
        db_question = QuizQuestion(
            category=question["category"],
            difficulty=question["difficulty"],
            question=question["question"],
            choices=question["choices"],
            correct_answer=question["correct_answer"]
        )
        db.add(db_question)

    db.commit()
    db.close()


if __name__ == "__main__":
    load_questions("data/questions_assessment.json")
