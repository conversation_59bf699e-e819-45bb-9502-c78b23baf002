[{"category": "Algorithms", "difficulty": 500, "question": "Which algorithm is used for dimensionality reduction?", "choices": ["Linear Regression", "Principal Component Analysis (PCA)", "Random Forest", "Grad<PERSON>"], "correct_answer": "Principal Component Analysis (PCA)"}, {"category": "Python", "difficulty": 300, "question": "What is the output of the following code snippet: print(8 // 3)?", "choices": ["2.666", "2", "3", "2.67"], "correct_answer": "2"}, {"category": "Data Science", "difficulty": 800, "question": "Which of the following is a technique for handling missing data?", "choices": ["Principal Component Analysis", "Imputation", "Overfitting", "Normalization"], "correct_answer": "Imputation"}, {"category": "NLP", "difficulty": 1100, "question": "What does 'TF-IDF' stand for?", "choices": ["Term Frequency-Inverse Document Frequency", "Total Frequency-Integrated Document Frequency", "Term Formation-Individual Document Frequency", "Term Frequency-Index Document Formation"], "correct_answer": "Term Frequency-Inverse Document Frequency"}, {"category": "Deep Learning", "difficulty": 1500, "question": "Which of the following is an example of a recurrent neural network?", "choices": ["CNN", "LSTM", "DBN", "GNN"], "correct_answer": "LSTM"}, {"category": "Visualization", "difficulty": 700, "question": "Which library in Python is most commonly used for data visualization?", "choices": ["<PERSON><PERSON>", "NumPy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SciPy"], "correct_answer": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"category": "Math", "difficulty": 900, "question": "What is the derivative of x^2?", "choices": ["2x", "x^2", "2x^2", "x/2"], "correct_answer": "2x"}, {"category": "MLOps", "difficulty": 2000, "question": "What is the main goal of MLOps?", "choices": ["Model training", "Bringing machine learning models into production", "Data cleaning", "Feature selection"], "correct_answer": "Bringing machine learning models into production"}, {"category": "CV", "difficulty": 2500, "question": "Which of the following tasks is a common application of computer vision?", "choices": ["Text generation", "Image classification", "Time series forecasting", "Sequence alignment"], "correct_answer": "Image classification"}, {"category": "Deep Learning", "difficulty": 3000, "question": "What distinguishes the Transformer architecture from RNNs?", "choices": ["The use of attention mechanisms", "The sequential processing of data", "The use of convolutional layers", "Fixed-size input vectors"], "correct_answer": "The use of attention mechanisms"}]