from fastapi import Fast<PERSON><PERSON>, Request
from pydantic import BaseModel
from datetime import datetime, timedelta
from fastapi.exceptions import HTTPException
from fastapi.middleware.cors import CORSMiddleware


from typing import Dict, List
import json

app = FastAPI()

# Add CORSMiddleware to the application
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins for demonstration purposes
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)


class Quiz(BaseModel):
    subject: str
    difficulty: int
    question_index: int = 0
    start_time: datetime = datetime.now()


MAX_QUIZ_DURATION = timedelta(minutes=30)


class QuizStart(BaseModel):
    subject: str
    difficulty: int


class Question(BaseModel):
    question: str
    options: List[str]


class Answer(BaseModel):
    answer: str


class Score(BaseModel):
    correct_answers: int
    total_questions: int
    percentage: float


class Response:
    correct_answers: int


# Example user data
users = {
    "user1": {"elo": 250, "wins": 0, "losses": 0},
}

# Load the questions from JSON files
questions_gk = json.load(open("questions_gk.json"))
questions_math = json.load(open("questions_math.json"))
questions_science = json.load(open("questions_science.json"))
questions_history = json.load(open("questions_history.json"))
questions_assessment = json.load(open("questions_assessment.json"))


# Define the available subjects and difficulties
SUBJECTS = ["GK", "Math", "Science", "History", "Assessment"]
# create a list of difficulties from 250 to 5000 in increments of 250
DIFFICULTIES = list(range(250, 5001, 250))


# Define the number of questions per evaluation
EVALUATION_INTERVAL = 1

# Define the quiz state
quiz: Quiz = None
responses: List[str] = []


@app.get("/")
async def index():
    return {"message": "Welcome to the quiz application!"}


@app.post("/start_quiz", tags=["Quiz Operations"])
async def start_quiz(subject: str, difficulty: int):
    """
    Start a new quiz with the specified subject and difficulty.

    - **subject**: The subject of the quiz (e.g., "Math").
    - **difficulty**: The difficulty level of the quiz.
    """
    global quiz, responses
    # Print subject and difficulty
    print("Testing this: ", subject, difficulty)

    # Check if the subject and difficulty are valid
    if subject not in SUBJECTS or difficulty not in DIFFICULTIES:
        raise HTTPException(
            status_code=400, detail="Invalid subject or difficulty")

    # Initialize the quiz
    quiz = Quiz(subject=subject, difficulty=difficulty)
    responses = []
    return {"message": "Quiz started successfully!"}


@app.get("/get_question", response_model=Question, tags=["Quiz Operations"])
async def get_question():
    """
    Retrieve the next question in the quiz.
    """
    global quiz
    print("get_question: ", response_model)
    # Check if the quiz is initialized
    if quiz is None:
        return {"error": "Quiz not started"}

    print("get_question")
    # Get the questions from the corresponding JSON file
    questions = []
    if quiz.subject == "GK":
        questions = questions_gk
    elif quiz.subject == "Math":
        questions = questions_math
    elif quiz.subject == "Science":
        questions = questions_science
    elif quiz.subject == "History":
        questions = questions_history
    elif quiz.subject == "Assessment":
        questions = questions_assessment

    # Check if the question index is valid
    if quiz.question_index >= len(questions):
        return {"error": "No more questions"}

    # Get the current question
    question = questions[quiz.question_index]

    # Create a copy of the question without the answer
    question_without_answer = question.copy()
    question_without_answer.pop("answer", None)

    # Increment the question index
    quiz.question_index += 1

    return question_without_answer


@app.post("/submit_answer", response_model=Score, tags=["Quiz Operations"])
async def submit_answer(answer: str):
    """
    Submit an answer for the current question.

    - **answer**: The user's answer to the question.
    """
    global quiz, responses

    # Check if the quiz is initialized
    if quiz is None:
        return {"error": "Quiz not started"}

    # Check if the answer is valid
    if quiz.question_index <= 0:
        return {"error": "No question to answer"}

    # Add the answer to the responses
    responses.append(answer)

    return {"message": "Answer received"}


@app.get("/get_score", response_model=Score, tags=["Quiz Operations"])
async def get_score():
    """
    Get the current score of the quiz.
    """
    global quiz, responses

    # Check if the quiz is initialized
    if quiz is None:
        return {"error": "Quiz not started"}

    # Check if the number of responses is enough for evaluation
    if len(responses) < EVALUATION_INTERVAL:
        return {"error": "Not enough answers in this level to evaluate"}

    # Get the questions from the corresponding JSON file
    questions = []
    if quiz.subject == "GK":
        questions = questions_gk
    elif quiz.subject == "Math":
        questions = questions_math
    elif quiz.subject == "Science":
        questions = questions_science
    elif quiz.subject == "History":
        questions = questions_history
    elif quiz.subject == "Assessment":
        questions = questions_assessment
        print(questions)

    # Evaluate the responses
    correct_answers = 0
    for i in range(0, len(responses)):
        if i <= len(responses):
            if responses[i] == questions[i]["answer"]:
                correct_answers += 1

    # Calculate the number of questions
    total_questions = len(questions)
    print(responses)

    # Calculate the percentage of correct answers
    percentage = (correct_answers / total_questions) * 100

    return {"correct_answers": correct_answers, "total_questions": total_questions, "percentage": percentage}


@app.post("/end_quiz")
async def end_quiz():
    global quiz, responses

    # Reset the quiz state
    quiz = None

    # Reset the responses
    responses = []

    return {"message": "Quiz ended and data reset"}


def update_elo_rating(user, points_earned, total_points, problem_difficulty=1, k=20.0):
    """Update the ELO rating of a user based on their points earned, total points, expected score, and problem difficulty."""
    # Calculate the actual score based on the points earned
    actual_score = 1.0 if points_earned == 0 else 0.0 + \
        (points_earned / total_points)

    # Calculate the expected score based on the problem difficulty
    expected_score = 1 / (1 + 10 ** ((problem_difficulty - user["elo"]) / 400))

    # Update the rating based on the actual score and expected score
    # round the ELO rating to int
    user["elo"] = int(user["elo"] + k * (actual_score - expected_score))

    # Update the number of wins or losses
    if actual_score > expected_score:
        user["wins"] += 1
    else:
        user["losses"] += 1


@app.post("/update_elo")
async def update_elo(user_id: str, points_earned: int, total_points: int, problem_difficulty: float):
    """Update the ELO rating of a user based on their points earned, total points, expected score, and problem difficulty."""
    if user_id not in users:
        return {"error": "The user does not exist."}

    user = users[user_id]

    # Ensure that the problem difficulty is non-negative
    problem_difficulty = max(0, problem_difficulty)

    # Ensure that the points earned are non-negative and less than or equal to the total points
    points_earned = max(0, points_earned)
    total_points = max(1, total_points)

    # Call the updated update_elo_rating function
    update_elo_rating(user, points_earned, total_points, problem_difficulty)

    return {"user": user}
