from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from auth import router as auth_router
from quiz import router as quiz_router
from survey import router as survey_router
#from recording import router as recording_router

from models import User


app = FastAPI()

# CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_router, prefix="/auth")
app.include_router(quiz_router, prefix="/quiz")
app.include_router(survey_router, prefix="/survey")
#app.include_router(recording_router, prefix="/recording")
