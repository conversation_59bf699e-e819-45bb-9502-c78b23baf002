from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>te<PERSON>, String, <PERSON><PERSON><PERSON>, En<PERSON>, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from datetime import datetime

from database import get_db

Base = declarative_base()


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    first_name = Column(String)
    last_name = Column(String)
    hashed_password = Column(String)
    quiz_sessions = relationship("QuizSession", back_populates="user")
    role = Column(Enum("student", "teacher", "admin", "bd",
                  name="user_roles", create_type=True), default="student")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)


class QuizQuestion(Base):
    __tablename__ = "quiz_questions"

    id = Column(Integer, primary_key=True, index=True)
    category = Column(String, index=True)
    difficulty = Column(Integer)
    type = Column(String, index=True)  # e.g., 'mcq', 'open-ended', 'coding'
    question = Column(String, unique=True)
    choices = Column(JSONB, nullable=True)  # Store choices as JSON, optional
    correct_answer = Column(String, nullable=True)  # For open-ended or coding, this might be more complex
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)



class QuizSession(Base):
    __tablename__ = 'quiz_sessions'

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    current_question_index = Column(Integer, default=0)
    total_score = Column(Integer, default=0)  # Track user's score
    questions = Column(JSON)  # Store question IDs and user responses
    user = relationship("User", back_populates="quiz_sessions")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)



class UserAnswer(Base):
    __tablename__ = 'user_answers'

    id = Column(Integer, primary_key=True, index=True)
    quiz_session_id = Column(Integer, ForeignKey('quiz_sessions.id'))
    question_id = Column(Integer, ForeignKey('quiz_questions.id'))
    user_response = Column(Text)  # Store user's response (text, code, etc.)
    correct = Column(Boolean)  # Whether the response was correct or not
    quiz_session = relationship("QuizSession", back_populates="answers")
    question = relationship("QuizQuestion")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class Survey(Base):
    __tablename__ = 'surveys'

    id = Column(Integer, primary_key=True, index=True)
    surveyName = Column(String, index=True)
    surveyType = Column(String)
    question = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)
    responses = Column(JSONB)
