from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from sqlalchemy.sql.expression import func
from pydantic import BaseModel
import json
from typing import List

from models import QuizQuestion
from database import get_db

router = APIRouter(tags=[__file__.split('/')[-1].split(".")[0]])

# Define Pydantic models for the question and choices


class Choice(BaseModel):
    choice: str
    is_correct: bool = False  # This field is unnecessary for the question sent to the user


class Question(BaseModel):
    id: int
    question: str
    choices: List[str]


class Answer(BaseModel):
    question_id: int
    choice: str


@router.get("/get_question")
def get_question(db: Session = Depends(get_db)):
    # Fetch a random question
    question = db.query(QuizQuestion).order_by(func.random()).first()
    if not question:
        raise HTTPException(
            status_code=404, detail="No questions available")

    # print(question.choices)
    # print(question.correct_answer)
    # Prepare response without exposing the correct answer
    return {
        "id": question.id,
        "question": question.question,
        "choices": question.choices
    }


@router.post("/submit_answer")
def submit_answer(answer: Answer, db: Session = Depends(get_db)):
   # Fetch the question by ID
    question = db.query(QuizQuestion).filter(
        QuizQuestion.id == answer.question_id).first()

    if not question:
        raise HTTPException(status_code=404, detail="Question not found")

    # Validate the answer
    is_correct = answer.choice == question.correct_answer

    if is_correct:
        return {"correct": True, "message": "Correct answer!"}
    else:
        return {"correct": False, "message": "Wrong answer, try again!"}
