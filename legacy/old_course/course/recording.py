from fastapi import APIRouter, HTTPException, Depends
from fastapi.testclient import TestClient

from sqlalchemy.orm import Session
from database import get_db

from pydantic import BaseModel, PositiveFloat, Field
from typing import List

from utils.azure_storage import azure_storage_class

router = APIRouter(tags=[__file__.split('/')[-1].split(".")[0]])

# Define Pydantic models
class BlobItem(BaseModel):
    filename: str
    blob_url:str

class RecordingBlob(BaseModel):
    blob_url: str
    expiry_time_hrs: PositiveFloat = 3.0

@router.get("/")
def get(db: Session = Depends(get_db)):
    #BUG: Fetch all the urls from db
    video_list = [
        BlobItem(   filename="Modern AI Pro - Class 01.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 01.mp4" ),
        Blob<PERSON><PERSON>(   filename="Modern AI Pro - Class 02.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 02.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 03.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 03.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 04.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 04.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 05 part1.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 05 part1.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 05 part2.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 05 part2.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 06.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 06.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 07.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 07.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 08.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 08.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 09.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 09.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 10.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 10.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 11 part1.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 11 part1.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 11 part2.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 11 part2.mp4" ),
        BlobItem(   filename="Modern AI Pro - Class 12.mp4",
                    blob_url="https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern AI Pro - Class 12.mp4" ),
    ]
    cohort_video = {
        "Extended Lectures": video_list,
    }
    return cohort_video

@router.post("/public_url")
def get_public_url(recording: RecordingBlob):
    container_name = "mitracourse-videos"
    url = azure_storage_class.get_public_url(recording.blob_url, recording.expiry_time_hrs)
    return url
