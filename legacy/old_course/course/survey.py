from typing import List, Union, Optional, Dict
from pydantic import BaseModel, conlist, validator
from enum import Enum
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from models import Survey as DBSurvey
from database import get_db
router = APIRouter(tags=[__file__.split('/')[-1].split(".")[0]])


class SurveyType(str, Enum):
    single_word = "single_word"
    single_sentence = "single_sentence"
    poll = "poll"
    quiz = "quiz"
    ranking = "ranking"


class Survey(BaseModel):
    surveyName: str
    surveyType: SurveyType
    question: str
    responses: Optional[Union[List[str], List[Dict[str, Union[str,
                                                              List[str]]]], Dict[str, Union[str, List[str]]]]] = None

    @validator('responses', pre=True, always=True)
    def set_responses_empty_for_certain_types(cls, v, values):
        if values.get('surveyType') in [SurveyType.single_word.value, SurveyType.single_sentence.value]:
            return None
        return v


@router.post("/create_survey/")
async def save_survey(survey: Survey, db: Session = Depends(get_db)):
    try:
        db_survey = DBSurvey(
            surveyName=survey.surveyName,
            surveyType=survey.surveyType.value,
            question=survey.question,
            responses=survey.responses
        )
        db.add(db_survey)
        db.commit()
        db.refresh(db_survey)
        return {"survey_id": db_survey.id}

    except SQLAlchemyError as e:
        db.rollback()
        # Log the error for server-side debugging
        print(f"Error saving survey: {e}")
        # Return a generic error message to the client
        raise HTTPException(
            status_code=500, detail="A database error occurred while saving the survey.")
