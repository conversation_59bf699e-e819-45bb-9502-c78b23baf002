# https://learn.microsoft.com/en-us/azure/storage/blobs/storage-blob-user-delegation-sas-create-python
from azure.storage.blob import generate_blob_sas, BlobSasPermissions, BlobServiceClient, BlobClient, ContentSettings, BlobBlock
from azure.identity import ClientSecretCredential
from datetime import datetime, timedelta, UTC
from pathlib import Path
from tqdm import tqdm

from settings import settings

class AzureStorage:
    def __init__(self) -> None:
        # Azure key settings
        self.tenant_id     = settings.azure_tenant_id
        self.client_id     = settings.azure_client_id
        self.client_secret = settings.azure_client_secret
        self.account_url   = settings.azure_storage_account_url
        # setting tmp value
        self._previous_user_delegation_key = None
        # getting credentials
        self.credential          = self._get_credential()
        self.blob_service_client = self._get_blob_service_client(self.credential)

    def _get_credential(self):
        credential = ClientSecretCredential(
            tenant_id     = self.tenant_id,
            client_id     = self.client_id,
            client_secret = self.client_secret,
        )
        return credential

    def _get_blob_client(self, blob_url:str, credential:ClientSecretCredential):
        blob_client = BlobClient.from_blob_url(blob_url, credential)
        return blob_client

    def _request_user_delegation_key(self, blob_service_client: BlobServiceClient):
        # Get a user delegation key that's valid for 1 day
        delegation_key_start_time = datetime.now(UTC)
        delegation_key_expiry_time = delegation_key_start_time + timedelta(days=1)

        user_delegation_key = blob_service_client.get_user_delegation_key(
            key_start_time=delegation_key_start_time,
            key_expiry_time=delegation_key_expiry_time,
        )
        return user_delegation_key

    @property
    def user_delegation_key(self):
        if not self._previous_user_delegation_key:
            print("Creating new user_delegation_key")
            self._previous_user_delegation_key = self._request_user_delegation_key(self.blob_service_client)
            return self._previous_user_delegation_key
        current_time = datetime.now(UTC)
        expiry_time  = datetime.strptime(self._previous_user_delegation_key.signed_expiry, '%Y-%m-%dT%H:%M:%SZ')
        if not current_time < expiry_time:
            print("user_delegation_key expired")
            self._previous_user_delegation_key = self._request_user_delegation_key(self.blob_service_client)
            return self._previous_user_delegation_key
        # Can use the previous one
        return self._previous_user_delegation_key

    def _get_blob_service_client(self, credential:ClientSecretCredential):
        blob_service_client = BlobServiceClient(self.account_url, credential=credential)
        return blob_service_client

    def get_public_url(self, blob_url:str, expiry_time_hrs:float):
        blob_client = self._get_blob_client(blob_url, self.credential)
        expiry_time = datetime.now(UTC) + timedelta(hours=expiry_time_hrs)
        permission  = BlobSasPermissions(read=True)
        sas_token = generate_blob_sas(
            # Blob Details
            account_name=blob_client.account_name,
            container_name=blob_client.container_name,
            blob_name=blob_client.blob_name,
            # Credentials
            user_delegation_key=self.user_delegation_key,
            # Output permissions
            permission=permission,
            expiry=expiry_time,
        )
        sas_url = f"{blob_client.url}?{sas_token}"
        # Create a BlobClient object with SAS authorization
        blob_client_sas = BlobClient.from_blob_url(blob_url=sas_url)
        return blob_client_sas.url
    
    # ___ Contributer permission required___
    def upload_file(self, container_name:str, folder:str, file:Path):
        container_client = self.blob_service_client.get_container_client(container=container_name)
        file_name = f"{folder}/{file.name}"
        with file.open("rb") as data:
            blob_client = container_client.upload_blob(name=file_name, data=data, overwrite=True)
        return blob_client

    def upload_bytes(self, container_name:str, folder:str, file_name:str, data:bytes):
        container_client = self.blob_service_client.get_container_client(container=container_name)
        file_uri = f"{folder}/{file_name}"
        blob_client = container_client.upload_blob(name=file_uri, data=data, overwrite=True)
        return blob_client

    def upload_url(self, container_name:str, source_url:str, file_name:str):
        content_settings=ContentSettings()
        if file_name.endswith(".mp4"):
            content_settings.content_type="video/mp4"
        destination = self.blob_service_client.get_blob_client(container=container_name, blob=file_name)
        destination.upload_blob_from_url(source_url, overwrite=False, content_settings=content_settings)

    def upload_file_blocks(self, container_name: str, local_file_path: Path, block_size: int = 4 * 1024 * 1024, file_name: str = None):
        blob_client = self.blob_service_client.get_blob_client(container_name, file_name or local_file_path.name)
        total_size = local_file_path.stat().st_size
        with local_file_path.open(mode="rb") as file_stream:
            block_id_list = []
            block_number = 0
            with tqdm(total=total_size, unit='B', unit_scale=True, desc='Uploading', leave=False) as pbar:
                while True:
                    buffer = file_stream.read(block_size)
                    if not buffer:
                        break
                    block_id = f"{block_number:08}"
                    block_id_list.append(BlobBlock(block_id=block_id))
                    blob_client.stage_block(block_id=block_id, data=buffer, length=len(buffer))
                    pbar.update(len(buffer))
                    block_number += 1
            blob_client.commit_block_list(block_id_list)

azure_storage_class = AzureStorage()

if __name__ == "__name__":
    # Testing
    azure_storage_class.upload_file_blocks(
        "mitracourse-videos",
        Path("/Users/<USER>/Downloads/testfiles/final.mp4"),
        file_name="Cohort 7:Day 2 - Part 1.mp4"
    )
    azure_storage_class.upload_url(
        "mitracourse-videos", 
        "https://downloads.tldv.io/3l1oi3_e45a8c3d9a4877072a98c8a6967e85a3601265780eb719a9b948d3e4db18c966.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=NGSD4QDEN83B4UDWERB4%2F20240421%2Feu-central-2%2Fs3%2Faws4_request&X-Amz-Date=20240421T112134Z&X-Amz-Expires=259200&X-Amz-Signature=2b25db0d1bb553f5d778253635c09b5d194cf7486ab10bf45d8ba5cdfdb8f863&X-Amz-SignedHeaders=host&x-id=GetObject",
        "Cohort 7:Day 3 - Part 4.mp4",
        )
    blob = azure_storage_class.upload_file("mitracourse-videos", "test", Path("/Users/<USER>/Desktop/Invento-robot-1/mitracourse/README.md"))
    print(blob.url)
    blob_url = "https://mitracourse.blob.core.windows.net/mitracourse-videos/cohort1/Modern%20AI%20Pro%20-%20Class%2001.mp4"
    expiry_time_hrs = 30.0
    url = azure_storage_class.get_public_url(blob_url, expiry_time_hrs)

    print(url)