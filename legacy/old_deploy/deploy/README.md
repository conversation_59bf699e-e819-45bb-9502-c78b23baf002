# Simple Azure VM Deployment

## Step 1: Create Azure VM
```bash
# Create Ubuntu 22.04 VM (Basic B1s is enough for internal use)
# Allow HTTP (80) and SSH (22) in security group
```

## Step 2: Upload your code
```bash
# From your local machine
scp -r mai-administrative azureuser@YOUR_VM_IP:~/
```

## Step 3: SSH and deploy
```bash
ssh azureuser@YOUR_VM_IP
cd mai-administrative
chmod +x deploy/deploy.sh
./deploy/deploy.sh
```

## Step 4: Configure environment
```bash
# Copy and edit the .env file
cp deploy/.env.production .env
nano .env  # Add your real Facebook credentials
```

## Step 5: Restart service
```bash
sudo systemctl restart mai-crm
```

## Usage
```bash
# Your API will be available at:
http://YOUR_VM_IP/api/leads/recent

# Test with:
curl -H "Authorization: Bearer your-api-key" http://YOUR_VM_IP/health
```

## Management Commands
```bash
# Check status
sudo systemctl status mai-crm

# View logs
sudo journalctl -u mai-crm -f

# Restart service
sudo systemctl restart mai-crm
```

**Total setup time: ~10 minutes**