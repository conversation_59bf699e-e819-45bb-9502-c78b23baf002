#!/bin/bash

# Simple Azure VM deployment script for MAI CRM API

echo "Setting up MAI CRM API on Azure VM..."

# Update system
sudo apt update
sudo apt install -y python3 python3-pip python3-venv nginx

# Create app directory
sudo mkdir -p /opt/mai-crm
sudo chown $USER:$USER /opt/mai-crm
cd /opt/mai-crm

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Copy your code (you'll need to upload this first)
# For now, clone or copy your files here
cp -r ~/mai-administrative/* .

# Install dependencies
pip install -r api/requirements.txt

# Create systemd service
sudo tee /etc/systemd/system/mai-crm.service > /dev/null <<EOF
[Unit]
Description=MAI CRM API
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/opt/mai-crm
Environment=PATH=/opt/mai-crm/venv/bin
ExecStart=/opt/mai-crm/venv/bin/python /opt/mai-crm/api/leads_api.py
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# Setup nginx reverse proxy
sudo tee /etc/nginx/sites-available/mai-crm > /dev/null <<EOF
server {
    listen 80;
    server_name _;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
}
EOF

# Enable nginx site
sudo ln -sf /etc/nginx/sites-available/mai-crm /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl restart nginx

# Start services
sudo systemctl daemon-reload
sudo systemctl enable mai-crm
sudo systemctl start mai-crm

echo "Deployment complete!"
echo "API should be available at http://YOUR_VM_IP"
echo "Check status with: sudo systemctl status mai-crm"