# AI Bootcamp Lead Management Dashboard

A comprehensive Next.js dashboard for managing AI bootcamp leads with Facebook integration.

## You can get user access token from here: 
https://developers.facebook.com/tools/explorer/

That is valid only for 2 hours. To get the extended token add that token
to .env and run the get_extended_token.py -- then copy that new token to .env


## Project Structure

```
/new/
├── dashboard/              # Main Next.js application
│   ├── components/        # React components
│   ├── lib/              # Database & utilities
│   ├── pages/            # Next.js pages & API routes
│   │   ├── api/         # Backend API endpoints
│   │   └── dashboard/   # Dashboard pages
│   ├── styles/          # CSS/Tailwind styles
│   └── public/          # Static assets
├── scripts/              # Standalone Python scripts
│   ├── fb_accounts_config.py
│   └── fb_multi_account_leads.py
├── config/              # Configuration files
├── data/               # Data exports and database
└── docs/              # Documentation
    ├── plan.md
    └── technical-specs.md
```

## Getting Started

### 1. Install Dependencies
```bash
cd dashboard
npm install
```

### 2. Set up Environment Variables
Copy `.env.local` and update with your actual values:
- Facebook API credentials
- Database path
- Authentication secrets

### 3. Run Development Server
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the dashboard.

## Features

- **Dashboard Overview** - Lead statistics and conversion metrics
- **Lead Management** - Full CRUD operations on leads
- **Sales Funnel** - 9-stage lead progression tracking
- **Facebook Integration** - Automated lead pulling
- **Mobile Responsive** - Works on all devices
- **Real-time Updates** - Live lead status changes

## Tech Stack

- **Framework**: Next.js 14 (React 18)
- **Database**: SQLite (upgradeable to PostgreSQL)
- **Styling**: Tailwind CSS
- **Charts**: Recharts
- **API**: Facebook Graph API

## Deployment

Ready for Azure VM deployment with:
- PM2 process management
- Nginx reverse proxy
- Automated lead pulling cron jobs

See `docs/technical-specs.md` for detailed deployment instructions.