<!DOCTYPE html><html><head>
      <title>CODEBASE_DEEP_DIVE</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:////home/<USER>/.windsurf/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/katex.min.css">
      
      
      <script type="text/javascript" src="file:////home/<USER>/.windsurf/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/mermaid/mermaid.min.js" charset="UTF-8"></script>
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="modern-ai-pro---codebase-deep-dive-reference-guide-">Modern AI Pro - Codebase Deep Dive Reference Guide 🚀 </h1>
<p><em>Personal reference guide for understanding the Modern AI Pro lead management system architecture</em></p>
<h2 id="️-system-architecture-overview">🏗️ System Architecture Overview </h2>
<div class="mermaid">graph TB
    subgraph "Frontend Layer"
        A[Next.js Dashboard] --&gt; B[React Components]
        B --&gt; C[TailwindCSS Styling]
    end
    
    subgraph "API Layer"
        D[Next.js API Routes] --&gt; E[Lead Management APIs]
        D --&gt; F[Facebook Integration APIs]
        D --&gt; G[Payment Processing APIs]
        D --&gt; H[Authentication APIs]
    end
    
    subgraph "Data Layer"
        I[SQLite Database] --&gt; J[Leads Table]
        I --&gt; K[Interactions Table]
        I --&gt; L[Workshops Table]
        I --&gt; M[Users Table]
    end
    
    subgraph "Integration Layer"
        N[Facebook Lead Ads API]
        O[Stripe Payment API]
        P[Email Automation]
        Q[SMS Notifications]
    end
    
    subgraph "Python Scripts"
        R[Data Migration Scripts]
        S[Facebook Automation]
        T[Monitoring &amp; Analytics]
        U[Email Processing]
    end
    
    A --&gt; D
    D --&gt; I
    D --&gt; N
    D --&gt; O
    D --&gt; P
    D --&gt; Q
    R --&gt; I
    S --&gt; N
    T --&gt; I
    U --&gt; P
</div><h2 id="-lead-management-flow">🔄 Lead Management Flow </h2>
<div class="mermaid">sequenceDiagram
    participant FB as Facebook Lead Ads
    participant API as Next.js API
    participant DB as SQLite Database
    participant UI as Dashboard UI
    participant Sales as Sales Team
    participant Email as Email System
    
    FB-&gt;&gt;API: New Lead Webhook
    API-&gt;&gt;DB: Store Lead Data
    API-&gt;&gt;Email: Send Auto-Response
    API-&gt;&gt;UI: Update Dashboard
    UI-&gt;&gt;Sales: Lead Notification
    Sales-&gt;&gt;API: Update Lead Status
    API-&gt;&gt;DB: Update Status
    API-&gt;&gt;UI: Refresh Dashboard
</div><h2 id="-database-schema-deep-dive">📊 Database Schema Deep Dive </h2>
<h3 id="core-tables-structure">Core Tables Structure </h3>
<div class="mermaid">erDiagram
    LEADS ||--o{ LEAD_INTERACTIONS : has
    LEADS ||--o{ WORKSHOP_ENROLLMENTS : enrolls_in
    WORKSHOPS ||--o{ WORKSHOP_ENROLLMENTS : contains
    USERS ||--o{ LEAD_INTERACTIONS : performs
    
    LEADS {
        int id PK
        string lead_id UK
        string account_name
        string campaign_name
        string full_name
        string email
        string phone_number
        string status
        string lead_segment
        int segment_score
        datetime created_time
        datetime updated_time
    }
    
    LEAD_INTERACTIONS {
        int id PK
        int lead_id FK
        int user_id FK
        string interaction_type
        string channel
        text notes
        datetime interaction_time
    }
    
    WORKSHOPS {
        int id PK
        string workshop_type
        string title
        datetime start_time
        datetime end_time
        int max_participants
        decimal price
    }
    
    USERS {
        int id PK
        string username
        string email
        string role
        string full_name
    }
</div><h3 id="lead-status-flow">Lead Status Flow </h3>
<div class="mermaid">stateDiagram-v2
    [*] --&gt; New : Facebook Lead
    New --&gt; AutoResponded : Instant Email/SMS
    AutoResponded --&gt; Contacted : Sales Outreach
    Contacted --&gt; Qualified : Meets Criteria
    Qualified --&gt; DemoScheduled : Workshop Booked
    DemoScheduled --&gt; DemoCompleted : Demo Conducted
    DemoCompleted --&gt; Enrolled : Payment Made
    Enrolled --&gt; WorkshopComplete : Graduated
    WorkshopComplete --&gt; AlumniNetwork : Community Join
    AlumniNetwork --&gt; SubscriptionProspect : Upsell Opportunity
    
    Contacted --&gt; NotInterested : Declined
    Qualified --&gt; NotInterested : Not Qualified
    DemoScheduled --&gt; NoShow : Missed Demo
    DemoCompleted --&gt; NotInterested : Didn't Convert
</div><h2 id="-key-api-endpoints">🎯 Key API Endpoints </h2>
<h3 id="lead-management-apis-pagesapi">Lead Management APIs (<code>/pages/api/</code>) </h3>
<table>
<thead>
<tr>
<th>Endpoint</th>
<th>Method</th>
<th>Purpose</th>
<th>Example Usage</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>/api/leads</code></td>
<td>GET</td>
<td>Fetch leads with filtering/sorting</td>
<td>Dashboard lead table</td>
</tr>
<tr>
<td><code>/api/leads</code></td>
<td>POST</td>
<td>Create new lead</td>
<td>Manual lead entry</td>
</tr>
<tr>
<td><code>/api/leads</code></td>
<td>PUT</td>
<td>Update lead status</td>
<td>Sales team actions</td>
</tr>
<tr>
<td><code>/api/facebook/webhook</code></td>
<td>POST</td>
<td>Receive Facebook leads</td>
<td>Automated lead capture</td>
</tr>
<tr>
<td><code>/api/facebook/accounts</code></td>
<td>GET</td>
<td>Get FB account data</td>
<td>Campaign management</td>
</tr>
<tr>
<td><code>/api/payments</code></td>
<td>POST</td>
<td>Process payments</td>
<td>Workshop enrollment</td>
</tr>
<tr>
<td><code>/api/workshops</code></td>
<td>GET</td>
<td>List workshops</td>
<td>Booking system</td>
</tr>
</tbody>
</table>
<h3 id="example-api-request-flow">Example API Request Flow </h3>
<pre data-role="codeBlock" data-info="javascript" class="language-javascript javascript"><code><span class="token comment">// Example: Fetching leads with filters</span>
<span class="token keyword keyword-const">const</span> response <span class="token operator">=</span> <span class="token keyword control-flow keyword-await">await</span> <span class="token function">fetch</span><span class="token punctuation">(</span><span class="token string">'/api/leads?status=New&amp;sortBy=created_time&amp;sortOrder=desc&amp;limit=50'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-const">const</span> <span class="token punctuation">{</span> leads<span class="token punctuation">,</span> totalCount <span class="token punctuation">}</span> <span class="token operator">=</span> <span class="token keyword control-flow keyword-await">await</span> response<span class="token punctuation">.</span><span class="token method function property-access">json</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Example: Updating lead status</span>
<span class="token keyword control-flow keyword-await">await</span> <span class="token function">fetch</span><span class="token punctuation">(</span><span class="token string">'/api/leads'</span><span class="token punctuation">,</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">method</span><span class="token operator">:</span> <span class="token string">'PUT'</span><span class="token punctuation">,</span>
  <span class="token literal-property property">headers</span><span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token string-property property">'Content-Type'</span><span class="token operator">:</span> <span class="token string">'application/json'</span> <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">body</span><span class="token operator">:</span> <span class="token known-class-name class-name">JSON</span><span class="token punctuation">.</span><span class="token method function property-access">stringify</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
    <span class="token literal-property property">id</span><span class="token operator">:</span> leadId<span class="token punctuation">,</span>
    <span class="token literal-property property">status</span><span class="token operator">:</span> <span class="token string">'Contacted'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">notes</span><span class="token operator">:</span> <span class="token string">'Called and left voicemail'</span>
  <span class="token punctuation">}</span><span class="token punctuation">)</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h2 id="-component-architecture">🧩 Component Architecture </h2>
<h3 id="key-react-components">Key React Components </h3>
<div class="mermaid">graph TD
    A[Layout.js] --&gt; B[Dashboard Pages]
    B --&gt; C[LeadsTable.js]
    B --&gt; D[FacebookAccountsTable.js]
    B --&gt; E[Workshops.js]
    B --&gt; F[MetricsCard.js]
    
    C --&gt; G[SearchableTable.js]
    C --&gt; H[AddLeadDialog.js]
    
    D --&gt; I[CampaignTable.js]
    D --&gt; J[LeadAdsTable.js]
    
    E --&gt; K[EventsSchedule.js]
    
    F --&gt; L[BarChart.js]
    F --&gt; M[ConversionFunnel.js]
</div><h3 id="component-responsibilities">Component Responsibilities </h3>
<ul>
<li><strong><code>LeadsTable.js</code></strong> - Main lead management interface with filtering, sorting, status updates</li>
<li><strong><code>FacebookAccountsTable.js</code></strong> - Facebook campaign and ad account management</li>
<li><strong><code>SearchableTable.js</code></strong> - Reusable table component with search/pagination</li>
<li><strong><code>Layout.js</code></strong> - Main app layout with navigation and authentication</li>
<li><strong><code>MetricsCard.js</code></strong> - Dashboard KPI displays</li>
<li><strong><code>Workshops.js</code></strong> - Workshop scheduling and management</li>
</ul>
<h2 id="-integration-points">🔌 Integration Points </h2>
<h3 id="facebook-lead-ads-integration">Facebook Lead Ads Integration </h3>
<div class="mermaid">graph LR
    A[Facebook Lead Ad] --&gt; B[Webhook Trigger]
    B --&gt; C["/api/facebook/webhook"]
    C --&gt; D[Validate Webhook]
    D --&gt; E[Extract Lead Data]
    E --&gt; F[Store in Database]
    F --&gt; G[Send Auto-Response]
    G --&gt; H[Update Dashboard]
</div><p><strong>Key Files:</strong></p>
<ul>
<li><code>/scripts/core/facebook-integration/</code> - Python automation scripts</li>
<li><code>/pages/api/facebook/webhook.js</code> - Webhook handler</li>
<li><code>/pages/api/facebook/accounts.js</code> - Account management</li>
</ul>
<h3 id="payment-processing-stripe">Payment Processing (Stripe) </h3>
<div class="mermaid">graph LR
    A[Workshop Enrollment] --&gt; B[Stripe Checkout]
    B --&gt; C[Payment Success]
    C --&gt; D["/api/payments webhook"]
    D --&gt; E[Update Lead Status]
    E --&gt; F[Send Confirmation]
    F --&gt; G[Workshop Access]
</div><h2 id="getting-started-guide">Getting Started Guide </h2>
<h3 id="1-environment-setup">1. Environment Setup </h3>
<p>Your <code>.env</code> file location is <strong>CORRECT</strong>: <code>/modernai/new/dashboard/.env</code></p>
<p>Required environment variables:</p>
<pre data-role="codeBlock" data-info="bash" class="language-bash bash"><code><span class="token comment"># Facebook API</span>
<span class="token assign-left variable">FACEBOOK_APP_ID</span><span class="token operator">=</span>your_app_id
<span class="token assign-left variable">FACEBOOK_APP_SECRET</span><span class="token operator">=</span>your_app_secret
<span class="token assign-left variable">FACEBOOK_ACCESS_TOKEN</span><span class="token operator">=</span>your_access_token

<span class="token comment"># Database</span>
<span class="token assign-left variable">DATABASE_PATH</span><span class="token operator">=</span>./database/leads.db

<span class="token comment"># Authentication</span>
<span class="token assign-left variable">JWT_SECRET</span><span class="token operator">=</span>your_jwt_secret
<span class="token assign-left variable">BCRYPT_SALT_ROUNDS</span><span class="token operator">=</span><span class="token number">10</span>

<span class="token comment"># Stripe (if using payments)</span>
<span class="token assign-left variable">STRIPE_SECRET_KEY</span><span class="token operator">=</span>your_stripe_secret
<span class="token assign-left variable">STRIPE_WEBHOOK_SECRET</span><span class="token operator">=</span>your_webhook_secret

<span class="token comment"># Email (if using notifications)</span>
<span class="token assign-left variable">EMAIL_SERVICE_API_KEY</span><span class="token operator">=</span>your_email_key
</code></pre><h3 id="2-starting-the-servers">2. Starting the Servers </h3>
<h4 id="nextjs-dashboard-server">Next.js Dashboard Server </h4>
<pre data-role="codeBlock" data-info="bash" class="language-bash bash"><code><span class="token builtin class-name">cd</span> /home/<USER>/Desktop/modern-ai-pro/modernai/new/dashboard
<span class="token function">npm</span> <span class="token function">install</span>                    <span class="token comment"># Install dependencies</span>
<span class="token function">npm</span> run setup-db              <span class="token comment"># Initialize database</span>
<span class="token function">npm</span> run dev                    <span class="token comment"># Start development server (http://localhost:3000)</span>
</code></pre><h4 id="python-scripts-optional">Python Scripts (Optional) </h4>
<pre data-role="codeBlock" data-info="bash" class="language-bash bash"><code><span class="token builtin class-name">cd</span> /home/<USER>/Desktop/modern-ai-pro/modernai/new/scripts
python <span class="token parameter variable">-m</span> venv venv           <span class="token comment"># Create virtual environment</span>
<span class="token builtin class-name">source</span> venv/bin/activate      <span class="token comment"># Activate environment</span>
pip <span class="token function">install</span> <span class="token parameter variable">-r</span> requirements.txt  <span class="token comment"># Install dependencies</span>
</code></pre><h3 id="3-database-initialization">3. Database Initialization </h3>
<p>The database will be automatically created when you run <code>npm run setup-db</code>. It includes:</p>
<ul>
<li><strong>Leads table</strong> - Core lead data with 9,726+ existing leads</li>
<li><strong>Lead interactions</strong> - All touchpoints and communications</li>
<li><strong>Workshops</strong> - Course scheduling and enrollment</li>
<li><strong>Users</strong> - Dashboard user management</li>
</ul>
<h2 id="-example-scenarios">📈 Example Scenarios </h2>
<h3 id="scenario-1-new-facebook-lead-processing">Scenario 1: New Facebook Lead Processing </h3>
<ol>
<li><strong>Lead Capture</strong>: Facebook user fills out lead form</li>
<li><strong>Webhook Trigger</strong>: Facebook sends POST to <code>/api/facebook/webhook</code></li>
<li><strong>Data Processing</strong>: Lead data extracted and validated</li>
<li><strong>Database Storage</strong>: Lead stored with status "New"</li>
<li><strong>Auto-Response</strong>: Instant email/SMS sent to lead</li>
<li><strong>Dashboard Update</strong>: Lead appears in dashboard table</li>
<li><strong>Sales Notification</strong>: Sales team gets notification</li>
</ol>
<h3 id="scenario-2-sales-team-lead-management">Scenario 2: Sales Team Lead Management </h3>
<ol>
<li><strong>Dashboard Access</strong>: Sales person logs into dashboard</li>
<li><strong>Lead Review</strong>: Views leads table filtered by "New" status</li>
<li><strong>Lead Contact</strong>: Calls lead and updates status to "Contacted"</li>
<li><strong>Notes Addition</strong>: Adds interaction notes via API</li>
<li><strong>Follow-up Scheduling</strong>: Sets reminder for next contact</li>
<li><strong>Status Progression</strong>: Moves qualified leads to "Demo Scheduled"</li>
</ol>
<h3 id="scenario-3-workshop-enrollment-flow">Scenario 3: Workshop Enrollment Flow </h3>
<ol>
<li><strong>Demo Completion</strong>: Lead completes workshop demo</li>
<li><strong>Payment Processing</strong>: Lead enrolls via Stripe checkout</li>
<li><strong>Status Update</strong>: Lead status changes to "Enrolled"</li>
<li><strong>Workshop Access</strong>: Lead gets access to workshop materials</li>
<li><strong>Progress Tracking</strong>: Workshop completion tracked</li>
<li><strong>Alumni Network</strong>: Graduated leads join community</li>
</ol>
<h2 id="️-development-best-practices-maang-style">🛠️ Development Best Practices (MAANG Style) </h2>
<h3 id="code-organization">Code Organization </h3>
<ul>
<li><strong>Separation of Concerns</strong>: Clear boundaries between frontend, API, and data layers</li>
<li><strong>Component Reusability</strong>: Shared components like <code>SearchableTable.js</code></li>
<li><strong>API Design</strong>: RESTful endpoints with proper HTTP methods</li>
<li><strong>Error Handling</strong>: Comprehensive error handling throughout</li>
</ul>
<h3 id="database-design">Database Design </h3>
<ul>
<li><strong>Normalized Schema</strong>: Proper foreign key relationships</li>
<li><strong>Indexing Strategy</strong>: Optimized queries for dashboard performance</li>
<li><strong>Data Integrity</strong>: Constraints and validation at database level</li>
<li><strong>Scalability</strong>: Designed for 400+ leads/day processing</li>
</ul>
<h3 id="security-considerations">Security Considerations </h3>
<ul>
<li><strong>Authentication</strong>: JWT-based user authentication</li>
<li><strong>Input Validation</strong>: Sanitization of all user inputs</li>
<li><strong>Environment Variables</strong>: Sensitive data in <code>.env</code> files</li>
<li><strong>API Security</strong>: Rate limiting and webhook validation</li>
</ul>
<h2 id="-debugging--monitoring">🔍 Debugging &amp; Monitoring </h2>
<h3 id="log-locations">Log Locations </h3>
<ul>
<li><strong>Next.js Logs</strong>: Console output during <code>npm run dev</code></li>
<li><strong>Python Script Logs</strong>: <code>/scripts/logs/</code> directory</li>
<li><strong>Database Queries</strong>: Enable SQLite logging for debugging</li>
</ul>
<h3 id="common-issues">Common Issues </h3>
<ol>
<li><strong>Database Connection</strong>: Check <code>DATABASE_PATH</code> in <code>.env</code></li>
<li><strong>Facebook Webhook</strong>: Verify webhook URL and validation</li>
<li><strong>API Errors</strong>: Check browser network tab for API responses</li>
<li><strong>Component Rendering</strong>: Use React DevTools for debugging</li>
</ol>
<h2 id="-further-exploration-areas">📚 Further Exploration Areas </h2>
<ol>
<li><strong>Advanced Analytics</strong> - Dive into conversion funnel analysis</li>
<li><strong>Email Automation</strong> - Explore email template system</li>
<li><strong>Mobile Responsiveness</strong> - Review mobile UI components</li>
<li><strong>Performance Optimization</strong> - Database query optimization</li>
<li><strong>Testing Strategy</strong> - Unit and integration test setup</li>
</ol>
<hr>
<p><em>This guide is for personal reference and understanding. Keep it updated as you explore more of the codebase!</em></p>

      </div>
      
      
    
    
    <script type="module">
// TODO: If ZenUML gets integrated into mermaid in the future,
//      we can remove the following lines.


var MERMAID_CONFIG = ({"startOnLoad":false});
if (typeof MERMAID_CONFIG !== 'undefined') {
  MERMAID_CONFIG.startOnLoad = false
  MERMAID_CONFIG.cloneCssStyles = false
  MERMAID_CONFIG.theme = "default"
}

mermaid.initialize(MERMAID_CONFIG || {})
if (typeof(window['Reveal']) !== 'undefined') {
  function mermaidRevealHelper(event) {
    var currentSlide = event.currentSlide
    var diagrams = currentSlide.querySelectorAll('.mermaid')
    for (var i = 0; i < diagrams.length; i++) {
      var diagram = diagrams[i]
      if (!diagram.hasAttribute('data-processed')) {
        mermaid.init(null, diagram, ()=> {
          Reveal.slide(event.indexh, event.indexv)
        })
      }
    }
  }
  Reveal.addEventListener('slidetransitionend', mermaidRevealHelper)
  Reveal.addEventListener('ready', mermaidRevealHelper)
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
} else {
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
}
</script>
    
    
    
  
    </body></html>