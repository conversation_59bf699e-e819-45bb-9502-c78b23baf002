# Modern AI Pro - Codebase Deep Dive Reference Guide 🚀

*Personal reference guide for understanding the Modern AI Pro lead management system architecture*

## 🏗️ System Architecture Overview

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Next.js Dashboard] --> B[React Components]
        B --> C[TailwindCSS Styling]
    end
    
    subgraph "API Layer"
        D[Next.js API Routes] --> E[Lead Management APIs]
        D --> F[Facebook Integration APIs]
        D --> G[Payment Processing APIs]
        D --> H[Authentication APIs]
    end
    
    subgraph "Data Layer"
        I[SQLite Database] --> J[Leads Table]
        I --> K[Interactions Table]
        I --> L[Workshops Table]
        I --> M[Users Table]
    end
    
    subgraph "Integration Layer"
        N[Facebook Lead Ads API]
        O[Stripe Payment API]
        P[Email Automation]
        Q[SMS Notifications]
    end
    
    subgraph "Python Scripts"
        R[Data Migration Scripts]
        S[Facebook Automation]
        T[Monitoring & Analytics]
        U[Email Processing]
    end
    
    A --> D
    D --> I
    D --> N
    D --> O
    D --> P
    D --> Q
    R --> I
    S --> N
    T --> I
    U --> P
```

## 🔄 Lead Management Flow

```mermaid
sequenceDiagram
    participant FB as Facebook Lead Ads
    participant API as Next.js API
    participant DB as SQLite Database
    participant UI as Dashboard UI
    participant Sales as Sales Team
    participant Email as Email System
    
    FB->>API: New Lead Webhook
    API->>DB: Store Lead Data
    API->>Email: Send Auto-Response
    API->>UI: Update Dashboard
    UI->>Sales: Lead Notification
    Sales->>API: Update Lead Status
    API->>DB: Update Status
    API->>UI: Refresh Dashboard
```

## 📊 Database Schema Deep Dive

### Core Tables Structure

```mermaid
erDiagram
    LEADS ||--o{ LEAD_INTERACTIONS : has
    LEADS ||--o{ WORKSHOP_ENROLLMENTS : enrolls_in
    WORKSHOPS ||--o{ WORKSHOP_ENROLLMENTS : contains
    USERS ||--o{ LEAD_INTERACTIONS : performs
    
    LEADS {
        int id PK
        string lead_id UK
        string account_name
        string campaign_name
        string full_name
        string email
        string phone_number
        string status
        string lead_segment
        int segment_score
        datetime created_time
        datetime updated_time
    }
    
    LEAD_INTERACTIONS {
        int id PK
        int lead_id FK
        int user_id FK
        string interaction_type
        string channel
        text notes
        datetime interaction_time
    }
    
    WORKSHOPS {
        int id PK
        string workshop_type
        string title
        datetime start_time
        datetime end_time
        int max_participants
        decimal price
    }
    
    USERS {
        int id PK
        string username
        string email
        string role
        string full_name
    }
```

### Lead Status Flow

```mermaid
stateDiagram-v2
    [*] --> New : Facebook Lead
    New --> AutoResponded : Instant Email/SMS
    AutoResponded --> Contacted : Sales Outreach
    Contacted --> Qualified : Meets Criteria
    Qualified --> DemoScheduled : Workshop Booked
    DemoScheduled --> DemoCompleted : Demo Conducted
    DemoCompleted --> Enrolled : Payment Made
    Enrolled --> WorkshopComplete : Graduated
    WorkshopComplete --> AlumniNetwork : Community Join
    AlumniNetwork --> SubscriptionProspect : Upsell Opportunity
    
    Contacted --> NotInterested : Declined
    Qualified --> NotInterested : Not Qualified
    DemoScheduled --> NoShow : Missed Demo
    DemoCompleted --> NotInterested : Didn't Convert
```

## 🎯 Key API Endpoints

### Lead Management APIs (`/pages/api/`)

| Endpoint | Method | Purpose | Example Usage |
|----------|--------|---------|---------------|
| `/api/leads` | GET | Fetch leads with filtering/sorting | Dashboard lead table |
| `/api/leads` | POST | Create new lead | Manual lead entry |
| `/api/leads` | PUT | Update lead status | Sales team actions |
| `/api/facebook/webhook` | POST | Receive Facebook leads | Automated lead capture |
| `/api/facebook/accounts` | GET | Get FB account data | Campaign management |
| `/api/payments` | POST | Process payments | Workshop enrollment |
| `/api/workshops` | GET | List workshops | Booking system |

### Example API Request Flow

```javascript
// Example: Fetching leads with filters
const response = await fetch('/api/leads?status=New&sortBy=created_time&sortOrder=desc&limit=50');
const { leads, totalCount } = await response.json();

// Example: Updating lead status
await fetch('/api/leads', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    id: leadId,
    status: 'Contacted',
    notes: 'Called and left voicemail'
  })
});
```

## 🧩 Component Architecture

### Key React Components

```mermaid
graph TD
    A[Layout.js] --> B[Dashboard Pages]
    B --> C[LeadsTable.js]
    B --> D[FacebookAccountsTable.js]
    B --> E[Workshops.js]
    B --> F[MetricsCard.js]
    
    C --> G[SearchableTable.js]
    C --> H[AddLeadDialog.js]
    
    D --> I[CampaignTable.js]
    D --> J[LeadAdsTable.js]
    
    E --> K[EventsSchedule.js]
    
    F --> L[BarChart.js]
    F --> M[ConversionFunnel.js]
```

### Component Responsibilities

- **`LeadsTable.js`** - Main lead management interface with filtering, sorting, status updates
- **`FacebookAccountsTable.js`** - Facebook campaign and ad account management
- **`SearchableTable.js`** - Reusable table component with search/pagination
- **`Layout.js`** - Main app layout with navigation and authentication
- **`MetricsCard.js`** - Dashboard KPI displays
- **`Workshops.js`** - Workshop scheduling and management

## 🔌 Integration Points

### Facebook Lead Ads Integration

```mermaid
graph LR
    A[Facebook Lead Ad] --> B[Webhook Trigger]
    B --> C["/api/facebook/webhook"]
    C --> D[Validate Webhook]
    D --> E[Extract Lead Data]
    E --> F[Store in Database]
    F --> G[Send Auto-Response]
    G --> H[Update Dashboard]
```

**Key Files:**
- `/scripts/core/facebook-integration/` - Python automation scripts
- `/pages/api/facebook/webhook.js` - Webhook handler
- `/pages/api/facebook/accounts.js` - Account management

### Payment Processing (Stripe)

```mermaid
graph LR
    A[Workshop Enrollment] --> B[Stripe Checkout]
    B --> C[Payment Success]
    C --> D["/api/payments webhook"]
    D --> E[Update Lead Status]
    E --> F[Send Confirmation]
    F --> G[Workshop Access]
```

## Getting Started Guide

### 1. Environment Setup

Your `.env` file location is **CORRECT**: `/modernai/new/dashboard/.env`

Required environment variables:
```bash
# Facebook API
FACEBOOK_APP_ID=your_app_id
FACEBOOK_APP_SECRET=your_app_secret
FACEBOOK_ACCESS_TOKEN=your_access_token

# Database
DATABASE_PATH=./database/leads.db

# Authentication
JWT_SECRET=your_jwt_secret
BCRYPT_SALT_ROUNDS=10

# Stripe (if using payments)
STRIPE_SECRET_KEY=your_stripe_secret
STRIPE_WEBHOOK_SECRET=your_webhook_secret

# Email (if using notifications)
EMAIL_SERVICE_API_KEY=your_email_key
```

### 2. Starting the Servers

#### Next.js Dashboard Server
```bash
cd /home/<USER>/Desktop/modern-ai-pro/modernai/new/dashboard
npm install                    # Install dependencies
npm run setup-db              # Initialize database
npm run dev                    # Start development server (http://localhost:3000)
```

#### Python Scripts (Optional)
```bash
cd /home/<USER>/Desktop/modern-ai-pro/modernai/new/scripts
python -m venv venv           # Create virtual environment
source venv/bin/activate      # Activate environment
pip install -r requirements.txt  # Install dependencies
```

### 3. Database Initialization

The database will be automatically created when you run `npm run setup-db`. It includes:
- **Leads table** - Core lead data with 9,726+ existing leads
- **Lead interactions** - All touchpoints and communications
- **Workshops** - Course scheduling and enrollment
- **Users** - Dashboard user management

## 📈 Example Scenarios

### Scenario 1: New Facebook Lead Processing

1. **Lead Capture**: Facebook user fills out lead form
2. **Webhook Trigger**: Facebook sends POST to `/api/facebook/webhook`
3. **Data Processing**: Lead data extracted and validated
4. **Database Storage**: Lead stored with status "New"
5. **Auto-Response**: Instant email/SMS sent to lead
6. **Dashboard Update**: Lead appears in dashboard table
7. **Sales Notification**: Sales team gets notification

### Scenario 2: Sales Team Lead Management

1. **Dashboard Access**: Sales person logs into dashboard
2. **Lead Review**: Views leads table filtered by "New" status
3. **Lead Contact**: Calls lead and updates status to "Contacted"
4. **Notes Addition**: Adds interaction notes via API
5. **Follow-up Scheduling**: Sets reminder for next contact
6. **Status Progression**: Moves qualified leads to "Demo Scheduled"

### Scenario 3: Workshop Enrollment Flow

1. **Demo Completion**: Lead completes workshop demo
2. **Payment Processing**: Lead enrolls via Stripe checkout
3. **Status Update**: Lead status changes to "Enrolled"
4. **Workshop Access**: Lead gets access to workshop materials
5. **Progress Tracking**: Workshop completion tracked
6. **Alumni Network**: Graduated leads join community

## 🛠️ Development Best Practices (MAANG Style)

### Code Organization
- **Separation of Concerns**: Clear boundaries between frontend, API, and data layers
- **Component Reusability**: Shared components like `SearchableTable.js`
- **API Design**: RESTful endpoints with proper HTTP methods
- **Error Handling**: Comprehensive error handling throughout

### Database Design
- **Normalized Schema**: Proper foreign key relationships
- **Indexing Strategy**: Optimized queries for dashboard performance
- **Data Integrity**: Constraints and validation at database level
- **Scalability**: Designed for 400+ leads/day processing

### Security Considerations
- **Authentication**: JWT-based user authentication
- **Input Validation**: Sanitization of all user inputs
- **Environment Variables**: Sensitive data in `.env` files
- **API Security**: Rate limiting and webhook validation

## 🔍 Debugging & Monitoring

### Log Locations
- **Next.js Logs**: Console output during `npm run dev`
- **Python Script Logs**: `/scripts/logs/` directory
- **Database Queries**: Enable SQLite logging for debugging

### Common Issues
1. **Database Connection**: Check `DATABASE_PATH` in `.env`
2. **Facebook Webhook**: Verify webhook URL and validation
3. **API Errors**: Check browser network tab for API responses
4. **Component Rendering**: Use React DevTools for debugging

## 📚 Further Exploration Areas

1. **Advanced Analytics** - Dive into conversion funnel analysis
2. **Email Automation** - Explore email template system
3. **Mobile Responsiveness** - Review mobile UI components
4. **Performance Optimization** - Database query optimization
5. **Testing Strategy** - Unit and integration test setup

---

*This guide is for personal reference and understanding. Keep it updated as you explore more of the codebase!*
