# Testing Guide

This directory contains Jest tests for the AI Bootcamp Dashboard application.

## Test Structure

```
__tests__/
├── api/                    # API route tests
│   ├── facebook/          # Facebook API tests
│   │   ├── accounts.test.js
│   │   ├── campaigns.test.js
│   │   └── lead-ads.test.js
│   ├── customers.test.js
│   ├── workshops.test.js
│   └── leads.test.js
├── pages/                 # Page component tests
│   ├── index.test.js
│   └── login.test.js
├── components/            # Component tests
│   ├── MetricsCard.test.js
│   └── SearchableTable.test.js
└── utils/                 # Test utilities
    └── testHelpers.js
```

## Running Tests

### Basic Commands

```bash
# Run all tests once
npm test

# Run tests in watch mode (re-runs on file changes)
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run tests for CI/CD (no watch, with coverage)
npm run test:ci
```

### Running Specific Tests

```bash
# Run tests for a specific file
npm test customers.test.js

# Run tests matching a pattern
npm test api/facebook

# Run tests for a specific component
npm test MetricsCard
```

## Test Configuration

- **Jest Config**: `jest.config.js` - Main Jest configuration
- **Setup File**: `jest.setup.js` - Global test setup and mocks
- **Test Environment**: jsdom (for React component testing)

## Test Utilities

The `testHelpers.js` file provides utilities for testing:

- `createApiMocks()` - Mock HTTP request/response for API testing
- `expectApiResponse()` - Assert API response format and status
- `expectApiError()` - Assert API error responses
- Sample data objects for consistent testing

## API Testing

API tests use node-mocks-http to simulate HTTP requests:

```javascript
import { createApiMocks, expectApiResponse } from '../utils/testHelpers'

const { req, res } = createApiMocks('GET', { id: '1' })
await handler(req, res)
const response = expectApiResponse(res, 200)
```

## Component Testing

Component tests use React Testing Library:

```javascript
import { render, screen, fireEvent } from '@testing-library/react'

render(<MyComponent prop="value" />)
expect(screen.getByText('Expected Text')).toBeInTheDocument()
```

## Mocking Strategy

- **Database**: Mocked using Jest mocks for sqlite3 and sqlite
- **Next.js Router**: Mocked in jest.setup.js
- **API Dependencies**: Mocked per test file as needed

## Coverage Reports

Coverage reports are generated in the `coverage/` directory and include:
- HTML report: `coverage/lcov-report/index.html`
- Text summary in terminal
- Coverage data for CI tools

## Best Practices

1. **Descriptive test names** - Clearly describe what is being tested
2. **Arrange, Act, Assert** - Structure tests clearly
3. **Mock external dependencies** - Keep tests isolated
4. **Test edge cases** - Include error conditions and boundary cases
5. **Clean up** - Reset mocks between tests

## Troubleshooting

### Common Issues

1. **Module not found errors**: Check that all imports are correctly mocked
2. **Database connection errors**: Ensure database mocks are properly configured
3. **Component rendering errors**: Verify all dependencies are mocked

### Debugging Tests

```bash
# Run tests with verbose output
npm test -- --verbose

# Run a single test file with debugging
npm test -- --testNamePattern="specific test name"
```

## Future Improvements

- [ ] Add integration tests for complete user flows
- [ ] Add visual regression testing
- [ ] Increase test coverage to 90%+
- [ ] Add performance testing for heavy components
- [ ] Set up automated testing in CI/CD pipeline