import handler from '../../pages/api/customers'
import { createAuthenticatedApiMocks, expectApiResponse, expectApiError, sampleCustomer, resetMocks } from '../utils/testHelpers'

// Mock sqlite
jest.mock('sqlite', () => ({
  open: jest.fn(() => ({
    get: jest.fn(),
    all: jest.fn(),
    run: jest.fn(),
    close: jest.fn(),
  })),
}))


describe('/api/customers', () => {
  beforeEach(() => {
    resetMocks()
  })

  it('should return all customers for GET request without ID', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET')
    
    const { open } = require('sqlite')
    const mockDb = {
      all: jest.fn(),
      get: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    // Mock the main customers query
    mockDb.all.mockResolvedValueOnce([sampleCustomer])
    
    // Mock the stats query
    mockDb.get.mockResolvedValueOnce({
      total_customers: 1,
      completed_customers: 0,
      certified_customers: 0,
      total_revenue_leads: 299,
      total_revenue_payments: 299,
      avg_order_value: 299
    })
    
    // Mock the workshop breakdown query
    mockDb.all.mockResolvedValueOnce([{
      workshop_type: 'AI_Essentials',
      customer_count: 1,
      revenue: 299
    }])

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response).toHaveProperty('customers')
    expect(response).toHaveProperty('stats')
    expect(response).toHaveProperty('workshopBreakdown')
    expect(response.customers).toBeInstanceOf(Array)
    expect(response.customers).toHaveLength(1)
  })

  it('should return specific customer details for GET request with ID', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { id: '1' })
    
    const { open } = require('sqlite')
    const mockDb = {
      get: jest.fn(),
      all: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    // Mock customer details query
    mockDb.get.mockResolvedValueOnce(sampleCustomer)
    
    // Mock payments query
    mockDb.all.mockResolvedValueOnce([{
      id: 1,
      amount: 299,
      payment_date: '2023-01-01',
      workshop_name: 'AI Essentials'
    }])
    
    // Mock workshops query
    mockDb.all.mockResolvedValueOnce([{
      id: 1,
      name: 'AI Essentials',
      enrolled_date: '2023-01-01'
    }])

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response).toHaveProperty('customer')
    expect(response).toHaveProperty('payments')
    expect(response).toHaveProperty('workshops')
    expect(response).toHaveProperty('summary')
    expect(response.customer.id).toBe(1)
  })

  it('should return 404 for non-existent customer', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { id: '999' })
    
    const { open } = require('sqlite')
    const mockDb = {
      get: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    mockDb.get.mockResolvedValueOnce(null)

    await handler(req, res)

    expectApiError(res, 404, 'Customer not found')
  })

  it('should handle PUT request for customer updates', async () => {
    const updateData = { full_name: 'Jane Doe', email: '<EMAIL>' }
    const { req, res } = createAuthenticatedApiMocks('PUT', { id: '1' }, updateData)
    
    const { open } = require('sqlite')
    const mockDb = {
      run: jest.fn(),
      get: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    mockDb.run.mockResolvedValueOnce({ changes: 1 })
    mockDb.get.mockResolvedValueOnce({ ...sampleCustomer, ...updateData })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response.full_name).toBe('Jane Doe')
    expect(response.email).toBe('<EMAIL>')
  })

  it('should filter customers by status', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { status: 'Paid' })
    
    const { open } = require('sqlite')
    const mockDb = {
      all: jest.fn(),
      get: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    mockDb.all.mockResolvedValueOnce([{ ...sampleCustomer, payment_status: 'Paid' }])
    mockDb.get.mockResolvedValueOnce({ total_customers: 1 })
    mockDb.all.mockResolvedValueOnce([])

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    expect(response.customers[0].payment_status).toBe('Paid')
  })

  it('should handle search functionality', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { search: 'John' })
    
    const { open } = require('sqlite')
    const mockDb = {
      all: jest.fn(),
      get: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    mockDb.all.mockResolvedValueOnce([sampleCustomer])
    mockDb.get.mockResolvedValueOnce({ total_customers: 1 })
    mockDb.all.mockResolvedValueOnce([])

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    expect(response.customers).toHaveLength(1)
  })

  it('should reject unsupported methods', async () => {
    const { req, res } = createAuthenticatedApiMocks('DELETE')

    await handler(req, res)

    expectApiError(res, 405, 'Method not allowed')
  })
})