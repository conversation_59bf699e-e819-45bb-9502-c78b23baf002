import handler from '../../../pages/api/facebook/accounts'
import { createAuthenticatedApiMocks, expectApiResponse, expectApiError, sampleFacebookAccount, resetMocks } from '../../utils/testHelpers'

// Mock sqlite3
jest.mock('sqlite3', () => ({
  Database: jest.fn(() => ({
    get: jest.fn(),
    all: jest.fn(),
    close: jest.fn(),
  })),
}))

// Mock middleware to avoid complex async chains during tests
jest.mock('../../../lib/auth/middleware.js', () => ({
  requireDataAccess: (permission) => (handler) => handler,
  rateLimit: (requests, windowMs) => (handler) => handler,
  sanitizeGDPRData: (data) => data
}))

// Mock the database config

describe.skip('/api/facebook/accounts', () => {
  beforeEach(() => {
    resetMocks()
  })

  it('should return Facebook accounts with summary for GET request', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { timePeriod: '7' })
    
    // Mock database calls
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.get.mockImplementationOnce((query, params, callback) => {
      // Mock exchange rate query
      callback(null, { rate: 83.2 })
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      // Mock accounts query
      callback(null, [sampleFacebookAccount])
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response).toHaveProperty('success', true)
    expect(response).toHaveProperty('accounts')
    expect(response).toHaveProperty('summary')
    expect(response.accounts).toBeInstanceOf(Array)
    expect(response.summary).toHaveProperty('total_accounts')
    expect(response.summary).toHaveProperty('total_leads_7d')
  })

  it('should filter accounts with 0 leads when filtering enabled', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { timePeriod: '7' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.get.mockImplementationOnce((query, params, callback) => {
      callback(null, { rate: 83.2 })
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      // Return account with 0 leads
      callback(null, [{ ...sampleFacebookAccount, leads_7d: 0 }])
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response.accounts).toHaveLength(0) // Should be filtered out
    expect(response.summary.total_accounts).toBe(0)
  })

  it('should handle database errors gracefully', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { timePeriod: '7' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.get.mockImplementationOnce((query, params, callback) => {
      callback(new Error('Database connection failed'), null)
    })

    await handler(req, res)

    expectApiError(res, 500)
  })

  it('should reject non-GET methods', async () => {
    const { req, res } = createAuthenticatedApiMocks('POST')

    await handler(req, res)

    expectApiError(res, 405, 'Method not allowed')
  })

  it('should handle different time periods', async () => {
    const timePeriods = ['1', '7', '30']
    
    for (const period of timePeriods) {
      const { req, res } = createAuthenticatedApiMocks('GET', { timePeriod: period })
      
      const sqlite3 = require('sqlite3')
      const mockDb = new sqlite3.Database()
      
      mockDb.get.mockImplementationOnce((query, params, callback) => {
        callback(null, { rate: 83.2 })
      })
      
      mockDb.all.mockImplementationOnce((query, params, callback) => {
        callback(null, [{ ...sampleFacebookAccount, [`leads_${period}d`]: 25 }])
      })

      await handler(req, res)

      const response = expectApiResponse(res, 200)
      expect(response.summary).toHaveProperty(`total_leads_${period}d`)
    }
  })
})