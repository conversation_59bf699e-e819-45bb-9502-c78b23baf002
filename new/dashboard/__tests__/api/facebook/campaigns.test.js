import handler from '../../../pages/api/facebook/campaigns'
import { createAuthenticatedApiMocks, expectApiResponse, expectApiError, resetMocks } from '../../utils/testHelpers'

jest.mock('sqlite3', () => ({
  Database: jest.fn(() => ({
    get: jest.fn(),
    all: jest.fn(),
    close: jest.fn(),
  })),
}))


// Mock middleware to avoid complex async chains during tests
jest.mock('../../../lib/auth/middleware.js', () => ({
  requireDataAccess: (permission) => (handler) => handler,
  rateLimit: (requests, windowMs) => (handler) => handler,
  sanitizeGDPRData: (data) => data
}))

const sampleCampaign = {
  id: "120229544404360103",
  name: "Test_Campaign",
  campaign_id: "120229544404360103",
  campaign_name: "Test_Campaign",
  account_name: "Test_Account",
  account_id: "****************",
  currency: "USD",
  impressions: 10000,
  clicks: 500,
  leads: 25,
  spend: 250.00,
  cost_per_lead: 10.0,
  ctr: 5.0,
  performance_score: 95.0
}

describe.skip('/api/facebook/campaigns', () => {
  beforeEach(() => {
    resetMocks()
  })

  it('should return Facebook campaigns with summary for GET request', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { timePeriod: '7' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.get.mockImplementationOnce((query, params, callback) => {
      callback(null, { rate: 83.2 })
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [sampleCampaign])
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response).toHaveProperty('success', true)
    expect(response).toHaveProperty('campaigns')
    expect(response).toHaveProperty('summary')
    expect(response.campaigns).toBeInstanceOf(Array)
    expect(response.campaigns[0]).toHaveProperty('leads')
    expect(response.summary).toHaveProperty('total_campaigns')
  })

  it('should filter campaigns with 0 leads', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { timePeriod: '7' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.get.mockImplementationOnce((query, params, callback) => {
      callback(null, { rate: 83.2 })
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [{ ...sampleCampaign, leads: 0 }])
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    expect(response.campaigns).toHaveLength(0)
  })

  it('should handle currency conversion correctly', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { timePeriod: '7' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.get.mockImplementationOnce((query, params, callback) => {
      callback(null, { rate: 80.0 })
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [{ ...sampleCampaign, spend: 100.00 }])
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    expect(response.campaigns[0].spend_inr).toBe(8000) // 100 * 80
  })

  it('should reject non-GET methods', async () => {
    const { req, res } = createAuthenticatedApiMocks('POST')

    await handler(req, res)

    expectApiError(res, 405, 'Method not allowed')
  })
})