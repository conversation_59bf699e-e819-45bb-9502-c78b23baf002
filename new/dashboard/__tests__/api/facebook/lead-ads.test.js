import handler from '../../../pages/api/facebook/lead-ads'
import { createAuthenticatedApiMocks, expectApiResponse, expectApiError, resetMocks } from '../../utils/testHelpers'

jest.mock('sqlite3', () => ({
  Database: jest.fn(() => ({
    get: jest.fn(),
    all: jest.fn(),
    close: jest.fn(),
  })),
}))


// Mock middleware to avoid complex async chains during tests
jest.mock('../../../lib/auth/middleware.js', () => ({
  requireDataAccess: (permission) => (handler) => handler,
  rateLimit: (requests, windowMs) => (handler) => handler,
  sanitizeGDPRData: (data) => data
}))

const sampleLeadAd = {
  ad_id: "120229708613370103",
  ad_name: "Test Lead Ad",
  account_name: "Test_Account",
  campaign_id: "120229544404360103",
  campaign_name: "Test_Campaign",
  campaign_objective: "OUTCOME_LEADS",
  ad_status: "ACTIVE",
  questionnaire_fields: 6,
  cumulative_leads: 100,
  period_leads: 25
}

describe.skip('/api/facebook/lead-ads', () => {
  beforeEach(() => {
    resetMocks()
  })

  it('should return lead ads with summary for GET request', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { timePeriod: '7' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [sampleLeadAd])
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response).toHaveProperty('success', true)
    expect(response).toHaveProperty('lead_ads')
    expect(response).toHaveProperty('summary')
    expect(response.lead_ads).toBeInstanceOf(Array)
    expect(response.summary).toHaveProperty('total_ads')
    expect(response.summary).toHaveProperty('period_leads')
  })

  it('should filter ads with 0 period leads', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { timePeriod: '7' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [{ ...sampleLeadAd, period_leads: 0 }])
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    expect(response.lead_ads).toHaveLength(0)
    expect(response.summary.period_leads).toBe(0)
  })

  it('should handle multiple ads correctly', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { timePeriod: '7' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    const multipleAds = [
      { ...sampleLeadAd, ad_id: "1", period_leads: 10 },
      { ...sampleLeadAd, ad_id: "2", period_leads: 15 },
      { ...sampleLeadAd, ad_id: "3", period_leads: 0 }, // Should be filtered
    ]
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, multipleAds)
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    expect(response.lead_ads).toHaveLength(2) // Only non-zero ads
    expect(response.summary.period_leads).toBe(25) // 10 + 15
  })

  it('should calculate working ads correctly', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { timePeriod: '7' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    const adsWithDifferentStatuses = [
      { ...sampleLeadAd, ad_id: "1", ad_status: "ACTIVE", period_leads: 10 },
      { ...sampleLeadAd, ad_id: "2", ad_status: "PAUSED", period_leads: 5 },
    ]
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, adsWithDifferentStatuses)
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    expect(response.summary.active_ads).toBe(1) // Only ACTIVE
    expect(response.summary.working_ads).toBe(2) // Both have leads
  })

  it('should reject non-GET methods', async () => {
    const { req, res } = createAuthenticatedApiMocks('POST')

    await handler(req, res)

    expectApiError(res, 405, 'Method not allowed')
  })
})