import handler from '../../pages/api/leads'
import { createAuthenticatedApiMocks, expectApiResponse, expectApiError, resetMocks } from '../utils/testHelpers'

jest.mock('sqlite3', () => ({
  Database: jest.fn(() => ({
    get: jest.fn(),
    all: jest.fn(),
    run: jest.fn(),
    close: jest.fn(),
  })),
}))


const sampleLead = {
  id: 1,
  full_name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+**********',
  lead_source: 'fb_ads',
  status: 'New',
  campaign_name: 'Test Campaign',
  account_name: 'Test Account',
  created_time: '2023-01-01T00:00:00Z'
}

// Mock middleware to avoid complex async chains during tests
jest.mock('../../lib/auth/middleware.js', () => ({
  requireDataAccess: (permission) => (handler) => handler,
  rateLimit: (requests, windowMs) => (handler) => handler,
  sanitizeGDPRData: (data) => data
}))

describe.skip('/api/leads', () => {
  beforeEach(() => {
    resetMocks()
  })

  it('should return paginated leads for GET request', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { page: '1', limit: '10' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    // Mock count query
    mockDb.get.mockImplementationOnce((query, params, callback) => {
      callback(null, { total: 25 })
    })
    
    // Mock leads query
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [sampleLead])
    })
    
    // Mock summary query
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [{ lead_source: 'fb_ads', count: 10 }])
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response).toHaveProperty('success', true)
    expect(response).toHaveProperty('leads')
    expect(response).toHaveProperty('pagination')
    expect(response).toHaveProperty('summary')
    expect(response.leads).toBeInstanceOf(Array)
    expect(response.pagination.total_leads).toBe(25)
    expect(response.pagination.total_pages).toBe(3) // ceil(25/10)
  })

  it('should handle search functionality', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { search: 'john' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.get.mockImplementationOnce((query, params, callback) => {
      callback(null, { total: 1 })
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [sampleLead])
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [])
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    expect(response.leads).toHaveLength(1)
    expect(response.filters.search).toBe('john')
  })

  it('should filter leads by source', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { source: 'fb_ads' })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.get.mockImplementationOnce((query, params, callback) => {
      callback(null, { total: 5 })
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [{ ...sampleLead, lead_source: 'fb_ads' }])
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [])
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    expect(response.leads[0].lead_source).toBe('fb_ads')
    expect(response.filters.source).toBe('fb_ads')
  })

  it('should handle POST request for creating new lead', async () => {
    const newLead = {
      product_type: 'ModernAI',
      full_name: 'Jane Doe',
      email: '<EMAIL>',
      lead_source: 'manual',
      phone: '+1987654321'
    }
    
    const { req, res } = createAuthenticatedApiMocks('POST', {}, newLead)
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.run.mockImplementationOnce(function(query, params, callback) {
      callback.call({ lastID: 100 }, null)
    })

    await handler(req, res)

    const response = expectApiResponse(res, 201)
    
    expect(response).toHaveProperty('success', true)
    expect(response).toHaveProperty('lead')
    expect(response.lead.id).toBe(100)
    expect(response.lead.full_name).toBe(newLead.full_name)
  })

  it('should validate required fields for POST request', async () => {
    const invalidLead = {
      full_name: 'Jane Doe',
      // Missing required fields: product_type, email, lead_source
    }
    
    const { req, res } = createAuthenticatedApiMocks('POST', {}, invalidLead)

    await handler(req, res)

    expectApiError(res, 400)
  })

  it('should handle PUT request for updating lead', async () => {
    const updateData = {
      id: 1,
      lead_source: 'updated_source'
    }
    
    const { req, res } = createAuthenticatedApiMocks('PUT', {}, updateData)
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.run.mockImplementationOnce(function(query, params, callback) {
      callback.call({ changes: 1 }, null)
    })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response).toHaveProperty('success', true)
    expect(response.changes).toBe(1)
  })

  it('should handle sorting by different columns', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { 
      sortBy: 'full_name', 
      sortOrder: 'asc' 
    })
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.get.mockImplementationOnce((query, params, callback) => {
      callback(null, { total: 1 })
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      // Verify the query includes proper sorting
      expect(query).toContain('ORDER BY')
      callback(null, [sampleLead])
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [])
    })

    await handler(req, res)

    expectApiResponse(res, 200)
  })

  it('should exclude customers (paid leads) from results', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET')
    
    const sqlite3 = require('sqlite3')
    const mockDb = new sqlite3.Database()
    
    mockDb.get.mockImplementationOnce((query, params, callback) => {
      // Verify the query excludes paid customers
      expect(query).toContain('payment_status')
      callback(null, { total: 1 })
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [sampleLead])
    })
    
    mockDb.all.mockImplementationOnce((query, params, callback) => {
      callback(null, [])
    })

    await handler(req, res)

    expectApiResponse(res, 200)
  })

  it('should reject unsupported methods', async () => {
    const { req, res } = createAuthenticatedApiMocks('DELETE')

    await handler(req, res)

    expectApiError(res, 405, 'Method not allowed')
  })
})