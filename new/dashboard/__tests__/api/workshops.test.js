import handler from '../../pages/api/workshops'
import { createAuthenticatedApiMocks, expectApiResponse, expectApiError, sampleWorkshop, resetMocks } from '../utils/testHelpers'

jest.mock('sqlite', () => ({
  open: jest.fn(() => ({
    get: jest.fn(),
    all: jest.fn(),
    run: jest.fn(),
    close: jest.fn(),
  })),
}))


describe('/api/workshops', () => {
  beforeEach(() => {
    resetMocks()
  })

  it('should return all workshops for GET request without ID', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET')
    
    const { open } = require('sqlite')
    const mockDb = {
      all: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    mockDb.all.mockResolvedValueOnce([sampleWorkshop])

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response).toBeInstanceOf(Array)
    expect(response).toHaveLength(1)
    expect(response[0]).toHaveProperty('enrolled_count')
    expect(response[0]).toHaveProperty('total_revenue')
  })

  it('should return specific workshop with students for GET request with ID', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { id: '1' })
    
    const { open } = require('sqlite')
    const mockDb = {
      get: jest.fn(),
      all: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    // Mock workshop details query
    mockDb.get.mockResolvedValueOnce(sampleWorkshop)
    
    // Mock students query
    mockDb.all.mockResolvedValueOnce([{
      id: 1,
      full_name: 'John Doe',
      email: '<EMAIL>',
      payment_status: 'Paid',
      enrolled_date: '2023-01-01'
    }])

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response).toHaveProperty('workshop')
    expect(response).toHaveProperty('students')
    expect(response.workshop.id).toBe(1)
    expect(response.students).toBeInstanceOf(Array)
  })

  it('should return 404 for non-existent workshop', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET', { id: '999' })
    
    const { open } = require('sqlite')
    const mockDb = {
      get: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    mockDb.get.mockResolvedValueOnce(null)

    await handler(req, res)

    expectApiError(res, 404, 'Workshop not found')
  })

  it('should handle POST request for creating new workshop', async () => {
    const newWorkshop = {
      name: 'New AI Workshop',
      type: 'L2',
      description: 'Advanced AI concepts',
      start_date: '2023-06-01',
      end_date: '2023-06-02',
      instructor: 'Jane Smith',
      price_usd: 399
    }
    
    const { req, res } = createAuthenticatedApiMocks('POST', {}, newWorkshop)
    
    const { open } = require('sqlite')
    const mockDb = {
      run: jest.fn(),
      get: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    mockDb.run.mockResolvedValueOnce({ lastID: 2 })
    mockDb.get.mockResolvedValueOnce({ id: 2, ...newWorkshop })

    await handler(req, res)

    const response = expectApiResponse(res, 201)
    
    expect(response.id).toBe(2)
    expect(response.name).toBe(newWorkshop.name)
    expect(response.price_usd).toBe(newWorkshop.price_usd)
  })

  it('should handle PUT request for updating workshop', async () => {
    const updateData = { 
      name: 'Updated Workshop Name',
      price_usd: 449 
    }
    
    const { req, res } = createAuthenticatedApiMocks('PUT', { id: '1' }, updateData)
    
    const { open } = require('sqlite')
    const mockDb = {
      run: jest.fn(),
      get: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    mockDb.run.mockResolvedValueOnce({ changes: 1 })
    mockDb.get.mockResolvedValueOnce({ ...sampleWorkshop, ...updateData })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response.name).toBe(updateData.name)
    expect(response.price_usd).toBe(updateData.price_usd)
  })

  it('should handle DELETE request for deactivating workshop', async () => {
    const { req, res } = createAuthenticatedApiMocks('DELETE', { id: '1' })
    
    const { open } = require('sqlite')
    const mockDb = {
      run: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    mockDb.run.mockResolvedValueOnce({ changes: 1 })

    await handler(req, res)

    const response = expectApiResponse(res, 200)
    
    expect(response).toHaveProperty('message', 'Workshop deactivated successfully')
  })

  it('should handle database errors gracefully', async () => {
    const { req, res } = createAuthenticatedApiMocks('GET')
    
    const { open } = require('sqlite')
    const mockDb = {
      all: jest.fn(),
      close: jest.fn(),
    }
    open.mockResolvedValue(mockDb)
    
    mockDb.all.mockRejectedValueOnce(new Error('Database error'))

    await handler(req, res)

    expectApiError(res, 500, 'Failed to fetch workshops')
  })

  it('should reject unsupported methods', async () => {
    const { req, res } = createAuthenticatedApiMocks('PATCH')

    await handler(req, res)

    expectApiError(res, 405, 'Method not allowed')
  })
})