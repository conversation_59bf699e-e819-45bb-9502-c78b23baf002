import { render, screen } from '@testing-library/react'
import MetricsCard from '../../components/MetricsCard'

describe('MetricsCard Component', () => {
  const defaultProps = {
    title: 'Test Metric',
    value: '1,234',
    change: '+12.5%',
    changeType: 'positive'
  }

  it('renders without crashing', () => {
    render(<MetricsCard {...defaultProps} />)
    
    expect(screen.getByText('Test Metric')).toBeInTheDocument()
    expect(screen.getByText('1,234')).toBeInTheDocument()
    expect(screen.getByText('+12.5%')).toBeInTheDocument()
  })

  it('displays the correct title', () => {
    render(<MetricsCard {...defaultProps} title="Custom Title" />)
    
    expect(screen.getByText('Custom Title')).toBeInTheDocument()
  })

  it('displays the correct value', () => {
    render(<MetricsCard {...defaultProps} value="5,678" />)
    
    expect(screen.getByText('5,678')).toBeInTheDocument()
  })

  it('displays positive change correctly', () => {
    render(<MetricsCard {...defaultProps} change="+25%" changeType="positive" />)
    
    const changeElement = screen.getByText('+25%')
    expect(changeElement).toBeInTheDocument()
    // Could add className checks here if styling classes are applied
  })

  it('displays negative change correctly', () => {
    render(<MetricsCard {...defaultProps} change="-15%" changeType="negative" />)
    
    const changeElement = screen.getByText('-15%')
    expect(changeElement).toBeInTheDocument()
  })

  it('handles missing change prop', () => {
    const propsWithoutChange = {
      title: 'Test Metric',
      value: '1,234'
    }
    
    render(<MetricsCard {...propsWithoutChange} />)
    
    expect(screen.getByText('Test Metric')).toBeInTheDocument()
    expect(screen.getByText('1,234')).toBeInTheDocument()
  })

  it('handles zero values', () => {
    render(<MetricsCard {...defaultProps} value="0" />)
    
    expect(screen.getByText('0')).toBeInTheDocument()
  })

  it('handles large numbers', () => {
    render(<MetricsCard {...defaultProps} value="1,234,567" />)
    
    expect(screen.getByText('1,234,567')).toBeInTheDocument()
  })
})