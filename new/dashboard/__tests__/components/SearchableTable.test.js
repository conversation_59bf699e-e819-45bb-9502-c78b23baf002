import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import SearchableTable from '../../components/SearchableTable'

const mockData = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', status: 'Active' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', status: 'Inactive' },
  { id: 3, name: '<PERSON>', email: '<EMAIL>', status: 'Active' },
]

const mockColumns = [
  { key: 'name', label: 'Name' },
  { key: 'email', label: 'Email' },
  { key: 'status', label: 'Status' },
]

describe('SearchableTable Component', () => {
  const defaultProps = {
    data: mockData,
    columns: mockColumns,
    searchPlaceholder: 'Search users...',
  }

  it('renders without crashing', () => {
    render(<SearchableTable {...defaultProps} />)
    
    expect(screen.getByPlaceholderText('Search users...')).toBeInTheDocument()
  })

  it('displays all data initially', () => {
    render(<SearchableTable {...defaultProps} />)
    
    expect(screen.getByText('<PERSON>')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    expect(screen.getByText('Bob Johnson')).toBeInTheDocument()
  })

  it('displays column headers', () => {
    render(<SearchableTable {...defaultProps} />)
    
    expect(screen.getByText('Name')).toBeInTheDocument()
    expect(screen.getByText('Email')).toBeInTheDocument()
    expect(screen.getByText('Status')).toBeInTheDocument()
  })

  it('filters data based on search input', async () => {
    const user = userEvent.setup()
    render(<SearchableTable {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText('Search users...')
    await user.type(searchInput, 'John')
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
      expect(screen.queryByText('Bob Johnson')).not.toBeInTheDocument()
    })
  })

  it('shows no results when search matches nothing', async () => {
    const user = userEvent.setup()
    render(<SearchableTable {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText('Search users...')
    await user.type(searchInput, 'NonexistentUser')
    
    await waitFor(() => {
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
      expect(screen.queryByText('Bob Johnson')).not.toBeInTheDocument()
    })
  })

  it('is case insensitive when searching', async () => {
    const user = userEvent.setup()
    render(<SearchableTable {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText('Search users...')
    await user.type(searchInput, 'JOHN')
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
    })
  })

  it('searches across multiple columns', async () => {
    const user = userEvent.setup()
    render(<SearchableTable {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText('Search users...')
    await user.type(searchInput, 'jane@example')
    
    await waitFor(() => {
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
    })
  })

  it('clears search and shows all data when input is empty', async () => {
    const user = userEvent.setup()
    render(<SearchableTable {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText('Search users...')
    
    // First search for something
    await user.type(searchInput, 'John')
    await waitFor(() => {
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
    })
    
    // Then clear the search
    await user.clear(searchInput)
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      expect(screen.getByText('Bob Johnson')).toBeInTheDocument()
    })
  })

  it('handles empty data gracefully', () => {
    render(<SearchableTable {...defaultProps} data={[]} />)
    
    expect(screen.getByPlaceholderText('Search users...')).toBeInTheDocument()
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
  })
})