// Sample data for testing

export const sampleUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    role: 'admin',
    first_name: '<PERSON>',
    last_name: 'Admin'
  },
  {
    id: 2,
    email: '<EMAIL>',
    role: 'user',
    first_name: '<PERSON>',
    last_name: 'User'
  }
]

export const sampleLeads = [
  {
    id: 1,
    full_name: 'Test Lead',
    email: '<EMAIL>',
    phone: '+1234567890',
    lead_source: 'fb_ads',
    status: 'New',
    created_time: '2023-01-01T00:00:00Z'
  }
]

export const sampleCustomers = [
  {
    id: 1,
    full_name: 'Test Customer',
    email: '<EMAIL>',
    payment_status: 'Paid',
    payment_amount: 299,
    workshop_type: 'AI_Essentials'
  }
]

export const sampleWorkshops = [
  {
    id: 1,
    name: 'AI Essentials Test',
    type: 'L1',
    instructor: 'Test Instructor',
    price_usd: 299,
    enrolled_count: 25
  }
]

export const sampleFacebookData = {
  account: {
    id: "****************",
    name: "Test_Account",
    accessible: true,
    leads_7d: 50,
    spend_7d: 250
  },
  campaign: {
    id: "120229544404360103",
    name: "Test_Campaign",
    leads: 25,
    spend: 125
  },
  leadAd: {
    ad_id: "120229708613370103",
    ad_name: "Test Lead Ad",
    period_leads: 10,
    cumulative_leads: 100
  }
}