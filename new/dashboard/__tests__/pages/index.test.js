import { render, screen } from '@testing-library/react'
import Home from '../../pages/index'

// Mock the ProtectedRoute component
jest.mock('../../components/auth/ProtectedRoute', () => {
  return function MockProtectedRoute({ children }) {
    return <div data-testid="protected-route">{children}</div>
  }
})

// Mock the WelcomeDashboard component
jest.mock('../../components/WelcomeDashboard', () => {
  return function MockWelcomeDashboard() {
    return <div data-testid="welcome-dashboard">Welcome Dashboard Content</div>
  }
})

describe('Home Page', () => {
  it('renders without crashing', () => {
    render(<Home />)
    
    expect(screen.getByTestId('protected-route')).toBeInTheDocument()
    expect(screen.getByTestId('welcome-dashboard')).toBeInTheDocument()
  })

  it('shows welcome dashboard content', () => {
    render(<Home />)
    
    expect(screen.getByText('Welcome Dashboard Content')).toBeInTheDocument()
  })

  it('is wrapped in ProtectedRoute for authentication', () => {
    render(<Home />)
    
    const protectedRoute = screen.getByTestId('protected-route')
    expect(protectedRoute).toBeInTheDocument()
    expect(protectedRoute).toContainElement(screen.getByTestId('welcome-dashboard'))
  })
})