import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Login from '../../pages/login'

// Mock AuthContext
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({
    user: null,
    isAuthenticated: false,
    isHydrated: true,
    login: jest.fn(),
    logout: jest.fn(),
    register: jest.fn(),
    forgotPassword: jest.fn(),
    resetPassword: jest.fn(),
    verifyEmail: jest.fn(),
    resendVerification: jest.fn(),
  }),
  AuthProvider: ({ children }) => children,
}))

// Mock Next.js router
const mockPush = jest.fn()
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: mockPush,
    query: {},
  }),
}))

// Mock the Login component
jest.mock('../../components/auth/Login', () => {
  return function MockLoginComponent() {
    return (
      <div data-testid="login-component">
        <h1>Login</h1>
        <form data-testid="login-form">
          <input 
            type="email" 
            placeholder="Email"
            data-testid="email-input"
          />
          <input 
            type="password" 
            placeholder="Password"
            data-testid="password-input"
          />
          <button type="submit" data-testid="login-button">
            Sign In
          </button>
        </form>
      </div>
    )
  }
})

describe('Login Page', () => {
  beforeEach(() => {
    mockPush.mockClear()
  })

  it('renders login page without crashing', () => {
    render(<Login />)
    
    expect(screen.getByTestId('login-component')).toBeInTheDocument()
    expect(screen.getByRole('heading', { name: /login/i })).toBeInTheDocument()
  })

  it('displays login form elements', () => {
    render(<Login />)
    
    expect(screen.getByTestId('email-input')).toBeInTheDocument()
    expect(screen.getByTestId('password-input')).toBeInTheDocument()
    expect(screen.getByTestId('login-button')).toBeInTheDocument()
  })

  it('has proper form structure', () => {
    render(<Login />)
    
    const form = screen.getByTestId('login-form')
    const emailInput = screen.getByTestId('email-input')
    const passwordInput = screen.getByTestId('password-input')
    const loginButton = screen.getByTestId('login-button')
    
    expect(form).toContainElement(emailInput)
    expect(form).toContainElement(passwordInput)
    expect(form).toContainElement(loginButton)
  })

  it('allows user interaction with form elements', async () => {
    const user = userEvent.setup()
    render(<Login />)
    
    const emailInput = screen.getByTestId('email-input')
    const passwordInput = screen.getByTestId('password-input')
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')
    
    expect(emailInput).toHaveValue('<EMAIL>')
    expect(passwordInput).toHaveValue('password123')
  })
})