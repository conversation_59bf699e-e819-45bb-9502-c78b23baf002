// Sample API test demonstrating the test setup

import { createApiMocks, expectApiResponse, expectApiError } from './utils/testHelpers'

// Mock a simple API handler
const sampleHandler = async (req, res) => {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }
  
  const { name } = req.query
  if (!name) {
    return res.status(400).json({ error: 'Name parameter is required' })
  }
  
  return res.status(200).json({ 
    success: true, 
    message: `Hello, ${name}!`,
    timestamp: new Date().toISOString()
  })
}

describe('Sample API Handler', () => {
  it('should return greeting for valid GET request', async () => {
    const { req, res } = createApiMocks('GET', { name: 'John' })
    
    await sampleHandler(req, res)
    
    const response = expectApiResponse(res, 200)
    expect(response.success).toBe(true)
    expect(response.message).toBe('Hello, <PERSON>!')
    expect(response.timestamp).toBeDefined()
  })
  
  it('should return error for missing name parameter', async () => {
    const { req, res } = createApiMocks('GET')
    
    await sampleHandler(req, res)
    
    expectApiError(res, 400, 'Name parameter is required')
  })
  
  it('should reject non-GET methods', async () => {
    const { req, res } = createApiMocks('POST', { name: 'John' })
    
    await sampleHandler(req, res)
    
    expectApiError(res, 405, 'Method not allowed')
  })
  
  it('should handle special characters in name', async () => {
    const { req, res } = createApiMocks('GET', { name: 'José María' })
    
    await sampleHandler(req, res)
    
    const response = expectApiResponse(res, 200)
    expect(response.message).toBe('Hello, José María!')
  })
})