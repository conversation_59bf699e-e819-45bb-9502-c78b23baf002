// Basic setup test to verify Jest configuration

describe('Test Setup', () => {
  it('should run basic JavaScript tests', () => {
    expect(1 + 1).toBe(2)
    expect('hello').toBe('hello')
  })

  it('should have access to Jest globals', () => {
    expect(jest).toBeDefined()
    expect(expect).toBeDefined()
    expect(describe).toBeDefined()
    expect(it).toBeDefined()
  })

  it('should have testing library matchers available', () => {
    const element = document.createElement('div')
    element.textContent = 'Hello World'
    document.body.appendChild(element)
    
    expect(element).toBeInTheDocument()
  })
})