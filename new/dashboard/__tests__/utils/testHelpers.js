import { createMocks } from 'node-mocks-http'
import React from 'react'
import { render } from '@testing-library/react'

// Mock the entire AuthContext module
jest.mock('../../contexts/AuthContext', () => {
  const mockAuthContext = {
    user: null,
    isAuthenticated: false,
    isHydrated: true,
    login: jest.fn(),
    logout: jest.fn(),
    register: jest.fn(),
    forgotPassword: jest.fn(),
    resetPassword: jest.fn(),
    verifyEmail: jest.fn(),
    resendVerification: jest.fn(),
  }

  return {
    AuthProvider: ({ children }) => children,
    useAuth: () => mockAuthContext,
  }
})

// Test JWT secret - matches the one used in middleware
const TEST_JWT_SECRET = 'your-super-secret-jwt-key-change-in-production'

// Generate test JWT token
export function createTestToken(payload = {}) {
  const jwt = require('jsonwebtoken')
  const defaultPayload = {
    userId: 1,
    username: 'testuser',
    email: '<EMAIL>',
    role: 'admin'
  }
  return jwt.sign({ ...defaultPayload, ...payload }, TEST_JWT_SECRET, { expiresIn: '1h' })
}

export function createApiMocks(method = 'GET', query = {}, body = {}, headers = {}) {
  return createMocks({
    method,
    query,
    body,
    headers,
  })
}

// Create authenticated API mocks with JWT token
export function createAuthenticatedApiMocks(method = 'GET', query = {}, body = {}, userPayload = {}) {
  const token = createTestToken(userPayload)
  return createMocks({
    method,
    query,
    body,
    headers: {
      authorization: `Bearer ${token}`,
    },
  })
}

export function expectApiResponse(res, expectedStatus = 200) {
  expect(res._getStatusCode()).toBe(expectedStatus)
  return JSON.parse(res._getData())
}

export function expectApiError(res, expectedStatus = 500, expectedMessage = null) {
  expect(res._getStatusCode()).toBe(expectedStatus)
  const data = JSON.parse(res._getData())
  expect(data).toHaveProperty('error')
  if (expectedMessage) {
    expect(data.error).toBe(expectedMessage)
  }
  return data
}

export const mockDatabase = {
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn(),
  close: jest.fn(),
}

export const mockSqlite3 = {
  Database: jest.fn(() => mockDatabase),
}

export function resetMocks() {
  Object.values(mockDatabase).forEach(mock => mock.mockClear())
}

export const sampleFacebookAccount = {
  id: "****************",
  name: "Test_Account",
  status: "1",
  accessible: true,
  leads_7d: 50,
  cost_per_lead: 5.0,
  conversion_rate: "20.0",
  campaigns_active: 2,
  ads_active: 3,
  health_status: "healthy",
  currency: "USD",
  impressions_7d: 10000,
  clicks_7d: 500,
  spend_7d: 250.00,
  ctr_7d: "5.0"
}

export const sampleCustomer = {
  id: 1,
  full_name: "John Doe",
  email: "<EMAIL>",
  phone: "+**********",
  workshop_type: "AI_Essentials",
  payment_status: "Paid",
  payment_amount: 299,
  total_spent_stripe: 299,
  payment_count: 1
}

export const sampleWorkshop = {
  id: 1,
  name: "AI Essentials - Test Workshop",
  type: "L1",
  description: "Test workshop description",
  instructor: "Test Instructor",
  price_usd: 299,
  enrolled_count: 25,
  completed_count: 20,
  total_revenue: 7475
}

// Custom render function for component tests
export const renderWithAuth = (ui, options = {}) => {
  return render(ui, options)
}