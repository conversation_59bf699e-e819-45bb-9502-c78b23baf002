import React, { useEffect, useState } from "react";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const BarChart = ({ 
  question = "Which algorithm is foundational for modern NLP models?", 
  choices = ["RNN", "Transformer", "CNN", "SVM"],
  initialVotes = null,
  autoUpdate = false,
  onVoteUpdate = null 
}) => {
  const [votes, setVotes] = useState(initialVotes || new Array(choices.length).fill(0));

  // Function to simulate votes (for demo purposes)
  const simulateVotes = () => {
    const newVotes = [...votes];
    const voteIndex = Math.floor(Math.random() * choices.length);
    newVotes[voteIndex]++;
    setVotes(newVotes);
    
    if (onVoteUpdate) {
      onVoteUpdate(newVotes);
    }
  };

  useEffect(() => {
    if (autoUpdate) {
      const intervalId = setInterval(simulateVotes, 2000); // Simulate a vote every 2 seconds
      return () => clearInterval(intervalId);
    }
  }, [votes, autoUpdate]);

  const data = {
    labels: choices,
    datasets: [
      {
        label: "Votes",
        data: votes,
        backgroundColor: [
          "rgba(147, 51, 234, 0.2)", // Purple
          "rgba(59, 130, 246, 0.2)",  // Blue
          "rgba(16, 185, 129, 0.2)",  // Green
          "rgba(245, 158, 11, 0.2)",  // Yellow
          "rgba(239, 68, 68, 0.2)",   // Red
          "rgba(168, 85, 247, 0.2)",  // Violet
        ],
        borderColor: [
          "rgba(147, 51, 234, 1)",
          "rgba(59, 130, 246, 1)", 
          "rgba(16, 185, 129, 1)",
          "rgba(245, 158, 11, 1)",
          "rgba(239, 68, 68, 1)",
          "rgba(168, 85, 247, 1)",
        ],
        borderWidth: 2,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: question,
        font: {
          size: 16,
          weight: 'bold',
        },
        color: '#374151',
        padding: {
          bottom: 20,
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(147, 51, 234, 1)',
        borderWidth: 1,
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
          color: '#6B7280',
        },
        grid: {
          color: 'rgba(156, 163, 175, 0.2)',
        }
      },
      x: {
        ticks: {
          color: '#6B7280',
          font: {
            weight: 'bold',
          }
        },
        grid: {
          display: false,
        }
      }
    },
    animation: {
      duration: 1000,
      easing: 'easeInOutQuart',
    }
  };

  const totalVotes = votes.reduce((sum, count) => sum + count, 0);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="h-80 mb-4">
        <Bar data={data} options={options} />
      </div>
      
      {/* Vote Summary */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>Total Responses: <strong className="text-gray-900">{totalVotes}</strong></span>
          {autoUpdate && (
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Live Updates</span>
            </div>
          )}
        </div>
        
        {/* Percentage breakdown */}
        <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
          {choices.map((choice, index) => {
            const percentage = totalVotes > 0 ? ((votes[index] / totalVotes) * 100).toFixed(1) : 0;
            return (
              <div key={index} className="text-center">
                <div className="font-medium text-gray-900">{choice}</div>
                <div className="text-gray-600">{percentage}%</div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default BarChart;