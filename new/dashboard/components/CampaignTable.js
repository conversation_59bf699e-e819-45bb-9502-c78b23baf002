import { useState, useEffect } from 'react';
import { formatCurrency } from '../lib/utils';
import { calculateAverageCPL, formatCurrencyWithConversion, convertCurrency } from '../lib/currency';
import { authenticatedFetch } from '../lib/auth/clientAuth';

export default function CampaignTable({ timePeriod = '7' }) {
  const [campaigns, setCampaigns] = useState([]);
  const [summary, setSummary] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchCampaignData();
  }, [timePeriod]);

  const fetchCampaignData = async () => {
    try {
      setLoading(true);
      const response = await authenticatedFetch(`/api/facebook/campaigns?days=${timePeriod}&limit=8`);
      const data = await response.json();
      
      if (data.success) {
        const campaignsWithStatus = data.campaigns.map(campaign => ({
          ...campaign,
          ...getOptimizationStatus(campaign)
        }));
        setCampaigns(campaignsWithStatus);
        setSummary(data.summary);
        setError(null);
      } else {
        setError(data.message || 'Failed to fetch campaign data');
      }
    } catch (err) {
      if (err.message === 'Authentication failed') {
        setError('Authentication required. Please log in again.');
      } else {
        setError('Network error: ' + err.message);
      }
      console.error('Error fetching campaign data:', err);
    } finally {
      setLoading(false);
    }
  };

  const getOptimizationStatus = (campaign) => {
    // Convert CPL to USD for consistent comparison
    const cplUSD = convertCurrency(campaign.cost_per_lead, campaign.currency, 'USD');
    const avgCPL = 5; // Target cost per lead benchmark in USD
    
    if (cplUSD === 0 && campaign.leads === 0) {
      return {
        status: 'inactive',
        statusColor: 'bg-gray-100 text-gray-800',
        recommendation: 'No performance data'
      };
    }
    
    if (cplUSD <= avgCPL * 0.5) {
      return {
        status: 'scale',
        statusColor: 'bg-green-100 text-green-800',
        recommendation: `Excellent! CPL $${cplUSD.toFixed(0)} << $${avgCPL} - Increase budget`
      };
    } else if (cplUSD <= avgCPL * 0.8) {
      return {
        status: 'best',
        statusColor: 'bg-blue-100 text-blue-800',
        recommendation: `Good performance $${cplUSD.toFixed(0)} - Maintain spend`
      };
    } else if (cplUSD <= avgCPL * 1.5) {
      return {
        status: 'optimize',
        statusColor: 'bg-yellow-100 text-yellow-800',
        recommendation: `CPL $${cplUSD.toFixed(0)} > target - A/B test creative`
      };
    } else {
      return {
        status: 'pause',
        statusColor: 'bg-red-100 text-red-800',
        recommendation: `High CPL $${cplUSD.toFixed(0)} >> $${avgCPL} - Pause or reduce`
      };
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'scale': return '🟢';
      case 'optimize': return '🟡';
      case 'best': return '🏆';
      case 'pause': return '🔴';
      case 'inactive': return '⚪';
      default: return '⚪';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'scale': return 'Scale';
      case 'optimize': return 'Optimize';
      case 'best': return 'Best';
      case 'pause': return 'Pause';
      case 'inactive': return 'Inactive';
      default: return status;
    }
  };

  if (loading) {
    return (
      <div id="campaigns" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div id="campaigns" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
        <div className="text-red-600">
          <h3 className="text-lg font-medium mb-2">❌ Error Loading Campaign Data</h3>
          <p className="text-sm">{error}</p>
          <button 
            onClick={fetchCampaignData}
            className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div id="campaigns" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">📊 Top Campaigns</h3>
        <button 
          onClick={fetchCampaignData}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 flex items-center space-x-1 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          <span>Refresh</span>
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div className="bg-green-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-900">{summary.total_campaigns || 0}</div>
          <div className="text-sm text-green-600">Active Campaigns</div>
        </div>
        <div className="bg-purple-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-purple-900">
            {(summary.total_impressions || 0).toLocaleString()}
          </div>
          <div className="text-sm text-purple-600">Total Impressions</div>
        </div>
        <div className="bg-orange-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-orange-900">
            {(summary.total_clicks || 0).toLocaleString()}
          </div>
          <div className="text-sm text-orange-600">Total Clicks</div>
        </div>
        <div className="bg-blue-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-900">{summary.total_leads || 0}</div>
          <div className="text-sm text-blue-600">Total Leads</div>
        </div>
        <div className="bg-cyan-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-cyan-900">
            {(summary.avg_ctr || 0).toFixed(2)}%
          </div>
          <div className="text-sm text-cyan-600">Avg CTR</div>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 px-4 font-medium text-gray-900">Campaign</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Account</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Spend ({timePeriod}d)</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Leads</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">CPL</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">CTR</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Optimization</th>
            </tr>
          </thead>
          <tbody>
            {campaigns.map((campaign, index) => (
              <tr key={campaign.id} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4">
                  <div className="font-medium text-gray-900">{campaign.name}</div>
                  <div className="text-sm text-gray-500">ID: {campaign.id}</div>
                </td>
                <td className="py-3 px-4 text-sm text-gray-600">{campaign.account_name}</td>
                <td className="py-3 px-4">
                  <div className="space-y-1">
                    <div className="font-medium text-green-600">
                      ${campaign.spend_usd}
                    </div>
                    <div className="text-sm text-orange-600">
                      ₹{campaign.spend_inr}
                    </div>
                  </div>
                </td>
                <td className="py-3 px-4 font-medium text-blue-600">{campaign.leads}</td>
                <td className="py-3 px-4">
                  <div className="space-y-1">
                    <div className="font-medium text-green-600">
                      ${campaign.cost_per_lead_usd}
                    </div>
                    <div className="text-sm text-orange-600">
                      ₹{campaign.cost_per_lead_inr}
                    </div>
                  </div>
                </td>
                <td className="py-3 px-4 font-medium text-gray-900">{campaign.ctr}%</td>
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-2">
                    <span>{getStatusIcon(campaign.status)}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${campaign.statusColor}`}>
                      {getStatusText(campaign.status)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">{campaign.recommendation}</div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {campaigns.length === 0 && !loading && (
        <div className="text-center py-8 text-gray-500">
          <div className="text-lg mb-2">📊</div>
          <div>No campaign data available for the selected period</div>
          <div className="text-sm mt-1">Try running the health check to fetch fresh data</div>
        </div>
      )}
    </div>
  );
}