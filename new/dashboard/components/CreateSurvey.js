import React, { useState } from "react";
import { FaPlus } from "react-icons/fa";

const surveyTypes = [
  { value: "poll", label: "Poll" },
  { value: "quiz", label: "Quiz" },
  { value: "single_word", label: "Single Word" },
  { value: "single_sentence", label: "Single Sentence" },
  { value: "ranking", label: "Ranking" }
];

const CreateSurvey = () => {
  const [surveyType, setSurveyType] = useState(surveyTypes[0].value);
  const [surveyName, setSurveyName] = useState("");
  const [question, setQuestion] = useState("How do you feel about this?");
  const [answers, setAnswers] = useState([""]);

  const handleSurveyTypeChange = (e) => setSurveyType(e.target.value);

  const handleQuestionChange = (e) => setQuestion(e.target.value);

  const handleAnswerChange = (index, value) => {
    const updatedAnswers = answers.map((answer, i) =>
      i === index ? value : answer
    );
    setAnswers(updatedAnswers);
  };

  const addAnswerOption = () => setAnswers([...answers, ""]);

  const handleSave = async () => {
    const surveyData = {
      surveyName,
      surveyType,
      question,
      // For types that do not include multiple choices, responses could be omitted or empty
      responses:
        surveyType === "single_word" || surveyType === "single_sentence"
          ? []
          : answers.filter((answer) => answer.trim() !== ""),
    };

    console.log("Saving survey data:", JSON.stringify(surveyData, null, 2));
    
    try {
      // In the future, this would integrate with the new Next.js API
      // const response = await fetch('/api/surveys', {
      //   method: "POST",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      //   body: JSON.stringify(surveyData),
      // });

      // For now, just simulate success
      console.log("Survey would be saved:", surveyData);
      alert("Survey saved successfully!");
      
      // Reset form
      setSurveyName("");
      setQuestion("How do you feel about this?");
      setAnswers([""]);
      
    } catch (error) {
      console.error("Failed to save the survey:", error);
      alert("Failed to save the survey.");
    }
  };

  const renderSurveyCreationFields = () => {
    switch (surveyType) {
      case "single_word":
      case "single_sentence":
        return (
          <input
            type="text"
            placeholder="Enter your question"
            value={question}
            onChange={handleQuestionChange}
            className="form-input"
          />
        );
        
      case "poll":
      case "quiz":
      case "ranking":
        return (
          <div className="space-y-4">
            <input
              type="text"
              placeholder="Enter your question"
              value={question}
              onChange={handleQuestionChange}
              className="form-input"
            />
            
            {answers.map((answer, index) => (
              <input
                key={index}
                type="text"
                placeholder={`Option ${index + 1}`}
                value={answer}
                onChange={(e) => handleAnswerChange(index, e.target.value)}
                className="form-input"
              />
            ))}
            
            <button
              type="button"
              onClick={addAnswerOption}
              className="flex items-center space-x-2 text-purple-600 hover:text-purple-700 transition-colors"
            >
              <FaPlus className="w-4 h-4" />
              <span>Add Option</span>
            </button>
          </div>
        );
        
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Survey Name
          </label>
          <input
            type="text"
            placeholder="Enter survey name"
            value={surveyName}
            onChange={(e) => setSurveyName(e.target.value)}
            className="form-input"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Survey Type
          </label>
          <select
            value={surveyType}
            onChange={handleSurveyTypeChange}
            className="form-input"
          >
            {surveyTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Survey Content
        </label>
        {renderSurveyCreationFields()}
      </div>

      <div className="flex justify-end space-x-4">
        <button
          type="button"
          onClick={() => {
            setSurveyName("");
            setQuestion("How do you feel about this?");
            setAnswers([""]);
          }}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Clear
        </button>
        
        <button
          type="button"
          onClick={handleSave}
          disabled={!surveyName.trim() || !question.trim()}
          className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          Save Survey
        </button>
      </div>
    </div>
  );
};

export default CreateSurvey;