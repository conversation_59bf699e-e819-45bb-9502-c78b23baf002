import React from "react";
import DiscordWidget from "./DiscordWidget";

const Discord = () => {
  const serverId = "1122269242373459968"; // Modern AI Pro Discord server

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Join the Modern AI Pro Community!
          </h1>
          <p className="text-gray-600">
            Connect with over 2,500+ AI professionals, alumni, and industry experts
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-purple-50 rounded-lg p-4 text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900">Expert Mentorship</h3>
            <p className="text-sm text-gray-600 mt-1">
              Get guidance from industry professionals and workshop instructors
            </p>
          </div>

          <div className="bg-blue-50 rounded-lg p-4 text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-4h3v4c0 1.1.9 2 2 2s2-.9 2-2v-4h2v4c0 1.1.9 2 2 2s2-.9 2-2v-4h3v4c0 2.21-1.79 4-4 4H8c-2.21 0-4-1.79-4-4z"/>
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900">Peer Learning</h3>
            <p className="text-sm text-gray-600 mt-1">
              Collaborate on projects and learn from fellow AI enthusiasts
            </p>
          </div>

          <div className="bg-green-50 rounded-lg p-4 text-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900">Job Opportunities</h3>
            <p className="text-sm text-gray-600 mt-1">
              Access exclusive job postings and career advancement opportunities
            </p>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">What You'll Find</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
              <div>
                <h4 className="font-medium text-gray-900">Workshop Alumni Network</h4>
                <p className="text-sm text-gray-600">Connect with graduates from all our AI workshops</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <div>
                <h4 className="font-medium text-gray-900">Technical Discussions</h4>
                <p className="text-sm text-gray-600">Deep-dive conversations about AI/ML topics</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <div>
                <h4 className="font-medium text-gray-900">Project Showcases</h4>
                <p className="text-sm text-gray-600">Share and get feedback on your AI projects</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <div>
                <h4 className="font-medium text-gray-900">Industry Updates</h4>
                <p className="text-sm text-gray-600">Stay updated with the latest AI trends and news</p>
              </div>
            </div>
          </div>
        </div>

        <DiscordWidget serverId={serverId} />

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            By joining our Discord, you agree to follow our community guidelines and code of conduct.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Discord;