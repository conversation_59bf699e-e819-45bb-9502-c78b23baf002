import { formatDistanceToNow } from 'date-fns';

export default function EventsSchedule() {
  const upcomingBootcamps = [
    {
      title: 'AI for Senior Developers',
      type: 'bootcamp',
      date: '2024-08-15',
      time: '09:00 AM IST',
      duration: '2 days',
      capacity: 25,
      registered: 18,
      status: 'filling-fast',
      description: 'Advanced LLM integration for senior developers'
    },
    {
      title: 'GenAI for CTOs & Tech Leads',
      type: 'bootcamp',
      date: '2024-08-22',
      time: '10:00 AM IST',
      duration: '2 days',
      capacity: 20,
      registered: 12,
      status: 'open',
      description: 'Strategic AI implementation for leadership'
    },
    {
      title: 'AI Essentials Workshop',
      type: 'workshop',
      date: '2024-08-28',
      time: '02:00 PM IST',
      duration: '4 hours',
      capacity: 50,
      registered: 35,
      status: 'open',
      description: 'Fundamentals of AI for business professionals'
    }
  ];

  const upcomingWebinars = [
    {
      title: 'Building Production-Ready LLM Applications',
      type: 'webinar',
      date: '2024-08-05',
      time: '07:00 PM IST',
      duration: '1 hour',
      capacity: 500,
      registered: 234,
      status: 'open',
      speaker: '<PERSON><PERSON><PERSON>',
      description: 'Live demo: RAG systems in production'
    },
    {
      title: 'AI Strategy for Startups',
      type: 'webinar',
      date: '2024-08-08',
      time: '06:30 PM IST',
      duration: '45 mins',
      capacity: 300,
      registered: 156,
      status: 'open',
      speaker: 'Industry Expert',
      description: 'Leveraging AI for competitive advantage'
    },
    {
      title: 'Future of AI in Enterprise',
      type: 'webinar',
      date: '2024-08-12',
      time: '08:00 PM IST',
      duration: '1 hour',
      capacity: 400,
      registered: 89,
      status: 'open',
      speaker: 'Panel Discussion',
      description: 'Enterprise AI adoption trends & case studies'
    }
  ];

  const getStatusBadge = (status, registered, capacity) => {
    const percentage = (registered / capacity) * 100;
    
    if (percentage >= 90) {
      return { text: 'Almost Full', color: 'bg-red-100 text-red-800' };
    } else if (percentage >= 70) {
      return { text: 'Filling Fast', color: 'bg-orange-100 text-orange-800' };
    } else if (percentage >= 40) {
      return { text: 'Good Progress', color: 'bg-yellow-100 text-yellow-800' };
    } else {
      return { text: 'Open', color: 'bg-green-100 text-green-800' };
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'bootcamp': return '🎓';
      case 'workshop': return '🛠️';
      case 'webinar': return '📹';
      default: return '📅';
    }
  };

  const formatEventDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const getDaysUntil = (dateString) => {
    const eventDate = new Date(dateString);
    const now = new Date();
    const diffTime = eventDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Past';
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    return `${diffDays} days`;
  };

  return (
    <div id="events" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900">
            🗓️ UPCOMING EVENTS & BOOTCAMPS
          </h3>
          <p className="text-sm text-gray-600 mt-1">Event scheduling and capacity management</p>
        </div>
        <button 
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 flex items-center space-x-1 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg"
          onClick={() => alert('Navigate to: Event Management')}
        >
          <span>Event Management →</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Bootcamps & Workshops */}
        <div>
          <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center">
            🎓 Bootcamps & Workshops
          </h4>
          <div className="space-y-4">
            {upcomingBootcamps.map((event, index) => {
              const statusInfo = getStatusBadge(event.status, event.registered, event.capacity);
              return (
                <div key={index} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getTypeIcon(event.type)}</span>
                      <h5 className="font-medium text-gray-900">{event.title}</h5>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}>
                      {statusInfo.text}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600 space-y-1">
                    <div className="flex items-center space-x-4">
                      <span>📅 {formatEventDate(event.date)}</span>
                      <span>🕒 {event.time}</span>
                      <span>⏱️ {event.duration}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span>👥 {event.registered}/{event.capacity} registered</span>
                      <span className="font-medium text-blue-600">
                        📍 {getDaysUntil(event.date)}
                      </span>
                    </div>
                    <p className="text-gray-500 mt-2">{event.description}</p>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(event.registered / event.capacity) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Webinars */}
        <div>
          <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center">
            📹 Webinars
          </h4>
          <div className="space-y-4">
            {upcomingWebinars.map((event, index) => {
              const statusInfo = getStatusBadge(event.status, event.registered, event.capacity);
              return (
                <div key={index} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getTypeIcon(event.type)}</span>
                      <h5 className="font-medium text-gray-900">{event.title}</h5>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}>
                      {statusInfo.text}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600 space-y-1">
                    <div className="flex items-center space-x-4">
                      <span>📅 {formatEventDate(event.date)}</span>
                      <span>🕒 {event.time}</span>
                      <span>⏱️ {event.duration}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span>👥 {event.registered}/{event.capacity} registered</span>
                      <span className="font-medium text-blue-600">
                        📍 {getDaysUntil(event.date)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span>🎤 {event.speaker}</span>
                    </div>
                    <p className="text-gray-500 mt-2">{event.description}</p>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(event.registered / event.capacity) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-blue-50 rounded-lg p-3">
          <div className="text-sm font-medium text-blue-800">Total Upcoming</div>
          <div className="text-lg font-bold text-blue-900">6 Events</div>
          <div className="text-xs text-blue-600">3 bootcamps, 3 webinars</div>
        </div>
        <div className="bg-green-50 rounded-lg p-3">
          <div className="text-sm font-medium text-green-800">Total Registered</div>
          <div className="text-lg font-bold text-green-900">714</div>
          <div className="text-xs text-green-600">Across all events</div>
        </div>
        <div className="bg-orange-50 rounded-lg p-3">
          <div className="text-sm font-medium text-orange-800">Next Event</div>
          <div className="text-lg font-bold text-orange-900">Aug 5</div>
          <div className="text-xs text-orange-600">LLM Applications Webinar</div>
        </div>
        <div className="bg-purple-50 rounded-lg p-3">
          <div className="text-sm font-medium text-purple-800">Capacity Utilization</div>
          <div className="text-lg font-bold text-purple-900">58%</div>
          <div className="text-xs text-purple-600">Average across events</div>
        </div>
      </div>
    </div>
  );
}