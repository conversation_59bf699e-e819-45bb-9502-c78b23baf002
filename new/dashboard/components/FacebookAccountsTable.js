import { useState, useEffect } from 'react';
import { formatCurrency, formatPercentage } from '../lib/utils';
import { calculateAverageCPL, formatCurrencyWithConversion } from '../lib/currency';
import { authenticatedFetch } from '../lib/auth/clientAuth';

export default function FacebookAccountsTable({ timePeriod = '7' }) {
  const [accounts, setAccounts] = useState([]);
  const [summary, setSummary] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    fetchFacebookAccounts();
  }, [timePeriod]);

  const fetchFacebookAccounts = async () => {
    try {
      setLoading(true);
      const response = await authenticatedFetch(`/api/facebook/accounts?days=${timePeriod}`);
      const data = await response.json();
      
      if (data.success) {
        setAccounts(data.accounts);
        setSummary(data.summary);
        setError(null);
      } else {
        setError(data.message || 'Failed to fetch Facebook accounts');
      }
    } catch (err) {
      if (err.message === 'Authentication failed') {
        setError('Authentication required. Please log in again.');
      } else {
        setError('Network error: ' + err.message);
      }
      console.error('Error fetching Facebook accounts:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAccountClick = (account) => {
    setSelectedAccount(account);
    setShowModal(true);
  };

  const getHealthStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'error': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getHealthStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return '🔄';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-red-600">
          <h3 className="text-lg font-medium mb-2">❌ Error Loading Facebook Accounts</h3>
          <p className="text-sm">{error}</p>
          <button 
            onClick={fetchFacebookAccounts}
            className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div id="facebook-accounts" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">📘 Facebook Ad Accounts</h3>
          <div className="flex items-center space-x-3">
            <div className="text-sm text-gray-600">
              Last updated: {new Date(summary.last_updated || new Date()).toLocaleTimeString()}
            </div>
            <button 
                onClick={fetchFacebookAccounts}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 flex items-center space-x-1 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>Refresh</span>
              </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-sm font-medium text-blue-800">Total Accounts</div>
            <div className="text-2xl font-bold text-blue-900">{summary.total_accounts || 0}</div>
            <div className="text-xs text-blue-600">
              {summary.accessible_accounts || 0} accessible
            </div>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-sm font-medium text-green-800">Leads ({timePeriod}d)</div>
            <div className="text-2xl font-bold text-green-900">{summary[`total_leads_${timePeriod}d`] || 0}</div>
            <div className="text-xs text-green-600">Performance period</div>
          </div>
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="text-sm font-medium text-purple-800">Total Spend ({timePeriod}d)</div>
            <div className="text-2xl font-bold text-purple-900">
              ${summary.total_spend_usd || 0}
            </div>
            <div className="text-xs text-purple-600">Ad investment</div>
          </div>
          <div className="bg-orange-50 rounded-lg p-4">
            <div className="text-sm font-medium text-orange-800">Avg CPL (USD)</div>
            <div className="text-2xl font-bold text-orange-900">
              ${Math.round(calculateAverageCPL(accounts))}
            </div>
            <div className="text-xs text-orange-600">Normalized average</div>
          </div>
        </div>

        {/* Accounts Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-900">Account</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Leads ({timePeriod}d)</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">CPL</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Spend ({timePeriod}d)</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">CTR</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Campaigns</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Active Ads</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody>
              {accounts.map((account, index) => (
                <tr 
                  key={account.id} 
                  className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleAccountClick(account)}
                >
                  <td className="py-3 px-4">
                    <div>
                      <div className="font-medium text-gray-900">{account.name}</div>
                      <div className="text-sm text-gray-500">ID: {account.id}</div>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getHealthStatusColor(account.campaigns_active > 0 ? 'healthy' : 'warning')}`}>
                      <span className="mr-1">{getHealthStatusIcon(account.campaigns_active > 0 ? 'healthy' : 'warning')}</span>
                      {account.campaigns_active > 0 ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="py-3 px-4 font-medium text-green-600">{account[`leads_${timePeriod}d`] || 0}</td>
                  <td className="py-3 px-4">
                    <span className={`font-medium ${account.currency === 'USD' ? 'text-green-600' : 'text-orange-600'}`}>
                      {account.currency === 'USD' ? '$' : '₹'}{Math.round(account.cost_per_lead || 0)}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`font-medium ${account.currency === 'USD' ? 'text-green-600' : 'text-orange-600'}`}>
                      {account.currency === 'USD' ? '$' : '₹'}{Math.round(account[`spend_${timePeriod}d`] || 0)}
                    </span>
                  </td>
                  <td className="py-3 px-4 font-medium text-blue-600">
                    {account[`ctr_${timePeriod}d`] || '0.00'}%
                  </td>
                  <td className="py-3 px-4">
                    <span className={account.campaigns_active > 0 ? 'font-medium text-green-600' : 'font-medium text-gray-400'}>
                      {account.campaigns_active} 
                      {account.campaigns_active === 1 ? ' campaign' : ' campaigns'}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={account.ads_active > 0 ? 'font-medium text-blue-600' : 'font-medium text-gray-400'}>
                      {account.ads_active} 
                      {account.ads_active === 1 ? ' ad' : ' ads'}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <button 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAccountClick(account);
                      }}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      View Details →
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Account Detail Modal */}
      {showModal && selectedAccount && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-900">
                📘 {selectedAccount.name}
              </h2>
              <button 
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-sm font-medium text-gray-600">Account ID</div>
                  <div className="text-lg font-mono">{selectedAccount.id}</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-sm font-medium text-gray-600">Status</div>
                  <div className="flex items-center space-x-2">
                    <span>{getHealthStatusIcon(selectedAccount.health_status)}</span>
                    <span className="font-medium">{selectedAccount.status}</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-green-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-900">{selectedAccount[`leads_${timePeriod}d`] || 0}</div>
                  <div className="text-sm text-green-600">Leads ({timePeriod}d)</div>
                </div>
                <div className="bg-purple-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-purple-900">
                    {selectedAccount.currency === 'USD' ? '$' : '₹'}{Math.round(selectedAccount.cost_per_lead || 0)}
                  </div>
                  <div className="text-sm text-purple-600">Cost Per Lead</div>
                </div>
                <div className="bg-blue-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-blue-900">
                    {selectedAccount.currency === 'USD' ? '$' : '₹'}{Math.round(selectedAccount[`spend_${timePeriod}d`] || 0)}
                  </div>
                  <div className="text-sm text-blue-600">Spend ({timePeriod}d)</div>
                </div>
                <div className="bg-orange-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-orange-900">{selectedAccount[`ctr_${timePeriod}d`] || '0.00'}%</div>
                  <div className="text-sm text-orange-600">CTR ({timePeriod}d)</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-indigo-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-indigo-900">{(selectedAccount[`impressions_${timePeriod}d`] || 0).toLocaleString()}</div>
                  <div className="text-sm text-indigo-600">Impressions ({timePeriod}d)</div>
                </div>
                <div className="bg-teal-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-teal-900">{(selectedAccount[`clicks_${timePeriod}d`] || 0).toLocaleString()}</div>
                  <div className="text-sm text-teal-600">Clicks ({timePeriod}d)</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-emerald-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-emerald-900">{selectedAccount.campaigns_active}</div>
                  <div className="text-sm text-emerald-600">Active Campaigns</div>
                </div>
                <div className="bg-cyan-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-cyan-900">{selectedAccount.ads_active}</div>
                  <div className="text-sm text-cyan-600">Active Ads</div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Account Details</h3>
                <div className="space-y-2 text-sm">
                  <div><span className="font-medium">Type:</span> {selectedAccount.type}</div>
                  <div><span className="font-medium">Active Campaigns:</span> {selectedAccount.campaigns_active}</div>
                  <div><span className="font-medium">Active Ads:</span> {selectedAccount.ads_active}</div>
                  <div><span className="font-medium">Last Checked:</span> {new Date(selectedAccount.last_checked).toLocaleString()}</div>
                  {selectedAccount.description && (
                    <div><span className="font-medium">Description:</span> {selectedAccount.description}</div>
                  )}
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button 
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Close
                </button>
                <button 
                  onClick={() => {
                    window.open(`https://business.facebook.com/adsmanager/manage/accounts?act=${selectedAccount.id}`, '_blank');
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Open in Facebook →
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}