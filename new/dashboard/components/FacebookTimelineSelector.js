export default function FacebookTimelineSelector({ activeTimeline, onTimelineChange }) {
  const timelineOptions = [
    { value: '1', label: 'Today', description: '1 day' },
    { value: '7', label: '7 Days', description: '1 week' },
    { value: '30', label: '30 Days', description: '1 month' }
  ];

  return (
    <div className="flex items-center space-x-4">
      <span className="text-sm font-medium text-gray-700">Time Period:</span>
      <div className="flex items-center space-x-2">
        {timelineOptions.map((option) => (
          <button
            key={option.value}
            onClick={() => onTimelineChange(option.value)}
            className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
              activeTimeline === option.value
                ? 'bg-blue-600 text-white shadow-sm'
                : 'bg-white text-gray-600 border border-gray-300 hover:bg-gray-50 hover:text-gray-900'
            }`}
          >
            {option.label}
          </button>
        ))}
      </div>
      <div className="text-xs text-gray-500">
        Showing data for the last {timelineOptions.find(o => o.value === activeTimeline)?.description}
      </div>
    </div>
  );
}