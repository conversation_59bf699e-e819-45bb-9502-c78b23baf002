import { formatCurrency, formatPercentage } from '../lib/utils';

export default function GeographyTable() {
  const geoData = [
    { 
      region: '🇮🇳 Bangalore', 
      leads: 420, 
      percentage: 35, 
      cpl: 125, 
      convRate: 8.2, 
      revenue: 680000, 
      currency: 'INR',
      status: 'best' 
    },
    { 
      region: '🇮🇳 NCR', 
      leads: 360, 
      percentage: 30, 
      cpl: 142, 
      convRate: 6.5, 
      revenue: 520000, 
      currency: 'INR',
      status: 'good' 
    },
    { 
      region: '🇺🇸 US West', 
      leads: 180, 
      percentage: 15, 
      cpl: 12, 
      convRate: 12.1, 
      revenue: 8200, 
      currency: 'USD',
      status: 'excellent' 
    },
    { 
      region: '🇮🇳 Mumbai', 
      leads: 234, 
      percentage: 20, 
      cpl: 158, 
      convRate: 7.1, 
      revenue: 410000, 
      currency: 'INR',
      status: 'average' 
    }
  ];

  const getStatusBadge = (status) => {
    switch (status) {
      case 'best': return '🏆';
      case 'excellent': return '🔥';
      default: return '';
    }
  };

  return (
    <div id="geography" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">
            📍 GEOGRAPHY & CAMPAIGN PERFORMANCE
          </h3>
          <p className="text-sm text-gray-600 mt-1">Regional performance and budget allocation insights</p>
        </div>
        <button 
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 flex items-center space-x-1 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg"
          onClick={() => alert('Navigate to: Geographic Analytics')}
        >
          <span>Geographic Analytics →</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 px-4 font-medium text-gray-900">Region</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Leads</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">CPL</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Conv Rate</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Revenue</th>
            </tr>
          </thead>
          <tbody>
            {geoData.map((region, index) => (
              <tr key={index} className="border-b border-gray-100">
                <td className="py-3 px-4 font-medium">{region.region}</td>
                <td className="py-3 px-4">
                  {region.leads} ({region.percentage}%)
                </td>
                <td className="py-3 px-4">
                  {formatCurrency(region.cpl, region.currency)}
                </td>
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-1">
                    <span>{formatPercentage(region.convRate)}</span>
                    <span>{getStatusBadge(region.status)}</span>
                  </div>
                </td>
                <td className="py-3 px-4">
                  {region.currency === 'INR' 
                    ? `${formatCurrency(region.revenue / 100000)}L`
                    : `${formatCurrency(region.revenue / 1000)}K`
                  }
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}