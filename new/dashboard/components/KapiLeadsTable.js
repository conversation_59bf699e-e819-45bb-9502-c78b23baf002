import { formatCurrency, humanFriendlyTime } from '../lib/utils';

export default function KapiLeadsTable({ timeline }) {
  const kapiLeads = [
    {
      id: 'KL001',
      companyName: 'TechCorp Solutions',
      contactName: '<PERSON><PERSON>',
      title: 'CTO',
      email: 'r<PERSON><PERSON>@techcorp.com',
      phone: '+91-98765-43210',
      modernAIGraduate: true,
      bootcampCompleted: '2024-07-15',
      kapiInterest: 'Enterprise AI Platform',
      dealSize: 450000,
      stage: 'demo_scheduled',
      probability: 75,
      expectedClose: '2024-08-20',
      lastContact: '2024-07-28',
      assignedTo: 'Sanyu',
      notes: 'Strong technical background, needs ROI metrics',
      crossSellOpportunity: 'Multi-team training',
      companySize: '500-1000',
      industry: 'FinTech'
    },
    {
      id: 'KL002',
      companyName: 'InnovateLabs Inc',
      contactName: '<PERSON><PERSON>',
      title: 'VP Engineering',
      email: '<EMAIL>',
      phone: '+91-99876-54321',
      modernAIGraduate: true,
      bootcampCompleted: '2024-06-28',
      kapiInterest: 'Custom LLM Training',
      dealSize: 750000,
      stage: 'proposal_sent',
      probability: 60,
      expectedClose: '2024-09-15',
      lastContact: '2024-07-30',
      assignedTo: 'Manish',
      notes: 'Budget approved, waiting for board decision',
      crossSellOpportunity: 'Advanced AI Bootcamp for team',
      companySize: '1000+',
      industry: 'Healthcare Tech'
    },
    {
      id: 'KL003',
      companyName: 'StartupXYZ',
      contactName: 'David Chen',
      title: 'Founder & CEO',
      email: '<EMAIL>',
      phone: '+1-************',
      modernAIGraduate: true,
      bootcampCompleted: '2024-07-10',
      kapiInterest: 'AI Consulting Package',
      dealSize: 120000,
      stage: 'qualified',
      probability: 40,
      expectedClose: '2024-08-30',
      lastContact: '2024-07-29',
      assignedTo: 'Shivam',
      notes: 'Early stage startup, cost-sensitive',
      crossSellOpportunity: 'Team AI workshops',
      companySize: '10-50',
      industry: 'EdTech'
    },
    {
      id: 'KL004',
      companyName: 'MegaCorp Enterprise',
      contactName: 'Sarah Johnson',
      title: 'Chief Data Officer',
      email: '<EMAIL>',
      phone: '+1-************',
      modernAIGraduate: false,
      bootcampCompleted: null,
      kapiInterest: 'Enterprise AI Strategy',
      dealSize: 1200000,
      stage: 'discovery',
      probability: 25,
      expectedClose: '2024-10-15',
      lastContact: '2024-07-25',
      assignedTo: 'Sanyu',
      notes: 'Large enterprise, long sales cycle',
      crossSellOpportunity: 'Executive AI Bootcamp + Team Training',
      companySize: '5000+',
      industry: 'Manufacturing'
    },
    {
      id: 'KL005',
      companyName: 'AgileMinds Consulting',
      contactName: 'Michael Brown',
      title: 'Head of Technology',
      email: '<EMAIL>',
      phone: '+91-97654-32109',
      modernAIGraduate: true,
      bootcampCompleted: '2024-05-20',
      kapiInterest: 'AI Implementation Framework',
      dealSize: 320000,
      stage: 'negotiation',
      probability: 85,
      expectedClose: '2024-08-10',
      lastContact: '2024-07-31',
      assignedTo: 'Manish',
      notes: 'Ready to close, finalizing contract terms',
      crossSellOpportunity: 'Client training programs',
      companySize: '100-500',
      industry: 'Consulting'
    }
  ];

  const getStageColor = (stage) => {
    const colors = {
      discovery: 'bg-blue-100 text-blue-800',
      qualified: 'bg-yellow-100 text-yellow-800',
      demo_scheduled: 'bg-purple-100 text-purple-800',
      proposal_sent: 'bg-orange-100 text-orange-800',
      negotiation: 'bg-green-100 text-green-800',
      closed_won: 'bg-emerald-100 text-emerald-800',
      closed_lost: 'bg-red-100 text-red-800'
    };
    return colors[stage] || 'bg-gray-100 text-gray-800';
  };

  const getStageText = (stage) => {
    const texts = {
      discovery: 'Discovery',
      qualified: 'Qualified',
      demo_scheduled: 'Demo Scheduled',
      proposal_sent: 'Proposal Sent',
      negotiation: 'Negotiation',
      closed_won: 'Closed Won',
      closed_lost: 'Closed Lost'
    };
    return texts[stage] || stage;
  };

  const getProbabilityColor = (probability) => {
    if (probability >= 75) return 'text-green-600';
    if (probability >= 50) return 'text-yellow-600';
    if (probability >= 25) return 'text-orange-600';
    return 'text-red-600';
  };

  const getTotalPipelineValue = () => {
    return kapiLeads.reduce((total, lead) => total + (lead.dealSize * lead.probability / 100), 0);
  };

  const getWeightedCloseDate = () => {
    const closeDates = kapiLeads.map(lead => new Date(lead.expectedClose));
    const avgTimestamp = closeDates.reduce((sum, date) => sum + date.getTime(), 0) / closeDates.length;
    return new Date(avgTimestamp).toLocaleDateString('en-IN', { month: 'short', day: 'numeric' });
  };

  return (
    <div id="kapi-pipeline" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900">
            🏢 KAPI ENTERPRISE PIPELINE ({timeline.toUpperCase()})
          </h3>
          <p className="text-sm text-gray-600 mt-1">High-value enterprise deals and cross-sell opportunities</p>
        </div>
        <button 
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 flex items-center space-x-1 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg"
          onClick={() => alert('Navigate to: Enterprise CRM')}
        >
          <span>Enterprise CRM →</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
        <div className="flex items-center space-x-4">
          <div className="text-right">
            <div className="text-sm text-gray-600">Weighted Pipeline</div>
            <div className="text-xl font-bold text-purple-600">
              {formatCurrency(getTotalPipelineValue())}
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-600">Avg Expected Close</div>
            <div className="text-lg font-medium text-blue-600">
              {getWeightedCloseDate()}
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 px-4 font-medium text-gray-900">Company & Contact</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Modern AI Graduate</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Kapi Interest</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Deal Size</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Stage</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Probability</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Expected Close</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Cross-Sell Opportunity</th>
            </tr>
          </thead>
          <tbody>
            {kapiLeads.map((lead, index) => (
              <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-4 px-4">
                  <div>
                    <div className="font-medium text-gray-900">{lead.companyName}</div>
                    <div className="text-sm text-gray-600">
                      {lead.contactName} • {lead.title}
                    </div>
                    <div className="text-xs text-gray-500">
                      {lead.industry} • {lead.companySize} employees
                    </div>
                  </div>
                </td>
                <td className="py-4 px-4">
                  {lead.modernAIGraduate ? (
                    <div className="flex items-center space-x-1">
                      <span className="text-green-600">✅</span>
                      <div className="text-xs">
                        <div className="font-medium text-green-700">Yes</div>
                        <div className="text-gray-500">
                          {new Date(lead.bootcampCompleted).toLocaleDateString('en-IN', { 
                            month: 'short', 
                            day: 'numeric' 
                          })}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1">
                      <span className="text-orange-600">⚠️</span>
                      <div className="text-xs">
                        <div className="font-medium text-orange-700">No</div>
                        <div className="text-gray-500">Potential upsell</div>
                      </div>
                    </div>
                  )}
                </td>
                <td className="py-4 px-4">
                  <div className="text-sm font-medium text-gray-900">{lead.kapiInterest}</div>
                  <div className="text-xs text-gray-500">Assigned: {lead.assignedTo}</div>
                </td>
                <td className="py-4 px-4">
                  <div className="font-bold text-gray-900">
                    {formatCurrency(lead.dealSize)}
                  </div>
                  <div className="text-xs text-gray-500">
                    Weighted: {formatCurrency(lead.dealSize * lead.probability / 100)}
                  </div>
                </td>
                <td className="py-4 px-4">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStageColor(lead.stage)}`}>
                    {getStageText(lead.stage)}
                  </span>
                </td>
                <td className="py-4 px-4">
                  <div className={`font-bold ${getProbabilityColor(lead.probability)}`}>
                    {lead.probability}%
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                    <div 
                      className="bg-blue-600 h-1 rounded-full"
                      style={{ width: `${lead.probability}%` }}
                    ></div>
                  </div>
                </td>
                <td className="py-4 px-4">
                  <div className="text-sm font-medium text-gray-900">
                    {new Date(lead.expectedClose).toLocaleDateString('en-IN', { 
                      month: 'short', 
                      day: 'numeric' 
                    })}
                  </div>
                  <div className="text-xs text-gray-500">
                    Last contact: {humanFriendlyTime(lead.lastContact)}
                  </div>
                </td>
                <td className="py-4 px-4">
                  <div className="text-sm text-purple-700 font-medium">
                    {lead.crossSellOpportunity}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {lead.notes}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pipeline Analytics */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-purple-50 rounded-lg p-4">
          <div className="text-sm font-medium text-purple-800">Total Pipeline</div>
          <div className="text-xl font-bold text-purple-900">
            {formatCurrency(kapiLeads.reduce((sum, lead) => sum + lead.dealSize, 0))}
          </div>
          <div className="text-xs text-purple-600">5 active opportunities</div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-sm font-medium text-green-800">Modern AI Graduates</div>
          <div className="text-xl font-bold text-green-900">
            {kapiLeads.filter(lead => lead.modernAIGraduate).length}/5
          </div>
          <div className="text-xs text-green-600">80% cross-sell rate</div>
        </div>
        
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-sm font-medium text-blue-800">Avg Deal Size</div>
          <div className="text-xl font-bold text-blue-900">
            {formatCurrency(kapiLeads.reduce((sum, lead) => sum + lead.dealSize, 0) / kapiLeads.length)}
          </div>
          <div className="text-xs text-blue-600">High-value enterprise deals</div>
        </div>
        
        <div className="bg-orange-50 rounded-lg p-4">
          <div className="text-sm font-medium text-orange-800">Closing This Month</div>
          <div className="text-xl font-bold text-orange-900">2</div>
          <div className="text-xs text-orange-600">
            {formatCurrency(770000)} potential revenue
          </div>
        </div>
      </div>

      {/* Cross-Sell Opportunities */}
      <div className="mt-6 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4">
        <h4 className="text-md font-medium text-gray-800 mb-3">
          🎯 Cross-Sell Opportunities
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="text-sm font-medium text-purple-700">High Priority</div>
            <ul className="text-sm text-gray-600 mt-1 space-y-1">
              <li>• MegaCorp Enterprise: Executive AI Bootcamp ($50K potential)</li>
              <li>• InnovateLabs Inc: Advanced team training ($30K potential)</li>
            </ul>
          </div>
          <div>
            <div className="text-sm font-medium text-blue-700">Pipeline Expansion</div>
            <ul className="text-sm text-gray-600 mt-1 space-y-1">
              <li>• 4/5 leads ready for additional training programs</li>
              <li>• Estimated additional revenue: $180K</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}