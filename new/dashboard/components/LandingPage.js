import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Logo from './layout/Logo';
import { useAuth } from '../contexts/AuthContext';
import {
  FaCode,
  FaUsers,
  FaTrophy,
  FaRocket,
  FaChartLine,
  FaLaptopCode,
  FaGraduationCap,
  FaAward,
  FaLinkedin,
  FaQuora,
  FaBrain,
  FaCheckCircle,
  FaStar,
  FaQuoteLeft,
} from 'react-icons/fa';

const Feature = ({ title, text, icon: Icon }) => {
  return (
    <div className="bg-gray-800 border border-purple-600 rounded-lg p-6 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl">
      <div className="flex items-center justify-center w-16 h-16 bg-purple-500 text-white rounded-full mb-4">
        <Icon className="w-10 h-10" />
      </div>
      <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
      <p className="text-gray-400">{text}</p>
    </div>
  );
};

const WorkshopCard = ({ title, description, price, duration }) => {
  const router = useRouter();
  
  return (
    <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 hover:border-purple-500 transition-all duration-300">
      <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
      <p className="text-gray-400 mb-4 text-sm">{description}</p>
      <div className="flex justify-between items-center">
        <span className="text-purple-400 font-bold">${price}</span>
        <span className="text-gray-500 text-sm">{duration}</span>
      </div>
    </div>
  );
};

const StatCard = ({ number, label, suffix = "" }) => {
  return (
    <div className="text-center">
      <div className="text-4xl md:text-5xl font-bold text-purple-400 mb-2">
        {number}{suffix}
      </div>
      <div className="text-gray-400">{label}</div>
    </div>
  );
};

const TestimonialCard = ({ quote, author, role, company, image }) => {
  return (
    <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
      <FaQuoteLeft className="text-purple-500 mb-4" size={24} />
      <p className="text-gray-300 mb-4 italic">"{quote}"</p>
      <div className="border-t border-gray-700 pt-4 flex items-center gap-4">
        {image && (
          <img 
            src={image} 
            alt={author} 
            className="w-12 h-12 rounded-full object-cover border-2 border-purple-500"
          />
        )}
        <div>
          <p className="text-white font-semibold">{author}</p>
          <p className="text-gray-400 text-sm">{role}{company && ` at ${company}`}</p>
        </div>
      </div>
    </div>
  );
};

const LandingPage = () => {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const featuresRef = useRef(null);
  const workshopsRef = useRef(null);
  const aboutRef = useRef(null);
  const instructorRef = useRef(null);

  useEffect(() => {
    document.title = 'Modern AI Pro - Transform Your Career with AI Skills';
  }, []);

  const scrollToSection = (ref) => {
    ref.current?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="bg-gradient-to-b from-gray-900 to-purple-900 min-h-screen text-white">
      {/* Navigation */}
      <nav className="sticky top-0 z-10 bg-gray-900 shadow-md">
        <div className="flex items-center justify-between w-full py-4 px-8">
          <div className="flex items-center">
            <Logo isDark className="max-w-40" />
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <button
              onClick={() => scrollToSection(workshopsRef)}
              className="text-gray-300 hover:text-white transition-colors cursor-pointer"
            >
              Workshops
            </button>
            <button
              onClick={() => scrollToSection(featuresRef)}
              className="text-gray-300 hover:text-white transition-colors cursor-pointer"
            >
              Platform
            </button>
            <button
              onClick={() => scrollToSection(instructorRef)}
              className="text-gray-300 hover:text-white transition-colors cursor-pointer"
            >
              Instructor
            </button>
            <button
              onClick={() => scrollToSection(aboutRef)}
              className="text-gray-300 hover:text-white transition-colors cursor-pointer"
            >
              About
            </button>
            {isAuthenticated ? (
              <button
                onClick={() => router.push('/')}
                className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                Go to Portal
              </button>
            ) : (
              <button
                onClick={() => router.push('/login')}
                className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                Enroll Now
              </button>
            )}
          </div>

          {/* Mobile Navigation */}
          {isAuthenticated ? (
            <button
              onClick={() => router.push('/')}
              className="md:hidden bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Go to Portal
            </button>
          ) : (
            <button
              onClick={() => router.push('/login')}
              className="md:hidden bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Enroll
            </button>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative overflow-hidden py-20 md:py-28 px-8">
        <div className="max-w-7xl mx-auto relative z-2">
          <div className="text-center space-y-6 md:space-y-8 py-10 md:py-18">
            <div className="mb-4">
              <span className="bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                🔥 2,500+ Professionals Trained
              </span>
            </div>
            <h1 className="font-bold text-3xl sm:text-4xl md:text-6xl leading-tight bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Master AI to Transform Your Career
            </h1>
            <p className="text-gray-300 max-w-4xl mx-auto text-lg md:text-xl">
              Join live workshops taught by industry experts who deploy AI for Fortune 500 companies and the US Federal Government. 
              Learn practical AI skills that actually work in production.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => router.push('/login')}
                className="bg-purple-500 hover:bg-purple-600 text-white font-bold px-8 py-4 rounded-full text-lg transition-colors shadow-lg hover:shadow-xl"
              >
                Start Your AI Journey
              </button>
              <button
                onClick={() => scrollToSection(workshopsRef)}
                className="border border-white/30 hover:border-white/50 text-white font-bold px-8 py-4 rounded-full text-lg transition-colors flex items-center justify-center gap-2"
              >
                <FaGraduationCap />
                View Workshops
              </button>
            </div>
            <p className="text-purple-300 text-sm">
              Next cohort starts in 3 days • Limited spots available
            </p>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="py-16 bg-gray-900/50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <StatCard number="2,500" suffix="+" label="Graduates" />
            <StatCard number="6" label="Workshop Types" />
            <StatCard number="95" suffix="%" label="Completion Rate" />
            <StatCard number="4.9" label="Average Rating" />
          </div>
        </div>
      </div>

      {/* Workshops Section */}
      <div ref={workshopsRef} className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4">
          <div className="space-y-12">
            <div className="text-center space-y-2">
              <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Industry-Leading AI Workshops
              </h2>
              <p className="text-lg text-gray-400 max-w-2xl mx-auto">
                Hands-on, practical training designed for working professionals. 
                Small class sizes ensure personalized attention.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <WorkshopCard
                title="AI Essentials"
                description="Foundation course covering LLMs, RAG systems, and practical AI deployment"
                price="320"
                duration="Weekend intensive"
              />
              <WorkshopCard
                title="AI Practitioner"
                description="Advanced implementation techniques for production-ready AI systems"
                price="320"
                duration="3-day workshop"
              />
              <WorkshopCard
                title="Agentic AI"
                description="Build autonomous AI agents and multi-agent systems"
                price="320"
                duration="Weekend intensive"
              />
              <WorkshopCard
                title="Vibe Coding"
                description="Master AI-assisted programming and code generation"
                price="320"
                duration="2-day workshop"
              />
              <WorkshopCard
                title="AI for PMs"
                description="Product management in the AI era - strategy and execution"
                price="320"
                duration="Weekend intensive"
              />
              <WorkshopCard
                title="AI for UX"
                description="Design AI-powered user experiences and interfaces"
                price="320"
                duration="2-day workshop"
              />
            </div>
            <div className="text-center">
              <div className="bg-purple-900/30 border border-purple-600 rounded-lg p-6 max-w-2xl mx-auto">
                <h3 className="text-xl font-bold text-white mb-2">🎯 Executive Masterclass</h3>
                <p className="text-gray-300 mb-4">
                  Exclusive monthly cohort taught directly by Dr. Balaji Viswanathan. 
                  Limited to 20 senior leaders.
                </p>
                <p className="text-purple-400 font-bold text-2xl">$2,500</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Instructor Section */}
      <div ref={instructorRef} className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Learn from Industry Leaders
              </h2>
              <div className="space-y-4">
                <div className="bg-gray-900 rounded-lg p-6">
                  <h3 className="text-xl font-bold text-white mb-2">Dr. Balaji Viswanathan</h3>
                  <p className="text-purple-400 font-semibold mb-2">CEO & Lead Instructor</p>
                  <ul className="space-y-2 text-gray-400">
                    <li className="flex items-start gap-2">
                      <FaCheckCircle className="text-purple-500 mt-1 flex-shrink-0" />
                      <span>PhD in AI from University of Maryland</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <FaCheckCircle className="text-purple-500 mt-1 flex-shrink-0" />
                      <span>Most followed writer on Quora (500K+ followers)</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <FaCheckCircle className="text-purple-500 mt-1 flex-shrink-0" />
                      <span>Built AI systems for Fortune 500 & US Federal Government</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <FaCheckCircle className="text-purple-500 mt-1 flex-shrink-0" />
                      <span>Founded multiple successful AI companies</span>
                    </li>
                  </ul>
                  <div className="flex gap-4 mt-4">
                    <a href="https://www.linkedin.com/in/balajivi/" target="_blank" rel="noopener noreferrer" 
                       className="text-purple-400 hover:text-purple-300">
                      <FaLinkedin size={24} />
                    </a>
                    <a href="#" className="text-purple-400 hover:text-purple-300">
                      <FaQuora size={24} />
                    </a>
                  </div>
                </div>
                <div className="bg-gray-900 rounded-lg p-6">
                  <h3 className="text-xl font-bold text-white mb-2">Arvind Nagraj</h3>
                  <p className="text-purple-400 font-semibold mb-2">Chief Architect</p>
                  <p className="text-gray-400">
                    Leading expert in production AI deployment with extensive experience 
                    in building scalable AI systems for enterprise clients.
                  </p>
                </div>
              </div>
            </div>
            <div className="relative">
              {/* Instructor Image */}
              <div className="bg-gray-700 rounded-lg overflow-hidden shadow-2xl">
                <img 
                  src="/images/Balaji6.jpeg" 
                  alt="Dr. Balaji Viswanathan - CEO, Invento Robotics" 
                  className="w-full h-[500px] object-cover"
                />
              </div>
              {/* Awards Placeholder */}
              <div className="absolute -bottom-6 -right-6 bg-gray-900 border border-purple-600 rounded-lg p-4 shadow-lg">
                <div className="flex items-center gap-2">
                  <FaAward className="text-purple-500" size={24} />
                  <div>
                    <p className="text-white font-semibold">Award Winner</p>
                    <p className="text-gray-400 text-sm">AI Excellence 2024</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Platform Features */}
      <div ref={featuresRef} className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4">
          <div className="space-y-12">
            <div className="text-center space-y-2">
              <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Beyond Workshops: Your AI Learning Ecosystem
              </h2>
              <p className="text-lg text-gray-400 max-w-2xl mx-auto">
                Subscribe to access our AI-powered learning platform with daily challenges, 
                pair programming, and continuous skill development.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Feature
                icon={FaLaptopCode}
                title="Kapi IDE Integration"
                text="Code in our AI-powered IDE with real-time collaboration and intelligent suggestions."
              />
              <Feature
                icon={FaTrophy}
                title="Competitive Learning"
                text="ELO rating system and daily challenges to track your progress against peers."
              />
              <Feature
                icon={FaUsers}
                title="Pair Programming"
                text="Weekly AI-matched coding partners for collaborative problem-solving."
              />
              <Feature
                icon={FaChartLine}
                title="Personalized Path"
                text="AI analyzes your strengths and weaknesses to create a custom learning journey."
              />
              <Feature
                icon={FaRocket}
                title="Project-Based Learning"
                text="Build real AI applications with guidance from industry practitioners."
              />
              <Feature
                icon={FaGraduationCap}
                title="Lifetime Access"
                text="Join our alumni network with ongoing support and advanced workshops."
              />
            </div>
          </div>
        </div>
      </div>

      {/* Testimonials */}
      <div className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4">
          <div className="space-y-12">
            <div className="text-center space-y-2">
              <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                What Our Graduates Say
              </h2>
              <p className="text-lg text-gray-400">
                Join thousands who've transformed their careers
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <TestimonialCard
                quote="Grateful for the invaluable knowledge and skills gained. Excited to apply them in real-world applications!"
                author="Irin Priya"
                role="Sr. ML Engineer"
                company="Reltio"
                image="/images/testimonials/irin.jpeg"
              />
              <TestimonialCard
                quote="It was very informative and eye-opening 3-day Boot Camp providing insights on various AI and LLM frameworks"
                author="Vikram G."
                role="Principal Architect"
                company="Maxbyte"
              />
              <TestimonialCard
                quote="The Deep Learning Bootcamp was an incredible and comprehensive learning experience"
                author="Bharat Vishal"
                role="Computer Vision"
                company="Carnegie Mellon University"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div className="py-20 bg-gradient-to-b from-purple-900 to-gray-900">
        <div className="max-w-7xl mx-auto px-4">
          <div className="bg-gray-800 rounded-2xl p-8 md:p-12 shadow-2xl">
            <div className="text-center space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold text-white">
                Ready to Start Your AI Journey?
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto mt-8">
                <div className="text-center space-y-2">
                  <p className="text-purple-400 font-bold text-3xl">$320</p>
                  <p className="text-white font-semibold">Per Workshop</p>
                  <p className="text-gray-400 text-sm">Weekend intensive training</p>
                </div>
                <div className="text-center space-y-2 border-l border-r border-gray-700 px-4">
                  <p className="text-purple-400 font-bold text-3xl">$30</p>
                  <p className="text-white font-semibold">Monthly Subscription</p>
                  <p className="text-gray-400 text-sm">Full platform access</p>
                </div>
                <div className="text-center space-y-2">
                  <p className="text-purple-400 font-bold text-3xl">$500</p>
                  <p className="text-white font-semibold">Premium (3 months)</p>
                  <p className="text-gray-400 text-sm">Unlimited workshops</p>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                <button
                  onClick={() => router.push('/login')}
                  className="bg-purple-500 hover:bg-purple-600 text-white font-bold px-8 py-4 rounded-full text-lg transition-all hover:-translate-y-0.5 shadow-lg"
                >
                  Enroll in Next Workshop
                </button>
                <button
                  onClick={() => scrollToSection(workshopsRef)}
                  className="border border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white font-bold px-8 py-4 rounded-full text-lg transition-all"
                >
                  Browse All Workshops
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div ref={aboutRef} className="bg-gray-900 text-gray-400 py-12 border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <h4 className="text-lg font-bold text-white mb-2">
                Modern AI Pro
              </h4>
              <p className="text-sm">
                Transforming professionals into AI leaders through practical, 
                hands-on education.
              </p>
              <p className="text-sm">
                Based in San Francisco, serving learners globally.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="text-lg font-bold text-white mb-2">
                Workshops
              </h4>
              <p className="hover:text-white transition-colors cursor-pointer text-sm">AI Essentials</p>
              <p className="hover:text-white transition-colors cursor-pointer text-sm">AI Practitioner</p>
              <p className="hover:text-white transition-colors cursor-pointer text-sm">Agentic AI</p>
              <p className="hover:text-white transition-colors cursor-pointer text-sm">Executive Masterclass</p>
            </div>
            <div className="space-y-2">
              <h4 className="text-lg font-bold text-white mb-2">
                Company
              </h4>
              <p className="hover:text-white transition-colors cursor-pointer text-sm">About Us</p>
              <p className="hover:text-white transition-colors cursor-pointer text-sm">Instructors</p>
              <p className="hover:text-white transition-colors cursor-pointer text-sm">Success Stories</p>
              <p className="hover:text-white transition-colors cursor-pointer text-sm">Contact</p>
            </div>
            <div className="space-y-2">
              <h4 className="text-lg font-bold text-white mb-2">
                Connect
              </h4>
              <p className="text-sm"><EMAIL></p>
            </div>
          </div>
          <div className="mt-12 pt-8 border-t border-gray-800 text-center">
            <p className="text-sm">
              © {new Date().getFullYear()} Modern AI Pro (Mitra Robot Inc.). All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;