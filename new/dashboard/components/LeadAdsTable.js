import { useState, useEffect } from 'react';
import { authenticatedFetch } from '../lib/auth/clientAuth';

export default function LeadAdsTable({ timePeriod = '7' }) {
  const [leadAds, setLeadAds] = useState([]);
  const [summary, setSummary] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchLeadAdsData();
  }, [timePeriod]);

  const fetchLeadAdsData = async () => {
    try {
      setLoading(true);
      const response = await authenticatedFetch(`/api/facebook/lead-ads?days=${timePeriod}`);
      const data = await response.json();
      
      if (data.success) {
        setLeadAds(data.lead_ads);
        setSummary(data.summary);
        setError(null);
      } else {
        setError(data.message || 'Failed to fetch lead ads data');
      }
    } catch (err) {
      if (err.message === 'Authentication failed') {
        setError('Authentication required. Please log in again.');
      } else {
        setError('Network error: ' + err.message);
      }
      console.error('Error fetching lead ads data:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (periodLeads, adStatus) => {
    if (adStatus !== 'ACTIVE') return '⚪';
    // Adjust thresholds based on time period
    const multiplier = timePeriod === '1' ? 1 : timePeriod === '7' ? 0.5 : 0.2;
    const highThreshold = Math.max(1, Math.floor(20 * multiplier));
    const goodThreshold = Math.max(1, Math.floor(5 * multiplier));
    
    if (periodLeads >= highThreshold) return '🟢';
    if (periodLeads >= goodThreshold) return '🟡'; 
    if (periodLeads > 0) return '🟠';
    return '🔴';
  };

  const getStatusText = (periodLeads, adStatus) => {
    if (adStatus !== 'ACTIVE') return 'Inactive';
    // Adjust thresholds based on time period
    const multiplier = timePeriod === '1' ? 1 : timePeriod === '7' ? 0.5 : 0.2;
    const highThreshold = Math.max(1, Math.floor(20 * multiplier));
    const goodThreshold = Math.max(1, Math.floor(5 * multiplier));
    
    if (periodLeads >= highThreshold) return 'High Volume';
    if (periodLeads >= goodThreshold) return 'Good';
    if (periodLeads > 0) return 'Low';
    return 'No Leads';
  };

  const getStatusColor = (periodLeads, adStatus) => {
    if (adStatus !== 'ACTIVE') return 'bg-gray-100 text-gray-800';
    // Adjust thresholds based on time period
    const multiplier = timePeriod === '1' ? 1 : timePeriod === '7' ? 0.5 : 0.2;
    const highThreshold = Math.max(1, Math.floor(20 * multiplier));
    const goodThreshold = Math.max(1, Math.floor(5 * multiplier));
    
    if (periodLeads >= highThreshold) return 'bg-green-100 text-green-800';
    if (periodLeads >= goodThreshold) return 'bg-yellow-100 text-yellow-800';
    if (periodLeads > 0) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  if (loading) {
    return (
      <div id="lead-ads" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div id="lead-ads" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
        <div className="text-red-600">
          <h3 className="text-lg font-medium mb-2">❌ Error Loading Lead Ads Data</h3>
          <p className="text-sm">{error}</p>
          <button 
            onClick={fetchLeadAdsData}
            className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div id="lead-ads" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">🎯 Lead Generation Ads</h3>
          <p className="text-sm text-gray-500">
            {timePeriod === '1' ? 'Last 24 hours' : 
             timePeriod === '7' ? 'Last 7 days' : 
             timePeriod === '30' ? 'Last 30 days' : `Last ${timePeriod} days`}
          </p>
        </div>
        <button 
          onClick={fetchLeadAdsData}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 flex items-center space-x-1 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          <span>Refresh</span>
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-900">{summary.total_ads || 0}</div>
          <div className="text-sm text-blue-600">Total Lead Ads</div>
        </div>
        <div className="bg-green-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-900">{summary.active_ads || 0}</div>
          <div className="text-sm text-green-600">Active Ads</div>
        </div>
        <div className="bg-purple-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-purple-900">{summary.period_leads || 0}</div>
          <div className="text-sm text-purple-600">
            Period Leads 
            <span className="block text-xs">
              ({timePeriod === '1' ? '24h' : `${timePeriod}d`})
            </span>
          </div>
        </div>
        <div className="bg-orange-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-orange-900">{summary.cumulative_leads || 0}</div>
          <div className="text-sm text-orange-600">
            Total Leads
            <span className="block text-xs">(All time)</span>
          </div>
        </div>
      </div>

      {/* Account Summary */}
      {summary.accounts && summary.accounts.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-900 mb-3">📊 Account Summary</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {summary.accounts.map((account, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-3">
                <div className="font-medium text-gray-900">{account.account_name}</div>
                <div className="text-sm text-gray-600 mt-1">
                  {account.working_ads}/{account.total_ads} ads working • 
                  <span className="font-medium text-purple-600"> {account.period_leads || 0}</span> period leads •
                  <span className="text-gray-500"> {account.cumulative_leads || 0} total</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 px-4 font-medium text-gray-900">Ad Details</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Campaign</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Account</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Leads (Period / Total)</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Form Fields</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
            </tr>
          </thead>
          <tbody>
            {leadAds.map((ad, index) => (
              <tr key={ad.ad_id} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4">
                  <div className="font-medium text-gray-900">{ad.ad_name}</div>
                  <div className="text-sm text-gray-500">ID: {ad.ad_id}</div>
                </td>
                <td className="py-3 px-4">
                  <div className="font-medium text-gray-900">{ad.campaign_name}</div>
                  <div className="text-sm text-gray-500">{ad.campaign_objective}</div>
                </td>
                <td className="py-3 px-4 text-sm text-gray-600">{ad.account_name}</td>
                <td className="py-3 px-4">
                  <div>
                    <span className={`font-bold text-lg ${ 
                      ad.period_leads > 50 ? 'text-green-600' : 
                      ad.period_leads > 5 ? 'text-yellow-600' : 
                      ad.period_leads > 0 ? 'text-orange-600' : 'text-gray-400'
                    }`}>
                      {ad.period_leads || 0}
                    </span>
                    <span className="text-gray-400 mx-1">/</span>
                    <span className="text-sm text-gray-600">
                      {ad.cumulative_leads || 0}
                    </span>
                  </div>
                </td>
                <td className="py-3 px-4">
                  <span className="text-sm font-medium text-blue-600">
                    {ad.questionnaire_fields} fields
                  </span>
                </td>
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-2">
                    <span>{getStatusIcon(ad.period_leads, ad.ad_status)}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ad.period_leads, ad.ad_status)}`}>
                      {getStatusText(ad.period_leads, ad.ad_status)}
                    </span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {leadAds.length === 0 && !loading && (
        <div className="text-center py-8 text-gray-500">
          <div className="text-lg mb-2">🎯</div>
          <div>No lead ads data available</div>
          <div className="text-sm mt-1">Run the health check to collect lead ads data</div>
        </div>
      )}

      {summary.last_updated && (
        <div className="mt-4 text-xs text-gray-500 text-center">
          Last updated: {new Date(summary.last_updated).toLocaleString()}
        </div>
      )}
    </div>
  );
}