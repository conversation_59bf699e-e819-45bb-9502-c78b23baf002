import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

// Helper function for friendly time formatting
const getFriendlyTime = (dateString) => {
  if (!dateString || dateString === 'N/A') return 'N/A';
  
  try {
    const date = new Date(dateString.replace('T', ' ').replace('+0000', 'Z'));
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffMins < 1) return 'now';
    if (diffMins < 60) return `${diffMins}min`;
    if (diffHours < 24) return `${diffHours}h`;
    if (diffDays < 7) return `${diffDays}d`;
    
    // For older dates, show month + day
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  } catch (e) {
    return 'N/A';
  }
};

export default function LeadsTable({ refreshTrigger }) {
  const router = useRouter();
  const [leads, setLeads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({});
  const [summary, setSummary] = useState({});
  const [filters, setFilters] = useState({
    search: '',
    source: 'all',
    page: 1,
    sortBy: 'created_time',
    sortOrder: 'desc'
  });

  const fetchLeads = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        page: filters.page,
        limit: 50,
        search: filters.search,
        source: filters.source,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder
      });

      const response = await fetch(`/api/leads?${queryParams}`);
      const data = await response.json();

      if (data.success) {
        setLeads(data.leads);
        setPagination(data.pagination);
        setSummary(data.summary);
      } else {
        setError(data.error || 'Failed to fetch leads');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeads();
  }, [filters, refreshTrigger]);

  const handleSearchChange = (e) => {
    setFilters(prev => ({ ...prev, search: e.target.value, page: 1 }));
  };

  const handleSort = (column) => {
    const newSortOrder = filters.sortBy === column && filters.sortOrder === 'asc' ? 'desc' : 'asc';
    setFilters(prev => ({ ...prev, sortBy: column, sortOrder: newSortOrder, page: 1 }));
  };

  const getSortIcon = (column) => {
    if (filters.sortBy !== column) {
      return <span className="text-gray-400 ml-1">↕️</span>;
    }
    return filters.sortOrder === 'asc' ? 
      <span className="text-blue-600 ml-1">↑</span> : 
      <span className="text-blue-600 ml-1">↓</span>;
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  const getSourceOptions = () => [
    { value: 'fb_ads', label: 'Facebook Ads', color: 'bg-blue-100 text-blue-800' },
    { value: 'google_ads', label: 'Google Ads', color: 'bg-red-100 text-red-800' },
    { value: 'referral', label: 'Referral', color: 'bg-purple-100 text-purple-800' },
    { value: 'organic', label: 'Organic', color: 'bg-green-100 text-green-800' },
    { value: 'linkedin', label: 'LinkedIn', color: 'bg-indigo-100 text-indigo-800' },
    { value: 'website', label: 'Website', color: 'bg-gray-100 text-gray-800' },
    { value: 'other', label: 'Other', color: 'bg-yellow-100 text-yellow-800' }
  ];

  const getSourceInfo = (source) => {
    const options = getSourceOptions();
    return options.find(opt => opt.value === source) || { 
      value: source, 
      label: source, 
      color: 'bg-gray-100 text-gray-800' 
    };
  };

  const handleSourceChange = async (leadId, newSource) => {
    try {
      const response = await fetch('/api/leads', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: leadId,
          lead_source: newSource
        })
      });

      const data = await response.json();

      if (data.success) {
        // Update the local state
        setLeads(prevLeads => 
          prevLeads.map(lead => 
            lead.id === leadId ? { ...lead, lead_source: newSource } : lead
          )
        );
      } else {
        console.error('Failed to update lead source:', data.error);
        // Optionally show an error message to the user
      }
    } catch (error) {
      console.error('Error updating lead source:', error);
      // Optionally show an error message to the user
    }
  };

  const getStatusBadge = (status) => {
    const badges = {
      'auto-resp-1': 'bg-blue-100 text-blue-800',
      'auto-resp-2': 'bg-blue-100 text-blue-800',
      'auto-resp-3': 'bg-blue-100 text-blue-800',
      'qualified': 'bg-green-100 text-green-800',
      'contacted': 'bg-yellow-100 text-yellow-800',
      'payment-sent': 'bg-purple-100 text-purple-800',
      'enrolled': 'bg-green-100 text-green-800',
      'declined': 'bg-red-100 text-red-800'
    };
    return badges[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusLabel = (status) => {
    const labels = {
      'auto-resp-1': 'Auto-Response #1',
      'auto-resp-2': 'Auto-Response #2',
      'auto-resp-3': 'Auto-Response #3',
      'qualified': 'Qualified',
      'contacted': 'Contacted',
      'payment-sent': 'Payment Link Sent',
      'enrolled': 'Enrolled',
      'declined': 'Declined'
    };
    return labels[status] || status;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-8">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(10)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-8">
        <div className="text-center">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Leads</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={fetchLeads}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="text-2xl font-bold text-green-600">{pagination.total_leads || 0}</div>
          <div className="text-sm text-gray-600">Active Prospects</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="text-2xl font-bold text-green-600">{summary.fb_ads || 0}</div>
          <div className="text-sm text-gray-600">Facebook Ads</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="text-2xl font-bold text-orange-600">
            {Object.values(summary).reduce((a, b) => a + b, 0) - (summary.fb_ads || 0)}
          </div>
          <div className="text-sm text-gray-600">Other Sources</div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search by name, email, phone, or campaign..."
              value={filters.search}
              onChange={handleSearchChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
            />
          </div>
          <div>
            <select
              value={filters.source}
              onChange={(e) => setFilters(prev => ({ ...prev, source: e.target.value, page: 1 }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
            >
              <option value="all">All Sources</option>
              {getSourceOptions().map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Leads Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('full_name')}
                >
                  <div className="flex items-center">
                    Name
                    {getSortIcon('full_name')}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('email')}
                >
                  <div className="flex items-center">
                    Email
                    {getSortIcon('email')}
                  </div>
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Phone
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Assigned To
                </th>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('lead_source')}
                >
                  <div className="flex items-center">
                    Source
                    {getSortIcon('lead_source')}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('created_time')}
                >
                  <div className="flex items-center">
                    Created
                    {getSortIcon('created_time')}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center">
                    Status
                    {getSortIcon('status')}
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {leads.map((lead) => (
                <tr key={lead.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3 whitespace-nowrap">
                    <button
                      onClick={() => router.push(`/leads/${lead.id}`)}
                      className="text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline text-left"
                    >
                      {lead.full_name}
                    </button>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{lead.email}</div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{lead.phone}</div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="text-sm text-gray-500">
                      {lead.assigned_to === 1 ? 'Manish' :
                       lead.assigned_to === 2 ? 'Mahalakshmi' : 
                       'Unassigned'}
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <select
                      value={lead.lead_source}
                      onChange={(e) => handleSourceChange(lead.id, e.target.value)}
                      className={`text-xs font-medium border-0 rounded-full px-2 py-1 focus:ring-2 focus:ring-blue-500 text-gray-900 ${getSourceInfo(lead.lead_source).color}`}
                    >
                      {getSourceOptions().map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                    {getFriendlyTime(lead.created_time)}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    {lead.status && lead.status !== '' ? (
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadge(lead.status)}`}>
                        {getStatusLabel(lead.status)}
                      </span>
                    ) : (
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                        Not Set
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination.total_pages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange(pagination.current_page - 1)}
                disabled={!pagination.has_prev}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                  pagination.has_prev 
                    ? 'text-gray-700 bg-white hover:bg-gray-50' 
                    : 'text-gray-400 bg-gray-100 cursor-not-allowed'
                }`}
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(pagination.current_page + 1)}
                disabled={!pagination.has_next}
                className={`relative ml-3 inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                  pagination.has_next 
                    ? 'text-gray-700 bg-white hover:bg-gray-50' 
                    : 'text-gray-400 bg-gray-100 cursor-not-allowed'
                }`}
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing{' '}
                  <span className="font-medium">
                    {((pagination.current_page - 1) * pagination.leads_per_page) + 1}
                  </span>{' '}
                  to{' '}
                  <span className="font-medium">
                    {Math.min(pagination.current_page * pagination.leads_per_page, pagination.total_leads)}
                  </span>{' '}
                  of{' '}
                  <span className="font-medium">{pagination.total_leads}</span> prospects
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => handlePageChange(pagination.current_page - 1)}
                    disabled={!pagination.has_prev}
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 text-sm font-medium ${
                      pagination.has_prev 
                        ? 'text-gray-500 bg-white hover:bg-gray-50' 
                        : 'text-gray-400 bg-gray-100 cursor-not-allowed'
                    }`}
                  >
                    ←
                  </button>
                  
                  {/* Page numbers */}
                  {[...Array(Math.min(pagination.total_pages, 10))].map((_, i) => {
                    const pageNum = i + 1;
                    const isActive = pageNum === pagination.current_page;
                    
                    return (
                      <button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          isActive 
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' 
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                  
                  <button
                    onClick={() => handlePageChange(pagination.current_page + 1)}
                    disabled={!pagination.has_next}
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 text-sm font-medium ${
                      pagination.has_next 
                        ? 'text-gray-500 bg-white hover:bg-gray-50' 
                        : 'text-gray-400 bg-gray-100 cursor-not-allowed'
                    }`}
                  >
                    →
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}