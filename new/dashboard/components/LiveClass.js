import React, { useState, useEffect } from "react";
import { useAuth } from "../contexts/AuthContext";
import Create<PERSON><PERSON><PERSON> from "./CreateSurvey";
import SelectSurvey from "./SelectSurvey";

const LiveClass = () => {
  const { user } = useAuth();
  const [selectedSurvey, setSelectedSurvey] = useState("");

  useEffect(() => {
    document.title = 'Live Class | Modern AI Pro';
  }, []);

  // Check if user has instructor/admin privileges
  const hasInstructorAccess = user?.role && ['admin', 'teacher', 'executive'].includes(user.role);

  if (!hasInstructorAccess) {
    return (
      <div className="p-5">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">Access Restricted</h3>
          <p className="text-yellow-700">You must be an instructor or admin to access the live class tools.</p>
        </div>
      </div>
    );
  }

  // Function to handle selection from SelectSurvey component
  const handleSurveySelection = (value) => {
    setSelectedSurvey(value);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Live Class Tools</h1>
          <p className="text-gray-600 mb-6">
            Create and manage interactive surveys, polls, and quizzes for your workshop sessions.
          </p>
          
          <SelectSurvey onSurveySelect={handleSurveySelection} />
        </div>
        
        {selectedSurvey === "new" && (
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Create New Survey</h2>
            <CreateSurvey />
          </div>
        )}
        
        {/* Integration Section */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Video Conference Integration</h3>
          <p className="text-gray-600 mb-4">
            Connect your workshop with popular video conferencing platforms for seamless delivery.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900">Zoom</h4>
              <p className="text-sm text-gray-600">Professional video meetings</p>
            </div>
            
            <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900">Google Meet</h4>
              <p className="text-sm text-gray-600">Integrated with GSuite</p>
            </div>
            
            <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900">Microsoft Teams</h4>
              <p className="text-sm text-gray-600">Enterprise collaboration</p>
            </div>
          </div>
        </div>
        
        {/* Live Session Controls */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Session Controls</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button className="flex items-center justify-center space-x-2 bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
              <span>Start Live Session</span>
            </button>
            
            <button className="flex items-center justify-center space-x-2 bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 6h12v12H6z"/>
              </svg>
              <span>End Session</span>
            </button>
            
            <button className="flex items-center justify-center space-x-2 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <span>Launch Poll</span>
            </button>
            
            <button className="flex items-center justify-center space-x-2 bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9 11H7v9h2v-9zm4 0h-2v9h2v-9zm4 0h-2v9h2v-9zm2-7H3v2h1v11c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6h1V4zM10 2h4v2h-4V2z"/>
              </svg>
              <span>Share Screen</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveClass;