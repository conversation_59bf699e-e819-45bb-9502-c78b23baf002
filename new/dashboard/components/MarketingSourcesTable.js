import { formatCurrency, formatPercentage } from '../lib/utils';

export default function MarketingSourcesTable({ timeline }) {
  // Timeline-based data for lead sources
  const getSourceData = (period) => {
    const data = {
      today: [
        { 
          source: '📘 FB Ads', 
          leads: 8, 
          cpl: 334, 
          coca: 3200, 
          closures: 2, 
          convRate: 25.0,
          trend: '+15%',
          trendType: 'negative'
        },
        { 
          source: '🌐 Organic', 
          leads: 3, 
          cpl: 0, 
          coca: 850, 
          closures: 1, 
          convRate: 33.3,
          trend: '+50%',
          trendType: 'positive'
        },
        { 
          source: '📞 Inbound', 
          leads: 1, 
          cpl: 0, 
          coca: 1200, 
          closures: 0, 
          convRate: 0,
          trend: '-50%',
          trendType: 'negative'
        },
        { 
          source: '🔗 Referral', 
          leads: 2, 
          cpl: 0, 
          coca: 600, 
          closures: 1, 
          convRate: 50.0,
          trend: '+100%',
          trendType: 'positive'
        },
        { 
          source: '💼 LinkedIn Ads', 
          leads: 0, 
          cpl: 0, 
          coca: 0, 
          closures: 0, 
          convRate: 0,
          trend: '0%',
          trendType: 'neutral'
        },
        { 
          source: '🔍 Google Ads', 
          leads: 1, 
          cpl: 450, 
          coca: 2800, 
          closures: 0, 
          convRate: 0,
          trend: 'New',
          trendType: 'neutral'
        }
      ],
      '7d': [
        { 
          source: '📘 FB Ads', 
          leads: 45, 
          cpl: 167, 
          coca: 2950, 
          closures: 12, 
          convRate: 26.7,
          trend: '+8%',
          trendType: 'negative'
        },
        { 
          source: '🌐 Organic', 
          leads: 18, 
          cpl: 0, 
          coca: 780, 
          closures: 4, 
          convRate: 22.2,
          trend: '+25%',
          trendType: 'positive'
        },
        { 
          source: '📞 Inbound', 
          leads: 6, 
          cpl: 0, 
          coca: 1150, 
          closures: 1, 
          convRate: 16.7,
          trend: '-10%',
          trendType: 'negative'
        },
        { 
          source: '🔗 Referral', 
          leads: 12, 
          cpl: 0, 
          coca: 550, 
          closures: 6, 
          convRate: 50.0,
          trend: '+80%',
          trendType: 'positive'
        },
        { 
          source: '💼 LinkedIn Ads', 
          leads: 3, 
          cpl: 280, 
          coca: 3500, 
          closures: 0, 
          convRate: 0,
          trend: 'New',
          trendType: 'neutral'
        },
        { 
          source: '🔍 Google Ads', 
          leads: 8, 
          cpl: 320, 
          coca: 2600, 
          closures: 2, 
          convRate: 25.0,
          trend: '+60%',
          trendType: 'positive'
        }
      ],
      '30d': [
        { 
          source: '📘 FB Ads', 
          leads: 180, 
          cpl: 143, 
          coca: 2806, 
          closures: 48, 
          convRate: 26.7,
          trend: '-15%',
          trendType: 'positive'
        },
        { 
          source: '🌐 Organic', 
          leads: 65, 
          cpl: 0, 
          coca: 720, 
          closures: 18, 
          convRate: 27.7,
          trend: '+35%',
          trendType: 'positive'
        },
        { 
          source: '📞 Inbound', 
          leads: 22, 
          cpl: 0, 
          coca: 980, 
          closures: 4, 
          convRate: 18.2,
          trend: '-5%',
          trendType: 'negative'
        },
        { 
          source: '🔗 Referral', 
          leads: 38, 
          cpl: 0, 
          coca: 520, 
          closures: 19, 
          convRate: 50.0,
          trend: '+65%',
          trendType: 'positive'
        },
        { 
          source: '💼 LinkedIn Ads', 
          leads: 15, 
          cpl: 250, 
          coca: 3200, 
          closures: 2, 
          convRate: 13.3,
          trend: '+25%',
          trendType: 'positive'
        },
        { 
          source: '🔍 Google Ads', 
          leads: 28, 
          cpl: 285, 
          coca: 2400, 
          closures: 8, 
          convRate: 28.6,
          trend: '+40%',
          trendType: 'positive'
        }
      ]
    };
    return data[period] || data['30d'];
  };

  const sourceData = getSourceData(timeline);

  const getStatusColor = (convRate) => {
    if (convRate >= 30) return 'text-green-600';
    if (convRate >= 20) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendColor = (trendType) => {
    switch (trendType) {
      case 'positive': return 'text-green-600';
      case 'negative': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div id="marketing-sources" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">
            📊 LEAD SOURCES PERFORMANCE ({timeline.toUpperCase()})
          </h3>
          <p className="text-sm text-gray-600 mt-1">Attribution analysis across all marketing channels</p>
        </div>
        <button 
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 flex items-center space-x-1 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg"
          onClick={() => alert('Navigate to: Marketing Analytics')}
        >
          <span>Marketing Analytics →</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 px-4 font-medium text-gray-900">Source</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Leads</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">CPL</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">COCA</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Closures</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Conv Rate</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Trend</th>
            </tr>
          </thead>
          <tbody>
            {sourceData.map((source, index) => (
              <tr key={index} className="border-b border-gray-100">
                <td className="py-3 px-4 font-medium">{source.source}</td>
                <td className="py-3 px-4">{source.leads}</td>
                <td className="py-3 px-4">
                  {source.cpl === 0 ? 'Free' : formatCurrency(source.cpl)}
                </td>
                <td className="py-3 px-4">
                  {source.coca === 0 ? '-' : formatCurrency(source.coca)}
                </td>
                <td className="py-3 px-4 font-medium">{source.closures}</td>
                <td className={`py-3 px-4 font-medium ${getStatusColor(source.convRate)}`}>
                  {source.convRate > 0 ? formatPercentage(source.convRate) : '-'}
                </td>
                <td className={`py-3 px-4 text-sm ${getTrendColor(source.trendType)}`}>
                  {source.trend}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Summary Stats */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-green-50 rounded-lg p-3">
          <div className="text-sm font-medium text-green-800">Best Performer</div>
          <div className="text-lg font-bold text-green-900">🔗 Referrals</div>
          <div className="text-xs text-green-600">50% conversion rate</div>
        </div>
        <div className="bg-blue-50 rounded-lg p-3">
          <div className="text-sm font-medium text-blue-800">Highest Volume</div>
          <div className="text-lg font-bold text-blue-900">📘 FB Ads</div>
          <div className="text-xs text-blue-600">{sourceData[0].leads} leads ({timeline})</div>
        </div>
        <div className="bg-purple-50 rounded-lg p-3">
          <div className="text-sm font-medium text-purple-800">Most Efficient</div>
          <div className="text-lg font-bold text-purple-900">🌐 Organic</div>
          <div className="text-xs text-purple-600">₹0 CPL, great conversion</div>
        </div>
        <div className="bg-orange-50 rounded-lg p-3">
          <div className="text-sm font-medium text-orange-800">Growth Opportunity</div>
          <div className="text-lg font-bold text-orange-900">🔍 Google Ads</div>
          <div className="text-xs text-orange-600">Strong performance, scale up</div>
        </div>
      </div>
    </div>
  );
}