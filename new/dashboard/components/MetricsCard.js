import { formatCurrency } from '../lib/utils';

export default function MetricsCard({ title, metrics, isHighValue = false }) {
  if (isHighValue) {
    // Enhanced card for high-value business metrics
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
        <div className="space-y-4">
          {metrics.map((metric, index) => (
            <div key={index} className="flex flex-col">
              <div className={`text-2xl font-bold ${metric.color || 'text-gray-900'}`}>
                {metric.value}
              </div>
              <div className="text-sm font-medium text-gray-900">
                {metric.label}
              </div>
              {metric.subtext && (
                <div className="text-xs text-gray-500 mt-1">
                  {metric.subtext}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Original card for regular metrics
  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
      <div className="space-y-4">
        {metrics.map((metric, index) => (
          <div key={index} className="flex flex-col">
            <div className="text-2xl font-semibold text-gray-900">
              {metric.value}
            </div>
            <div className="text-sm text-gray-600">
              {metric.label}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}