import React, { useEffect } from "react";
import { useRouter } from "next/router";
import Link from "next/link";

const NotFoundPage = () => {
  const router = useRouter();

  useEffect(() => {
    document.title = "404 | Modern AI Pro";
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-9xl font-bold text-purple-600 mb-4">404</div>
          <div className="w-64 h-64 mx-auto mb-6 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center">
            <svg 
              className="w-32 h-32 text-purple-500" 
              fill="currentColor" 
              viewBox="0 0 24 24"
            >
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
            </svg>
          </div>
        </div>

        {/* Error Message */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Page Not Found
        </h1>
        
        <p className="text-gray-600 mb-8 leading-relaxed">
          Sorry, we couldn't find the page you're looking for. 
          The page might have been moved, deleted, or you entered the wrong URL.
        </p>

        {/* Action Buttons */}
        <div className="space-y-4">
          <button
            onClick={() => router.back()}
            className="w-full bg-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-purple-700 transition-colors"
          >
            Go Back
          </button>
          
          <Link href="/">
            <button className="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-200 transition-colors">
              Return to Home
            </button>
          </Link>
        </div>

        {/* Helpful Links */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500 mb-4">You might be looking for:</p>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <Link href="/workshops" className="text-purple-600 hover:text-purple-700 transition-colors">
              Workshops
            </Link>
            <Link href="/learn" className="text-purple-600 hover:text-purple-700 transition-colors">
              Learning Portal
            </Link>
            <Link href="/dashboard" className="text-purple-600 hover:text-purple-700 transition-colors">
              Dashboard
            </Link>
            <Link href="/community" className="text-purple-600 hover:text-purple-700 transition-colors">
              Community
            </Link>
          </div>
        </div>

        {/* Contact Support */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            Still need help? 
            <a 
              href="mailto:<EMAIL>" 
              className="font-medium text-blue-600 hover:text-blue-700 ml-1"
            >
              Contact Support
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;