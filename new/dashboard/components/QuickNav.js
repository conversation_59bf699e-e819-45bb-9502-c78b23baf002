import { useState, useEffect } from 'react';

export default function QuickNav() {
  const [activeSection, setActiveSection] = useState('');

  const sections = [
    { id: 'key-metrics', icon: '📊', label: 'Key Metrics' },
    { id: 'pipeline-growth', icon: '📈', label: 'Pipeline' },
    { id: 'campaigns', icon: '🎯', label: 'Campaigns' },
    { id: 'funnel', icon: '🔄', label: 'Funnel' },
    { id: 'sales-team', icon: '👥', label: 'Sales Team' },
    { id: 'marketing-sources', icon: '📊', label: 'Marketing' },
    { id: 'geography', icon: '🌍', label: 'Geography' },
    { id: 'events', icon: '📅', label: 'Events' },
    { id: 'kapi-pipeline', icon: '🏢', label: 'Kapi' },
    { id: 'urgent-actions', icon: '🚨', label: 'Actions' }
  ];

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 100;
      
      for (const section of sections) {
        const element = document.getElementById(section.id);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(section.id);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Set initial active section
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-50 hidden lg:block">
      <div className="bg-white shadow-lg rounded-lg border p-2 space-y-1">
        {sections.map((section) => (
          <button
            key={section.id}
            onClick={() => scrollToSection(section.id)}
            className={`w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200 group relative ${
              activeSection === section.id
                ? 'bg-blue-500 text-white shadow-md'
                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
            }`}
            title={section.label}
          >
            <span className="text-sm">{section.icon}</span>
            
            {/* Tooltip */}
            <div className="absolute right-12 bg-gray-900 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
              {section.label}
              <div className="absolute top-1/2 left-full w-0 h-0 border-l-4 border-l-gray-900 border-t-2 border-b-2 border-t-transparent border-b-transparent transform -translate-y-1/2"></div>
            </div>
          </button>
        ))}
        
        {/* Back to top button */}
        <div className="border-t pt-1 mt-2">
          <button
            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
            className="w-10 h-10 rounded-lg flex items-center justify-center text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200"
            title="Back to top"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}