import { formatPercentage } from '../lib/utils';

export default function SVGFunnel() {
  const funnelData = [
    { stage: 'Leads', count: 1194, percentage: 100, color: '#3B82F6' },
    { stage: 'Auto-Resp', count: 900, percentage: 75.4, color: '#10B981' },
    { stage: 'Contacted', count: 450, percentage: 37.7, color: '#F59E0B' },
    { stage: 'Qualified', count: 180, percentage: 15.1, color: '#EF4444' },
    { stage: 'Demo Sched', count: 90, percentage: 7.5, color: '#8B5CF6' },
    { stage: 'Demo Done', count: 72, percentage: 6.0, color: '#06B6D4' },
    { stage: 'Enrolled', count: 61, percentage: 5.1, color: '#84CC16' },
    { stage: 'Complete', count: 54, percentage: 4.5, color: '#F97316' }
  ];

  const svgWidth = 600;
  const svgHeight = 400;
  const maxWidth = 480;
  const startY = 30;
  const stepHeight = 45;

  return (
    <div id="funnel" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900">
            🎯 CONVERSION FUNNEL (Last 30 Days)
          </h3>
          <p className="text-sm text-gray-600 mt-1">Lead progression through sales stages</p>
        </div>
        <button 
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 flex items-center space-x-1 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg"
          onClick={() => alert('Navigate to: Funnel Optimizer')}
        >
          <span>Optimize Funnel →</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      
      <div className="flex flex-col lg:flex-row items-center justify-between">
        {/* SVG Funnel */}
        <div className="flex-1 flex justify-center">
          <svg width={svgWidth} height={svgHeight} viewBox={`0 0 ${svgWidth} ${svgHeight}`}>
            <defs>
              <linearGradient id="funnelGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#1E40AF" stopOpacity="0.9"/>
              </linearGradient>
            </defs>
            
            {funnelData.map((item, index) => {
              const width = (item.percentage / 100) * maxWidth;
              const x = (svgWidth - width) / 2;
              const y = startY + (index * stepHeight);
              const nextWidth = index < funnelData.length - 1 
                ? (funnelData[index + 1].percentage / 100) * maxWidth 
                : width * 0.8;
              const nextX = (svgWidth - nextWidth) / 2;
              
              return (
                <g key={index}>
                  {/* Funnel Section */}
                  <path
                    d={`M ${x} ${y} 
                        L ${x + width} ${y} 
                        L ${nextX + nextWidth} ${y + stepHeight - 5} 
                        L ${nextX} ${y + stepHeight - 5} 
                        Z`}
                    fill={item.color}
                    fillOpacity="0.8"
                    stroke="#fff"
                    strokeWidth="2"
                  />
                  
                  {/* Stage Label */}
                  <text
                    x={svgWidth / 2}
                    y={y + 20}
                    textAnchor="middle"
                    fill="white"
                    fontSize="12"
                    fontWeight="bold"
                  >
                    {item.stage}
                  </text>
                  
                  {/* Count */}
                  <text
                    x={svgWidth / 2}
                    y={y + 35}
                    textAnchor="middle"
                    fill="white"
                    fontSize="14"
                    fontWeight="bold"
                  >
                    {item.count}
                  </text>
                  
                  {/* Percentage on the side */}
                  <text
                    x={x + width + 15}
                    y={y + 25}
                    fill="#374151"
                    fontSize="12"
                    fontWeight="medium"
                  >
                    {formatPercentage(item.percentage)}
                  </text>
                  
                  {/* Drop-off indicator */}
                  {index < funnelData.length - 1 && (
                    <text
                      x={svgWidth / 2}
                      y={y + stepHeight - 8}
                      textAnchor="middle"
                      fill="#9CA3AF"
                      fontSize="10"
                    >
                      ↓ -{(item.count - funnelData[index + 1].count)}
                    </text>
                  )}
                </g>
              );
            })}
          </svg>
        </div>
        
        {/* Statistics */}
        <div className="lg:w-64 lg:ml-8 mt-6 lg:mt-0">
          <div className="space-y-4">
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-sm font-medium text-green-800">Conversion Rate</div>
              <div className="text-2xl font-bold text-green-900">4.5%</div>
              <div className="text-xs text-green-600">Leads → Complete</div>
            </div>
            
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-sm font-medium text-blue-800">Total Dropoff</div>
              <div className="text-2xl font-bold text-blue-900">1,140</div>
              <div className="text-xs text-blue-600">95.5% at various stages</div>
            </div>
            
            <div className="bg-orange-50 rounded-lg p-4">
              <div className="text-sm font-medium text-orange-800">Biggest Drop</div>
              <div className="text-2xl font-bold text-orange-900">450 → 180</div>
              <div className="text-xs text-orange-600">Contacted → Qualified</div>
            </div>
            
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="text-sm font-medium text-purple-800">Best Stage</div>
              <div className="text-2xl font-bold text-purple-900">Demo Done</div>
              <div className="text-xs text-purple-600">85% proceed to enroll</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}