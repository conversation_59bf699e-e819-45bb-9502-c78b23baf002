import { formatPercentage } from '../lib/utils';

export default function SalesTeamTable() {
  const salesData = [
    { 
      name: '<PERSON><PERSON>', 
      leadsAssigned: 82, 
      callsMade: 68, 
      closures: 18, 
      rate: 22.0, 
      status: 'excellent' 
    },
    { 
      name: 'Manish', 
      leadsAssigned: 169, 
      callsMade: 142, 
      closures: 36, 
      rate: 21.3, 
      status: 'good' 
    },
    { 
      name: '<PERSON><PERSON>', 
      leadsAssigned: 80, 
      callsMade: 45, 
      closures: 0, 
      rate: 0.0, 
      status: 'poor' 
    },
    { 
      name: '<PERSON><PERSON>', 
      leadsAssigned: 158, 
      callsMade: 134, 
      closures: 7, 
      rate: 4.4, 
      status: 'good' 
    }
  ];

  const getStatusIndicator = (status) => {
    switch (status) {
      case 'excellent': return '🟢';
      case 'good': return '🟡';
      case 'poor': return '🔴';
      default: return '⚪';
    }
  };

  const getStatusBadge = (status, rate) => {
    if (status === 'excellent') return '🏆';
    if (status === 'poor') return '⚠️';
    return '';
  };

  return (
    <div id="sales-team" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">
            👥 SALES TEAM PERFORMANCE
          </h3>
          <p className="text-sm text-gray-600 mt-1">Individual rep performance and conversion rates</p>
        </div>
        <button 
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 flex items-center space-x-1 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg"
          onClick={() => alert('Navigate to: Team Management')}
        >
          <span>Team Management →</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 px-4 font-medium text-gray-900">Rep</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Leads Assigned</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Calls Made</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Closures (30-day)</th>
            </tr>
          </thead>
          <tbody>
            {salesData.map((rep, index) => (
              <tr key={index} className="border-b border-gray-100">
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-2">
                    <span>{getStatusIndicator(rep.status)}</span>
                    <span className="font-medium">{rep.name}</span>
                  </div>
                </td>
                <td className="py-3 px-4 text-gray-700">{rep.leadsAssigned}</td>
                <td className="py-3 px-4 text-gray-700">{rep.callsMade}</td>
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">
                      {rep.closures} ({formatPercentage(rep.rate)} rate)
                    </span>
                    <span>{getStatusBadge(rep.status, rep.rate)}</span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}