import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { authenticatedFetch } from '../lib/auth/clientAuth';

export default function SearchableTable({ 
  apiEndpoint,
  columns,
  title,
  description,
  searchPlaceholder = "Search...",
  filters: additionalFilters = [],
  defaultSortBy = 'created_time',
  defaultSortOrder = 'desc',
  defaultPageSize = 20,
  onRowClick = null,
  renderStats = null,
  className = ""
}) {
  const router = useRouter();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState(null);
  const [filters, setFilters] = useState({
    page: 1,
    limit: defaultPageSize,
    search: '',
    sortBy: defaultSortBy,
    sortOrder: defaultSortOrder,
    // Initialize additional filters with default values
    ...additionalFilters.reduce((acc, filter) => {
      acc[filter.key] = filter.defaultValue || 'all';
      return acc;
    }, {})
  });
  const [pagination, setPagination] = useState({});

  useEffect(() => {
    fetchData();
  }, [filters]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      
      // Add all filters as query parameters
      Object.entries(filters).forEach(([key, value]) => {
        params.append(key, value.toString());
      });

      const response = await authenticatedFetch(`${apiEndpoint}?${params}`);
      const result = await response.json();

      if (result.success) {
        // Handle different API response structures
        if (result.data) {
          // Payments API structure
          setData(result.data.payments || result.data.items || []);
          setStats(result.data.stats);
          setPagination(result.data.pagination);
        } else {
          // Leads API structure
          setData(result.leads || result.items || []);
          setStats(result.stats || result.summary);
          setPagination(result.pagination);
        }
      } else {
        setError(result.message || result.error || 'Failed to fetch data');
      }
    } catch (err) {
      if (err.message === 'Authentication failed') {
        setError('Authentication required. Please log in again.');
      } else if (err.message === 'No authentication token available') {
        setError('Not authenticated. Please log in.');
      } else {
        setError('Network error: ' + err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    setFilters({ ...filters, search: e.target.value, page: 1 });
  };

  const handleFilterChange = (filterKey, value) => {
    setFilters({ ...filters, [filterKey]: value, page: 1 });
  };

  const handleSort = (column) => {
    const newSortOrder = filters.sortBy === column && filters.sortOrder === 'asc' ? 'desc' : 'asc';
    setFilters({ ...filters, sortBy: column, sortOrder: newSortOrder, page: 1 });
  };

  const handlePageChange = (newPage) => {
    setFilters({ ...filters, page: newPage });
  };

  const getSortIcon = (column) => {
    if (filters.sortBy !== column) {
      return <span className="text-gray-400 ml-1">↕️</span>;
    }
    return filters.sortOrder === 'asc' ? 
      <span className="text-blue-600 ml-1">↑</span> : 
      <span className="text-blue-600 ml-1">↓</span>;
  };

  const handleRowClick = (row) => {
    if (onRowClick) {
      onRowClick(row);
    }
  };

  const handleRowUpdate = (rowId, updateData) => {
    setData(prevData => 
      prevData.map(row => 
        row.id === rowId ? { ...row, ...updateData } : row
      )
    );
  };

  if (loading && data.length === 0) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Stats Section */}
      {renderStats && stats && (
        <div className="mb-6">
          {renderStats(stats)}
        </div>
      )}

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search Input */}
          <div className="flex-1">
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              id="search"
              placeholder={searchPlaceholder}
              value={filters.search}
              onChange={handleSearchChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
            />
          </div>

          {/* Additional Filters */}
          {additionalFilters.map((filter) => (
            <div key={filter.key} className="sm:w-48">
              <label htmlFor={filter.key} className="block text-sm font-medium text-gray-700 mb-1">
                {filter.label}
              </label>
              <select
                id={filter.key}
                value={filters[filter.key]}
                onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
              >
                {filter.options.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          ))}
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        {title && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
            {description && <p className="text-sm text-gray-600 mt-1">{description}</p>}
          </div>
        )}
        
        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th 
                    key={column.key}
                    className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                      column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                    }`}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center">
                      {column.label}
                      {column.sortable && getSortIcon(column.key)}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.map((row, index) => (
                <tr 
                  key={row.id || index} 
                  className={`hover:bg-gray-50 ${onRowClick ? 'cursor-pointer' : ''}`}
                  onClick={(e) => {
                    // Don't trigger row click if clicking on assignment dropdown
                    if (e.target.closest('[data-assignment-dropdown]')) {
                      e.stopPropagation();
                      return;
                    }
                    handleRowClick(row);
                  }}
                >
                  {columns.map((column) => (
                    <td key={column.key} className="px-6 py-4 whitespace-nowrap">
                      {column.render ? column.render(row, { onRowUpdate: handleRowUpdate, columnKey: column.key }) : (
                        <div className="text-sm text-gray-900">
                          {row[column.key] || 'N/A'}
                        </div>
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {(pagination.totalPages || pagination.total_pages) > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange(Math.max(1, (pagination.page || pagination.current_page) - 1))}
                disabled={(pagination.page || pagination.current_page) === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(Math.min((pagination.totalPages || pagination.total_pages), (pagination.page || pagination.current_page) + 1))}
                disabled={(pagination.page || pagination.current_page) === (pagination.totalPages || pagination.total_pages)}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing{' '}
                  <span className="font-medium">
                    {((pagination.page || pagination.current_page) - 1) * (pagination.limit || pagination.leads_per_page) + 1}
                  </span>{' '}
                  to{' '}
                  <span className="font-medium">
                    {Math.min((pagination.page || pagination.current_page) * (pagination.limit || pagination.leads_per_page), (pagination.total || pagination.total_leads))}
                  </span>{' '}
                  of{' '}
                  <span className="font-medium">{pagination.total || pagination.total_leads}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => handlePageChange(Math.max(1, (pagination.page || pagination.current_page) - 1))}
                    disabled={(pagination.page || pagination.current_page) === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  {[...Array(Math.min(5, (pagination.totalPages || pagination.total_pages)))].map((_, i) => {
                    const pageNum = i + 1;
                    return (
                      <button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          pageNum === (pagination.page || pagination.current_page)
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                  <button
                    onClick={() => handlePageChange(Math.min((pagination.totalPages || pagination.total_pages), (pagination.page || pagination.current_page) + 1))}
                    disabled={(pagination.page || pagination.current_page) === (pagination.totalPages || pagination.total_pages)}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}

        {data.length === 0 && !loading && (
          <div className="p-8 text-center">
            <div className="text-4xl mb-4">📋</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No data found</h3>
            <p className="text-gray-600">
              {filters.search || Object.values(filters).some(v => v !== 'all' && v !== 1 && v !== '' && v !== defaultSortBy && v !== defaultSortOrder) 
                ? 'Try adjusting your filters to see more results.'
                : 'No data available yet.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}