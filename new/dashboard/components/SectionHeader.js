export default function SectionHeader({ 
  title, 
  moreLink, 
  moreLinkText = "View Details →", 
  id,
  description 
}) {
  return (
    <div id={id} className="flex justify-between items-center mb-4 scroll-mt-20">
      <div>
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        {description && (
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        )}
      </div>
      {moreLink && (
        <button 
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 flex items-center space-x-1 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg"
          onClick={() => {
            // For now, just show an alert - later we'll navigate to actual pages
            alert(`Navigate to: ${moreLink}`);
          }}
        >
          <span>{moreLinkText}</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      )}
    </div>
  );
}