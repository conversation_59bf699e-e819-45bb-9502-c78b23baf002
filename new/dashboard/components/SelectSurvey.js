import React, { useState, useEffect } from "react";

const SelectSurvey = ({ onSurveySelect }) => {
  const [selectedSurvey, setSelectedSurvey] = useState("");
  const [existingSurveys, setExistingSurveys] = useState([]);

  useEffect(() => {
    // Simulate fetching existing surveys - would integrate with API
    const mockSurveys = [
      { id: 1, name: "Workshop Feedback Poll", type: "poll" },
      { id: 2, name: "AI Knowledge Quiz", type: "quiz" },
      { id: 3, name: "Learning Progress Check", type: "single_sentence" }
    ];
    
    setExistingSurveys(mockSurveys);
  }, []);

  const handleSelectionChange = (e) => {
    const value = e.target.value;
    setSelectedSurvey(value);
    onSurveySelect(value);
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Survey Action
        </label>
        <select
          value={selectedSurvey}
          onChange={handleSelectionChange}
          className="form-select"
        >
          <option value="">Choose an option...</option>
          <option value="new">Create New Survey</option>
          {existingSurveys.map((survey) => (
            <option key={survey.id} value={survey.id}>
              Launch: {survey.name} ({survey.type})
            </option>
          ))}
        </select>
      </div>

      {selectedSurvey && selectedSurvey !== "new" && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-900 mb-2">Survey Selected</h4>
          <p className="text-blue-800 text-sm mb-3">
            Ready to launch the selected survey in your live session.
          </p>
          <div className="flex space-x-2">
            <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
              Launch Now
            </button>
            <button className="px-4 py-2 border border-blue-600 text-blue-600 rounded hover:bg-blue-50 transition-colors">
              Preview
            </button>
          </div>
        </div>
      )}

      {existingSurveys.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Recent Surveys</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {existingSurveys.map((survey) => (
              <div key={survey.id} className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                <h5 className="font-medium text-gray-900 text-sm">{survey.name}</h5>
                <p className="text-xs text-gray-500 capitalize">{survey.type}</p>
                <div className="mt-2 flex space-x-2">
                  <button className="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded hover:bg-purple-200 transition-colors">
                    Edit
                  </button>
                  <button className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors">
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectSurvey;