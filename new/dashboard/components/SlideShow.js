import React, { useEffect, useRef, useState } from "react";

const GoogleSlides = ({ 
  width = "100%", 
  height = 480, 
  slidesLink, 
  slideDuration = 0, 
  position = 1, 
  showControls = true, 
  loop = false,
  autoAdvance = false 
}) => {
  const [currentPosition, setCurrentPosition] = useState(position);
  const [isPlaying, setIsPlaying] = useState(autoAdvance);
  const timerRef = useRef(null);
  
  // Extract presentation ID from the link
  const getPresentationId = (link) => {
    if (!link) return "";
    // Extract ID from various Google Slides URL formats
    const regex = /\/d\/([a-zA-Z0-9-_]+)/;
    const match = link.match(regex);
    return match ? match[1] : "";
  };
  
  const presentationId = getPresentationId(slidesLink);
  
  // Format the embed URL with parameters
  const getEmbedUrl = () => {
    if (!presentationId) return "";
    
    let url = `https://docs.google.com/presentation/d/e/${presentationId}/embed?`;
    
    const params = [];
    
    if (currentPosition > 1) {
      params.push(`slide=id.g${currentPosition}`);
    }
    
    params.push('start=false'); // Don't auto-start
    
    if (!showControls) {
      params.push('rm=minimal');
    }
    
    if (loop) {
      params.push('loop=true');
    }
    
    return url + params.join('&');
  };

  // Handle slide advancement
  useEffect(() => {
    if (isPlaying && slideDuration > 0) {
      timerRef.current = setInterval(() => {
        setCurrentPosition(prev => prev + 1);
      }, slideDuration * 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isPlaying, slideDuration]);

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const goToSlide = (slideNumber) => {
    setCurrentPosition(slideNumber);
  };

  const nextSlide = () => {
    setCurrentPosition(prev => prev + 1);
  };

  const prevSlide = () => {
    setCurrentPosition(prev => Math.max(1, prev - 1));
  };

  if (!slidesLink || !presentationId) {
    return (
      <div className="bg-gray-100 rounded-lg p-8 text-center border border-gray-300">
        <div className="text-gray-500 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">No Presentation Loaded</h3>
        <p className="text-gray-600">Please provide a valid Google Slides link to display the presentation.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      {/* Presentation Header */}
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Workshop Presentation</h3>
          
          {/* Presentation Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={prevSlide}
              disabled={currentPosition <= 1}
              className="p-2 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Previous Slide"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"/>
              </svg>
            </button>

            <span className="text-sm text-gray-600 px-2">
              Slide {currentPosition}
            </span>

            <button
              onClick={nextSlide}
              className="p-2 rounded hover:bg-gray-200 transition-colors"
              title="Next Slide"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8.59 16.09l4.58-4.59-4.58-4.59L10 5.5l6 6-6 6z"/>
              </svg>
            </button>

            {slideDuration > 0 && (
              <button
                onClick={togglePlayPause}
                className={`p-2 rounded transition-colors ${
                  isPlaying ? 'bg-red-100 text-red-600 hover:bg-red-200' : 'bg-green-100 text-green-600 hover:bg-green-200'
                }`}
                title={isPlaying ? "Pause Auto-Advance" : "Start Auto-Advance"}
              >
                {isPlaying ? (
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Presentation Iframe */}
      <div className="relative" style={{ height: `${height}px` }}>
        <iframe
          key={currentPosition} // Force re-render when position changes
          src={getEmbedUrl()}
          width="100%"
          height="100%"
          frameBorder="0"
          allowFullScreen
          title="Google Slides Presentation"
          className="w-full h-full"
        />
        
        {/* Loading overlay */}
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-gray-600">Loading presentation...</p>
          </div>
        </div>
      </div>

      {/* Presentation Footer */}
      {(isPlaying && slideDuration > 0) && (
        <div className="bg-blue-50 px-4 py-2 border-t border-blue-200">
          <div className="flex items-center justify-center space-x-2 text-sm text-blue-800">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span>Auto-advancing every {slideDuration} seconds</span>
          </div>
        </div>
      )}
    </div>
  );
};

// Default SlideShow component with sample presentation
const SlideShow = ({ 
  slidesLink = "https://docs.google.com/presentation/d/1hXcru7QqAy7SkvVoNnODCfvb5o9R0wtf4Eik1xGSZJc",
  ...props 
}) => {
  return (
    <div className="space-y-4">
      <GoogleSlides
        width="100%"
        height={480}
        slidesLink={slidesLink}
        slideDuration={5}
        position={1}
        showControls={true}
        loop={false}
        autoAdvance={false}
        {...props}
      />
      
      {!slidesLink && (
        <div className="text-center text-sm text-gray-500">
          <p>To use this component, provide a Google Slides sharing link.</p>
          <p className="mt-1">Make sure the presentation is set to "Anyone with the link can view".</p>
        </div>
      )}
    </div>
  );
};

export default SlideShow;