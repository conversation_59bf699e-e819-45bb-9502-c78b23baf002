import { useState } from 'react';

export default function TimelineSelector({ activeTimeline, onTimelineChange }) {
  const timelines = [
    { id: 'today', label: 'Today', icon: '📅' },
    { id: '7d', label: '7 Days', icon: '📊' },
    { id: '30d', label: '30 Days', icon: '📈' },
    { id: '90d', label: '90 Days', icon: '📉' },
    { id: 'all', label: 'All Time', icon: '🕐' }
  ];

  return (
    <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
      {timelines.map((timeline) => (
        <button
          key={timeline.id}
          onClick={() => onTimelineChange(timeline.id)}
          className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTimeline === timeline.id
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
          }`}
        >
          <span>{timeline.icon}</span>
          <span>{timeline.label}</span>
        </button>
      ))}
    </div>
  );
}