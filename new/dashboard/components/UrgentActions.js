export default function UrgentActions() {
  const immediateActions = [
    'PAUSE Junior_AI_Basic campaign (₹8,100 COCA too high)',
    'SCALE Tech_Lead_LLM budget by 150% (best COCA at ₹3,200)',
    'SHIFT 30% budget from Mumbai to Bangalore (₹125 vs ₹158 CPL)'
  ];

  const weeklyActions = [
    'A/B test Day 3 email subject lines (28% open rate too low)',
    'Increase WhatsApp sequence allocation (52% response rate)',
    'Geographic expansion to US West (12.1% conv vs 7.1% average)'
  ];

  const impact = [
    'Cost reduction: ₹45,000/month (16% improvement)',
    'Lead volume increase: +35% with same budget',
    'Conversion rate improvement: +2.8% overall'
  ];

  return (
    <div id="urgent-actions" className="bg-white rounded-lg shadow-sm border p-6 scroll-mt-20">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">
            🚨 URGENT OPTIMIZATION ACTIONS
          </h3>
          <p className="text-sm text-gray-600 mt-1">Immediate actions to improve performance and reduce costs</p>
        </div>
        <button 
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 flex items-center space-x-1 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg"
          onClick={() => alert('Navigate to: Action Center')}
        >
          <span>Action Center →</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      
      <div className="space-y-6">
        {/* Immediate Actions */}
        <div>
          <h4 className="text-md font-medium text-red-600 mb-3">
            🔥 IMMEDIATE (Next 2 hours):
          </h4>
          <ul className="space-y-2">
            {immediateActions.map((action, index) => (
              <li key={index} className="flex items-start space-x-2">
                <span className="text-red-500 mt-1">•</span>
                <span className="text-sm text-gray-700">{action}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Weekly Actions */}
        <div>
          <h4 className="text-md font-medium text-orange-600 mb-3">
            📈 THIS WEEK:
          </h4>
          <ul className="space-y-2">
            {weeklyActions.map((action, index) => (
              <li key={index} className="flex items-start space-x-2">
                <span className="text-orange-500 mt-1">•</span>
                <span className="text-sm text-gray-700">{action}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Impact */}
        <div className="bg-green-50 rounded-lg p-4">
          <h4 className="text-md font-medium text-green-700 mb-3">
            💡 ESTIMATED IMPACT:
          </h4>
          <ul className="space-y-2">
            {impact.map((item, index) => (
              <li key={index} className="flex items-start space-x-2">
                <span className="text-green-600 mt-1">•</span>
                <span className="text-sm text-green-700 font-medium">{item}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}