import React, { useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../contexts/AuthContext';
import Logo from './layout/Logo';

const WelcomeDashboard = () => {
  const canvasRef = useRef(null);
  const router = useRouter();
  const { user } = useAuth();
  
  // Get user's first name
  const firstName = user?.firstName || 'Modern AI Pro Learner';
  
  useEffect(() => {
    document.title = 'Welcome | Modern AI Pro';
    
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width = window.innerWidth;
    const height = canvas.height = window.innerHeight;
    
    // Confetti pieces array
    const confetti = [];
    
    // Animation control
    let animationFrameId;
    let isAnimating = true;
    
    // Confetti colors - using our theme colors (purple tones)
    const colors = ['#ffffff', '#9333ea', '#6b21a8', '#a855f7', '#c084fc'];
    
    // Create confetti pieces
    const createConfetti = () => {
      for (let i = 0; i < 200; i++) {
        confetti.push({
          x: Math.random() * width,
          y: Math.random() * height - height,
          width: Math.random() * 10 + 5,
          height: Math.random() * 10 + 5,
          speed: Math.random() * 3 + 2,
          color: colors[Math.floor(Math.random() * colors.length)],
          rotation: Math.random() * 360,
          rotationSpeed: (Math.random() - 0.5) * 2,
          oscillationSpeed: Math.random() * 0.5 + 0.5,
          oscillationAmplitude: Math.random() * 5
        });
      }
    };
    
    // Animation loop
    const animate = () => {
      if (!isAnimating) {
        ctx.clearRect(0, 0, width, height);
        return;
      }
      
      ctx.clearRect(0, 0, width, height);
      
      confetti.forEach((piece, index) => {
        // Update position
        piece.y += piece.speed;
        piece.x += Math.sin(piece.y * piece.oscillationSpeed) * piece.oscillationAmplitude;
        piece.rotation += piece.rotationSpeed;
        
        // Draw confetti piece
        ctx.save();
        ctx.translate(piece.x, piece.y);
        ctx.rotate((piece.rotation * Math.PI) / 180);
        
        ctx.fillStyle = piece.color;
        ctx.fillRect(-piece.width / 2, -piece.height / 2, piece.width, piece.height);
        
        ctx.restore();
        
        // Remove pieces that are out of screen
        if (piece.y > height) {
          // Put the piece back at the top
          piece.y = -piece.height;
          piece.x = Math.random() * width;
        }
      });
      
      // Continue animation
      animationFrameId = requestAnimationFrame(animate);
    };
    
    // Initial confetti creation
    createConfetti();
    
    // Start animation
    animate();
    
    // Stop confetti after 10 seconds
    const confettiTimer = setTimeout(() => {
      isAnimating = false;
      cancelAnimationFrame(animationFrameId);
      ctx.clearRect(0, 0, width, height);
    }, 10000);
    
    // Handle window resize
    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(confettiTimer);
      cancelAnimationFrame(animationFrameId);
      isAnimating = false;
    };
  }, []);

  const handleContinueLearning = () => {
    router.push('/learn');
  };

  const handlePairProgramming = () => {
    router.push('/pair-programming');
  };

  const handleExploreWorkshops = () => {
    router.push('/workshops');
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen relative bg-gray-900 text-white overflow-hidden">
      <canvas
        ref={canvasRef}
        className="absolute w-full h-full"
        style={{ zIndex: 0 }}
      />
      
      <div className="relative z-10 text-center p-8 max-w-4xl mx-auto">
        <div className="space-y-6 mb-10">
          <div className="text-6xl">
            <Logo isDark={false} />
          </div>
          <h1 className="text-4xl font-bold text-white" style={{ textShadow: '0 0 10px rgba(0,0,0,0.3)' }}>
            Welcome back, {firstName}!
          </h1>
          <p className="text-xl text-white">
            Ready to continue your AI mastery journey?
          </p>
        </div>
        
        <div className="bg-white bg-opacity-5 backdrop-blur-md rounded-xl p-6 mb-8 border border-purple-700"
             style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.2)' }}>
          <div className="space-y-4">
            <h3 className="text-2xl font-bold mb-4 text-white">
              Your Learning Dashboard
            </h3>
            
            <div className="flex justify-between flex-wrap">
              <div className="min-w-48 p-3">
                <p className="text-gray-400">Current ELO Rating</p>
                <p className="text-2xl text-white font-bold">{user?.elo_rating || 1000}</p>
              </div>
              
              <div className="min-w-48 p-3">
                <p className="text-gray-400">Challenges Completed</p>
                <p className="text-2xl text-white font-bold">{user?.total_challenges_completed || 0}</p>
              </div>
              
              <div className="min-w-48 p-3">
                <p className="text-gray-400">Current Streak</p>
                <p className="text-2xl text-white font-bold">{user?.streak_count || 0} days</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex gap-5 mt-10 justify-center flex-wrap">
          <button 
            className="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-4 px-8 rounded-lg h-18 w-50 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg"
            style={{ boxShadow: '0 4px 8px rgba(0,0,0,0.3)' }}
            onClick={handleContinueLearning}
          >
            Continue Learning
          </button>
          
          <button 
            className="bg-purple-800 hover:bg-purple-700 text-white font-semibold py-4 px-8 rounded-lg h-18 w-50 transition-all duration-200 transform hover:-translate-y-1"
            style={{ boxShadow: '0 4px 8px rgba(0,0,0,0.3)' }}
            onClick={handlePairProgramming}
          >
            Pair Programming
          </button>
          
          <button 
            className="bg-white bg-opacity-15 hover:bg-opacity-25 text-white font-semibold py-4 px-8 rounded-lg h-18 w-50 transition-all duration-200 transform hover:-translate-y-1"
            style={{ boxShadow: '0 4px 8px rgba(0,0,0,0.3)' }}
            onClick={handleExploreWorkshops}
          >
            Explore Workshops
          </button>
        </div>
        
        <p className="mt-16 text-sm text-gray-400">
          Your personalized learning path is updated based on your recent activity
        </p>
      </div>
    </div>
  );
};

export default WelcomeDashboard;