import React, { useRef, useEffect, useState } from "react";
import { Chart, registerables } from "chart.js";
import { WordCloudController, WordElement } from "chartjs-chart-wordcloud";

// Register Chart.js components and word cloud controller and element
Chart.register(...registerables, WordCloudController, WordElement);

const WordCloudChart = ({ 
  question = "How do you feel about the course so far?",
  words = null,
  autoUpdate = false,
  updateInterval = 3000,
  onWordsUpdate = null 
}) => {
  const chartRef = useRef(null);
  const chartInstanceRef = useRef(null);
  
  // Default feedback words
  const defaultFeedbackWords = [
    "insightful", "challenging", "engaging", "difficult", "rewarding",
    "intense", "informative", "overwhelming", "motivating", "transformative",
    "excellent", "helpful", "confusing", "clear", "innovative",
    "practical", "theoretical", "hands-on", "comprehensive", "detailed"
  ];

  // Function to generate random feedback words
  const generateRandomWords = () => {
    return defaultFeedbackWords
      .map((word) => ({
        text: word,
        weight: Math.floor(Math.random() * 50) + 10,
      }))
      .filter(() => Math.random() < 0.6) // 60% chance to include each word
      .slice(0, 15); // Limit to 15 words for better visualization
  };

  const [currentWords, setCurrentWords] = useState(words || generateRandomWords());

  // Function to create/update the chart
  const updateChart = (wordData) => {
    if (chartRef.current && wordData.length > 0) {
      const ctx = chartRef.current.getContext("2d");

      // Destroy existing chart instance
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
      }

      // Create new chart instance
      chartInstanceRef.current = new Chart(ctx, {
        type: WordCloudController.id,
        data: {
          labels: wordData.map((word) => word.text),
          datasets: [
            {
              label: "Feedback",
              data: wordData,
              color: (ctx) => {
                // Generate colors based on word weight
                const weight = ctx.parsed;
                const opacity = Math.min(weight / 50, 1);
                const colors = [
                  `rgba(147, 51, 234, ${opacity})`,   // Purple
                  `rgba(59, 130, 246, ${opacity})`,   // Blue
                  `rgba(16, 185, 129, ${opacity})`,   // Green
                  `rgba(245, 158, 11, ${opacity})`,   // Yellow
                  `rgba(239, 68, 68, ${opacity})`,    // Red
                ];
                return colors[ctx.dataIndex % colors.length];
              },
              size: (ctx) => {
                // Size based on weight
                const weight = ctx.parsed;
                return Math.max(weight * 0.8, 12); // Minimum size of 12px
              },
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: question,
              font: {
                size: 18,
                weight: "bold",
              },
              color: "#374151",
              padding: {
                bottom: 20,
              }
            },
            legend: {
              display: false,
            },
            tooltip: {
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              titleColor: '#fff',
              bodyColor: '#fff',
              callbacks: {
                label: function(context) {
                  return `${context.label}: ${context.parsed} mentions`;
                }
              }
            }
          },
          layout: {
            padding: 20,
          }
        },
      });
    }
  };

  // Initialize chart
  useEffect(() => {
    updateChart(currentWords);

    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
      }
    };
  }, []);

  // Update chart when words change
  useEffect(() => {
    updateChart(currentWords);
  }, [currentWords, question]);

  // Auto-update functionality
  useEffect(() => {
    if (autoUpdate) {
      const intervalId = setInterval(() => {
        const newWords = generateRandomWords();
        setCurrentWords(newWords);
        
        if (onWordsUpdate) {
          onWordsUpdate(newWords);
        }
      }, updateInterval);

      return () => clearInterval(intervalId);
    }
  }, [autoUpdate, updateInterval]);

  // Update words when prop changes
  useEffect(() => {
    if (words) {
      setCurrentWords(words);
    }
  }, [words]);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="h-96 relative">
        <canvas ref={chartRef} className="w-full h-full" />
      </div>
      
      {/* Word Cloud Summary */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex justify-between items-center text-sm text-gray-600 mb-3">
          <span>Total Words: <strong className="text-gray-900">{currentWords.length}</strong></span>
          {autoUpdate && (
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Live Updates</span>
            </div>
          )}
        </div>
        
        {/* Top words list */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
          {currentWords
            .sort((a, b) => b.weight - a.weight)
            .slice(0, 8)
            .map((word, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="font-medium text-gray-900 truncate">{word.text}</span>
                <span className="text-gray-600 ml-1">{word.weight}</span>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default WordCloudChart;