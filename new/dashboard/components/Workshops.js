import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { 
  FaCalendar, 
  FaMapMarkerAlt, 
  FaClock, 
  FaUsers, 
  FaDollarSign, 
  FaStar, 
  FaVideo, 
  FaFilter, 
  FaChevronDown,
  FaCheck,
  FaTimes,
  FaBookmark,
  FaGlobe,
  FaDownload,
  FaPlay
} from 'react-icons/fa';

// Mock data - would come from API
const workshopsData = [
  {
    id: 1,
    title: "Building Production-Ready LLM Applications",
    banner: "https://images.unsplash.com/photo-1524419986249-348e8fa6ad4a?w=800&auto=format&fit=crop",
    description: "Learn how to develop, deploy, and maintain large language model applications at scale with industry best practices",
    startDate: "2024-02-10T09:00:00",
    endDate: "2024-02-12T17:00:00",
    geography: "Virtual",
    isVirtual: true,
    cost: 149.99,
    capacity: 50,
    enrolled: 38,
    tags: ["LLMs", "Production", "Deployment"],
    level: "Advanced",
    instructors: [
      {
        id: 101,
        name: "<PERSON>. <PERSON>",
        title: "AI Research Lead at TechCorp",
        avatar: "https://i.pravatar.cc/150?u=maya"
      },
      {
        id: 102,
        name: "<PERSON> <PERSON>",
        title: "Senior ML Engineer",
        avatar: "https://i.pravatar.cc/150?u=james"
      }
    ],
    rating: 4.9,
    reviews: 42,
    recorded: true,
    status: "open"
  },
  {
    id: 2,
    title: "Data Engineering for ML Pipelines",
    banner: "https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?w=800&auto=format&fit=crop",
    description: "Master the art of building robust data pipelines for training and serving machine learning models",
    startDate: "2024-02-15T10:00:00",
    endDate: "2024-02-16T16:00:00",
    geography: "New York, NY",
    isVirtual: false,
    cost: 299.99,
    capacity: 30,
    enrolled: 27,
    tags: ["Data Engineering", "ETL", "MLOps"],
    level: "Intermediate",
    instructors: [
      {
        id: 103,
        name: "Sophia Rodriguez",
        title: "Principal Data Engineer",
        avatar: "https://i.pravatar.cc/150?u=sophia"
      }
    ],
    rating: 4.8,
    reviews: 36,
    recorded: true,
    status: "almost-full"
  },
  {
    id: 3,
    title: "Reinforcement Learning from Human Feedback",
    banner: "https://images.unsplash.com/photo-1591696331111-ef9586a5b17a?w=800&auto=format&fit=crop",
    description: "Explore RLHF techniques used to train and align large language models with human preferences",
    startDate: "2024-03-05T09:00:00",
    endDate: "2024-03-07T17:00:00",
    geography: "Virtual",
    isVirtual: true,
    cost: 199.99,
    capacity: 40,
    enrolled: 18,
    tags: ["RLHF", "LLMs", "Alignment"],
    level: "Advanced",
    instructors: [
      {
        id: 104,
        name: "Dr. Marcus Johnson",
        title: "AI Researcher",
        avatar: "https://i.pravatar.cc/150?u=marcus"
      },
      {
        id: 105,
        name: "Olivia Park",
        title: "ML Engineer",
        avatar: "https://i.pravatar.cc/150?u=olivia"
      }
    ],
    rating: 4.7,
    reviews: 12,
    recorded: true,
    status: "open"
  }
];

const myWorkshopsData = [
  {
    id: 101,
    title: "Advanced NLP Techniques",
    date: "May 15-17, 2023",
    status: "completed",
    certificate: true,
    recordingsAvailable: true
  },
  {
    id: 102,
    title: "Data Engineering for ML Pipelines",
    date: "February 15-16, 2024",
    status: "upcoming",
    paymentComplete: true
  }
];

// Workshop Card Component
const WorkshopCard = ({ workshop }) => {
  const router = useRouter();
  
  // Format dates
  const startDate = new Date(workshop.startDate);
  const endDate = new Date(workshop.endDate);
  
  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };
  
  const formattedDateRange = `${formatDate(startDate)} - ${formatDate(endDate)}`;
  
  // Status badge
  const getStatusProps = (status) => {
    switch(status) {
      case 'open':
        return { colorClass: 'bg-green-100 text-green-800', text: 'Open for Registration' };
      case 'almost-full':
        return { colorClass: 'bg-yellow-100 text-yellow-800', text: 'Almost Full' };
      case 'full':
        return { colorClass: 'bg-red-100 text-red-800', text: 'Sold Out' };
      default:
        return { colorClass: 'bg-gray-100 text-gray-800', text: status };
    }
  };
  
  const statusProps = getStatusProps(workshop.status);
  
  const handleEnroll = () => {
    // Integration with new workshop API would go here
    console.log(`Enrolling in workshop ${workshop.id}`);
    // router.push(`/workshops/${workshop.id}/enroll`);
  };
  
  return (
    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
      <div className="relative">
        <img
          src={workshop.banner}
          alt={workshop.title}
          className="w-full h-45 object-cover"
        />
        <div className="absolute top-2 right-2 flex space-x-2">
          {workshop.isVirtual && (
            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center space-x-1">
              <FaVideo className="w-3 h-3" />
              <span>Virtual</span>
            </span>
          )}
          {workshop.recorded && (
            <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full flex items-center space-x-1">
              <FaDownload className="w-3 h-3" />
              <span>Recorded</span>
            </span>
          )}
        </div>
        <span className={`absolute bottom-2 left-2 text-xs px-2 py-1 rounded-full ${
          workshop.level === 'Beginner' ? 'bg-green-100 text-green-800' :
          workshop.level === 'Intermediate' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
        }`}>
          {workshop.level}
        </span>
      </div>
      
      <div className="p-5">
        <h3 className="text-lg font-semibold mb-2 truncate">{workshop.title}</h3>
        
        <p className="text-gray-500 text-sm mb-3 line-clamp-2">{workshop.description}</p>
        
        <div className="flex items-center mb-3 text-sm text-gray-500">
          <FaCalendar className="w-4 h-4 mr-2" />
          <span>{formattedDateRange}</span>
        </div>
        
        <div className="flex items-center mb-3 text-sm text-gray-500">
          {workshop.isVirtual ? <FaGlobe className="w-4 h-4 mr-2" /> : <FaMapMarkerAlt className="w-4 h-4 mr-2" />}
          <span>{workshop.geography}</span>
        </div>
        
        <div className="flex items-center justify-between mb-4 text-sm">
          <div className="flex items-center">
            <FaDollarSign className="w-4 h-4 mr-1 text-gray-500" />
            <span className="font-bold">${workshop.cost}</span>
          </div>
          
          <div className="flex items-center text-gray-500">
            <FaUsers className="w-4 h-4 mr-1" />
            <span>{workshop.enrolled}/{workshop.capacity}</span>
          </div>
          
          <div className="flex items-center">
            <FaStar className="w-4 h-4 mr-1 text-yellow-500" />
            <span>{workshop.rating} ({workshop.reviews})</span>
          </div>
        </div>
        
        <hr className="mb-4" />
        
        <div className="flex items-center justify-between mb-4">
          <div className="flex -space-x-2">
            {workshop.instructors.slice(0, 2).map(instructor => (
              <img
                key={instructor.id}
                src={instructor.avatar}
                alt={instructor.name}
                className="w-8 h-8 rounded-full border-2 border-white"
                title={instructor.name}
              />
            ))}
            {workshop.instructors.length > 2 && (
              <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-100 flex items-center justify-center text-xs">
                +{workshop.instructors.length - 2}
              </div>
            )}
          </div>
          
          <span className={`text-xs px-2 py-1 rounded ${statusProps.colorClass}`}>
            {statusProps.text}
          </span>
        </div>
        
        <button 
          onClick={handleEnroll}
          disabled={workshop.status === 'full'}
          className={`w-full py-2 px-4 rounded text-sm font-medium transition-colors ${
            workshop.status === 'full' 
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-purple-600 text-white hover:bg-purple-700'
          }`}
        >
          {workshop.status === 'full' ? 'Sold Out' : 'Register Now'}
        </button>
      </div>
    </div>
  );
};

// My Workshops List Item Component
const MyWorkshopItem = ({ workshop }) => {
  const getStatusProps = (status) => {
    switch(status) {
      case 'completed':
        return { icon: FaCheck, iconColor: 'text-green-500', text: 'Completed' };
      case 'upcoming':
        return { icon: FaCalendar, iconColor: 'text-blue-500', text: 'Upcoming' };
      case 'in-progress':
        return { icon: FaClock, iconColor: 'text-yellow-500', text: 'In Progress' };
      default:
        return { icon: FaTimes, iconColor: 'text-gray-500', text: status };
    }
  };
  
  const statusProps = getStatusProps(workshop.status);
  const StatusIcon = statusProps.icon;
  
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm flex justify-between items-center">
      <div className="flex items-center space-x-4">
        <StatusIcon className={`w-5 h-5 ${statusProps.iconColor}`} />
        <div>
          <h4 className="font-semibold">{workshop.title}</h4>
          <p className="text-sm text-gray-500">{workshop.date}</p>
        </div>
      </div>
      
      <div className="flex items-center space-x-2">
        {workshop.status === 'completed' && (
          <>
            {workshop.certificate && (
              <button className="px-3 py-1 text-sm border border-green-500 text-green-600 rounded hover:bg-green-50">
                Certificate
              </button>
            )}
            
            {workshop.recordingsAvailable && (
              <button className="px-3 py-1 text-sm border border-purple-500 text-purple-600 rounded hover:bg-purple-50 flex items-center space-x-1">
                <FaPlay className="w-3 h-3" />
                <span>Recordings</span>
              </button>
            )}
          </>
        )}
        
        {workshop.status === 'upcoming' && (
          <span className={`text-xs px-2 py-1 rounded ${
            workshop.paymentComplete ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'
          }`}>
            {workshop.paymentComplete ? 'Confirmed' : 'Payment Due'}
          </span>
        )}
        
        <button className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">
          Details
        </button>
      </div>
    </div>
  );
};

// Main Workshops Component
const Workshops = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [workshops, setWorkshops] = useState([]);
  const [myWorkshops, setMyWorkshops] = useState([]);
  const [activeTab, setActiveTab] = useState('upcoming');
  
  useEffect(() => {
    document.title = 'Workshops | Modern AI Pro';
    
    // Simulate API fetch - would integrate with new workshop API
    setTimeout(() => {
      setWorkshops(workshopsData);
      setMyWorkshops(myWorkshopsData);
      setIsLoading(false);
    }, 1000);
  }, []);
  
  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[1, 2, 3].map(i => (
        <div key={i} className="bg-gray-200 animate-pulse rounded-lg h-96"></div>
      ))}
    </div>
  );
  
  return (
    <div className="p-4">
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">AI Workshops</h1>
          <p className="text-gray-500">Learn from industry experts in interactive sessions</p>
        </div>
        
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('upcoming')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'upcoming'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Upcoming Workshops
            </button>
            <button
              onClick={() => setActiveTab('my-workshops')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'my-workshops'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              My Workshops
            </button>
          </nav>
        </div>
        
        {/* Tab Content */}
        {activeTab === 'upcoming' && (
          <div>
            {/* Filters */}
            <div className="flex justify-between items-center mb-6">
              <div className="flex space-x-4">
                <button className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded text-sm hover:bg-gray-50">
                  <FaFilter className="w-4 h-4" />
                  <span>Filter</span>
                </button>
                
                <button className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded text-sm hover:bg-gray-50">
                  <span>Sort By</span>
                  <FaChevronDown className="w-4 h-4" />
                </button>
              </div>
              
              <button className="flex items-center space-x-2 px-3 py-2 border border-purple-500 text-purple-600 rounded hover:bg-purple-50">
                <FaBookmark className="w-4 h-4" />
                <span>Saved Workshops</span>
              </button>
            </div>
            
            {isLoading ? (
              <LoadingSkeleton />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {workshops.map(workshop => (
                  <WorkshopCard key={workshop.id} workshop={workshop} />
                ))}
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'my-workshops' && (
          <div>
            {/* Stats */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-4">Your Workshop Journey</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-purple-50 p-4 rounded-lg">
                  <p className="text-gray-500 text-sm">Workshops Completed</p>
                  <h3 className="text-2xl font-bold">1</h3>
                </div>
                
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-gray-500 text-sm">Upcoming Workshops</p>
                  <h3 className="text-2xl font-bold">1</h3>
                </div>
                
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-gray-500 text-sm">Certificates Earned</p>
                  <h3 className="text-2xl font-bold">1</h3>
                </div>
              </div>
            </div>
            
            <h2 className="text-lg font-semibold mb-4">Your Workshops</h2>
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="bg-gray-200 animate-pulse rounded-lg h-20"></div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {myWorkshops.map(workshop => (
                  <MyWorkshopItem key={workshop.id} workshop={workshop} />
                ))}
              </div>
            )}
            
            {/* Recommendations */}
            <div className="mt-8 p-5 rounded-lg bg-gray-100 border border-gray-200">
              <h3 className="text-lg font-semibold mb-2">Workshop Recommendations</h3>
              <p className="mb-4 text-gray-600">Based on your learning path and interests, we recommend these upcoming workshops:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3 p-3 bg-white rounded border border-gray-200">
                  <FaVideo className="w-5 h-5 text-purple-500" />
                  <div>
                    <p className="font-semibold">Reinforcement Learning from Human Feedback</p>
                    <p className="text-sm text-gray-500">March 5-7, 2024</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 p-3 bg-white rounded border border-gray-200">
                  <FaMapMarkerAlt className="w-5 h-5 text-blue-500" />
                  <div>
                    <p className="font-semibold">Computer Vision for Medical Applications</p>
                    <p className="text-sm text-gray-500">April 10-12, 2024</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Workshops;