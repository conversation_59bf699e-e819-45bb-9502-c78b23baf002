import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Logo from '../layout/Logo';
import { useAuth } from '../../contexts/AuthContext';
import { AiOutlineEye, AiOutlineEyeInvisible } from 'react-icons/ai';

// Toast notification component for feedback
const Toast = ({ message, type, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, 5000);
    return () => clearTimeout(timer);
  }, [onClose]);

  const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-yellow-500';
  
  return (
    <div className={`fixed top-4 right-4 z-50 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg transition-all duration-300`}>
      <div className="flex items-center space-x-2">
        <span>{message}</span>
        <button onClick={onClose} className="text-white hover:text-gray-200">
          ×
        </button>
      </div>
    </div>
  );
};

// Login view component
const LoginView = ({ onForgotPassword, onRegister, onSubmit, onVerify, loading, loginError }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [remember, setRemember] = useState(false);
  
  const isUnverifiedEmailError = loginError && loginError.toLowerCase().includes('email not verified');
  
  const handleLogin = (e) => {
    e.preventDefault();
    onSubmit(username, password, remember);
  };
  
  return (
    <form onSubmit={handleLogin} className="space-y-6">
      <div>
        <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Username
        </label>
        <input
          id="username"
          type="text"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          required
          className="w-full px-3 py-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-lg"
        />
      </div>
      
      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Password
        </label>
        <div className="relative">
          <input
            id="password"
            type={showPassword ? 'text' : 'password'}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="w-full px-3 py-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-lg pr-12"
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <AiOutlineEyeInvisible className="h-5 w-5 text-gray-400 hover:text-gray-600" />
            ) : (
              <AiOutlineEye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
            )}
          </button>
        </div>
      </div>
      
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <input
            id="remember"
            type="checkbox"
            checked={remember}
            onChange={(e) => setRemember(e.target.checked)}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="remember" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
            Remember me
          </label>
        </div>
        <button
          type="button"
          onClick={onForgotPassword}
          className="text-sm text-purple-400 hover:text-purple-300"
        >
          Forgot password?
        </button>
      </div>
      
      {isUnverifiedEmailError && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Email not verified.
              </p>
              <button
                type="button"
                onClick={onVerify}
                className="mt-1 text-sm text-purple-600 hover:text-purple-500 underline"
              >
                Resend verification email
              </button>
            </div>
          </div>
        </div>
      )}
      
      <button
        type="submit"
        disabled={loading}
        className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-purple-500 hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {loading ? 'Signing in...' : 'Sign in'}
      </button>
      
      <div className="pt-6">
        <p className="text-center text-sm text-gray-600 dark:text-gray-400">
          Don't have an account?{' '}
          <button
            type="button"
            onClick={onRegister}
            className="text-purple-400 hover:text-purple-300"
          >
            Register
          </button>
        </p>
      </div>
    </form>
  );
};

// Register view component
const RegisterView = ({ onLogin, onSubmit, loading }) => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    welcomeCode: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  
  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData({
      ...formData,
      [id]: value,
    });
    
    if (formErrors[id]) {
      setFormErrors({
        ...formErrors,
        [id]: '',
      });
    }
  };
  
  const validateForm = () => {
    const errors = {};
    
    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }
    
    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
    }
    
    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }
    
    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }
    
    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }
    
    if (!formData.welcomeCode.trim()) {
      errors.welcomeCode = 'Welcome code is required';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  const handleRegister = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit({
        username: formData.username,
        email: formData.email,
        password: formData.password,
        first_name: formData.firstName,
        last_name: formData.lastName,
        welcome_code: formData.welcomeCode,
      });
    }
  };
  
  return (
    <form onSubmit={handleRegister} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            First Name
          </label>
          <input
            id="firstName"
            type="text"
            value={formData.firstName}
            onChange={handleChange}
            required
            className="form-input"
          />
          {formErrors.firstName && (
            <p className="mt-1 text-sm text-red-600">{formErrors.firstName}</p>
          )}
        </div>
        
        <div>
          <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Last Name
          </label>
          <input
            id="lastName"
            type="text"
            value={formData.lastName}
            onChange={handleChange}
            required
            className="form-input"
          />
          {formErrors.lastName && (
            <p className="mt-1 text-sm text-red-600">{formErrors.lastName}</p>
          )}
        </div>
      </div>
      
      <div>
        <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Username
        </label>
        <input
          id="username"
          type="text"
          value={formData.username}
          onChange={handleChange}
          required
          className="form-input"
        />
        {formErrors.username && (
          <p className="mt-1 text-sm text-red-600">{formErrors.username}</p>
        )}
      </div>
      
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Email
        </label>
        <input
          id="email"
          type="email"
          value={formData.email}
          onChange={handleChange}
          required
          className="form-input"
        />
        {formErrors.email && (
          <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>
        )}
      </div>
      
      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Password
        </label>
        <div className="relative">
          <input
            id="password"
            type={showPassword ? 'text' : 'password'}
            value={formData.password}
            onChange={handleChange}
            required
            className="form-input pr-10"
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <AiOutlineEyeInvisible className="h-5 w-5 text-gray-400" />
            ) : (
              <AiOutlineEye className="h-5 w-5 text-gray-400" />
            )}
          </button>
        </div>
        {formErrors.password && (
          <p className="mt-1 text-sm text-red-600">{formErrors.password}</p>
        )}
      </div>
      
      <div>
        <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Confirm Password
        </label>
        <input
          id="confirmPassword"
          type={showPassword ? 'text' : 'password'}
          value={formData.confirmPassword}
          onChange={handleChange}
          required
          className="form-input"
        />
        {formErrors.confirmPassword && (
          <p className="mt-1 text-sm text-red-600">{formErrors.confirmPassword}</p>
        )}
      </div>
      
      <div>
        <label htmlFor="welcomeCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Welcome Code
        </label>
        <input
          id="welcomeCode"
          type="text"
          value={formData.welcomeCode}
          onChange={handleChange}
          required
          placeholder="Enter the welcome code you received"
          className="form-input"
        />
        {formErrors.welcomeCode && (
          <p className="mt-1 text-sm text-red-600">{formErrors.welcomeCode}</p>
        )}
        <p className="mt-1 text-xs text-gray-600 dark:text-gray-400">
          A welcome code is required to register. You should have received this code from your instructor or administrator.
        </p>
      </div>
      
      <div className="space-y-6 pt-4">
        <button
          type="submit"
          disabled={loading}
          className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-purple-500 hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
        >
          {loading ? 'Creating Account...' : 'Create Account'}
        </button>
        
        <p className="text-center text-sm text-gray-600 dark:text-gray-400">
          Already have an account?{' '}
          <button
            type="button"
            onClick={onLogin}
            className="text-purple-400 hover:text-purple-300"
          >
            Sign In
          </button>
        </p>
      </div>
    </form>
  );
};

// Forgot Password view component
const ForgotPasswordView = ({ onLogin, onSubmit, loading, success }) => {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  
  const validateEmail = () => {
    if (!email.trim()) {
      setEmailError('Email is required');
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Email is invalid');
      return false;
    }
    setEmailError('');
    return true;
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateEmail()) {
      onSubmit(email);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">
                Password reset link has been sent to your email.
              </p>
            </div>
          </div>
        </div>
      )}
      
      <p className="text-sm text-gray-600 dark:text-gray-400">
        Enter your email and we'll send you a link to reset your password.
      </p>
      
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Email
        </label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => {
            setEmail(e.target.value);
            if (emailError) setEmailError('');
          }}
          required
          className="form-input"
        />
        {emailError && (
          <p className="mt-1 text-sm text-red-600">{emailError}</p>
        )}
      </div>
      
      <div className="space-y-6 pt-4">
        <button
          type="submit"
          disabled={loading}
          className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-purple-500 hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
        >
          {loading ? 'Sending...' : 'Send Reset Link'}
        </button>
        
        <p className="text-center text-sm text-gray-600 dark:text-gray-400">
          Remember your password?{' '}
          <button
            type="button"
            onClick={onLogin}
            className="text-purple-400 hover:text-purple-300"
          >
            Sign In
          </button>
        </p>
      </div>
    </form>
  );
};

// Main Login component
const Login = () => {
  const router = useRouter();
  const [view, setView] = useState('login');
  const [successMessage, setSuccessMessage] = useState('');
  const [loginErrorMessage, setLoginErrorMessage] = useState('');
  const [toast, setToast] = useState(null);
  
  const { login, register, forgotPassword, loading } = useAuth();
  
  useEffect(() => {
    document.title = 'Login | Modern AI Pro';
  }, []);

  const showToast = (message, type) => {
    setToast({ message, type });
  };

  const getRedirectPath = (userRole) => {
    switch (userRole) {
      case 'sales':
        return '/leads';
      case 'admin':
        return '/dashboard';
      case 'student':
        return '/learn';
      default:
        return '/welcome';
    }
  };

  const handleLogin = async (username, password, remember) => {
    setLoginErrorMessage('');
    try {
      const result = await login({ username, password });
      showToast('Login successful! Welcome!', 'success');
      
      // Redirect based on user role
      const redirectPath = getRedirectPath(result.user?.role);
      router.push(redirectPath);
    } catch (error) {
      setLoginErrorMessage(error || 'An error occurred during login');
      showToast(error || 'Login failed', 'error');
    }
  };
  
  const handleRegister = async (userData) => {
    try {
      await register(userData);
      showToast('Registration successful! You can now log in.', 'success');
      setView('login');
    } catch (error) {
      showToast(error || 'Registration failed', 'error');
    }
  };
  
  const handleForgotPassword = async (email) => {
    try {
      await forgotPassword(email);
      setSuccessMessage('Password reset link has been sent to your email.');
      showToast('Password reset link sent!', 'success');
    } catch (error) {
      showToast(error || 'Failed to send reset link', 'error');
    }
  };

  return (
    <div className="min-h-screen flex justify-center items-center relative bg-cover bg-center bg-no-repeat" 
         style={{backgroundImage: "url('/images/robot_background.jpg')"}}>
      {/* Semi-transparent overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-60 z-1"></div>
      
      {/* Toast notifications */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
      
      <div className="relative z-2 w-full max-w-lg mx-auto py-12 px-6">
        <div className="text-center space-y-3 mb-8">
          <Logo isDark className="max-w-60 mx-auto" />
          <h1 className="text-3xl font-bold text-white">
            {view === 'login' && 'Sign in to your account'}
            {view === 'register' && 'Create an account'}
            {view === 'forgotPassword' && 'Forgot Password'}
          </h1>
          {view === 'login' && (
            <p className="text-lg text-gray-200">
              Get ready to accelerate your AI career
            </p>
          )}
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-8 border border-purple-400">
          {view === 'login' && (
            <LoginView
              onForgotPassword={() => setView('forgotPassword')}
              onRegister={() => setView('register')}
              onVerify={() => setView('verifyEmail')}
              onSubmit={handleLogin}
              loading={loading}
              loginError={loginErrorMessage}
            />
          )}
          
          {view === 'register' && (
            <RegisterView
              onLogin={() => setView('login')}
              onSubmit={handleRegister}
              loading={loading}
            />
          )}
          
          {view === 'forgotPassword' && (
            <ForgotPasswordView
              onLogin={() => setView('login')}
              onSubmit={handleForgotPassword}
              loading={loading}
              success={!!successMessage}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default Login;