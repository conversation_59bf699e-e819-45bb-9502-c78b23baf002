import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';

const ProtectedRoute = ({ children }) => {
  const router = useRouter();
  const { isAuthenticated, loading, isHydrated } = useAuth();

  useEffect(() => {
    // If hydrated, not loading, and not authenticated, redirect to login
    if (isHydrated && !loading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, loading, isHydrated, router]);

  // Show loading spinner while hydrating or loading
  if (!isHydrated || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show loading while redirecting non-authenticated users
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Access Restricted
          </h1>
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // Render protected content for authenticated users
  return children;
};

export default ProtectedRoute;