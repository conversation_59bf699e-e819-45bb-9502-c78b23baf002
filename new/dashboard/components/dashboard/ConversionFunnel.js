import { useState, useEffect } from 'react';

/**
 * Conversion Funnel Component
 * Visualizes the proven 6-7% conversion funnel from Aug 30 PRD
 * Shows lead progression through each stage
 */
const ConversionFunnel = () => {
  const [funnelData, setFunnelData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('week'); // today, week, month

  // Funnel stages based on PRD requirements
  const funnelStages = [
    { key: 'new', label: 'New Leads', color: 'bg-blue-500', icon: '📊' },
    { key: 'auto_responded', label: 'Auto-Responded', color: 'bg-indigo-500', icon: '📧' },
    { key: 'contacted', label: 'Contacted', color: 'bg-purple-500', icon: '📞' },
    { key: 'qualified', label: 'Qualified', color: 'bg-pink-500', icon: '✅' },
    { key: 'demo_scheduled', label: 'Demo Scheduled', color: 'bg-red-500', icon: '📅' },
    { key: 'demo_completed', label: 'Demo Completed', color: 'bg-orange-500', icon: '🎥' },
    { key: 'enrolled', label: 'Enrolled', color: 'bg-yellow-500', icon: '🎓' },
    { key: 'workshop_complete', label: 'Workshop Complete', color: 'bg-green-500', icon: '🏆' }
  ];

  // Fetch funnel data
  useEffect(() => {
    const fetchFunnelData = async () => {
      try {
        const response = await fetch(`/api/dashboard/funnel?range=${timeRange}`);
        const data = await response.json();
        setFunnelData(data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching funnel data:', error);
        // Mock data for development
        const mockData = [
          { stage: 'new', count: 400, percentage: 100 },
          { stage: 'auto_responded', count: 380, percentage: 95 },
          { stage: 'contacted', count: 320, percentage: 80 },
          { stage: 'qualified', count: 180, percentage: 45 },
          { stage: 'demo_scheduled', count: 120, percentage: 30 },
          { stage: 'demo_completed', count: 100, percentage: 25 },
          { stage: 'enrolled', count: 28, percentage: 7 },
          { stage: 'workshop_complete', count: 25, percentage: 6.25 }
        ];
        setFunnelData(mockData);
        setLoading(false);
      }
    };

    fetchFunnelData();
    
    // Refresh every 10 minutes
    const interval = setInterval(fetchFunnelData, 10 * 60 * 1000);
    return () => clearInterval(interval);
  }, [timeRange]);

  const getFunnelStageInfo = (stage) => {
    return funnelStages.find(s => s.key === stage) || funnelStages[0];
  };

  const getConversionRate = () => {
    const newLeads = funnelData.find(d => d.stage === 'new')?.count || 0;
    const enrolled = funnelData.find(d => d.stage === 'enrolled')?.count || 0;
    return newLeads > 0 ? ((enrolled / newLeads) * 100).toFixed(2) : 0;
  };

  const getDropoffRate = (currentIndex) => {
    if (currentIndex === 0) return 0;
    const current = funnelData[currentIndex]?.count || 0;
    const previous = funnelData[currentIndex - 1]?.count || 0;
    return previous > 0 ? (((previous - current) / previous) * 100).toFixed(1) : 0;
  };

  if (loading) {
    return (
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-16 bg-gray-200 rounded flex-1"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-bold text-gray-900">Conversion Funnel</h2>
          <p className="text-sm text-gray-600">
            Target: 6-7% conversion | Current: {getConversionRate()}%
            <span className={`ml-2 ${parseFloat(getConversionRate()) >= 6 ? 'text-green-600' : 'text-red-600'}`}>
              {parseFloat(getConversionRate()) >= 6 ? '🎯 On Target' : '⚠️ Below Target'}
            </span>
          </p>
        </div>
        
        {/* Time Range Selector */}
        <div className="flex space-x-2">
          {[
            { key: 'today', label: 'Today' },
            { key: 'week', label: 'Week' },
            { key: 'month', label: 'Month' }
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => setTimeRange(key)}
              className={`px-3 py-1 text-xs font-medium rounded transition-colors ${
                timeRange === key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Funnel Visualization */}
      <div className="space-y-3">
        {funnelData.map((data, index) => {
          const stageInfo = getFunnelStageInfo(data.stage);
          const maxCount = funnelData[0]?.count || 1;
          const width = (data.count / maxCount) * 100;
          const dropoff = getDropoffRate(index);

          return (
            <div key={data.stage} className="relative">
              {/* Drop-off indicator */}
              {index > 0 && dropoff > 0 && (
                <div className="text-center text-xs text-red-600 mb-1">
                  📉 {dropoff}% drop-off
                </div>
              )}
              
              {/* Funnel Stage */}
              <div className="relative">
                <div className="flex items-center space-x-4">
                  {/* Stage Bar */}
                  <div className="flex-1 relative">
                    <div className="h-12 bg-gray-100 rounded-lg overflow-hidden">
                      <div 
                        className={`h-full ${stageInfo.color} transition-all duration-1000 ease-out flex items-center justify-between px-4 text-white font-medium`}
                        style={{ width: `${width}%` }}
                      >
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{stageInfo.icon}</span>
                          <span className="text-sm">{stageInfo.label}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="font-bold">{data.count.toLocaleString()}</span>
                          <span className="text-xs opacity-90">({data.percentage.toFixed(1)}%)</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Conversion Rate from Previous Stage */}
                  {index > 0 && (
                    <div className="w-16 text-center">
                      <div className="text-xs text-gray-500">Convert</div>
                      <div className="text-sm font-semibold text-gray-900">
                        {(100 - dropoff).toFixed(1)}%
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary Stats */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">
              {funnelData[0]?.count?.toLocaleString() || 0}
            </div>
            <div className="text-xs text-gray-600">Total Leads</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">
              {funnelData.find(d => d.stage === 'enrolled')?.count || 0}
            </div>
            <div className="text-xs text-gray-600">Enrollments</div>
          </div>
          
          <div className="text-center">
            <div className={`text-lg font-bold ${parseFloat(getConversionRate()) >= 6 ? 'text-green-600' : 'text-red-600'}`}>
              {getConversionRate()}%
            </div>
            <div className="text-xs text-gray-600">Conversion Rate</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-bold text-purple-600">
              ${((funnelData.find(d => d.stage === 'enrolled')?.count || 0) * 320).toLocaleString()}
            </div>
            <div className="text-xs text-gray-600">Revenue</div>
          </div>
        </div>
      </div>

      {/* Performance Indicators */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Performance vs Target:</span>
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-1 ${parseFloat(getConversionRate()) >= 6 ? 'text-green-600' : 'text-red-600'}`}>
              <span>{parseFloat(getConversionRate()) >= 6 ? '✅' : '❌'}</span>
              <span>Conversion Rate</span>
            </div>
            <div className={`flex items-center space-x-1 ${funnelData[0]?.count >= 50 ? 'text-green-600' : 'text-yellow-600'}`}>
              <span>{funnelData[0]?.count >= 50 ? '✅' : '⚠️'}</span>
              <span>Lead Volume</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConversionFunnel;