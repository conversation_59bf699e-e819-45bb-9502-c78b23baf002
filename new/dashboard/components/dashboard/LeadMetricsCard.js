import { useState, useEffect } from 'react';

/**
 * Lead Metrics Card Component
 * Displays key lead metrics with real-time updates
 * Based on Aug 30 PRD requirements for 400 leads/day scale
 */
const LeadMetricsCard = ({ title, value, change, changeType, icon, color = 'blue' }) => {
  const [animatedValue, setAnimatedValue] = useState(0);

  // Animate number changes
  useEffect(() => {
    const duration = 1000; // 1 second animation
    const steps = 30;
    const increment = value / steps;
    let current = 0;

    const timer = setInterval(() => {
      current += increment;
      if (current >= value) {
        setAnimatedValue(value);
        clearInterval(timer);
      } else {
        setAnimatedValue(Math.floor(current));
      }
    }, duration / steps);

    return () => clearInterval(timer);
  }, [value]);

  const colorClasses = {
    blue: 'bg-blue-50 border-blue-200 text-blue-900',
    green: 'bg-green-50 border-green-200 text-green-900',
    yellow: 'bg-yellow-50 border-yellow-200 text-yellow-900',
    red: 'bg-red-50 border-red-200 text-red-900',
    purple: 'bg-purple-50 border-purple-200 text-purple-900'
  };

  const iconColorClasses = {
    blue: 'text-blue-600 bg-blue-100',
    green: 'text-green-600 bg-green-100',
    yellow: 'text-yellow-600 bg-yellow-100',
    red: 'text-red-600 bg-red-100',
    purple: 'text-purple-600 bg-purple-100'
  };

  const getChangeIcon = () => {
    if (changeType === 'increase') return '📈';
    if (changeType === 'decrease') return '📉';
    return '📊';
  };

  const getChangeColor = () => {
    if (changeType === 'increase') return 'text-green-600';
    if (changeType === 'decrease') return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <div className={`p-6 rounded-lg border-2 ${colorClasses[color]} hover:shadow-lg transition-all duration-300`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-3xl font-bold mb-2">
            {typeof animatedValue === 'number' ? animatedValue.toLocaleString() : animatedValue}
          </p>
          {change !== undefined && (
            <div className={`flex items-center text-sm ${getChangeColor()}`}>
              <span className="mr-1">{getChangeIcon()}</span>
              <span className="font-medium">
                {Math.abs(change)}% vs yesterday
              </span>
            </div>
          )}
        </div>
        
        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${iconColorClasses[color]}`}>
          <span className="text-xl">{icon}</span>
        </div>
      </div>
    </div>
  );
};

/**
 * Lead Metrics Dashboard Component
 * Displays all key metrics for lead management
 */
const LeadMetricsDashboard = () => {
  const [metrics, setMetrics] = useState({
    totalLeads: 0,
    hotLeads: 0,
    qualified: 0,
    enrolled: 0,
    conversionRate: 0,
    avgResponseTime: 0,
    loading: true
  });

  const [timeRange, setTimeRange] = useState('today'); // today, week, month

  // Fetch metrics from API
  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await fetch(`/api/dashboard/metrics?range=${timeRange}`);
        const data = await response.json();
        setMetrics({ ...data, loading: false });
      } catch (error) {
        console.error('Error fetching metrics:', error);
        // Mock data for development
        setMetrics({
          totalLeads: 87,
          hotLeads: 23,
          qualified: 31,
          enrolled: 6,
          conversionRate: 6.9,
          avgResponseTime: 3.2,
          loading: false,
          changes: {
            totalLeads: 15,
            hotLeads: 8,
            qualified: 12,
            enrolled: 2,
            conversionRate: 0.3,
            avgResponseTime: -0.8
          }
        });
      }
    };

    fetchMetrics();
    
    // Refresh every 5 minutes
    const interval = setInterval(fetchMetrics, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [timeRange]);

  if (metrics.loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 h-32 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Lead Performance Metrics</h2>
        <div className="flex space-x-2">
          {[
            { key: 'today', label: 'Today' },
            { key: 'week', label: 'This Week' },
            { key: 'month', label: 'This Month' }
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => setTimeRange(key)}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                timeRange === key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <LeadMetricsCard
          title="Total New Leads"
          value={metrics.totalLeads}
          change={metrics.changes?.totalLeads}
          changeType={metrics.changes?.totalLeads > 0 ? 'increase' : 'decrease'}
          icon="📊"
          color="blue"
        />

        <LeadMetricsCard
          title="Hot Leads (Priority)"
          value={metrics.hotLeads}
          change={metrics.changes?.hotLeads}
          changeType={metrics.changes?.hotLeads > 0 ? 'increase' : 'decrease'}
          icon="🔥"
          color="red"
        />

        <LeadMetricsCard
          title="Qualified Leads"
          value={metrics.qualified}
          change={metrics.changes?.qualified}
          changeType={metrics.changes?.qualified > 0 ? 'increase' : 'decrease'}
          icon="✅"
          color="green"
        />

        <LeadMetricsCard
          title="Enrollments"
          value={metrics.enrolled}
          change={metrics.changes?.enrolled}
          changeType={metrics.changes?.enrolled > 0 ? 'increase' : 'decrease'}
          icon="🎓"
          color="purple"
        />

        <LeadMetricsCard
          title="Conversion Rate"
          value={`${metrics.conversionRate}%`}
          change={metrics.changes?.conversionRate}
          changeType={metrics.changes?.conversionRate > 0 ? 'increase' : 'decrease'}
          icon="📈"
          color="green"
        />

        <LeadMetricsCard
          title="Avg Response Time"
          value={`${metrics.avgResponseTime}min`}
          change={metrics.changes?.avgResponseTime}
          changeType={metrics.changes?.avgResponseTime < 0 ? 'increase' : 'decrease'} // Lower is better
          icon="⚡"
          color="yellow"
        />
      </div>

      {/* Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        {/* Conversion Rate Target */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Conversion Rate Target</h3>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Current: {metrics.conversionRate}%</span>
                <span>Target: 6-7%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className={`h-3 rounded-full transition-all duration-1000 ${
                    metrics.conversionRate >= 6 ? 'bg-green-500' : 'bg-yellow-500'
                  }`}
                  style={{ width: `${Math.min((metrics.conversionRate / 10) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
            <div className="text-2xl">
              {metrics.conversionRate >= 6 ? '🎯' : '⚠️'}
            </div>
          </div>
        </div>

        {/* Response Time Goal */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Response Time Goal</h3>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Current: {metrics.avgResponseTime}min</span>
                <span>Goal: &lt;5min</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className={`h-3 rounded-full transition-all duration-1000 ${
                    metrics.avgResponseTime <= 5 ? 'bg-green-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${Math.min((metrics.avgResponseTime / 10) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
            <div className="text-2xl">
              {metrics.avgResponseTime <= 5 ? '⚡' : '🐌'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeadMetricsDashboard;