import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';

/**
 * Lead Table Component
 * Displays leads with filtering, sorting, and actions
 * Supports role-based access (admin sees all, sales reps see assigned)
 */
const LeadTable = () => {
  const { user } = useAuth();
  const [leads, setLeads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: 'all',
    segment: 'all',
    assignedTo: user?.role === 'sales_rep' ? user.username : 'all',
    search: ''
  });
  const [sortBy, setSortBy] = useState('created_time');
  const [sortOrder, setSortOrder] = useState('desc');
  const [selectedLeads, setSelectedLeads] = useState([]);

  // Lead status options based on PRD
  const statusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: 'New', label: 'New' },
    { value: 'Auto-Responded', label: 'Auto-Responded' },
    { value: 'Contacted', label: 'Contacted' },
    { value: 'Qualified', label: 'Qualified' },
    { value: 'Demo Scheduled', label: 'Demo Scheduled' },
    { value: 'Demo Completed', label: 'Demo Completed' },
    { value: 'Enrolled', label: 'Enrolled' },
    { value: 'Workshop Complete', label: 'Workshop Complete' },
    { value: 'Lost', label: 'Lost' }
  ];

  const segmentOptions = [
    { value: 'all', label: 'All Segments' },
    { value: 'Hot', label: 'Hot (15-20% convert)' },
    { value: 'Warm', label: 'Warm (8-12% convert)' },
    { value: 'Medium', label: 'Medium (3-5% convert)' }
  ];

  // Fetch leads from API
  useEffect(() => {
    const fetchLeads = async () => {
      try {
        const params = new URLSearchParams({
          status: filters.status,
          segment: filters.segment,
          assignedTo: filters.assignedTo,
          search: filters.search,
          sortBy,
          sortOrder
        });

        const response = await fetch(`/api/leads?${params}`);
        const data = await response.json();
        setLeads(data.leads || []);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching leads:', error);
        // Mock data for development
        const mockLeads = [
          {
            id: 1,
            lead_id: 'fb_123456789',
            full_name: 'John Smith',
            email: '<EMAIL>',
            phone_number: '+1234567890',
            lead_segment: 'Hot',
            segment_score: 85,
            status: 'Auto-Responded',
            assigned_to: 'sales_rep_1',
            created_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            response_time_minutes: 3,
            ai_experience: 'Intermediate',
            city: 'San Francisco',
            country_code: 'US',
            campaign_name: 'AI Essentials - US Q3'
          },
          {
            id: 2,
            lead_id: 'fb_123456790',
            full_name: 'Priya Sharma',
            email: '<EMAIL>',
            phone_number: '+************',
            lead_segment: 'Warm',
            segment_score: 55,
            status: 'New',
            assigned_to: 'sales_rep_2',
            created_time: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            response_time_minutes: null,
            ai_experience: 'Beginner',
            city: 'Bangalore',
            country_code: 'IN',
            campaign_name: 'AI for PMs - India'
          }
        ];
        setLeads(mockLeads);
        setLoading(false);
      }
    };

    fetchLeads();
  }, [filters, sortBy, sortOrder]);

  // Get segment badge styling
  const getSegmentBadge = (segment, score) => {
    const badges = {
      Hot: 'bg-red-100 text-red-800 border-red-200',
      Warm: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      Medium: 'bg-blue-100 text-blue-800 border-blue-200'
    };
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${badges[segment] || badges.Medium}`}>
        {segment} ({score})
      </span>
    );
  };

  // Get status badge styling
  const getStatusBadge = (status) => {
    const badges = {
      'New': 'bg-gray-100 text-gray-800',
      'Auto-Responded': 'bg-blue-100 text-blue-800',
      'Contacted': 'bg-indigo-100 text-indigo-800',
      'Qualified': 'bg-purple-100 text-purple-800',
      'Demo Scheduled': 'bg-pink-100 text-pink-800',
      'Demo Completed': 'bg-orange-100 text-orange-800',
      'Enrolled': 'bg-green-100 text-green-800',
      'Workshop Complete': 'bg-emerald-100 text-emerald-800',
      'Lost': 'bg-red-100 text-red-800'
    };

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${badges[status] || badges.New}`}>
        {status}
      </span>
    );
  };

  // Get response time styling
  const getResponseTimeColor = (minutes) => {
    if (!minutes) return 'text-gray-500';
    if (minutes <= 5) return 'text-green-600';
    if (minutes <= 15) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Handle lead assignment
  const handleAssignLead = async (leadId, assignTo) => {
    try {
      await fetch(`/api/leads/${leadId}/assign`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ assignedTo: assignTo })
      });
      
      // Refresh leads
      setLeads(leads.map(lead => 
        lead.id === leadId ? { ...lead, assigned_to: assignTo } : lead
      ));
    } catch (error) {
      console.error('Error assigning lead:', error);
    }
  };

  // Handle status update
  const handleStatusUpdate = async (leadId, newStatus) => {
    try {
      await fetch(`/api/leads/${leadId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      });
      
      // Refresh leads
      setLeads(leads.map(lead => 
        lead.id === leadId ? { ...lead, status: newStatus } : lead
      ));
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  // Format time ago
  const timeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      {/* Header and Filters */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Lead Management</h2>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">
              {leads.length} leads
            </span>
            {selectedLeads.length > 0 && (
              <button className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700">
                Bulk Actions ({selectedLeads.length})
              </button>
            )}
          </div>
        </div>

        {/* Filter Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div>
            <input
              type="text"
              placeholder="Search leads..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className="form-input text-sm"
            />
          </div>

          {/* Status Filter */}
          <div>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="form-input text-sm"
            >
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Segment Filter */}
          <div>
            <select
              value={filters.segment}
              onChange={(e) => setFilters({ ...filters, segment: e.target.value })}
              className="form-input text-sm"
            >
              {segmentOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Assignment Filter (Admin only) */}
          {user?.role !== 'sales_rep' && (
            <div>
              <select
                value={filters.assignedTo}
                onChange={(e) => setFilters({ ...filters, assignedTo: e.target.value })}
                className="form-input text-sm"
              >
                <option value="all">All Assigned</option>
                <option value="unassigned">Unassigned</option>
                <option value="sales_rep_1">Sales Rep 1</option>
                <option value="sales_rep_2">Sales Rep 2</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input
                  type="checkbox"
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedLeads(leads.map(lead => lead.id));
                    } else {
                      setSelectedLeads([]);
                    }
                  }}
                  className="rounded border-gray-300"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Lead Details
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Segment
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Response Time
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {leads.map((lead) => (
              <tr key={lead.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedLeads.includes(lead.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedLeads([...selectedLeads, lead.id]);
                      } else {
                        setSelectedLeads(selectedLeads.filter(id => id !== lead.id));
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-700">
                          {lead.full_name?.charAt(0) || '?'}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {lead.full_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {lead.email}
                      </div>
                      <div className="text-xs text-gray-400">
                        {lead.city}, {lead.country_code} • {timeAgo(lead.created_time)}
                      </div>
                    </div>
                  </div>
                </td>

                <td className="px-6 py-4 whitespace-nowrap">
                  {getSegmentBadge(lead.lead_segment, lead.segment_score)}
                  <div className="text-xs text-gray-500 mt-1">
                    {lead.ai_experience}
                  </div>
                </td>

                <td className="px-6 py-4 whitespace-nowrap">
                  <select
                    value={lead.status}
                    onChange={(e) => handleStatusUpdate(lead.id, e.target.value)}
                    className="text-sm border-none bg-transparent focus:ring-0 p-0"
                    disabled={user?.role === 'sales_rep' && lead.assigned_to !== user.username}
                  >
                    {statusOptions.slice(1).map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </td>

                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <span className={getResponseTimeColor(lead.response_time_minutes)}>
                    {lead.response_time_minutes ? `${lead.response_time_minutes}min` : 'Pending'}
                  </span>
                  {lead.response_time_minutes && lead.response_time_minutes <= 5 && (
                    <span className="ml-1">⚡</span>
                  )}
                </td>

                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {/* Open lead details modal */}}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      View
                    </button>
                    <button
                      onClick={() => {/* Open contact modal */}}
                      className="text-green-600 hover:text-green-900"
                    >
                      Contact
                    </button>
                    {user?.role !== 'sales_rep' && (
                      <select
                        value={lead.assigned_to || ''}
                        onChange={(e) => handleAssignLead(lead.id, e.target.value)}
                        className="text-xs border border-gray-300 rounded px-2 py-1"
                      >
                        <option value="">Unassigned</option>
                        <option value="sales_rep_1">Rep 1</option>
                        <option value="sales_rep_2">Rep 2</option>
                      </select>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
        <div className="text-sm text-gray-700">
          Showing {leads.length} leads
        </div>
        <div className="flex items-center space-x-2">
          <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
            Previous
          </button>
          <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default LeadTable;