import { useState } from 'react';
import { authenticatedFetch } from '../../lib/auth/clientAuth';

export default function AddLeadDialog({ isOpen, onClose, onLeadAdded }) {
  const [formData, setFormData] = useState({
    product_type: 'ModernAI',
    full_name: '',
    email: '',
    phone: '',
    lead_source: 'Referral',
    // Kapi-specific fields
    deal_size: '',
    company: '',
    location: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleProductTypeChange = (e) => {
    const productType = e.target.value;
    setFormData(prev => ({
      ...prev,
      product_type: productType,
      // Reset Kapi-specific fields when switching away from Kapi
      deal_size: productType === 'Kapi' ? prev.deal_size : '',
      company: productType === 'Kapi' ? prev.company : '',
      location: productType === 'Kapi' ? prev.location : ''
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await authenticatedFetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (result.success) {
        // Reset form
        setFormData({
          product_type: 'ModernAI',
          full_name: '',
          email: '',
          phone: '',
          lead_source: 'Referral',
          deal_size: '',
          company: '',
          location: ''
        });
        
        // Notify parent component
        if (onLeadAdded) {
          onLeadAdded();
        }
        
        onClose();
      } else {
        setError(result.error || 'Failed to create lead');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Add New Lead</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Product Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product Type *
            </label>
            <select
              name="product_type"
              value={formData.product_type}
              onChange={handleProductTypeChange}
              className="form-select"
              required
            >
              <option value="ModernAI">ModernAI Pro</option>
              <option value="Kapi">Kapi Enterprise</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Full Name *
              </label>
              <input
                type="text"
                name="full_name"
                value={formData.full_name}
                onChange={handleInputChange}
                className="form-input"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email *
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="form-input"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="form-input"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Source *
              </label>
              <select
                name="lead_source"
                value={formData.lead_source}
                onChange={handleInputChange}
                className="form-select"
                required
              >
                <option value="Referral">Referral</option>
                <option value="Google Ads">Google Ads</option>
                <option value="LinkedIn">LinkedIn</option>
                <option value="Email Campaign">Email Campaign</option>
                <option value="Website">Website</option>
                <option value="Cold Outreach">Cold Outreach</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>

          {/* Kapi-specific fields */}
          {formData.product_type === 'Kapi' && (
            <>
              <div className="border-t pt-4">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Kapi Enterprise Details</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Name
                    </label>
                    <input
                      type="text"
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                      className="form-input"
                      placeholder="e.g. Microsoft, Google"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Deal Size
                      </label>
                      <select
                        name="deal_size"
                        value={formData.deal_size}
                        onChange={handleInputChange}
                        className="form-select"
                      >
                        <option value="">Select range</option>
                        <option value="$10K-$25K">$10K - $25K</option>
                        <option value="$25K-$50K">$25K - $50K</option>
                        <option value="$50K-$100K">$50K - $100K</option>
                        <option value="$100K+">$100K+</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Location
                      </label>
                      <input
                        type="text"
                        name="location"
                        value={formData.location}
                        onChange={handleInputChange}
                        className="form-input"
                        placeholder="e.g. San Francisco, Mumbai"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Error Message */}
          {error && (
            <div className="text-red-600 text-sm bg-red-50 p-2 rounded">
              {error}
            </div>
          )}

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Lead'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}