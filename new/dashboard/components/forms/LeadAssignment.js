import { useState, useEffect } from 'react';
import { authenticatedFetch } from '../../lib/auth/clientAuth';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

export default function LeadAssignment({ leadId, currentAssignee, onAssignmentChange, className = "" }) {
  const [assignableUsers, setAssignableUsers] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [localAssignee, setLocalAssignee] = useState(currentAssignee);

  // Load assignable users on component mount
  useEffect(() => {
    async function loadAssignableUsers() {
      try {
        const response = await authenticatedFetch('/api/users/assignable');
        const data = await response.json();
        
        if (data.success) {
          setAssignableUsers(data.users);
        } else {
          setError('Failed to load users');
        }
      } catch (err) {
        console.error('Error loading assignable users:', err);
        setError('Failed to load users');
      }
    }

    loadAssignableUsers();
  }, []);

  // Update local state when prop changes
  useEffect(() => {
    setLocalAssignee(currentAssignee);
  }, [currentAssignee]);

  const handleAssignment = async (userId) => {
    setLoading(true);
    setError('');

    try {
      const response = await authenticatedFetch('/api/leads/assign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          leadId: leadId,
          assignedTo: userId // null to unassign, or user ID to assign
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Update local state immediately
        setLocalAssignee(data.lead.assignedTo);
        
        // Call parent callback to update the UI
        if (onAssignmentChange) {
          onAssignmentChange(leadId, {
            assignedTo: data.lead.assignedTo,
            assigneeName: data.lead.assigneeName,
            assigneeRole: data.lead.assigneeRole
          });
        }
        setIsOpen(false);
      } else {
        setError(data.error || 'Assignment failed');
      }
    } catch (err) {
      console.error('Error assigning lead:', err);
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const currentUser = assignableUsers.find(user => user.id === localAssignee);

  return (
    <div className={`relative ${className}`} data-assignment-dropdown>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={loading}
        className={`inline-flex items-center justify-between w-full px-3 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
          loading ? 'opacity-50 cursor-not-allowed' : ''
        }`}
      >
        <span className="flex items-center">
          {currentUser ? (
            <div className="flex items-center">
              <span className="font-medium text-gray-900">{currentUser.name}</span>
              <span className="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full capitalize">
                {currentUser.role}
              </span>
            </div>
          ) : (
            <span className="text-gray-500 italic">Unassigned</span>
          )}
        </span>
        <ChevronDownIcon className="w-4 h-4 ml-2 text-gray-400" />
      </button>

      {isOpen && (
        <div className="absolute z-50 w-64 mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
          <div className="py-1 max-h-60 overflow-y-auto">
            {/* Unassign option */}
            <button
              onClick={() => handleAssignment(null)}
              disabled={loading}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100"
            >
              <span className="italic text-gray-500">Unassign</span>
            </button>
            
            <div className="border-t border-gray-100"></div>
            
            {/* User options */}
            {assignableUsers.map((user) => (
              <button
                key={user.id}
                onClick={() => handleAssignment(user.id)}
                disabled={loading}
                className={`w-full px-4 py-2 text-left text-sm hover:bg-gray-100 focus:outline-none focus:bg-gray-100 ${
                  localAssignee === user.id ? 'bg-blue-50 text-blue-900' : 'text-gray-700 hover:text-gray-900'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{user.name}</div>
                    <div className="text-xs text-gray-500">{user.email}</div>
                  </div>
                  <span className="px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded-full capitalize">
                    {user.role}
                  </span>
                </div>
              </button>
            ))}
          </div>
          
          {error && (
            <div className="px-4 py-2 text-xs text-red-600 bg-red-50 border-t border-gray-100">
              {error}
            </div>
          )}
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        ></div>
      )}
    </div>
  );
}