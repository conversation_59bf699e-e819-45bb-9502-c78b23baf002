import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aYoutube } from "react-icons/fa";
import Logo from "./Logo";

const SocialButton = ({ children, label, href }) => {
  return (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="w-8 h-8 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded-full flex items-center justify-center transition-colors duration-300 cursor-pointer"
      aria-label={label}
      title={label}
    >
      {children}
    </a>
  );
};

const Footer = () => {
  return (
    <footer className="bg-gray-50 dark:bg-gray-900 text-gray-700 dark:text-gray-200 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Logo className="h-8" />
          </div>
          
          {/* Copyright */}
          <div className="text-center">
            <p className="text-sm">
              © {new Date().getFullYear()} Modern AI Pro. All rights reserved
            </p>
          </div>
          
          {/* Social Links */}
          <div className="flex space-x-4">
            <SocialButton
              label="Twitter"
              href="https://twitter.com/modernaipro"
            >
              <FaTwitter className="w-4 h-4" />
            </SocialButton>
            <SocialButton
              label="YouTube"
              href="https://www.youtube.com/@modernaipro"
            >
              <FaYoutube className="w-4 h-4" />
            </SocialButton>
            <SocialButton
              label="LinkedIn"
              href="https://www.linkedin.com/company/modernaipro"
            >
              <FaLinkedin className="w-4 h-4" />
            </SocialButton>
          </div>
        </div>
        
        {/* Additional Links */}
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-col md:flex-row items-center justify-center space-y-2 md:space-y-0 md:space-x-8">
            <a 
              href="/privacy" 
              className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
            >
              Privacy Policy
            </a>
            <a 
              href="/terms" 
              className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
            >
              Terms of Service
            </a>
            <a 
              href="/contact" 
              className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
            >
              Contact Us
            </a>
            <a 
              href="/workshops" 
              className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
            >
              Workshops
            </a>
            <a 
              href="/community" 
              className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
            >
              Community
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;