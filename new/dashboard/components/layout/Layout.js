import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import { useState, useEffect } from 'react';

export default function Layout({ children, title = "Modern AI Pro" }) {
  const router = useRouter();
  const { user, logout } = useAuth();
  const [mounted, setMounted] = useState(false);
  
  const isLearningPortal = router.pathname.startsWith('/learn');
  const isDashboard = router.pathname.startsWith('/dashboard') || router.pathname.startsWith('/leads') || router.pathname.startsWith('/payments') || router.pathname.startsWith('/workshops') || router.pathname.startsWith('/customers');
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  const handleLogout = () => {
    logout();
    router.push('/');
  };

  const getAppTitle = () => {
    if (isLearningPortal) return '🎓 Learning Portal';
    if (router.pathname.startsWith('/leads')) return '👥 Lead Management';
    if (router.pathname.startsWith('/payments')) return '💳 Payment Management';
    if (router.pathname.startsWith('/workshops')) return '🎓 Workshop Management';
    if (router.pathname.startsWith('/customers')) return '👥 Customer Management';
    if (isDashboard) return '📊 Dashboard';
    return '🚀 Modern AI Pro';
  };

  const getNavigationItems = () => {
    if (!mounted) return []; // Don't render navigation until client-side mounted
    
    const userRole = user?.role || (typeof window !== 'undefined' ? localStorage.getItem('role') : null);
    
    if (isLearningPortal) {
      return [
        { label: '🏠 My Learning', href: '/learn', active: router.pathname === '/learn' },
        { label: '📚 All Workshops', href: '/workshops', active: router.pathname === '/workshops' },
        { label: '💬 Community', href: '/community', active: router.pathname === '/community' },
        { label: '🎯 Welcome', href: '/welcome', active: router.pathname === '/welcome' },
      ];
    }
    
    if (isDashboard && ['admin', 'executive', 'sales_rep', 'bd'].includes(userRole)) {
      const baseItems = [
        { label: '📊 Dashboard', href: '/dashboard', active: router.pathname === '/dashboard' },
        { label: '👥 Leads', href: '/leads', active: router.pathname.startsWith('/leads') },
        { label: '💳 Payments', href: '/payments', active: router.pathname.startsWith('/payments') },
        { label: '🎓 Workshops', href: '/workshops', active: router.pathname.startsWith('/workshops') },
        { label: '👥 Customers', href: '/customers', active: router.pathname.startsWith('/customers') },
      ];
      
      // Add future dashboard link for admins
      if (['admin', 'executive'].includes(userRole)) {
        baseItems.push({ label: '🔮 Future Dashboard', href: '/dashboard-future', active: router.pathname === '/dashboard-future' });
      }
      
      return baseItems;
    }

    // Add instructor-specific navigation for teachers and admins
    if (['admin', 'teacher', 'executive'].includes(userRole)) {
      const instructorItems = [
        { label: '🎥 Live Class', href: '/teach/live-class', active: router.pathname === '/teach/live-class' },
        { label: '📊 Feedback Tools', href: '/teach/feedback-tools', active: router.pathname === '/teach/feedback-tools' },
      ];
      
      if (isLearningPortal) {
        return [
          { label: '🏠 My Learning', href: '/learn', active: router.pathname === '/learn' },
          { label: '📚 All Workshops', href: '/workshops', active: router.pathname === '/workshops' },
          { label: '💬 Community', href: '/community', active: router.pathname === '/community' },
          { label: '🎯 Welcome', href: '/welcome', active: router.pathname === '/welcome' },
          ...instructorItems,
        ];
      }
      
      return instructorItems;
    }
    
    return [];
  };

  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content="Modern AI Pro - Learning and Analytics Platform" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-4">
                <h1 className="text-xl font-semibold text-gray-900">
                  {getAppTitle()}
                </h1>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-4">
                  {/* Section Switcher for Admins */}
                  {mounted && user?.role && ['admin', 'executive'].includes(user.role) && (
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => router.push('/dashboard')}
                        className={`px-3 py-1 text-sm rounded-md transition-colors ${
                          isDashboard ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'
                        }`}
                      >
                        Dashboard
                      </button>
                      <button
                        onClick={() => router.push('/learn')}
                        className={`px-3 py-1 text-sm rounded-md transition-colors ${
                          isLearningPortal ? 'bg-purple-100 text-purple-700' : 'text-gray-600 hover:bg-gray-100'
                        }`}
                      >
                        Learning
                      </button>
                      <button
                        onClick={() => router.push('/teach/feedback-tools')}
                        className={`px-3 py-1 text-sm rounded-md transition-colors ${
                          router.pathname.startsWith('/teach') ? 'bg-green-100 text-green-700' : 'text-gray-600 hover:bg-gray-100'
                        }`}
                      >
                        Teaching
                      </button>
                    </div>
                  )}
                  
                  <button
                    onClick={() => router.push('/landing')}
                    className="text-gray-600 hover:text-gray-900 text-sm"
                  >
                    🌐 Landing
                  </button>
                  
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-700">
                      👤 {mounted ? (user?.firstName || 'User') : 'User'}
                    </span>
                    <button
                      onClick={() => router.push('/settings')}
                      className="text-gray-400 hover:text-gray-600 text-sm"
                    >
                      ⚙️ Settings
                    </button>
                    <button 
                      onClick={handleLogout}
                      className="text-gray-400 hover:text-gray-600 text-sm"
                    >
                      Logout
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Navigation Tabs */}
        {getNavigationItems().length > 0 && (
          <nav className="bg-white border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex space-x-8">
                {getNavigationItems().map((item) => (
                  <button
                    key={item.href}
                    onClick={() => router.push(item.href)}
                    className={`border-b-2 py-4 px-1 text-sm font-medium transition-colors ${
                      item.active
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            </div>
          </nav>
        )}

        {/* Main Content */}
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          {children}
        </main>
      </div>
    </>
  );
}