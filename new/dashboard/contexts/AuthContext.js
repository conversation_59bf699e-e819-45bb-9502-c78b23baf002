import { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import clientAuth, { setToken, setUser as setStoredUser, removeToken } from '../lib/auth/clientAuth';

const AuthContext = createContext();

// Local Next.js API endpoints
const API_ENDPOINTS = {
  LOGIN: '/api/auth/login',
  REGISTER: '/api/auth/register',
  FORGOT_PASSWORD: '/api/auth/forgot-password',
  RESET_PASSWORD: '/api/auth/reset-password',
  VERIFY_EMAIL: '/api/auth/verify-email',
  RESEND_VERIFICATION: '/api/auth/resend-verification',
};

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isHydrated, setIsHydrated] = useState(false);

  // Initialize auth state on client-side hydration
  useEffect(() => {
    if (typeof window !== 'undefined') {
      console.log('🔍 Checking authentication on load...');
      const isAuth = clientAuth.isAuthenticated();
      const userData = clientAuth.getUser();
      
      console.log('📊 Auth check result:', { isAuth, userData });
      localStorage.setItem('debug_auth_check', JSON.stringify({ 
        timestamp: new Date().toISOString(),
        isAuth, 
        hasUser: !!userData,
        token: !!clientAuth.getToken()
      }));
      
      setIsAuthenticated(isAuth);
      setUser(userData);
      setIsHydrated(true);
    }
  }, []);

  const login = async (credentials) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(API_ENDPOINTS.LOGIN, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: credentials.username,
          password: credentials.password
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || 'Login failed');
      }

      const { access_token, token_type, user: userData } = data;

      if (access_token && token_type) {
        console.log('Login successful, storing token:', access_token.substring(0, 20) + '...');
        
        // Store JWT token and user data using clientAuth
        setToken(access_token);
        const userInfo = {
          id: userData.id,
          username: userData.username,
          email: userData.email,
          firstName: userData.first_name,
          lastName: userData.last_name,
          role: userData.role,
          subscriptionTier: userData.subscription_tier,
        };
        setStoredUser(userInfo);

        // Verify token was stored
        const storedToken = clientAuth.getToken();
        console.log('Token stored successfully?', !!storedToken);
        console.log('Is authenticated after storage?', clientAuth.isAuthenticated());

        // Update state
        setUser(userInfo);
        setIsAuthenticated(true);

        // Force a small delay to ensure localStorage is written
        await new Promise(resolve => setTimeout(resolve, 100));

        return { success: true, user: userData };
      } else {
        throw new Error('Login failed due to server response');
      }
    } catch (error) {
      let errorMessage = error.message || 'Login failed: Invalid username or password';
      
      // Special handling for unverified email
      if (error.message?.includes('Email not verified')) {
        errorMessage = 'Email not verified. Please check your email for verification link.';
      }
      
      setError(errorMessage);
      console.error('Login request failed:', error);
      throw errorMessage;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setIsAuthenticated(false);
    setUser(null);
    
    // Clear JWT token and user data using clientAuth
    removeToken();
  };
  
  const register = async (userData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(API_ENDPOINTS.REGISTER, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.detail || 'Registration failed');
      }
      
      return data;
    } catch (error) {
      let errorMessage = error.message || 'Registration failed. Please try again.';
      
      // Special handling for welcome code errors
      if (error.response?.status === 400 && 
          (error.response?.headers?.['x-error'] === 'invalid_welcome_code' || 
           error.response?.data?.detail === 'Invalid welcome code')) {
        errorMessage = 'Invalid welcome code. Please check and try again.';
      }
      
      setError(errorMessage);
      throw errorMessage;
    } finally {
      setLoading(false);
    }
  };
  
  const forgotPassword = async (email) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.post(
        API_ENDPOINTS.FORGOT_PASSWORD,
        { email },
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
      
      return response.data;
    } catch (error) {
      let errorMessage = 'Failed to send password reset email. Please try again.';
      
      if (error.response?.data?.detail) {
        if (typeof error.response.data.detail === 'string') {
          errorMessage = error.response.data.detail;
        } else if (typeof error.response.data.detail === 'object') {
          errorMessage = JSON.stringify(error.response.data.detail);
        }
      }
      
      setError(errorMessage);
      throw errorMessage;
    } finally {
      setLoading(false);
    }
  };
  
  const resetPassword = async (token, newPassword) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.post(
        API_ENDPOINTS.RESET_PASSWORD,
        { token, new_password: newPassword },
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
      
      return response.data;
    } finally {
      setLoading(false);
    }
  };

  const verifyEmail = async (token) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.post(
        API_ENDPOINTS.VERIFY_EMAIL,
        { token },
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
      
      return response.data;
    } catch (error) {
      let errorMessage = 'Failed to verify email. Token may be invalid or expired.';
      
      if (error.response?.data?.detail) {
        if (typeof error.response.data.detail === 'string') {
          errorMessage = error.response.data.detail;
        } else if (typeof error.response.data.detail === 'object') {
          errorMessage = JSON.stringify(error.response.data.detail);
        }
      }
      
      setError(errorMessage);
      throw errorMessage;
    } finally {
      setLoading(false);
    }
  };
  
  const resendVerification = async (email) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.post(
        API_ENDPOINTS.RESEND_VERIFICATION,
        { email },
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
      
      return response.data;
    } catch (error) {
      let errorMessage = 'Failed to resend verification email.';
      
      if (error.response?.data?.detail) {
        if (typeof error.response.data.detail === 'string') {
          errorMessage = error.response.data.detail;
        } else if (typeof error.response.data.detail === 'object') {
          errorMessage = JSON.stringify(error.response.data.detail);
        }
      }
      
      setError(errorMessage);
      throw errorMessage;
    } finally {
      setLoading(false);
    }
  };

  // Setup axios interceptor for authentication
  useEffect(() => {
    const token = typeof window !== 'undefined' ? clientAuth.getToken() : null;
    
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete axios.defaults.headers.common['Authorization'];
    }
  }, [isAuthenticated]);

  const value = {
    isAuthenticated,
    user,
    loading,
    error,
    isHydrated,
    login,
    logout,
    register,
    forgotPassword,
    resetPassword,
    verifyEmail,
    resendVerification,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};