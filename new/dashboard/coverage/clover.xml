<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1755155470791" clover="3.2.0">
  <project timestamp="1755155470791" name="All files">
    <metrics statements="2962" coveredstatements="49" conditionals="2117" coveredconditionals="0" methods="750" coveredmethods="0" elements="5829" coveredelements="49" complexity="0" loc="2962" ncloc="2962" packages="21" files="87" classes="87"/>
    <package name="components">
      <metrics statements="831" coveredstatements="15" conditionals="610" coveredconditionals="0" methods="272" coveredmethods="0"/>
      <file name="BarChart.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/BarChart.js">
        <metrics statements="23" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
      </file>
      <file name="CampaignTable.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/CampaignTable.js">
        <metrics statements="55" coveredstatements="0" conditionals="43" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="46" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="stmt"/>
      </file>
      <file name="CreateSurvey.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/CreateSurvey.js">
        <metrics statements="38" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="98" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
      </file>
      <file name="Discord.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/Discord.js">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
      </file>
      <file name="DiscordWidget.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/DiscordWidget.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
      </file>
      <file name="EventsSchedule.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/EventsSchedule.js">
        <metrics statements="36" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
      </file>
      <file name="FacebookAccountsTable.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/FacebookAccountsTable.js">
        <metrics statements="49" coveredstatements="0" conditionals="76" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
      </file>
      <file name="FacebookTimelineSelector.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/FacebookTimelineSelector.js">
        <metrics statements="5" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
      </file>
      <file name="GeographyTable.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/GeographyTable.js">
        <metrics statements="10" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
      </file>
      <file name="KapiLeadsTable.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/KapiLeadsTable.js">
        <metrics statements="25" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
      </file>
      <file name="LandingPage.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/LandingPage.js">
        <metrics statements="35" coveredstatements="12" conditionals="6" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="50" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="61" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="478" count="0" type="stmt"/>
        <line num="541" count="1" type="stmt"/>
      </file>
      <file name="LeadAdsTable.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/LeadAdsTable.js">
        <metrics statements="52" coveredstatements="0" conditionals="80" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
      </file>
      <file name="LeadsTable.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/LeadsTable.js">
        <metrics statements="92" coveredstatements="0" conditionals="61" coveredconditionals="0" methods="39" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
      </file>
      <file name="LiveClass.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/LiveClass.js">
        <metrics statements="14" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
      </file>
      <file name="MarketingSourcesTable.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/MarketingSourcesTable.js">
        <metrics statements="17" coveredstatements="0" conditionals="15" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
      </file>
      <file name="MetricsCard.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/MetricsCard.js">
        <metrics statements="5" coveredstatements="1" conditionals="6" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
      </file>
      <file name="NotFoundPage.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/NotFoundPage.js">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
      </file>
      <file name="QuickNav.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/QuickNav.js">
        <metrics statements="24" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
      </file>
      <file name="SVGFunnel.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/SVGFunnel.js">
        <metrics statements="15" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
      </file>
      <file name="SalesTeamTable.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/SalesTeamTable.js">
        <metrics statements="15" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
      </file>
      <file name="SearchableTable.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/SearchableTable.js">
        <metrics statements="71" coveredstatements="2" conditionals="116" coveredconditionals="0" methods="29" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="159" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="217" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="281" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="9"/>
      </file>
      <file name="SectionHeader.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/SectionHeader.js">
        <metrics statements="2" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
      </file>
      <file name="SelectSurvey.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/SelectSurvey.js">
        <metrics statements="14" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
      </file>
      <file name="SlideShow.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/SlideShow.js">
        <metrics statements="43" coveredstatements="0" conditionals="41" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="195" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
      </file>
      <file name="TimelineSelector.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/TimelineSelector.js">
        <metrics statements="5" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
      </file>
      <file name="UrgentActions.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/UrgentActions.js">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
      </file>
      <file name="WelcomeDashboard.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/WelcomeDashboard.js">
        <metrics statements="63" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
      </file>
      <file name="WordCloudChart.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/WordCloudChart.js">
        <metrics statements="48" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
      </file>
      <file name="Workshops.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/Workshops.js">
        <metrics statements="49" coveredstatements="0" conditionals="36" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="463" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.auth">
      <metrics statements="136" coveredstatements="0" conditionals="94" coveredconditionals="0" methods="34" coveredmethods="0"/>
      <file name="Login.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/auth/Login.js">
        <metrics statements="123" coveredstatements="0" conditionals="83" coveredconditionals="0" methods="32" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="210" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="411" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="413" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="423" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="424" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="461" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="496" count="0" type="stmt"/>
        <line num="497" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="501" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
        <line num="505" count="0" type="stmt"/>
        <line num="506" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="514" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="516" count="0" type="stmt"/>
        <line num="518" count="0" type="stmt"/>
        <line num="520" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="526" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="533" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="536" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="537" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="541" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="543" count="0" type="stmt"/>
        <line num="544" count="0" type="stmt"/>
        <line num="545" count="0" type="stmt"/>
        <line num="547" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="551" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="553" count="0" type="stmt"/>
        <line num="554" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="557" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="572" count="0" type="stmt"/>
        <line num="594" count="0" type="stmt"/>
        <line num="595" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="625" count="0" type="stmt"/>
      </file>
      <file name="ProtectedRoute.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/auth/ProtectedRoute.js">
        <metrics statements="13" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="12" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.dashboard">
      <metrics statements="163" coveredstatements="0" conditionals="126" coveredconditionals="0" methods="57" coveredmethods="0"/>
      <file name="ConversionFunnel.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/dashboard/ConversionFunnel.js">
        <metrics statements="43" coveredstatements="0" conditionals="45" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
      </file>
      <file name="LeadMetricsCard.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/dashboard/LeadMetricsCard.js">
        <metrics statements="44" coveredstatements="0" conditionals="38" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
      </file>
      <file name="LeadsTable.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/dashboard/LeadsTable.js">
        <metrics statements="76" coveredstatements="0" conditionals="43" coveredconditionals="0" methods="28" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="210" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="340" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="341" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.forms">
      <metrics statements="100" coveredstatements="0" conditionals="55" coveredconditionals="0" methods="22" coveredmethods="0"/>
      <file name="AddLeadDialog.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/forms/AddLeadDialog.js">
        <metrics statements="27" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="ChangePassword.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/forms/ChangePassword.js">
        <metrics statements="32" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
      </file>
      <file name="LeadAssignment.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/forms/LeadAssignment.js">
        <metrics statements="41" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.layout">
      <metrics statements="55" coveredstatements="2" conditionals="56" coveredconditionals="0" methods="15" coveredmethods="0"/>
      <file name="Footer.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/layout/Footer.js">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
      </file>
      <file name="Layout.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/layout/Layout.js">
        <metrics statements="46" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="48" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
      </file>
      <file name="Logo.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/components/layout/Logo.js">
        <metrics statements="3" coveredstatements="2" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="1" type="cond" truecount="0" falsecount="2"/>
        <line num="2" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="1" type="stmt"/>
      </file>
    </package>
    <package name="lib">
      <metrics statements="206" coveredstatements="11" conditionals="97" coveredconditionals="0" methods="42" coveredmethods="0"/>
      <file name="auth.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/auth.js">
        <metrics statements="28" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
      </file>
      <file name="currency.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/currency.js">
        <metrics statements="43" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
      </file>
      <file name="db.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/db.js">
        <metrics statements="31" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
      </file>
      <file name="dbConfig.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/dbConfig.js">
        <metrics statements="6" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
      </file>
      <file name="facebook.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/facebook.js">
        <metrics statements="35" coveredstatements="0" conditionals="15" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
      </file>
      <file name="roleAuth.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/roleAuth.js">
        <metrics statements="27" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
      </file>
      <file name="utils.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/utils.js">
        <metrics statements="36" coveredstatements="11" conditionals="18" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
    </package>
    <package name="lib.communications">
      <metrics statements="109" coveredstatements="0" conditionals="37" coveredconditionals="0" methods="41" coveredmethods="0"/>
      <file name="email.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/communications/email.js">
        <metrics statements="29" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
      </file>
      <file name="sms.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/communications/sms.js">
        <metrics statements="45" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="124" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
      </file>
      <file name="whatsapp.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/communications/whatsapp.js">
        <metrics statements="35" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="171" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
      </file>
    </package>
    <package name="lib.config">
      <metrics statements="60" coveredstatements="0" conditionals="69" coveredconditionals="0" methods="15" coveredmethods="0"/>
      <file name="leadsTableConfig.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/config/leadsTableConfig.js">
        <metrics statements="33" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="paymentsTableConfig.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/lib/config/paymentsTableConfig.js">
        <metrics statements="27" coveredstatements="0" conditionals="35" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages">
      <metrics statements="68" coveredstatements="8" conditionals="48" coveredconditionals="0" methods="13" coveredmethods="0"/>
      <file name="community.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/community.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
      </file>
      <file name="index.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/index.js">
        <metrics statements="18" coveredstatements="4" conditionals="15" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
      </file>
      <file name="landing.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/landing.js">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="login.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/login.js">
        <metrics statements="20" coveredstatements="4" conditionals="13" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
      </file>
      <file name="payments.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/payments.js">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
      </file>
      <file name="settings.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/settings.js">
        <metrics statements="11" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
      </file>
      <file name="welcome.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/welcome.js">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.api">
      <metrics statements="269" coveredstatements="4" conditionals="151" coveredconditionals="0" methods="32" coveredmethods="0"/>
      <file name="customers.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/customers.js">
        <metrics statements="57" coveredstatements="4" conditionals="17" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
      </file>
      <file name="leads.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/leads.js">
        <metrics statements="106" coveredstatements="0" conditionals="84" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="58" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="139" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="226" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
      </file>
      <file name="payments.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/payments.js">
        <metrics statements="46" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
      </file>
      <file name="workshops.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/workshops.js">
        <metrics statements="60" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.api.auth">
      <metrics statements="182" coveredstatements="0" conditionals="105" coveredconditionals="0" methods="7" coveredmethods="0"/>
      <file name="change-password.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/auth/change-password.js">
        <metrics statements="35" coveredstatements="0" conditionals="23" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="15" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="42" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
      </file>
      <file name="forgot-password.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/auth/forgot-password.js">
        <metrics statements="24" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
      </file>
      <file name="login.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/auth/login.js">
        <metrics statements="24" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="13" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
      </file>
      <file name="register.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/auth/register.js">
        <metrics statements="29" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="21" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
      </file>
      <file name="resend-verification.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/auth/resend-verification.js">
        <metrics statements="19" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
      </file>
      <file name="reset-password.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/auth/reset-password.js">
        <metrics statements="29" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
      </file>
      <file name="verify-email.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/auth/verify-email.js">
        <metrics statements="22" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.api.facebook">
      <metrics statements="159" coveredstatements="9" conditionals="112" coveredconditionals="0" methods="31" coveredmethods="0"/>
      <file name="accounts.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/facebook/accounts.js">
        <metrics statements="47" coveredstatements="3" conditionals="57" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
      </file>
      <file name="campaigns.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/facebook/campaigns.js">
        <metrics statements="74" coveredstatements="3" conditionals="28" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
      </file>
      <file name="lead-ads.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/facebook/lead-ads.js">
        <metrics statements="38" coveredstatements="3" conditionals="27" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.api.leads">
      <metrics statements="124" coveredstatements="0" conditionals="86" coveredconditionals="0" methods="9" coveredmethods="0"/>
      <file name="[id].js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/leads/[id].js">
        <metrics statements="100" coveredstatements="0" conditionals="64" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="235" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
      </file>
      <file name="assign.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/leads/assign.js">
        <metrics statements="24" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.api.users">
      <metrics statements="11" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="assignable.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/api/users/assignable.js">
        <metrics statements="11" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.customers">
      <metrics statements="83" coveredstatements="0" conditionals="99" coveredconditionals="0" methods="24" coveredmethods="0"/>
      <file name="[id].js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/customers/[id].js">
        <metrics statements="40" coveredstatements="0" conditionals="59" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
      </file>
      <file name="index.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/customers/index.js">
        <metrics statements="43" coveredstatements="0" conditionals="40" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.dashboard">
      <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="index.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/dashboard/index.js">
        <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.leads">
      <metrics statements="122" coveredstatements="0" conditionals="119" coveredconditionals="0" methods="36" coveredmethods="0"/>
      <file name="[id].js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/leads/[id].js">
        <metrics statements="105" coveredstatements="0" conditionals="119" coveredconditionals="0" methods="30" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="71" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="81" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="273" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="291" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="586" count="0" type="stmt"/>
      </file>
      <file name="index.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/leads/index.js">
        <metrics statements="17" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.learn">
      <metrics statements="45" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="20" coveredmethods="0"/>
      <file name="index.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/learn/index.js">
        <metrics statements="45" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="20" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.learn.workshop">
      <metrics statements="36" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="13" coveredmethods="0"/>
      <file name="[id].js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/learn/workshop/[id].js">
        <metrics statements="36" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="318" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.teach">
      <metrics statements="19" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="feedback-tools.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/teach/feedback-tools.js">
        <metrics statements="15" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
      </file>
      <file name="live-class.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/teach/live-class.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
      </file>
    </package>
    <package name="pages.workshops">
      <metrics statements="172" coveredstatements="0" conditionals="202" coveredconditionals="0" methods="58" coveredmethods="0"/>
      <file name="[id].js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/workshops/[id].js">
        <metrics statements="43" coveredstatements="0" conditionals="44" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
      </file>
      <file name="index.js" path="/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/pages/workshops/index.js">
        <metrics statements="129" coveredstatements="0" conditionals="158" coveredconditionals="0" methods="46" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="457" count="0" type="stmt"/>
        <line num="468" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="485" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="544" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="558" count="0" type="stmt"/>
        <line num="587" count="0" type="stmt"/>
        <line num="588" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="602" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="618" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="640" count="0" type="stmt"/>
        <line num="652" count="0" type="stmt"/>
        <line num="670" count="0" type="stmt"/>
        <line num="685" count="0" type="stmt"/>
        <line num="698" count="0" type="stmt"/>
        <line num="712" count="0" type="stmt"/>
        <line num="724" count="0" type="stmt"/>
        <line num="735" count="0" type="stmt"/>
        <line num="755" count="0" type="stmt"/>
        <line num="768" count="0" type="stmt"/>
        <line num="781" count="0" type="stmt"/>
        <line num="793" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
