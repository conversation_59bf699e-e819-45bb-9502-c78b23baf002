-- ============================================================================
-- Email Schema Enhancement Migration
-- Purpose: Apply enhanced email types for parent-child scenarios
-- Date: August 11, 2025
-- ============================================================================

-- Create a temporary table with the new email types
CREATE TABLE customer_emails_temp (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    
    email TEXT NOT NULL,
    email_type TEXT DEFAULT 'personal' CHECK (email_type IN (
        'personal',
        'work',
        'payment',      -- Used for payments (father pays for son)
        'certificate',  -- Email for receiving certificates
        'emergency',    -- Emergency contact email
        'spouse',       -- Family member email
        'parent',       -- Parent email (for child participants)
        'child',        -- Child email (when parent pays)
        'other'
    )),
    is_primary BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    UNIQUE(customer_id, email)
);

-- Copy existing data if customer_emails table exists
INSERT INTO customer_emails_temp (id, customer_id, email, email_type, is_primary, is_verified, created_at)
SELECT id, customer_id, email, email_type, is_primary, is_verified, created_at 
FROM customer_emails WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='customer_emails');

-- Drop the old table if it exists
DROP TABLE IF EXISTS customer_emails;

-- Rename the temporary table
ALTER TABLE customer_emails_temp RENAME TO customer_emails;

-- Recreate the index
CREATE INDEX idx_customer_emails_customer ON customer_emails(customer_id);
CREATE INDEX idx_customer_emails_email ON customer_emails(email);

-- ============================================================================
-- Example usage for father-son scenario:
-- ============================================================================
-- 
-- INSERT INTO customer_emails (customer_id, email, email_type, is_primary) VALUES
-- -- Father's record (customer_id = 1)
-- (1, '<EMAIL>', 'payment', TRUE),     -- Payment email (primary)
-- (1, '<EMAIL>', 'certificate', FALSE), -- Also receives certificates
-- 
-- -- Son's record (customer_id = 2)  
-- (2, '<EMAIL>', 'personal', TRUE),       -- Son's personal email (primary)
-- (2, '<EMAIL>', 'parent', FALSE);     -- Father's email as parent contact
-- 
-- This structure allows:
-- - Both father and son to have separate customer records
-- - Father's email linked to payment processing
-- - Son's email for direct communication
-- - Flexible certificate delivery to either email
-- - Clear relationship tracking between parent and child
-- ============================================================================