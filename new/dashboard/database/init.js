/**
 * Database Initialization Script
 * Creates SQLite database with lead management schema
 * Designed for 400 leads/day scale with 6-7% conversion tracking
 */

const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');

const DB_PATH = path.join(__dirname, 'leads.db');
const SCHEMA_PATH = path.join(__dirname, 'schema.sql');

/**
 * Initialize the database with schema
 */
async function initializeDatabase() {
    return new Promise((resolve, reject) => {
        console.log('🗄️ Initializing Lead Management Database...');
        
        // Remove existing database if it exists (for fresh start)
        if (fs.existsSync(DB_PATH)) {
            console.log('📄 Removing existing database...');
            fs.unlinkSync(DB_PATH);
        }
        
        // Create new database
        const db = new sqlite3.Database(DB_PATH, (err) => {
            if (err) {
                console.error('❌ Error creating database:', err.message);
                reject(err);
                return;
            }
            console.log('✅ Database file created successfully');
        });
        
        // Read schema file
        const schema = fs.readFileSync(SCHEMA_PATH, 'utf8');
        
        // Execute schema (split by semicolon and execute each statement)
        const statements = schema
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
        
        console.log(`📋 Executing ${statements.length} SQL statements...`);
        
        // Execute statements sequentially
        let completed = 0;
        
        function executeNext() {
            if (completed >= statements.length) {
                console.log('✅ Database schema created successfully');
                
                // Verify tables were created
                db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
                    if (err) {
                        console.error('❌ Error verifying tables:', err.message);
                        reject(err);
                        return;
                    }
                    
                    console.log('📊 Tables created:');
                    tables.forEach(table => {
                        console.log(`   - ${table.name}`);
                    });
                    
                    console.log('\n🎉 Database initialization complete!');
                    console.log(`📍 Database location: ${DB_PATH}`);
                    
                    db.close((err) => {
                        if (err) {
                            console.error('❌ Error closing database:', err.message);
                            reject(err);
                        } else {
                            resolve(DB_PATH);
                        }
                    });
                });
                return;
            }
            
            const statement = statements[completed];
            db.run(statement, (err) => {
                if (err) {
                    console.error(`❌ Error executing statement ${completed + 1}:`, err.message);
                    console.error(`Statement: ${statement.substring(0, 100)}...`);
                    reject(err);
                    return;
                }
                
                completed++;
                executeNext();
            });
        }
        
        executeNext();
    });
}

/**
 * Add sample data for testing
 */
async function addSampleData() {
    return new Promise((resolve, reject) => {
        console.log('📝 Adding sample data for testing...');
        
        const db = new sqlite3.Database(DB_PATH);
        
        const sampleLeads = [
            {
                lead_id: 'fb_123456789',
                account_name: 'Invento Robotics',
                account_id: '****************',
                campaign_name: 'AI Essentials - US Q3',
                campaign_id: 'camp_001',
                full_name: 'John Smith',
                email: '<EMAIL>',
                phone_number: '+**********',
                city: 'San Francisco',
                ai_experience: 'Intermediate',
                expectations: 'Learn practical AI implementation',
                python_experience: 'Yes, 3 years',
                lead_segment: 'Hot',
                segment_score: 85,
                segment_reason: 'Senior title + AI experience',
                status: 'Auto-Responded',
                assigned_to: 'sales_rep_1',
                priority: 1,
                created_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
                country_code: 'US'
            },
            {
                lead_id: 'fb_123456790',
                account_name: 'Invento_US',
                account_id: '****************',
                campaign_name: 'AI for PMs - India',
                campaign_id: 'camp_002',
                full_name: 'Priya Sharma',
                email: '<EMAIL>',
                phone_number: '+************',
                city: 'Bangalore',
                ai_experience: 'Beginner',
                expectations: 'Understand AI for product strategy',
                python_experience: 'No',
                lead_segment: 'Warm',
                segment_score: 55,
                segment_reason: 'Product Manager role',
                status: 'New',
                assigned_to: 'sales_rep_2',
                priority: 3,
                created_time: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
                country_code: 'IN'
            },
            {
                lead_id: 'fb_123456791',
                account_name: 'Invento Robotics',
                account_id: '****************',
                campaign_name: 'Agentic AI - Advanced',
                campaign_id: 'camp_003',
                full_name: 'Ahmed Hassan',
                email: '<EMAIL>',
                phone_number: '+************',
                city: 'Dubai',
                ai_experience: 'Advanced',
                expectations: 'Build autonomous AI agents',
                python_experience: 'Yes, 5+ years',
                lead_segment: 'Hot',
                segment_score: 92,
                segment_reason: 'Advanced AI + Senior Dev + Middle East',
                status: 'Qualified',
                assigned_to: 'sales_rep_1',
                priority: 1,
                created_time: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
                country_code: 'AE'
            }
        ];
        
        const insertLead = db.prepare(`
            INSERT INTO leads (
                lead_id, account_name, account_id, campaign_name, campaign_id,
                full_name, email, phone_number, city, ai_experience, expectations,
                python_experience, lead_segment, segment_score, segment_reason,
                status, assigned_to, priority, created_time, country_code
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        sampleLeads.forEach(lead => {
            insertLead.run([
                lead.lead_id, lead.account_name, lead.account_id, lead.campaign_name, lead.campaign_id,
                lead.full_name, lead.email, lead.phone_number, lead.city, lead.ai_experience,
                lead.expectations, lead.python_experience, lead.lead_segment, lead.segment_score,
                lead.segment_reason, lead.status, lead.assigned_to, lead.priority, lead.created_time,
                lead.country_code
            ]);
        });
        
        insertLead.finalize();
        
        // Add sample workshops
        const insertWorkshop = db.prepare(`
            INSERT INTO workshops (workshop_type, start_date, end_date, max_students, instructor_name, status)
            VALUES (?, ?, ?, ?, ?, ?)
        `);
        
        const workshops = [
            ['AI Essentials', '2025-08-15', '2025-08-17', 50, 'Dr. Sarah Johnson', 'Scheduled'],
            ['AI Practitioner', '2025-08-22', '2025-08-24', 40, 'Prof. Mike Chen', 'Scheduled'],
            ['Agentic AI', '2025-09-05', '2025-09-07', 30, 'Dr. Emily Rodriguez', 'Scheduled']
        ];
        
        workshops.forEach(workshop => {
            insertWorkshop.run(workshop);
        });
        
        insertWorkshop.finalize();
        
        console.log('✅ Sample data added successfully');
        
        db.close((err) => {
            if (err) {
                console.error('❌ Error closing database:', err.message);
                reject(err);
            } else {
                console.log('📦 Database ready for use!');
                resolve();
            }
        });
    });
}

/**
 * Main initialization function
 */
async function main() {
    try {
        await initializeDatabase();
        await addSampleData();
        
        console.log('\n🚀 Lead Management Database is ready!');
        console.log('\n📋 Next steps:');
        console.log('   1. Start the dashboard: npm run dev');
        console.log('   2. Set up Python script integration');
        console.log('   3. Configure cron jobs for lead collection');
        console.log('   4. Test with Facebook Lead Ads API');
        
    } catch (error) {
        console.error('💥 Database initialization failed:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = {
    initializeDatabase,
    addSampleData,
    DB_PATH
};