-- Lead Management Dashboard Database Schema
-- Based on August 30, 2025 PRD Requirements
-- Designed for 400 leads/day scale with 6-7% conversion tracking

-- ============================================================================
-- LEADS TABLE - Core lead information from Facebook Lead Ads
-- ============================================================================
CREATE TABLE leads (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Facebook Source Data
    lead_id TEXT UNIQUE NOT NULL,           -- Facebook lead ID
    account_name TEXT NOT NULL,             -- Invento Robotics / Invento_US
    account_id TEXT NOT NULL,               -- Facebook account ID
    campaign_name TEXT NOT NULL,            -- Campaign name
    campaign_id TEXT NOT NULL,              -- Facebook campaign ID
    adset_name TEXT,                        -- Ad set name
    adset_id TEXT,                          -- Facebook adset ID
    ad_name TEXT,                           -- Ad name
    ad_id TEXT,                             -- Facebook ad ID
    
    -- Lead Information
    full_name TEXT NOT NULL,                -- Contact full name
    email TEXT,                             -- Email address
    phone_number TEXT,                      -- Phone number (cleaned)
    city TEXT,                              -- Location
    
    -- AI/Workshop Specific Fields (for segmentation)
    ai_experience TEXT,                     -- AI experience level
    expectations TEXT,                      -- Workshop expectations
    python_experience TEXT,                 -- Python knowledge level
    other_languages TEXT,                   -- Other programming languages
    
    -- Smart Segmentation (Hot/Warm/Medium)
    lead_segment TEXT DEFAULT 'Medium' CHECK (lead_segment IN ('Hot', 'Warm', 'Medium')),
    segment_score INTEGER DEFAULT 0,        -- Auto-calculated score (0-100)
    segment_reason TEXT,                    -- Why this segment was assigned
    
    -- Lead Status (Proven 6-7% Conversion Funnel)
    status TEXT DEFAULT 'New' CHECK (status IN (
        'New',                              -- Fresh lead from Facebook
        'Auto-Responded',                   -- Instant email + SMS sent
        'Contacted',                        -- Sales person reached out
        'Qualified',                        -- Meets criteria
        'Demo Scheduled',                   -- Workshop demo booked
        'Demo Completed',                   -- Demo conducted
        'Enrolled',                         -- Paid enrollment
        'Workshop Complete',                -- Graduated
        'Alumni Network',                   -- In graduate community
        'Subscription Prospect',            -- Future upsell opportunity
        'Lost'                              -- Not interested/unqualified
    )),
    
    -- Assignment & Ownership
    assigned_to TEXT,                       -- Sales rep assigned
    priority INTEGER DEFAULT 5,            -- Priority (1=highest, 10=lowest)
    
    -- Response Tracking (< 5 minute goal)
    first_response_time DATETIME,          -- When first contacted
    response_time_minutes INTEGER,         -- Minutes from lead to first response
    
    -- Communication History
    email_sent BOOLEAN DEFAULT FALSE,      -- Auto-email sent
    sms_sent BOOLEAN DEFAULT FALSE,        -- Auto-SMS sent
    whatsapp_sent BOOLEAN DEFAULT FALSE,   -- WhatsApp message sent
    call_attempted BOOLEAN DEFAULT FALSE,  -- Call was attempted
    
    -- Workshop Selection
    workshop_type TEXT CHECK (workshop_type IN (
        'AI Essentials',
        'AI Practitioner', 
        'Agentic AI',
        'Vibe Coding',
        'AI for PMs',
        'AI for UX'
    )),
    workshop_price_usd DECIMAL(10,2),      -- Price paid (320 USD or ₹15k converted)
    workshop_currency TEXT DEFAULT 'USD',  -- Currency
    
    -- Certificate Management
    certificate_generated BOOLEAN DEFAULT FALSE,
    certificate_sent BOOLEAN DEFAULT FALSE,
    certificate_delivery_date DATETIME,
    certificate_shared_linkedin BOOLEAN DEFAULT FALSE,
    certificate_shared_twitter BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_time DATETIME NOT NULL,        -- Facebook lead creation time
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME,                 -- When first processed by system
    
    -- Raw Data Storage
    raw_field_data TEXT,                   -- JSON dump of all Facebook fields
    
    -- Geographic & Pricing
    country_code TEXT,                     -- US/IN/Middle East
    timezone TEXT,
    local_time_created DATETIME,
    
    -- Referral Tracking
    referral_code TEXT,                    -- 15% discount code if referred
    referred_by INTEGER,                   -- Reference to other lead ID
    
    FOREIGN KEY (referred_by) REFERENCES leads(id)
);

-- ============================================================================
-- LEAD INTERACTIONS - Track all touchpoints
-- ============================================================================
CREATE TABLE lead_interactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    lead_id INTEGER NOT NULL,
    
    -- Interaction Details
    interaction_type TEXT NOT NULL CHECK (interaction_type IN (
        'email_sent',
        'sms_sent', 
        'whatsapp_sent',
        'call_attempted',
        'call_connected',
        'demo_scheduled',
        'demo_completed',
        'enrollment',
        'workshop_completed',
        'certificate_sent',
        'follow_up',
        'note_added'
    )),
    
    interaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    user_email TEXT,                       -- Who performed the interaction
    
    -- Content
    subject TEXT,                          -- Email subject / call purpose
    message TEXT,                          -- Message content / call notes
    outcome TEXT,                          -- Result of interaction
    next_action TEXT,                      -- Follow-up required
    next_action_date DATETIME,             -- When to follow up
    
    -- Technical Details
    email_message_id TEXT,                 -- For email tracking
    sms_message_id TEXT,                   -- For SMS tracking
    whatsapp_message_id TEXT,              -- For WhatsApp tracking
    
    FOREIGN KEY (lead_id) REFERENCES leads(id)
);

-- ============================================================================
-- CAMPAIGNS - Facebook campaign performance tracking
-- ============================================================================
CREATE TABLE campaigns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Facebook Campaign Info
    campaign_id TEXT UNIQUE NOT NULL,
    campaign_name TEXT NOT NULL,
    account_name TEXT NOT NULL,
    account_id TEXT NOT NULL,
    
    -- Campaign Metrics
    total_leads INTEGER DEFAULT 0,
    hot_leads INTEGER DEFAULT 0,
    warm_leads INTEGER DEFAULT 0,
    medium_leads INTEGER DEFAULT 0,
    
    -- Conversion Tracking
    qualified_leads INTEGER DEFAULT 0,
    demo_scheduled INTEGER DEFAULT 0,
    enrollments INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,2),          -- % conversion rate
    
    -- Cost Analysis
    cost_per_lead DECIMAL(10,2),
    cost_per_qualified_lead DECIMAL(10,2),
    cost_per_enrollment DECIMAL(10,2),
    total_spend DECIMAL(10,2),
    
    -- Geographic Performance
    us_leads INTEGER DEFAULT 0,
    india_leads INTEGER DEFAULT 0,
    middle_east_leads INTEGER DEFAULT 0,
    
    -- Timestamps
    campaign_start_date DATE,
    campaign_end_date DATE,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- USERS - Team members and role-based access
-- ============================================================================
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Authentication
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    
    -- Profile
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    
    -- Role-based Access Control
    role TEXT NOT NULL CHECK (role IN (
        'admin',                           -- Full access
        'executive',                       -- Dashboard + reports
        'sales_rep',                       -- Lead management only
        'bd'                              -- Business development
    )),
    
    -- Settings
    timezone TEXT DEFAULT 'UTC',
    notification_email BOOLEAN DEFAULT TRUE,
    notification_sms BOOLEAN DEFAULT FALSE,
    
    -- Performance Tracking (for sales reps)
    leads_assigned INTEGER DEFAULT 0,
    leads_qualified INTEGER DEFAULT 0,
    enrollments_closed INTEGER DEFAULT 0,
    avg_response_time_minutes DECIMAL(8,2),
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    is_active BOOLEAN DEFAULT TRUE
);

-- ============================================================================
-- WORKSHOPS - Track workshop cohorts and completions
-- ============================================================================
CREATE TABLE workshops (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Workshop Details
    workshop_type TEXT NOT NULL CHECK (workshop_type IN (
        'AI Essentials',
        'AI Practitioner',
        'Agentic AI', 
        'Vibe Coding',
        'AI for PMs',
        'AI for UX'
    )),
    
    -- Schedule
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    session_times TEXT,                    -- JSON array of session times
    timezone TEXT DEFAULT 'UTC',
    
    -- Capacity
    max_students INTEGER DEFAULT 50,
    enrolled_count INTEGER DEFAULT 0,
    completed_count INTEGER DEFAULT 0,
    
    -- Instructor
    instructor_name TEXT,
    instructor_email TEXT,
    
    -- Status
    status TEXT DEFAULT 'Scheduled' CHECK (status IN (
        'Scheduled',                       -- Future workshop
        'Active',                          -- Currently running
        'Completed',                       -- Finished
        'Cancelled'                        -- Cancelled
    )),
    
    -- Certificates
    certificates_generated BOOLEAN DEFAULT FALSE,
    certificates_sent BOOLEAN DEFAULT FALSE,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- WORKSHOP_ENROLLMENTS - Many-to-many relationship
-- ============================================================================
CREATE TABLE workshop_enrollments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    lead_id INTEGER NOT NULL,
    workshop_id INTEGER NOT NULL,
    
    -- Enrollment Details
    enrollment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    payment_amount DECIMAL(10,2),
    payment_currency TEXT DEFAULT 'USD',
    payment_status TEXT DEFAULT 'Pending' CHECK (payment_status IN (
        'Pending',
        'Paid',
        'Refunded',
        'Failed'
    )),
    
    -- Completion Tracking
    attendance_day1 BOOLEAN DEFAULT FALSE,
    attendance_day2 BOOLEAN DEFAULT FALSE,
    attendance_day3 BOOLEAN DEFAULT FALSE,
    completion_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- Certificate
    certificate_earned BOOLEAN DEFAULT FALSE,
    certificate_file_path TEXT,
    certificate_sent_date DATETIME,
    
    -- Feedback
    workshop_rating INTEGER CHECK (workshop_rating BETWEEN 1 AND 5),
    feedback_text TEXT,
    
    FOREIGN KEY (lead_id) REFERENCES leads(id),
    FOREIGN KEY (workshop_id) REFERENCES workshops(id),
    UNIQUE(lead_id, workshop_id)
);

-- ============================================================================
-- ANALYTICS_DAILY - Daily rollup for fast dashboard loading
-- ============================================================================
CREATE TABLE analytics_daily (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Date
    date DATE NOT NULL UNIQUE,
    
    -- Lead Volume
    new_leads_count INTEGER DEFAULT 0,
    hot_leads_count INTEGER DEFAULT 0,
    warm_leads_count INTEGER DEFAULT 0,
    medium_leads_count INTEGER DEFAULT 0,
    
    -- Conversion Funnel
    auto_responded_count INTEGER DEFAULT 0,
    contacted_count INTEGER DEFAULT 0,
    qualified_count INTEGER DEFAULT 0,
    demo_scheduled_count INTEGER DEFAULT 0,
    demo_completed_count INTEGER DEFAULT 0,
    enrolled_count INTEGER DEFAULT 0,
    
    -- Performance Metrics
    avg_response_time_minutes DECIMAL(8,2),
    conversion_rate DECIMAL(5,2),
    cost_per_lead DECIMAL(10,2),
    cost_per_enrollment DECIMAL(10,2),
    
    -- Revenue
    total_revenue_usd DECIMAL(12,2) DEFAULT 0,
    
    -- Geographic Split
    us_leads INTEGER DEFAULT 0,
    india_leads INTEGER DEFAULT 0,
    middle_east_leads INTEGER DEFAULT 0,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- SYSTEM_SETTINGS - Configuration and feature flags
-- ============================================================================
CREATE TABLE system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type TEXT DEFAULT 'string' CHECK (setting_type IN ('string', 'integer', 'boolean', 'json')),
    description TEXT,
    
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
);

-- ============================================================================
-- INDEXES for Performance (400 leads/day scale)
-- ============================================================================

-- Lead lookups
CREATE INDEX idx_leads_lead_id ON leads(lead_id);
CREATE INDEX idx_leads_email ON leads(email);
CREATE INDEX idx_leads_phone ON leads(phone_number);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_segment ON leads(lead_segment);
CREATE INDEX idx_leads_assigned_to ON leads(assigned_to);
CREATE INDEX idx_leads_created_time ON leads(created_time);
CREATE INDEX idx_leads_account_campaign ON leads(account_name, campaign_name);

-- Fast dashboard queries
CREATE INDEX idx_leads_status_created ON leads(status, created_time);
CREATE INDEX idx_leads_segment_status ON leads(lead_segment, status);

-- Interaction lookups
CREATE INDEX idx_interactions_lead_id ON lead_interactions(lead_id);
CREATE INDEX idx_interactions_type_date ON lead_interactions(interaction_type, interaction_date);

-- Campaign performance
CREATE INDEX idx_campaigns_account ON campaigns(account_name);
CREATE INDEX idx_campaigns_dates ON campaigns(campaign_start_date, campaign_end_date);

-- Workshop tracking
CREATE INDEX idx_workshops_type_status ON workshops(workshop_type, status);
CREATE INDEX idx_workshops_dates ON workshops(start_date, end_date);
CREATE INDEX idx_enrollments_workshop ON workshop_enrollments(workshop_id);
CREATE INDEX idx_enrollments_lead ON workshop_enrollments(lead_id);

-- Daily analytics
CREATE INDEX idx_analytics_date ON analytics_daily(date);

-- ============================================================================
-- INITIAL DATA - Default system settings
-- ============================================================================

-- Default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('auto_response_enabled', 'true', 'boolean', 'Enable automatic email/SMS responses'),
('response_time_goal_minutes', '5', 'integer', 'Target response time in minutes'),
('hot_lead_threshold', '70', 'integer', 'Score threshold for Hot leads'),
('warm_lead_threshold', '40', 'integer', 'Score threshold for Warm leads'),
('conversion_rate_target', '6.5', 'string', 'Target conversion rate percentage'),
('daily_lead_target', '400', 'integer', 'Daily lead processing target'),
('workshop_price_usd', '320', 'string', 'Standard workshop price in USD'),
('workshop_price_inr', '15000', 'string', 'Standard workshop price in INR'),
('certificate_auto_send', 'true', 'boolean', 'Automatically send certificates'),
('referral_discount_percent', '15', 'integer', 'Referral discount percentage');

-- Default admin user (password: admin123 - should be changed)
INSERT INTO users (username, email, password_hash, first_name, last_name, role) VALUES
('admin', '<EMAIL>', '$2b$10$rBSJbV.zW8hFGvCK8Q8QNO8kGkS1k.wXGLm.VzY5P9L8cYyMnGt7e', 'Admin', 'User', 'admin');

-- ============================================================================
-- VIEWS for Common Dashboard Queries
-- ============================================================================

-- Lead conversion funnel view
CREATE VIEW lead_funnel_current AS
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM leads), 2) as percentage
FROM leads 
GROUP BY status
ORDER BY 
    CASE status
        WHEN 'New' THEN 1
        WHEN 'Auto-Responded' THEN 2
        WHEN 'Contacted' THEN 3
        WHEN 'Qualified' THEN 4
        WHEN 'Demo Scheduled' THEN 5
        WHEN 'Demo Completed' THEN 6
        WHEN 'Enrolled' THEN 7
        WHEN 'Workshop Complete' THEN 8
        WHEN 'Alumni Network' THEN 9
        WHEN 'Subscription Prospect' THEN 10
        WHEN 'Lost' THEN 11
    END;

-- Hot leads requiring immediate attention
CREATE VIEW hot_leads_priority AS
SELECT 
    l.*,
    (julianday('now') - julianday(l.created_time)) * 24 * 60 as minutes_since_created
FROM leads l
WHERE l.lead_segment = 'Hot' 
    AND l.status IN ('New', 'Auto-Responded')
    AND l.assigned_to IS NOT NULL
ORDER BY l.created_time ASC;

-- Sales rep performance view
CREATE VIEW sales_rep_performance AS
SELECT 
    u.username,
    u.first_name || ' ' || u.last_name as full_name,
    COUNT(l.id) as total_assigned,
    COUNT(CASE WHEN l.status NOT IN ('New', 'Lost') THEN 1 END) as contacted,
    COUNT(CASE WHEN l.status IN ('Qualified', 'Demo Scheduled', 'Demo Completed', 'Enrolled', 'Workshop Complete', 'Alumni Network') THEN 1 END) as qualified,
    COUNT(CASE WHEN l.status IN ('Enrolled', 'Workshop Complete', 'Alumni Network') THEN 1 END) as enrollments,
    ROUND(AVG(l.response_time_minutes), 2) as avg_response_time,
    ROUND(COUNT(CASE WHEN l.status IN ('Enrolled', 'Workshop Complete', 'Alumni Network') THEN 1 END) * 100.0 / COUNT(l.id), 2) as conversion_rate
FROM users u
LEFT JOIN leads l ON l.assigned_to = u.username
WHERE u.role = 'sales_rep' AND u.is_active = 1
GROUP BY u.id, u.username, u.first_name, u.last_name;

-- Daily lead volume trend
CREATE VIEW daily_lead_trend AS
SELECT 
    DATE(created_time) as date,
    COUNT(*) as total_leads,
    COUNT(CASE WHEN lead_segment = 'Hot' THEN 1 END) as hot_leads,
    COUNT(CASE WHEN lead_segment = 'Warm' THEN 1 END) as warm_leads,
    COUNT(CASE WHEN lead_segment = 'Medium' THEN 1 END) as medium_leads,
    AVG(response_time_minutes) as avg_response_time
FROM leads 
WHERE created_time >= DATE('now', '-30 days')
GROUP BY DATE(created_time)
ORDER BY date DESC;