-- ============================================================================
-- Modern AI Pro Database Schema V2 Migration
-- Purpose: Add customer tracking, payment management, and multi-workshop support
-- Date: 2025-01-01
-- ============================================================================

-- ============================================================================
-- LEADS TABLE MODIFICATIONS
-- ============================================================================
ALTER TABLE leads ADD COLUMN customer_id INTEGER REFERENCES customers(id);
ALTER TABLE leads ADD COLUMN lead_source TEXT DEFAULT 'meta_ads' CHECK (lead_source IN (
    'meta_ads',
    'google_ads',
    'referral',
    'organic',
    'email_campaign',
    'direct',
    'partner',
    'event'
));
ALTER TABLE leads ADD COLUMN lead_source_detail TEXT; -- JSON: referral:customer_123, utm_source, etc.

-- ============================================================================
-- CUSTOMERS TABLE - Track paying customers separately from leads
-- ============================================================================
CREATE TABLE customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    lead_id INTEGER NOT NULL,
    
    -- Customer Identity
    customer_email TEXT NOT NULL,           -- Primary customer email
    customer_phone TEXT,                    -- Primary phone
    
    -- Payment Information
    stripe_customer_id TEXT UNIQUE,         -- Stripe customer ID
    payment_email TEXT,                     -- Email used for payment (may differ)
    payment_method TEXT DEFAULT 'stripe',   -- Primary payment method
    
    -- Subscription Status
    subscription_status TEXT DEFAULT 'none' CHECK (subscription_status IN (
        'none',
        'trial',
        'active',
        'paused',
        'cancelled',
        'expired'
    )),
    subscription_start_date DATE,
    subscription_end_date DATE,
    subscription_plan TEXT CHECK (subscription_plan IN (
        'basic',     -- $30/month
        'premium',   -- $500/3-months
        'team',      -- Custom pricing
        'enterprise' -- Annual contracts
    )),
    
    -- Lifetime Value Tracking
    total_spent DECIMAL(10,2) DEFAULT 0,
    total_spent_usd DECIMAL(10,2) DEFAULT 0,  -- USD normalized
    workshops_completed INTEGER DEFAULT 0,
    workshops_enrolled INTEGER DEFAULT 0,
    last_purchase_date DATE,
    
    -- Customer Segment
    customer_segment TEXT DEFAULT 'new' CHECK (customer_segment IN (
        'new',              -- First purchase
        'repeat',           -- 2+ workshops
        'loyal',            -- 3+ workshops
        'subscriber',       -- Active subscription
        'vip',              -- High LTV or executive
        'churned'           -- No activity >6 months
    )),
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (lead_id) REFERENCES leads(id)
);

-- ============================================================================
-- PAYMENTS TABLE - Track all payment transactions
-- ============================================================================
CREATE TABLE payments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    lead_id INTEGER NOT NULL,
    
    -- Payment Details
    amount DECIMAL(10,2) NOT NULL,
    amount_usd DECIMAL(10,2) NOT NULL,      -- USD equivalent for reporting
    currency TEXT NOT NULL DEFAULT 'USD',
    payment_method TEXT NOT NULL CHECK (payment_method IN (
        'stripe',
        'upi',
        'bank_transfer',
        'razorpay',
        'paypal',
        'cash',
        'other'
    )),
    
    -- Stripe Fields
    stripe_payment_intent_id TEXT UNIQUE,
    stripe_charge_id TEXT,
    stripe_invoice_id TEXT,
    payment_email TEXT,                     -- Email used for this payment
    
    -- Manual Entry Fields
    manual_reference TEXT,                  -- UPI ID, bank reference, etc.
    manual_entered_by TEXT,                 -- User who entered manual payment
    manual_entry_notes TEXT,
    
    -- Product Details
    product_type TEXT NOT NULL CHECK (product_type IN (
        'workshop',
        'workshop_package',     -- Team packages
        'subscription',
        'coaching',
        'certification',
        'other'
    )),
    workshop_id INTEGER,
    package_details TEXT,                   -- JSON for complex products
    
    -- Status
    status TEXT DEFAULT 'pending' CHECK (status IN (
        'pending',
        'processing',
        'completed',
        'failed',
        'refunded',
        'partially_refunded',
        'disputed'
    )),
    processed_at DATETIME,
    refund_amount DECIMAL(10,2) DEFAULT 0,
    refund_reason TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (lead_id) REFERENCES leads(id),
    FOREIGN KEY (workshop_id) REFERENCES workshops(id)
);

-- ============================================================================
-- WORKSHOP_ENROLLMENTS MODIFICATIONS
-- ============================================================================
ALTER TABLE workshop_enrollments ADD COLUMN customer_id INTEGER REFERENCES customers(id);
ALTER TABLE workshop_enrollments ADD COLUMN enrollment_source TEXT DEFAULT 'new_purchase' CHECK (enrollment_source IN (
    'new_purchase',
    'subscription',
    'package',
    'referral_reward',
    'complimentary'
));
ALTER TABLE workshop_enrollments ADD COLUMN payment_id INTEGER REFERENCES payments(id);

-- Communication tracking
ALTER TABLE workshop_enrollments ADD COLUMN pre_workshop_email_sent BOOLEAN DEFAULT FALSE;
ALTER TABLE workshop_enrollments ADD COLUMN pre_workshop_email_date DATETIME;
ALTER TABLE workshop_enrollments ADD COLUMN reminder_72h_sent BOOLEAN DEFAULT FALSE;
ALTER TABLE workshop_enrollments ADD COLUMN reminder_24h_sent BOOLEAN DEFAULT FALSE;
ALTER TABLE workshop_enrollments ADD COLUMN reminder_1h_sent BOOLEAN DEFAULT FALSE;
ALTER TABLE workshop_enrollments ADD COLUMN zoom_link_sent BOOLEAN DEFAULT FALSE;
ALTER TABLE workshop_enrollments ADD COLUMN post_workshop_email_sent BOOLEAN DEFAULT FALSE;
ALTER TABLE workshop_enrollments ADD COLUMN post_workshop_email_date DATETIME;

-- ============================================================================
-- WORKSHOP_COMMUNICATIONS - Bulk email tracking per workshop
-- ============================================================================
CREATE TABLE workshop_communications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    workshop_id INTEGER NOT NULL,
    
    -- Email Campaign Status
    welcome_email_sent BOOLEAN DEFAULT FALSE,
    welcome_email_date DATETIME,
    welcome_email_count INTEGER DEFAULT 0,
    
    reminder_72h_sent BOOLEAN DEFAULT FALSE,
    reminder_72h_date DATETIME,
    reminder_72h_count INTEGER DEFAULT 0,
    
    reminder_24h_sent BOOLEAN DEFAULT FALSE,
    reminder_24h_date DATETIME,
    reminder_24h_count INTEGER DEFAULT 0,
    
    reminder_1h_sent BOOLEAN DEFAULT FALSE,
    reminder_1h_date DATETIME,
    reminder_1h_count INTEGER DEFAULT 0,
    
    post_workshop_sent BOOLEAN DEFAULT FALSE,
    post_workshop_date DATETIME,
    post_workshop_count INTEGER DEFAULT 0,
    
    certificate_batch_sent BOOLEAN DEFAULT FALSE,
    certificate_batch_date DATETIME,
    certificate_batch_count INTEGER DEFAULT 0,
    
    -- Bulk Operations Summary
    total_enrolled INTEGER DEFAULT 0,
    total_emails_sent INTEGER DEFAULT 0,
    total_certificates_sent INTEGER DEFAULT 0,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (workshop_id) REFERENCES workshops(id)
);

-- ============================================================================
-- CUSTOMER_EMAILS - Handle multiple emails per customer
-- ============================================================================
CREATE TABLE customer_emails (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    
    email TEXT NOT NULL,
    email_type TEXT DEFAULT 'personal' CHECK (email_type IN (
        'personal',
        'work',
        'payment',      -- Used for payments (father pays for son)
        'certificate',  -- Email for receiving certificates
        'emergency',    -- Emergency contact email
        'spouse',       -- Family member email
        'parent',       -- Parent email (for child participants)
        'child',        -- Child email (when parent pays)
        'other'
    )),
    is_primary BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    UNIQUE(customer_id, email)
);

-- ============================================================================
-- SUBSCRIPTIONS - Detailed subscription tracking
-- ============================================================================
CREATE TABLE subscriptions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    
    -- Subscription Details
    stripe_subscription_id TEXT UNIQUE,
    plan_type TEXT NOT NULL CHECK (plan_type IN ('basic', 'premium', 'team', 'enterprise')),
    status TEXT NOT NULL,
    
    -- Billing
    billing_period TEXT CHECK (billing_period IN ('monthly', 'quarterly', 'annual')),
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    
    -- Dates
    start_date DATE NOT NULL,
    current_period_start DATE,
    current_period_end DATE,
    cancelled_at DATETIME,
    cancel_reason TEXT,
    
    -- Usage
    workshops_attended INTEGER DEFAULT 0,
    workshops_remaining INTEGER,            -- For limited plans
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);

-- ============================================================================
-- NEW INDEXES for customer and payment lookups
-- ============================================================================
CREATE INDEX idx_customers_lead_id ON customers(lead_id);
CREATE INDEX idx_customers_stripe_id ON customers(stripe_customer_id);
CREATE INDEX idx_customers_email ON customers(customer_email);
CREATE INDEX idx_customers_segment ON customers(customer_segment);

CREATE INDEX idx_payments_customer_id ON payments(customer_id);
CREATE INDEX idx_payments_stripe_intent ON payments(stripe_payment_intent_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_product_type ON payments(product_type);

CREATE INDEX idx_customer_emails_customer ON customer_emails(customer_id);
CREATE INDEX idx_customer_emails_email ON customer_emails(email);

CREATE INDEX idx_workshop_enrollments_customer ON workshop_enrollments(customer_id);
CREATE INDEX idx_workshop_enrollments_payment ON workshop_enrollments(payment_id);

-- ============================================================================
-- VIEWS for customer management and workshop operations
-- ============================================================================

-- Customer workshop history with complete details
CREATE VIEW customer_workshop_history AS
SELECT 
    c.id as customer_id,
    c.customer_email,
    l.full_name,
    COUNT(DISTINCT we.workshop_id) as workshops_taken,
    GROUP_CONCAT(DISTINCT w.workshop_type) as workshop_types,
    MIN(we.enrollment_date) as first_workshop_date,
    MAX(we.enrollment_date) as last_workshop_date,
    SUM(CASE WHEN we.completion_percentage >= 80 THEN 1 ELSE 0 END) as workshops_completed,
    SUM(CASE WHEN we.certificate_earned THEN 1 ELSE 0 END) as certificates_earned,
    AVG(we.workshop_rating) as avg_rating,
    c.total_spent,
    c.subscription_status,
    c.customer_segment
FROM customers c
JOIN leads l ON c.lead_id = l.id
JOIN workshop_enrollments we ON c.id = we.customer_id
JOIN workshops w ON we.workshop_id = w.id
GROUP BY c.id;

-- Workshop roster for operations
CREATE VIEW workshop_roster AS
SELECT 
    w.id as workshop_id,
    w.workshop_type,
    w.start_date,
    w.status as workshop_status,
    l.full_name,
    l.email as lead_email,
    l.phone_number,
    c.customer_email,
    c.payment_email,
    we.enrollment_date,
    we.payment_status,
    we.attendance_day1,
    we.attendance_day2,
    we.attendance_day3,
    we.completion_percentage,
    we.certificate_earned,
    we.pre_workshop_email_sent,
    we.reminder_24h_sent,
    we.zoom_link_sent,
    we.post_workshop_email_sent
FROM workshops w
JOIN workshop_enrollments we ON w.id = we.workshop_id
JOIN leads l ON we.lead_id = l.id
LEFT JOIN customers c ON we.customer_id = c.id
ORDER BY w.start_date DESC, l.full_name;

-- Customer journey tracking
CREATE VIEW customer_journey AS
SELECT 
    c.id as customer_id,
    l.full_name,
    l.lead_source,
    l.created_time as lead_date,
    c.created_at as customer_date,
    JULIANDAY(c.created_at) - JULIANDAY(l.created_time) as days_to_convert,
    COUNT(DISTINCT we.workshop_id) as workshops_taken,
    c.subscription_status,
    c.total_spent_usd,
    c.customer_segment,
    MAX(p.processed_at) as last_purchase_date,
    CASE 
        WHEN c.subscription_status = 'active' THEN 'Active Subscriber'
        WHEN JULIANDAY('now') - JULIANDAY(MAX(p.processed_at)) > 180 THEN 'Churned'
        WHEN JULIANDAY('now') - JULIANDAY(MAX(p.processed_at)) > 90 THEN 'At Risk'
        ELSE 'Active'
    END as engagement_status
FROM customers c
JOIN leads l ON c.lead_id = l.id
LEFT JOIN workshop_enrollments we ON c.id = we.customer_id
LEFT JOIN payments p ON c.id = p.customer_id AND p.status = 'completed'
GROUP BY c.id;

-- Payment reconciliation view
CREATE VIEW payment_reconciliation AS
SELECT 
    p.id as payment_id,
    p.created_at as payment_date,
    l.full_name,
    l.email as lead_email,
    c.customer_email,
    p.payment_email,
    p.amount,
    p.currency,
    p.payment_method,
    p.status,
    p.product_type,
    w.workshop_type,
    w.start_date as workshop_date,
    CASE 
        WHEN p.payment_email = c.customer_email THEN 'Matched'
        WHEN p.payment_email = l.email THEN 'Lead Email'
        ELSE 'Different Email'
    END as email_match_status
FROM payments p
JOIN customers c ON p.customer_id = c.id
JOIN leads l ON p.lead_id = l.id
LEFT JOIN workshops w ON p.workshop_id = w.id
ORDER BY p.created_at DESC;

-- Lead source ROI analysis
CREATE VIEW lead_source_roi AS
SELECT 
    l.lead_source,
    COUNT(DISTINCT l.id) as total_leads,
    COUNT(DISTINCT c.id) as customers,
    ROUND(COUNT(DISTINCT c.id) * 100.0 / COUNT(DISTINCT l.id), 2) as conversion_rate,
    SUM(c.total_spent_usd) as total_revenue,
    AVG(c.total_spent_usd) as avg_customer_value,
    COUNT(DISTINCT CASE WHEN c.subscription_status = 'active' THEN c.id END) as active_subscribers
FROM leads l
LEFT JOIN customers c ON l.id = c.lead_id
GROUP BY l.lead_source;

-- ============================================================================
-- TRIGGERS for data integrity and automation
-- ============================================================================

-- Update customer total spent on payment completion
CREATE TRIGGER update_customer_spend
AFTER UPDATE OF status ON payments
WHEN NEW.status = 'completed' AND OLD.status != 'completed'
BEGIN
    UPDATE customers
    SET 
        total_spent = total_spent + NEW.amount,
        total_spent_usd = total_spent_usd + NEW.amount_usd,
        last_purchase_date = DATE(NEW.processed_at),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.customer_id;
END;

-- Update customer segment based on workshops completed
CREATE TRIGGER update_customer_segment
AFTER UPDATE OF workshops_completed ON customers
BEGIN
    UPDATE customers
    SET customer_segment = CASE
        WHEN subscription_status = 'active' THEN 'subscriber'
        WHEN workshops_completed >= 3 THEN 'loyal'
        WHEN workshops_completed >= 2 THEN 'repeat'
        ELSE 'new'
    END,
    updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.id;
END;

-- Update workshop enrolled count
CREATE TRIGGER update_workshop_enrollment_count
AFTER INSERT ON workshop_enrollments
BEGIN
    UPDATE workshops
    SET enrolled_count = (
        SELECT COUNT(*) 
        FROM workshop_enrollments 
        WHERE workshop_id = NEW.workshop_id 
        AND payment_status = 'Paid'
    )
    WHERE id = NEW.workshop_id;
END;