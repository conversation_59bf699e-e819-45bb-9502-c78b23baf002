const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const DB_PATH = path.join(__dirname, '../../data/database/leads.db');

// User IDs from documentation
const MANISH_ID = 3;  // Manish <PERSON>las<PERSON> - Indian market
const MAHALAKSHMI_ID = 9;  // <PERSON><PERSON><PERSON><PERSON><PERSON> - International market

function isIndianPhone(phone) {
  if (!phone || phone === 'N/A' || phone === '') return false;
  
  // Clean the phone number
  const cleanPhone = phone.replace(/\s+/g, '').replace(/[-()]/g, '');
  
  // Check for Indian numbers
  return cleanPhone.startsWith('+91') || 
         cleanPhone.startsWith('91') ||
         (cleanPhone.length === 10 && /^[6-9]/.test(cleanPhone)); // Indian mobile pattern
}

async function assignExistingLeads() {
  const db = new sqlite3.Database(DB_PATH);
  
  try {
    console.log('🔍 Analyzing existing leads for assignment...\n');
    
    // Get all unassigned leads with phone numbers
    const leads = await new Promise((resolve, reject) => {
      db.all(`
        SELECT id, full_name, phone, email, lead_source, created_time
        FROM leads 
        WHERE assigned_to IS NULL 
          AND data_source != 'stripe' 
          AND (payment_status IS NULL OR payment_status NOT IN ('Paid', 'Partial'))
        ORDER BY created_time DESC
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`📊 Found ${leads.length} unassigned leads to process\n`);

    if (leads.length === 0) {
      console.log('✅ No leads to assign. All leads are already assigned.');
      return;
    }

    let assignments = {
      manish: { count: 0, leads: [] },
      mahalakshmi: { count: 0, leads: [] },
      analysis: {
        total: leads.length,
        withPhone: 0,
        withoutPhone: 0,
        indianPhone: 0,
        internationalPhone: 0
      }
    };

    // Analyze and assign leads
    for (const lead of leads) {
      let assigneeId;
      let assigneeName;
      let reason;

      if (lead.phone && lead.phone !== 'N/A' && lead.phone !== '') {
        assignments.analysis.withPhone++;
        
        if (isIndianPhone(lead.phone)) {
          assigneeId = MANISH_ID;
          assigneeName = 'Manish';
          reason = 'Indian phone number';
          assignments.analysis.indianPhone++;
          assignments.manish.count++;
          assignments.manish.leads.push(lead);
        } else {
          assigneeId = MAHALAKSHMI_ID;
          assigneeName = 'Mahalakshmi';
          reason = 'International phone number';
          assignments.analysis.internationalPhone++;
          assignments.mahalakshmi.count++;
          assignments.mahalakshmi.leads.push(lead);
        }
      } else {
        assignments.analysis.withoutPhone++;
        assigneeId = MAHALAKSHMI_ID;
        assigneeName = 'Mahalakshmi';
        reason = 'No phone number (default to international)';
        assignments.mahalakshmi.count++;
        assignments.mahalakshmi.leads.push(lead);
      }

      // Update the lead assignment
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE leads SET assigned_to = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [assigneeId, lead.id],
          function(err) {
            if (err) reject(err);
            else resolve(this.changes);
          }
        );
      });

      console.log(`✅ ${lead.full_name} (${lead.phone || 'No phone'}) → ${assigneeName} (${reason})`);
    }

    // Print summary
    console.log('\n' + '='.repeat(60));
    console.log('📈 ASSIGNMENT SUMMARY');
    console.log('='.repeat(60));
    console.log(`📊 Total leads processed: ${assignments.analysis.total}`);
    console.log(`📱 Leads with phone: ${assignments.analysis.withPhone}`);
    console.log(`❌ Leads without phone: ${assignments.analysis.withoutPhone}`);
    console.log(`🇮🇳 Indian phone numbers: ${assignments.analysis.indianPhone}`);
    console.log(`🌍 International/other: ${assignments.analysis.internationalPhone}`);
    console.log('');
    console.log('👥 FINAL ASSIGNMENTS:');
    console.log(`🇮🇳 Manish Tulasi: ${assignments.manish.count} leads`);
    console.log(`🌍 Mahalakshmi Radhakrushnun: ${assignments.mahalakshmi.count} leads`);
    console.log('');
    
    const manishPercentage = ((assignments.manish.count / assignments.analysis.total) * 100).toFixed(1);
    const mahalakshmiPercentage = ((assignments.mahalakshmi.count / assignments.analysis.total) * 100).toFixed(1);
    
    console.log('📊 DISTRIBUTION:');
    console.log(`Manish: ${manishPercentage}%`);
    console.log(`Mahalakshmi: ${mahalakshmiPercentage}%`);
    console.log('');

    // Sample assignments for verification
    console.log('🔍 SAMPLE ASSIGNMENTS:');
    console.log('');
    console.log('Manish (Indian numbers):');
    assignments.manish.leads.slice(0, 5).forEach(lead => {
      console.log(`  • ${lead.full_name} - ${lead.phone} - ${lead.email}`);
    });
    if (assignments.manish.leads.length > 5) {
      console.log(`  ... and ${assignments.manish.leads.length - 5} more`);
    }
    
    console.log('');
    console.log('Mahalakshmi (International/other):');
    assignments.mahalakshmi.leads.slice(0, 5).forEach(lead => {
      console.log(`  • ${lead.full_name} - ${lead.phone || 'No phone'} - ${lead.email}`);
    });
    if (assignments.mahalakshmi.leads.length > 5) {
      console.log(`  ... and ${assignments.mahalakshmi.leads.length - 5} more`);
    }

    console.log('\n✅ Assignment completed successfully!');

  } catch (error) {
    console.error('❌ Error during assignment:', error);
  } finally {
    db.close();
  }
}

// Run the assignment
console.log('🚀 Starting existing leads assignment based on phone numbers...\n');
assignExistingLeads();