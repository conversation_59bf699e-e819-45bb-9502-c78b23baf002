const bcrypt = require('bcrypt');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const DB_PATH = path.join(__dirname, '../../database/leads.db');

async function createAdminAccount() {
  const db = new sqlite3.Database(DB_PATH);
  
  const adminData = {
    username: 'bala<PERSON>',
    email: '<EMAIL>',
    first_name: '<PERSON><PERSON><PERSON>',
    last_name: '<PERSON><PERSON><PERSON><PERSON>',
    password: 'AdminMitra2025!Secure#99',
    role: 'admin'
  };

  try {
    // Check if user already exists
    const existingUser = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM users WHERE email = ? OR username = ?',
        [adminData.email, adminData.username],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    const passwordHash = await bcrypt.hash(adminData.password, 12);

    if (existingUser) {
      // Update existing user
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users SET 
           email = ?, 
           first_name = ?, 
           last_name = ?, 
           password_hash = ?, 
           role = ?,
           is_active = 1,
           notification_email = 1,
           notification_sms = 1
           WHERE username = ?`,
          [
            adminData.email,
            adminData.first_name,
            adminData.last_name,
            passwordHash,
            adminData.role,
            adminData.username
          ],
          function(err) {
            if (err) reject(err);
            else resolve(this.changes);
          }
        );
      });
      console.log('✅ Updated existing admin account for:', adminData.username);
    } else {
      // Create new user
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO users (
            username, email, first_name, last_name, 
            password_hash, role, is_active, notification_email, notification_sms
          ) VALUES (?, ?, ?, ?, ?, ?, 1, 1, 1)`,
          [
            adminData.username,
            adminData.email,
            adminData.first_name,
            adminData.last_name,
            passwordHash,
            adminData.role
          ],
          function(err) {
            if (err) reject(err);
            else resolve(this.lastID);
          }
        );
      });
      console.log('✅ Created new admin account for:', adminData.username);
    }

    // Verify the account was created/updated
    const verifyUser = await new Promise((resolve, reject) => {
      db.get(
        'SELECT username, email, first_name, last_name, role, is_active FROM users WHERE email = ?',
        [adminData.email],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    console.log('\n🎉 ADMIN ACCOUNT DETAILS:');
    console.log('=' .repeat(40));
    console.log('👤 Name:', `${verifyUser.first_name} ${verifyUser.last_name}`);
    console.log('📧 Email:', verifyUser.email);
    console.log('👤 Username:', verifyUser.username);
    console.log('🔑 Password:', adminData.password);
    console.log('🏷️  Role:', verifyUser.role);
    console.log('✅ Email Verified:', verifyUser.email_verified ? 'Yes' : 'No');
    console.log('💎 Subscription:', verifyUser.subscription_tier);
    console.log('\n🔒 SECURITY NOTES:');
    console.log('• This is a temporary password');
    console.log('• Change password on first login');
    console.log('• Strong password with uppercase, lowercase, numbers, special chars');
    console.log('• Login at: http://localhost:3000/login');
    console.log('• Change password at: http://localhost:3000/settings');

  } catch (error) {
    console.error('❌ Error creating admin account:', error);
  } finally {
    db.close();
  }
}

createAdminAccount();