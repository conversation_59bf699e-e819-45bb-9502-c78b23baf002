const bcrypt = require('bcryptjs');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, '../../data/database/leads.db');

async function createSalesAccounts() {
  const db = new sqlite3.Database(dbPath);
  
  // Strong temporary passwords
  const passwords = {
    '<EMAIL>': 'TempSales2025!Manish#42',
    '<EMAIL>': 'TempSales2025!Maha#78'
  };

  console.log('🔐 Creating sales accounts with authentication...\n');

  for (const [email, password] of Object.entries(passwords)) {
    try {
      // Hash the password
      const passwordHash = await bcrypt.hash(password, 12);
      
      // Extract name parts
      const username = email.split('@')[0];
      const firstName = username.charAt(0).toUpperCase() + username.slice(1);
      const lastName = email === '<EMAIL>' ? 'Kumar' : 'Sales';

      // Check if user already exists
      const existingUser = await new Promise((resolve, reject) => {
        db.get('SELECT * FROM users WHERE email = ? OR username = ?', [email, username], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      if (existingUser) {
        // Update existing user
        await new Promise((resolve, reject) => {
          db.run(`
            UPDATE users SET 
              email = ?,
              password_hash = ?,
              email_verified = TRUE,
              role = 'sales',
              first_name = ?,
              last_name = ?,
              is_active = TRUE,
              subscription_tier = 'basic'
            WHERE id = ?
          `, [email, passwordHash, firstName, lastName, existingUser.id], function(err) {
            if (err) reject(err);
            else resolve();
          });
        });
        
        console.log(`✅ Updated existing user: ${email}`);
      } else {
        // Create new user
        await new Promise((resolve, reject) => {
          db.run(`
            INSERT INTO users (
              username, email, first_name, last_name, 
              password_hash, email_verified, role, 
              is_active, subscription_tier
            ) VALUES (?, ?, ?, ?, ?, TRUE, 'sales', TRUE, 'basic')
          `, [username, email, firstName, lastName, passwordHash], function(err) {
            if (err) reject(err);
            else resolve();
          });
        });
        
        console.log(`✅ Created new user: ${email}`);
      }
      
      console.log(`   Username: ${username}`);
      console.log(`   Password: ${password}`);
      console.log(`   Role: sales`);
      console.log(`   Status: Active & Email Verified\n`);
      
    } catch (error) {
      console.error(`❌ Error creating account for ${email}:`, error);
    }
  }

  // Update role enum to include all three roles
  console.log('📋 Available roles: admin, sales, student');
  console.log('\n🔒 SECURITY NOTE:');
  console.log('These are temporary passwords. Users should change them on first login.');
  
  db.close();
}

// Run the script
createSalesAccounts().catch(console.error);