const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const DB_PATH = path.join(__dirname, '../../data/database/leads.db');

// User IDs
const MANISH_ID = 3;  // Only +91 and 91 prefixes
const MAHALAKSHMI_ID = 9;  // Everything else (US, international, no phone)

function isIndianPhone(phone) {
  if (!phone || phone === 'N/A' || phone === '') return false;
  
  // Clean the phone number
  const cleanPhone = phone.replace(/\s+/g, '').replace(/[-()]/g, '');
  
  // ONLY +91 or 91 prefixes (be very specific)
  return cleanPhone.startsWith('+91') || cleanPhone.startsWith('91');
}

async function fixLeadAssignments() {
  const db = new sqlite3.Database(DB_PATH);
  
  try {
    console.log('🔧 Fixing lead assignments with correct logic...\n');
    
    // Get all leads that were assigned by our previous script
    const leads = await new Promise((resolve, reject) => {
      db.all(`
        SELECT id, full_name, phone, email, assigned_to
        FROM leads 
        WHERE assigned_to IS NOT NULL 
          AND data_source != 'stripe' 
          AND (payment_status IS NULL OR payment_status NOT IN ('Paid', 'Partial'))
        ORDER BY created_time DESC
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`📊 Found ${leads.length} assigned leads to review\n`);

    let corrections = {
      manish: { count: 0, leads: [] },
      mahalakshmi: { count: 0, leads: [] },
      noChange: 0,
      analysis: {
        total: leads.length,
        indianPhone: 0,
        otherPhone: 0,
        corrections: 0
      }
    };

    // Fix assignments
    for (const lead of leads) {
      let correctAssigneeId;
      let correctAssigneeName;
      let reason;
      let needsChange = false;

      if (isIndianPhone(lead.phone)) {
        correctAssigneeId = MANISH_ID;
        correctAssigneeName = 'Manish';
        reason = 'Indian phone (+91/91 prefix)';
        corrections.analysis.indianPhone++;
        corrections.manish.leads.push(lead);
        
        if (lead.assigned_to !== MANISH_ID) {
          needsChange = true;
          corrections.manish.count++;
        }
      } else {
        correctAssigneeId = MAHALAKSHMI_ID;
        correctAssigneeName = 'Mahalakshmi';
        reason = 'Non-Indian (US/international/no phone)';
        corrections.analysis.otherPhone++;
        corrections.mahalakshmi.leads.push(lead);
        
        if (lead.assigned_to !== MAHALAKSHMI_ID) {
          needsChange = true;
          corrections.mahalakshmi.count++;
        }
      }

      if (needsChange) {
        // Update the lead assignment
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE leads SET assigned_to = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [correctAssigneeId, lead.id],
            function(err) {
              if (err) reject(err);
              else resolve(this.changes);
            }
          );
        });

        corrections.analysis.corrections++;
        console.log(`✅ Fixed: ${lead.full_name} (${lead.phone || 'No phone'}) → ${correctAssigneeName} (${reason})`);
      } else {
        corrections.noChange++;
      }
    }

    // Print corrected summary
    console.log('\n' + '='.repeat(60));
    console.log('📈 CORRECTED ASSIGNMENT SUMMARY');
    console.log('='.repeat(60));
    console.log(`📊 Total leads reviewed: ${corrections.analysis.total}`);
    console.log(`🔄 Assignments corrected: ${corrections.analysis.corrections}`);
    console.log(`✓ Already correct: ${corrections.noChange}`);
    console.log('');
    console.log(`🇮🇳 Indian phone numbers (+91/91): ${corrections.analysis.indianPhone}`);
    console.log(`🌍 Other (US/international/no phone): ${corrections.analysis.otherPhone}`);
    console.log('');
    console.log('👥 FINAL DISTRIBUTION:');
    console.log(`🇮🇳 Manish Tulasi: ${corrections.manish.leads.length} leads`);
    console.log(`🌍 Mahalakshmi Radhakrushnun: ${corrections.mahalakshmi.leads.length} leads`);
    console.log('');
    
    const manishPercentage = ((corrections.manish.leads.length / corrections.analysis.total) * 100).toFixed(1);
    const mahalakshmiPercentage = ((corrections.mahalakshmi.leads.length / corrections.analysis.total) * 100).toFixed(1);
    
    console.log('📊 CORRECTED DISTRIBUTION:');
    console.log(`Manish: ${manishPercentage}% (only +91/91 prefixes)`);
    console.log(`Mahalakshmi: ${mahalakshmiPercentage}% (US/international/no phone)`);
    console.log('');

    // Sample corrected assignments
    console.log('🔍 SAMPLE CORRECTED ASSIGNMENTS:');
    console.log('');
    console.log('Manish (Indian +91/91 only):');
    corrections.manish.leads.slice(0, 5).forEach(lead => {
      console.log(`  • ${lead.full_name} - ${lead.phone} - ${lead.email}`);
    });
    
    console.log('');
    console.log('Mahalakshmi (US/International/No phone):');
    corrections.mahalakshmi.leads.slice(0, 5).forEach(lead => {
      console.log(`  • ${lead.full_name} - ${lead.phone || 'No phone'} - ${lead.email}`);
    });

    console.log('\n✅ Assignment correction completed successfully!');

  } catch (error) {
    console.error('❌ Error during assignment correction:', error);
  } finally {
    db.close();
  }
}

// Run the correction
console.log('🚀 Starting lead assignment correction (only +91/91 → Manish)...\n');
fixLeadAssignments();