const { getDb } = require('../lib/db');

// Enhanced user model migration script
function migrateUserModel() {
  const db = getDb();
  
  console.log('Starting user model migration...');
  
  try {
    // Add subscription columns
    db.exec(`
      ALTER TABLE users ADD COLUMN subscription_tier TEXT DEFAULT 'free';
      ALTER TABLE users ADD COLUMN subscription_start_date DATETIME;
      ALTER TABLE users ADD COLUMN subscription_end_date DATETIME;
    `);
    console.log('✅ Added subscription columns');
  } catch (error) {
    if (error.message.includes('duplicate column name')) {
      console.log('⚠️ Subscription columns already exist');
    } else {
      throw error;
    }
  }

  try {
    // Add gamification columns
    db.exec(`
      ALTER TABLE users ADD COLUMN elo_rating INTEGER DEFAULT 1000;
      ALTER TABLE users ADD COLUMN skills_graph TEXT DEFAULT '{}';
      ALTER TABLE users ADD COLUMN total_challenges_completed INTEGER DEFAULT 0;
      ALTER TABLE users ADD COLUMN streak_count INTEGER DEFAULT 0;
      ALTER TABLE users ADD COLUMN last_active_date DATETIME;
    `);
    console.log('✅ Added gamification columns');
  } catch (error) {
    if (error.message.includes('duplicate column name')) {
      console.log('⚠️ Gamification columns already exist');
    } else {
      throw error;
    }
  }

  try {
    // Add email verification columns
    db.exec(`
      ALTER TABLE users ADD COLUMN email_verified INTEGER DEFAULT 0;
      ALTER TABLE users ADD COLUMN email_verification_token TEXT;
      ALTER TABLE users ADD COLUMN email_verification_sent_at DATETIME;
    `);
    console.log('✅ Added email verification columns');
  } catch (error) {
    if (error.message.includes('duplicate column name')) {
      console.log('⚠️ Email verification columns already exist');
    } else {
      throw error;
    }
  }

  try {
    // Add referral columns
    db.exec(`
      ALTER TABLE users ADD COLUMN referred_by INTEGER;
      ALTER TABLE users ADD COLUMN referral_code TEXT;
    `);
    console.log('✅ Added referral columns');
  } catch (error) {
    if (error.message.includes('duplicate column name')) {
      console.log('⚠️ Referral columns already exist');
    } else {
      throw error;
    }
  }

  // Create new tables for enhanced functionality
  try {
    // Payment history table
    db.exec(`
      CREATE TABLE IF NOT EXISTS payment_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        subscription_tier TEXT NOT NULL,
        payment_method TEXT NOT NULL,
        transaction_id TEXT NOT NULL UNIQUE,
        status TEXT NOT NULL DEFAULT 'succeeded',
        FOREIGN KEY (user_id) REFERENCES users(id)
      );
    `);
    console.log('✅ Created payment_history table');

    // Workshops table
    db.exec(`
      CREATE TABLE IF NOT EXISTS workshops (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        start_date DATETIME NOT NULL,
        end_date DATETIME NOT NULL,
        syllabus_link TEXT,
        geography TEXT,
        payment_link TEXT,
        cost REAL,
        max_participants INTEGER,
        is_active INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Created workshops table');

    // Workshop participants table
    db.exec(`
      CREATE TABLE IF NOT EXISTS workshop_participants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        workshop_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        enrollment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        payment_status TEXT DEFAULT 'pending',
        FOREIGN KEY (workshop_id) REFERENCES workshops(id),
        FOREIGN KEY (user_id) REFERENCES users(id),
        UNIQUE(workshop_id, user_id)
      );
    `);
    console.log('✅ Created workshop_participants table');

    // Lessons table
    db.exec(`
      CREATE TABLE IF NOT EXISTS lessons (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        category TEXT,
        order_index INTEGER NOT NULL,
        content_type TEXT DEFAULT 'text',
        content_url TEXT,
        text_content TEXT,
        llm_prompt TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Created lessons table');

    // User progress table
    db.exec(`
      CREATE TABLE IF NOT EXISTS user_progress (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        lesson_id INTEGER NOT NULL,
        completed INTEGER DEFAULT 0,
        started_at DATETIME,
        completed_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (lesson_id) REFERENCES lessons(id),
        UNIQUE(user_id, lesson_id)
      );
    `);
    console.log('✅ Created user_progress table');

    // ELO history table
    db.exec(`
      CREATE TABLE IF NOT EXISTS elo_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        previous_rating INTEGER NOT NULL,
        new_rating INTEGER NOT NULL,
        change INTEGER NOT NULL,
        reason TEXT NOT NULL,
        opponent_id INTEGER,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (opponent_id) REFERENCES users(id)
      );
    `);
    console.log('✅ Created elo_history table');

    // Referrals table
    db.exec(`
      CREATE TABLE IF NOT EXISTS referrals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        referrer_id INTEGER NOT NULL,
        referred_id INTEGER NOT NULL,
        referral_code TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        referrer_reward_given INTEGER DEFAULT 0,
        referred_reward_given INTEGER DEFAULT 0,
        reward_type TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (referrer_id) REFERENCES users(id),
        FOREIGN KEY (referred_id) REFERENCES users(id)
      );
    `);
    console.log('✅ Created referrals table');

  } catch (error) {
    console.error('Error creating tables:', error);
    throw error;
  }

  console.log('🎉 User model migration completed successfully!');
}

// Run migration if called directly
if (require.main === module) {
  try {
    migrateUserModel();
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

module.exports = { migrateUserModel };