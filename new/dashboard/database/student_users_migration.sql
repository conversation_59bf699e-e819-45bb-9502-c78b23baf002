-- ============================================================================
-- Student Users Migration - Separate Learning Platform Access
-- Purpose: Create student user tables separate from staff users
-- Date: August 11, 2025
-- ============================================================================

-- ============================================================================
-- STUDENT_USERS - Learning platform access for paying customers
-- ============================================================================
CREATE TABLE student_users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    
    -- Authentication
    email TEXT NOT NULL UNIQUE,           -- Learning platform login email
    password_hash TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    
    -- Profile Information
    display_name TEXT,                    -- How they appear in platform
    profile_image_url TEXT,
    timezone TEXT DEFAULT 'UTC',
    preferred_language TEXT DEFAULT 'en',
    
    -- Subscription & Access
    subscription_plan TEXT CHECK (subscription_plan IN (
        'free',                           -- Free tier (limited access)
        'basic',                          -- Basic monthly ($30)
        'premium',                        -- Premium quarterly ($500)
        'team',                           -- Team plan (custom)
        'lifetime'                        -- Lifetime access
    )) DEFAULT 'free',
    subscription_status TEXT DEFAULT 'inactive' CHECK (subscription_status IN (
        'inactive',                       -- No active subscription
        'active',                         -- Currently subscribed
        'paused',                         -- Temporarily paused
        'cancelled',                      -- Cancelled but still in period
        'expired'                         -- Subscription expired
    )),
    subscription_start_date DATE,
    subscription_end_date DATE,
    access_level TEXT DEFAULT 'basic' CHECK (access_level IN (
        'basic',                          -- Workshop materials only
        'standard',                       -- + Practice exercises
        'premium',                        -- + Live sessions, 1:1 coaching
        'vip'                            -- + Everything, priority support
    )),
    
    -- Learning Preferences
    learning_preferences TEXT,            -- JSON: pace, notification_settings, etc.
    notification_preferences TEXT,        -- JSON: email, SMS, push preferences
    
    -- Progress Tracking
    total_courses_enrolled INTEGER DEFAULT 0,
    total_courses_completed INTEGER DEFAULT 0,
    total_certificates_earned INTEGER DEFAULT 0,
    total_learning_hours DECIMAL(8,2) DEFAULT 0,
    current_streak_days INTEGER DEFAULT 0,
    longest_streak_days INTEGER DEFAULT 0,
    
    -- Engagement Metrics
    last_login DATETIME,
    last_activity DATETIME,
    login_count INTEGER DEFAULT 0,
    total_sessions INTEGER DEFAULT 0,
    avg_session_duration_minutes DECIMAL(8,2) DEFAULT 0,
    
    -- Platform Features
    ai_assistant_enabled BOOLEAN DEFAULT TRUE,
    code_execution_enabled BOOLEAN DEFAULT TRUE,
    collaboration_enabled BOOLEAN DEFAULT TRUE,
    mentorship_enabled BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- ============================================================================
-- COURSES - Available courses/modules in learning platform
-- ============================================================================
CREATE TABLE courses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Course Identity
    course_code TEXT UNIQUE NOT NULL,     -- 'AI_ESS_001', 'AI_PRAC_002'
    course_title TEXT NOT NULL,           -- 'AI Essentials for Executives'
    course_subtitle TEXT,
    course_description TEXT,
    course_objectives TEXT,               -- JSON array of learning objectives
    
    -- Course Structure
    course_type TEXT CHECK (course_type IN (
        'workshop',                       -- Live workshop (existing)
        'self_paced',                     -- Self-paced online course
        'cohort',                         -- Cohort-based course
        'masterclass',                    -- Advanced masterclass
        'certification'                   -- Certification program
    )) DEFAULT 'self_paced',
    difficulty_level TEXT CHECK (difficulty_level IN (
        'beginner',
        'intermediate', 
        'advanced',
        'expert'
    )) DEFAULT 'beginner',
    
    -- Access & Prerequisites
    access_level_required TEXT DEFAULT 'basic',
    prerequisite_courses TEXT,            -- JSON array of course IDs
    estimated_hours INTEGER,              -- Total learning time
    
    -- Content Organization
    total_modules INTEGER DEFAULT 0,
    total_lessons INTEGER DEFAULT 0,
    total_exercises INTEGER DEFAULT 0,
    total_assessments INTEGER DEFAULT 0,
    
    -- Pricing (if sold separately)
    price_usd DECIMAL(10,2),
    price_inr DECIMAL(10,2),
    
    -- Status
    status TEXT DEFAULT 'draft' CHECK (status IN (
        'draft',                          -- Under development
        'published',                      -- Available to students
        'archived',                       -- No longer available
        'coming_soon'                     -- Announced but not ready
    )),
    
    -- SEO & Marketing
    seo_title TEXT,
    seo_description TEXT,
    featured_image_url TEXT,
    promotional_video_url TEXT,
    
    -- Instructor Information
    instructor_id INTEGER,               -- FK to users table (staff)
    instructor_bio TEXT,
    guest_instructors TEXT,              -- JSON array if multiple
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    published_at DATETIME,
    
    FOREIGN KEY (instructor_id) REFERENCES users(id)
);

-- ============================================================================  
-- STUDENT_PROGRESS - Detailed progress tracking per student per course
-- ============================================================================
CREATE TABLE student_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_user_id INTEGER NOT NULL,
    course_id INTEGER NOT NULL,
    
    -- Enrollment Details
    enrollment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    enrollment_source TEXT DEFAULT 'direct' CHECK (enrollment_source IN (
        'direct',                         -- Direct course purchase
        'subscription',                   -- Included in subscription
        'workshop_upgrade',               -- Upgraded from workshop
        'bundle',                         -- Part of course bundle
        'complimentary'                   -- Free access granted
    )),
    
    -- Progress Tracking
    status TEXT DEFAULT 'enrolled' CHECK (status IN (
        'enrolled',                       -- Just enrolled
        'in_progress',                    -- Actively learning
        'completed',                      -- Finished all requirements
        'paused',                         -- Temporarily paused
        'dropped'                         -- Discontinued
    )),
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    
    -- Module Progress (JSON structure)
    modules_completed TEXT,               -- JSON array of completed module IDs
    lessons_completed TEXT,               -- JSON array of completed lesson IDs  
    exercises_completed TEXT,             -- JSON array of completed exercise IDs
    assessments_completed TEXT,           -- JSON array of completed assessment IDs
    
    -- Time Tracking
    total_time_spent_minutes INTEGER DEFAULT 0,
    last_accessed_at DATETIME,
    estimated_completion_date DATE,
    actual_completion_date DATETIME,
    
    -- Performance Metrics
    average_quiz_score DECIMAL(5,2),
    total_quiz_attempts INTEGER DEFAULT 0,
    code_exercises_passed INTEGER DEFAULT 0,
    code_exercises_attempted INTEGER DEFAULT 0,
    
    -- Engagement Data
    notes_count INTEGER DEFAULT 0,
    bookmarks_count INTEGER DEFAULT 0,
    discussion_posts INTEGER DEFAULT 0,
    peer_interactions INTEGER DEFAULT 0,
    
    -- Certificate & Recognition
    certificate_earned BOOLEAN DEFAULT FALSE,
    certificate_issued_date DATETIME,
    certificate_file_path TEXT,
    final_grade DECIMAL(5,2),
    
    -- Feedback
    course_rating INTEGER CHECK (course_rating BETWEEN 1 AND 5),
    course_review TEXT,
    instructor_rating INTEGER CHECK (instructor_rating BETWEEN 1 AND 5),
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (student_user_id) REFERENCES student_users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE(student_user_id, course_id)
);

-- ============================================================================
-- STUDENT_SESSIONS - Track individual learning sessions
-- ============================================================================
CREATE TABLE student_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_user_id INTEGER NOT NULL,
    course_id INTEGER,                    -- NULL for general platform usage
    
    -- Session Details
    session_start DATETIME NOT NULL,
    session_end DATETIME,
    duration_minutes INTEGER,             -- Calculated on session end
    device_type TEXT,                     -- desktop/mobile/tablet
    browser_info TEXT,
    ip_address TEXT,
    
    -- Activity During Session
    pages_visited INTEGER DEFAULT 0,
    videos_watched INTEGER DEFAULT 0,
    exercises_attempted INTEGER DEFAULT 0,
    notes_created INTEGER DEFAULT 0,
    downloads INTEGER DEFAULT 0,
    
    -- Session Quality Metrics
    engagement_score DECIMAL(5,2),        -- Calculated engagement metric
    idle_time_minutes INTEGER DEFAULT 0,
    active_time_minutes INTEGER DEFAULT 0,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (student_user_id) REFERENCES student_users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL
);

-- ============================================================================
-- INDEXES for Performance
-- ============================================================================

-- Student Users
CREATE INDEX idx_student_users_customer ON student_users(customer_id);
CREATE INDEX idx_student_users_email ON student_users(email);
CREATE INDEX idx_student_users_subscription ON student_users(subscription_status, subscription_plan);
CREATE INDEX idx_student_users_active ON student_users(is_active, last_login);

-- Courses
CREATE INDEX idx_courses_code ON courses(course_code);
CREATE INDEX idx_courses_type_status ON courses(course_type, status);
CREATE INDEX idx_courses_instructor ON courses(instructor_id);

-- Student Progress
CREATE INDEX idx_progress_student ON student_progress(student_user_id);
CREATE INDEX idx_progress_course ON student_progress(course_id);
CREATE INDEX idx_progress_status ON student_progress(status, completion_percentage);
CREATE INDEX idx_progress_completion ON student_progress(actual_completion_date);

-- Student Sessions
CREATE INDEX idx_sessions_student ON student_sessions(student_user_id);
CREATE INDEX idx_sessions_course ON student_sessions(course_id);
CREATE INDEX idx_sessions_date ON student_sessions(session_start);

-- ============================================================================
-- TRIGGERS for Data Integrity and Automation
-- ============================================================================

-- Update student progress completion percentage
CREATE TRIGGER calculate_completion_percentage
AFTER UPDATE OF modules_completed, lessons_completed ON student_progress
BEGIN
    -- This would need custom logic based on course structure
    -- For now, just update the updated_at timestamp
    UPDATE student_progress 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- Update student user totals when progress changes
CREATE TRIGGER update_student_totals
AFTER UPDATE OF status ON student_progress
WHEN NEW.status = 'completed' AND OLD.status != 'completed'
BEGIN
    UPDATE student_users
    SET 
        total_courses_completed = total_courses_completed + 1,
        total_certificates_earned = CASE 
            WHEN NEW.certificate_earned THEN total_certificates_earned + 1 
            ELSE total_certificates_earned 
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.student_user_id;
END;

-- Calculate session duration on session end
CREATE TRIGGER calculate_session_duration
AFTER UPDATE OF session_end ON student_sessions
WHEN NEW.session_end IS NOT NULL AND OLD.session_end IS NULL
BEGIN
    UPDATE student_sessions
    SET duration_minutes = CAST(
        (julianday(NEW.session_end) - julianday(NEW.session_start)) * 24 * 60 AS INTEGER
    )
    WHERE id = NEW.id;
END;

-- ============================================================================
-- VIEWS for Common Student Queries
-- ============================================================================

-- Active student dashboard
CREATE VIEW active_students_dashboard AS
SELECT 
    su.id,
    su.email,
    su.display_name,
    c.customer_email,
    su.subscription_plan,
    su.subscription_status,
    su.total_courses_enrolled,
    su.total_courses_completed,
    su.total_certificates_earned,
    su.last_login,
    COUNT(sp.id) as active_courses,
    AVG(sp.completion_percentage) as avg_progress
FROM student_users su
JOIN customers c ON su.customer_id = c.id
LEFT JOIN student_progress sp ON su.id = sp.student_user_id AND sp.status = 'in_progress'
WHERE su.is_active = TRUE
GROUP BY su.id;

-- Course enrollment summary
CREATE VIEW course_enrollment_summary AS
SELECT 
    co.id,
    co.course_title,
    co.course_type,
    co.status,
    COUNT(sp.id) as total_enrollments,
    COUNT(CASE WHEN sp.status = 'completed' THEN 1 END) as completions,
    AVG(sp.completion_percentage) as avg_progress,
    AVG(sp.course_rating) as avg_rating,
    COUNT(CASE WHEN sp.certificate_earned THEN 1 END) as certificates_issued
FROM courses co
LEFT JOIN student_progress sp ON co.id = sp.course_id
GROUP BY co.id;

-- Student learning analytics
CREATE VIEW student_learning_analytics AS
SELECT 
    su.id as student_id,
    su.email,
    c.customer_email,
    c.total_spent_usd,
    su.total_learning_hours,
    su.current_streak_days,
    su.login_count,
    COUNT(sp.id) as courses_enrolled,
    COUNT(CASE WHEN sp.status = 'completed' THEN 1 END) as courses_completed,
    AVG(sp.course_rating) as avg_course_rating,
    MAX(ss.session_start) as last_activity
FROM student_users su
JOIN customers c ON su.customer_id = c.id
LEFT JOIN student_progress sp ON su.id = sp.student_user_id
LEFT JOIN student_sessions ss ON su.id = ss.student_user_id
GROUP BY su.id;

-- ============================================================================
-- INITIAL DATA - Sample courses
-- ============================================================================

-- Insert sample courses based on existing workshops
INSERT INTO courses (course_code, course_title, course_type, difficulty_level, estimated_hours, status, instructor_id) VALUES
('AI_ESS_001', 'AI Essentials for Executives', 'self_paced', 'beginner', 8, 'published', 1),
('AI_PRAC_002', 'AI Practitioner Bootcamp', 'cohort', 'intermediate', 24, 'published', 1),
('AGENT_AI_003', 'Agentic AI Development', 'self_paced', 'advanced', 16, 'published', 1),
('VIBE_CODE_004', 'Vibe Coding Fundamentals', 'self_paced', 'beginner', 12, 'published', 1),
('AI_PM_005', 'AI for Product Managers', 'cohort', 'intermediate', 10, 'published', 1),
('AI_UX_006', 'AI for UX Designers', 'self_paced', 'intermediate', 8, 'published', 1);

-- ============================================================================
-- Migration Notes
-- ============================================================================

-- This migration creates the foundation for the learning platform with:
-- 1. Separate student user accounts (linked to customers)
-- 2. Course catalog and structure
-- 3. Detailed progress tracking per student per course
-- 4. Session tracking for engagement analytics
-- 5. Performance views for dashboards

-- Next steps after migration:
-- 1. Create admin interface for course management
-- 2. Build student dashboard for progress tracking  
-- 3. Implement learning path recommendations
-- 4. Add gamification features (badges, leaderboards)
-- 5. Integration with video hosting and assessment tools