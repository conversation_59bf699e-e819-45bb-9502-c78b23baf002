// Custom hook to manage timeline-specific data
export function useTimelineData(timeline) {
  // Mock data - in real app, this would fetch from API based on timeline
  const getMetricsForTimeline = (period) => {
    const data = {
      today: {
        closures: { value: '3', trend: '+50%', trendType: 'positive' },
        revenue: { value: '₹85K', trend: '+12%', trendType: 'positive' },
        adSpend: { value: '₹4.2K', trend: '+5%', trendType: 'negative' },
        coca: { value: '₹3,200', trend: '+15%', trendType: 'negative' },
        cpl: { value: '₹334', trend: '+100%', trendType: 'negative' },
        
        pipelineValue: { value: '₹12.1L', trend: '+8%', trendType: 'positive' },
        nps: { value: '8.9', trend: '+0.2', trendType: 'positive' },
        referrals: { value: '12', trend: '+200%', trendType: 'positive' },
        ltvcac: { value: '3.4:1', trend: '+0.2', trendType: 'positive' },
        payback: { value: '12 days', trend: '-2d', trendType: 'positive' },
        
        kapiPipeline: { value: '₹15K', trend: '+25%', trendType: 'positive' },
        multiCourse: { value: '35%', trend: '+7%', trendType: 'positive' },
        optimization: { value: '₹2.1L', trend: 'New', trendType: 'neutral' }
      },
      '7d': {
        closures: { value: '18', trend: '+25%', trendType: 'positive' },
        revenue: { value: '₹4.2L', trend: '+18%', trendType: 'positive' },
        adSpend: { value: '₹28K', trend: '+8%', trendType: 'negative' },
        coca: { value: '₹2,950', trend: '+5%', trendType: 'negative' },
        cpl: { value: '₹167', trend: '-12%', trendType: 'positive' },
        
        pipelineValue: { value: '₹28.5L', trend: '+15%', trendType: 'positive' },
        nps: { value: '8.5', trend: '-0.2', trendType: 'negative' },
        referrals: { value: '45', trend: '+80%', trendType: 'positive' },
        ltvcac: { value: '3.1:1', trend: '-0.1', trendType: 'negative' },
        payback: { value: '16 days', trend: '+2d', trendType: 'negative' },
        
        kapiPipeline: { value: '₹45K', trend: '+50%', trendType: 'positive' },
        multiCourse: { value: '31%', trend: '+3%', trendType: 'positive' },
        optimization: { value: '₹5.8L', trend: '+38%', trendType: 'positive' }
      },
      '30d': {
        closures: { value: '61', trend: '+18%', trendType: 'positive' },
        revenue: { value: '₹18.3L', trend: '+22%', trendType: 'positive' },
        adSpend: { value: '₹1.71L', trend: '+15%', trendType: 'negative' },
        coca: { value: '₹2,806', trend: '-8%', trendType: 'positive' },
        cpl: { value: '₹143', trend: '-15%', trendType: 'positive' },
        
        pipelineValue: { value: '₹45.2L', trend: '+28%', trendType: 'positive' },
        nps: { value: '8.7', trend: '+0.4', trendType: 'positive' },
        referrals: { value: '124', trend: '+65%', trendType: 'positive' },
        ltvcac: { value: '3.2:1', trend: '+0.3', trendType: 'positive' },
        payback: { value: '14 days', trend: '-6d', trendType: 'positive' },
        
        kapiPipeline: { value: '₹90K', trend: '+80%', trendType: 'positive' },
        multiCourse: { value: '28%', trend: '+12%', trendType: 'positive' },
        optimization: { value: '₹8.1L', trend: '+65%', trendType: 'positive' }
      },
      '90d': {
        closures: { value: '156', trend: '+35%', trendType: 'positive' },
        revenue: { value: '₹48.7L', trend: '+42%', trendType: 'positive' },
        adSpend: { value: '₹7.8L', trend: '+20%', trendType: 'negative' },
        coca: { value: '₹2,650', trend: '-18%', trendType: 'positive' },
        cpl: { value: '₹128', trend: '-25%', trendType: 'positive' },
        
        pipelineValue: { value: '₹125L', trend: '+45%', trendType: 'positive' },
        nps: { value: '8.9', trend: '+1.2', trendType: 'positive' },
        referrals: { value: '285', trend: '+95%', trendType: 'positive' },
        ltvcac: { value: '3.8:1', trend: '+0.8', trendType: 'positive' },
        payback: { value: '11 days', trend: '-12d', trendType: 'positive' },
        
        kapiPipeline: { value: '₹180K', trend: '+120%', trendType: 'positive' },
        multiCourse: { value: '32%', trend: '+18%', trendType: 'positive' },
        optimization: { value: '₹15.2L', trend: '+85%', trendType: 'positive' }
      },
      all: {
        closures: { value: '312', trend: '+48%', trendType: 'positive' },
        revenue: { value: '₹1.2Cr', trend: '+55%', trendType: 'positive' },
        adSpend: { value: '₹22.5L', trend: '+35%', trendType: 'negative' },
        coca: { value: '₹2,480', trend: '-28%', trendType: 'positive' },
        cpl: { value: '₹118', trend: '-32%', trendType: 'positive' },
        
        pipelineValue: { value: '₹285L', trend: '+68%', trendType: 'positive' },
        nps: { value: '9.1', trend: '+1.8', trendType: 'positive' },
        referrals: { value: '642', trend: '+180%', trendType: 'positive' },
        ltvcac: { value: '4.2:1', trend: '+1.2', trendType: 'positive' },
        payback: { value: '9 days', trend: '-18d', trendType: 'positive' },
        
        kapiPipeline: { value: '₹450K', trend: '+250%', trendType: 'positive' },
        multiCourse: { value: '38%', trend: '+25%', trendType: 'positive' },
        optimization: { value: '₹28.5L', trend: '+150%', trendType: 'positive' }
      }
    };

    return data[period] || data['30d'];
  };

  const metrics = getMetricsForTimeline(timeline);

  // Format into the structure expected by components
  const keyBusinessMetrics = [
    { 
      value: metrics.closures.value, 
      label: '🎯 Closures',
      subtext: `${metrics.closures.trend} vs prev period`,
      color: metrics.closures.trendType === 'positive' ? 'text-green-600' : 'text-red-600'
    },
    { 
      value: metrics.revenue.value, 
      label: '💰 Revenue',
      subtext: `${metrics.revenue.trend} growth`,
      color: metrics.revenue.trendType === 'positive' ? 'text-green-600' : 'text-red-600'
    },
    { 
      value: metrics.adSpend.value, 
      label: '📊 Ad Spend',
      subtext: `${metrics.adSpend.trend} vs prev period`,
      color: metrics.adSpend.trendType === 'positive' ? 'text-red-600' : 'text-green-600'
    },
    { 
      value: metrics.coca.value, 
      label: '🎯 COCA',
      subtext: `${metrics.coca.trend} vs prev period`,
      color: metrics.coca.trendType === 'positive' ? 'text-green-600' : 'text-orange-600'
    }
  ];

  const pipelineMetrics = [
    { 
      value: metrics.pipelineValue.value, 
      label: '📈 Pipeline Value',
      subtext: `${metrics.pipelineValue.trend} growth`,
      color: 'text-purple-600'
    },
    { 
      value: metrics.nps.value, 
      label: '⭐ NPS Score',
      subtext: `${metrics.nps.trend} improvement`,
      color: metrics.nps.trendType === 'positive' ? 'text-green-600' : 'text-red-600'
    },
    { 
      value: metrics.referrals.value, 
      label: '🔗 Referrals',
      subtext: `${metrics.referrals.trend} growth`,
      color: metrics.referrals.trendType === 'positive' ? 'text-green-600' : 'text-red-600'
    },
    { 
      value: metrics.ltvcac.value, 
      label: '📊 LTV:CAC',
      subtext: `${metrics.ltvcac.trend} improvement`,
      color: metrics.ltvcac.trendType === 'positive' ? 'text-green-600' : 'text-red-600'
    }
  ];

  const opportunityMetrics = [
    { 
      value: metrics.kapiPipeline.value, 
      label: '🏢 Kapi Pipeline',
      subtext: `${metrics.kapiPipeline.trend} growth`,
      color: 'text-indigo-600'
    },
    { 
      value: metrics.multiCourse.value, 
      label: '🔄 Multi-Course Rate',
      subtext: `${metrics.multiCourse.trend} improvement`,
      color: 'text-teal-600'
    },
    { 
      value: metrics.optimization.value, 
      label: '💡 Optimization Potential',
      subtext: metrics.optimization.trend === 'New' ? 'Available now' : `${metrics.optimization.trend} potential`,
      color: 'text-red-600'
    }
  ];

  return {
    keyBusinessMetrics,
    pipelineMetrics,
    opportunityMetrics
  };
}