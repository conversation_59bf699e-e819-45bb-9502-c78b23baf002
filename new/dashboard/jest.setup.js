import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn().mockResolvedValue(undefined),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
      isFallback: false,
      isLocaleDomain: false,
      isReady: true,
      defaultLocale: 'en',
      domainLocales: [],
      isPreview: false,
    }
  },
}))

// Mock environment variables
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = '__tests__/fixtures/test.db'

// Global test utilities
global.mockApiRequest = (handler) => {
  return {
    json: jest.fn(),
    status: jest.fn(() => ({ json: jest.fn() })),
    setHeader: jest.fn(),
  }
}

// Mock database for tests
jest.mock('./lib/dbConfig.js', () => ({
  getDatabasePath: jest.fn(() => '__tests__/fixtures/test.db'),
}))

// Mock sqlite3 for authentication middleware
jest.mock('sqlite3', () => ({
  Database: jest.fn().mockImplementation(() => ({
    get: jest.fn((query, params, callback) => {
      // Mock user data for authentication tests
      const mockUser = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        role: 'admin',
        is_active: 1
      }
      // Use nextTick to avoid synchronous execution issues
      process.nextTick(() => callback(null, mockUser))
    }),
    all: jest.fn((query, params, callback) => {
      // Default empty response
      process.nextTick(() => callback(null, []))
    }),
    run: jest.fn((query, params, callback) => {
      const result = { lastID: 1, changes: 1 }
      process.nextTick(() => callback.call(result, null))
    }),
    close: jest.fn(() => {
      process.nextTick(() => {})
    }),
  })),
}))

// Mock sqlite (used by some API endpoints)
jest.mock('sqlite', () => ({
  open: jest.fn(() => Promise.resolve({
    get: jest.fn(),
    all: jest.fn(),
    run: jest.fn(),
    close: jest.fn(),
  })),
}))

// Mock fetch for components
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
  })
)