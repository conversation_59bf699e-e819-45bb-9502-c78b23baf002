// Client-side JWT token management utilities

class ClientTokenManager {
  constructor() {
    this.tokenKey = 'mai_auth_token';
    this.userKey = 'mai_user_data';
  }

  // Get token from localStorage
  getToken() {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.tokenKey);
  }

  // Store token in localStorage
  setToken(token) {
    console.log('🔐 setToken called with:', token ? token.substring(0, 20) + '...' : 'null/undefined');
    if (typeof window === 'undefined') {
      console.log('❌ Window is undefined, cannot store token');
      return;
    }
    console.log('💾 Storing token with key:', this.tokenKey);
    localStorage.setItem(this.tokenKey, token);
    
    // Verify it was stored
    const stored = localStorage.getItem(this.tokenKey);
    console.log('✅ Token stored verification:', !!stored);
  }

  // Remove token from localStorage
  removeToken() {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
  }

  // Get user data from localStorage
  getUser() {
    if (typeof window === 'undefined') return null;
    const userData = localStorage.getItem(this.userKey);
    return userData ? JSON.parse(userData) : null;
  }

  // Store user data in localStorage
  setUser(user) {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.userKey, JSON.stringify(user));
  }

  // Check if token exists and not expired
  isAuthenticated() {
    const token = this.getToken();
    if (!token) {
      console.log('No token found');
      return false;
    }

    try {
      // Basic JWT structure check
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      
      console.log('Token payload:', payload);
      console.log('Current time:', currentTime);
      console.log('Token expires:', payload.exp);
      console.log('Is expired?', payload.exp <= currentTime);
      
      // Check if token is expired
      return payload.exp > currentTime;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  // Create Authorization header for API calls
  getAuthHeader() {
    const token = this.getToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  // Make authenticated API request
  async authenticatedFetch(url, options = {}) {
    const token = this.getToken();
    
    if (!token) {
      throw new Error('No authentication token available');
    }

    const authHeaders = {
      'Content-Type': 'application/json',
      ...this.getAuthHeader(),
      ...options.headers
    };

    const response = await fetch(url, {
      ...options,
      headers: authHeaders
    });

    // If unauthorized, clear token and redirect to login
    if (response.status === 401) {
      console.warn('Authentication failed (401), checking token validity...');
      
      // Double-check if the token is actually expired before logging out
      if (!this.isAuthenticated()) {
        console.warn('Token is expired or invalid, clearing tokens and redirecting');
        this.removeToken();
        
        // Redirect to login if in browser
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      } else {
        console.warn('Token appears valid but server rejected it - possible server issue');
      }
      
      throw new Error('Authentication failed');
    }

    return response;
  }
}

// Create singleton instance
const clientAuth = new ClientTokenManager();

// Export both the class and instance
export default clientAuth;
export { ClientTokenManager };

// Convenience functions
export const getToken = () => clientAuth.getToken();
export const setToken = (token) => clientAuth.setToken(token);
export const removeToken = () => clientAuth.removeToken();
export const getUser = () => clientAuth.getUser();
export const setUser = (user) => clientAuth.setUser(user);
export const isAuthenticated = () => clientAuth.isAuthenticated();
export const getAuthHeader = () => clientAuth.getAuthHeader();
export const authenticatedFetch = (url, options) => clientAuth.authenticatedFetch(url, options);