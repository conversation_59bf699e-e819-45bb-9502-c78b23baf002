/**
 * Authentication & Authorization Middleware
 * Protects sensitive API endpoints from unauthorized access
 */

const jwt = require('jsonwebtoken')
const { getDb } = require('../db')

// JWT Secret - REQUIRED environment variable
console.log('🛡️ [MIDDLEWARE] Loading JWT_SECRET from environment...');
console.log('🔍 [MIDDLEWARE] process.env.JWT_SECRET exists:', !!process.env.JWT_SECRET);
console.log('🔑 [MIDDLEWARE] JWT_SECRET length:', process.env.JWT_SECRET ? process.env.JWT_SECRET.length : 'undefined');
console.log('🔑 [MIDDLEWARE] JWT_SECRET preview:', process.env.JWT_SECRET ? process.env.JWT_SECRET.substring(0, 20) + '...' : 'NOT SET');

const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  throw new Error('JWT_SECRET environment variable is required but not found. Check your .env file configuration.');
}

/**
 * Authentication middleware - verifies JWT token
 */
function requireAuth(handler) {
  return async (req, res) => {
    try {
      // Check for Authorization header
      const authHeader = req.headers.authorization
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Valid authentication token required',
          code: 'MISSING_TOKEN'
        })
      }

      // Extract and verify token
      const token = authHeader.substring(7) // Remove 'Bearer '
      const decoded = jwt.verify(token, JWT_SECRET)
      
      // Get user details from database - using decoded.id instead of decoded.userId
      const user = await getUserById(decoded.id)
      if (!user || !user.is_active) {
        return res.status(401).json({
          error: 'Unauthorized', 
          message: 'Invalid or expired token',
          code: 'INVALID_TOKEN'
        })
      }

      // Attach user to request
      req.user = user
      req.userId = user.id
      req.userRole = user.role

      // Log access attempt
      logAccess(req, user)

      return handler(req, res)
      
    } catch (error) {
      if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid token format',
          code: 'MALFORMED_TOKEN'
        })
      }
      
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Token has expired',
          code: 'EXPIRED_TOKEN'
        })
      }

      console.error('Auth middleware error:', error)
      return res.status(500).json({
        error: 'Authentication Error',
        message: 'Failed to validate authentication'
      })
    }
  }
}

/**
 * Role-based authorization middleware
 */
function requireRole(...allowedRoles) {
  return function(handler) {
    return requireAuth(async (req, res) => {
      if (!allowedRoles.includes(req.userRole)) {
        return res.status(403).json({
          error: 'Forbidden',
          message: `Access denied. Required roles: ${allowedRoles.join(', ')}`,
          code: 'INSUFFICIENT_PERMISSIONS',
          userRole: req.userRole,
          requiredRoles: allowedRoles
        })
      }

      return handler(req, res)
    })
  }
}

/**
 * Data access authorization - restrict sensitive data
 */
function requireDataAccess(dataType = 'general') {
  const accessLevels = {
    'leads': ['admin', 'sales', 'sales_rep'],
    'customers': ['admin', 'sales', 'sales_rep'],
    'financial': ['admin'],
    'facebook': ['admin', 'sales'],
    'analytics': ['admin', 'sales'],
    'general': ['admin', 'sales', 'sales_rep', 'user']
  }

  const allowedRoles = accessLevels[dataType] || ['admin']
  
  return requireRole(...allowedRoles)
}

/**
 * Rate limiting middleware
 */
const rateLimitStore = new Map()

// Get rate limiting configuration from environment variables
const DEFAULT_MAX_REQUESTS = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 500;
const DEFAULT_WINDOW_MS = parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 60 * 60 * 1000; // 1 hour

console.log('🚦 [RATE LIMIT] Configuration:', {
  maxRequests: DEFAULT_MAX_REQUESTS,
  windowMs: DEFAULT_WINDOW_MS,
  windowMinutes: Math.round(DEFAULT_WINDOW_MS / (60 * 1000))
});

function rateLimit(maxRequests = DEFAULT_MAX_REQUESTS, windowMs = DEFAULT_WINDOW_MS) {
  return function(handler) {
    return async (req, res) => {
      const key = req.ip || req.connection.remoteAddress || 'unknown'
      const now = Date.now()
      const windowStart = now - windowMs

      // Clean old entries
      const userRequests = rateLimitStore.get(key) || []
      const validRequests = userRequests.filter(timestamp => timestamp > windowStart)

      if (validRequests.length >= maxRequests) {
        return res.status(429).json({
          error: 'Too Many Requests',
          message: `Rate limit exceeded. Max ${maxRequests} requests per ${windowMs/1000/60} minutes`,
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: Math.ceil((validRequests[0] + windowMs - now) / 1000)
        })
      }

      // Add current request
      validRequests.push(now)
      rateLimitStore.set(key, validRequests)

      return handler(req, res)
    }
  }
}

/**
 * GDPR compliance - sanitize sensitive data
 */
function sanitizeGDPRData(data, userRole = 'user') {
  if (!data) return data

  const isAuthorized = ['admin', 'sales', 'sales_rep'].includes(userRole)
  
  if (Array.isArray(data)) {
    return data.map(item => sanitizeGDPRData(item, userRole))
  }

  if (typeof data === 'object') {
    const sanitized = { ...data }
    
    // Remove/mask sensitive fields for unauthorized users
    if (!isAuthorized) {
      // Mask email addresses
      if (sanitized.email) {
        sanitized.email = maskEmail(sanitized.email)
      }
      
      // Mask phone numbers
      if (sanitized.phone) {
        sanitized.phone = maskPhone(sanitized.phone)
      }
      
      // Remove personal identifiers
      delete sanitized.full_name
      delete sanitized.lead_id
      delete sanitized.facebook_lead_id
      
      // Remove financial data
      delete sanitized.payment_amount
      delete sanitized.total_spent_stripe
    }
    
    return sanitized
  }

  return data
}

// Helper functions
async function getUserById(userId) {
  try {
    const db = getDb()
    const user = db.prepare('SELECT id, username, email, first_name, last_name, role, is_active FROM users WHERE id = ?').get(userId)
    return user
  } catch (error) {
    console.error('Error fetching user by ID:', error)
    return null
  }
}

function maskEmail(email) {
  const [local, domain] = email.split('@')
  const maskedLocal = local.charAt(0) + '*'.repeat(local.length - 2) + local.charAt(local.length - 1)
  return `${maskedLocal}@${domain}`
}

function maskPhone(phone) {
  if (!phone) return phone
  return phone.replace(/\d(?=\d{4})/g, '*')
}

function logAccess(req, user) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    userId: user.id,
    username: user.username,
    role: user.role,
    endpoint: req.url,
    method: req.method,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.headers['user-agent']
  }
  
  // In production, log to secure audit system
  console.log('API Access:', JSON.stringify(logEntry))
}

// Helper function for standard rate limiting (uses env config)
const standardRateLimit = () => rateLimit();

// Helper function for high-frequency operations (2x standard)
const highFrequencyRateLimit = () => rateLimit(DEFAULT_MAX_REQUESTS * 2);

// Helper function for low-frequency operations (half standard)  
const lowFrequencyRateLimit = () => rateLimit(Math.floor(DEFAULT_MAX_REQUESTS / 2));

module.exports = {
  requireAuth,
  requireRole,
  requireDataAccess,
  rateLimit,
  standardRateLimit,
  highFrequencyRateLimit,
  lowFrequencyRateLimit,
  sanitizeGDPRData
}

