/**
 * JWT Token Management for API Authentication
 */

import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'

/**
 * Generate JWT token for authenticated user
 */
export function generateToken(user) {
  const payload = {
    userId: user.id,
    username: user.username,
    email: user.email,
    role: user.role,
    iat: Math.floor(Date.now() / 1000)
  }

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'modernai-dashboard',
    audience: 'modernai-api'
  })
}

/**
 * Generate API key for programmatic access (longer expiry)
 */
export function generateApiKey(user, expiresIn = '30d') {
  const payload = {
    userId: user.id,
    username: user.username,
    role: user.role,
    type: 'api_key',
    iat: Math.floor(Date.now() / 1000)
  }

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn,
    issuer: 'modernai-dashboard',
    audience: 'modernai-api'
  })
}

/**
 * Verify and decode JWT token
 */
export function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'modernai-dashboard',
      audience: 'modernai-api'
    })
  } catch (error) {
    throw new Error(`Invalid token: ${error.message}`)
  }
}

/**
 * Generate secure session token for web UI
 */
export function generateSessionToken(user) {
  const payload = {
    userId: user.id,
    username: user.username,
    role: user.role,
    type: 'session',
    iat: Math.floor(Date.now() / 1000)
  }

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '24h', // Shorter for web sessions
    issuer: 'modernai-dashboard',
    audience: 'modernai-web'
  })
}