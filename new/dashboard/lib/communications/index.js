// Communication services index
// Centralized exports for all communication channels

export { default as WhatsAppService } from './whatsapp.js';
export { default as EmailService } from './email.js';
export { default as SMSService } from './sms.js';

/**
 * Unified communication service that orchestrates all channels
 * Handles the 5-minute instant response workflow
 */
class CommunicationService {
  constructor(config = {}) {
    this.whatsapp = new (await import('./whatsapp.js')).default(config.whatsapp);
    this.email = new (await import('./email.js')).default(config.email);
    this.sms = new (await import('./sms.js')).default(config.sms);
  }
  
  /**
   * Execute instant response workflow (within 5 minutes)
   * Sends SMS + Email + WhatsApp in sequence
   * @param {Object} lead - Lead object
   */
  async executeInstantResponse(lead) {
    const results = {
      sms: null,
      email: null,
      whatsapp: null,
      startTime: new Date(),
      success: false
    };
    
    try {
      // 1. SMS first (fastest delivery)
      results.sms = await this.sms.sendInstantResponse(lead);
      
      // 2. Email with detailed info
      results.email = await this.email.sendInstantResponse(lead);
      
      // 3. WhatsApp as backup/additional channel
      if (lead.phone && lead.whatsapp_consent) {
        results.whatsapp = await this.whatsapp.sendInstantResponse(lead);
      }
      
      results.endTime = new Date();
      results.duration = results.endTime - results.startTime;
      results.success = results.sms.success || results.email.success;
      
      // Log performance metrics
      console.log(`Instant response completed in ${results.duration}ms for lead ${lead.id}`);
      
      return results;
      
    } catch (error) {
      console.error('Failed to execute instant response:', error);
      results.error = error.message;
      return results;
    }
  }
  
  /**
   * Send demo reminders across all channels
   * @param {Object} lead - Lead object
   * @param {Date} demoDate - Demo date/time
   */
  async sendDemoReminders(lead, demoDate) {
    const promises = [
      this.email.sendDemoConfirmation(lead, demoDate, lead.meeting_link),
      this.sms.sendDemoReminder(lead, demoDate)
    ];
    
    if (lead.whatsapp_consent) {
      promises.push(this.whatsapp.sendDemoReminder(lead, demoDate));
    }
    
    const results = await Promise.allSettled(promises);
    
    return {
      email: results[0],
      sms: results[1],
      whatsapp: results[2] || null
    };
  }
  
  /**
   * Send certificate delivery notifications
   * @param {Object} lead - Lead object
   * @param {string} certificateUrl - Certificate download URL
   */
  async sendCertificateDelivery(lead, certificateUrl) {
    const promises = [
      this.email.sendCertificate(lead, certificateUrl)
    ];
    
    if (lead.whatsapp_consent) {
      promises.push(this.whatsapp.sendCompletionMessage(lead, certificateUrl));
    }
    
    const results = await Promise.allSettled(promises);
    
    return {
      email: results[0],
      whatsapp: results[1] || null
    };
  }
}

export default CommunicationService;