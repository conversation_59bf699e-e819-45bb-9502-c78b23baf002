// SMS service for instant lead communication
// Handles SMS notifications and instant responses

/**
 * SMS service for automated lead communication
 * Primary channel for instant response within 5 minutes
 */

class SMSService {
  constructor(config = {}) {
    this.provider = config.provider || 'twilio'; // twilio, textlocal, msg91
    this.apiKey = config.apiKey || process.env.SMS_API_KEY;
    this.senderId = config.senderId || process.env.SMS_SENDER_ID || 'MDRNAI';
    this.client = null;
    
    this.initializeClient();
  }
  
  initializeClient() {
    try {
      // TODO: Initialize SMS provider client
      console.log(`SMS service initialized with ${this.provider} (placeholder)`);
    } catch (error) {
      console.error('Failed to initialize SMS client:', error);
    }
  }
  
  /**
   * Send SMS to lead
   * @param {string} to - Recipient phone number
   * @param {string} message - SMS content (160 chars recommended)
   * @param {Object} options - Additional options
   */
  async sendSMS(to, message, options = {}) {
    try {
      const phoneNumber = this.formatPhoneNumber(to);
      
      // TODO: Implement actual SMS sending
      console.log(`[PLACEHOLDER] SMS sent to ${phoneNumber}:`, message);
      
      return {
        success: true,
        messageId: 'sms_' + Date.now(),
        to: phoneNumber,
        status: 'sent',
        timestamp: new Date().toISOString(),
        cost: this.calculateCost(message)
      };
      
    } catch (error) {
      console.error('Failed to send SMS:', error);
      return {
        success: false,
        error: error.message,
        to: to
      };
    }
  }
  
  /**
   * Send instant response SMS (critical for 5-minute target)
   * @param {Object} lead - Lead object
   */
  async sendInstantResponse(lead) {
    const message = this.getInstantResponseMessage(lead);
    return await this.sendSMS(lead.phone, message, {
      leadId: lead.id,
      type: 'instant_response',
      priority: 'high'
    });
  }
  
  /**
   * Send demo reminder SMS
   * @param {Object} lead - Lead object
   * @param {Date} demoDate - Demo date/time
   */
  async sendDemoReminder(lead, demoDate) {
    const message = this.getDemoReminderMessage(lead, demoDate);
    return await this.sendSMS(lead.phone, message, {
      leadId: lead.id,
      type: 'demo_reminder'
    });
  }
  
  /**
   * Send qualification call notification
   * @param {Object} lead - Lead object
   * @param {number} minutesDelay - Minutes until call
   */
  async sendCallNotification(lead, minutesDelay = 2) {
    const message = this.getCallNotificationMessage(lead, minutesDelay);
    return await this.sendSMS(lead.phone, message, {
      leadId: lead.id,
      type: 'call_notification'
    });
  }
  
  // SMS Templates (optimized for 160 characters)
  getInstantResponseMessage(lead) {
    return `Hi ${lead.first_name}! Thanks for your AI Bootcamp interest. Our specialist will call you in 2-3 mins for a quick chat. Check email for details. -Modern AI Team`;
  }
  
  getDemoReminderMessage(lead, demoDate) {
    const timeStr = demoDate.toLocaleTimeString('en-IN', { 
      hour: 'numeric', 
      minute: '2-digit' 
    });
    
    return `Hi ${lead.first_name}! Reminder: Your AI Bootcamp demo is today at ${timeStr}. Meeting link sent via email. See you soon! -Modern AI`;
  }
  
  getCallNotificationMessage(lead, minutes) {
    return `Hi ${lead.first_name}! Our AI specialist will call you in ${minutes} minutes. Please keep your phone handy. Excited to chat! -Modern AI Team`;
  }
  
  // Utility methods
  formatPhoneNumber(phone) {
    // Remove all non-digits
    const cleaned = phone.replace(/\D/g, '');
    
    // Add country code if missing (assume India +91)
    if (!cleaned.startsWith('91') && cleaned.length === 10) {
      return '91' + cleaned;
    }
    
    // Remove + if present
    return cleaned.replace(/^\+/, '');
  }
  
  /**
   * Calculate SMS cost (helpful for budget tracking)
   * @param {string} message - SMS content
   */
  calculateCost(message) {
    const segments = Math.ceil(message.length / 160);
    const costPerSegment = 0.05; // ₹0.05 per SMS in India
    return segments * costPerSegment;
  }
  
  /**
   * Validate phone number format for SMS
   * @param {string} phoneNumber - Phone number to validate
   */
  validatePhoneNumber(phoneNumber) {
    const cleaned = this.formatPhoneNumber(phoneNumber);
    
    // Indian mobile number validation (10 digits starting with 6-9)
    const indianMobileRegex = /^91[6-9]\d{9}$/;
    
    return {
      isValid: indianMobileRegex.test(cleaned),
      formatted: cleaned,
      country: 'IN'
    };
  }
  
  /**
   * Check SMS delivery status
   * @param {string} messageId - SMS message ID
   */
  async checkDeliveryStatus(messageId) {
    // TODO: Implement delivery status check
    return {
      messageId: messageId,
      status: 'delivered', // delivered, failed, pending
      deliveredAt: new Date().toISOString()
    };
  }
  
  /**
   * Send bulk SMS (for campaigns)
   * @param {Array} recipients - Array of {phone, message, leadId}
   */
  async sendBulkSMS(recipients) {
    const results = [];
    
    for (const recipient of recipients) {
      const result = await this.sendSMS(
        recipient.phone, 
        recipient.message, 
        { leadId: recipient.leadId }
      );
      results.push(result);
      
      // Add small delay to avoid rate limiting
      await this.delay(100);
    }
    
    return {
      totalSent: results.filter(r => r.success).length,
      totalFailed: results.filter(r => !r.success).length,
      results: results
    };
  }
  
  // Helper method for delays
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default SMSService;

// Usage examples:
/*
import SMSService from './sms.js';

const sms = new SMSService();

// Send instant response
await sms.sendInstantResponse({
  id: 123,
  first_name: 'Rajesh',
  phone: '9876543210'
});

// Send demo reminder
await sms.sendDemoReminder(lead, new Date('2024-08-15T10:00:00'));
*/