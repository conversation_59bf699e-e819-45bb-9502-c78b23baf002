// WhatsApp integration for lead communication
// Node.js rewrite of Python send_whatsapp.py

/**
 * WhatsApp messaging service for lead management
 * Replaces Python script with Node.js implementation using Twilio WhatsApp API
 */

class WhatsAppService {
  constructor(config = {}) {
    this.accountSid = config.accountSid || process.env.TWILIO_ACCOUNT_SID;
    this.authToken = config.authToken || process.env.TWILIO_AUTH_TOKEN;
    this.fromNumber = config.fromNumber || process.env.TWILIO_WHATSAPP_FROM; // whatsapp:+***********
    this.client = null;
    
    this.initializeClient();
  }
  
  initializeClient() {
    try {
      // Will be implemented with Twilio SDK
      // const twilio = require('twilio');
      // this.client = twilio(this.accountSid, this.authToken);
      console.log('WhatsApp service initialized (placeholder)');
    } catch (error) {
      console.error('Failed to initialize WhatsApp client:', error);
    }
  }
  
  /**
   * Send WhatsApp message to lead
   * @param {string} to - Recipient phone number (with country code)
   * @param {string} message - Message content
   * @param {Object} options - Additional options
   */
  async sendMessage(to, message, options = {}) {
    try {
      // Validate phone number format
      const phoneNumber = this.formatPhoneNumber(to);
      
      // TODO: Implement actual Twilio WhatsApp API call
      // const result = await this.client.messages.create({
      //   from: this.fromNumber,
      //   to: `whatsapp:${phoneNumber}`,
      //   body: message,
      //   ...options
      // });
      
      console.log(`[PLACEHOLDER] WhatsApp message sent to ${phoneNumber}:`, message);
      
      // Return placeholder response
      return {
        success: true,
        messageId: 'placeholder_' + Date.now(),
        to: phoneNumber,
        status: 'sent',
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('Failed to send WhatsApp message:', error);
      return {
        success: false,
        error: error.message,
        to: to
      };
    }
  }
  
  /**
   * Send instant response to new lead (within 5 minutes target)
   * @param {Object} lead - Lead object with contact details
   */
  async sendInstantResponse(lead) {
    const message = this.getInstantResponseMessage(lead);
    return await this.sendMessage(lead.phone, message, {
      leadId: lead.id,
      type: 'instant_response'
    });
  }
  
  /**
   * Send demo reminder to lead
   * @param {Object} lead - Lead object
   * @param {Date} demoDate - Demo date/time
   */
  async sendDemoReminder(lead, demoDate) {
    const message = this.getDemoReminderMessage(lead, demoDate);
    return await this.sendMessage(lead.phone, message, {
      leadId: lead.id,
      type: 'demo_reminder'
    });
  }
  
  /**
   * Send bootcamp completion congratulations
   * @param {Object} lead - Lead object
   * @param {string} certificateUrl - Certificate download URL
   */
  async sendCompletionMessage(lead, certificateUrl) {
    const message = this.getCompletionMessage(lead, certificateUrl);
    return await this.sendMessage(lead.phone, message, {
      leadId: lead.id,
      type: 'completion'
    });
  }
  
  // Message templates
  getInstantResponseMessage(lead) {
    return `Hi ${lead.first_name}! 👋

Thank you for your interest in our AI Bootcamp! We received your inquiry and one of our AI specialists will call you within the next few minutes.

In the meantime, you can check out our success stories: https://modernaipro.com/success

Looking forward to speaking with you!
- Modern AI Team

Reply STOP to opt out.`;
  }
  
  getDemoReminderMessage(lead, demoDate) {
    const dateStr = demoDate.toLocaleDateString('en-IN', {
      weekday: 'long',
      month: 'long', 
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit'
    });
    
    return `Hi ${lead.first_name}! 📅

This is a friendly reminder about your AI Bootcamp demo scheduled for ${dateStr}.

Demo Link: [Meeting link will be provided]
Duration: 30 minutes

What to expect:
• Personalized AI learning path
• Live coding demonstration
• Q&A with our AI expert

See you soon!
- Modern AI Team`;
  }
  
  getCompletionMessage(lead, certificateUrl) {
    return `Congratulations ${lead.first_name}! 🎉

You've successfully completed the AI Bootcamp! Your certificate is ready:

📜 Download Certificate: ${certificateUrl}

Share your achievement:
• Add to LinkedIn profile
• Show to your network
• Use for career advancement

Interested in advanced AI programs? Reply YES for exclusive offers.

- Modern AI Team`;
  }
  
  // Utility methods
  formatPhoneNumber(phone) {
    // Remove all non-digits and format for WhatsApp
    const cleaned = phone.replace(/\D/g, '');
    
    // Add country code if missing (assume India +91)
    if (!cleaned.startsWith('91') && cleaned.length === 10) {
      return '+91' + cleaned;
    }
    
    // Add + if missing
    if (!cleaned.startsWith('+')) {
      return '+' + cleaned;
    }
    
    return cleaned;
  }
  
  /**
   * Validate WhatsApp number is opt-in compliant
   * @param {string} phoneNumber - Phone number to validate
   */
  async validateOptIn(phoneNumber) {
    // TODO: Implement opt-in validation logic
    // Check against opt-out database, validate format, etc.
    return true;
  }
  
  /**
   * Handle opt-out requests
   * @param {string} phoneNumber - Phone number requesting opt-out
   */
  async handleOptOut(phoneNumber) {
    // TODO: Implement opt-out handling
    console.log(`Processing opt-out request for ${phoneNumber}`);
  }
}

// Factory function for easy instantiation
export function createWhatsAppService(config) {
  return new WhatsAppService(config);
}

// Default export
export default WhatsAppService;

// Usage examples:
/*
import WhatsAppService from './whatsapp.js';

const whatsapp = new WhatsAppService();

// Send instant response to new lead
await whatsapp.sendInstantResponse({
  id: 123,
  first_name: 'Rajesh',
  phone: '+919876543210'
});

// Send demo reminder
await whatsapp.sendDemoReminder(lead, new Date('2024-08-15T10:00:00'));
*/