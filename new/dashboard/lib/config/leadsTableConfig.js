import Link from 'next/link';
import LeadAssignment from '../../components/forms/LeadAssignment';

// Helper function for friendly time formatting
const getFriendlyTime = (dateString) => {
  if (!dateString || dateString === 'N/A') return 'N/A';
  
  try {
    const date = new Date(dateString.replace('T', ' ').replace('+0000', 'Z'));
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffMins < 1) return 'now';
    if (diffMins < 60) return `${diffMins}min`;
    if (diffHours < 24) return `${diffHours}h`;
    if (diffDays < 7) return `${diffDays}d`;
    
    // For older dates, show month + day
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  } catch (e) {
    return 'N/A';
  }
};

const getStatusBadge = (status) => {
  const badges = {
    'auto-resp-1': 'bg-blue-100 text-blue-800',
    'auto-resp-2': 'bg-blue-100 text-blue-800',
    'auto-resp-3': 'bg-blue-100 text-blue-800',
    'qualified': 'bg-green-100 text-green-800',
    'contacted': 'bg-yellow-100 text-yellow-800',
    'payment-sent': 'bg-purple-100 text-purple-800',
    'enrolled': 'bg-green-100 text-green-800',
    'declined': 'bg-red-100 text-red-800'
  };
  return badges[status] || 'bg-gray-100 text-gray-800';
};

const getStatusLabel = (status) => {
  const labels = {
    'auto-resp-1': 'Auto-Response #1',
    'auto-resp-2': 'Auto-Response #2',
    'auto-resp-3': 'Auto-Response #3',
    'qualified': 'Qualified',
    'contacted': 'Contacted',
    'payment-sent': 'Payment Link Sent',
    'enrolled': 'Enrolled',
    'declined': 'Declined'
  };
  return labels[status] || status;
};

const getSourceOptions = () => [
  { value: 'fb_ads', label: 'Facebook Ads', color: 'bg-blue-100 text-blue-800' },
  { value: 'google_ads', label: 'Google Ads', color: 'bg-red-100 text-red-800' },
  { value: 'referral', label: 'Referral', color: 'bg-purple-100 text-purple-800' },
  { value: 'organic', label: 'Organic', color: 'bg-green-100 text-green-800' },
  { value: 'linkedin', label: 'LinkedIn', color: 'bg-indigo-100 text-indigo-800' },
  { value: 'website', label: 'Website', color: 'bg-gray-100 text-gray-800' },
  { value: 'other', label: 'Other', color: 'bg-yellow-100 text-yellow-800' }
];

const getSourceInfo = (source) => {
  const options = getSourceOptions();
  return options.find(opt => opt.value === source) || { 
    value: source, 
    label: source, 
    color: 'bg-gray-100 text-gray-800' 
  };
};

export const leadsTableConfig = {
  apiEndpoint: '/api/leads',
  title: '👤 PROSPECTS',
  description: 'Active prospects who haven\'t become customers yet',
  searchPlaceholder: 'Search by name, email, phone, or campaign...',
  defaultSortBy: 'created_time',
  defaultSortOrder: 'desc',
  defaultPageSize: 50,
  
  filters: [
    {
      key: 'source',
      label: 'Source',
      defaultValue: 'all',
      options: [
        { value: 'all', label: 'All Sources' },
        ...getSourceOptions()
      ]
    }
  ],

  columns: [
    {
      key: 'full_name',
      label: 'Name',
      sortable: true,
      render: (row) => (
        <Link 
          href={`/leads/${row.id}`}
          className="text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline"
        >
          {row.full_name}
        </Link>
      )
    },
    {
      key: 'email',
      label: 'Email',
      sortable: true,
      render: (row) => (
        <div className="text-sm text-gray-900">{row.email}</div>
      )
    },
    {
      key: 'phone',
      label: 'Phone',
      sortable: false,
      render: (row) => (
        <div className="text-sm text-gray-900">{row.phone}</div>
      )
    },
    {
      key: 'assigned_to',
      label: 'Assigned To',
      sortable: false,
      render: (row, { onRowUpdate }) => (
        <LeadAssignment
          leadId={row.id}
          currentAssignee={row.assigned_to}
          onAssignmentChange={(leadId, assignmentData) => {
            // Update the row data in the parent table
            if (onRowUpdate) {
              onRowUpdate(leadId, {
                assigned_to: assignmentData.assignedTo,
                assignee_name: assignmentData.assigneeName,
                assignee_role: assignmentData.assigneeRole
              });
            }
          }}
          className="min-w-[200px]"
        />
      )
    },
    {
      key: 'lead_source',
      label: 'Source',
      sortable: true,
      render: (row) => {
        const sourceInfo = getSourceInfo(row.lead_source);
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${sourceInfo.color}`}>
            {sourceInfo.label}
          </span>
        );
      }
    },
    {
      key: 'created_time',
      label: 'Created',
      sortable: true,
      render: (row) => (
        <div className="text-sm text-gray-500">
          {getFriendlyTime(row.created_time)}
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          row.status && row.status !== '' ? getStatusBadge(row.status) : 'bg-gray-100 text-gray-800'
        }`}>
          {row.status && row.status !== '' ? getStatusLabel(row.status) : 'Not Set'}
        </span>
      )
    }
  ]
};

export const leadsStatsRenderer = (summary) => (
  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
    <div className="bg-white rounded-lg shadow-sm border p-4">
      <div className="text-2xl font-bold text-green-600">{summary.total || 0}</div>
      <div className="text-sm text-gray-600">Active Prospects</div>
    </div>
    <div className="bg-white rounded-lg shadow-sm border p-4">
      <div className="text-2xl font-bold text-green-600">{summary.fb_ads || 0}</div>
      <div className="text-sm text-gray-600">Facebook Ads</div>
    </div>
    <div className="bg-white rounded-lg shadow-sm border p-4">
      <div className="text-2xl font-bold text-orange-600">
        {Object.values(summary).reduce((a, b) => a + b, 0) - (summary.fb_ads || 0)}
      </div>
      <div className="text-sm text-gray-600">Other Sources</div>
    </div>
  </div>
);