import Link from 'next/link';

const formatCurrency = (amount, currency) => {
  // If currency is not provided, use amount-based detection
  let detectedCurrency = currency;
  if (!currency || currency === 'USD') {
    // Use heuristic: amounts < 1000 likely USD, >= 1000 likely INR
    detectedCurrency = parseFloat(amount) < 1000 ? 'USD' : 'INR';
  }
  
  const currencySymbol = detectedCurrency === 'USD' ? '$' : '₹';
  return `${currencySymbol}${parseFloat(amount).toLocaleString()}`;
};

const formatRevenueWithAbbreviation = (amount, currency) => {
  const num = parseFloat(amount || 0);
  const currencySymbol = currency === 'INR' ? '₹' : '$';
  
  if (num >= 1000000) {
    return `${currencySymbol}${(num / 1000000).toFixed(1)}M`;
  } else if (num >= 1000) {
    return `${currencySymbol}${Math.round(num / 1000)}K`;
  } else {
    return `${currencySymbol}${Math.round(num)}`;
  }
};

const cleanCustomerName = (name, email) => {
  if (!name || name === 'N/A' || name.startsWith('unknown_pi_')) {
    return email || 'Unknown Customer';
  }
  return name;
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getStatusBadge = (status) => {
  const statusStyles = {
    'Paid': 'bg-green-100 text-green-800',
    'succeeded': 'bg-green-100 text-green-800',
    'pending': 'bg-yellow-100 text-yellow-800',
    'failed': 'bg-red-100 text-red-800',
    'canceled': 'bg-gray-100 text-gray-800'
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles[status] || statusStyles.canceled}`}>
      {status || 'unknown'}
    </span>
  );
};

export const paymentsTableConfig = {
  apiEndpoint: '/api/payments',
  title: '💳 PAYMENTS',
  description: 'Stripe payment records with customer and transaction details',
  searchPlaceholder: 'Search by name, email, or payment ID...',
  defaultSortBy: 'payment_created_at',
  defaultSortOrder: 'desc',
  defaultPageSize: 20,
  
  filters: [
    {
      key: 'status',
      label: 'Status',
      defaultValue: 'all',
      options: [
        { value: 'all', label: 'All Status' },
        { value: 'Paid', label: 'Paid' },
        { value: 'succeeded', label: 'Succeeded' },
        { value: 'pending', label: 'Pending' },
        { value: 'failed', label: 'Failed' },
        { value: 'canceled', label: 'Canceled' }
      ]
    }
  ],

  columns: [
    {
      key: 'full_name',
      label: 'Customer',
      sortable: true,
      render: (row) => (
        <div>
          <div className="text-sm font-medium">
            <Link 
              href={`/leads/${row.id}`}
              className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
            >
              {cleanCustomerName(row.full_name, row.email)}
            </Link>
          </div>
          <div className="text-sm text-gray-500">
            {row.email || 'N/A'}
          </div>
        </div>
      )
    },
    {
      key: 'payment_amount',
      label: 'Amount',
      sortable: true,
      render: (row) => (
        <div className="text-sm text-gray-900">
          {formatCurrency(row.payment_amount, row.payment_currency)}
        </div>
      )
    },
    {
      key: 'payment_status',
      label: 'Status',
      sortable: true,
      render: (row) => getStatusBadge(row.payment_status)
    },
    {
      key: 'workshop_enrolled',
      label: 'Workshop',
      sortable: true,
      render: (row) => (
        <div className="text-sm text-gray-900">
          {row.workshop_enrolled || 'N/A'}
        </div>
      )
    },
    {
      key: 'payment_created_at',
      label: 'Date',
      sortable: true,
      render: (row) => (
        <div className="text-sm text-gray-500">
          {formatDate(row.payment_created_at)}
        </div>
      )
    },
    {
      key: 'stripe_payment_intent_id',
      label: 'Payment ID',
      sortable: false,
      render: (row) => (
        <div className="text-sm text-gray-500 font-mono">
          {row.stripe_payment_intent_id ? 
            row.stripe_payment_intent_id.substring(0, 20) + '...' : 
            'N/A'
          }
        </div>
      )
    }
  ]
};

export const paymentsStatsRenderer = (stats) => (
  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
    <div className="bg-white rounded-lg shadow-sm border p-3">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 text-sm font-medium">💳</span>
          </div>
        </div>
        <div className="ml-3 min-w-0">
          <p className="text-xs font-medium text-gray-600 truncate">Transactions</p>
          <p className="text-xl font-bold text-gray-900">{stats.total_payments}</p>
        </div>
      </div>
    </div>

    <div className="bg-white rounded-lg shadow-sm border p-3">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
            <span className="text-orange-600 text-sm font-medium">🔄</span>
          </div>
        </div>
        <div className="ml-3 min-w-0">
          <p className="text-xs font-medium text-gray-600 truncate">Repeats</p>
          <p className="text-xl font-bold text-gray-900">{stats.repeat_customers}</p>
        </div>
      </div>
    </div>

    <div className="bg-white rounded-lg shadow-sm border p-3">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <span className="text-green-600 text-sm font-medium">$</span>
          </div>
        </div>
        <div className="ml-3 min-w-0">
          <p className="text-xs font-medium text-gray-600 truncate">USD</p>
          <p className="text-xl font-bold text-gray-900">{formatRevenueWithAbbreviation(stats.total_usd, 'USD')}</p>
        </div>
      </div>
    </div>

    <div className="bg-white rounded-lg shadow-sm border p-3">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
            <span className="text-yellow-600 text-sm font-medium">₹</span>
          </div>
        </div>
        <div className="ml-3 min-w-0">
          <p className="text-xs font-medium text-gray-600 truncate">INR</p>
          <p className="text-xl font-bold text-gray-900">{formatRevenueWithAbbreviation(stats.total_inr, 'INR')}</p>
        </div>
      </div>
    </div>
  </div>
);