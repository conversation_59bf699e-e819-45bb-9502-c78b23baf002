// Currency exchange rates (USD as base currency)
// TODO: Later replace with live exchange rate API
export const EXCHANGE_RATES = {
  USD: 1.0,     // Base currency
  INR: 83.25,   // Current USD to INR rate (update as needed)
  EUR: 0.85,    // USD to EUR
  GBP: 0.73,    // USD to GBP
  CAD: 1.25,    // USD to CAD
  AUD: 1.35     // USD to AUD
};

export const DEFAULT_CURRENCY = 'USD';

/**
 * Convert amount from one currency to another
 * @param {number} amount - Amount to convert
 * @param {string} fromCurrency - Source currency code
 * @param {string} toCurrency - Target currency code (default: USD)
 * @returns {number} Converted amount
 */
export function convertCurrency(amount, fromCurrency, toCurrency = DEFAULT_CURRENCY) {
  try {
    if (fromCurrency === toCurrency) {
      return parseFloat(amount);
    }
    
    const fromRate = EXCHANGE_RATES[fromCurrency.toUpperCase()] || 1.0;
    const toRate = EXCHANGE_RATES[toCurrency.toUpperCase()] || 1.0;
    
    // Convert to USD first, then to target currency
    const usdAmount = parseFloat(amount) / fromRate;
    const convertedAmount = usdAmount * toRate;
    
    return convertedAmount;
  } catch (error) {
    console.warn('Currency conversion error:', error);
    return 0.0;
  }
}

/**
 * Get exchange rate between two currencies
 * @param {string} fromCurrency - Source currency
 * @param {string} toCurrency - Target currency (default: USD)
 * @returns {number} Exchange rate
 */
export function getExchangeRate(fromCurrency, toCurrency = DEFAULT_CURRENCY) {
  try {
    const fromRate = EXCHANGE_RATES[fromCurrency.toUpperCase()] || 1.0;
    const toRate = EXCHANGE_RATES[toCurrency.toUpperCase()] || 1.0;
    return toRate / fromRate;
  } catch (error) {
    return 1.0;
  }
}

/**
 * Normalize array of amounts with different currencies to a single currency
 * @param {Array} amounts - Array of {amount, currency} objects
 * @param {string} targetCurrency - Target currency (default: USD)
 * @returns {Array} Array of normalized amounts
 */
export function normalizeCurrencyAmounts(amounts, targetCurrency = DEFAULT_CURRENCY) {
  return amounts.map(item => {
    if (typeof item === 'object' && item.amount !== undefined && item.currency) {
      return convertCurrency(item.amount, item.currency, targetCurrency);
    } else if (typeof item === 'number') {
      return item; // Assume already in target currency
    }
    return 0;
  });
}

/**
 * Calculate average cost per lead across different currencies
 * @param {Array} accounts - Array of account objects with cost_per_lead and currency
 * @returns {number} Average CPL in USD
 */
export function calculateAverageCPL(accounts) {
  const validAccounts = accounts.filter(acc => 
    acc.cost_per_lead > 0 && acc.currency
  );
  
  if (validAccounts.length === 0) return 0;
  
  const normalizedCPLs = validAccounts.map(acc => 
    convertCurrency(acc.cost_per_lead, acc.currency, DEFAULT_CURRENCY)
  );
  
  return normalizedCPLs.reduce((sum, cpl) => sum + cpl, 0) / normalizedCPLs.length;
}

/**
 * Format currency with proper symbol and conversion info
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code
 * @param {boolean} showUSDEquivalent - Show USD equivalent for non-USD currencies
 * @returns {string} Formatted currency string
 */
export function formatCurrencyWithConversion(amount, currency = 'USD', showUSDEquivalent = true) {
  const symbols = {
    USD: '$',
    INR: '₹',
    EUR: '€',
    GBP: '£',
    CAD: 'C$',
    AUD: 'A$'
  };
  
  const symbol = symbols[currency.toUpperCase()] || currency;
  const formattedAmount = `${symbol}${Math.round(amount)}`;
  
  if (showUSDEquivalent && currency.toUpperCase() !== 'USD') {
    const usdAmount = convertCurrency(amount, currency, 'USD');
    return `${formattedAmount} (~$${Math.round(usdAmount)})`;
  }
  
  return formattedAmount;
}

/**
 * Get current exchange rate info for display
 * @returns {Object} Exchange rate information
 */
export function getExchangeRateInfo() {
  return {
    rates: EXCHANGE_RATES,
    lastUpdated: 'Static rates - live rates coming soon', // TODO: Add timestamp when using live API
    baseCurrency: DEFAULT_CURRENCY
  };
}