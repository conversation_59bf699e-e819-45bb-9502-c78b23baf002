const Database = require('better-sqlite3');
const { getDatabasePath } = require('./dbConfig');

const dbPath = getDatabasePath();

let db = null;

// Get database connection (singleton)
function getDb() {
  if (!db) {
    try {
      db = new Database(dbPath);
      db.pragma('journal_mode = WAL');
      console.log('Connected to SQLite database at:', dbPath);
    } catch (error) {
      console.error('Error connecting to database:', error);
      throw error;
    }
  }
  return db;
}

// Helper function to run queries
function query(sql, params = []) {
  const database = getDb();
  try {
    return database.prepare(sql).all(params);
  } catch (error) {
    console.error('Query error:', error);
    throw error;
  }
}

// Helper function to run insert/update queries
function run(sql, params = []) {
  const database = getDb();
  try {
    return database.prepare(sql).run(params);
  } catch (error) {
    console.error('Run error:', error);
    throw error;
  }
}

// Helper function to get a single row
function get(sql, params = []) {
  const database = getDb();
  try {
    return database.prepare(sql).get(params);
  } catch (error) {
    console.error('Get error:', error);
    throw error;
  }
}

// Close database connection
function closeDb() {
  if (db) {
    db.close();
    db = null;
  }
}

module.exports = {
  getDb,
  query,
  run,
  get,
  closeDb
};