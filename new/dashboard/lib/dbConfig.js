const path = require('path');

/**
 * Get the database path from environment variable or fallback to default
 * @returns {string} Absolute path to the database file
 */
function getDatabasePath() {
  // Use DATABASE_URL from environment, fallback to relative path
  const dbPath = process.env.DATABASE_URL || '../data/database/leads.db';
  
  // If relative path, make it absolute from dashboard directory
  if (!path.isAbsolute(dbPath)) {
    return path.join(process.cwd(), dbPath);
  }
  
  return dbPath;
}

module.exports = {
  getDatabasePath
};