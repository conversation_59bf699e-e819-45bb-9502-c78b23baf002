// Facebook API integration utilities
export class FacebookLeadsAP<PERSON> {
  constructor(accessToken) {
    this.accessToken = accessToken;
    this.baseUrl = 'https://graph.facebook.com/v18.0';
  }

  async getAdAccounts() {
    try {
      const response = await fetch(
        `${this.baseUrl}/me/adaccounts?access_token=${this.accessToken}`
      );
      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('Error fetching ad accounts:', error);
      throw error;
    }
  }

  async getLeadGenForms(adAccountId) {
    try {
      const response = await fetch(
        `${this.baseUrl}/${adAccountId}/leadgen_forms?access_token=${this.accessToken}`
      );
      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('Error fetching lead gen forms:', error);
      throw error;
    }
  }

  async getLeads(formId, since = null) {
    try {
      let url = `${this.baseUrl}/${formId}/leads?access_token=${this.accessToken}`;
      if (since) {
        url += `&since=${since}`;
      }
      
      const response = await fetch(url);
      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('Error fetching leads:', error);
      throw error;
    }
  }

  async getLeadDetails(leadId) {
    try {
      const response = await fetch(
        `${this.baseUrl}/${leadId}?fields=id,created_time,field_data&access_token=${this.accessToken}`
      );
      return await response.json();
    } catch (error) {
      console.error('Error fetching lead details:', error);
      throw error;
    }
  }
}

// Helper function to extract field values from Facebook lead data
export function getFieldValue(fieldData, fieldName) {
  const field = fieldData.find(f => f.name === fieldName);
  return field?.values?.[0] || null;
}

// Map Facebook lead to our database format
export function mapFacebookLead(fbLead, accountInfo) {
  const fieldData = fbLead.field_data || [];
  
  return {
    fb_lead_id: fbLead.id,
    full_name: getFieldValue(fieldData, 'full_name') || getFieldValue(fieldData, 'name'),
    email: getFieldValue(fieldData, 'email'),
    phone: getFieldValue(fieldData, 'phone_number'),
    current_title: getFieldValue(fieldData, 'job_title'),
    company: getFieldValue(fieldData, 'company_name'),
    location: getFieldValue(fieldData, 'city'),
    ai_experience_level: getFieldValue(fieldData, 'ai_experience'),
    python_knowledge: getFieldValue(fieldData, 'python_exp'),
    bootcamp_expectations: getFieldValue(fieldData, 'expectations'),
    ad_account: accountInfo.name,
    campaign_name: accountInfo.campaign,
    status: 'new',
    created_at: fbLead.created_time
  };
}