const { verifyToken } = require('./auth');
const { getDb } = require('./db');

const requireRole = (allowedRoles) => {
  return (handler) => {
    return async (req, res) => {
      try {
        // Extract token from Authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return res.status(401).json({ error: 'No token provided' });
        }

        const token = authHeader.substring(7);
        const decoded = verifyToken(token);
        
        if (!decoded) {
          return res.status(401).json({ error: 'Invalid or expired token' });
        }

        // Get fresh user data from database to ensure current role
        const db = getDb();
        const user = db.prepare('SELECT * FROM users WHERE id = ? AND is_active = TRUE').get(decoded.id);
        
        if (!user) {
          return res.status(401).json({ error: 'User not found or inactive' });
        }

        // Check if user has required role
        if (!allowedRoles.includes(user.role)) {
          return res.status(403).json({ 
            error: 'Insufficient permissions',
            required: allowedRoles,
            current: user.role
          });
        }

        // Add user info to request
        req.user = {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          first_name: user.first_name,
          last_name: user.last_name
        };

        return handler(req, res);
      } catch (error) {
        console.error('Role auth error:', error);
        return res.status(500).json({ error: 'Internal server error' });
      }
    };
  };
};

// Convenience functions for common role requirements
const requireAdmin = requireRole(['admin']);
const requireSales = requireRole(['admin', 'sales']);
const requireAnyAuth = requireRole(['admin', 'sales', 'student']);

module.exports = {
  requireRole,
  requireAdmin,
  requireSales,
  requireAnyAuth
};