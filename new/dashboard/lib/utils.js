import { formatDistanceToNow, format } from 'date-fns';
import { zonedTimeToUtc, utcToZonedTime } from 'date-fns-tz';
import { parsePhoneNumber, isValidPhoneNumber } from 'libphonenumber-js';

// Time utilities (replaces Python time_utils.py)
export const humanFriendlyTime = (timestamp) => {
  return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
};

export const formatIST = (timestamp) => {
  const istTime = utcToZonedTime(new Date(timestamp), 'Asia/Kolkata');
  return format(istTime, 'EEE h a'); // "Mon 2 PM"
};

// Phone validation utilities (replaces Python phone_validation.py)
export const validatePhone = (phone, country = 'IN') => {
  try {
    return isValidPhoneNumber(phone, country);
  } catch {
    return false;
  }
};

export const cleanPhone = (phone, country = 'IN') => {
  try {
    const phoneNumber = parsePhoneNumber(phone, country);
    return phoneNumber?.format('E.164') || ''; // +919876543210
  } catch {
    return '';
  }
};

export const formatPhoneDisplay = (phone, country = 'IN') => {
  try {
    const phoneNumber = parsePhoneNumber(phone, country);
    return phoneNumber?.formatNational() || phone; // 98765 43210
  } catch {
    return phone;
  }
};

// Utility functions for dashboard data formatting
export const formatCurrency = (amount, currency = 'INR') => {
  if (currency === 'INR') {
    return `₹${amount.toLocaleString('en-IN')}`;
  } else if (currency === 'USD') {
    return `$${amount.toLocaleString('en-US')}`;
  }
  return amount.toString();
};

export const formatPercentage = (value) => {
  // Handle both string and number inputs
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) {
    return '0.0%';
  }
  return `${numValue.toFixed(1)}%`;
};

export const getStatusColor = (status) => {
  const colors = {
    new: 'bg-blue-100 text-blue-800',
    auto_responded: 'bg-cyan-100 text-cyan-800',
    contacted: 'bg-yellow-100 text-yellow-800',
    qualified: 'bg-purple-100 text-purple-800',
    demo_scheduled: 'bg-orange-100 text-orange-800',
    demo_completed: 'bg-indigo-100 text-indigo-800',
    enrolled: 'bg-green-100 text-green-800',
    bootcamp_complete: 'bg-emerald-100 text-emerald-800',
    kapi_opportunity: 'bg-teal-100 text-teal-800',
    lost: 'bg-red-100 text-red-800'
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};