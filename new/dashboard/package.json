{"name": "ai-bootcamp-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup-db": "node scripts/setup-database.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@heroicons/react": "^2.2.0", "axios": "^1.11.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "better-sqlite3": "^12.2.0", "chart.js": "^4.5.0", "chartjs-chart-wordcloud": "^4.4.4", "clsx": "^2.1.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.10", "next": "14.0.4", "react": "^18", "react-chartjs-2": "^5.3.0", "react-dom": "^18", "react-icons": "^5.5.0", "recharts": "^2.8.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "node-fetch": "^3.3.2", "node-mocks-http": "^1.17.2", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}