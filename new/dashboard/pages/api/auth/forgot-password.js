// const User = require('../../../lib/models/User'); // TODO: Create User model
const { run } = require('../../../lib/db');
const crypto = require('crypto');

// In-memory token storage (in production, use Redis or database)
const resetTokens = new Map();

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Find user by email
    const user = User.findByEmail(email);
    
    // Always return success for security (don't reveal if email exists)
    if (!user) {
      return res.status(200).json({
        message: 'If your email is registered, you will receive a password reset link.'
      });
    }

    // Generate secure reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    
    // Store token with expiration (24 hours)
    resetTokens.set(resetToken, {
      userId: user.id,
      email: user.email,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    });

    // Clean up expired tokens
    for (const [token, data] of resetTokens.entries()) {
      if (new Date() > data.expires) {
        resetTokens.delete(token);
      }
    }

    // In production, send email here
    console.log(`Password reset token for ${email}: ${resetToken}`);
    console.log(`Reset URL: ${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`);

    return res.status(200).json({
      message: 'If your email is registered, you will receive a password reset link.'
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

// Export tokens map for use in reset-password.js
module.exports.resetTokens = resetTokens;