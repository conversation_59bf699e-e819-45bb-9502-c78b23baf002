const { verifyPassword, signToken } = require('../../../lib/auth');
const { getDb } = require('../../../lib/db');

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ 
        detail: 'Username and password are required' 
      });
    }

    // Find user by username or email
    const db = getDb();
    let user = db.prepare('SELECT * FROM users WHERE username = ?').get(username);
    
    // If not found by username, try email
    if (!user) {
      user = db.prepare('SELECT * FROM users WHERE email = ?').get(username);
    }
    
    if (!user) {
      return res.status(401).json({ 
        detail: 'Invalid username or password' 
      });
    }

    // Check if email is verified
    if (!user.email_verified) {
      return res.status(403).json({ 
        detail: 'Email not verified. Please check your email for verification link.',
        headers: { 'x-error': 'email_not_verified' }
      });
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.password_hash);
    
    if (!isValidPassword) {
      return res.status(401).json({ 
        detail: 'Invalid username or password' 
      });
    }

    // Generate JWT token
    const token = signToken({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
    });

    // Return successful login response
    res.status(200).json({
      access_token: token,
      token_type: 'Bearer',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role: user.role,
        subscription_tier: user.subscription_tier,
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ 
      detail: 'Internal server error' 
    });
  }
}