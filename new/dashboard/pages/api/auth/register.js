import { hashPassword } from '../../../lib/auth';
// import { User } from '../../../models'; // TODO: Create models

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const { 
      username, 
      email, 
      password, 
      first_name, 
      last_name, 
      welcome_code 
    } = req.body;

    // Validation
    if (!username || !email || !password || !first_name || !last_name || !welcome_code) {
      return res.status(400).json({ 
        detail: 'All fields are required' 
      });
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ 
        detail: 'Invalid email format' 
      });
    }

    // Password length validation
    if (password.length < 8) {
      return res.status(400).json({ 
        detail: 'Password must be at least 8 characters long' 
      });
    }

    // Check if welcome code is valid (for now, accept any non-empty code)
    // In production, you'd validate against a database of valid codes
    if (!welcome_code.trim()) {
      return res.status(400).json({ 
        detail: 'Invalid welcome code',
        headers: { 'x-error': 'invalid_welcome_code' }
      });
    }

    // Check if user already exists
    const existingUser = await User.findByUsername(username);
    if (existingUser) {
      return res.status(400).json({ 
        detail: 'Username already exists' 
      });
    }

    const existingEmail = await User.findByEmail(email);
    if (existingEmail) {
      return res.status(400).json({ 
        detail: 'Email already registered' 
      });
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const userData = {
      username,
      email,
      password_hash: hashedPassword,
      first_name,
      last_name,
      role: 'student', // Default role
      subscription_tier: 'free', // Default tier
      email_verified: false, // Would be true after email verification
      welcome_code_used: welcome_code,
    };

    const userId = await User.create(userData);

    // Return success response
    res.status(201).json({
      message: 'User created successfully',
      user_id: userId,
      detail: 'Registration successful. You can now log in with your new account.'
    });

  } catch (error) {
    console.error('Registration error:', error);
    
    // Handle specific database errors
    if (error.message.includes('UNIQUE constraint failed')) {
      return res.status(400).json({ 
        detail: 'Username or email already exists' 
      });
    }

    res.status(500).json({ 
      detail: 'Internal server error' 
    });
  }
}