// import { User } from '../../../models'; // TODO: Create models
import { signToken } from '../../../lib/auth';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ 
        detail: 'Email is required' 
      });
    }

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      // Don't reveal if email exists or not for security
      return res.status(200).json({
        message: 'If your email is registered, you will receive a verification link.',
        detail: 'Verification email sent successfully'
      });
    }

    // Check if already verified
    if (user.email_verified) {
      return res.status(200).json({
        message: 'Email already verified',
        detail: 'Your email has already been verified'
      });
    }

    // Generate verification token
    const verificationToken = signToken(
      { 
        id: user.id, 
        email: user.email, 
        type: 'email_verification' 
      },
      '24h'
    );

    // In a real application, you would send an email here
    // For now, we'll just log the token (in production, remove this)
    console.log(`Email verification token for ${email}: ${verificationToken}`);
    console.log(`Verification URL: ${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/login?verify=${verificationToken}`);

    res.status(200).json({
      message: 'Verification email sent successfully',
      detail: 'If your email is registered, you will receive a new verification link.'
    });

  } catch (error) {
    console.error('Resend verification error:', error);
    res.status(500).json({ 
      detail: 'Internal server error' 
    });
  }
}