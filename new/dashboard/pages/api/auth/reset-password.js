// const User = require('../../../lib/models/User');  // TODO: Create User model
const bcrypt = require('bcrypt');

// Import the resetTokens map from forgot-password.js
let resetTokens;
try {
  resetTokens = require('./forgot-password').resetTokens;
} catch (error) {
  // Fallback if import fails
  resetTokens = new Map();
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json({ 
        error: 'Token and new password are required' 
      });
    }

    // Password length validation
    if (newPassword.length < 8) {
      return res.status(400).json({ 
        error: 'Password must be at least 8 characters long' 
      });
    }

    // Validate token
    const tokenData = resetTokens.get(token);
    if (!tokenData) {
      return res.status(400).json({ 
        error: 'Invalid or expired token' 
      });
    }

    // Check token expiration
    if (new Date() > tokenData.expires) {
      resetTokens.delete(token);
      return res.status(400).json({ 
        error: 'Token has expired' 
      });
    }

    // Find user
    const user = User.findById(tokenData.userId);
    if (!user) {
      return res.status(400).json({ 
        error: 'User not found' 
      });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update user password
    const userInstance = new User(user);
    await userInstance.update({ hashedPassword });

    // Remove used token
    resetTokens.delete(token);

    return res.status(200).json({
      message: 'Password has been reset successfully'
    });

  } catch (error) {
    console.error('Reset password error:', error);
    return res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
}