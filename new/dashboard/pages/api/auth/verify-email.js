// const User = require('../../../lib/models/User');  // TODO: Create User model
const { get } = require('../../../lib/db');

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ error: 'Verification token is required' });
    }

    // Find user with the verification token
    const user = get(
      'SELECT * FROM users WHERE email_verification_token = ?',
      [token]
    );
    
    if (!user) {
      return res.status(400).json({ error: 'Invalid verification token' });
    }

    // Check if already verified
    if (user.email_verified) {
      return res.status(200).json({ 
        message: 'Email already verified', 
        email: user.email 
      });
    }

    // Check token age (48 hours validity)
    if (user.email_verification_sent_at) {
      const tokenAge = new Date() - new Date(user.email_verification_sent_at);
      if (tokenAge > 48 * 60 * 60 * 1000) { // 48 hours in milliseconds
        return res.status(400).json({ 
          error: 'Verification token has expired. Please request a new one.' 
        });
      }
    }

    // Mark email as verified
    const userInstance = new User(user);
    await userInstance.update({
      email_verified: 1,
      email_verification_token: null
    });

    return res.status(200).json({
      message: 'Email verified successfully',
      email: user.email,
      username: user.username
    });

  } catch (error) {
    console.error('Email verification error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}