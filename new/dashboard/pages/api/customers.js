import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import { getDatabasePath } from '../../lib/dbConfig.js';
import { requireDataAccess, rateLimit, sanitizeGDPRData } from '../../lib/auth/middleware.js';

const dbPath = getDatabasePath();

async function openDb() {
  return open({
    filename: dbPath,
    driver: sqlite3.Database
  });
}

// Protect customer data with authentication, authorization, and rate limiting
export default requireDataAccess('customers')(
  rateLimit(30, 15 * 60 * 1000)(async function handler(req, res) {
    if (req.method === 'GET') {
      return handleGet(req, res);
    } else if (req.method === 'PUT') {
      return handlePut(req, res);
    } else {
      res.setHeader('Allow', ['GET', 'PUT']);
      res.status(405).json({ error: 'Method not allowed' });
    }
  })
)

async function handleGet(req, res) {
  try {
    const db = await openDb();
    const { id, status, workshop_type, search } = req.query;
    
    if (id) {
      // Get specific customer details
      const customer = await db.get(`
        SELECT l.*,
               COUNT(p.id) as total_payments,
               SUM(p.amount) as total_spent_payments,
               MAX(p.payment_date) as last_payment_date
        FROM leads l
        LEFT JOIN payments p ON p.customer_email = l.email
        WHERE l.id = ? AND l.payment_status IN ('Paid', 'Partial')
        GROUP BY l.id
      `, [id]);
      
      if (!customer) {
        return res.status(404).json({ error: 'Customer not found' });
      }
      
      // Get customer's payment history
      const payments = await db.all(`
        SELECT p.*, w.name as workshop_name
        FROM payments p
        LEFT JOIN workshops w ON w.type = p.workshop_type OR w.name LIKE '%' || p.workshop_type || '%'
        WHERE p.customer_email = ?
        ORDER BY p.payment_date DESC
      `, [customer.email]);
      
      // Get customer's workshop enrollments
      const workshops = await db.all(`
        SELECT w.*, l.enrolled_date, l.workshop_completed_date, 
               l.certificate_generated, l.certificate_sent
        FROM workshops w
        JOIN leads l ON l.workshop_type = w.type
        WHERE l.email = ? 
        ORDER BY w.start_date DESC
      `, [customer.email]);
      
      res.json({ 
        customer, 
        payments,
        workshops,
        summary: {
          total_spent: customer.total_spent_payments || customer.payment_amount || 0,
          workshops_enrolled: workshops.length,
          workshops_completed: workshops.filter(w => w.workshop_completed_date).length,
          certificates_earned: workshops.filter(w => w.certificate_generated).length
        }
      });
    } else {
      // Get all customers (leads with payments)
      let query = `
        SELECT l.id, l.full_name, l.email, l.phone, l.location, l.company,
               l.workshop_type, l.payment_status, l.enrolled_date, l.workshop_completed_date,
               l.payment_amount, l.payment_date, l.certificate_generated,
               l.lead_source, l.created_time, l.account_manager,
               COUNT(p.id) as payment_count,
               SUM(p.amount) as total_spent_stripe,
               MAX(p.payment_date) as last_payment_date,
               w.name as workshop_name
        FROM leads l
        LEFT JOIN payments p ON p.customer_email = l.email
        LEFT JOIN workshops w ON w.type = l.workshop_type
        WHERE l.payment_status IN ('Paid', 'Partial')
      `;
      
      const params = [];
      
      if (status) {
        query += ` AND l.payment_status = ?`;
        params.push(status);
      }
      
      if (workshop_type) {
        query += ` AND l.workshop_type = ?`;
        params.push(workshop_type);
      }
      
      if (search) {
        query += ` AND (l.full_name LIKE ? OR l.email LIKE ? OR l.company LIKE ?)`;
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }
      
      query += ` GROUP BY l.id ORDER BY l.payment_date DESC`;
      
      const customers = await db.all(query, params);
      
      // Get summary stats
      const stats = await db.get(`
        SELECT 
          COUNT(DISTINCT l.id) as total_customers,
          COUNT(DISTINCT CASE WHEN l.workshop_completed_date IS NOT NULL THEN l.id END) as completed_customers,
          COUNT(DISTINCT CASE WHEN l.certificate_generated = 1 THEN l.id END) as certified_customers,
          SUM(l.payment_amount) as total_revenue_leads,
          SUM(p.amount) as total_revenue_payments,
          AVG(l.payment_amount) as avg_order_value
        FROM leads l
        LEFT JOIN payments p ON p.customer_email = l.email
        WHERE l.payment_status IN ('Paid', 'Partial')
      `);
      
      // Get workshop type breakdown
      const workshopBreakdown = await db.all(`
        SELECT 
          l.workshop_type,
          COUNT(*) as customer_count,
          SUM(l.payment_amount) as revenue
        FROM leads l
        WHERE l.payment_status IN ('Paid', 'Partial')
        GROUP BY l.workshop_type
        ORDER BY customer_count DESC
      `);
      
      res.json({ 
        customers,
        stats,
        workshopBreakdown
      });
    }
    
    await db.close();
  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({ error: 'Failed to fetch customers' });
  }
}

async function handlePut(req, res) {
  try {
    const db = await openDb();
    const { id } = req.query;
    const updateData = req.body;
    
    const fields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updateData);
    values.push(id);
    
    await db.run(`
      UPDATE leads 
      SET ${fields}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND payment_status IN ('Paid', 'Partial')
    `, values);
    
    const updatedCustomer = await db.get(`
      SELECT * FROM leads WHERE id = ? AND payment_status IN ('Paid', 'Partial')
    `, [id]);
    
    await db.close();
    res.json(updatedCustomer);
  } catch (error) {
    console.error('Error updating customer:', error);
    res.status(500).json({ error: 'Failed to update customer' });
  }
}