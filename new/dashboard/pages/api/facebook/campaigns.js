import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import { getDatabasePath } from '../../../lib/dbConfig.js';
import { requireDataAccess } from '../../../lib/auth/middleware.js';

// Protect Facebook campaigns business intelligence data  
export default requireDataAccess('facebook')(async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { days = 7, limit = 10 } = req.query;
    const timePeriod = parseInt(days);
    const campaignLimit = parseInt(limit);
    
    // Validate parameters
    if (![1, 7, 30].includes(timePeriod)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid days parameter. Must be 1, 7, or 30.' 
      });
    }

    const dbPath = getDatabasePath();
    const db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });

    // Create campaign performance table if it doesn't exist
    await db.exec(`
      CREATE TABLE IF NOT EXISTS facebook_campaign_performance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        check_date DATE NOT NULL,
        account_id TEXT NOT NULL,
        account_name TEXT NOT NULL,
        campaign_id TEXT NOT NULL,
        campaign_name TEXT NOT NULL,
        time_period_days INTEGER NOT NULL,
        impressions INTEGER NOT NULL,
        clicks INTEGER NOT NULL,
        leads INTEGER NOT NULL,
        spend REAL NOT NULL,
        cost_per_lead REAL NOT NULL,
        ctr REAL NOT NULL,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(check_date, campaign_id, time_period_days)
      )
    `);

    // First, try to get real campaign performance data
    const campaignQuery = `
      SELECT 
        campaign_id,
        campaign_name,
        account_id,
        account_name,
        impressions,
        clicks,
        leads,
        spend,
        cost_per_lead,
        ctr,
        check_date,
        time_period_days
      FROM facebook_campaign_performance
      WHERE time_period_days = ?
        AND check_date = (
          SELECT MAX(check_date) 
          FROM facebook_campaign_performance 
          WHERE time_period_days = ?
        )
        AND leads > 0
      ORDER BY cost_per_lead ASC, leads DESC
      LIMIT ?
    `;

    let campaigns = await db.all(campaignQuery, [timePeriod, timePeriod, campaignLimit]);

    // If no campaign performance data exists, fall back to account-level simulation
    if (campaigns.length === 0) {
      console.log('No campaign performance data found, using account-level fallback');
      
      const accountQuery = `
        SELECT 
          h.account_id,
          h.account_name,
          m.account_currency,
          m.total_impressions,
          m.total_clicks,
          m.total_leads,
          m.total_spent,
          m.cost_per_lead,
          m.num_active_campaigns,
          m.check_date
        FROM facebook_account_health h
        INNER JOIN facebook_campaign_metrics m ON (
          h.account_id = m.account_id 
          AND m.time_period_days = ?
          AND m.check_date = (
            SELECT MAX(check_date) 
            FROM facebook_campaign_metrics 
            WHERE account_id = h.account_id AND time_period_days = ?
          )
        )
        WHERE h.accessible = 1 
          AND m.total_leads > 0
          AND m.num_active_campaigns > 0
        GROUP BY h.account_id
        ORDER BY m.cost_per_lead ASC, m.total_leads DESC
        LIMIT ?
      `;

      const accountRows = await db.all(accountQuery, [timePeriod, timePeriod, Math.ceil(campaignLimit / 2)]);
      
      // Create one campaign per account to avoid duplication
      campaigns = accountRows.map((row) => {
        const originalCurrency = row.account_currency || 'USD';
        const spend = parseFloat(row.total_spent.toFixed(2));
        const costPerLead = parseFloat(row.cost_per_lead.toFixed(2));
        
        // Convert to both USD and INR for display
        const exchangeRate = 83.25; // USD to INR
        let spendUSD, spendINR, costPerLeadUSD, costPerLeadINR;
        
        if (originalCurrency === 'USD') {
          spendUSD = Math.round(spend);
          spendINR = Math.round(spend * exchangeRate);
          costPerLeadUSD = Math.round(costPerLead * 10) / 10;
          costPerLeadINR = Math.round(costPerLead * exchangeRate * 10) / 10;
        } else {
          spendINR = Math.round(spend);
          spendUSD = Math.round(spend / exchangeRate);
          costPerLeadINR = Math.round(costPerLead * 10) / 10;
          costPerLeadUSD = Math.round(costPerLead / exchangeRate * 10) / 10;
        }
        
        return {
          id: `${row.account_id}_main_campaign`,
          name: `${row.account_name} - Main Campaign`,
          campaign_id: `${row.account_id}_main_campaign`,
          campaign_name: `${row.account_name} - Main Campaign`,
          account_name: row.account_name,
          account_id: row.account_id,
          currency: originalCurrency,
          impressions: row.total_impressions,
          clicks: row.total_clicks,
          leads: row.total_leads,
          spend: spend,
          spend_usd: spendUSD,
          spend_inr: spendINR,
          cost_per_lead: costPerLead,
          cost_per_lead_usd: costPerLeadUSD,
          cost_per_lead_inr: costPerLeadINR,
          ctr: row.total_clicks > 0 ? 
            parseFloat((row.total_clicks / Math.max(1, row.total_impressions) * 100).toFixed(2)) : 0,
          performance_score: parseFloat((100 - (costPerLead / 10)).toFixed(1)),
          check_date: row.check_date,
          time_period_days: timePeriod
        };
      });
    } else {
      // Use real campaign data - add currency info from account metrics
      const currencyQuery = `
        SELECT DISTINCT account_id, account_currency 
        FROM facebook_campaign_metrics 
        WHERE time_period_days = ?
      `;
      const currencyMap = {};
      const currencyRows = await db.all(currencyQuery, [timePeriod]);
      currencyRows.forEach(row => {
        currencyMap[row.account_id] = row.account_currency || 'USD';
      });

      campaigns = campaigns.map(campaign => {
        const originalCurrency = currencyMap[campaign.account_id] || 'USD';
        const spend = parseFloat(campaign.spend.toFixed(2));
        const costPerLead = parseFloat(campaign.cost_per_lead.toFixed(2));
        
        // Convert to both USD and INR for display
        const exchangeRate = 83.25; // USD to INR
        let spendUSD, spendINR, costPerLeadUSD, costPerLeadINR;
        
        if (originalCurrency === 'USD') {
          spendUSD = Math.round(spend);
          spendINR = Math.round(spend * exchangeRate);
          costPerLeadUSD = Math.round(costPerLead * 10) / 10;
          costPerLeadINR = Math.round(costPerLead * exchangeRate * 10) / 10;
        } else {
          spendINR = Math.round(spend);
          spendUSD = Math.round(spend / exchangeRate);
          costPerLeadINR = Math.round(costPerLead * 10) / 10;
          costPerLeadUSD = Math.round(costPerLead / exchangeRate * 10) / 10;
        }
        
        return {
          id: campaign.campaign_id,
          name: campaign.campaign_name,
          campaign_id: campaign.campaign_id,
          campaign_name: campaign.campaign_name,
          account_name: campaign.account_name,
          account_id: campaign.account_id,
          currency: originalCurrency,
          impressions: campaign.impressions,
          clicks: campaign.clicks,
          leads: campaign.leads,
          spend: spend,
          spend_usd: spendUSD,
          spend_inr: spendINR,
          cost_per_lead: costPerLead,
          cost_per_lead_usd: costPerLeadUSD,
          cost_per_lead_inr: costPerLeadINR,
          ctr: parseFloat(campaign.ctr.toFixed(2)),
          performance_score: parseFloat((100 - (costPerLead / 10)).toFixed(1)),
          check_date: campaign.check_date,
          time_period_days: campaign.time_period_days
        };
      });
    }

    await db.close();

    // Sort by performance score (best campaigns first)
    const topCampaigns = campaigns
      .filter(c => c.leads > 0)
      .sort((a, b) => {
        // Primary: lowest cost per lead
        if (a.cost_per_lead !== b.cost_per_lead) {
          return a.cost_per_lead - b.cost_per_lead;
        }
        // Secondary: highest leads
        return b.leads - a.leads;
      })
      .slice(0, campaignLimit);

    // Calculate summary
    const summary = {
      total_campaigns: topCampaigns.length,
      total_leads: topCampaigns.reduce((sum, c) => sum + c.leads, 0),
      total_impressions: topCampaigns.reduce((sum, c) => sum + c.impressions, 0),
      total_clicks: topCampaigns.reduce((sum, c) => sum + c.clicks, 0),
      total_spend: topCampaigns.reduce((sum, c) => sum + c.spend, 0),
      total_spend_usd: topCampaigns.reduce((sum, c) => sum + c.spend_usd, 0),
      total_spend_inr: topCampaigns.reduce((sum, c) => sum + c.spend_inr, 0),
      avg_cost_per_lead: topCampaigns.length > 0 
        ? topCampaigns.reduce((sum, c) => sum + c.cost_per_lead, 0) / topCampaigns.length 
        : 0,
      avg_ctr: topCampaigns.length > 0 
        ? topCampaigns.reduce((sum, c) => sum + c.ctr, 0) / topCampaigns.length 
        : 0,
      time_period_days: timePeriod,
      last_updated: new Date().toISOString()
    };

    res.status(200).json({
      success: true,
      campaigns: topCampaigns,
      summary: summary
    });

  } catch (error) {
    console.error('Error fetching campaign performance:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
})