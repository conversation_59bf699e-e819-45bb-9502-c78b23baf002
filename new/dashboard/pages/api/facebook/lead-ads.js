import sqlite3 from 'sqlite3';
import path from 'path';
import { requireDataAccess } from '../../../lib/auth/middleware.js';

const dbPath = path.join(process.cwd(), '..', 'data', 'database', 'leads.db');

// Protect Facebook lead ads business intelligence data
export default requireDataAccess('facebook')(function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { days = 7 } = req.query;
  const timePeriod = parseInt(days);
  
  // Validate days parameter
  if (![1, 7, 30].includes(timePeriod)) {
    return res.status(400).json({ 
      success: false, 
      message: 'Invalid days parameter. Must be 1, 7, or 30.' 
    });
  }

  const db = new sqlite3.Database(dbPath);

  try {
    // Get latest lead ads data with campaign performance metrics
    // Get lead ads with actual lead counts from leads table, using fb_ad_id to avoid double counting
    const query = `
      SELECT 
        la.ad_id,
        la.ad_name,
        la.account_name,
        la.campaign_id,
        la.campaign_name,
        la.campaign_objective,
        la.ad_status,
        la.questionnaire_fields,
        la.check_date,
        la.last_updated,
        -- Get actual cumulative lead count from leads table by ad_id
        COALESCE(l_total.total_leads, 0) as cumulative_leads,
        -- Get period-specific lead count from leads table by ad_id
        COALESCE(l_period.period_leads, 0) as period_leads
      FROM facebook_lead_ads la
      LEFT JOIN (
        SELECT 
          fb_ad_id,
          COUNT(*) as total_leads
        FROM leads 
        WHERE lead_source = 'fb_ads' AND fb_ad_id IS NOT NULL AND fb_ad_id != ''
        GROUP BY fb_ad_id
      ) l_total ON la.ad_id = l_total.fb_ad_id
      LEFT JOIN (
        SELECT 
          fb_ad_id,
          COUNT(*) as period_leads
        FROM leads 
        WHERE lead_source = 'fb_ads' AND fb_ad_id IS NOT NULL AND fb_ad_id != ''
          AND datetime(substr(created_time, 1, 19)) >= datetime('now', '-${timePeriod} days')
        GROUP BY fb_ad_id
      ) l_period ON la.ad_id = l_period.fb_ad_id
      WHERE la.accessible = 1
      ORDER BY COALESCE(l_total.total_leads, 0) DESC, la.account_name ASC
    `;

    db.all(query, [], (err, rows) => {
      if (err) {
        console.error('Database error:', err);
        db.close();
        return res.status(500).json({ 
          success: false, 
          error: 'Database query failed',
          message: err.message 
        });
      }

      // Filter out ads with 0 leads in the selected time period
      const filteredRows = rows.filter(row => (row.period_leads || 0) > 0);

      // Calculate summary statistics using actual lead counts from database
      const cumulativeLeads = filteredRows.reduce((sum, row) => sum + (row.cumulative_leads || 0), 0);
      const periodLeads = filteredRows.reduce((sum, row) => sum + (row.period_leads || 0), 0);
      const activeAds = filteredRows.filter(row => row.ad_status === 'ACTIVE').length;
      const workingAds = filteredRows.filter(row => (row.cumulative_leads || 0) > 0).length;
      
      // Group by account for summary
      const accountSummary = filteredRows.reduce((acc, row) => {
        if (!acc[row.account_name]) {
          acc[row.account_name] = {
            account_name: row.account_name,
            total_ads: 0,
            cumulative_leads: 0,
            period_leads: 0,
            working_ads: 0
          };
        }
        acc[row.account_name].total_ads += 1;
        acc[row.account_name].cumulative_leads += (row.cumulative_leads || 0);
        acc[row.account_name].period_leads += (row.period_leads || 0);
        if ((row.cumulative_leads || 0) > 0) {
          acc[row.account_name].working_ads += 1;
        }
        return acc;
      }, {});

      const summary = {
        total_ads: filteredRows.length,
        active_ads: activeAds,
        working_ads: workingAds,
        cumulative_leads: cumulativeLeads,
        period_leads: periodLeads,
        time_period_days: timePeriod,
        accounts: Object.values(accountSummary)
      };

      res.status(200).json({
        success: true,
        summary,
        lead_ads: filteredRows,
        last_updated: filteredRows.length > 0 ? filteredRows[0].last_updated : null,
        time_period_days: timePeriod
      });
      
      db.close();
    });

  } catch (error) {
    console.error('API error:', error);
    db.close();
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error',
      message: error.message 
    });
  }
})