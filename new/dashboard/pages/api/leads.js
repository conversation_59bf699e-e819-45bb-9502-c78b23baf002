import sqlite3 from 'sqlite3';
import { getDatabasePath } from '../../lib/dbConfig.js';
const { requireDataAccess, sanitizeGDPRData } = require('../../lib/auth/middleware');

const dbPath = getDatabasePath();

function getSortClause(sortBy, sortOrder) {
  const validSortColumns = {
    'full_name': 'l.full_name',
    'email': 'l.email',
    'lead_source': 'l.lead_source',
    'created_time': 'l.created_time',
    'status': 'l.status'
  };
  
  const column = validSortColumns[sortBy] || 'l.created_time';
  const order = sortOrder === 'asc' ? 'ASC' : 'DESC';
  
  if (column === 'l.created_time') {
    return `
      CASE 
        WHEN l.created_time IS NOT NULL THEN datetime(substr(l.created_time, 1, 19))
        ELSE datetime('1970-01-01')
      END ${order},
      l.id DESC
    `;
  }
  
  return `${column} ${order}, l.id DESC`;
}

// Apply security middleware: Auth + Data access control (no rate limiting for high-frequency UI API)
export default requireDataAccess('leads')(function handler(req, res) {
    if (req.method === 'GET') {
      return handleGetLeads(req, res);
    } else if (req.method === 'POST') {
      return handleCreateLead(req, res);
    } else if (req.method === 'PUT') {
      return handleUpdateLead(req, res);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  })

function handleCreateLead(req, res) {
  const { 
    product_type,
    full_name, 
    email, 
    lead_source, 
    phone,
    // Kapi-specific fields
    deal_size,
    company,
    location
  } = req.body;
  
  // Validate required fields
  if (!product_type || !full_name || !email || !lead_source) {
    return res.status(400).json({ 
      success: false, 
      error: 'Missing required fields: product_type, full_name, email, lead_source' 
    });
  }

  const db = new sqlite3.Database(dbPath);

  try {
    // Generate a unique lead_id
    const lead_id = `manual_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    // Base fields for all lead types
    const baseData = {
      lead_id,
      full_name,
      email,
      phone: phone || null,
      lead_source,
      data_source: 'manual',
      status: 'New',
      created_time: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Kapi-specific fields
    if (product_type === 'Kapi') {
      baseData.company = company || null;
      baseData.location = location || null;
      baseData.notes = deal_size ? `Deal size: ${deal_size}` : null;
      baseData.priority = 'High'; // Kapi leads are high priority
    } else if (product_type === 'ModernAI') {
      baseData.workshop_type = 'AI Essentials'; // Default workshop
      baseData.priority = 'Medium';
    } else {
      baseData.priority = 'Low';
    }

    const fields = Object.keys(baseData).join(', ');
    const placeholders = Object.keys(baseData).map(() => '?').join(', ');
    const values = Object.values(baseData);

    const insertQuery = `INSERT INTO leads (${fields}) VALUES (${placeholders})`;
    
    db.run(insertQuery, values, function(err) {
      if (err) {
        console.error('Insert error:', err);
        return res.status(500).json({ 
          success: false, 
          error: 'Database insert failed',
          message: err.message 
        });
      }

      res.status(201).json({
        success: true,
        message: 'Lead created successfully',
        lead: {
          id: this.lastID,
          lead_id,
          ...baseData
        }
      });
    });

  } catch (error) {
    console.error('API error:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error',
      message: error.message 
    });
  } finally {
    db.close();
  }
}

function handleUpdateLead(req, res) {
  const { id, lead_source } = req.body;
  
  if (!id || !lead_source) {
    return res.status(400).json({ 
      success: false, 
      error: 'Missing required fields: id and lead_source' 
    });
  }

  const db = new sqlite3.Database(dbPath);

  try {
    const updateQuery = `UPDATE leads SET lead_source = ? WHERE id = ?`;
    
    db.run(updateQuery, [lead_source, id], function(err) {
      if (err) {
        console.error('Update error:', err);
        return res.status(500).json({ 
          success: false, 
          error: 'Database update failed',
          message: err.message 
        });
      }

      if (this.changes === 0) {
        return res.status(404).json({ 
          success: false, 
          error: 'Lead not found' 
        });
      }

      res.status(200).json({
        success: true,
        message: 'Lead source updated successfully',
        changes: this.changes
      });
    });

  } catch (error) {
    console.error('API error:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error',
      message: error.message 
    });
  } finally {
    db.close();
  }
}

async function handleGetLeads(req, res) {
  const { 
    page = 1, 
    limit = 50, 
    search = '', 
    source = 'all',
    sortBy = 'created_time',
    sortOrder = 'desc'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const pageLimit = parseInt(limit);

  const db = new sqlite3.Database(dbPath);

  // Promisify database operations
  const dbGet = (query, params) => {
    return new Promise((resolve, reject) => {
      db.get(query, params, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  };

  const dbAll = (query, params) => {
    return new Promise((resolve, reject) => {
      db.all(query, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  };

  try {
    // Build WHERE clause - exclude customers (those with payment status) and Stripe customers
    let whereClause = "WHERE l.data_source != 'stripe' AND (l.payment_status IS NULL OR l.payment_status NOT IN ('Paid', 'Partial'))";
    let params = [];

    if (search) {
      whereClause += ` AND (
        l.full_name LIKE ? OR 
        l.email LIKE ? OR 
        l.phone LIKE ? OR 
        l.campaign_name LIKE ?
      )`;
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern, searchPattern);
    }

    if (source !== 'all') {
      whereClause += ` AND l.lead_source = ?`;
      params.push(source);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM leads l ${whereClause}`;
    const countResult = await dbGet(countQuery, params);
    const totalLeads = countResult.total;
    const totalPages = Math.ceil(totalLeads / pageLimit);

    // Get leads with pagination and assignee information
    const leadsQuery = `
      SELECT 
        l.id,
        l.facebook_lead_id,
        l.full_name,
        l.email,
        l.phone,
        l.campaign_name,
        l.account_name,
        l.campaign_id,
        l.account_id,
        l.lead_source,
        l.status,
        l.assigned_to,
        l.notes,
        l.ai_experience_level,
        l.programming_experience,
        l.learning_goals,
        l.created_time,
        l.data_source,
        u.first_name as assignee_first_name,
        u.last_name as assignee_last_name,
        u.role as assignee_role
      FROM leads l
      LEFT JOIN users u ON l.assigned_to = u.id
      ${whereClause}
      ORDER BY ${getSortClause(sortBy, sortOrder)}
      LIMIT ? OFFSET ?
    `;

    const queryParams = [...params, pageLimit, offset];
    const rows = await dbAll(leadsQuery, queryParams);

    // Process and format the leads data
    const leads = rows.map(row => ({
      ...row,
      // Format the created_time for display
      created_time_formatted: row.created_time ? 
        new Date(row.created_time.replace('T', ' ').replace('+0000', 'Z')).toLocaleString() : 
        'N/A',
      // Clean up empty fields
      full_name: row.full_name || 'N/A',
      email: row.email || 'N/A',
      phone: row.phone || 'N/A',
      campaign_name: row.campaign_name || 'N/A',
      account_name: row.account_name || 'N/A',
      status: row.status || '', // Keep empty for proper handling in UI
      assigned_to: row.assigned_to || null,
      assignee_name: row.assignee_first_name && row.assignee_last_name 
        ? `${row.assignee_first_name} ${row.assignee_last_name}`.trim() 
        : null,
      assignee_role: row.assignee_role || null,
      notes: row.notes || '',
      ai_experience_level: row.ai_experience_level || 'N/A',
      programming_experience: row.programming_experience || 'N/A',
      learning_goals: row.learning_goals || 'N/A'
    }));

    // Get summary stats - exclude customers and Stripe customers
    const summaryQuery = `
      SELECT 
        l.lead_source,
        COUNT(*) as count
      FROM leads l
      WHERE l.data_source != 'stripe' AND (l.payment_status IS NULL OR l.payment_status NOT IN ('Paid', 'Partial'))
      GROUP BY l.lead_source
      ORDER BY count DESC
    `;

    let summary = {};
    try {
      const summaryRows = await dbAll(summaryQuery, []);
      summary = summaryRows.reduce((acc, row) => {
        acc[row.lead_source] = row.count;
        return acc;
      }, {});
    } catch (err) {
      console.error('Summary query error:', err);
      // Continue without summary if it fails
    }

    db.close();

    // Apply GDPR data sanitization based on user role
    const sanitizedLeads = sanitizeGDPRData(leads, req.userRole);

    res.status(200).json({
      success: true,
      leads: sanitizedLeads,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_leads: totalLeads,
        leads_per_page: pageLimit,
        has_next: parseInt(page) < totalPages,
        has_prev: parseInt(page) > 1
      },
      summary,
      filters: {
        search,
        source
      },
      // Include security metadata for audit
      _security: {
        userRole: req.userRole,
        userId: req.userId,
        dataAccess: 'leads',
        sanitized: req.userRole !== 'admin'
      }
    });

  } catch (error) {
    console.error('API error:', error);
    db.close();
    res.status(500).json({ 
      success: false, 
      error: 'Database query failed',
      message: error.message 
    });
  }
}