import sqlite3 from 'sqlite3';
import path from 'path';
import { requireDataAccess, rateLimit } from '../../../lib/auth/middleware.js';

const dbPath = path.join(process.cwd(), '..', 'data', 'database', 'leads.db');

function handler(req, res) {
  if (req.method === 'GET') {
    return handleGetLead(req, res);
  } else if (req.method === 'PUT') {
    return handleUpdateLead(req, res);
  } else {
    return res.status(405).json({ error: 'Method not allowed' });
  }
}

function handleUpdateLead(req, res) {
  const { id } = req.query;
  const { full_name, email, phone, status, assigned_to, notes } = req.body;

  if (!id) {
    return res.status(400).json({ 
      success: false, 
      error: 'Lead ID is required' 
    });
  }

  const db = new sqlite3.Database(dbPath);

  try {
    // Build dynamic update query based on provided fields
    const updates = [];
    const values = [];

    if (full_name !== undefined) {
      updates.push('full_name = ?');
      values.push(full_name);
    }
    if (email !== undefined) {
      updates.push('email = ?');
      values.push(email);
    }
    if (phone !== undefined) {
      updates.push('phone = ?');
      values.push(phone);
    }
    if (status !== undefined) {
      updates.push('status = ?');
      values.push(status);
    }
    if (assigned_to !== undefined) {
      updates.push('assigned_to = ?');
      // Convert to integer for database storage (or NULL if empty)
      values.push(assigned_to === '' ? null : parseInt(assigned_to));
    }
    if (notes !== undefined) {
      updates.push('notes = ?');
      values.push(notes);
    }

    if (updates.length === 0) {
      db.close();
      return res.status(400).json({ 
        success: false, 
        error: 'No fields to update' 
      });
    }

    values.push(id); // Add ID for WHERE clause
    
    const updateQuery = `UPDATE leads SET ${updates.join(', ')} WHERE id = ?`;
    
    db.run(updateQuery, values, function(err) {
      db.close(); // Close database after operation completes
      
      if (err) {
        console.error('Update error:', err);
        return res.status(500).json({ 
          success: false, 
          error: 'Database update failed',
          message: err.message 
        });
      }

      if (this.changes === 0) {
        return res.status(404).json({ 
          success: false, 
          error: 'Lead not found' 
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Lead updated successfully',
        changes: this.changes
      });
    });

  } catch (error) {
    console.error('API error:', error);
    db.close();
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error',
      message: error.message 
    });
  }
}

function handleGetLead(req, res) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ 
      success: false, 
      error: 'Lead ID is required' 
    });
  }

  const db = new sqlite3.Database(dbPath);

  try {
    const query = `
      SELECT 
        l.id,
        l.facebook_lead_id,
        l.full_name,
        l.email,
        l.phone,
        l.campaign_name,
        l.account_name,
        l.campaign_id,
        l.account_id,
        l.lead_source,
        l.status,
        l.assigned_to,
        l.notes,
        l.ai_experience_level,
        l.programming_experience,
        l.learning_goals,
        l.created_time,
        l.data_source,
        l.payment_history,
        l.stripe_payment_id,
        l.payment_amount,
        l.payment_status,
        l.customer_type,
        l.customer_lifetime_value,
        l.total_payments,
        u.first_name AS assignee_first_name,
        u.last_name AS assignee_last_name,
        u.role AS assignee_role
      FROM leads l
      LEFT JOIN users u ON l.assigned_to = u.id
      WHERE l.id = ?
    `;

    db.get(query, [id], (err, row) => {
      db.close(); // Close database after operation completes
      
      if (err) {
        console.error('Database error:', err);
        return res.status(500).json({ 
          success: false, 
          error: 'Database query failed',
          message: err.message 
        });
      }

      if (!row) {
        return res.status(404).json({ 
          success: false, 
          error: 'Lead not found' 
        });
      }

      // Parse form data from notes if it contains Facebook raw data
      let formData = [];
      let actualNotes = row.notes;
      
      if (row.notes && row.notes.startsWith('Raw fields: ')) {
        try {
          // Extract just the JSON part (before any | character)
          let jsonStr = row.notes.replace('Raw fields: ', '');
          const pipeIndex = jsonStr.indexOf(' | ');
          if (pipeIndex !== -1) {
            jsonStr = jsonStr.substring(0, pipeIndex);
          }
          const rawFields = JSON.parse(jsonStr);
          
          formData = rawFields.map(field => ({
            question: field.name || 'Unknown',
            answer: field.values ? field.values.join(', ') : 'N/A',
            // Clean up the question for better display
            displayQuestion: cleanupQuestion(field.name || 'Unknown')
          }));
          
          // Clear notes for editing since it contains form data
          actualNotes = '';
        } catch (e) {
          console.error('Error parsing form data:', e);
          formData = [{ 
            question: 'Raw Data', 
            answer: row.notes,
            displayQuestion: 'Raw Form Data'
          }];
          actualNotes = '';
        }
      }

      // Parse payment history if available
      let paymentHistory = [];
      let customerMetrics = null;
      
      if (row.payment_history) {
        try {
          const paymentData = JSON.parse(row.payment_history);
          if (paymentData.payment_history) {
            paymentHistory = paymentData.payment_history;
          }
          if (paymentData.customer_metrics) {
            customerMetrics = paymentData.customer_metrics;
          }
          // If payment_history is directly an array
          else if (Array.isArray(paymentData)) {
            paymentHistory = paymentData;
          }
        } catch (e) {
          console.error('Error parsing payment history JSON:', e);
        }
      }
      
      // Build customer metrics from individual fields if not in JSON
      if (!customerMetrics && (row.customer_type || row.customer_lifetime_value || row.total_payments)) {
        customerMetrics = {
          customer_type: row.customer_type,
          total_spent: row.customer_lifetime_value,
          total_payments: row.total_payments
        };
      }

      // Format the response
      const lead = {
        ...row,
        notes: actualNotes, // Use cleaned notes for editing
        created_time_formatted: row.created_time ? 
          new Date(row.created_time.replace('T', ' ').replace('+0000', 'Z')).toLocaleString() : 
          'N/A',
        formData: formData,
        paymentHistory: paymentHistory,
        customerMetrics: customerMetrics,
        // Add assignee name if available
        assignee_name: row.assignee_first_name && row.assignee_last_name ? 
          `${row.assignee_first_name} ${row.assignee_last_name}` : 
          null
      };

      return res.status(200).json({
        success: true,
        lead
      });
    });

  } catch (error) {
    console.error('API error:', error);
    db.close();
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error',
      message: error.message 
    });
  }
}

// Protect individual lead data - full GDPR protection
export default requireDataAccess('leads')(
  rateLimit(50, 15 * 60 * 1000)(handler)
)

// Helper function to clean up question text for better display
function cleanupQuestion(question) {
  return question
    .replace(/[_?]/g, ' ')  // Replace underscores and question marks with spaces
    .replace(/\s+/g, ' ')   // Replace multiple spaces with single space
    .trim()                 // Remove leading/trailing spaces
    .toLowerCase()          // Convert to lowercase
    .split(' ')             // Split into words
    .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
    .join(' ');             // Join back together
}