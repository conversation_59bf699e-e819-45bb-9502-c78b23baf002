import { requireAuth } from '../../../lib/auth';
import { run, get } from '../../../lib/db';

export default requireAuth(async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { leadId, assignedTo } = req.body;

    if (!leadId) {
      return res.status(400).json({ error: 'Lead ID is required' });
    }

    // If assignedTo is null or 0, we're unassigning the lead
    const assigneeId = assignedTo && assignedTo > 0 ? assignedTo : null;

    // Verify the lead exists
    const lead = get('SELECT id FROM leads WHERE id = ?', [leadId]);
    if (!lead) {
      return res.status(404).json({ error: 'Lead not found' });
    }

    // If assigneeId is provided, verify the user exists and has appropriate role
    if (assigneeId) {
      const assignee = get('SELECT id, role FROM users WHERE id = ? AND role IN (?, ?)', [assigneeId, 'admin', 'sales']);
      if (!assignee) {
        return res.status(400).json({ error: 'Invalid assignee. Must be admin or sales user.' });
      }
    }

    // Update the lead assignment
    const result = run(
      'UPDATE leads SET assigned_to = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', 
      [assigneeId, leadId]
    );

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Lead not found or no changes made' });
    }

    // Get the updated lead info for response
    const updatedLead = get(`
      SELECT 
        l.id, 
        l.full_name, 
        l.assigned_to,
        u.first_name,
        u.last_name,
        u.role
      FROM leads l 
      LEFT JOIN users u ON l.assigned_to = u.id 
      WHERE l.id = ?
    `, [leadId]);

    res.status(200).json({
      success: true,
      message: assigneeId ? 'Lead assigned successfully' : 'Lead unassigned successfully',
      lead: {
        id: updatedLead.id,
        name: updatedLead.full_name,
        assignedTo: updatedLead.assigned_to,
        assigneeName: updatedLead.first_name && updatedLead.last_name 
          ? `${updatedLead.first_name} ${updatedLead.last_name}` 
          : null,
        assigneeRole: updatedLead.role
      }
    });

  } catch (error) {
    console.error('Error assigning lead:', error);
    res.status(500).json({ 
      error: 'Failed to assign lead',
      details: error.message 
    });
  }
});