const { query } = require('../../lib/db');
const { requireDataAccess, rateLimit, sanitizeGDPRData } = require('../../lib/auth/middleware');

// Protect financial data with highest security - admin only
export default requireDataAccess('financial')(
  rateLimit(500, 60 * 60 * 1000)(async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { page = 1, limit = 20, search = '', status = 'all', sortBy = 'payment_date', sortOrder = 'desc' } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Function to get sort clause
    const getSortClause = (sortBy, sortOrder) => {
      const validSortColumns = {
        'full_name': 'full_name',
        'email': 'email',
        'payment_amount': 'payment_amount',
        'payment_status': 'payment_status',
        'workshop_enrolled': 'workshop_type',
        'payment_date': 'p.payment_date',
        'payment_created_at': 'p.payment_date'
      };
      
      const column = validSortColumns[sortBy] || 'p.payment_date';
      const order = sortOrder === 'asc' ? 'ASC' : 'DESC';
      return `${column} ${order}, p.id DESC`;
    };

    // Build the WHERE clause based on filters
    let whereClause = 'WHERE 1=1';
    let params = [];

    if (search) {
      whereClause += ' AND (full_name LIKE ? OR email LIKE ? OR stripe_payment_id LIKE ?)';
      const searchParam = `%${search}%`;
      params.push(searchParam, searchParam, searchParam);
    }

    if (status !== 'all') {
      whereClause += ' AND payment_status = ?';
      params.push(status);
    }

    // Update WHERE clause for payments table
    let paymentsWhereClause = 'WHERE 1=1';
    let paymentsParams = [];

    if (search) {
      paymentsWhereClause += ' AND (p.customer_name LIKE ? OR p.customer_email LIKE ? OR p.stripe_payment_intent_id LIKE ?)';
      const searchParam = `%${search}%`;
      paymentsParams.push(searchParam, searchParam, searchParam);
    }

    if (status !== 'all') {
      // Map payment_status to payments table status
      if (status === 'Paid') {
        paymentsWhereClause += ' AND p.status = ?';
        paymentsParams.push('succeeded');
      } else {
        paymentsWhereClause += ' AND p.status = ?';
        paymentsParams.push(status.toLowerCase());
      }
    }

    // Get total count for pagination from payments table
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM payments p
      LEFT JOIN leads l ON p.lead_id = l.id
      ${paymentsWhereClause}
    `;
    const countResult = query(countQuery, paymentsParams);
    const total = countResult[0]?.total || 0;

    // Get payments data from payments table with lead info
    const paymentsQuery = `
      SELECT 
        p.id,
        COALESCE(p.customer_name, l.full_name) as full_name,
        p.customer_email as email,
        p.stripe_payment_intent_id,
        p.amount as payment_amount,
        p.currency as payment_currency,
        p.amount as payment_amount_usd,
        CASE 
          WHEN p.status = 'succeeded' THEN 'Paid'
          ELSE UPPER(SUBSTR(p.status, 1, 1)) || SUBSTR(p.status, 2)
        END as payment_status,
        p.payment_date as payment_created_at,
        p.workshop_type as workshop_enrolled,
        COALESCE(l.created_time, p.payment_date) as created_at
      FROM payments p
      LEFT JOIN leads l ON p.lead_id = l.id
      ${paymentsWhereClause}
      ORDER BY ${getSortClause(sortBy, sortOrder)}
      LIMIT ? OFFSET ?
    `;
    
    const payments = query(paymentsQuery, [...paymentsParams, parseInt(limit), offset]);

    // Get summary statistics from payments table
    const statsQuery = `
      SELECT 
        COUNT(*) as total_payments,
        SUM(CASE WHEN status = 'succeeded' THEN 1 ELSE 0 END) as successful_payments,
        SUM(CASE WHEN status = 'succeeded' AND currency = 'USD' THEN amount ELSE 0 END) as total_usd,
        SUM(CASE WHEN status = 'succeeded' AND currency = 'INR' THEN amount ELSE 0 END) as total_inr,
        SUM(CASE WHEN status = 'succeeded' THEN amount ELSE 0 END) as total_usd_equivalent
      FROM payments
    `;
    
    const stats = query(statsQuery)[0];

    // Get customer analytics from payments table (unique customers, repeat customers)
    const customerAnalyticsQuery = `
      SELECT 
        COUNT(DISTINCT customer_email) as unique_customers,
        COUNT(*) - COUNT(DISTINCT customer_email) as repeat_customer_count,
        SUM(CASE WHEN email_count > 1 THEN 1 ELSE 0 END) as customers_with_repeats
      FROM (
        SELECT 
          customer_email,
          COUNT(*) as email_count
        FROM payments
        WHERE customer_email IS NOT NULL 
          AND customer_email != ''
          AND customer_email NOT LIKE 'unknown%'
        GROUP BY customer_email
      ) customer_counts
    `;
    
    const customerStats = query(customerAnalyticsQuery)[0];
    
    // Merge customer analytics with regular stats
    const combinedStats = {
      ...stats,
      unique_customers: customerStats.unique_customers || 0,
      repeat_customers: customerStats.customers_with_repeats || 0,
      repeat_transactions: customerStats.repeat_customer_count || 0,
      total_customers: customerStats.unique_customers || 0
    };

    res.status(200).json({
      success: true,
      data: {
        payments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / parseInt(limit))
        },
        stats: combinedStats
      }
    });

  } catch (error) {
    console.error('Error fetching payments:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
  })
)