import { requireDataAccess } from '../../../lib/auth/middleware.js';
import { run, get, query } from '../../../lib/db';

async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get all sales users who can be assigned leads
    const assignableUsers = query(`
      SELECT 
        id, 
        username, 
        first_name, 
        last_name, 
        email, 
        role
      FROM users 
      WHERE role = 'sales' 
        AND is_active = 1
      ORDER BY first_name ASC
    `);

    const formattedUsers = assignableUsers.map(user => ({
      id: user.id,
      name: `${user.first_name} ${user.last_name}`.trim(),
      username: user.username,
      email: user.email,
      role: user.role
    }));

    res.status(200).json({
      success: true,
      users: formattedUsers
    });

  } catch (error) {
    console.error('Error fetching assignable users:', error);
    res.status(500).json({ 
      error: 'Failed to fetch assignable users',
      details: error.message 
    });
  }
}

// Protect user directory data - sales access required for lead assignment
// No rate limiting for high-frequency user interface API
export default requireDataAccess('leads')(handler)

// TEMPORARY DEBUG VERSION - Remove auth to test
// export default handler