import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import { getDatabasePath } from '../../lib/dbConfig.js';
import { requireDataAccess, rateLimit, sanitizeGDPRData } from '../../lib/auth/middleware.js';

const dbPath = getDatabasePath();

async function openDb() {
  return open({
    filename: dbPath,
    driver: sqlite3.Database
  });
}

// Protect workshop business data and customer information
export default requireDataAccess('analytics')(
  rateLimit(30, 15 * 60 * 1000)(async function handler(req, res) {
    if (req.method === 'GET') {
      return handleGet(req, res);
    } else if (req.method === 'POST') {
      return handlePost(req, res);
    } else if (req.method === 'PUT') {
      return handlePut(req, res);
    } else if (req.method === 'DELETE') {
      return handleDelete(req, res);
    } else {
      res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
      res.status(405).json({ error: 'Method not allowed' });
    }
  })
)

async function handleGet(req, res) {
  try {
    const db = await openDb();
    
    const { id } = req.query;
    
    if (id) {
      // Get specific workshop with enrollment stats
      const workshop = await db.get(`
        SELECT w.*,
          COUNT(DISTINCT l.id) as enrolled_count,
          COUNT(DISTINCT CASE WHEN l.workshop_completed_date IS NOT NULL THEN l.id END) as completed_count,
          COALESCE(SUM(DISTINCT CASE WHEN l.payment_status = 'Paid' THEN l.payment_amount ELSE 0 END), 0) as total_revenue,
          COALESCE(SUM(DISTINCT p.amount), 0) as payment_revenue
        FROM workshops w
        LEFT JOIN leads l ON l.workshop_type = w.type 
          AND l.data_source LIKE '%US Leads.xlsx%'
        LEFT JOIN payments p ON p.lead_id = l.id
        WHERE w.id = ?
        GROUP BY w.id
      `, [id]);
      
      if (!workshop) {
        return res.status(404).json({ error: 'Workshop not found' });
      }
      
      // Get enrolled students/customers with reconciled payment data
      const students = await db.all(`
        SELECT l.id, l.full_name, l.email, l.payment_status, 
               l.enrolled_date, l.payment_amount as lead_payment_amount,
               COALESCE(p.amount, l.payment_amount, 0) as final_payment_amount,
               CASE 
                 WHEN p.amount IS NOT NULL THEN 'Stripe'
                 WHEN l.payment_amount IS NOT NULL THEN 'Manual' 
                 ELSE 'None'
               END as payment_source
        FROM leads l
        LEFT JOIN payments p ON p.lead_id = l.id OR p.customer_email = l.email
        WHERE l.workshop_type = ? 
          AND l.data_source LIKE '%US Leads.xlsx%'
        ORDER BY l.payment_status DESC, l.enrolled_date ASC, l.full_name ASC
      `, [workshop.type]);
      
      res.json({ workshop, students });
    } else {
      // Get all workshops with basic stats
      const workshops = await db.all(`
        SELECT w.*,
          COUNT(DISTINCT l.id) as enrolled_count,
          COUNT(DISTINCT CASE WHEN l.workshop_completed_date IS NOT NULL THEN l.id END) as completed_count,
          COALESCE(SUM(DISTINCT CASE WHEN l.payment_status = 'Paid' THEN l.payment_amount ELSE 0 END), 0) as total_revenue,
          COALESCE(SUM(DISTINCT p.amount), 0) as payment_revenue
        FROM workshops w
        LEFT JOIN leads l ON l.workshop_type = w.type 
          AND l.data_source LIKE '%US Leads.xlsx%'
        LEFT JOIN payments p ON p.lead_id = l.id
        GROUP BY w.id
        ORDER BY w.start_date DESC
      `);
      
      res.json(workshops);
    }
    
    await db.close();
  } catch (error) {
    console.error('Error fetching workshops:', error);
    res.status(500).json({ error: 'Failed to fetch workshops' });
  }
}

async function handlePost(req, res) {
  try {
    const db = await openDb();
    const { 
      name, type, description, start_date, end_date, instructor, 
      max_participants, price_usd, price_inr, timezone, meeting_link, materials_link 
    } = req.body;
    
    const result = await db.run(`
      INSERT INTO workshops (
        name, type, description, start_date, end_date, instructor,
        max_participants, price_usd, price_inr, timezone, meeting_link, materials_link
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      name, type, description, start_date, end_date, instructor,
      max_participants || 50, price_usd, price_inr, timezone || 'Asia/Kolkata', 
      meeting_link, materials_link
    ]);
    
    const newWorkshop = await db.get('SELECT * FROM workshops WHERE id = ?', [result.lastID]);
    
    await db.close();
    res.status(201).json(newWorkshop);
  } catch (error) {
    console.error('Error creating workshop:', error);
    res.status(500).json({ error: 'Failed to create workshop' });
  }
}

async function handlePut(req, res) {
  try {
    const db = await openDb();
    const { id } = req.query;
    const updateData = req.body;
    
    const fields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updateData);
    values.push(id);
    
    await db.run(`UPDATE workshops SET ${fields} WHERE id = ?`, values);
    
    const updatedWorkshop = await db.get('SELECT * FROM workshops WHERE id = ?', [id]);
    
    await db.close();
    res.json(updatedWorkshop);
  } catch (error) {
    console.error('Error updating workshop:', error);
    res.status(500).json({ error: 'Failed to update workshop' });
  }
}

async function handleDelete(req, res) {
  try {
    const db = await openDb();
    const { id } = req.query;
    
    await db.run('UPDATE workshops SET is_active = FALSE WHERE id = ?', [id]);
    
    await db.close();
    res.json({ message: 'Workshop deactivated successfully' });
  } catch (error) {
    console.error('Error deactivating workshop:', error);
    res.status(500).json({ error: 'Failed to deactivate workshop' });
  }
}