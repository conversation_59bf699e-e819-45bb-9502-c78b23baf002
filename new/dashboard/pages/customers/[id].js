import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Layout from '../../components/layout/Layout';
import { 
  FaArrowLeft, FaEnvelope, FaPhone, FaMapMarkerAlt, 
  FaBuilding, FaDollarSign, FaCalendar, FaCertificate,
  FaExternalLinkAlt, FaEdit, FaUsers, FaFileInvoiceDollar
} from 'react-icons/fa';

export default function CustomerDetailPage() {
  const [customer, setCustomer] = useState(null);
  const [payments, setPayments] = useState([]);
  const [workshops, setWorkshops] = useState([]);
  const [summary, setSummary] = useState({});
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const router = useRouter();
  const { id } = router.query;

  useEffect(() => {
    if (id) {
      fetchCustomerDetails();
    }
  }, [id]);

  const fetchCustomerDetails = async () => {
    try {
      const response = await fetch(`/api/customers?id=${id}`);
      const data = await response.json();
      
      setCustomer(data.customer);
      setPayments(data.payments);
      setWorkshops(data.workshops);
      setSummary(data.summary);
    } catch (error) {
      console.error('Error fetching customer details:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    if (!amount) return '$0.00';
    return `$${Number(amount).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </Layout>
    );
  }

  if (!customer) {
    return (
      <Layout>
        <div className="text-center py-12">
          <p className="text-gray-500">Customer not found.</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="p-2 text-gray-600 hover:text-gray-800"
            >
              <FaArrowLeft />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{customer.full_name}</h1>
              <p className="text-gray-600">Customer since {formatDate(customer.created_time)}</p>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => router.push(`/leads/${customer.id}`)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <FaEdit /> <span>Edit Details</span>
            </button>
            {customer.email && (
              <a
                href={`mailto:${customer.email}`}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
              >
                <FaEnvelope /> <span>Email</span>
              </a>
            )}
          </div>
        </div>

        {/* Customer Info Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Contact Information */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <FaUsers className="mr-2 text-blue-600" />
              Contact Information
            </h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <FaEnvelope className="text-gray-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-600">Email</p>
                  <p className="text-gray-900">{customer.email}</p>
                </div>
              </div>
              
              {customer.phone && (
                <div className="flex items-center">
                  <FaPhone className="text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Phone</p>
                    <p className="text-gray-900">{customer.phone}</p>
                  </div>
                </div>
              )}
              
              {customer.location && (
                <div className="flex items-center">
                  <FaMapMarkerAlt className="text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Location</p>
                    <p className="text-gray-900">{customer.location}</p>
                  </div>
                </div>
              )}
              
              {customer.company && (
                <div className="flex items-center">
                  <FaBuilding className="text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Company</p>
                    <p className="text-gray-900">{customer.company}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Financial Summary */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <FaDollarSign className="mr-2 text-green-600" />
              Financial Summary
            </h3>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Total Spent</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(summary.total_spent)}
                </p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Payments Made</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {customer.total_payments || payments.length || 0}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Last Payment</p>
                  <p className="text-sm text-gray-900">
                    {formatDate(customer.last_payment_date || customer.payment_date)}
                  </p>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-gray-600">Payment Status</p>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  customer.payment_status === 'Paid' ? 'bg-green-100 text-green-800' : 
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {customer.payment_status}
                </span>
              </div>
            </div>
          </div>

          {/* Learning Progress */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <FaCertificate className="mr-2 text-purple-600" />
              Learning Progress
            </h3>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Workshops Enrolled</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {summary.workshops_enrolled || workshops.length || 0}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Completed</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {summary.workshops_completed || 0}
                  </p>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-gray-600">Certificates Earned</p>
                <p className="text-lg font-semibold text-gray-900">
                  {summary.certificates_earned || 0}
                </p>
              </div>
              
              {customer.workshop_type && (
                <div>
                  <p className="text-sm text-gray-600">Current Workshop</p>
                  <p className="text-gray-900">{customer.workshop_type}</p>
                  <p className="text-sm text-gray-500">
                    {customer.workshop_completed_date ? 
                      `Completed ${formatDate(customer.workshop_completed_date)}` : 
                      'In Progress'
                    }
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {['overview', 'payments', 'workshops', 'activity'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">Customer Details</h3>
              <div className="space-y-3">
                <div>
                  <span className="font-medium text-gray-700">Lead Source:</span>
                  <span className="ml-2 text-gray-600">{customer.lead_source}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">First Contact:</span>
                  <span className="ml-2 text-gray-600">{formatDate(customer.created_time)}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Conversion Date:</span>
                  <span className="ml-2 text-gray-600">{formatDate(customer.payment_date)}</span>
                </div>
                {customer.ai_experience_level && (
                  <div>
                    <span className="font-medium text-gray-700">AI Experience:</span>
                    <span className="ml-2 text-gray-600">{customer.ai_experience_level}</span>
                  </div>
                )}
                {customer.programming_experience && (
                  <div>
                    <span className="font-medium text-gray-700">Programming Experience:</span>
                    <span className="ml-2 text-gray-600">{customer.programming_experience}</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <a
                  href={`mailto:${customer.email}`}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center">
                    <FaEnvelope className="text-blue-600 mr-3" />
                    <span>Send Email</span>
                  </div>
                  <FaExternalLinkAlt className="text-gray-400" />
                </a>
                
                <button className="flex items-center justify-between w-full p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center">
                    <FaCertificate className="text-purple-600 mr-3" />
                    <span>Generate Certificate</span>
                  </div>
                </button>
                
                <button className="flex items-center justify-between w-full p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center">
                    <FaFileInvoiceDollar className="text-green-600 mr-3" />
                    <span>View Invoices</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'payments' && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Payment History ({payments.length})</h3>
            </div>
            
            {payments.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Method
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reference
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {payments.map((payment) => (
                      <tr key={payment.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(payment.payment_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {formatCurrency(payment.amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {payment.workshop_name || payment.product_type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {payment.payment_method || 'Stripe'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            payment.status === 'completed' || payment.status === 'succeeded' ? 
                            'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {payment.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {payment.stripe_payment_intent_id || 'N/A'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <FaDollarSign className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-500">No payment history available.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'workshops' && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Workshop Enrollments ({workshops.length})</h3>
            </div>
            
            {workshops.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {workshops.map((workshop) => (
                  <div key={workshop.id} className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="text-lg font-medium text-gray-900">{workshop.name}</h4>
                        <p className="text-sm text-gray-600">{workshop.type}</p>
                        <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                          <span>📅 {formatDate(workshop.start_date)}</span>
                          {workshop.instructor && <span>👨‍🏫 {workshop.instructor}</span>}
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          workshop.workshop_completed_date ? 
                          'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {workshop.workshop_completed_date ? 'Completed' : 'In Progress'}
                        </span>
                        {workshop.certificate_generated && (
                          <div className="mt-2">
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                              Certificate {workshop.certificate_sent ? 'Sent' : 'Generated'}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <FaUsers className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-500">No workshop enrollments found.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">Activity Log</h3>
            <div className="text-center py-12">
              <FaCalendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500">Activity tracking coming soon...</p>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}