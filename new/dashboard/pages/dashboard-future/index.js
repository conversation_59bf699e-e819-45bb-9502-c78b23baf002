import { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Layout from '@/components/layout/Layout';
import MetricsCard from '@/components/MetricsCard';
import TimelineSelector from '@/components/TimelineSelector';
import SVGFunnel from '@/components/SVGFunnel';
import SalesTeamTable from '@/components/SalesTeamTable';
import GeographyTable from '@/components/GeographyTable';
import CampaignTable from '@/components/CampaignTable';
import UrgentActions from '@/components/UrgentActions';
import MarketingSourcesTable from '@/components/MarketingSourcesTable';
import EventsSchedule from '@/components/EventsSchedule';
import KapiLeadsTable from '@/components/KapiLeadsTable';
import FacebookAccountsTable from '@/components/FacebookAccountsTable';
import LeadAdsTable from '@/components/LeadAdsTable';
import SectionHeader from '@/components/SectionHeader';
import QuickNav from '@/components/QuickNav';
import { useTimelineData } from '@/hooks/useTimelineData';

export default function Dashboard() {
  const [activeTimeline, setActiveTimeline] = useState('30d');
  const { keyBusinessMetrics, pipelineMetrics, opportunityMetrics } = useTimelineData(activeTimeline);

  const handleTimelineChange = (timeline) => {
    setActiveTimeline(timeline);
  };

  return (
    <ProtectedRoute>
      <Layout title="AI Bootcamp Dashboard">
      <QuickNav />
      <div className="space-y-6">
        {/* Timeline Selector */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
          <TimelineSelector 
            activeTimeline={activeTimeline}
            onTimelineChange={handleTimelineChange}
          />
        </div>

        {/* High-Value Business Metrics */}
        <div>
          <SectionHeader
            id="key-metrics"
            title={`🎯 KEY BUSINESS PERFORMANCE (${activeTimeline.toUpperCase()})`}
            moreLink="/revenue"
            moreLinkText="Revenue Management →"
            description="Core business metrics with timeline comparison"
          />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {keyBusinessMetrics.map((metric, index) => (
              <MetricsCard 
                key={index}
                title=""
                metrics={[metric]} 
                isHighValue={true}
              />
            ))}
          </div>
        </div>

        {/* Pipeline & Growth Metrics */}
        <div>
          <SectionHeader
            id="pipeline-growth"
            title={`📈 PIPELINE & GROWTH METRICS (${activeTimeline.toUpperCase()})`}
            moreLink="/analytics"
            moreLinkText="Advanced Analytics →"
            description="Pipeline value, NPS, referrals, and growth indicators"
          />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {pipelineMetrics.map((metric, index) => (
              <MetricsCard 
                key={index}
                title=""
                metrics={[metric]} 
                isHighValue={true}
              />
            ))}
          </div>
        </div>

        {/* Opportunity & Optimization */}
        <div>
          <SectionHeader
            id="opportunities"
            title={`💡 OPPORTUNITIES & POTENTIAL (${activeTimeline.toUpperCase()})`}
            moreLink="/optimization"
            moreLinkText="Optimization Center →"
            description="Kapi pipeline, cross-sells, and revenue optimization"
          />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {opportunityMetrics.map((metric, index) => (
              <MetricsCard 
                key={index}
                title=""
                metrics={[metric]} 
                isHighValue={true}
              />
            ))}
          </div>
        </div>

        {/* Top Campaigns - Moved Up */}
        <CampaignTable />

        {/* Conversion Funnel */}
        <SVGFunnel />

        {/* Sales Team Performance */}
        <SalesTeamTable />

        {/* Marketing Lead Sources */}
        <MarketingSourcesTable timeline={activeTimeline} />

        {/* Facebook Ad Accounts */}
        <FacebookAccountsTable timeline={activeTimeline} />

        {/* Geography & Campaign Performance */}
        <GeographyTable />

        {/* Events & Bootcamps Schedule */}
        <EventsSchedule />

        {/* Kapi Enterprise Pipeline */}
        <KapiLeadsTable timeline={activeTimeline} />

        {/* Lead Segmentation */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            🔥 LEAD SEGMENTATION (Today) - Quality Focus
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">🎯</span>
                <div>
                  <div className="font-medium text-gray-900">Hot: 3 leads (25% conv rate)</div>
                  <div className="text-sm text-gray-600">← FOCUS HERE! Scale this!</div>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">🟡</span>
                <div>
                  <div className="font-medium text-gray-900">Warm: 6 leads (12% conv rate)</div>
                  <div className="text-sm text-gray-600">Good potential</div>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">⚪</span>
                <div>
                  <div className="font-medium text-gray-900">Medium: 3 leads (4% conv rate)</div>
                  <div className="text-sm text-gray-600">Review targeting</div>
                </div>
              </div>
            </div>
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 mt-1">💡</span>
                <div className="text-sm text-blue-700">
                  <div className="font-medium">Action: Hot leads = ₹2,500 COCA vs ₹8,100 for Medium</div>
                  <div>Recommendation: Increase Hot segment budget by 200%</div>
                </div>
              </div>
            </div>
          </div>
        </div>


        {/* Sequence Performance */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            📧 SEQUENCE PERFORMANCE (Response Rate Optimization)
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="text-sm">Day 1 Email: 45% open, 12% reply ✅ (Above 40% target)</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="text-sm">Day 2 SMS: 38% response 📱 (Above 30% target)</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
              <span className="text-sm">Day 3 Email: 28% open, 8% reply 📧 (Below 35% target) ⚠️</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="text-sm">Day 5 WhatsApp: 52% response 💬 (Excellent!)</span>
            </div>
            
            <div className="mt-4 p-3 bg-orange-50 rounded-lg">
              <h4 className="font-medium text-orange-800 mb-2">🎯 Friction Points:</h4>
              <ul className="text-sm text-orange-700 space-y-1">
                <li>• Day 3 email underperforming - A/B test subject lines</li>
                <li>• WhatsApp shows highest engagement - increase allocation</li>
                <li>• SMS response drops 15% after 6pm - optimize send times</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Revenue & Efficiency */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            💰 REVENUE & EFFICIENCY (Cost Reduction Focus)
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className="text-center">
              <div className="text-2xl font-semibold text-gray-900">₹18,30,000</div>
              <div className="text-sm text-gray-600">Revenue (30d) 📈 (+22% vs last month)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-gray-900">₹2,806</div>
              <div className="text-sm text-gray-600">COCA per customer 🎯 (Target: ₹2,500 - Close!)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-gray-900">3.2:1</div>
              <div className="text-sm text-gray-600">LTV:CAC Ratio 🏆 (Above 3:1 target)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-gray-900">14 days</div>
              <div className="text-sm text-gray-600">Payback ⚡ (Excellent - under 30d target)</div>
            </div>
          </div>
          
          <div className="bg-red-50 rounded-lg p-4">
            <h4 className="font-medium text-red-800 mb-2">🚨 Cost Reduction Opportunities:</h4>
            <ul className="text-sm text-red-700 space-y-1">
              <li>• Pause Junior_AI_Basic (₹8,100 COCA vs ₹2,806 avg)</li>
              <li>• Scale Tech_Lead_LLM (₹3,200 COCA - 43% below average)</li>
              <li>• Geographic: US West shows 67% lower cost per conversion</li>
            </ul>
          </div>
        </div>

        {/* Urgent Actions */}
        <UrgentActions />
      </div>
    </Layout>
    </ProtectedRoute>
  );
}