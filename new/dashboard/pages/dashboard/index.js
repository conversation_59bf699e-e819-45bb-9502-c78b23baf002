import { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Layout from '@/components/layout/Layout';
import FacebookAccountsTable from '@/components/FacebookAccountsTable';
import CampaignTable from '@/components/CampaignTable';
import LeadAdsTable from '@/components/LeadAdsTable';
import FacebookTimelineSelector from '@/components/FacebookTimelineSelector';
import SectionHeader from '@/components/SectionHeader';

export default function Dashboard() {
  const [activeTimeline, setActiveTimeline] = useState('7');

  const handleTimelineChange = (timeline) => {
    setActiveTimeline(timeline);
  };

  return (
    <ProtectedRoute>
      <Layout title="Facebook Advertising Dashboard">
        <div className="space-y-6">
          {/* Time Period Selector */}
          <div className="flex justify-end">
            <FacebookTimelineSelector 
              activeTimeline={activeTimeline}
              onTimelineChange={handleTimelineChange}
            />
          </div>

          {/* Facebook Account Health */}
          <div>
            <FacebookAccountsTable timePeriod={activeTimeline} />
          </div>

          {/* Top Campaigns */}
          <div>
            <CampaignTable timePeriod={activeTimeline} />
          </div>

          {/* Lead Generation Ads */}
          <div>
            <LeadAdsTable timePeriod={activeTimeline} />
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">⚡ Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <a 
                href="/leads" 
                className="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
              >
                <div className="text-2xl mr-3">👥</div>
                <div>
                  <div className="font-medium text-gray-900">Lead Management</div>
                  <div className="text-sm text-gray-600">Access detailed lead information</div>
                </div>
              </a>
              
              <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl mr-3">🔄</div>
                <div>
                  <div className="font-medium text-gray-900">Auto Health Checks</div>
                  <div className="text-sm text-gray-600">Runs daily via cron jobs</div>
                </div>
              </div>
              
              <div className="flex items-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl mr-3">📈</div>
                <div>
                  <div className="font-medium text-gray-900">Real-time Data</div>
                  <div className="text-sm text-gray-600">Updated automatically</div>
                </div>
              </div>
            </div>
          </div>

          {/* Data Freshness Indicator */}
          <div className="text-center text-sm text-gray-500">
            Dashboard data is collected automatically via health checks and stored in local database.
            <br />
            For detailed lead information and sales tools, visit the <a href="/leads" className="text-blue-600 hover:text-blue-800">Lead Management</a> page.
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}