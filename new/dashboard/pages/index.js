import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import LandingPage from '../components/LandingPage';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, isHydrated, user } = useAuth();

  useEffect(() => {
    // Only redirect after hydration is complete
    console.log('🔄 Redirect check:', { isHydrated, isAuthenticated, user });
    
    if (isHydrated && isAuthenticated && user) {
      // Role-based routing
      const userRole = user.role || localStorage.getItem('role');
      
      if (userRole === 'admin') {
        router.push('/dashboard');
      } else if (['sales', 'sales_rep', 'executive', 'bd'].includes(userRole)) {
        router.push('/leads');
      } else if (userRole === 'student') {
        router.push('/learn');
      } else {
        // Default to dashboard for unknown roles
        router.push('/dashboard');
      }
    }
  }, [isAuthenticated, isHydrated, user, router]);

  // Show loading during hydration
  if (!isHydrated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show landing page for non-authenticated users
  if (!isAuthenticated) {
    return <LandingPage />;
  }

  // Show loading while redirecting authenticated users
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Modern AI Pro
        </h1>
        <p className="text-gray-600">Redirecting to dashboard...</p>
      </div>
    </div>
  );
}