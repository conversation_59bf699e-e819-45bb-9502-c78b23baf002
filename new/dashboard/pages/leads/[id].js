import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Layout from '@/components/layout/Layout';
import { authenticatedFetch } from '@/lib/auth/clientAuth';

export default function LeadDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const [lead, setLead] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [saving, setSaving] = useState(false);
  const [toast, setToast] = useState(null);
  const [assignableUsers, setAssignableUsers] = useState([]);
  const [usersLoading, setUsersLoading] = useState(true);
  const [editableFields, setEditableFields] = useState({
    full_name: '',
    email: '',
    phone: '',
    status: '',
    assigned_to: '',
    notes: ''
  });
  
  // SDR Contact Form State
  const [contactForm, setContactForm] = useState({
    channel: 'email',
    template: '',
    customMessage: ''
  });
  const [sendingMessage, setSendingMessage] = useState(false);

  const statusOptions = [
    { value: '', label: 'Not Set' },
    { value: 'auto-resp-1', label: 'Auto-Response #1' },
    { value: 'auto-resp-2', label: 'Auto-Response #2' },
    { value: 'auto-resp-3', label: 'Auto-Response #3' },
    { value: 'qualified', label: 'Qualified' },
    { value: 'contacted', label: 'Contacted' },
    { value: 'payment-sent', label: 'Payment Link Sent' },
    { value: 'enrolled', label: 'Enrolled' },
    { value: 'declined', label: 'Declined' }
  ];

  // Contact options
  const contactChannels = [
    { value: 'email', label: 'Email' }
    // Future: SMS, WhatsApp, etc.
  ];

  const messageTemplates = [
    { value: '', label: 'Select a template...' },
    { value: 'payment_link', label: 'Payment Link - Send workshop enrollment link' },
    { value: 'syllabus_link', label: 'Syllabus Link - Send course syllabus and details' },
    { value: 'follow_up_1', label: 'Follow-up #1 - Initial interest follow-up' },
    { value: 'follow_up_2', label: 'Follow-up #2 - Second follow-up attempt' },
    { value: 'follow_up_3', label: 'Follow-up #3 - Final follow-up before closing' },
    { value: 'workshop_reminder', label: 'Workshop Reminder - Upcoming session reminder' },
    { value: 'custom', label: 'Custom Message - Write your own message' }
  ];

  // Fetch assignable users only when needed
  const fetchAssignableUsers = async () => {
    if (assignableUsers.length > 1) return; // Already fetched
    
    try {
      setUsersLoading(true);
      const response = await authenticatedFetch('/api/users/assignable');
      const data = await response.json();
      
      if (data.success) {
        const userOptions = [
          { value: '', label: 'Unassigned' },
          ...data.users.map(user => ({
            value: user.id.toString(),
            label: user.name
          }))
        ];
        setAssignableUsers(userOptions);
      } else {
        console.error('Failed to fetch assignable users:', data.error);
        // Fallback - create an option for the current assignee if available
        const currentAssigneeOption = lead?.assigned_to && lead?.assignee_name ? 
          [{ value: lead.assigned_to.toString(), label: lead.assignee_name }] : [];
        setAssignableUsers([
          { value: '', label: 'Unassigned' },
          ...currentAssigneeOption
        ]);
      }
    } catch (err) {
      console.error('Error fetching assignable users:', err);
      // Fallback - create an option for the current assignee if available
      const currentAssigneeOption = lead?.assigned_to && lead?.assignee_name ? 
        [{ value: lead.assigned_to.toString(), label: lead.assignee_name }] : [];
      setAssignableUsers([
        { value: '', label: 'Unassigned' },
        ...currentAssigneeOption
      ]);
    } finally {
      setUsersLoading(false);
    }
  };
  
  // Initialize basic assignee options based on current lead AND fetch all users
  useEffect(() => {
    if (lead) {
      const currentAssigneeOption = lead.assigned_to && lead.assignee_name ? 
        [{ value: lead.assigned_to.toString(), label: lead.assignee_name }] : [];
      
      setAssignableUsers([
        { value: '', label: 'Unassigned' },
        ...currentAssigneeOption
      ]);
      setUsersLoading(false);
      
      // Also fetch all assignable users immediately
      fetchAssignableUsers();
    }
  }, [lead]);

  useEffect(() => {
    if (!id) return;

    const fetchLead = async () => {
      try {
        setLoading(true);
        const response = await authenticatedFetch(`/api/leads/${id}`);
        const data = await response.json();

        if (data.success) {
          setLead(data.lead);
          // Initialize editable fields
          setEditableFields({
            full_name: data.lead.full_name || '',
            email: data.lead.email || '',
            phone: data.lead.phone || '',
            status: data.lead.status || '',
            assigned_to: data.lead.assigned_to ? data.lead.assigned_to.toString() : '',
            notes: data.lead.notes || ''
          });
        } else {
          setError(data.error || 'Failed to fetch lead details');
        }
      } catch (err) {
        setError('Network error: ' + err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchLead();
  }, [id]);

  const handleSave = async () => {
    try {
      setSaving(true);
      const response = await authenticatedFetch(`/api/leads/${id}`, {
        method: 'PUT',
        body: JSON.stringify(editableFields)
      });

      const data = await response.json();

      if (data.success) {
        // Update the lead state with new values
        setLead(prev => ({
          ...prev,
          full_name: editableFields.full_name,
          email: editableFields.email,
          phone: editableFields.phone,
          status: editableFields.status,
          assigned_to: editableFields.assigned_to,
          notes: editableFields.notes
        }));
        
        // Show success toast
        setToast({
          type: 'success',
          message: 'Lead updated successfully!'
        });
        
        // Clear toast after 3 seconds
        setTimeout(() => setToast(null), 3000);
      } else {
        setToast({
          type: 'error',
          message: data.error || 'Failed to update lead'
        });
        setTimeout(() => setToast(null), 5000);
      }
    } catch (err) {
      setToast({
        type: 'error',
        message: 'Network error: ' + err.message
      });
      setTimeout(() => setToast(null), 5000);
    } finally {
      setSaving(false);
    }
  };

  const handleFieldChange = (field, value) => {
    setEditableFields(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // SDR Contact Form Handlers
  const handleContactFormChange = (field, value) => {
    setContactForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSendMessage = async () => {
    if (!contactForm.template) {
      setToast({
        type: 'error',
        message: 'Please select a message template'
      });
      setTimeout(() => setToast(null), 3000);
      return;
    }

    if (contactForm.template === 'custom' && !contactForm.customMessage.trim()) {
      setToast({
        type: 'error',
        message: 'Please enter a custom message'
      });
      setTimeout(() => setToast(null), 3000);
      return;
    }

    try {
      setSendingMessage(true);
      
      // TODO: Replace with actual API call to communication service
      const response = await authenticatedFetch('/api/communications/send', {
        method: 'POST',
        body: JSON.stringify({
          leadId: lead.id,
          channel: contactForm.channel,
          template: contactForm.template,
          customMessage: contactForm.customMessage,
          recipient: {
            name: lead.full_name,
            email: lead.email,
            phone: lead.phone
          }
        })
      });

      const data = await response.json();

      if (data.success) {
        setToast({
          type: 'success',
          message: `Message sent successfully via ${contactForm.channel}!`
        });
        
        // Reset form
        setContactForm({
          channel: 'email',
          template: '',
          customMessage: ''
        });
        
        // Update lead status if needed
        if (contactForm.template === 'payment_link') {
          handleFieldChange('status', 'payment-sent');
        } else if (['follow_up_1', 'follow_up_2', 'follow_up_3'].includes(contactForm.template)) {
          handleFieldChange('status', 'contacted');
        }
      } else {
        throw new Error(data.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setToast({
        type: 'error',
        message: `Failed to send message: ${error.message}`
      });
    } finally {
      setSendingMessage(false);
      setTimeout(() => setToast(null), 5000);
    }
  };

  const getSourceBadge = (source) => {
    const badges = {
      'fb_ads': 'bg-blue-100 text-blue-800',
      'google_ads': 'bg-red-100 text-red-800',
      'referral': 'bg-purple-100 text-purple-800',
      'organic': 'bg-green-100 text-green-800',
      'linkedin': 'bg-indigo-100 text-indigo-800',
      'website': 'bg-gray-100 text-gray-800',
      'other': 'bg-yellow-100 text-yellow-800'
    };
    return badges[source] || 'bg-gray-100 text-gray-800';
  };

  const getSourceLabel = (source) => {
    const labels = {
      'fb_ads': 'Facebook Ads',
      'google_ads': 'Google Ads',
      'referral': 'Referral',
      'organic': 'Organic',
      'linkedin': 'LinkedIn',
      'website': 'Website',
      'other': 'Other'
    };
    return labels[source] || source;
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <Layout title="Lead Details - AI Bootcamp">
          <div className="max-w-4xl mx-auto">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <Layout title="Lead Details - AI Bootcamp">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
              <div className="text-red-500 text-4xl mb-4">⚠️</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Lead</h3>
              <p className="text-red-600 mb-4">{error}</p>
              <button 
                onClick={() => router.back()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 mr-2"
              >
                Go Back
              </button>
              <button 
                onClick={() => router.reload()}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Retry
              </button>
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  if (!lead) {
    return (
      <ProtectedRoute>
        <Layout title="Lead Details - AI Bootcamp">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
              <div className="text-gray-400 text-4xl mb-4">👤</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Lead Not Found</h3>
              <p className="text-gray-600 mb-4">The lead you're looking for doesn't exist or has been removed.</p>
              <button 
                onClick={() => router.push('/leads')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Back to Leads
              </button>
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout title={`${lead.full_name} - Lead Details`}>
        {/* Toast Notification */}
        {toast && (
          <div className={`fixed top-4 right-4 z-50 max-w-sm w-full ${
            toast.type === 'success' ? 'bg-green-500' : 'bg-red-500'
          } text-white px-6 py-4 rounded-lg shadow-lg transform transition-all duration-300`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {toast.type === 'success' ? (
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{toast.message}</p>
              </div>
              <div className="ml-auto pl-3">
                <button
                  onClick={() => setToast(null)}
                  className="inline-flex text-white hover:text-gray-200 focus:outline-none focus:text-gray-200"
                >
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}
        
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-3">
                <button 
                  onClick={() => router.back()}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ← Back
                </button>
                <h1 className="text-2xl font-bold text-gray-900">{lead.full_name}</h1>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getSourceBadge(lead.lead_source)}`}>
                  {getSourceLabel(lead.lead_source)}
                </span>
              </div>
              <p className="text-gray-600 mt-1">Lead Details and Form Responses</p>
            </div>
            <div className="text-sm text-gray-500">
              Created: {lead.created_time_formatted}
            </div>
          </div>

          {/* Editable Contact Information */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Contact Information</h2>
              <button
                onClick={handleSave}
                disabled={saving}
                className={`px-4 py-2 rounded-lg text-white font-medium ${
                  saving 
                    ? 'bg-gray-400 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                <input
                  type="text"
                  value={editableFields.full_name}
                  onChange={(e) => handleFieldChange('full_name', e.target.value)}
                  className="input-secondary"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  value={editableFields.email}
                  onChange={(e) => handleFieldChange('email', e.target.value)}
                  className="input-secondary"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                <input
                  type="tel"
                  value={editableFields.phone}
                  onChange={(e) => handleFieldChange('phone', e.target.value)}
                  className="input-secondary"
                />
              </div>
            </div>
          </div>

          {/* Lead Management */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Lead Management</h2>
            
            {/* Current Assignment Display */}
            {lead.assigned_to && lead.assignee_name && (
              <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center space-x-2">
                  <svg className="h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <div className="text-sm font-medium text-blue-900">
                      Currently Assigned To
                    </div>
                    <div className="text-lg font-semibold text-blue-800">
                      {lead.assignee_name}
                    </div>
                    {lead.assignee_role && (
                      <div className="text-xs text-blue-600 capitalize">
                        {lead.assignee_role}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={editableFields.status}
                  onChange={(e) => handleFieldChange('status', e.target.value)}
                  className="select-primary"
                >
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Assigned To</label>
                <select
                  value={editableFields.assigned_to}
                  onChange={(e) => handleFieldChange('assigned_to', e.target.value)}
                  onFocus={fetchAssignableUsers}
                  className="select-primary"
                >
                  {assignableUsers.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
              <textarea
                value={editableFields.notes}
                onChange={(e) => handleFieldChange('notes', e.target.value)}
                rows="4"
                className="input-secondary"
                placeholder="Add notes about this lead..."
              />
            </div>
          </div>

          {/* SDR Contact Section */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <span className="mr-2">📧</span>
                SDR Contact Lead
              </h2>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">Contact via:</span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {lead.email ? 'Email Available' : 'No Email'}
                </span>
              </div>
            </div>

            {!lead.email ? (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-800">
                      <strong>No Email Available</strong> - Update the lead's contact information to enable messaging.
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Channel Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Contact Channel
                    </label>
                    <select
                      value={contactForm.channel}
                      onChange={(e) => handleContactFormChange('channel', e.target.value)}
                      className="select-primary"
                    >
                      {contactChannels.map(channel => (
                        <option key={channel.value} value={channel.value}>
                          {channel.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Template Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Message Template
                    </label>
                    <select
                      value={contactForm.template}
                      onChange={(e) => handleContactFormChange('template', e.target.value)}
                      className="select-primary"
                    >
                      {messageTemplates.map(template => (
                        <option key={template.value} value={template.value}>
                          {template.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Custom Message Field (shown when 'custom' template is selected) */}
                {contactForm.template === 'custom' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Custom Message
                    </label>
                    <textarea
                      value={contactForm.customMessage}
                      onChange={(e) => handleContactFormChange('customMessage', e.target.value)}
                      rows="4"
                      className="input-secondary"
                      placeholder="Write your custom message here..."
                    />
                  </div>
                )}

                {/* Template Preview */}
                {contactForm.template && contactForm.template !== 'custom' && (
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="text-sm font-medium text-gray-700 mb-2">Preview:</div>
                    <div className="text-sm text-gray-600">
                      {contactForm.template === 'payment_link' && (
                        <p>Hi {lead.full_name}, here's your workshop enrollment link with payment options. Complete your registration to secure your spot!</p>
                      )}
                      {contactForm.template === 'syllabus_link' && (
                        <p>Hi {lead.full_name}, thanks for your interest! Here's the detailed syllabus and what you'll learn in our AI workshop.</p>
                      )}
                      {contactForm.template === 'follow_up_1' && (
                        <p>Hi {lead.full_name}, I wanted to follow up on your interest in our AI workshop. Do you have any questions I can help answer?</p>
                      )}
                      {contactForm.template === 'follow_up_2' && (
                        <p>Hi {lead.full_name}, just checking in again about the AI workshop. The next cohort is filling up quickly - would you like to secure your spot?</p>
                      )}
                      {contactForm.template === 'follow_up_3' && (
                        <p>Hi {lead.full_name}, this is my final follow-up about the AI workshop. I'd hate for you to miss this opportunity. Let me know if you're still interested!</p>
                      )}
                      {contactForm.template === 'workshop_reminder' && (
                        <p>Hi {lead.full_name}, your AI workshop session is coming up soon! Here are the details and what to prepare beforehand.</p>
                      )}
                    </div>
                  </div>
                )}

                {/* Send Button */}
                <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-500">
                    Recipient: <strong>{lead.full_name}</strong> ({lead.email})
                  </div>
                  <button
                    onClick={handleSendMessage}
                    disabled={sendingMessage || !contactForm.template}
                    className={`px-6 py-2 rounded-lg text-white font-medium flex items-center space-x-2 ${
                      sendingMessage || !contactForm.template
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700'
                    }`}
                  >
                    {sendingMessage ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Sending...</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                        <span>Send Message</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Form Data */}
          {lead.formData && lead.formData.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Form Responses</h2>
              <div className="space-y-4">
                {lead.formData.map((field, index) => (
                  <div key={index} className="border-b border-gray-100 pb-4 last:border-b-0 last:pb-0">
                    <div className="flex flex-col md:flex-row md:items-start md:space-x-4">
                      <div className="md:w-1/3">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {field.displayQuestion}
                        </label>
                        <div className="text-xs text-gray-500 font-mono">
                          {field.question}
                        </div>
                      </div>
                      <div className="md:w-2/3 mt-2 md:mt-0">
                        <div className="text-sm text-gray-900 bg-gray-50 rounded-lg p-3">
                          <span className="text-gray-900">{field.answer}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Payment History */}
          {(lead.paymentHistory && lead.paymentHistory.length > 0) || lead.stripe_payment_id && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                💳 Payment History
                {lead.customerMetrics && (
                  <span className="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {lead.customerMetrics.customer_type || 'Customer'}
                  </span>
                )}
              </h2>

              {/* Customer Metrics */}
              {lead.customerMetrics && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <div className="text-sm font-medium text-gray-500">Total Spent</div>
                    <div className="text-lg font-bold text-gray-900">
                      ${lead.customerMetrics.total_spent?.toFixed(2) || '0.00'}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">Total Payments</div>
                    <div className="text-lg font-bold text-gray-900">
                      {lead.customerMetrics.total_payments || 1}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">First Purchase</div>
                    <div className="text-sm text-gray-900">
                      {lead.customerMetrics.first_purchase ? new Date(lead.customerMetrics.first_purchase).toLocaleDateString() : 'N/A'}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">Last Purchase</div>
                    <div className="text-sm text-gray-900">
                      {lead.customerMetrics.last_purchase ? new Date(lead.customerMetrics.last_purchase).toLocaleDateString() : 'N/A'}
                    </div>
                  </div>
                </div>
              )}

              {/* Payment History Table */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Course
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Payment ID
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {/* Show consolidated payment history if available */}
                    {lead.paymentHistory && lead.paymentHistory.length > 0 ? (
                      lead.paymentHistory.map((payment, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {new Date(payment.date).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            ${payment.amount?.toFixed(2) || '0.00'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              payment.status === 'Paid' || payment.status === 'succeeded' 
                                ? 'bg-green-100 text-green-800' 
                                : payment.status === 'pending' 
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {payment.status || 'Unknown'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {payment.course || 'Unknown'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                            {payment.payment_id ? payment.payment_id.substring(0, 20) + '...' : 'N/A'}
                          </td>
                        </tr>
                      ))
                    ) : (
                      /* Show single payment if no consolidated history */
                      lead.stripe_payment_id && (
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {lead.created_time_formatted}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            ${lead.payment_amount?.toFixed(2) || '0.00'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              lead.payment_status === 'Paid' || lead.payment_status === 'succeeded' 
                                ? 'bg-green-100 text-green-800' 
                                : lead.payment_status === 'pending' 
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {lead.payment_status || 'Unknown'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Unknown
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                            {lead.stripe_payment_id ? lead.stripe_payment_id.substring(0, 20) + '...' : 'N/A'}
                          </td>
                        </tr>
                      )
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Source Information - Moved to bottom */}
          {lead.campaign_name && lead.campaign_name !== 'N/A' && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Source Details</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Campaign</label>
                  <div className="text-sm text-gray-900">{lead.campaign_name}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Account</label>
                  <div className="text-sm text-gray-900">{lead.account_name || 'N/A'}</div>
                </div>
                {lead.facebook_lead_id && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Facebook Lead ID</label>
                    <div className="text-sm text-gray-900 font-mono">{lead.facebook_lead_id}</div>
                  </div>
                )}
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Lead ID</label>
                  <div className="text-sm text-gray-900">#{lead.id}</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Layout>
    </ProtectedRoute>
  );
}