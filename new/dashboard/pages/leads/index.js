import { useState } from 'react';
import { useRouter } from 'next/router';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Layout from '@/components/layout/Layout';
import SearchableTable from '@/components/SearchableTable';
import AddLeadDialog from '@/components/forms/AddLeadDialog';
import { leadsTableConfig, leadsStatsRenderer } from '@/lib/config/leadsTableConfig';

export default function LeadsPage() {
  const router = useRouter();
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleLeadAdded = () => {
    // Trigger refresh of the leads table
    setRefreshTrigger(prev => prev + 1);
  };

  const handleRowClick = (row) => {
    router.push(`/leads/${row.id}`);
  };

  return (
    <ProtectedRoute>
      <Layout title="Lead Management - AI Bootcamp">
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Prospect Management</h1>
              <p className="text-gray-600 mt-1">Active prospects who haven't become customers yet</p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowAddDialog(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Add Prospect</span>
              </button>
              <div className="text-sm text-gray-500">
                Real-time data from database
              </div>
            </div>
          </div>

          {/* Searchable Leads Table */}
          <SearchableTable
            key={refreshTrigger}
            {...leadsTableConfig}
            onRowClick={handleRowClick}
            renderStats={leadsStatsRenderer}
          />

          {/* Add Lead Dialog */}
          <AddLeadDialog 
            isOpen={showAddDialog}
            onClose={() => setShowAddDialog(false)}
            onLeadAdded={handleLeadAdded}
          />
        </div>
      </Layout>
    </ProtectedRoute>
  );
}