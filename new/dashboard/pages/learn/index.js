import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import ProtectedRoute from '../../components/auth/ProtectedRoute';
import Layout from '../../components/layout/Layout';
import { useAuth } from '../../contexts/AuthContext';

// Student Dashboard Component
const StudentDashboard = ({ user }) => {
  const router = useRouter();
  const [enrolledWorkshops, setEnrolledWorkshops] = useState([]);
  const [certificates, setCertificates] = useState([]);
  const [progress, setProgress] = useState({});

  // Mock data - would come from API based on user
  useEffect(() => {
    // Simulate API calls
    setEnrolledWorkshops([
      {
        id: 1,
        title: 'AI Essentials for Professionals',
        status: 'completed',
        progress: 100,
        startDate: '2024-01-15',
        endDate: '2024-01-17',
        certificate: true,
        recordingsAvailable: true
      },
      {
        id: 2,
        title: 'Advanced Machine Learning',
        status: 'in-progress',
        progress: 65,
        startDate: '2024-02-01',
        endDate: '2024-02-03',
        nextSession: '2024-02-02T10:00:00Z'
      },
      {
        id: 3,
        title: 'Deep Learning Fundamentals',
        status: 'upcoming',
        progress: 0,
        startDate: '2024-03-01',
        endDate: '2024-03-03'
      }
    ]);

    setCertificates([
      {
        id: 1,
        workshopTitle: 'AI Essentials for Professionals',
        issueDate: '2024-01-18',
        certificateUrl: '/certificates/ai-essentials-certificate.pdf'
      }
    ]);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'upcoming': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const downloadCertificate = (certificateUrl, workshopTitle) => {
    // Create a temporary link to trigger download
    const link = document.createElement('a');
    link.href = certificateUrl;
    link.download = `${workshopTitle.replace(/\s+/g, '_')}_Certificate.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const navigateToWorkshop = (workshopId) => {
    router.push(`/learn/workshop/${workshopId}`);
  };

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 text-white">
        <h1 className="text-3xl font-bold mb-2">
          Welcome back, {user?.firstName || 'Student'}! 👋
        </h1>
        <p className="text-purple-100">
          Continue your AI learning journey. You're making great progress!
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-bold text-sm">✓</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Completed</p>
              <p className="text-2xl font-bold text-gray-900">
                {enrolledWorkshops.filter(w => w.status === 'completed').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-bold text-sm">⏳</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">In Progress</p>
              <p className="text-2xl font-bold text-gray-900">
                {enrolledWorkshops.filter(w => w.status === 'in-progress').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 font-bold text-sm">📅</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Upcoming</p>
              <p className="text-2xl font-bold text-gray-900">
                {enrolledWorkshops.filter(w => w.status === 'upcoming').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600 font-bold text-sm">🏆</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Certificates</p>
              <p className="text-2xl font-bold text-gray-900">{certificates.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* My Workshops */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">My Workshops</h2>
          <p className="text-gray-600">Track your learning progress and access materials</p>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {enrolledWorkshops.map((workshop) => (
              <div key={workshop.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h3 className="font-semibold text-gray-900">{workshop.title}</h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(workshop.status)}`}>
                        {workshop.status.replace('-', ' ').toUpperCase()}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {formatDate(workshop.startDate)} - {formatDate(workshop.endDate)}
                    </p>
                    
                    {/* Progress Bar */}
                    {workshop.status !== 'upcoming' && (
                      <div className="mt-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Progress</span>
                          <span className="font-medium">{workshop.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${workshop.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    {workshop.status === 'completed' && workshop.certificate && (
                      <button 
                        onClick={() => downloadCertificate('/certificates/ai-essentials-certificate.pdf', workshop.title)}
                        className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
                      >
                        View Certificate
                      </button>
                    )}
                    {workshop.recordingsAvailable && (
                      <button 
                        onClick={() => navigateToWorkshop(workshop.id)}
                        className="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors"
                      >
                        Recordings
                      </button>
                    )}
                    {workshop.status === 'in-progress' && (
                      <button 
                        onClick={() => navigateToWorkshop(workshop.id)}
                        className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                      >
                        Continue Learning
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Certificates Section */}
      {certificates.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900">My Certificates</h2>
            <p className="text-gray-600">Download and share your achievements</p>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {certificates.map((cert) => (
                <div key={cert.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900">{cert.workshopTitle}</h3>
                      <p className="text-sm text-gray-600">Issued: {formatDate(cert.issueDate)}</p>
                    </div>
                    <button 
                      onClick={() => downloadCertificate(cert.certificateUrl, cert.workshopTitle)}
                      className="px-3 py-1 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                    >
                      Download
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Quick Access Links */}
      <div className="mt-8 bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">Quick Access</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => router.push('/workshops')}
            className="flex items-center space-x-3 p-3 bg-gray-50 rounded border border-gray-200 hover:bg-gray-100 transition-colors text-left"
          >
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <span className="text-purple-600 text-lg">📚</span>
            </div>
            <div>
              <p className="font-medium text-gray-900">All Workshops</p>
              <p className="text-xs text-gray-500">Browse & enroll</p>
            </div>
          </button>
          
          <button
            onClick={() => router.push('/community')}
            className="flex items-center space-x-3 p-3 bg-gray-50 rounded border border-gray-200 hover:bg-gray-100 transition-colors text-left"
          >
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <span className="text-blue-600 text-lg">💬</span>
            </div>
            <div>
              <p className="font-medium text-gray-900">Community</p>
              <p className="text-xs text-gray-500">Join 2,500+ alumni</p>
            </div>
          </button>
          
          <button
            onClick={() => router.push('/welcome')}
            className="flex items-center space-x-3 p-3 bg-gray-50 rounded border border-gray-200 hover:bg-gray-100 transition-colors text-left"
          >
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <span className="text-green-600 text-lg">🎯</span>
            </div>
            <div>
              <p className="font-medium text-gray-900">Welcome</p>
              <p className="text-xs text-gray-500">Celebrate progress</p>
            </div>
          </button>

          {user?.role && ['admin', 'teacher', 'executive'].includes(user.role) && (
            <button
              onClick={() => router.push('/teach/feedback-tools')}
              className="flex items-center space-x-3 p-3 bg-orange-50 rounded border border-orange-200 hover:bg-orange-100 transition-colors text-left"
            >
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <span className="text-orange-600 text-lg">📊</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">Feedback Tools</p>
                <p className="text-xs text-gray-500">Instructor tools</p>
              </div>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default function Learn() {
  const { user } = useAuth();

  return (
    <ProtectedRoute>
      <Layout title="My Learning Journey">
        <StudentDashboard user={user} />
      </Layout>
    </ProtectedRoute>
  );
}