import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import ProtectedRoute from '../../../components/auth/ProtectedRoute';
import Layout from '../../../components/layout/Layout';
import { useAuth } from '../../../contexts/AuthContext';
import { FaPlay, FaDownload, FaCheckCircle, FaClock, FaCalendar } from 'react-icons/fa';

const WorkshopDetail = () => {
  const router = useRouter();
  const { id } = router.query;
  const { user } = useAuth();
  const [workshop, setWorkshop] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (id) {
      // Mock workshop data - would come from API
      const mockWorkshop = {
        id: parseInt(id),
        title: 'AI Essentials for Professionals',
        description: 'Comprehensive introduction to AI technologies, machine learning fundamentals, and practical applications in business.',
        instructor: 'Dr. <PERSON>',
        instructor<PERSON><PERSON>: 'AI researcher with 10+ years experience in machine learning and deep learning applications.',
        status: 'completed',
        progress: 100,
        startDate: '2024-01-15',
        endDate: '2024-01-17',
        duration: '3 days',
        certificate: true,
        sessions: [
          {
            id: 1,
            title: 'Introduction to AI and Machine Learning',
            date: '2024-01-15',
            duration: '2 hours',
            completed: true,
            recordingUrl: '/recordings/session-1.mp4',
            materials: [
              { name: 'Session 1 Slides', url: '/materials/session-1-slides.pdf' },
              { name: 'Code Examples', url: '/materials/session-1-code.zip' },
              { name: 'Reading List', url: '/materials/session-1-reading.pdf' }
            ]
          },
          {
            id: 2,
            title: 'Deep Learning Fundamentals',
            date: '2024-01-16',
            duration: '2.5 hours',
            completed: true,
            recordingUrl: '/recordings/session-2.mp4',
            materials: [
              { name: 'Session 2 Slides', url: '/materials/session-2-slides.pdf' },
              { name: 'Neural Network Lab', url: '/materials/session-2-lab.ipynb' }
            ]
          },
          {
            id: 3,
            title: 'AI in Business Applications',
            date: '2024-01-17',
            duration: '2 hours',
            completed: true,
            recordingUrl: '/recordings/session-3.mp4',
            materials: [
              { name: 'Session 3 Slides', url: '/materials/session-3-slides.pdf' },
              { name: 'Case Studies', url: '/materials/case-studies.pdf' },
              { name: 'Final Project Template', url: '/materials/final-project.docx' }
            ]
          }
        ],
        assignments: [
          {
            id: 1,
            title: 'AI Use Case Analysis',
            description: 'Analyze a real-world AI application in your industry',
            dueDate: '2024-01-20',
            submitted: true,
            grade: 'A',
            feedback: 'Excellent analysis of AI applications in fintech. Great insights!'
          }
        ],
        certificateUrl: '/certificates/ai-essentials-certificate.pdf'
      };
      
      setWorkshop(mockWorkshop);
    }
  }, [id]);

  if (!workshop) {
    return (
      <ProtectedRoute>
        <Layout title="Loading Workshop...">
          <div className="flex items-center justify-center min-h-96">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const downloadCertificate = (certificateUrl, workshopTitle) => {
    // Create a temporary link to trigger download
    const link = document.createElement('a');
    link.href = certificateUrl;
    link.download = `${workshopTitle.replace(/\s+/g, '_')}_Certificate.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <ProtectedRoute>
      <Layout title={workshop.title}>
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold mb-2">{workshop.title}</h1>
                <p className="text-purple-100 mb-4">{workshop.description}</p>
                <div className="flex items-center space-x-6 text-sm">
                  <div className="flex items-center space-x-2">
                    <FaCalendar />
                    <span>{formatDate(workshop.startDate)} - {formatDate(workshop.endDate)}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FaClock />
                    <span>{workshop.duration}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span>Instructor: {workshop.instructor}</span>
                  </div>
                </div>
              </div>
              {workshop.certificate && workshop.status === 'completed' && (
                <button 
                  onClick={() => downloadCertificate(workshop.certificateUrl, workshop.title)}
                  className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-md font-medium transition-colors flex items-center space-x-2"
                >
                  <FaDownload />
                  <span>Download Certificate</span>
                </button>
              )}
            </div>
          </div>

          {/* Progress Bar */}
          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold">Course Progress</h3>
              <span className="text-sm font-medium">{workshop.progress}% Complete</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-green-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${workshop.progress}%` }}
              ></div>
            </div>
          </div>

          {/* Tabs */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {['overview', 'sessions', 'assignments', 'materials'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab
                        ? 'border-purple-500 text-purple-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.charAt(0).toUpperCase() + tab.slice(1)}
                  </button>
                ))}
              </nav>
            </div>

            <div className="p-6">
              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3">About this Workshop</h3>
                    <p className="text-gray-600 leading-relaxed">{workshop.description}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Instructor</h3>
                    <div className="flex items-start space-x-4">
                      <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                        <span className="text-purple-600 font-bold text-xl">
                          {workshop.instructor.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <h4 className="font-semibold">{workshop.instructor}</h4>
                        <p className="text-gray-600">{workshop.instructorBio}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Sessions Tab */}
              {activeTab === 'sessions' && (
                <div className="space-y-4">
                  {workshop.sessions.map((session, index) => (
                    <div key={session.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex-shrink-0">
                            {session.completed ? (
                              <FaCheckCircle className="w-6 h-6 text-green-500" />
                            ) : (
                              <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                                <span className="text-gray-500 text-sm">{index + 1}</span>
                              </div>
                            )}
                          </div>
                          <div>
                            <h4 className="font-semibold">{session.title}</h4>
                            <p className="text-sm text-gray-600">
                              {formatDate(session.date)} • {session.duration}
                            </p>
                          </div>
                        </div>
                        
                        {session.completed && session.recordingUrl && (
                          <button className="flex items-center space-x-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-md hover:bg-blue-200 transition-colors">
                            <FaPlay className="w-4 h-4" />
                            <span>Watch Recording</span>
                          </button>
                        )}
                      </div>
                      
                      {session.materials && session.materials.length > 0 && (
                        <div className="mt-4 pt-4 border-t">
                          <h5 className="font-medium mb-2">Session Materials:</h5>
                          <div className="flex flex-wrap gap-2">
                            {session.materials.map((material, idx) => (
                              <button
                                key={idx}
                                className="flex items-center space-x-1 text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-md hover:bg-gray-200 transition-colors"
                              >
                                <FaDownload className="w-3 h-3" />
                                <span>{material.name}</span>
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Assignments Tab */}
              {activeTab === 'assignments' && (
                <div className="space-y-4">
                  {workshop.assignments.map((assignment) => (
                    <div key={assignment.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold">{assignment.title}</h4>
                          <p className="text-gray-600 mt-1">{assignment.description}</p>
                          <p className="text-sm text-gray-500 mt-2">
                            Due: {formatDate(assignment.dueDate)}
                          </p>
                        </div>
                        <div className="text-right">
                          {assignment.submitted ? (
                            <div>
                              <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mb-2">
                                Submitted
                              </div>
                              {assignment.grade && (
                                <div className="text-lg font-bold text-green-600">
                                  Grade: {assignment.grade}
                                </div>
                              )}
                            </div>
                          ) : (
                            <button className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors">
                              Submit Assignment
                            </button>
                          )}
                        </div>
                      </div>
                      {assignment.feedback && (
                        <div className="mt-4 p-3 bg-blue-50 rounded-md">
                          <h5 className="font-medium text-blue-900">Instructor Feedback:</h5>
                          <p className="text-blue-800 mt-1">{assignment.feedback}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Materials Tab */}
              {activeTab === 'materials' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">All Course Materials</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {workshop.sessions.flatMap(session => 
                        session.materials?.map((material, idx) => (
                          <div key={`${session.id}-${idx}`} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium">{material.name}</h4>
                                <p className="text-sm text-gray-600">From: {session.title}</p>
                              </div>
                              <button className="bg-purple-100 text-purple-700 p-2 rounded-md hover:bg-purple-200 transition-colors">
                                <FaDownload className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        )) || []
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  );
};

export default WorkshopDetail;