import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../contexts/AuthContext';
import Login from '../components/auth/Login';

export default function LoginPage() {
  const router = useRouter();
  const { isAuthenticated, isHydrated, user } = useAuth();

  const getRedirectPath = (userRole) => {
    switch (userRole) {
      case 'sales':
        return '/leads';
      case 'admin':
        return '/dashboard';
      case 'student':
        return '/learn';
      default:
        return '/welcome';
    }
  };

  useEffect(() => {
    // If already authenticated after hydration, redirect based on role
    if (isHydrated && isAuthenticated && user?.role) {
      const redirectPath = getRedirectPath(user.role);
      router.push(redirectPath);
    }
  }, [isAuthenticated, isHydrated, user, router]);

  // Show loading during hydration
  if (!isHydrated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render login form if already authenticated
  if (isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Already Logged In
          </h1>
          <p className="text-gray-600">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  return <Login />;
}