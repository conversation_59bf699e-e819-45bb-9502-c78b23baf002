import { useRouter } from 'next/router';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Layout from '@/components/layout/Layout';
import SearchableTable from '@/components/SearchableTable';
import { paymentsTableConfig, paymentsStatsRenderer } from '@/lib/config/paymentsTableConfig';

export default function PaymentsPage() {
  const router = useRouter();

  const handleRowClick = (row) => {
    router.push(`/leads/${row.id}`);
  };

  return (
    <ProtectedRoute>
      <Layout title="Payments - Modern AI Pro">
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Payment Management</h1>
              <p className="text-gray-600 mt-1">Track and manage Stripe payments from workshop enrollments. Customer insights show unique vs repeat customers.</p>
            </div>
          </div>

          {/* Searchable Payments Table */}
          <SearchableTable
            {...paymentsTableConfig}
            onRowClick={handleRowClick}
            renderStats={paymentsStatsRenderer}
          />
        </div>
      </Layout>
    </ProtectedRoute>
  );
}