import { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Layout from '@/components/layout/Layout';
import ChangePassword from '@/components/forms/ChangePassword';
import { useAuth } from '@/contexts/AuthContext';

export default function SettingsPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');

  const tabs = [
    { id: 'profile', label: 'Profile', icon: '👤' },
    { id: 'security', label: 'Security', icon: '🔐' },
  ];

  return (
    <ProtectedRoute>
      <Layout title="Settings - Modern AI Pro">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600 mt-1">Manage your account settings and preferences</p>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Profile Information</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                  <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                    {user?.first_name || 'N/A'}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                  <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                    {user?.last_name || 'N/A'}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                    {user?.email || 'N/A'}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Username</label>
                  <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                    {user?.username || 'N/A'}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                  <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      user?.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                      user?.role === 'sales' ? 'bg-green-100 text-green-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {user?.role || 'N/A'}
                    </span>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Subscription</label>
                  <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                    {user?.subscription_tier || 'Basic'}
                  </div>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  <strong>Note:</strong> Profile information can only be updated by an administrator. 
                  Please contact your system administrator if you need to change your details.
                </p>
              </div>
            </div>
          )}

          {/* Security Tab */}
          {activeTab === 'security' && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Security Settings</h2>
              
              <div className="space-y-6">
                {/* Password Change Section */}
                <div className="border-b border-gray-200 pb-6">
                  <h3 className="text-md font-medium text-gray-900 mb-2">Change Password</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Update your password to keep your account secure. Use a strong password with 
                    uppercase letters, lowercase letters, numbers, and special characters.
                  </p>
                  
                  <ChangePassword showAsModal={false} />
                </div>
                
                {/* Account Info */}
                <div>
                  <h3 className="text-md font-medium text-gray-900 mb-2">Account Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Account Status</label>
                      <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email Verified</label>
                      <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          ✓ Verified
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Security Tips */}
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <h4 className="text-sm font-medium text-yellow-800 mb-2">🔒 Security Tips</h4>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>• Use a unique password that you don't use elsewhere</li>
                    <li>• Include uppercase letters, lowercase letters, numbers, and symbols</li>
                    <li>• Make your password at least 8 characters long</li>
                    <li>• Don't share your login credentials with others</li>
                    <li>• Log out when using shared or public computers</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </Layout>
    </ProtectedRoute>
  );
}