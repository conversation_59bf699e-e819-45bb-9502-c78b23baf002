import { useState } from 'react';
import ProtectedRoute from '../../components/auth/ProtectedRoute';
import Layout from '../../components/layout/Layout';
import BarChart from '../../components/BarChart';
import WordCloudChart from '../../components/WordCloudChart';
import SlideShow from '../../components/SlideShow';
import { useAuth } from '../../contexts/AuthContext';

export default function FeedbackToolsPage() {
  const { user } = useAuth();
  const [activeDemo, setActiveDemo] = useState('poll');

  // Check if user has instructor/admin privileges
  const hasInstructorAccess = user?.role && ['admin', 'teacher', 'executive'].includes(user.role);

  if (!hasInstructorAccess) {
    return (
      <ProtectedRoute>
        <Layout title="Feedback Tools | Modern AI Pro">
          <div className="p-5">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">Access Restricted</h3>
              <p className="text-yellow-700">You must be an instructor or admin to access the feedback tools.</p>
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout title="Feedback Tools | Modern AI Pro">
        <div className="max-w-6xl mx-auto p-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Live Workshop Feedback Tools</h1>
            <p className="text-gray-600">
              Interactive tools to gather real-time feedback during your workshop sessions
            </p>
          </div>

          {/* Tool Selection */}
          <div className="mb-6">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveDemo('poll')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeDemo === 'poll'
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Live Polling
                </button>
                <button
                  onClick={() => setActiveDemo('wordcloud')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeDemo === 'wordcloud'
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Word Cloud
                </button>
                <button
                  onClick={() => setActiveDemo('slideshow')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeDemo === 'slideshow'
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Presentation
                </button>
              </nav>
            </div>
          </div>

          {/* Demo Content */}
          <div className="space-y-6">
            {activeDemo === 'poll' && (
              <div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <h3 className="font-semibold text-blue-900 mb-2">Live Polling Demo</h3>
                  <p className="text-blue-800 text-sm">
                    This interactive poll updates in real-time as participants vote. Perfect for gauging understanding during workshops.
                  </p>
                </div>
                <BarChart 
                  question="Which deep learning framework do you prefer for production?"
                  choices={["TensorFlow", "PyTorch", "JAX", "Hugging Face"]}
                  autoUpdate={true}
                />
              </div>
            )}

            {activeDemo === 'wordcloud' && (
              <div>
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6">
                  <h3 className="font-semibold text-purple-900 mb-2">Word Cloud Demo</h3>
                  <p className="text-purple-800 text-sm">
                    Collect and visualize feedback words from participants. Great for sentiment analysis and quick feedback collection.
                  </p>
                </div>
                <WordCloudChart 
                  question="Describe your experience with this AI workshop in one word"
                  autoUpdate={true}
                  updateInterval={4000}
                />
              </div>
            )}

            {activeDemo === 'slideshow' && (
              <div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <h3 className="font-semibold text-green-900 mb-2">Presentation Integration</h3>
                  <p className="text-green-800 text-sm">
                    Embed Google Slides presentations directly in your workshop interface with full control features.
                  </p>
                </div>
                <SlideShow 
                  slidesLink="https://docs.google.com/presentation/d/1hXcru7QqAy7SkvVoNnODCfvb5o9R0wtf4Eik1xGSZJc"
                  height={400}
                />
              </div>
            )}
          </div>

          {/* Usage Instructions */}
          <div className="mt-12 bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">How to Use These Tools</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Live Polling</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Create multiple choice questions</li>
                  <li>• Share poll link with participants</li>
                  <li>• View results in real-time</li>
                  <li>• Export data for analysis</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Word Cloud</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Ask open-ended questions</li>
                  <li>• Collect text responses</li>
                  <li>• Visualize sentiment patterns</li>
                  <li>• Identify key themes</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Presentations</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Upload Google Slides</li>
                  <li>• Control presentation flow</li>
                  <li>• Auto-advance slides</li>
                  <li>• Engage with content</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}