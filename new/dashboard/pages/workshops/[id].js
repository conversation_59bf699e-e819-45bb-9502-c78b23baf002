import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Layout from '../../components/layout/Layout';
import { 
  FaArrowLeft, FaUsers, FaCalendar, FaDollarSign, 
  FaCertificate, FaVideo, FaFileAlt, FaEnvelope,
  FaCheck, FaTimes, FaExternalLinkAlt, FaDownload
} from 'react-icons/fa';

export default function WorkshopDetailPage() {
  const [workshop, setWorkshop] = useState(null);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('students');
  const router = useRouter();
  const { id } = router.query;

  useEffect(() => {
    if (id) {
      fetchWorkshopDetails();
    }
  }, [id]);

  const fetchWorkshopDetails = async () => {
    try {
      const response = await fetch(`/api/workshops?id=${id}`);
      const data = await response.json();
      setWorkshop(data.workshop);
      setStudents(data.students);
    } catch (error) {
      console.error('Error fetching workshop details:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getWorkshopStatus = (workshop) => {
    const now = new Date();
    const start = new Date(workshop.start_date);
    const end = new Date(workshop.end_date);
    
    if (!workshop.is_active) return { status: 'Inactive', color: 'gray' };
    if (now < start) return { status: 'Upcoming', color: 'blue' };
    if (now >= start && now <= end) return { status: 'Ongoing', color: 'green' };
    return { status: 'Completed', color: 'purple' };
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </Layout>
    );
  }

  if (!workshop) {
    return (
      <Layout>
        <div className="text-center py-12">
          <p className="text-gray-500">Workshop not found.</p>
        </div>
      </Layout>
    );
  }

  const status = getWorkshopStatus(workshop);
  const completionRate = workshop.enrolled_count > 0 ? 
    (workshop.completed_count / workshop.enrolled_count * 100).toFixed(1) : 0;

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="p-2 text-gray-600 hover:text-gray-800"
            >
              <FaArrowLeft />
            </button>
            <div>
              <div className="flex items-center space-x-3">
                <h1 className="text-3xl font-bold text-gray-900">{workshop.name}</h1>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  status.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                  status.color === 'green' ? 'bg-green-100 text-green-800' :
                  status.color === 'purple' ? 'bg-purple-100 text-purple-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {status.status}
                </span>
              </div>
              <p className="text-gray-600">{workshop.type}</p>
            </div>
          </div>
          
          <div className="flex space-x-3">
            {workshop.meeting_link && (
              <a
                href={workshop.meeting_link}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
              >
                <FaVideo /> <span>Join Meeting</span>
              </a>
            )}
            {workshop.materials_link && (
              <a
                href={workshop.materials_link}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
              >
                <FaFileAlt /> <span>Materials</span>
              </a>
            )}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <FaUsers className="text-blue-600 text-2xl mr-3" />
              <div>
                <p className="text-sm text-gray-600">Enrolled</p>
                <p className="text-2xl font-bold text-gray-900">
                  {workshop.enrolled_count}/{workshop.max_participants}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <FaCertificate className="text-green-600 text-2xl mr-3" />
              <div>
                <p className="text-sm text-gray-600">Completion Rate</p>
                <p className="text-2xl font-bold text-gray-900">{completionRate}%</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <FaDollarSign className="text-yellow-600 text-2xl mr-3" />
              <div>
                <p className="text-sm text-gray-600">Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${workshop.payment_revenue || workshop.total_revenue || 0}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <FaCalendar className="text-purple-600 text-2xl mr-3" />
              <div>
                <p className="text-sm text-gray-600">Duration</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Math.ceil((new Date(workshop.end_date) - new Date(workshop.start_date)) / (1000 * 60 * 60 * 24))} days
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {['students', 'overview', 'communications'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">Workshop Details</h3>
              <div className="space-y-3">
                <div>
                  <span className="font-medium text-gray-700">Start Date:</span>
                  <span className="ml-2 text-gray-600">{formatDate(workshop.start_date)}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">End Date:</span>
                  <span className="ml-2 text-gray-600">{formatDate(workshop.end_date)}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Instructor:</span>
                  <span className="ml-2 text-gray-600">{workshop.instructor || 'TBD'}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Timezone:</span>
                  <span className="ml-2 text-gray-600">{workshop.timezone}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Price:</span>
                  <span className="ml-2 text-gray-600">
                    ${workshop.price_usd} / ₹{workshop.price_inr}
                  </span>
                </div>
              </div>
              
              {workshop.description && (
                <div className="mt-6">
                  <h4 className="font-medium text-gray-700 mb-2">Description</h4>
                  <p className="text-gray-600">{workshop.description}</p>
                </div>
              )}
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
              <div className="space-y-3">
                {workshop.meeting_link && (
                  <a
                    href={workshop.meeting_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center">
                      <FaVideo className="text-green-600 mr-3" />
                      <span>Join Workshop Meeting</span>
                    </div>
                    <FaExternalLinkAlt className="text-gray-400" />
                  </a>
                )}
                
                {workshop.materials_link && (
                  <a
                    href={workshop.materials_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center">
                      <FaFileAlt className="text-blue-600 mr-3" />
                      <span>Access Materials</span>
                    </div>
                    <FaExternalLinkAlt className="text-gray-400" />
                  </a>
                )}
                
                <button className="flex items-center justify-between w-full p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center">
                    <FaEnvelope className="text-purple-600 mr-3" />
                    <span>Send Bulk Email</span>
                  </div>
                </button>
                
                <button className="flex items-center justify-between w-full p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center">
                    <FaDownload className="text-orange-600 mr-3" />
                    <span>Export Student List</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'students' && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Enrolled Customers ({students.length})</h3>
                <div className="text-sm text-gray-600">
                  {students.filter(s => s.payment_status === 'Paid').length} Paid • 
                  {students.filter(s => s.payment_status === 'Partial').length} Partial • 
                  {students.filter(s => s.payment_status === 'Unpaid').length} Unpaid
                </div>
              </div>
            </div>
            
            {students.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Email
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Payment
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {students.map((student) => (
                      <tr key={student.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {student.full_name}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{student.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                ${student.final_payment_amount || 0}
                              </div>
                              <div className="text-xs text-gray-500">
                                {student.payment_source}
                              </div>
                            </div>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              student.payment_status === 'Paid' ? 'bg-green-100 text-green-800' : 
                              student.payment_status === 'Partial' ? 'bg-yellow-100 text-yellow-800' : 
                              'bg-red-100 text-red-800'
                            }`}>
                              {student.payment_status}
                            </span>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500">No students enrolled yet.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'communications' && (
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">Communication History</h3>
            <div className="text-center py-12">
              <p className="text-gray-500">Communication tracking coming soon...</p>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}