import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Layout from '../../components/layout/Layout';
import { authenticatedFetch } from '../../lib/auth/clientAuth';
import { 
  FaPlus, FaEdit, FaTrash, FaUsers, FaCalendar, 
  FaDollarSign, FaCertificate, FaSearch, FaFilter,
  FaChevronDown, FaExternalLinkAlt, FaTh, FaList,
  FaSortUp, FaSortDown 
} from 'react-icons/fa';

export default function WorkshopsPage() {
  const [workshops, setWorkshops] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingWorkshop, setEditingWorkshop] = useState(null);
  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'
  const [sortField, setSortField] = useState('start_date');
  const [sortDirection, setSortDirection] = useState('desc');
  const router = useRouter();

  useEffect(() => {
    fetchWorkshops();
  }, []);

  const fetchWorkshops = async () => {
    try {
      const response = await authenticatedFetch('/api/workshops');
      const data = await response.json();
      setWorkshops(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching workshops:', error);
      setWorkshops([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  const handleCreateWorkshop = async (workshopData) => {
    try {
      const response = await authenticatedFetch('/api/workshops', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(workshopData)
      });
      
      if (response.ok) {
        fetchWorkshops();
        setShowCreateModal(false);
      }
    } catch (error) {
      console.error('Error creating workshop:', error);
    }
  };

  const handleUpdateWorkshop = async (id, updateData) => {
    try {
      const response = await authenticatedFetch(`/api/workshops?id=${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });
      
      if (response.ok) {
        fetchWorkshops();
        setEditingWorkshop(null);
      }
    } catch (error) {
      console.error('Error updating workshop:', error);
    }
  };

  const handleDeactivateWorkshop = async (id) => {
    if (!confirm('Are you sure you want to deactivate this workshop?')) return;
    
    try {
      const response = await authenticatedFetch(`/api/workshops?id=${id}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        fetchWorkshops();
      }
    } catch (error) {
      console.error('Error deactivating workshop:', error);
    }
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const filteredAndSortedWorkshops = (workshops || [])
    .filter(workshop => {
      const matchesSearch = workshop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           workshop.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           workshop.instructor?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || 
        (statusFilter === 'active' && workshop.is_active) ||
        (statusFilter === 'upcoming' && new Date(workshop.start_date) > new Date()) ||
        (statusFilter === 'ongoing' && new Date(workshop.start_date) <= new Date() && new Date(workshop.end_date) >= new Date()) ||
        (statusFilter === 'completed' && new Date(workshop.end_date) < new Date());
      
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      let aValue, bValue;
      
      switch (sortField) {
        case 'start_date':
          aValue = new Date(a.start_date);
          bValue = new Date(b.start_date);
          break;
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        case 'enrolled_count':
          aValue = a.enrolled_count || 0;
          bValue = b.enrolled_count || 0;
          break;
        case 'total_revenue':
          aValue = parseFloat(a.payment_revenue || a.total_revenue) || 0;
          bValue = parseFloat(b.payment_revenue || b.total_revenue) || 0;
          break;
        default:
          return 0;
      }
      
      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getWorkshopStatus = (workshop) => {
    const now = new Date();
    const start = new Date(workshop.start_date);
    const end = new Date(workshop.end_date);
    
    if (!workshop.is_active) return { status: 'Inactive', color: 'gray' };
    if (now < start) return { status: 'Upcoming', color: 'blue' };
    if (now >= start && now <= end) return { status: 'Ongoing', color: 'green' };
    return { status: 'Completed', color: 'purple' };
  };

  const getWorkshopTypeInfo = (type) => {
    const typeMap = {
      'L1': { label: 'AI Essentials', level: 'Entry Point', color: 'bg-green-100 text-green-800', icon: '🎯' },
      'L2': { label: 'AI Practitioner', level: 'Advanced', color: 'bg-blue-100 text-blue-800', icon: '🚀' },
      'A1': { label: 'Agentic AI', level: 'Specialized', color: 'bg-purple-100 text-purple-800', icon: '🤖' },
      'V1': { label: 'Vibe Coding', level: 'Development', color: 'bg-orange-100 text-orange-800', icon: '💻' }
    };
    return typeMap[type] || { label: type, level: 'Workshop', color: 'bg-gray-100 text-gray-800', icon: '📚' };
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Workshop Management</h1>
            <p className="text-gray-600">Manage your AI workshops and track enrollments</p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
          >
            <FaPlus /> <span>Create Workshop</span>
          </button>
        </div>

        {/* Workshop Types Overview */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4">Workshop Learning Path</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
              <span className="text-2xl">🎯</span>
              <div>
                <h4 className="font-medium text-green-800">L1 - AI Essentials</h4>
                <p className="text-sm text-green-600">Entry Point • Foundation</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <span className="text-2xl">🚀</span>
              <div>
                <h4 className="font-medium text-blue-800">L2 - AI Practitioner</h4>
                <p className="text-sm text-blue-600">Advanced • Deployments</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
              <span className="text-2xl">🤖</span>
              <div>
                <h4 className="font-medium text-purple-800">A1 - Agentic AI</h4>
                <p className="text-sm text-purple-600">Specialized • Agents</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg">
              <span className="text-2xl">💻</span>
              <div>
                <h4 className="font-medium text-orange-800">V1 - Vibe Coding</h4>
                <p className="text-sm text-orange-600">Development • AI Tools</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters & View Toggle */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search workshops..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div className="relative">
              <FaFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="pl-10 pr-8 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              >
                <option value="all">All Status</option>
                <option value="upcoming">Upcoming</option>
                <option value="ongoing">Ongoing</option>
                <option value="completed">Completed</option>
                <option value="active">Active</option>
              </select>
              <FaChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
            </div>

            <div className="text-sm text-gray-600">
              {filteredAndSortedWorkshops.length} workshop{filteredAndSortedWorkshops.length !== 1 ? 's' : ''}
            </div>
            
            {/* View Toggle */}
            <div className="flex justify-end">
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('cards')}
                  className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm transition-colors ${
                    viewMode === 'cards' 
                      ? 'bg-white text-blue-600 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <FaTh className="w-4 h-4" />
                  <span>Cards</span>
                </button>
                <button
                  onClick={() => setViewMode('table')}
                  className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm transition-colors ${
                    viewMode === 'table' 
                      ? 'bg-white text-blue-600 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <FaList className="w-4 h-4" />
                  <span>Table</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Workshops Display */}
        {viewMode === 'cards' ? (
          /* Card View */
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredAndSortedWorkshops.map((workshop) => {
              const status = getWorkshopStatus(workshop);
              const typeInfo = getWorkshopTypeInfo(workshop.type);
              
              return (
                <div key={workshop.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-lg">{typeInfo.icon}</span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
                            {workshop.type} - {typeInfo.label}
                          </span>
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-1">
                          {workshop.name}
                        </h3>
                        <p className="text-sm text-gray-600">{typeInfo.level}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        status.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                        status.color === 'green' ? 'bg-green-100 text-green-800' :
                        status.color === 'purple' ? 'bg-purple-100 text-purple-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {status.status}
                      </span>
                    </div>

                    <div className="space-y-3 mb-6">
                      <div className="flex items-center text-sm text-gray-600">
                        <FaCalendar className="mr-2" />
                        <span>{formatDate(workshop.start_date)} - {formatDate(workshop.end_date)}</span>
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-600">
                        <FaUsers className="mr-2" />
                        <span>{workshop.enrolled_count || 0}/{workshop.max_participants} enrolled</span>
                      </div>
                      
                      {workshop.instructor && (
                        <div className="flex items-center text-sm text-gray-600">
                          <FaCertificate className="mr-2" />
                          <span>{workshop.instructor}</span>
                        </div>
                      )}
                      
                      <div className="flex items-center text-sm text-gray-600">
                        <FaDollarSign className="mr-2" />
                        <span>
                          ${workshop.price_usd} / ₹{workshop.price_inr}
                          {(workshop.payment_revenue || workshop.total_revenue) ? ` ($${workshop.payment_revenue || workshop.total_revenue} revenue)` : ''}
                        </span>
                      </div>
                    </div>

                    {workshop.description && (
                      <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                        {workshop.description}
                      </p>
                    )}

                    <div className="flex justify-between items-center">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => router.push(`/workshops/${workshop.id}`)}
                          className="text-blue-600 hover:text-blue-800"
                          title="View Details"
                        >
                          <FaExternalLinkAlt />
                        </button>
                        <button
                          onClick={() => setEditingWorkshop(workshop)}
                          className="text-green-600 hover:text-green-800"
                          title="Edit"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => handleDeactivateWorkshop(workshop.id)}
                          className="text-red-600 hover:text-red-800"
                          title="Deactivate"
                        >
                          <FaTrash />
                        </button>
                      </div>
                      
                      {workshop.meeting_link && (
                        <a
                          href={workshop.meeting_link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 text-sm"
                        >
                          Join Meeting
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          /* Table View */
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Workshop</span>
                        {sortField === 'name' && (
                          sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />
                        )}
                      </div>
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('type')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Type</span>
                        {sortField === 'type' && (
                          sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />
                        )}
                      </div>
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('start_date')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Date</span>
                        {sortField === 'start_date' && (
                          sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />
                        )}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('enrolled_count')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Enrolled</span>
                        {sortField === 'enrolled_count' && (
                          sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />
                        )}
                      </div>
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('total_revenue')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Revenue</span>
                        {sortField === 'total_revenue' && (
                          sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />
                        )}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredAndSortedWorkshops.map((workshop) => {
                    const status = getWorkshopStatus(workshop);
                    const typeInfo = getWorkshopTypeInfo(workshop.type);
                    
                    return (
                      <tr key={workshop.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <span className="text-lg mr-3">{typeInfo.icon}</span>
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {workshop.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {workshop.instructor || 'No instructor assigned'}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${typeInfo.color}`}>
                            {workshop.type}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div>
                            {formatDate(workshop.start_date)}
                          </div>
                          <div className="text-xs text-gray-500">
                            to {formatDate(workshop.end_date)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            status.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                            status.color === 'green' ? 'bg-green-100 text-green-800' :
                            status.color === 'purple' ? 'bg-purple-100 text-purple-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {status.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="flex items-center">
                            <FaUsers className="mr-1 text-gray-400" />
                            {workshop.enrolled_count || 0}/{workshop.max_participants}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div>
                            ${workshop.price_usd}
                          </div>
                          {(workshop.payment_revenue || workshop.total_revenue) && (
                            <div className="text-xs text-green-600 font-medium">
                              ${workshop.payment_revenue || workshop.total_revenue} earned
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => router.push(`/workshops/${workshop.id}`)}
                              className="text-blue-600 hover:text-blue-800"
                              title="View Details"
                            >
                              <FaExternalLinkAlt />
                            </button>
                            <button
                              onClick={() => setEditingWorkshop(workshop)}
                              className="text-green-600 hover:text-green-800"
                              title="Edit"
                            >
                              <FaEdit />
                            </button>
                            <button
                              onClick={() => handleDeactivateWorkshop(workshop.id)}
                              className="text-red-600 hover:text-red-800"
                              title="Deactivate"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {filteredAndSortedWorkshops.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No workshops found matching your criteria.</p>
          </div>
        )}
      </div>

      {/* Create/Edit Modal */}
      {(showCreateModal || editingWorkshop) && (
        <WorkshopModal
          workshop={editingWorkshop}
          onClose={() => {
            setShowCreateModal(false);
            setEditingWorkshop(null);
          }}
          onSave={editingWorkshop ? 
            (data) => handleUpdateWorkshop(editingWorkshop.id, data) : 
            handleCreateWorkshop
          }
        />
      )}
    </Layout>
  );
}

// Workshop Create/Edit Modal Component
function WorkshopModal({ workshop, onClose, onSave }) {
  const [formData, setFormData] = useState({
    name: workshop?.name || '',
    type: workshop?.type || '',
    description: workshop?.description || '',
    start_date: workshop?.start_date || '',
    end_date: workshop?.end_date || '',
    instructor: workshop?.instructor || '',
    max_participants: workshop?.max_participants || 50,
    price_usd: workshop?.price_usd || '',
    price_inr: workshop?.price_inr || '',
    timezone: workshop?.timezone || 'Asia/Kolkata',
    meeting_link: workshop?.meeting_link || '',
    materials_link: workshop?.materials_link || ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-2xl font-bold mb-4">
            {workshop ? 'Edit Workshop' : 'Create New Workshop'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Workshop Name *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Workshop Type *
                </label>
                <select
                  required
                  value={formData.type}
                  onChange={(e) => setFormData({...formData, type: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select Type</option>
                  <option value="L1">L1 - AI Essentials (Entry Point)</option>
                  <option value="L2">L2 - AI Practitioner (Advanced)</option>
                  <option value="A1">A1 - Agentic AI (Specialized)</option>
                  <option value="V1">V1 - Vibe Coding (Development)</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows="3"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date & Time *
                </label>
                <input
                  type="datetime-local"
                  required
                  value={formData.start_date}
                  onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Date & Time *
                </label>
                <input
                  type="datetime-local"
                  required
                  value={formData.end_date}
                  onChange={(e) => setFormData({...formData, end_date: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Instructor
                </label>
                <input
                  type="text"
                  value={formData.instructor}
                  onChange={(e) => setFormData({...formData, instructor: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Participants
                </label>
                <input
                  type="number"
                  value={formData.max_participants}
                  onChange={(e) => setFormData({...formData, max_participants: parseInt(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Timezone
                </label>
                <select
                  value={formData.timezone}
                  onChange={(e) => setFormData({...formData, timezone: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="Asia/Kolkata">Asia/Kolkata</option>
                  <option value="America/New_York">America/New_York</option>
                  <option value="America/Los_Angeles">America/Los_Angeles</option>
                  <option value="Europe/London">Europe/London</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price (USD)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.price_usd}
                  onChange={(e) => setFormData({...formData, price_usd: parseFloat(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price (INR)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.price_inr}
                  onChange={(e) => setFormData({...formData, price_inr: parseFloat(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Meeting Link
              </label>
              <input
                type="url"
                value={formData.meeting_link}
                onChange={(e) => setFormData({...formData, meeting_link: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Materials Link
              </label>
              <input
                type="url"
                value={formData.materials_link}
                onChange={(e) => setFormData({...formData, materials_link: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                {workshop ? 'Update Workshop' : 'Create Workshop'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}