@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* Force black text for light mode (override dark mode detection) */
@media (prefers-color-scheme: light), (prefers-color-scheme: no-preference) {
  body {
    color: #111827; /* gray-900 */
  }
  
  /* Ensure all form elements have black text */
  input, textarea, select {
    color: #111827 !important; /* gray-900 */
  }
}

/* Ensure form elements always have proper text color */
input, textarea, select {
  color: #111827; /* gray-900 - force black text */
}

input::placeholder, textarea::placeholder {
  color: #6b7280; /* gray-500 for placeholder text */
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Common form input styles */
@layer components {
  .input-primary {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500;
  }
  
  .input-secondary {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-900 bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent;
  }
  
  .select-primary {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500;
  }
  
  /* Standard form element styles */
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }
  
  .form-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }
  
  .form-textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }
}