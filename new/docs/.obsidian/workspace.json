{"main": {"id": "ac2d30c0a708b646", "type": "split", "children": [{"id": "14c8e92df0e75940", "type": "tabs", "children": [{"id": "26dc7389a808e836", "type": "leaf", "state": {"type": "markdown", "state": {"file": "8-courses/Practitioner/Pods.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Pods"}}, {"id": "54e35010b1625b0a", "type": "leaf", "state": {"type": "markdown", "state": {"file": "8-courses/Practitioner/RAG flow.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "RAG flow"}}]}], "direction": "vertical"}, "left": {"id": "747523bdd2cbf488", "type": "split", "children": [{"id": "3f072abf6f59f8f4", "type": "tabs", "children": [{"id": "d8af3fb8ad9cb657", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "f8a9595625efd653", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "726b0f5672d25870", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 236.5}, "right": {"id": "f98503203d00aa76", "type": "split", "children": [{"id": "1a3a73e4559f88d3", "type": "tabs", "children": [{"id": "aa601b320121fb27", "type": "leaf", "state": {"type": "backlink", "state": {"file": "scaled_education_business_plan.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for scaled_education_business_plan"}}, {"id": "ee8076266f288ca6", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "scaled_education_business_plan.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from scaled_education_business_plan"}}, {"id": "8a81549119589b8f", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "abb0a27742cb1815", "type": "leaf", "state": {"type": "outline", "state": {"file": "scaled_education_business_plan.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of scaled_education_business_plan"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"bases:Create new base": false, "switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "26dc7389a808e836", "lastOpenFiles": ["8-courses/Practitioner/Pod4/backend/init_governance_db.py", "8-courses/Practitioner/Pod4/frontend/src/utils/complianceUtils.js", "8-courses/Practitioner/Pod4/frontend/src/utils/riskCalculations.js", "8-courses/Practitioner/Pod4/frontend/src/services/auditAPI.js", "8-courses/Practitioner/Pod4/frontend/src/services/governanceAPI.js", "8-courses/Practitioner/Pod4/frontend/src/services/riskAPI.js", "8-courses/Practitioner/Pod4/frontend/src/components/AuditReporting.jsx", "8-courses/Practitioner/Pod4/frontend/src/components/BiasAnalysis.jsx", "8-courses/Practitioner/Pod4/frontend/src/components/PolicyCompliance.jsx", "8-courses/Practitioner/Pod4/frontend/src/components/RiskAssessment.jsx", "8-courses/Practitioner/Pod4/backend/database/schema.sql", "8-courses/Practitioner/Pod2/USAGE.md", "8-courses/Practitioner/Pod2/frontend/README.md", "8-courses/Practitioner/Pod4/README.md", "8-courses/Practitioner/Pod2/Pod2.md", "8-courses/Practitioner/Pod2/README.md", "8-courses/Practitioner/Pods.md", "8-courses/Practitioner/Pod1/Pod1.md", "8-courses/Modern AI Pro Practitioner September 2025.md", "8-courses/Practitioner/Pod3/README.md", "8-courses/Practitioner/Pod1/frontend/node_modules/core-js-pure/README.md", "8-courses/Practitioner/Pod1/frontend/node_modules/core-js/README.md", "8-courses/Practitioner/Pod1/frontend/node_modules/es-abstract/README.md", "8-courses/Practitioner/Pod1/frontend/node_modules/es-abstract/CHANGELOG.md", "8-courses/Practitioner/Pod1/frontend/node_modules/jest-watch-typeahead/node_modules/@jest/console/node_modules/slash/readme.md", "8-courses/Practitioner/Pod1/frontend/node_modules/svgo/node_modules/domutils/node_modules/domelementtype/readme.md", "8-courses/Practitioner/Pod1/frontend/node_modules/send/node_modules/debug/node_modules/ms/readme.md", "8-courses/Practitioner/Pod1/frontend/node_modules/send/node_modules/debug/node_modules/ms/license.md", "8-courses/Practitioner/Pod1/frontend/node_modules/jest-watch-typeahead/node_modules/string-length/node_modules/char-regex/readme.md", "8-courses/Practitioner/Pod1/frontend/node_modules/jest-watch-typeahead/node_modules/strip-ansi/node_modules/ansi-regex/readme.md", "8-courses/Practitioner/Pod1/frontend/node_modules/jest-watch-typeahead/node_modules/jest-watcher/node_modules/strip-ansi/readme.md", "8-courses/Practitioner/Pod1/frontend/node_modules/jest-watch-typeahead/node_modules/jest-watcher/node_modules/string-length/readme.md", "8-courses/Practitioner/Pod1/frontend/node_modules/jest-watch-typeahead/node_modules/jest-message-util/node_modules/slash/readme.md", "8-courses/Practitioner/Pod1/frontend/node_modules/hpack.js/node_modules/readable-stream/doc/wg-meetings/2015-01-30.md", "8-courses/Practitioner/Pod1/frontend/node_modules/@types/express/node_modules/@types/express-serve-static-core/README.md", "8-courses/Practitioner/Pod1/frontend/node_modules/@rollup/pluginutils/node_modules/@types/estree/README.md", "8-courses/Practitioner/Pod1/frontend/node_modules/istanbul-reports/lib/html-spa/assets/sort-arrow-sprite.png", "8-courses/Practitioner/Pod1/frontend/node_modules/istanbul-reports/lib/html/assets/sort-arrow-sprite.png", "8-courses/Practitioner/Pod1/frontend/node_modules/istanbul-reports/lib/html/assets/favicon.png", "8-courses/Practitioner/Pod1/frontend/node_modules/@jest/core/build/assets/jest_logo.png", "8-courses/Practitioner/Pod1/frontend/node_modules/serve-index/public/icons/page_world.png", "8-courses/Practitioner/Pod1/frontend/node_modules/serve-index/public/icons/page_word.png", "8-courses/Practitioner/Pod1/frontend/node_modules/serve-index/public/icons/page_white_zip.png", "8-courses/Practitioner/Pod1/frontend/node_modules/serve-index/public/icons/page_white_wrench.png", "8-courses/Practitioner/Pod1/frontend/node_modules/serve-index/public/icons/page_white_world.png", "8-courses/Practitioner/Pod1/frontend/node_modules/serve-index/public/icons/page_white_word.png"]}