# Leads Management Page - Low-Fi Mockup

## Layout Structure
```
┌─────────────────────────────────────────────────────────────────────┐
│ 🚀 AI Bootcamp CRM > 👥 Leads Management                            │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│ 🔍 [Search leads...] [🎯 Hot] [🟡 Warm] [⚪ Medium] [📅 Today ▼]  │
│ [➕ New Lead] [📊 Bulk Actions ▼] [📤 Export CSV] [🔄 Sync FB]     │
│                                                                     │
│ 📊 LEAD SUMMARY                                                     │
│ ┌─────────────┬─────────────┬─────────────┬─────────────────────┐   │
│ │ 🎯 HOT (24) │ 🟡 WARM(45) │⚪MEDIUM(73) │ 📞 NEEDS CALL (18) │   │
│ │ >15% conv   │ 8-12% conv  │ 3-5% conv   │ <5min response      │   │
│ │ CTO, Sr Dev │ Mid-level   │ Junior/Int  │ Auto-responded      │   │
│ └─────────────┴─────────────┴─────────────┴─────────────────────┘   │
│                                                                     │
│ 📋 LEADS LIST                                                       │
│ ┌───┬─────────────────┬──────────┬───────────┬──────────┬─────────┐ │
│ │🔥│ Raj<PERSON>    │ 🎯 HOT   │ New       │ 2m ago   │ [CALL]  │ │
│ │  │ CTO @ Paytm     │ Python++ │           │          │ [ASSIGN]│ │
│ │  │ <EMAIL> │ AI: Expert│          │          │         │ │
│ ├───┼─────────────────┼──────────┼───────────┼──────────┼─────────┤ │
│ │🟡│ Priya Sharma    │🟡 WARM   │Auto-Resp  │ 15m ago  │ [CALL]  │ │
│ │  │ Tech Lead @Zomato│ Python+  │ Shivam   │          │ [VIEW]  │ │
│ │  │ <EMAIL>│ AI: Inter │          │          │         │ │
│ ├───┼─────────────────┼──────────┼───────────┼──────────┼─────────┤ │
│ │⚪│ Amit Singh      │⚪ MEDIUM │ Contacted │ 2h ago   │ [FOLLOW]│ │
│ │  │ Dev @ Infosys   │ Python-  │ Manish    │          │ [UPDATE]│ │
│ │  │ <EMAIL>│ AI: Begin │          │          │         │ │
│ ├───┼─────────────────┼──────────┼───────────┼──────────┼─────────┤ │
│ │🟢│ Sarah Johnson   │ 🎯 HOT   │ Demo Sched│ 1d ago   │ [PREP]  │ │
│ │  │ VP Eng @Spotify │ Python+++│ Sanyu     │          │ [NOTES] │ │
│ │  │ <EMAIL>│AI: Expert│          │          │         │ │
│ ├───┼─────────────────┼──────────┼───────────┼──────────┼─────────┤ │
│ │🏆│ David Chen      │ ENROLLED │ Enrolled  │ 3d ago   │ [TRACK] │ │
│ │  │ Sr Arch @Netflix│ Paid ✓   │ Complete  │          │ [CERT]  │ │
│ │  │ <EMAIL>│ Batch #47 │          │          │         │ │
│ └───┴─────────────────┴──────────┴───────────┴──────────┴─────────┘ │
│                                                                     │
│ [< Prev] Page 1 of 12 [Next >]     Showing 10 of 142 leads         │
│                                                                     │
│ 📱 QUICK ACTIONS PANEL                                              │
│ ┌─────────────────────────────────────────────────────────────────┐ │
│ │ 🚨 URGENT (Next 2 hours)                                       │ │
│ │ • Call Rajesh Kumar (CTO @ Paytm) - New hot lead               │ │
│ │ • Follow up: Demo with Sarah (VP @ Spotify) in 30 mins         │ │
│ │ • Send certificate: David Chen completed bootcamp              │ │
│ │                                                                 │ │
│ │ 📞 SMART ASSIGNMENT                                             │ │
│ │ • 12 unassigned hot leads → [Auto-assign by expertise]         │ │
│ │ • Shivam: 3 leads (speciality: CTOs, Senior roles)            │ │
│ │ • Manish: 5 leads (speciality: Tech Leads, High volume)       │ │
│ │ • Sanyu: 4 leads (speciality: Mid-level, International)       │ │
│ └─────────────────────────────────────────────────────────────────┘ │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

## Advanced Features:

### 1. **Smart Lead Segmentation**
- 🎯 **Hot Leads**: CTOs, VPs, Senior Developers (15-20% conversion)
- 🟡 **Warm Leads**: Tech Leads, Mid-level (8-12% conversion)  
- ⚪ **Medium Leads**: Junior, Students (3-5% conversion)

### 2. **Lead Scoring Indicators**
- Python knowledge level: `+` (basic) to `+++` (expert)
- AI experience: Beginner/Intermediate/Advanced/Expert
- Company AI adoption indicators
- Response time urgency

### 3. **Status-Based Actions**
- **New**: [CALL] [ASSIGN] buttons prominent
- **Auto-Responded**: [FOLLOW-UP] within 5 minutes
- **Demo Scheduled**: [PREP] [NOTES] for preparation
- **Enrolled**: [TRACK] progress, [CERT] management

### 4. **Mobile-First Design**
- Cards collapse to single column
- Swipe actions for quick updates
- Touch-friendly buttons and tap targets
- Pull-to-refresh for real-time updates

### 5. **Instant Response Tracking**
- Visual indicator for <5 minute response goal
- Auto-response status with timestamps
- SMS/Email delivery confirmation

### Data Integration:
- Facebook Lead Ads API (real-time)
- Smart segmentation algorithms
- Call tracking and notes
- Certificate delivery status