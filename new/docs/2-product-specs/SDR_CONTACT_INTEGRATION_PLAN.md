# SDR Contact Integration Plan

## Overview
The lead detail page now includes a comprehensive SDR contact section allowing sales representatives to send templated messages to leads via email. This document outlines the integration plan for connecting the UI with the actual communication scripts.

## Current Implementation (UI Complete ✅)

### Components Added:
1. **Channel Selection Dropdown** - Currently supports Email only
2. **Message Template Dropdown** - 7 pre-defined templates + custom option
3. **Template Previews** - Live preview of message content
4. **Custom Message Field** - For fully customized communications
5. **Send Button** with loading states and validation
6. **Error Handling** and success notifications

### Message Templates Available:
- `payment_link` - Send workshop enrollment link
- `syllabus_link` - Send course syllabus and details  
- `follow_up_1` - Initial interest follow-up
- `follow_up_2` - Second follow-up attempt
- `follow_up_3` - Final follow-up before closing
- `workshop_reminder` - Upcoming session reminder
- `custom` - Write your own message

## Integration Requirements

### 1. API Endpoint Creation
**File:** `/pages/api/communications/send.js`

**Expected Payload:**
```json
{
  "leadId": 12345,
  "channel": "email",
  "template": "payment_link",
  "customMessage": "Optional custom message text",
  "recipient": {
    "name": "<PERSON> Doe",
    "email": "<EMAIL>", 
    "phone": "+1234567890"
  }
}
```

**Expected Response:**
```json
{
  "success": true,
  "messageId": "msg_abc123",
  "message": "Email sent successfully"
}
```

### 2. Email Template Integration

#### Template Storage Options:
1. **Database Storage** - Store templates in `message_templates` table
2. **File-based** - Templates as HTML/text files in `/templates/email/`
3. **Configuration** - Templates in config files with placeholders

#### Template Variables:
All templates should support these placeholders:
- `{lead.full_name}` - Lead's full name
- `{lead.email}` - Lead's email address  
- `{lead.phone}` - Lead's phone number
- `{workshop.name}` - Workshop name
- `{workshop.date}` - Next workshop date
- `{payment.link}` - Personalized payment link
- `{syllabus.link}` - Course syllabus download link
- `{sdr.name}` - SDR's name (from current user)
- `{sdr.email}` - SDR's contact email

### 3. Communication Service Integration

#### Existing Scripts Location:
Check for existing email scripts in:
- `/scripts/integrations/email/`
- `/lib/communications/`
- `/services/email/`

#### Integration Points:
1. **SMTP Configuration** - Use existing email service
2. **Template Engine** - Mustache, Handlebars, or simple string replacement
3. **Delivery Tracking** - Log sent messages for follow-up tracking
4. **Rate Limiting** - Prevent spam, respect email service limits

### 4. Database Schema Updates

#### New Table: `communications_log`
```sql
CREATE TABLE communications_log (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  lead_id INTEGER NOT NULL,
  sent_by_user_id INTEGER NOT NULL,
  channel VARCHAR(50) NOT NULL, -- 'email', 'sms', 'whatsapp'
  template_used VARCHAR(100),
  custom_message TEXT,
  recipient_email VARCHAR(255),
  recipient_phone VARCHAR(50),
  sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status VARCHAR(50) DEFAULT 'pending', -- 'sent', 'delivered', 'failed'
  message_id VARCHAR(255), -- External service message ID
  error_message TEXT,
  FOREIGN KEY (lead_id) REFERENCES leads(id),
  FOREIGN KEY (sent_by_user_id) REFERENCES users(id)
);
```

#### Update `leads` table to track last contact:
```sql
ALTER TABLE leads ADD COLUMN last_contacted_at TIMESTAMP;
ALTER TABLE leads ADD COLUMN last_contacted_by INTEGER;
ALTER TABLE leads ADD COLUMN contact_attempts INTEGER DEFAULT 0;
```

### 5. Status Updates Integration

The UI automatically updates lead status based on template:
- `payment_link` → `payment-sent`
- `follow_up_*` → `contacted`

Ensure the backend respects these status updates and tracks contact history.

### 6. Future Enhancements

#### Phase 2 - Additional Channels:
- SMS integration via Twilio
- WhatsApp Business API
- LinkedIn messaging

#### Phase 3 - Advanced Features:
- A/B testing for templates
- Automated follow-up sequences
- Response tracking and analytics
- Template performance metrics

## Development Steps

### Step 1: Create API Endpoint ⏳
1. Create `/pages/api/communications/send.js`
2. Add authentication middleware
3. Validate request payload
4. Handle template selection logic

### Step 2: Email Service Integration ⏳
1. Identify existing email service configuration
2. Create template processing function
3. Implement variable substitution
4. Add delivery tracking

### Step 3: Database Integration ⏳
1. Create `communications_log` table
2. Update `leads` table schema
3. Log all communication attempts
4. Update lead status and timestamps

### Step 4: Testing ⏳
1. Test all message templates
2. Verify error handling
3. Check rate limiting
4. Validate status updates

## Current Status
- ✅ UI Implementation Complete
- ⏳ API Endpoint Creation (Pending)
- ⏳ Email Service Integration (Pending)
- ⏳ Database Schema Updates (Pending)
- ⏳ Testing and Validation (Pending)

## Notes for Developers

1. **Error Handling**: The UI expects specific error format - ensure API returns `{success: false, error: "message"}`

2. **Loading States**: UI shows sending state - API should be reasonably fast or consider async processing

3. **Template Validation**: Validate template selection on backend to prevent unauthorized template usage

4. **Audit Trail**: Every communication should be logged for compliance and follow-up tracking

5. **Rate Limiting**: Consider implementing per-user rate limiting to prevent abuse

This integration plan ensures seamless connection between the new SDR contact UI and the existing communication infrastructure.