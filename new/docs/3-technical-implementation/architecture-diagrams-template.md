# Architecture Diagrams Template & Guidelines

## Purpose
This document provides templates and examples for creating high-level architectural documentation that bridges user stories and implementation code. All diagrams should focus on system design, data flow, and component interactions rather than code details.

---

## 1. System Context Diagram (C4 Level 1)

### Modern AI Pro System Overview
```mermaid
graph TB
    subgraph "External Users"
        L[Leads<br/>Workshop Prospects]
        S[Students<br/>Workshop Participants]
        E[Enterprise<br/>Decision Makers]
        A[Alumni<br/>Community Members]
    end
    
    subgraph "Modern AI Pro System"
        MAI[Modern AI Pro<br/>Platform]
    end
    
    subgraph "Internal Users"
        EX[Executives<br/>Analytics & Strategy]
        SDR[Sales Reps<br/>Lead Conversion]
        INS[Instructors<br/>Workshop Delivery]
        ADM[Admins<br/>System Management]
    end
    
    subgraph "External Systems"
        FB[Facebook<br/>Lead Ads API]
        ST[Stripe<br/>Payment Processing]
        TW[Twilio<br/>SMS/WhatsApp]
        BR[Brevo<br/>Email Marketing]
        GM[Gmail API<br/>Email Delivery]
        OAI[OpenAI<br/>Content Generation]
        LI[LinkedIn<br/>Profile Enrichment]
    end
    
    L -->|Submits Info| FB
    FB -->|Lead Data| MAI
    L -->|Direct Signup| MAI
    S -->|Learning| MAI
    E -->|Bulk Enrollment| MAI
    A -->|Community| MAI
    
    MAI -->|Communications| TW
    MAI -->|Email| BR
    MAI -->|Email| GM
    MAI -->|Payments| ST
    MAI -->|AI Generation| OAI
    MAI -->|Enrichment| LI
    
    EX -->|Analytics| MAI
    SDR -->|Lead Mgmt| MAI
    INS -->|Teaching| MAI
    ADM -->|Config| MAI
    
    ST -->|Webhooks| MAI
    FB -->|Webhooks| MAI
```

---

## 2. Container Diagram (C4 Level 2)

### Application Architecture
```mermaid
graph TB
    subgraph "Client Applications"
        WEB[Web Application<br/>Next.js + React]
        MOB[Mobile Web<br/>Responsive PWA]
    end
    
    subgraph "Application Layer"
        API[API Gateway<br/>Next.js API Routes]
        AUTH[Auth Service<br/>JWT + NextAuth]
        WORK[Workflow Engine<br/>Python Scripts]
    end
    
    subgraph "Business Logic"
        LEAD[Lead Management<br/>Service]
        COMM[Communication<br/>Orchestrator]
        WORK2[Workshop<br/>Manager]
        LEARN[Learning<br/>Platform]
        PAY[Payment<br/>Processor]
    end
    
    subgraph "Data Layer"
        DB[(Primary Database<br/>SQLite/PostgreSQL)]
        CACHE[(Redis Cache<br/>Session & Metrics)]
        FILES[(File Storage<br/>Azure Blob)]
    end
    
    subgraph "Integration Layer"
        FB_INT[Facebook<br/>Collector]
        STRIPE_INT[Stripe<br/>Handler]
        EMAIL_INT[Email<br/>Service]
        SMS_INT[SMS/WhatsApp<br/>Service]
    end
    
    WEB --> API
    MOB --> API
    API --> AUTH
    API --> LEAD
    API --> COMM
    API --> WORK2
    API --> LEARN
    API --> PAY
    
    LEAD --> DB
    COMM --> EMAIL_INT
    COMM --> SMS_INT
    PAY --> STRIPE_INT
    
    WORK --> DB
    WORK --> FB_INT
    WORK --> COMM
    
    LEAD --> CACHE
    AUTH --> CACHE
```

---

## 3. Lead Lifecycle State Machine

### Lead Status Progression
```mermaid
stateDiagram-v2
    [*] --> New: Lead Captured
    New --> Contacted: First Outreach
    New --> Stale: No Response (7 days)
    
    Contacted --> Qualified: Positive Response
    Contacted --> NotInterested: Negative Response
    Contacted --> FollowUp: No Response
    
    Qualified --> Enrolled: Payment Completed
    Qualified --> Lost: Did Not Convert
    
    FollowUp --> Qualified: Responded
    FollowUp --> Nurture: Multiple Attempts
    
    Enrolled --> Attended: Workshop Completed
    Enrolled --> NoShow: Did Not Attend
    
    Attended --> Alumni: Certificate Issued
    Alumni --> Referrer: Made Referral
    
    Nurture --> Qualified: Re-engaged
    Nurture --> Archived: Long-term Cold
    
    Stale --> Contacted: Re-activation
    Stale --> Archived: Never Contacted
```

---

## 4. Multi-Channel Communication Sequence

### 7-Day Engagement Flow
```mermaid
sequenceDiagram
    participant L as Lead
    participant S as System
    participant E as Email Service
    participant W as WhatsApp
    participant SM as SMS Service
    participant SD as SDR
    participant DB as Database
    
    Note over L,DB: Day 1 - Immediate Response
    L->>S: Submit Lead Form
    S->>DB: Store Lead (Hot/Warm/Medium)
    S->>E: Send Welcome Email + Resource
    E-->>L: "Python for Executives" eBook
    S->>DB: Log Interaction
    S->>SD: Hot Lead Alert
    
    Note over L,DB: Day 2 - WhatsApp Follow-up
    S->>DB: Check Engagement
    alt Email Opened
        S->>W: Send Personalized Message
        W-->>L: Workshop Benefits
    else Not Opened
        S->>W: Send Introduction
        W-->>L: Value Proposition
    end
    
    Note over L,DB: Day 3 - Second Email
    S->>E: Send AI Trends Report
    E-->>L: Industry-specific Content
    
    Note over L,DB: Day 4 - SMS Engagement
    S->>SM: Send Interactive SMS
    SM-->>L: "Reply YES for Info"
    L-->>SM: YES
    SM->>S: Trigger SDR Call
    S->>SD: Priority Call Alert
    
    Note over L,DB: Day 5 - Success Story
    S->>W: Send Case Study
    W-->>L: Similar Company Success
    
    Note over L,DB: Day 6 - Video Content
    S->>E: Send Video Email
    E-->>L: "3 AI Mistakes to Avoid"
    
    Note over L,DB: Day 7 - Decision Point
    S->>DB: Check All Interactions
    alt Any Engagement
        S->>SD: Schedule Call
        SD->>L: Personal Outreach
    else No Engagement
        S->>E: Add to Newsletter
        S->>DB: Move to Nurture
    end
```

---

## 5. Workshop Management Flow

### End-to-End Workshop Process
```mermaid
graph LR
    subgraph "Pre-Workshop"
        A[Lead Interest] --> B[Payment Link]
        B --> C[Stripe Payment]
        C --> D[Webhook Received]
        D --> E[Auto-Enrollment]
        E --> F[Welcome Sequence]
        F --> G[Prep Materials]
    end
    
    subgraph "Workshop Delivery"
        G --> H[Day 1 Session]
        H --> I[Attendance Tracking]
        I --> J[Day 2 Session]
        J --> K[Engagement Metrics]
        K --> L[Day 3 Session]
        L --> M[Completion Check]
    end
    
    subgraph "Post-Workshop"
        M --> N{Attended 3 Days?}
        N -->|Yes| O[Generate Certificate]
        N -->|No| P[Follow-up Sequence]
        O --> Q[Email Certificate]
        Q --> R[Alumni Network]
        P --> S[Makeup Options]
        R --> T[Referral Program]
        R --> U[Subscription Upsell]
    end
    
    style A fill:#e1f5fe
    style C fill:#c8e6c9
    style O fill:#fff9c4
    style R fill:#f3e5f5
```

---

## 6. Data Flow Architecture

### Lead Data Pipeline
```mermaid
graph TD
    subgraph "Data Sources"
        FB[Facebook Leads API]
        WEB[Website Forms]
        REF[Referral System]
        CSV[Manual Imports]
    end
    
    subgraph "Ingestion Layer"
        COLL[Lead Collector<br/>Cron: Every Minute]
        VAL[Validation Service]
        DUP[Duplicate Detection]
    end
    
    subgraph "Processing Layer"
        SCORE[Lead Scoring Engine]
        ENRICH[Data Enrichment]
        SEG[Segmentation Service]
        ASSIGN[Assignment Logic]
    end
    
    subgraph "Storage Layer"
        LEADS[(Leads Table)]
        INT[(Interactions Table)]
        CUST[(Customers Table)]
    end
    
    subgraph "Action Layer"
        COMM[Communication Trigger]
        DASH[Dashboard Update]
        ALERT[SDR Notifications]
        REPORT[Analytics Engine]
    end
    
    FB --> COLL
    WEB --> COLL
    REF --> COLL
    CSV --> VAL
    
    COLL --> VAL
    VAL --> DUP
    DUP --> SCORE
    
    SCORE --> ENRICH
    ENRICH --> SEG
    SEG --> ASSIGN
    
    ASSIGN --> LEADS
    ASSIGN --> COMM
    ASSIGN --> ALERT
    
    LEADS --> DASH
    LEADS --> REPORT
    
    COMM --> INT
    ALERT --> INT
```

---

## 7. Payment Integration Architecture

### Stripe Payment Flow with Reconciliation
```mermaid
sequenceDiagram
    participant SDR as Sales Rep
    participant D as Dashboard
    participant S as Stripe API
    participant W as Webhook Server
    participant DB as Database
    participant C as Communication Service
    participant L as Lead/Customer
    
    SDR->>D: Create Payment Link
    D->>S: Generate Payment Link
    S-->>D: Return Link URL
    D->>DB: Store Link Metadata
    D-->>SDR: Display Link
    
    SDR->>L: Send Payment Link
    L->>S: Complete Payment
    
    S->>W: payment_intent.succeeded
    W->>W: Verify Signature
    W->>DB: Update Lead Status
    W->>DB: Create Payment Record
    W->>DB: Create Enrollment
    
    W->>C: Trigger Communications
    C->>L: Send Confirmation Email
    C->>L: Send WhatsApp Welcome
    C->>L: Send Calendar Invite
    
    W-->>S: Return 200 OK
    
    Note over DB: Async Processing
    DB->>D: Update Dashboard
    DB->>C: Schedule Follow-ups
```

---

## 8. System Component Relationships

### High-Level Component Map
```mermaid
graph TB
    subgraph "User Interface Layer"
        LAND[Landing Pages]
        DASH[Dashboard]
        LEARN[Learning Portal]
        TEACH[Teaching Tools]
    end
    
    subgraph "API Layer"
        AUTH_API[Auth API]
        LEAD_API[Lead API]
        WORK_API[Workshop API]
        PAY_API[Payment API]
        COMM_API[Communication API]
    end
    
    subgraph "Service Layer"
        LEAD_SVC[Lead Service]
        WORK_SVC[Workshop Service]
        PAY_SVC[Payment Service]
        COMM_SVC[Comm Service]
        REPORT_SVC[Analytics Service]
    end
    
    subgraph "Integration Services"
        FB_SVC[Facebook Collector]
        STRIPE_SVC[Stripe Handler]
        EMAIL_SVC[Email Sender]
        SMS_SVC[SMS/WhatsApp]
        AI_SVC[AI Generator]
    end
    
    subgraph "Data Stores"
        DB[(Main Database)]
        CACHE[(Cache)]
        QUEUE[(Message Queue)]
        BLOB[(File Storage)]
    end
    
    LAND --> AUTH_API
    DASH --> LEAD_API
    DASH --> WORK_API
    DASH --> PAY_API
    LEARN --> AUTH_API
    TEACH --> WORK_API
    
    LEAD_API --> LEAD_SVC
    WORK_API --> WORK_SVC
    PAY_API --> PAY_SVC
    COMM_API --> COMM_SVC
    
    LEAD_SVC --> DB
    LEAD_SVC --> FB_SVC
    LEAD_SVC --> AI_SVC
    
    COMM_SVC --> EMAIL_SVC
    COMM_SVC --> SMS_SVC
    COMM_SVC --> QUEUE
    
    PAY_SVC --> STRIPE_SVC
    PAY_SVC --> DB
    
    REPORT_SVC --> DB
    REPORT_SVC --> CACHE
```

---

## 9. Error Handling & Resilience Pattern

### Fault Tolerance Architecture
```mermaid
graph LR
    subgraph "Request Flow"
        REQ[Incoming Request]
        CB[Circuit Breaker]
        RT[Retry Logic]
        SVC[Service Call]
    end
    
    subgraph "Error Handling"
        ERR{Error?}
        LOG[Error Logger]
        DLQ[Dead Letter Queue]
        ALERT[Alert System]
    end
    
    subgraph "Fallback Strategy"
        CACHE[Cache Response]
        DEFAULT[Default Response]
        MANUAL[Manual Queue]
    end
    
    REQ --> CB
    CB --> RT
    RT --> SVC
    SVC --> ERR
    
    ERR -->|Yes| LOG
    LOG --> DLQ
    LOG --> ALERT
    ERR -->|Timeout| CACHE
    ERR -->|Service Down| DEFAULT
    ERR -->|Critical| MANUAL
    
    ERR -->|No| SUCCESS[Success Response]
    
    DLQ --> RETRY[Retry Processor]
    RETRY --> RT
```

---

## 10. Deployment Architecture

### Infrastructure Overview
```mermaid
graph TB
    subgraph "Azure Cloud"
        subgraph "Web Tier"
            LB[Load Balancer]
            VM1[Web Server 1]
            VM2[Web Server 2]
        end
        
        subgraph "Application Tier"
            APP1[App Server]
            WORK[Worker Services]
            CRON[Cron Jobs]
        end
        
        subgraph "Data Tier"
            DB[(PostgreSQL)]
            REDIS[(Redis Cache)]
            BLOB[(Blob Storage)]
        end
        
        subgraph "Security"
            FW[Firewall]
            CERT[SSL Certificates]
            VAULT[Key Vault]
        end
    end
    
    subgraph "External Services"
        CDN[CloudFlare CDN]
        MON[Monitoring Service]
        BACKUP[Backup Service]
    end
    
    subgraph "Development"
        GIT[GitHub Repo]
        CI[CI/CD Pipeline]
        STG[Staging Env]
    end
    
    Internet --> CDN
    CDN --> LB
    LB --> VM1
    LB --> VM2
    VM1 --> APP1
    VM2 --> APP1
    APP1 --> DB
    APP1 --> REDIS
    APP1 --> BLOB
    
    WORK --> DB
    CRON --> DB
    
    GIT --> CI
    CI --> STG
    STG --> APP1
    
    APP1 --> MON
    DB --> BACKUP
```

---

## Documentation Guidelines

### 1. Diagram Standards
- Use Mermaid for all diagrams (version controlled)
- Include diagram title and description
- Keep diagrams focused on single concern
- Update version number when modified

### 2. Color Coding Convention
```mermaid
graph LR
    A[User Action] 
    B[System Process]
    C[External Service]
    D[(Database)]
    E{Decision Point}
    F[Error State]
    
    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#f3e5f5
    style D fill:#e8f5e9
    style E fill:#fff9c4
    style F fill:#ffebee
```

### 3. Naming Conventions
- Components: PascalCase (LeadService)
- Databases: lowercase_underscore (leads_table)
- APIs: kebab-case (/api/lead-management)
- Events: dot.notation (payment.completed)

### 4. Documentation Structure
Each architectural document should include:
- **Purpose**: What problem does this solve?
- **Scope**: What's included/excluded?
- **Diagram**: Visual representation
- **Description**: Explain the flow/components
- **Key Decisions**: Why this approach?
- **Trade-offs**: What are we optimizing for?
- **Implementation Status**: Current state
- **Related Documents**: Links to other docs

### 5. Maintenance Schedule
- **Weekly**: Update implementation status
- **Bi-weekly**: Review and update diagrams
- **Monthly**: Architecture review meeting
- **Quarterly**: Full documentation audit

---

## Example: Complete Feature Documentation

### Feature: Automated Certificate Generation

#### Purpose
Automatically generate and deliver certificates upon workshop completion.

#### Architecture
```mermaid
sequenceDiagram
    participant I as Instructor
    participant D as Dashboard
    participant WS as Workshop Service
    participant CG as Certificate Generator
    participant ES as Email Service
    participant DB as Database
    participant S as Student
    
    I->>D: Mark Workshop Complete
    D->>WS: Update Completion Status
    WS->>DB: Get Attendee List
    DB-->>WS: Return Attendees
    
    loop For Each Attendee
        WS->>WS: Check Attendance (3 days)
        alt Attended All Days
            WS->>CG: Generate Certificate
            CG->>CG: Create PDF
            CG->>ES: Send Certificate
            ES->>S: Deliver Email
            WS->>DB: Update Certificate Status
        else Missed Days
            WS->>ES: Send Incomplete Notice
            ES->>S: Offer Makeup Options
        end
    end
    
    WS->>D: Update Dashboard
    D-->>I: Show Completion Report
```

#### Key Components
- Certificate Generator: Python service using PDF generation library
- Template Storage: Customizable certificate templates
- Email Service: Gmail API integration
- Validation: Attendance verification logic

#### Implementation Status
- ❌ Not Implemented
- 📅 Planned for Phase 2
- 🔗 Related: Workshop Management, Email Service

---

This template provides the foundation for documenting Modern AI Pro's architecture at the right level of abstraction, bridging the gap between business requirements and technical implementation.