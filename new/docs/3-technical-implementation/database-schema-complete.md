# Modern AI Pro Database Schema - Complete Reference

## Quick Table Reference

| Table | Purpose | Key Fields | Records |
|-------|---------|------------|---------|
| **LEADS** | Marketing prospects from Facebook ads | email, status, lead_segment | 10,098 |
| **CUSTOMERS** | Paying customers with LTV tracking | customer_email, total_spent, stripe_customer_id | 1,135 |
| **PAYMENTS** | All payment transactions | amount, payment_method, stripe_payment_intent_id | ~2,000 |
| **WORKSHOPS** | Live workshop cohorts | workshop_type, start_date, enrolled_count | Active |
| **WORKSHOP_ENROLLMENTS** | Who's enrolled in which workshop | lead_id, workshop_id, payment_status | Active |
| **USERS** | Staff members (admin, sales, instructors) | username, role, performance_metrics | 10-50 |
| **STUDENT_USERS** | Learning platform accounts | customer_id, subscription_plan, progress_stats | 0+ |
| **COURSES** | Online course catalog | course_code, course_type, instructor_id | 6 |
| **STUDENT_PROGRESS** | Course completion tracking | completion_percentage, certificate_earned | Learning data |
| **CUSTOMER_EMAILS** | Multiple emails per customer | email_type (payment/certificate/parent) | Multi-email support |
| **CAMPAIGNS** | Facebook campaign performance | conversion_rate, cost_per_lead | Marketing ROI |
| **SUBSCRIPTIONS** | Platform subscription details | plan_type, billing_period, status | Revenue tracking |

---

## Overview

The Modern AI Pro database uses a sophisticated multi-table architecture designed for 400 leads/day scale with proven 9% lead-to-customer conversion tracking. The schema handles complex scenarios including family enrollments where one person pays for another (e.g., father pays for son's course).

## Current System Metrics (August 2025)

- **10,098 total leads** from Facebook campaigns
- **1,135 paying customers** (11.2% conversion rate)
- **1,097 email overlaps** (lead → customer progression)
- **8.4MB primary database** with excellent performance
- **2+ years operational** with proven business metrics

---

## Core Architecture Principles

### 1. Lead-to-Customer Journey
```
Facebook Lead → LEADS table → (conversion) → CUSTOMERS table
```

**LEADS** = Everyone who shows interest (marketing data)
**CUSTOMERS** = Only those who pay (business data)

This separation provides:
- **Performance**: Queries on 1,135 customers vs 10,098 leads
- **Compliance**: Different retention policies (leads vs financial records)
- **Clarity**: Marketing pipeline vs business operations

### 2. Email as Business Key
- Email serves as the primary business identifier (not database primary key)
- Auto-incrementing IDs for performance and referential integrity
- Multiple emails per customer through `customer_emails` table
- Handles scenarios where payment email differs from participant email

### 3. Multi-Email Architecture
The `customer_emails` table supports complex scenarios:

```sql
email_type IN (
    'personal',     -- Primary personal email
    'work',         -- Work email address  
    'payment',      -- Email used for payments (father pays for son)
    'certificate',  -- Email for receiving certificates
    'emergency',    -- Emergency contact email
    'spouse',       -- Family member email
    'parent',       -- Parent email (for child participants)
    'child',        -- Child email (when parent pays)
    'other'         -- Other email types
)
```

---

## Database Schema (ERD)

```mermaid
erDiagram
    LEADS ||--o| CUSTOMERS : "converts to"
    LEADS ||--o{ LEAD_INTERACTIONS : "has"
    LEADS }o--|| CAMPAIGNS : "comes from"
    LEADS ||--o{ WORKSHOP_ENROLLMENTS : "enrolls in"
    
    CUSTOMERS ||--o{ PAYMENTS : "makes"
    CUSTOMERS ||--o{ CUSTOMER_EMAILS : "has"
    CUSTOMERS ||--o{ SUBSCRIPTIONS : "subscribes to"
    CUSTOMERS ||--o{ WORKSHOP_ENROLLMENTS : "enrolls in"
    
    WORKSHOPS ||--o{ WORKSHOP_ENROLLMENTS : "has"
    WORKSHOPS ||--|| WORKSHOP_COMMUNICATIONS : "has"
    
    WORKSHOP_ENROLLMENTS }o--|| PAYMENTS : "paid by"
    
    USERS ||--o{ LEAD_INTERACTIONS : "performs"
    USERS ||--o{ LEADS : "assigned to"
    USERS ||--o{ COURSES : "instructs"
    
    CUSTOMERS ||--o| STUDENT_USERS : "becomes learning platform user"
    STUDENT_USERS ||--o{ STUDENT_PROGRESS : "tracks"
    STUDENT_USERS ||--o{ STUDENT_SESSIONS : "has"
    
    COURSES ||--o{ STUDENT_PROGRESS : "enrolled in"

    LEADS {
        int id PK
        string lead_id UK "Facebook lead ID"
        string full_name
        string email
        string phone_number
        string lead_source
        string lead_source_detail
        string status "New → Contacted → Qualified → Enrolled"
        string lead_segment "Hot/Warm/Medium"
        int customer_id FK "nullable"
        string campaign_name
        string workshop_type
        datetime created_time
        datetime updated_at
    }

    CUSTOMERS {
        int id PK
        int lead_id FK "Required - every customer starts as lead"
        string customer_email "Primary business email"
        string payment_email "May differ from customer_email"
        string stripe_customer_id UK
        string subscription_status
        string customer_segment "new/repeat/loyal/subscriber/vip"
        decimal total_spent "Lifetime value in USD"
        decimal total_spent_usd
        int workshops_completed
        int workshops_enrolled
        date last_purchase_date
        datetime created_at
        datetime updated_at
    }

    PAYMENTS {
        int id PK
        int customer_id FK
        int lead_id FK
        decimal amount
        decimal amount_usd "Normalized for reporting"
        string currency "USD/INR"
        string payment_method "stripe/upi/bank_transfer"
        string stripe_payment_intent_id UK
        string payment_email "Email used for this payment"
        string product_type "workshop/subscription/coaching"
        int workshop_id FK "nullable"
        string status "pending/completed/failed/refunded"
        datetime processed_at
        datetime created_at
    }

    WORKSHOPS {
        int id PK
        string workshop_type "AI Essentials/Practitioner/Agentic AI"
        date start_date
        date end_date
        string session_times "JSON array"
        int max_students "Default 50"
        int enrolled_count
        int completed_count
        string instructor_name
        string status "Scheduled/Active/Completed/Cancelled"
        boolean certificates_generated
        datetime created_at
    }

    WORKSHOP_ENROLLMENTS {
        int id PK
        int lead_id FK
        int customer_id FK "nullable initially"
        int workshop_id FK
        int payment_id FK "nullable"
        string enrollment_source "new_purchase/subscription/package"
        string payment_status "Pending/Paid/Refunded"
        boolean attendance_day1
        boolean attendance_day2  
        boolean attendance_day3
        decimal completion_percentage
        boolean certificate_earned
        datetime enrollment_date
        boolean pre_workshop_email_sent
        boolean reminder_24h_sent
        boolean zoom_link_sent
        boolean post_workshop_email_sent
    }

    LEAD_INTERACTIONS {
        int id PK
        int lead_id FK
        string interaction_type "email_sent/call_attempted/demo_scheduled"
        string user_email "Staff member who performed action"
        string subject
        string message
        string outcome
        string next_action
        datetime interaction_date
        string email_message_id
        string sms_message_id
    }

    CAMPAIGNS {
        int id PK
        string campaign_id UK "Facebook campaign ID"
        string campaign_name
        string account_name "Invento Robotics/Invento_US"
        string account_id
        int total_leads
        int hot_leads
        int warm_leads
        int qualified_leads
        int enrollments
        decimal conversion_rate
        decimal cost_per_lead
        decimal cost_per_enrollment
        date campaign_start_date
        date campaign_end_date
    }

    CUSTOMER_EMAILS {
        int id PK
        int customer_id FK
        string email "Additional email address"
        string email_type "personal/work/payment/certificate/parent/child"
        boolean is_primary
        boolean is_verified
        datetime created_at
    }

    SUBSCRIPTIONS {
        int id PK
        int customer_id FK
        string stripe_subscription_id UK
        string plan_type "basic/premium/team/enterprise"
        string status
        string billing_period "monthly/quarterly/annual"
        decimal amount
        string currency
        date start_date
        date current_period_start
        date current_period_end
        datetime cancelled_at
        int workshops_attended
        int workshops_remaining
    }

    WORKSHOP_COMMUNICATIONS {
        int id PK
        int workshop_id FK
        boolean welcome_email_sent
        datetime welcome_email_date
        int welcome_email_count
        boolean reminder_72h_sent
        boolean reminder_24h_sent
        boolean reminder_1h_sent
        boolean post_workshop_sent
        boolean certificate_batch_sent
        int total_enrolled
        int total_emails_sent
        datetime updated_at
    }

    USERS {
        int id PK
        string username UK
        string email UK
        string password_hash
        string first_name
        string last_name
        string role "admin/executive/sales_rep/bd/instructor"
        string timezone
        boolean is_active
        int leads_assigned
        int leads_qualified
        int enrollments_closed
        decimal avg_response_time_minutes
        datetime last_login
        datetime created_at
    }

    STUDENT_USERS {
        int id PK
        int customer_id FK "Links to paying customer"
        string email UK "Learning platform login"
        string password_hash
        boolean is_active
        boolean email_verified
        string display_name
        string subscription_plan "free/basic/premium/team/lifetime"
        string subscription_status "inactive/active/paused/cancelled/expired"
        string access_level "basic/standard/premium/vip"
        date subscription_start_date
        date subscription_end_date
        int total_courses_enrolled
        int total_courses_completed
        int total_certificates_earned
        decimal total_learning_hours
        int current_streak_days
        datetime last_login
        datetime last_activity
        int login_count
        boolean ai_assistant_enabled
        datetime created_at
        datetime updated_at
    }

    COURSES {
        int id PK
        string course_code UK "AI_ESS_001, AI_PRAC_002"
        string course_title
        string course_description
        string course_type "workshop/self_paced/cohort/masterclass/certification"
        string difficulty_level "beginner/intermediate/advanced/expert"
        string access_level_required "basic/standard/premium/vip"
        int estimated_hours
        int total_modules
        int total_lessons
        decimal price_usd
        decimal price_inr
        string status "draft/published/archived/coming_soon"
        int instructor_id FK "Links to users table"
        datetime created_at
        datetime updated_at
        datetime published_at
    }

    STUDENT_PROGRESS {
        int id PK
        int student_user_id FK
        int course_id FK
        string enrollment_source "direct/subscription/workshop_upgrade/bundle"
        string status "enrolled/in_progress/completed/paused/dropped"
        decimal completion_percentage
        text modules_completed "JSON array"
        text lessons_completed "JSON array"
        int total_time_spent_minutes
        datetime last_accessed_at
        datetime actual_completion_date
        decimal average_quiz_score
        int code_exercises_passed
        boolean certificate_earned
        datetime certificate_issued_date
        int course_rating "1-5 stars"
        text course_review
        datetime created_at
        datetime updated_at
    }

    STUDENT_SESSIONS {
        int id PK
        int student_user_id FK
        int course_id FK "nullable for general platform usage"
        datetime session_start
        datetime session_end
        int duration_minutes
        string device_type "desktop/mobile/tablet"
        int pages_visited
        int videos_watched
        int exercises_attempted
        decimal engagement_score
        datetime created_at
    }
```

---

## Business Logic Flow

**Primary Reference**: See comprehensive user journey flows in [User Workflows Documentation](../7-marketing/user-workflows.md)

### Database-Centric Technical Flow

```mermaid
graph TD
    A[Facebook Lead → LEADS table] --> B{Auto-Segmentation<br/>lead_segment}
    B -->|Hot 70+| C[SDR Assignment<br/>assigned_to]
    B -->|Warm 40-69| D[Priority Queue<br/>status='New']
    B -->|Medium <40| E[Nurture Campaign<br/>status='Nurturing']
    
    C --> F[LEAD_INTERACTIONS<br/>contact logged]
    D --> F
    E --> G[Email/SMS Sequence<br/>via CAMPAIGNS]
    
    F --> H{Qualified?<br/>status='Qualified'}
    G --> H
    
    H -->|Yes| I[Demo Scheduled<br/>interaction_type='demo']
    H -->|No| E
    
    I --> J[Demo Conducted<br/>outcome logged]
    J --> K{Enrolls & Pays?<br/>Payment processed}
    
    K -->|Yes| L[CUSTOMERS record created<br/>+ PAYMENTS entry]
    K -->|No| E
    
    L --> M[WORKSHOP_ENROLLMENTS<br/>payment_status='Paid']
    M --> N[Email automation<br/>WORKSHOP_COMMUNICATIONS]
    N --> O[Workshop delivery<br/>attendance tracking]
    O --> P[Completion tracking<br/>certificate_earned]
    P --> Q[Alumni status<br/>customer_segment updated]
    Q --> R[Subscription upsell<br/>STUDENT_USERS creation]
```

**Key Database Transitions**:
- **LEADS → CUSTOMERS**: 9% conversion rate (1,135 of 10,098)
- **CUSTOMERS → STUDENT_USERS**: Learning platform activation
- **Status progression**: New → Contacted → Qualified → Enrolled → Alumni
- **Revenue tracking**: PAYMENTS → customer total_spent updates

---

## Complex Scenario: Father-Son Enrollment

When a father pays for his son's course, both need separate records:

```sql
-- 1. Father's Lead Record
INSERT INTO leads (lead_id, full_name, email, phone_number, status) 
VALUES ('fb_123_father', 'John Doe Sr', '<EMAIL>', '+1234567890', 'Enrolled');

-- 2. Son's Lead Record  
INSERT INTO leads (lead_id, full_name, email, phone_number, status)
VALUES ('fb_124_son', 'John Doe Jr', '<EMAIL>', '+1234567891', 'Enrolled');

-- 3. Father's Customer Record (handles payment)
INSERT INTO customers (lead_id, customer_email, payment_email, total_spent)
VALUES (1, '<EMAIL>', '<EMAIL>', 320.00);

-- 4. Son's Customer Record (workshop participant)
INSERT INTO customers (lead_id, customer_email, payment_email, total_spent) 
VALUES (2, '<EMAIL>', '<EMAIL>', 0.00);

-- 5. Link Father's Email to Son's Record
INSERT INTO customer_emails (customer_id, email, email_type, is_primary) VALUES
(2, '<EMAIL>', 'parent', FALSE);  -- Son can receive emails at father's address

-- 6. Single Payment Record
INSERT INTO payments (customer_id, lead_id, amount, payment_email, product_type, status)
VALUES (1, 1, 320.00, '<EMAIL>', 'workshop', 'completed');

-- 7. Both Workshop Enrollments
INSERT INTO workshop_enrollments (lead_id, customer_id, workshop_id, payment_id, payment_status) VALUES
(1, 1, 101, 1, 'Paid'),    -- Father enrolled
(2, 2, 101, 1, 'Paid');    -- Son enrolled, same payment
```

This structure allows:
- **Separate tracking**: Individual completion, certificates, future enrollments
- **Shared payment**: Clear financial responsibility
- **Flexible communication**: Emails can go to either address
- **Business intelligence**: Proper attribution for both participants

---

## Learning Platform Architecture

### Two Distinct User Systems

#### 1. Staff Users (USERS table)
- **Purpose**: Internal operations, dashboard access, course instruction
- **Examples**: Admin, sales reps, instructors, BD team  
- **Roles**: admin, executive, sales_rep, bd, instructor
- **Data**: Username, role permissions, performance metrics
- **Scale**: 10-50 users (controlled growth)

#### 2. Student Users (STUDENT_USERS table)
- **Purpose**: Learning platform access, course progress tracking
- **Examples**: Paying customers who access educational content
- **Link**: Connected to CUSTOMERS table via customer_id
- **Data**: Course progress, certificates, learning analytics, subscription status
- **Scale**: 10,000+ users (exponential growth)

### Customer to Student User Journey

```sql
-- When a customer wants learning platform access:
-- 1. Customer record exists (from workshop payment)
INSERT INTO customers (customer_email, total_spent) 
VALUES ('<EMAIL>', 320.00);

-- 2. Create student_users record linked to customer
INSERT INTO student_users (customer_id, email, subscription_plan, access_level)
SELECT id, customer_email, 'premium', 'standard'
FROM customers WHERE customer_email = '<EMAIL>';

-- 3. Enroll in courses based on subscription
INSERT INTO student_progress (student_user_id, course_id, enrollment_source)
VALUES (1, 1, 'subscription');
```

### Learning Analytics Capability

The new student user system provides comprehensive analytics:

```sql
-- Individual student dashboard
SELECT 
    su.display_name,
    su.total_courses_enrolled,
    su.total_courses_completed,
    su.total_certificates_earned,
    su.current_streak_days,
    AVG(sp.completion_percentage) as avg_progress
FROM student_users su
LEFT JOIN student_progress sp ON su.id = sp.student_user_id
WHERE su.customer_id = @customer_id;

-- Course performance metrics  
SELECT 
    c.course_title,
    COUNT(sp.id) as enrollments,
    AVG(sp.completion_percentage) as avg_completion,
    AVG(sp.course_rating) as avg_rating
FROM courses c
LEFT JOIN student_progress sp ON c.id = sp.course_id
GROUP BY c.id;
```

### Subscription & Access Levels

The system supports tiered access:

- **Free**: Workshop materials only
- **Basic** ($30/month): + Practice exercises  
- **Premium** ($500/quarter): + Live sessions, 1:1 coaching
- **VIP**: + Everything, priority support
- **Lifetime**: Permanent access to all content

---

## Performance & Scale

### Current Performance
- **8.4MB database** handles 10K+ leads efficiently
- **Indexed lookups** on email, lead_id, customer_id
- **Optimized views** for dashboard queries
- **Sub-second response** for all common queries

### Scale Projections (Business Plan)
- **400 leads/day** = 146K leads/year
- **9% conversion** = 13K customers/year  
- **Database growth** = ~30MB/year (sustainable)
- **Performance maintained** with proper indexing

### Key Indexes
```sql
-- Performance-critical indexes
CREATE INDEX idx_leads_email ON leads(email);
CREATE INDEX idx_leads_status_created ON leads(status, created_time);
CREATE INDEX idx_customers_lead_id ON customers(lead_id);
CREATE INDEX idx_customers_email ON customers(customer_email);
CREATE INDEX idx_payments_customer_id ON payments(customer_id);
CREATE INDEX idx_customer_emails_email ON customer_emails(email);

-- Student platform indexes
CREATE INDEX idx_student_users_customer ON student_users(customer_id);
CREATE INDEX idx_student_users_email ON student_users(email);
CREATE INDEX idx_student_users_subscription ON student_users(subscription_status, subscription_plan);
CREATE INDEX idx_progress_student ON student_progress(student_user_id);
CREATE INDEX idx_progress_course ON student_progress(course_id);
CREATE INDEX idx_sessions_student ON student_sessions(student_user_id);
CREATE INDEX idx_courses_code ON courses(course_code);
```

---

## Data Quality & Integrity

### Current Issues Identified
- **217 orphaned customers** (no matching lead record)
- **106 duplicate customer emails** (need deduplication)
- **56 email mismatches** (lead.email ≠ customer.customer_email)
- **26 duplicate emails in leads**

### Integrity Constraints Needed
```sql
-- Prevent orphaned customers
ALTER TABLE customers 
ADD CONSTRAINT fk_customer_lead 
FOREIGN KEY (lead_id) REFERENCES leads(id) ON DELETE RESTRICT;

-- Prevent duplicate customer emails
CREATE UNIQUE INDEX idx_customer_email_unique ON customers(customer_email);

-- Ensure email consistency
CREATE TRIGGER ensure_email_consistency
AFTER INSERT ON customers
BEGIN
  UPDATE customers 
  SET customer_email = (SELECT email FROM leads WHERE id = NEW.lead_id)
  WHERE id = NEW.id AND NEW.customer_email IS NULL;
END;
```

---

## Security & Compliance

### Data Protection
- **PCI Compliance**: No sensitive payment data stored (Stripe handles)
- **GDPR Compliance**: Lead data purging after 6 months if no conversion
- **Financial Records**: Customer/payment data retained 7 years
- **Access Control**: Role-based permissions (admin/executive/sales_rep)

### Audit Trail
- All tables include `created_at` and `updated_at` timestamps
- Lead interactions tracked with user attribution
- Payment status changes logged with reasons
- Workshop communications tracked for deliverability

---

## Migration History & Future

### Schema Evolution
1. **V1 (2023)**: Basic lead management, single table approach
2. **V2 (2024)**: Customer separation, payment tracking, subscriptions
3. **V2.1 (2025)**: Enhanced email types for family scenarios
4. **V3 (Future)**: AI-powered lead scoring, advanced segmentation

### Current Files
- **`leads.db`** (8.4MB) - Production database
- **`dashboard.db`** - User authentication  
- **`schema_v2_migration.sql`** - Latest schema definition
- **`apply_email_enhancements.sql`** - Recent email improvements

### Next Steps
1. **Data cleanup**: Fix orphaned records and duplicates
2. **Add constraints**: Implement referential integrity
3. **Performance monitoring**: Set up query analysis
4. **Backup strategy**: Implement automated backups

---

## Quick Reference

### Key Metrics
- **Conversion Rate**: 9.08% (industry standard: 2-5%)
- **Customer LTV**: $500+ average
- **Response Time**: <5 minutes target
- **Workshop Completion**: 80%+ for certificates

### Common Queries
```sql
-- Dashboard: Today's hot leads
SELECT * FROM leads 
WHERE lead_segment = 'Hot' AND status = 'New' 
ORDER BY created_time;

-- Sales: Customer lifetime value
SELECT customer_email, total_spent_usd, workshops_completed
FROM customers 
ORDER BY total_spent_usd DESC;

-- Operations: Workshop roster
SELECT l.full_name, l.email, w.workshop_type, w.start_date
FROM workshop_enrollments we
JOIN leads l ON we.lead_id = l.id
JOIN workshops w ON we.workshop_id = w.id
WHERE we.payment_status = 'Paid';

-- Learning Platform: Active students
SELECT su.display_name, su.total_courses_enrolled, 
       su.total_courses_completed, su.last_login
FROM student_users su
WHERE su.is_active = TRUE AND su.subscription_status = 'active'
ORDER BY su.last_login DESC;

-- Course Analytics: Top performing courses
SELECT c.course_title, COUNT(sp.id) as enrollments,
       AVG(sp.completion_percentage) as avg_progress,
       AVG(sp.course_rating) as avg_rating
FROM courses c
JOIN student_progress sp ON c.id = sp.course_id
WHERE c.status = 'published'
GROUP BY c.id
ORDER BY avg_rating DESC, enrollments DESC;
```

This schema successfully supports Modern AI Pro's proven business model with room for significant scale growth.