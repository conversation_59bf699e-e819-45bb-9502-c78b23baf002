# Lead Assignment Strategy

## Overview
This document outlines the automated lead assignment logic for our sales team to optimize lead distribution and improve conversion rates.

## Sales Team
- **Man<PERSON>** (sales) - Primary focus: Indian market, domestic leads
- **<PERSON><PERSON>kshmi <PERSON>** (sales) - Primary focus: International market, global leads

## Assignment Rules

### 1. Existing Leads Assignment (One-time)
**Based on Phone Number Geography:**

- **Indian Numbers � Manish**
  - Phone starts with `+91`
  - Phone starts with `91` (without +)
  - Rationale: Local market expertise, timezone alignment

- **International Numbers � Mahalakshmi**
  - All other phone number formats
  - No phone number provided
  - Rationale: International market focus, diverse timezone handling

### 2. New Facebook Leads Assignment (Ongoing)
**Dynamic Configuration-Based Distribution:**

**Current Assignment (Launch Day):**
- **Manish: 100%** of new leads
- **Mahalakshmi: 0%** of new leads

**Configuration File:** `/config/lead_assignment.yaml`
```yaml
# Change these percentages to adjust lead distribution without code changes
manish_percentage: 100
mahalakshmi_percentage: 0
```

**Implementation:**
- Dynamic assignment ratios loaded from YAML configuration
- Use deterministic algorithm based on lead ID hash
- Ensures consistent assignment if script re-runs
- Distributes leads evenly over time based on configured percentages
- **No code changes required** - just edit the YAML file

**Implementation File:** 
`/scripts/integrations/facebook/lead_assignment_logic.py`

### 3. Manual Assignment Override
- Admin and sales users can manually reassign any lead
- Override persists and won't be changed by automated rules
- Useful for:
  - Special client requirements
  - Workload balancing
  - Skills-based assignment

## User IDs Reference
```
Manish Tulasi: user_id = 3
Mahalakshmi Radhakrushnun: user_id = 9
```

## Implementation Status

###  Completed
- [x] Manual assignment UI in leads dashboard
- [x] Assignment API endpoints
- [x] Database schema support

### = In Progress
- [ ] Existing leads phone-based assignment script
- [ ] Facebook leads 20:80 distribution logic
- [ ] Assignment audit logging

### =� Planned
- [ ] Advanced assignment rules (skills, availability, timezone)
- [ ] Performance-based assignment weights
- [ ] Assignment analytics dashboard

## Future Enhancements

### Geographic Assignment Rules
- Country-based assignment for better timezone alignment
- Language preference matching
- Regional expertise consideration

### Performance-Based Assignment
- Adjust ratios based on conversion rates
- Reward high-performing reps with more qualified leads
- Balance workload based on current pipeline

### Smart Assignment
- Lead scoring integration
- Skills-based routing
- Availability-based assignment
- Follow-up scheduling optimization

## Assignment Audit Trail
All assignment changes are tracked with:
- Timestamp
- Assigner (system/user)
- Previous assignee
- New assignee
- Reason code
- Lead characteristics at time of assignment

## Configuration

### Current Configuration System
Assignment ratios are managed through:
- **Primary:** `/config/lead_assignment.yaml` - Edit percentages without code changes
- **Implementation:** `/scripts/integrations/facebook/lead_assignment_logic.py`

### Configuration File Structure
```yaml
# Lead Assignment Configuration
manish_percentage: 100    # 0-100
mahalakshmi_percentage: 0 # 0-100
manish_id: 3             # Database user ID
mahalakshmi_id: 9        # Database user ID
```

### How to Change Assignment Ratios
1. Edit `/config/lead_assignment.yaml`
2. Change the percentage values (must add up to 100)
3. Save the file - changes take effect immediately
4. No code deployment or restart required

### Future Configuration Options
- Database: `assignment_rules` table (planned)
- Admin panel for rule management (planned)
- Performance-based automatic adjustments (planned)

---
*Last updated: August 14, 2025*
*Version: 2.0 - Dynamic YAML Configuration*