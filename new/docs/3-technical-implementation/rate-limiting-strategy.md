# Rate Limiting Strategy

## Overview
Smart rate limiting implementation that balances security with user experience by categorizing APIs based on sensitivity and usage patterns.

## Rate Limiting Categories

### **No Rate Limiting** (High-frequency UI APIs)
APIs called frequently by the user interface that need unrestricted access:

- `/api/users/assignable` - User dropdown lists
- `/api/leads` - Main data tables and search
- `/api/facebook/accounts` - Dashboard widgets
- `/api/facebook/campaigns` - Dashboard widgets  
- `/api/facebook/lead-ads` - Dashboard widgets

**Rationale:** These APIs power the UI and may be called hundreds of times during normal usage. Rate limiting causes "Failed to load" errors and poor UX.

### **Rate Limited APIs** (Sensitive/Financial)
APIs handling sensitive data that require protection:

- `/api/payments` - Financial data (500 requests/hour)
- Other financial/sensitive endpoints as needed

**Rationale:** Financial and sensitive data requires protection against abuse while still allowing reasonable business usage.

## Configuration

### Environment Variables
```bash
# .env file
RATE_LIMIT_MAX_REQUESTS=500
RATE_LIMIT_WINDOW_MS=3600000  # 1 hour
```

### Implementation
```javascript
// High-frequency APIs - No rate limiting
export default requireDataAccess('leads')(handler)

// Sensitive APIs - Rate limited
export default requireDataAccess('financial')(
  rateLimit(500, 60 * 60 * 1000)(handler)
)
```

## Helper Functions Available
- `standardRateLimit()` - Uses env config (500/hour)
- `highFrequencyRateLimit()` - 2x standard (1000/hour)
- `lowFrequencyRateLimit()` - Half standard (250/hour)

## Problem Solved
**Before:** Users seeing "Failed to load users", "Unassigned" leads, and rate limit errors  
**After:** Smooth UI operation with appropriate security for sensitive data

## Monitoring
Rate limit configuration logged on server startup:
```
🚦 [RATE LIMIT] Configuration: { maxRequests: 500, windowMs: 3600000, windowMinutes: 60 }
```