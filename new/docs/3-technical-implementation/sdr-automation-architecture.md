# SDR Automation Architecture - MVP

## System Overview

Modern AI Pro's MVP focuses on automating the SDR workflow to scale lead conversion from 50-60 leads/day to 400 leads/day while maintaining 6-7% conversion rates.

## Core Architecture

```mermaid
graph TB
    subgraph "Lead Sources"
        FB[Facebook Lead Ads]
        FORM[Landing Page Forms]
    end
    
    subgraph "Lead Processing"
        WEBHOOK[Lead Webhooks]
        IMPORT[Lead Import Service]
        SCORE[Lead Scoring Engine]
        ASSIGN[Assignment Engine]
        SELECT[Lead Selector Service]
    end
    
    subgraph "SDR Dashboard"
        CRM[Lead Management]
        NOTIFY[Real-time Notifications]
        ACTIONS[Quick Actions]
        METRICS[Performance Metrics]
    end
    
    subgraph "Communication"
        EMAIL[Email Sequences]
        SMS[SMS/Twilio]
        PAYMENT[Payment Links]
    end
    
    subgraph "Analytics"
        FUNNEL[Conversion Funnel]
        PERF[SDR Performance]
        REV[Revenue Tracking]
    end
    
    FB --> WEBHOOK
    FORM --> WEBHOOK
    WEBHOOK --> IMPORT
    IMPORT --> SCORE
    SCORE --> ASSIGN
    ASSIGN --> SELECT
    SELECT --> CRM
    CRM --> NOTIFY
    CRM --> ACTIONS
    ACTIONS --> EMAIL
    ACTIONS --> SMS
    ACTIONS --> PAYMENT
    CRM --> METRICS
    METRICS --> FUNNEL
    METRICS --> PERF
    METRICS --> REV
```

## Lead Lifecycle State Machine

```mermaid
stateDiagram-v2
    [*] --> New: Facebook Import
    New --> Assigned: Auto Assignment
    Assigned --> Contacted: SDR Action
    Contacted --> Qualified: SDR Assessment
    Qualified --> Payment_Sent: Payment Link
    Payment_Sent --> Enrolled: Stripe Success
    Payment_Sent --> Lost: Payment Failed
    Contacted --> Nurture: Low Engagement
    Nurture --> Qualified: Re-engagement
    Nurture --> Lost: No Response
    Qualified --> Lost: SDR Decision
    Enrolled --> [*]
    Lost --> [*]
```

## System Components

### 1. Lead Import Engine

```mermaid
sequenceDiagram
    participant FB as Facebook Lead Ads
    participant WH as Webhook Handler  
    participant LS as Lead Scorer
    participant DB as Database
    participant AS as Assignment Service
    participant SDR as SDR Dashboard
    
    FB->>WH: Lead Submission
    WH->>LS: Score Lead Profile
    LS->>DB: Store Lead + Score
    DB->>AS: Trigger Assignment
    AS->>DB: Update Assignment
    AS->>SDR: Real-time Notification
    SDR->>SDR: Display New Lead
```

### 2. Email Automation Flow

```mermaid
graph LR
    subgraph "7-Day Email Sequence"
        D1[Day 1: Welcome + Ebook]
        D2[Day 2: Value Follow-up]  
        D3[Day 3: Case Study]
        D4[Day 4: Social Proof]
        D5[Day 5: Limited Offer]
        D6[Day 6: Final Call]
        D7[Day 7: SDR Handoff]
    end
    
    TRIGGER[Lead Import] --> D1
    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> D5
    D5 --> D6
    D6 --> D7
    D7 --> SDR_QUEUE[SDR Call Queue]
```

### 3. Payment Processing Architecture

```mermaid
sequenceDiagram
    participant SDR as SDR Dashboard
    participant STRIPE as Stripe API
    participant WH as Webhook Handler
    participant DB as Database
    participant EMAIL as Email Service
    
    SDR->>STRIPE: Create Payment Link
    STRIPE->>SDR: Return Link
    SDR->>Customer: Send Payment Link
    Customer->>STRIPE: Complete Payment
    STRIPE->>WH: Payment Success Webhook
    WH->>DB: Update Lead Status
    WH->>EMAIL: Send Confirmation
    DB->>SDR: Update Dashboard
```

### 4. Lead Selector Service

The Lead Selector Service provides intelligent lead selection for campaign orchestration and SDR workflows.

```mermaid
graph TD
    subgraph "Lead Selector API"
        LATEST[Get Latest Unprocessed]
        RECENT[Get Recent Leads]
        STATUS[Get by Status]
        CRITERIA[Get by Criteria]
        STATS[Lead Statistics]
    end
    
    subgraph "Selection Logic"
        FILTER[Lead Filtering]
        ENRICH[Data Enrichment]
        PRIORITY[Priority Scoring]
        INTERACT[Interaction History]
    end
    
    subgraph "Data Sources"
        LEADS_DB[(Leads Table)]
        INTERACTIONS_DB[(Lead Interactions)]
        FB_DATA[Facebook Form Data]
    end
    
    LATEST --> FILTER
    RECENT --> FILTER
    STATUS --> FILTER
    CRITERIA --> FILTER
    FILTER --> ENRICH
    ENRICH --> PRIORITY
    PRIORITY --> INTERACT
    
    LEADS_DB --> FILTER
    INTERACTIONS_DB --> INTERACT
    FB_DATA --> ENRICH
```

#### Lead Selection Methods

| Method | Purpose | Key Parameters |
|--------|---------|----------------|
| `latest` | Get most recent unprocessed lead | `hours_back`, `status` |
| `recent` | Get recent leads for bulk campaigns | `hours`, `limit`, `location` |
| `status` | Get leads by lifecycle status | `status`, `include_interactions` |
| `criteria` | Advanced filtering | `location`, `campaign`, `assigned_to` |
| `stats` | System monitoring | Lead counts, conversion rates |

#### Status-Based Lead Selection

```mermaid
graph LR
    subgraph "Lead Statuses"
        NEW[New: 5,576]
        CONTACTED[Contacted: 763]
        CUSTOMER[Customer: 768]
        ENROLLED[Enrolled: 88]
        QUALIFIED[Qualified: 2]
    end
    
    subgraph "Campaign Types"
        INTRO[Intro Campaigns]
        FOLLOWUP[Follow-up Campaigns]
        UPSELL[Upsell Campaigns]
        COMPLETION[Completion Campaigns]
    end
    
    NEW --> INTRO
    CONTACTED --> FOLLOWUP
    CUSTOMER --> UPSELL
    ENROLLED --> COMPLETION
```

#### Priority Scoring Algorithm

```mermaid
graph TD
    BASE[Base Priority Score]
    
    subgraph "Scoring Factors"
        RECENCY[Hours Since Created<br/>< 1hr: +100<br/>< 6hr: +50<br/>< 24hr: +25]
        CONTACT[Contact Info<br/>Phone: +10<br/>Experience: +5<br/>Goals: +5]
        FB_FORM[Facebook Form Data<br/>Complete: +10]
        LOCATION[Location Detection<br/>Phone-based]
    end
    
    subgraph "Interaction Adjustments"
        ENROLLED[Enrolled: ×0]
        HEAVY_CONTACT[3+ Emails: ×0.1]
        RESPONSIVE[Response Received: ×2]
        FOLLOWUP[Follow-up Required: ×1.5]
    end
    
    BASE --> RECENCY
    BASE --> CONTACT
    BASE --> FB_FORM
    RECENCY --> ENROLLED
    CONTACT --> HEAVY_CONTACT
    FB_FORM --> RESPONSIVE
    LOCATION --> FOLLOWUP
```

#### Usage Examples

```bash
# Get latest unprocessed lead for immediate action
./venv/bin/python core/lead_selector.py --action latest

# Get top 10 recent leads for bulk campaign
./venv/bin/python core/lead_selector.py --action recent --hours 168 --limit 10

# Get contacted leads needing follow-up
./venv/bin/python core/lead_selector.py --action status --status "Contacted" --include-interactions

# Get customers for upsell campaigns
./venv/bin/python core/lead_selector.py --action status --status "Customer" --limit 20

# Get leads by location for regional campaigns
./venv/bin/python core/lead_selector.py --action criteria --location "US" --limit 50

# Monitor system health
./venv/bin/python core/lead_selector.py --action stats
./venv/bin/python core/lead_selector.py --action status-summary
```

#### API Response Format

```json
{
  "timestamp": "2025-08-14T10:52:57.349639",
  "action": "status",
  "parameters": {
    "status": "Contacted",
    "limit": 10,
    "include_interactions": true
  },
  "count": 5,
  "leads": [
    {
      "id": 13404,
      "full_name": "Pallavi Gaikwad",
      "email": "<EMAIL>",
      "detected_location": "US",
      "priority_score": 45,
      "facebook_form_data": {
        "programming_proficiency": "intermediary",
        "goals": "exploring_new_job_opportunities"
      },
      "interactions": [],
      "email_count": 0,
      "enrolled": false,
      "follow_up_required": false
    }
  ]
}
```

## Data Architecture

### Lead Scoring Matrix

```mermaid
graph TD
    LEAD[New Lead] --> TITLE{Job Title}
    TITLE -->|Executive/Director| HOT[Hot: 15-20%]
    TITLE -->|Manager/Senior| WARM[Warm: 8-12%]  
    TITLE -->|Other| MEDIUM[Medium: 3-6%]
    
    HOT --> ASSIGN_TOP[Assign to Top SDR]
    WARM --> ASSIGN_RR[Round Robin]
    MEDIUM --> ASSIGN_RR
    
    ASSIGN_TOP --> NOTIFY_1[Instant Notification]
    ASSIGN_RR --> NOTIFY_5[5-minute Notification]
```

### Performance Metrics Dashboard

```mermaid
graph TB
    subgraph "Executive View"
        FUNNEL[Conversion Funnel]
        REVENUE[Daily/Weekly Revenue]
        LEADS[Lead Volume Trends]
        PERF[Team Performance]
    end
    
    subgraph "SDR View"
        MYCONVERSIONS[My Conversions]
        MYLEADS[My Lead Queue]
        TARGETS[Target Progress]
        LEADERBOARD[Team Ranking]
    end
    
    subgraph "Admin View"
        SYSTEM[System Health]
        INTEGRATIONS[API Status]
        COSTS[Communication Costs]
        ASSIGNMENTS[Lead Distribution]
    end
```

## Technology Stack (MVP)

### Frontend
- **Next.js 14**: React framework with App Router
- **Tailwind CSS**: Utility-first styling
- **React Query**: Server state management
- **Chart.js**: Analytics visualizations

### Backend
- **Next.js API Routes**: Serverless backend
- **SQLite**: Local development database
- **Prisma**: Database ORM and migrations
- **Lead Selector Service**: Intelligent lead selection and prioritization
- **Email Library**: Multi-sender email automation with attachments

### External Integrations
- **Facebook Lead Ads API**: Lead collection
- **Stripe API**: Payment processing and webhooks
- **Gmail SMTP**: Email automation
- **Twilio API**: SMS communication
- **Brevo API**: WhatsApp integration (future)

### Infrastructure
- **Vercel**: Deployment platform
- **Railway**: Database hosting (production)
- **GitHub**: Version control and CI/CD

## Security & Compliance

### Data Protection
- Lead data encrypted at rest
- API keys stored in environment variables
- Role-based access control (Admin, SDR)
- Session-based authentication

### Integration Security
- Webhook signature verification
- Rate limiting for external APIs
- Secure payment processing via Stripe
- GDPR compliance for lead data handling

## Performance Targets

### Lead Processing
- **Import Speed**: <30 seconds from Facebook to dashboard
- **Assignment Speed**: <1 minute automated assignment
- **Notification Speed**: <5 minutes SDR notification

### Dashboard Performance  
- **Load Time**: <2 seconds for lead views
- **Real-time Updates**: <500ms notification display
- **Analytics**: <3 seconds for conversion reports

### System Reliability
- **Uptime**: 99.5% dashboard availability
- **Facebook Integration**: 99% successful imports
- **Payment Processing**: 99% webhook success rate
- **Email Delivery**: 95% successful automation

## Deployment Architecture

```mermaid
graph TB
    subgraph "Development"
        DEV[Local SQLite]
        NEXT_DEV[Next.js Dev Server]
    end
    
    subgraph "Production"
        VERCEL[Vercel Platform]
        RAILWAY[Railway Database]
        STRIPE_PROD[Stripe Production]
        FB_PROD[Facebook Production]
    end
    
    DEV --> VERCEL
    VERCEL --> RAILWAY
    VERCEL --> STRIPE_PROD
    VERCEL --> FB_PROD
```

## Implementation Phases

### Phase 1 (Weeks 1-2): Core Infrastructure
- Facebook lead import automation
- Basic lead dashboard and assignment
- Stripe payment processing
- Admin role management
- **Lead Selector Service**: Core selection algorithms

### Phase 2 (Weeks 3-4): Automation & Analytics
- Email sequence automation with Lead Selector integration
- SMS integration via Twilio
- Conversion funnel analytics
- SDR performance metrics
- **Advanced Lead Selection**: Status-based campaigns and priority scoring

### Future Enhancements (Post-MVP)
- Advanced lead scoring with ML
- WhatsApp integration via Brevo
- Predictive analytics dashboard
- Advanced reporting and exports