# Modern AI Pro - Product Requirements & Implementation Timeline

## System Architecture
3-component Next.js application with role-based routing:

### 1. Marketing (`/landing`)
- Marketing automation and visitor acquisition
- Features, pricing, and sign-up flow
- Email sequences and newsletter management
- Lead capture and nurturing workflows
- Accessible to all users

### 2. <PERSON>rn (`/learn`) 
- Student portal for workshops and courses
- Progress tracking and certificate downloads
- Session recordings and materials access
- AI skills assessments and personalized pathways
- Role: `student`

### 3. Dashboard (`/dashboard`)
- Analytics and CRM for business operations
- Lead management and campaign tracking
- User administration and content management
- Roles: `admin`, `executive`, `sales_rep`, `bd`

## User Flow
1. Visitors land on marketing page (`/landing`)
2. Lead capture triggers email sequences and CRM tracking
3. Sign up creates account with appropriate role
4. Automatic role-based redirect:
   - Students → `/learn` (workshop portal)
   - Business users → `/dashboard` (analytics)
5. Marketing automation handles nurturing and follow-ups
6. Seamless navigation between sections (admin users only)

## Authentication
- JWT-based auth with Next.js API routes
- SQLite database with bcrypt password hashing
- Protected routes and role-based access control
- Hydration-safe state management

## Implementation Timeline

### Phase 1: Dashboard & Marketing Foundation (By August 30, 2025)
**Goal**: Build core infrastructure for lead management and analytics

#### Dashboard Component (`/dashboard`)
- **Lead Management System**
  - Real-time Facebook ads integration (every minute polling)
  - Lead listing with Hot/Warm/Medium segmentation
  - Status tracking through sales funnel stages
  - Contact information and professional background display
  - Lead assignment and priority scoring
- **Analytics Dashboard**
  - Conversion funnel visualization (6-7% target tracking)
  - Campaign performance metrics and ROI
  - Sales team performance tracking
  - Lead response time analytics (<5 minutes goal)
  - Geographic performance (US/India/Middle East)
- **CRM Functions**
  - Email/WhatsApp/SMS sending capabilities
  - Call scheduling and tracking
  - Follow-up task management
  - Lead notes and interaction history
- **User Management**
  - Role-based access control (admin, executive, sales_rep)
  - Team member management
  - Permission settings

#### Marketing Component (`/landing`)
- **Landing Pages**
  - Workshop-specific landing pages (6 types)
  - Responsive design with A/B testing framework
  - Social proof and testimonials
  - Clear CTAs for lead capture
- **Lead Capture Forms**
  - Facebook lead ads integration
  - Direct sign-up forms with validation
  - Geographic detection for pricing display
  - GDPR compliance and data privacy
- **Basic Marketing Automation**
  - Instant response system (<5 minutes)
  - 6-step email sequence setup
  - SMS/WhatsApp integration (Twilio)
  - Newsletter subscription management
  - Bounce handling and unsubscribe

#### Infrastructure Setup
- **Database Schema**
  - Leads table with segmentation fields
  - User management tables
  - Campaign tracking tables
  - Email sequence tracking
- **API Integrations**
  - Facebook Lead Ads API
  - Email service provider (SendGrid/Mailchimp)
  - SMS gateway (Twilio)
  - WhatsApp Business API
- **Deployment**
  - Azure VM setup
  - SSL certificates
  - Monitoring and logging
  - Daily backups

### Phase 2: Learning Platform (By September 30, 2025)
**Goal**: Enable direct user engagement through assessments and learning

#### Learn Component (`/learn`)
- **User Onboarding**
  - Account creation from marketing funnel
  - Profile setup with role/company information
  - Learning pathway selection
  - Welcome sequence activation
- **AI Skills Assessment**
  - Initial AI literacy assessment for all users
  - Dynamic question branching based on responses
  - Skill level determination (Beginner/Intermediate/Advanced)
  - Workshop recommendations based on results
  - Results dashboard with visual representation
- **Assessment Engine**
  - Question bank management system
  - Multiple question types (MCQ, coding, scenario-based)
  - Time tracking and progress saving
  - Adaptive difficulty adjustment
  - Performance analytics and insights
- **Learning Pathways**
  - 6 workshop-specific pathways
  - Prerequisites and recommended sequences
  - Progress tracking and milestone badges
  - Personalized recommendations
- **Content Delivery**
  - Video player with progress tracking
  - Workshop materials download center
  - Resource library organization
  - Mobile-responsive learning interface
- **Certificate System**
  - Automated certificate generation
  - Completion tracking
  - Social sharing integration
  - Verification system

#### Teaching Tools (`/teach`)
- **Live Class Management** (`/teach/live-class`)
  - Real-time workshop delivery interface
  - Student attendance tracking
  - Interactive session controls
  - Screen sharing and presentation tools
  - Session recording capabilities
- **Feedback Collection Tools** (`/teach/feedback-tools`)
  - Interactive polling with real-time bar charts
  - Word cloud generation from participant responses
  - Google Slides integration with auto-advance
  - Sentiment analysis and feedback analytics
  - Export capabilities for post-session analysis
- **Instructor Dashboard**
  - Workshop scheduling and management
  - Student progress monitoring
  - Content library for instructors
  - Performance metrics and engagement tracking
  - Role-based access (admin, teacher, executive only)

#### Data Collection for Strategic Decision
- **User Behavior Tracking**
  - Time spent on assessments
  - Content engagement metrics
  - Drop-off points identification
  - Feature usage analytics
- **Strategic Data Points**
  - Role and seniority capture
  - Company size and industry
  - Team size and budget authority
  - AI maturity indicators
  - Enterprise needs signals

#### Integration Points
- **Dashboard Connection**
  - Assessment results feed to CRM
  - Lead scoring updates based on engagement
  - SDR alerts for high-intent behaviors
  - Conversion tracking from learn to purchase
- **Marketing Automation**
  - Triggered emails based on assessment results
  - Personalized follow-up sequences
  - Workshop recommendations
  - Nurture campaigns for different pathways

## Success Metrics

### August 30 Deliverables
- ✅ 100 leads/day processing capability
- ✅ <5 minute automated response time
- ✅ Complete sales funnel tracking
- ✅ 6 workshop landing pages live
- ✅ Basic email/SMS automation active
- ✅ Role-based dashboard access
- ✅ Real-time analytics dashboard

### September 30 Deliverables
- ✅ AI skills assessment live
- ✅ 500+ assessment questions deployed
- ✅ Learning pathways for all 6 workshops
- ✅ Certificate automation functional
- ✅ User behavior tracking active
- ✅ Strategic data collection framework
- ✅ 90%+ data completeness on key fields

## Technical Stack
- **Frontend**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS
- **Database**: SQLite (PostgreSQL ready)
- **Authentication**: JWT with secure cookies
- **Email**: SendGrid API
- **SMS/WhatsApp**: Twilio
- **Hosting**: Azure VM
- **Monitoring**: Application Insights

## Risk Mitigation
- Phased rollout with feature flags
- A/B testing framework for optimization
- Comprehensive error logging
- Daily backups and disaster recovery
- Load testing for 400 leads/day capacity
- Fallback systems for critical integrations