# Modern AI Pro Technical Specifications

## Overview
This document outlines the technical architecture and implementation details for Modern AI Pro's lead management, customer tracking, and workshop operations system.

## Database Architecture

### Core Design Principles
- **Lead-to-Customer Journey**: Clear separation between leads (marketing) and customers (revenue)
- **Multi-Channel Attribution**: Track leads from various sources (Meta, Google, referrals, etc.)
- **Payment Flexibility**: Handle multiple payment methods and email mismatches
- **Workshop Operations**: Support multiple workshop enrollments per customer
- **Scalability**: Designed for 400 leads/day and 750 customers/month

### Schema Overview

#### 1. Lead Management
```
leads                     → Core lead information from all sources
lead_interactions        → Track all touchpoints (calls, emails, SMS)
campaigns               → Campaign performance tracking
```

#### 2. Customer & Payment Management
```
customers               → Paying customer records
payments               → All payment transactions
customer_emails        → Multiple emails per customer
subscriptions         → Subscription details
```

#### 3. Workshop Operations
```
workshops              → Workshop cohorts
workshop_enrollments   → Student enrollments (many-to-many)
workshop_communications → Bulk email tracking
```

### Key Relationships
```
leads (1) → (1) customers
customers (1) → (many) payments
customers (1) → (many) workshop_enrollments
workshops (1) → (many) workshop_enrollments
customers (1) → (many) customer_emails
customers (1) → (many) subscriptions
```

## Lead Source Management

### Supported Lead Sources
- **meta_ads**: Facebook/Instagram campaigns
- **google_ads**: Google Ads campaigns
- **referral**: Customer referral program
- **organic**: SEO/direct traffic
- **email_campaign**: Email marketing
- **direct**: Direct signups
- **partner**: Partner channels
- **event**: Conferences/webinars

### Lead Source Tracking
```javascript
// Example lead source detail JSON
{
  // For referrals
  "referrer_customer_id": 123,
  "referral_code": "JOHN20",
  
  // For campaigns
  "utm_source": "newsletter",
  "utm_medium": "email",
  "utm_campaign": "workshop_promo",
  
  // For partners
  "partner_id": "tech_conf_2025",
  "partner_commission": 0.15
}
```

## Payment Processing

### Payment Methods
1. **Stripe** (Primary)
   - Credit/debit cards
   - International payments
   - Subscriptions

2. **UPI** (India)
   - Manual entry required
   - Reference number tracking

3. **Bank Transfer**
   - Manual reconciliation
   - Invoice matching

### Payment Email Matching

#### Challenge
Customers often pay with different emails (spouse, work email, etc.)

#### Solution
```sql
-- customer_emails table tracks multiple emails
-- payment_reconciliation view helps identify mismatches
-- Manual review queue for unmatched payments
```

#### Matching Algorithm
```javascript
async function matchPaymentToCustomer(paymentEmail, leadEmail) {
  // 1. Try exact match with customer email
  let customer = await findByCustomerEmail(paymentEmail);
  
  // 2. Try lead email
  if (!customer) {
    customer = await findByLeadEmail(paymentEmail);
  }
  
  // 3. Check alternate emails
  if (!customer) {
    customer = await findByAlternateEmail(paymentEmail);
  }
  
  // 4. Fuzzy match (same domain, similar name)
  if (!customer) {
    const candidates = await fuzzyMatchEmail(paymentEmail, leadEmail);
    // Queue for manual review with suggestions
  }
  
  return customer;
}
```

## Workshop Operations

### Enrollment Flow
```mermaid
graph LR
    A[Lead Pays] --> B[Create Customer]
    B --> C[Create Payment Record]
    C --> D[Enroll in Workshop]
    D --> E[Send Welcome Email]
    E --> F[Track Attendance]
    F --> G[Generate Certificate]
```

### Communication Schedule
| Timing | Email Type | Trigger |
|--------|-----------|---------|
| Immediately | Welcome + Zoom link | Payment confirmed |
| 72 hours before | Reminder + Prep work | Automated |
| 24 hours before | Final reminder | Automated |
| 1 hour before | Last reminder | Automated |
| After completion | Certificate + Survey | Manual/Auto |

### Bulk Operations

#### Send Workshop Emails
```javascript
async function sendWorkshopEmails(workshopId, emailType) {
  const roster = await getWorkshopRoster(workshopId);
  
  for (const attendee of roster) {
    if (!attendee[`${emailType}_sent`]) {
      await sendEmail(attendee.email, emailType);
      await markEmailSent(attendee.enrollment_id, emailType);
    }
  }
  
  await updateWorkshopCommunications(workshopId, emailType);
}
```

#### Generate Certificates
```javascript
async function generateWorkshopCertificates(workshopId) {
  const graduates = await getWorkshopGraduates(workshopId);
  
  for (const graduate of graduates) {
    if (graduate.completion_percentage >= 80) {
      const cert = await generateCertificate(graduate);
      await saveCertificatePath(graduate.enrollment_id, cert.path);
      await sendCertificateEmail(graduate.email, cert);
    }
  }
}
```

## Customer Lifetime Value Tracking

### Customer Segments
- **new**: First purchase
- **repeat**: 2+ workshops
- **loyal**: 3+ workshops  
- **subscriber**: Active subscription
- **vip**: High LTV or executive
- **churned**: No activity >6 months

### LTV Calculation
```sql
-- Tracked automatically via triggers
total_spent = SUM(all completed payments)
total_spent_usd = SUM(payments converted to USD)
workshops_completed = COUNT(enrollments with >80% completion)
```

## Multi-Workshop Support

### Use Cases
1. **Sequential Learning**: AI Essentials → Practitioner → Agentic AI
2. **Role-Based**: Same person takes "AI for PMs" and "AI for UX"
3. **Team Training**: Manager takes multiple workshops with their team
4. **Subscription Model**: Unlimited workshops for subscribers

### Tracking Benefits
- **Progress Tracking**: See complete learning journey
- **Personalization**: Recommend next workshops
- **Alumni Network**: Engage across cohorts
- **Upsell Opportunities**: Identify power users

## API Endpoints

### Lead Management
```
POST   /api/leads                 # Create lead from any source
GET    /api/leads                 # List with filters
GET    /api/leads/:id            # Get lead details
PUT    /api/leads/:id            # Update lead
POST   /api/leads/:id/convert    # Convert to customer
```

### Customer Management
```
GET    /api/customers            # List customers
GET    /api/customers/:id        # Customer details
GET    /api/customers/:id/workshops  # Workshop history
GET    /api/customers/:id/payments   # Payment history
POST   /api/customers/:id/emails    # Add alternate email
```

### Workshop Operations
```
GET    /api/workshops            # List workshops
GET    /api/workshops/:id/roster # Get attendee list
POST   /api/workshops/:id/enroll # Enroll customer
POST   /api/workshops/:id/email  # Send bulk email
POST   /api/workshops/:id/certificates # Generate certificates
```

### Payment Processing
```
POST   /api/payments/stripe-webhook  # Stripe webhook
POST   /api/payments/manual         # Manual payment entry
GET    /api/payments/unmatched      # Payments needing review
POST   /api/payments/:id/match      # Match to customer
```

## Performance Optimizations

### Indexes
- Lead lookups by email, phone, status
- Customer lookups by email, stripe_id
- Payment lookups by stripe_payment_intent_id
- Workshop enrollments by customer and workshop

### Views
- Pre-computed aggregations for dashboards
- Complex joins for common queries
- Real-time conversion funnel metrics

### Daily Analytics
- Rollup tables for historical data
- Fast dashboard loading
- Trend analysis without heavy queries

## Security Considerations

### Data Protection
- Payment details encrypted at rest
- PII access logged and audited
- Role-based access control (RBAC)
- API rate limiting

### Payment Security
- No credit card numbers stored
- Stripe handles all card processing
- Manual payments require authorization
- Audit trail for all transactions

## Integration Points

### External Services
1. **Stripe**
   - Payments API
   - Webhooks for payment events
   - Customer portal for subscriptions

2. **Gmail API**
   - Automated email sending
   - Email tracking
   - Service account authentication

3. **Brevo (SendinBlue)**
   - SMS campaigns
   - WhatsApp messaging
   - Email marketing

4. **Meta Ads API**
   - Lead sync
   - Campaign performance
   - Audience updates

### Internal Services
1. **Kapi IDE**
   - User authentication
   - Workshop access control
   - Progress tracking

2. **Certificate Generator**
   - PDF generation
   - Template management
   - Bulk processing

3. **Analytics Engine**
   - Real-time metrics
   - Cohort analysis
   - Revenue reporting

## Deployment Architecture

### Infrastructure
```
┌─────────────────┐     ┌─────────────────┐
│   Web Server    │────▶│   API Server    │
│  (Next.js/React)│     │   (Node.js)     │
└─────────────────┘     └─────────────────┘
                               │
                               ▼
                        ┌─────────────────┐
                        │   SQLite DB     │
                        │  (Production)   │
                        └─────────────────┘
```

### Scaling Considerations
- SQLite handles 400 leads/day easily
- Consider PostgreSQL at 1000+ leads/day
- Redis for session management
- CDN for static assets

## Monitoring & Alerts

### Key Metrics
- Lead response time (target: <5 minutes)
- Conversion rate (target: 6-7%)
- Payment success rate
- Email delivery rate
- Workshop attendance rate

### Alert Conditions
- Lead response time >10 minutes
- Payment failures >5%
- Unmatched payments >10
- Workshop capacity >90%
- Database size >1GB

## Development Workflow

### Database Migrations
```bash
# Apply schema v2 migration
sqlite3 leads.db < schema_v2_migration.sql

# Backup before migrations
cp leads.db leads.db.backup
```

### Testing Strategy
- Unit tests for payment matching logic
- Integration tests for API endpoints
- End-to-end tests for critical flows
- Load testing for 400+ leads/day

### Code Organization
```
/dashboard
  /api          # API endpoints
  /components   # React components
  /lib          # Business logic
  /database     # Schema and migrations
  /tests        # Test suites
```

## Future Enhancements

### Phase 1 (Immediate)
- Payment email matching automation
- Workshop email automation
- Customer portal

### Phase 2 (3 months)
- Advanced analytics dashboard
- A/B testing framework
- Mobile app for SDRs

### Phase 3 (6 months)
- AI-powered lead scoring
- Predictive churn modeling
- Enterprise features

## Conclusion

This architecture provides a solid foundation for scaling Modern AI Pro from 2,500 to 10,000+ students annually while maintaining operational efficiency and data integrity. The separation of leads and customers, flexible payment tracking, and comprehensive workshop management ensure smooth operations as the business grows.