# API Overview

## Introduction
Dashboard API endpoints for ModernAI Pro administrative system.

## Base Configuration
- **Base URL**: `/api/`
- **Database**: SQLite (`leads.db`)
- **Authentication**: Session-based

## API Flow

```mermaid
graph TD
    A[Client Request] --> B{Method Check}
    B -->|GET| C[Fetch Data]
    B -->|POST| D[Create Record]
    B -->|PUT| E[Update Record]
    B -->|Other| F[405 Method Not Allowed]
    
    C --> G[Query Database]
    D --> H[Validate Data]
    E --> I[Validate ID & Data]
    
    G --> J[Format Response]
    H --> K[Insert Record]
    I --> L[Update Record]
    
    J --> M[JSON Response]
    K --> M
    L --> M
    F --> M
```

## Data Processing Flow

```mermaid
graph LR
    A[External Sources] --> B[API Endpoints]
    B --> C[Data Validation]
    C --> D[Database Operations]
    D --> E[Response Formatting]
    E --> F[JSON Response]
    
    G[Facebook Ads] --> B
    H[Stripe Webhooks] --> B
    I[Manual Entry] --> B
    
    D --> J[(SQLite DB)]
    J --> K[Leads Table]
    J --> L[Payments Table]
    J --> M[Workshops Table]
```

## Quick Reference

| Category | Documentation |
|----------|---------------|
| **Endpoints** | [endpoints-reference.md](endpoints-reference.md) |
| **Authentication** | [authentication.md](authentication.md) |
| **Data Models** | [data-models.md](data-models.md) |