# Authentication

## Authentication Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant A as Auth API
    participant DB as Database
    participant E as Email Service
    
    C->>A: POST /auth/register
    A->>DB: Create user
    A->>E: Send verification email
    A-->>C: {success, message}
    
    C->>A: POST /auth/verify-email
    A->>DB: Verify token
    A-->>C: {success, verified}
    
    C->>A: POST /auth/login
    A->>DB: Validate credentials
    A-->>C: {success, token, user}
```

## Session-Based Authentication

The API uses session-based authentication for dashboard access. Sessions are managed server-side with secure cookies.

### Login Process
1. User submits credentials via `POST /api/auth/login`
2. Server validates credentials against database
3. On success, creates session and returns session token
4. Client includes session token in subsequent requests

### Session Management
- Sessions expire after 24 hours of inactivity
- Session tokens are HTTP-only cookies for security
- Logout endpoint clears session: `POST /api/auth/logout`

## Password Reset Flow

```mermaid
graph TD
    A[User requests reset] --> B[POST /auth/forgot-password]
    B --> C[Generate reset token]
    C --> D[Send email with token]
    D --> E[User clicks link]
    E --> F[POST /auth/reset-password]
    F --> G[Validate token]
    G --> H{Token valid?}
    H -->|Yes| I[Update password]
    H -->|No| J[Return error]
    I --> K[Clear token]
    K --> L[Success response]
```

## Security Considerations

### Password Requirements
- Minimum 8 characters
- Must contain uppercase, lowercase, and numbers
- Special characters recommended

### Rate Limiting
- Login attempts: 5 per 15 minutes
- Password reset: 3 per hour
- Registration: 10 per hour per IP

### Token Security
- Reset tokens expire in 1 hour
- Verification tokens expire in 24 hours
- All tokens are cryptographically secure random strings