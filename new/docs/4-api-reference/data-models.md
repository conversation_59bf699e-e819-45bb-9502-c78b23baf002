# Data Models

## Lead Model

```javascript
{
  id: Number,                    // Primary key
  lead_id: String,              // External lead identifier
  full_name: String,            // Lead's full name
  email: String,                // Email address
  phone: String,                // Phone number
  lead_source: String,          // Source (Facebook, Manual, etc.)
  status: String,               // Current lead status
  created_time: ISO String,     // Lead creation timestamp
  
  // Workshop-specific fields
  workshop_type: String,        // Workshop enrolled in
  payment_status: String,       // Payment status
  enrolled_date: ISO String,    // Enrollment date
  
  // Business lead fields
  company: String,              // Company name
  location: String,             // Geographic location
  deal_size: String,            // Potential deal value
  
  // Tracking fields
  updated_at: ISO String,       // Last update timestamp
  data_source: String           // Data origin (stripe, facebook, manual)
}
```

## Customer Model

```javascript
{
  id: Number,                   // Primary key
  email: String,                // Unique email identifier
  full_name: String,            // Customer name
  payment_status: 'Paid' | 'Partial', // Payment status
  workshop_type: String,        // Enrolled workshop
  payment_amount: Number,       // Payment amount
  payment_date: ISO String,     // Payment timestamp
  
  // Aggregated fields
  total_payments: Number,       // Count of payments
  total_spent_stripe: Number,   // Total spent via Stripe
  workshops_enrolled: Number,   // Number of workshops
  workshops_completed: Number,  // Completed workshops
  certificates_earned: Number   // Certificates obtained
}
```

## Payment Model

```javascript
{
  stripe_payment_intent_id: String, // Stripe payment ID
  payment_amount: Number,           // Payment amount
  payment_currency: String,         // Currency (USD, INR)
  payment_status: String,           // Payment status
  payment_created_at: ISO String,   // Payment timestamp
  workshop_enrolled: String,        // Associated workshop
  customer_email: String            // Customer identifier
}
```

## Workshop Model

```javascript
{
  id: Number,                   // Primary key
  name: String,                 // Workshop name
  type: String,                 // Workshop type/category
  description: String,          // Workshop description
  start_date: ISO String,       // Workshop start date
  end_date: ISO String,         // Workshop end date
  price_usd: Number,           // Price in USD
  price_inr: Number,           // Price in INR
  capacity: Number,            // Maximum participants
  status: String               // Workshop status (active, completed)
}
```

## Facebook Campaign Model

```javascript
{
  campaign_id: String,          // Facebook campaign ID
  campaign_name: String,        // Campaign name
  account_id: String,          // Facebook account ID
  account_name: String,        // Account name
  status: String,              // Campaign status
  objective: String,           // Campaign objective
  
  // Performance metrics
  impressions: Number,         // Total impressions
  clicks: Number,             // Total clicks
  leads: Number,              // Leads generated
  spend: Number,              // Amount spent
  cost_per_lead: Number,      // Cost per lead
  
  // Timestamps
  created_time: ISO String,   // Creation date
  updated_time: ISO String    // Last update
}
```

## Database Schema Relationships

```mermaid
erDiagram
    LEADS {
        int id PK
        string lead_id
        string email
        string full_name
        string workshop_type
        string payment_status
        datetime created_time
    }
    
    PAYMENTS {
        int id PK
        string stripe_payment_id
        string customer_email FK
        decimal amount
        string currency
        datetime payment_date
    }
    
    WORKSHOPS {
        int id PK
        string name
        string type
        decimal price_usd
        decimal price_inr
        datetime start_date
    }
    
    FACEBOOK_CAMPAIGNS {
        int id PK
        string campaign_id
        string account_id
        string name
        int leads_generated
        decimal spend
    }
    
    LEADS ||--o{ PAYMENTS : "customer_email"
    LEADS ||--o{ WORKSHOPS : "workshop_type"
    FACEBOOK_CAMPAIGNS ||--o{ LEADS : "generates"
```

## Field Validation Rules

### Email Validation
- Must be valid email format
- Unique across leads table
- Required for customer records

### Payment Amounts
- Must be positive numbers
- Stored as decimal/float
- Currency specified separately

### Status Values

#### Lead Status Options
- `New` - Recently created lead
- `Contacted` - Initial contact made
- `Interested` - Expressed interest
- `Enrolled` - Workshop enrolled
- `Workshop Complete` - Completed workshop
- `Alumni Network` - Former student

#### Payment Status Options
- `Pending` - Payment initiated
- `Paid` - Payment successful
- `Partial` - Partial payment received
- `Failed` - Payment failed
- `Refunded` - Payment refunded

### Date Formats
- All dates stored as ISO 8601 strings
- UTC timezone preferred
- Format: `YYYY-MM-DDTHH:mm:ss.sssZ`