# API Endpoints Reference

## Authentication Endpoints

| Endpoint | Method | Purpose | Request Body | Response |
|----------|--------|---------|--------------|----------|
| `/api/auth/login` | POST | User login | `{email, password}` | `{success, token}` |
| `/api/auth/register` | POST | User registration | `{email, password, name}` | `{success, user}` |
| `/api/auth/verify-email` | POST | Email verification | `{token}` | `{success, message}` |
| `/api/auth/forgot-password` | POST | Password reset request | `{email}` | `{success, message}` |
| `/api/auth/reset-password` | POST | Password reset | `{token, password}` | `{success, message}` |
| `/api/auth/resend-verification` | POST | Resend verification | `{email}` | `{success, message}` |

## Core Data Endpoints

| Endpoint | Method | Purpose | Query Params | Response |
|----------|--------|---------|--------------|----------|
| `/api/leads` | GET | Fetch leads | `page`, `limit`, `search`, `source` | `{success, leads, pagination, summary}` |
| `/api/leads` | POST | Create lead | - | `{success, lead}` |
| `/api/leads` | PUT | Update lead | - | `{success, changes}` |
| `/api/customers` | GET | Fetch customers | `id`, `status`, `workshop_type`, `search` | `{customer, payments, workshops, summary}` |
| `/api/customers` | PUT | Update customer | - | Updated customer object |
| `/api/payments` | GET | Fetch payments | `page`, `limit`, `search`, `status` | `{success, data: {payments, pagination, stats}}` |

## External Integration Endpoints

| Endpoint | Method | Purpose | Query Params | Response |
|----------|--------|---------|--------------|----------|
| `/api/facebook/accounts` | GET | Facebook ad accounts | - | Account list |
| `/api/facebook/campaigns` | GET | Facebook campaigns | `account_id` | Campaign list |
| `/api/facebook/lead-ads` | GET | Facebook lead ads | `campaign_id` | Lead ads list |
| `/api/workshops` | GET | Workshop data | `type`, `status` | Workshop list |

## Query Parameters

### Pagination
- `page`: Page number (default: 1)
- `limit`: Records per page (default: 20-50)
- `search`: Search term for filtering
- `status`: Filter by status

### Filtering
- `source`: Lead source filter (`all`, `Facebook`, `Manual`, etc.)
- `workshop_type`: Workshop type filter
- `id`: Specific record ID

## Error Handling

| Status Code | Description | Response Format |
|-------------|-------------|------------------|
| 200 | Success | `{success: true, ...data}` |
| 201 | Created | `{success: true, message, ...data}` |
| 400 | Bad Request | `{success: false, error, message}` |
| 404 | Not Found | `{success: false, error}` |
| 405 | Method Not Allowed | `{error: 'Method not allowed'}` |
| 500 | Server Error | `{success: false, error, message?}` |

## Response Patterns

### List Response
```javascript
{
  success: true,
  [data_type]: Array,
  pagination: {
    current_page: Number,
    total_pages: Number,
    total_[items]: Number,
    has_next: Boolean,
    has_prev: Boolean
  },
  summary?: Object,
  stats?: Object
}
```

### Single Item Response
```javascript
{
  success: true,
  [item]: Object,
  [related_data]?: Array,
  summary?: Object
}
```