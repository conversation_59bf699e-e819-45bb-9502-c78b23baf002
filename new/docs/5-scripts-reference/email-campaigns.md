# Email Campaign Scripts

## Overview
Automated email marketing and communication scripts using Gmail API and Google Sheets integration.

## Core Scripts

### send_sales_mail_mahalakshmi.py

#### Purpose
Sales email campaigns for AI Bootcamp prospects with Google Sheets integration.

#### Features
- Google Sheets contact list integration
- Domain-wide delegation (<NAME_EMAIL>)
- HTML email templates
- Bulk sending with rate limiting
- Delivery confirmation

#### Usage Flow
```mermaid
graph TD
    A[send_sales_mail_mahalakshmi.py] --> B[Google Sheets Access]
    B --> C[Fetch Contact List]
    C --> D[Validate Email Addresses]
    D --> E[Generate Email Content]
    E --> F[Gmail API Setup]
    F --> G[Domain Delegation]
    G --> H[Send Emails]
    H --> I[Rate Limiting]
    I --> J{More Emails?}
    J -->|Yes| H
    J -->|No| K[Campaign Summary]
    
    style A fill:#e1f5fe
    style H fill:#e8f5e8
    style K fill:#f3e5f5
```

#### Email Template
```html
<p>Hey,</p>
<p>Hope you're doing well! I'm <PERSON><PERSON><PERSON><PERSON><PERSON> from Modern AI. I saw you were interested in our AI Bootcamp with <strong>Dr. <PERSON><PERSON><PERSON>wanathan</strong>.</p>
<p><strong>Quick overview:</strong></p>
<p>2-day hands-on workshop where you'll build real AI applications (LLMs, RAG systems, chatbots)...</p>
```

### send_followup_python_book.py

#### Purpose
Follow-up email sequences for Python book purchasers and course prospects.

#### Features
- Targeted follow-up sequences
- Personalized content based on purchase history
- Automated scheduling
- A/B testing capabilities

## Google Services Integration

### Google Sheets Configuration
```python
SHEET_URL = 'https://docs.google.com/spreadsheets/d/...'
SCOPES = ['https://www.googleapis.com/auth/spreadsheets']

# Expected sheet structure:
# Column A: Name
# Column B: Email  
# Column C: Status (optional)
# Column D: Notes (optional)
```

### Gmail API Setup
```python
SCOPES = ['https://www.googleapis.com/auth/gmail.send']

# Domain-wide delegation
delegated_credentials = credentials.with_subject('<EMAIL>')
service = build('gmail', 'v1', credentials=delegated_credentials)
```

## Service Account Configuration

### Required Files
- `modernaipro-b601d7749092.json` - Service account credentials
- Located in: `new/config/`

### Permissions Setup
1. Enable Gmail API and Google Sheets API
2. Configure domain-wide delegation
3. Grant service account permissions:
   - `https://www.googleapis.com/auth/gmail.send`
   - `https://www.googleapis.com/auth/spreadsheets`

### Domain Delegation Flow
```mermaid
graph TD
    A[Service Account] --> B[Domain Admin Approval]
    B --> C[API Scopes Granted]
    C --> D[Impersonate User Email]
    D --> E[Send Email as User]
    
    F[<EMAIL>] --> D
    G[Google Workspace Admin] --> B
    
    style A fill:#e1f5fe
    style E fill:#e8f5e8
```

## Campaign Management

### Contact List Management
```python
def get_email_list_from_sheets():
    # Authenticate with service account
    credentials = Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE)
    gc = gspread.authorize(credentials)
    
    # Open sheet and get contacts
    sheet = gc.open_by_url(SHEET_URL)
    worksheet = sheet.worksheet("Email")
    records = worksheet.get_all_records()
    
    # Extract and validate emails
    return [(record['Email'], record['Name']) for record in records]
```

### Email Validation
- Basic format validation (`@` symbol presence)
- Domain validation (optional)
- Bounce detection (via Gmail API responses)
- Unsubscribe handling

### Rate Limiting
```python
# Delay between emails to avoid rate limits
time.sleep(2)  # 2 seconds between emails

# Gmail API limits:
# - 1000 requests/100 seconds per user
# - Daily quotas vary by account type
```

## Email Templates

### Sales Email Template
- **Subject**: `Re: Modern AI workshop with Dr. Balaji (Aug 15-17)`
- **Sender**: `<EMAIL>`
- **Reply-To**: `<EMAIL>`
- **Format**: HTML with fallback text

### Follow-up Templates
- Course reminder sequences
- Workshop completion follow-ups
- Alumni network invitations
- Upsell opportunities

## Deliverability Best Practices

### Email Authentication
- SPF records configured
- DKIM signatures enabled
- DMARC policy set
- Domain reputation monitoring

### Content Guidelines
- Avoid spam trigger words
- Include unsubscribe links
- Use proper HTML structure
- Test across email clients

### List Hygiene
- Regular bounce cleaning
- Unsubscribe processing
- Engagement-based segmentation
- Re-engagement campaigns

## Monitoring & Analytics

### Campaign Metrics
- **Delivery Rate**: Emails successfully sent
- **Open Rate**: Via pixel tracking (if implemented)
- **Click-Through Rate**: Via link tracking
- **Response Rate**: Manual tracking

### Success Reporting
```python
print(f"📊 Email Campaign Summary:")
print(f"   Total emails: {total_count}")
print(f"   Successful: {success_count}")
print(f"   Failed: {total_count - success_count}")
print(f"   Success rate: {(success_count/total_count)*100:.1f}%")
```

## Error Handling

### Common Issues
| Error | Cause | Solution |
|-------|-------|----------|
| 403 Insufficient Permission | Missing API scopes | Update service account permissions |
| 429 Rate Limit Exceeded | Too many requests | Implement exponential backoff |
| 400 Invalid Email | Malformed recipient | Validate email format |
| Sheets Access Denied | Sheet not shared | Share with service account email |

### Logging
```python
# Error logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('email_campaigns.log'),
        logging.StreamHandler()
    ]
)
```

## Compliance & Privacy

### GDPR Compliance
- Explicit consent for email collection
- Easy unsubscribe mechanism
- Data retention policies
- Privacy policy links

### CAN-SPAM Act
- Clear sender identification
- Truthful subject lines
- Physical address in emails
- Honor unsubscribe requests promptly

### Best Practices
- Double opt-in for new subscribers
- Regular list cleaning
- Segmentation based on engagement
- A/B testing for optimization

## Automation & Scheduling

### Cron Job Setup
```bash
# Weekly sales email campaign (Friday 2 PM)
0 14 * * 5 cd /path/to/scripts/email && python send_sales_mail_mahalakshmi.py

# Monthly follow-up sequence (1st of month, 10 AM)
0 10 1 * * cd /path/to/scripts/email && python send_followup_python_book.py
```

### Campaign Scheduling
- Pre-workshop reminders
- Post-workshop follow-ups
- Course milestone celebrations
- Re-engagement sequences