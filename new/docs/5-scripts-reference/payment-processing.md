# Payment Processing Scripts

## Overview
Stripe payment integration and synchronization scripts for ModernAI Pro.

## Core Scripts

### sync_stripe_payments.py

#### Purpose
Historical payment synchronization from Stripe to local database.

#### Usage
```bash
# Sync last 30 days (default)
python sync_stripe_payments.py

# Sync specific period
python sync_stripe_payments.py --days 7

# Sync all historical payments
python sync_stripe_payments.py --all

# Preview without changes
python sync_stripe_payments.py --dry-run
```

#### Process Flow
```mermaid
graph TD
    A[sync_stripe_payments.py] --> B{Sync Type}
    B -->|Recent| C[Get Last N Days]
    B -->|All| D[Get All Historical]
    B -->|Dry Run| E[Preview Only]
    
    C --> F[Fetch Payment Intents]
    D --> F
    F --> G[Process Each Payment]
    
    G --> H{Lead Exists?}
    H -->|Yes| I[Update Existing Lead]
    H -->|No| J[Create New Lead]
    
    I --> K[Log Interaction]
    J --> K
    K --> L[Generate Summary Report]
    
    style A fill:#e1f5fe
    style L fill:#e8f5e8
```

### validate_stripe_data.py

#### Purpose
Data integrity validation for Stripe payment records.

#### Features
- Validates payment amounts
- Checks currency consistency
- Verifies customer email formats
- Identifies duplicate payments

### create_payment_link.py

#### Purpose
Dynamic payment link generation for workshops.

#### Features
- Creates Stripe payment links
- Configures workshop metadata
- Sets pricing by currency (USD/INR)
- Handles success/cancel URLs

### stripe_webhook_server.py

#### Purpose
Real-time webhook processing for Stripe events.

#### Supported Events
- `payment_intent.succeeded`
- `payment_intent.failed`
- `customer.created`
- `invoice.payment_succeeded`

#### Webhook Flow
```mermaid
graph TD
    A[Stripe Webhook] --> B[Signature Verification]
    B --> C{Event Type}
    C -->|payment_intent.succeeded| D[Process Payment]
    C -->|payment_intent.failed| E[Log Failure]
    C -->|customer.created| F[Create Customer]
    
    D --> G[Update Lead Status]
    G --> H[Send Confirmation]
    
    E --> I[Update Payment Status]
    F --> J[Store Customer Data]
    
    style A fill:#e1f5fe
    style G fill:#e8f5e8
```

## Database Integration

### Tables Updated
- `leads` - Payment status and enrollment data
- `lead_interactions` - Payment activity logging
- Custom Stripe tracking tables (if configured)

### Payment Status Flow
```mermaid
graph LR
    A[New Lead] --> B[Payment Initiated]
    B --> C[Payment Succeeded]
    C --> D[Enrolled Status]
    D --> E[Workshop Access]
    
    B --> F[Payment Failed]
    F --> G[Follow-up Required]
    
    style C fill:#e8f5e8
    style E fill:#f3e5f5
```

## Configuration

### Environment Variables
```bash
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
```

### Workshop Configuration
```python
WORKSHOP_PRICING = {
    'AI Essentials': {'USD': 497, 'INR': 24000},
    'AI Practitioner': {'USD': 997, 'INR': 49000},
    'Agentic AI': {'USD': 1497, 'INR': 74000}
}
```

## Error Handling

### Common Issues
| Error | Cause | Solution |
|-------|-------|----------|
| Invalid webhook signature | Wrong webhook secret | Verify STRIPE_WEBHOOK_SECRET |
| Payment not found | Deleted payment intent | Check Stripe dashboard |
| Database lock | Concurrent updates | Implement retry logic |
| Currency mismatch | Incorrect pricing config | Verify workshop pricing |

### Logging
- **Log File**: `new/logs/stripe_sync.log`
- **Log Level**: INFO for normal operations, ERROR for failures
- **Rotation**: Manual (consider implementing daily rotation)

## Monitoring & Alerts

### Key Metrics
- Payment success rate
- Webhook processing time
- Database sync accuracy
- Failed payment volume

### Health Checks
```bash
# Test Stripe API connection
python -c "import stripe; stripe.api_key='sk_test_...'; print(stripe.Account.retrieve())"

# Verify webhook endpoint
curl -X POST http://localhost:3000/api/webhooks/stripe \
  -H "Content-Type: application/json" \
  -d '{"test": "ping"}'
```

## Security Considerations

### Payment Data
- Never log full payment details
- Use Stripe customer IDs, not payment methods
- Implement PCI DSS compliance guidelines
- Regular security audits

### Webhook Security
- Always verify webhook signatures
- Use HTTPS endpoints only
- Implement replay attack protection
- Rate limiting on webhook endpoints