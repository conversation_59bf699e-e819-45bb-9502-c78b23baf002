# Scripts Overview

## Purpose
Administrative automation scripts for ModernAI Pro business operations.

## Directory Structure

```
new/scripts/
├── facebook/        # Facebook Ads management
├── stripe/          # Payment processing
├── email/           # Email automation
├── utilities/       # General utilities
├── gdocs_colab/     # Google Docs/Colab integration
└── analysis/        # Data analysis scripts
```

## Script Categories

| Category | Documentation | Purpose |
|----------|---------------|---------|
| **Facebook Automation** | [facebook-automation.md](facebook-automation.md) | Ad account management, lead collection |
| **Payment Processing** | [payment-processing.md](payment-processing.md) | Stripe integration, payment sync |
| **Email Campaigns** | [email-campaigns.md](email-campaigns.md) | Automated email marketing |
| **Utilities** | [utilities-reference.md](utilities-reference.md) | Database, Excel, messaging utilities |

## Environment Setup

### Required Environment Variables

| Variable | Purpose | Scripts Using |
|----------|---------|---------------|
| `FB_EXTENDED_TOKEN` | Facebook API access | Facebook scripts |
| `FB_APP_SECRET` | Facebook app authentication | Facebook scripts |
| `FB_APP_ID` | Facebook app ID | Facebook scripts |
| `STRIPE_SECRET_KEY` | Stripe API access | Stripe scripts |
| `STRIPE_WEBHOOK_SECRET` | Webhook verification | Webhook server |

### Database Integration
- **Primary Database**: SQLite (`leads.db`)
- **Location**: `new/dashboard/database/leads.db`
- **Tables**: leads, payments, workshops, facebook_* tables

### Logging
- **Log Directory**: `new/logs/`
- **Log Files**: `fb_ads.log`, `stripe_sync.log`, `email_campaigns.log`

## Common Usage Patterns

### Facebook Operations
```bash
# Daily health check
python fb_account_manager.py health-check

# Performance metrics
python fb_account_manager.py stats 7
```

### Payment Operations
```bash
# Daily payment sync
python sync_stripe_payments.py --days 1

# Historical sync
python sync_stripe_payments.py --all
```

### Email Campaigns
```bash
# Sales email campaign
python send_sales_mail_mahalakshmi.py
```

## Error Handling & Monitoring

### Status Codes
- `0`: Success
- `1`: Warning (partial success)
- `2`: Critical error

### Health Indicators
- Facebook account accessibility
- Stripe sync success rates
- Email delivery rates

## Security Considerations
- Service account credentials stored securely
- API tokens with limited scopes
- Database encryption at rest
- Audit logging for all operations