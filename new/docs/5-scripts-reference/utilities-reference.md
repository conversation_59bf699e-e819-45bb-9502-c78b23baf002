# Utilities Reference

## Overview
General-purpose utility scripts for database management, data processing, and system operations.

## Database Utilities

### database_init.py

#### Purpose
Initialize and set up the SQLite database schema for the ModernAI Pro system.

#### Features
- Creates all required tables
- Sets up indexes for performance
- Initializes default data
- Database migration support

#### Usage
```bash
cd new/scripts/utilities
python database_init.py
```

#### Database Schema
```sql
-- Core tables created
CREATE TABLE leads (
    id INTEGER PRIMARY KEY,
    lead_id TEXT UNIQUE,
    full_name TEXT,
    email TEXT,
    phone TEXT,
    workshop_type TEXT,
    payment_status TEXT,
    created_time DATETIME
);

CREATE TABLE lead_interactions (
    id INTEGER PRIMARY KEY,
    lead_id INTEGER,
    interaction_type TEXT,
    message TEXT,
    interaction_date DATETIME,
    FOREIGN KEY (lead_id) REFERENCES leads(id)
);

-- Facebook tracking tables
CREATE TABLE facebook_account_health (...);
CREATE TABLE facebook_campaign_metrics (...);
CREATE TABLE facebook_lead_ads (...);
```

## Data Processing Utilities

### excel_reader.py

#### Purpose
Excel file processing for workshop enrollment data and lead imports.

#### Features
- Reads `.xlsx` and `.xls` files
- Handles multiple worksheets
- Data validation and cleaning
- Batch processing capabilities

#### Usage
```python
from excel_reader import ExcelProcessor

processor = ExcelProcessor()
data = processor.read_workshop_enrollments('workshop_data.xlsx')
clean_data = processor.clean_and_validate(data)
```

#### Supported Formats
- Workshop enrollment sheets
- Lead export data
- Payment records
- Customer contact lists

### parse_l1_april.py

#### Purpose
Parse specific workshop data from April L1 cohort.

#### Features
- Legacy data migration
- Format standardization
- Duplicate detection
- Error reporting

### sheet20_parser.py

#### Purpose
Process data from Sheet20 format exports.

#### Features
- Custom sheet format handling
- Data transformation
- Validation rules
- Database integration

## Communication Utilities

### send_whatsapp.py

#### Purpose
WhatsApp messaging automation for customer notifications.

#### Features
- WhatsApp Business API integration
- Template message support
- Delivery status tracking
- Rate limiting compliance

#### Usage
```python
from send_whatsapp import WhatsAppSender

sender = WhatsAppSender()
sender.send_notification(
    phone_number="+1234567890",
    template="workshop_reminder",
    variables=["John Doe", "AI Essentials"]
)
```

#### Message Templates
- Workshop reminders
- Payment confirmations
- Course completion notifications
- Support messages

### WhatsApp API Flow
```mermaid
graph TD
    A[send_whatsapp.py] --> B[WhatsApp Business API]
    B --> C[Template Validation]
    C --> D[Message Queuing]
    D --> E[Delivery Attempt]
    E --> F{Delivered?}
    F -->|Yes| G[Success Callback]
    F -->|No| H[Retry Logic]
    H --> I[Error Logging]
    
    style A fill:#e1f5fe
    style G fill:#e8f5e8
    style I fill:#ffebee
```

## System Management Utilities

### establish_workshop_connections.py

#### Purpose
Set up workshop enrollment relationships and data connections.

#### Features
- Link leads to workshops
- Establish payment relationships
- Create enrollment records
- Generate certificates

### workshop_parser_template.py

#### Purpose
Template for parsing various workshop data formats.

#### Features
- Configurable field mapping
- Format detection
- Error handling
- Extensible architecture

#### Usage
```python
from workshop_parser_template import WorkshopParser

parser = WorkshopParser()
parser.configure_mapping({
    'name': 'full_name',
    'email': 'email_address',
    'workshop': 'course_type'
})
results = parser.process_file('workshop_data.csv')
```

## Legacy Migration Utilities

### legacy_migration_plan.py

#### Purpose
Comprehensive plan and utilities for migrating legacy system data.

#### Features
- Data mapping strategies
- Migration validation
- Rollback procedures
- Progress tracking

#### Migration Flow
```mermaid
graph TD
    A[Legacy Data] --> B[Extract]
    B --> C[Transform]
    C --> D[Validate]
    D --> E{Valid?}
    E -->|Yes| F[Load to New System]
    E -->|No| G[Error Handling]
    F --> H[Verification]
    G --> I[Manual Review]
    
    style A fill:#e1f5fe
    style F fill:#e8f5e8
    style I fill:#fff3e0
```

## Google Services Integration

### Google Colab Utilities
Located in `new/scripts/gdocs_colab/`:

#### colab_extractor.py
- Extract data from Google Colab notebooks
- Process research results
- Generate reports
- Archive completed work

## Configuration Management

### Environment Variables
```bash
# Database
DATABASE_PATH=./database/leads.db

# WhatsApp API
WHATSAPP_TOKEN=your_whatsapp_token
WHATSAPP_PHONE_ID=your_phone_number_id

# Google Services
GOOGLE_SERVICE_ACCOUNT_FILE=./config/service-account.json
```

### Configuration Files
- `config.json` - Application settings
- `field_mappings.json` - Data field mappings
- `templates.json` - Message templates

## Error Handling & Logging

### Standard Logging Setup
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('utilities.log'),
        logging.StreamHandler()
    ]
)
```

### Common Error Patterns
| Error Type | Common Cause | Solution |
|------------|--------------|----------|
| File Not Found | Missing input files | Verify file paths and permissions |
| Database Lock | Concurrent access | Implement connection pooling |
| API Rate Limit | Too many requests | Add exponential backoff |
| Data Validation | Invalid formats | Improve input validation |

## Performance Optimization

### Database Operations
- Use batch inserts for large datasets
- Implement connection pooling
- Add appropriate indexes
- Regular database maintenance

### File Processing
- Stream large files instead of loading entirely
- Use generators for memory efficiency
- Implement progress tracking
- Parallel processing where appropriate

## Testing & Validation

### Unit Testing
```python
import unittest
from excel_reader import ExcelProcessor

class TestExcelProcessor(unittest.TestCase):
    def setUp(self):
        self.processor = ExcelProcessor()
    
    def test_read_valid_file(self):
        data = self.processor.read_file('test_data.xlsx')
        self.assertIsNotNone(data)
        self.assertGreater(len(data), 0)
```

### Integration Testing
- Database connection tests
- API endpoint validation
- File format compatibility
- Error handling verification

## Monitoring & Maintenance

### Health Checks
```bash
# Database connectivity
python -c "import sqlite3; conn = sqlite3.connect('leads.db'); print('DB OK')"

# File permissions
ls -la database/ logs/ config/

# Python dependencies
pip list | grep -E "(pandas|requests|sqlite)"
```

### Maintenance Tasks
- Regular log rotation
- Database optimization
- Dependency updates
- Security patches

## Security Considerations

### Data Protection
- Encrypt sensitive data at rest
- Use secure communication channels
- Implement access controls
- Regular security audits

### API Security
- Use API keys securely
- Implement rate limiting
- Validate all inputs
- Log security events

### Database Security
- Regular backups
- Access logging
- Query parameterization
- Connection encryption