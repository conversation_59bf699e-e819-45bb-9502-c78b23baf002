# Modern AI Pro SDR Automation - Usage Guide

## Overview

This guide covers the unified Modern AI Pro SDR automation system that combines intelligent lead processing, AI-powered email generation, and comprehensive workflow tracking to scale from 50-60 to 400 leads/day.

## System Architecture

```
Facebook Lead Ads → Lead Selector → Email Campaign Manager → Email Library → Gmail
                        ↓                    ↓                     ↓
                  Priority Scoring    LLM/Template Gen    Assignment-based Sending
                        ↓                    ↓                     ↓
                  Workflow Logger ← Enhanced Interaction Logger → Database
```

## Core Components

### 1. Lead Selector Service
**File**: `scripts/core/lead_selector.py`
**Purpose**: Intelligent lead selection with enrichment and priority scoring

### 2. Email Campaign Manager  
**File**: `scripts/core/email_campaign_manager.py`
**Purpose**: High-level campaign orchestration

### 3. Enhanced Email Composer
**File**: `scripts/core/enhanced_email_composer.py` 
**Purpose**: LLM-powered email generation with template fallback

### 4. Email Library
**File**: `scripts/core/email_library.py`
**Purpose**: Modern Gmail API integration with attachments

### 5. Workflow Integration
**File**: `scripts/core/workflow_integration.py`
**Purpose**: Enhanced logging and sequence management

## Quick Start

### 1. Environment Setup

```bash
# Navigate to project directory
cd /Users/<USER>/Code/modernaipro/mai-administrative/new

# Ensure database exists
ls data/database/leads.db

# Test database connection
python scripts/core/lead_selector.py --action stats
```

### 2. Basic Lead Selection

```bash
# Get latest unprocessed lead
python scripts/core/lead_selector.py --action latest

# Get recent leads for campaign
python scripts/core/lead_selector.py --action recent --hours 24 --limit 10

# Get leads by status
python scripts/core/lead_selector.py --action status --status "New" --limit 5
```

### 3. Email Campaign Execution

```bash
# Test mode (logs emails without sending)
python scripts/core/email_campaign_manager.py --max-emails 5

# Production mode (actually send emails)
python scripts/core/email_campaign_manager.py --production --max-emails 10

# Target specific status
python scripts/core/email_campaign_manager.py --status "Contacted" --max-emails 5
```

### 4. Enhanced Workflow with Logging

```bash
# Enhanced campaign with comprehensive logging
python scripts/core/workflow_integration.py --max-emails 10 --hours-back 24

# Production mode with full tracking
python scripts/core/workflow_integration.py --production --max-emails 50

# View interaction statistics
python scripts/core/workflow_integration.py --stats
```

## Command Reference

### Lead Selector Commands

| Command | Purpose | Example |
|---------|---------|---------|
| `--action latest` | Get most recent unprocessed lead | `python lead_selector.py --action latest` |
| `--action recent` | Get recent leads for campaigns | `python lead_selector.py --action recent --hours 168 --limit 20` |
| `--action status` | Get leads by specific status | `python lead_selector.py --action status --status "Customer"` |
| `--action criteria` | Advanced filtering | `python lead_selector.py --action criteria --location "US" --limit 50` |
| `--action stats` | System health metrics | `python lead_selector.py --action stats` |

### Campaign Manager Commands

| Command | Purpose | Example |
|---------|---------|---------|
| `--production` | Send actual emails | `python email_campaign_manager.py --production` |
| `--status` | Target specific lead status | `python email_campaign_manager.py --status "New"` |
| `--max-emails` | Limit email volume | `python email_campaign_manager.py --max-emails 25` |
| `--hours-back` | Time window for leads | `python email_campaign_manager.py --hours-back 48` |
| `--assigned-to` | Target specific assignment | `python email_campaign_manager.py --assigned-to 3` |

## Daily Operations

### Morning Routine (9 AM)

```bash
# Check system health
python scripts/core/lead_selector.py --action stats

# Process overnight leads (test mode first)
python scripts/core/workflow_integration.py --max-emails 20 --hours-back 12

# If results look good, run production
python scripts/core/workflow_integration.py --production --max-emails 20 --hours-back 12
```

### Afternoon Follow-up (2 PM)

```bash
# Process new leads from morning
python scripts/core/workflow_integration.py --production --max-emails 30 --hours-back 6

# Target contacted leads for follow-up
python scripts/core/email_campaign_manager.py --production --status "Contacted" --max-emails 15
```

### Evening Cleanup (6 PM)

```bash
# Final processing of the day
python scripts/core/workflow_integration.py --production --max-emails 25 --hours-back 8

# View daily statistics
python scripts/core/workflow_integration.py --stats
```

## Cron Job Setup

### Every 5 Minutes - Facebook Lead Processing
```cron
*/5 * * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/facebook/import_leads.py >> logs/cron.log 2>&1
```

### Every 30 Minutes - Email Campaigns
```cron
*/30 * * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/core/workflow_integration.py --production --max-emails 15 --hours-back 2 >> logs/email_campaigns.log 2>&1
```

### Every 2 Hours - Follow-up Campaigns  
```cron
0 */2 * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/core/email_campaign_manager.py --production --status "Contacted" --max-emails 10 >> logs/followup_campaigns.log 2>&1
```

### Daily at 8 AM - System Health Check
```cron
0 8 * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/core/lead_selector.py --action stats >> logs/daily_health.log 2>&1
```

## Monitoring & Logging

### Log File Locations

```
data/logs/
├── email_campaigns.log          # Campaign activity log
├── email_compositions.log       # Email composition details
├── workflow_runs.log           # Workflow execution summary
├── email_generation_stats.log  # LLM vs template statistics
└── cron.log                    # Cron job execution logs
```

### Key Metrics to Monitor

1. **Lead Processing Rate**: Should maintain 6-7% conversion rate
2. **Email Generation Method**: Target >70% LLM success rate
3. **Response Time**: <5 minutes from lead import to first email
4. **Daily Volume**: Scale from 60 to 400 leads/day progressively

### Health Check Commands

```bash
# System overview
python scripts/core/lead_selector.py --action stats

# Recent campaign performance
tail -20 data/logs/workflow_runs.log

# Email generation success rate
tail -10 data/logs/email_generation_stats.log

# Interaction statistics
python scripts/core/workflow_integration.py --stats
```

## Troubleshooting

### Common Issues

#### 1. No Leads Found
```bash
# Check database connection
python scripts/core/lead_selector.py --action stats

# Verify recent leads exist
python scripts/core/lead_selector.py --action recent --hours 48

# Check lead statuses
python scripts/core/lead_selector.py --action status-summary
```

#### 2. Email Sending Failures
```bash
# Verify email configuration
python scripts/core/email_library.py --validate-config

# Check Gmail API quotas
# Review service account permissions

# Test with single lead
python scripts/core/enhanced_email_composer.py --test-mode
```

#### 3. LLM Generation Failures
```bash
# Check OpenAI API key
echo $OPENAI_API_KEY

# Verify LLM service
python scripts/integrations/openai/email_generator.py --test

# Monitor fallback rate
grep "Template" data/logs/email_generation_stats.log
```

### Performance Optimization

#### Scaling to 400 Leads/Day

1. **Batch Processing**: Increase `--max-emails` gradually (5 → 15 → 30 → 50)
2. **Frequency**: Reduce cron intervals (30min → 15min → 10min)
3. **Assignment Distribution**: Balance between Manish (ID: 3) and Mahalakshmi (ID: 9)
4. **Rate Limiting**: Adjust email sending intervals for volume

#### Database Optimization

```bash
# Weekly database cleanup
sqlite3 data/database/leads.db "VACUUM;"

# Index optimization
sqlite3 data/database/leads.db "REINDEX;"

# Backup database
cp data/database/leads.db data/backups/leads_$(date +%Y%m%d).db
```

## Security & Compliance

### API Key Management
- Store OpenAI API key in `config/.env`
- Gmail service account file in `config/modernaipro-b601d7749092.json`
- Never commit API keys to version control

### Data Protection
- Lead data encrypted at rest
- Email interactions logged with minimal PII
- Regular database backups every 8 hours

### Rate Limiting
- Gmail API: 1 email per second default
- OpenAI API: Respect tier limits
- Facebook API: 5-minute polling interval

## Advanced Usage

### Custom Campaigns

```bash
# Target high-priority US leads
python scripts/core/lead_selector.py --action criteria --location "US" --limit 20 | \
python scripts/core/email_campaign_manager.py --production --max-emails 20

# Weekend follow-up campaign
python scripts/core/email_campaign_manager.py --production --status "Contacted" --hours-back 168

# Assignment-specific campaigns
python scripts/core/email_campaign_manager.py --production --assigned-to 3 --max-emails 15
```

### Reporting & Analytics

```bash
# Daily campaign summary
python scripts/core/workflow_integration.py --stats

# Lead conversion funnel
python scripts/core/lead_selector.py --action status-summary

# Email generation performance
tail -50 data/logs/email_generation_stats.log | awk '{llm+=$2; template+=$3} END {print "LLM Rate:", llm/(llm+template)*100"%"}'
```

## Best Practices

### 1. Progressive Scaling
- Start with 10 emails/batch in test mode
- Monitor conversion rates and error rates
- Gradually increase volume while maintaining quality

### 2. Quality Control
- Review email compositions in test mode first
- Monitor LLM vs template generation ratios
- Ensure assignment-based sender distribution

### 3. Monitoring Discipline
- Check logs daily for errors
- Monitor database growth and performance  
- Track lead-to-customer conversion rates

### 4. Backup Strategy
- Database backups every 8 hours
- Configuration file versioning
- Log file rotation weekly

## Support & Maintenance

### Weekly Tasks
- Review workflow performance logs
- Check email generation success rates
- Optimize lead selection criteria
- Update email templates if needed

### Monthly Tasks
- Analyze conversion funnel metrics
- Review and update priority scoring algorithm
- Database maintenance and optimization
- API usage and cost analysis

### Emergency Procedures
- **System Down**: Check database connection and API keys
- **High Error Rate**: Switch to test mode, review recent changes
- **API Quota Exceeded**: Implement temporary rate limiting
- **Database Corruption**: Restore from latest backup

For technical support, refer to the development team or check the troubleshooting section first.
