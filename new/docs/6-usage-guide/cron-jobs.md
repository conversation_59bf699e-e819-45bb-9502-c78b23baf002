# Cron Job Configuration Guide

## Overview

This guide provides production-ready cron job configurations for Modern AI Pro's SDR automation system to scale from 50-60 to 400 leads/day.

## Cron Job Strategy

### Frequency Distribution
- **Lead Import**: Every 5 minutes (Facebook API polling)
- **Email Campaigns**: Every 15-30 minutes (primary outreach)
- **Follow-ups**: Every 2 hours (contacted leads)
- **Health Checks**: Daily (system monitoring)

## Production Cron Configuration

### 1. Facebook Lead Import (Every 5 Minutes)
```bash
# Import new leads from Facebook Lead Ads API
*/5 * * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/facebook/import_leads.py >> data/logs/facebook_import.log 2>&1
```

**Purpose**: Continuous lead ingestion from Facebook
**Expected Volume**: 10-15 leads per hour during peak times
**Monitoring**: Check `facebook_import.log` for API errors

### 2. Primary Email Campaigns (Every 15 Minutes)
```bash
# Process new leads with enhanced workflow logging
*/15 * * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/core/workflow_integration.py --production --max-emails 25 --hours-back 2 >> data/logs/primary_campaigns.log 2>&1
```

**Purpose**: Process fresh leads (< 2 hours old) with high priority
**Expected Volume**: 25 emails every 15 minutes = 100 emails/hour
**Rate**: 1,600 emails/day (scaling target)

### 3. Secondary Email Campaigns (Every 30 Minutes)
```bash
# Process slightly older leads (backup processing)
*/30 * * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/core/email_campaign_manager.py --production --status "New" --max-emails 15 --hours-back 6 >> data/logs/secondary_campaigns.log 2>&1
```

**Purpose**: Catch any leads missed by primary campaigns
**Expected Volume**: 15 emails every 30 minutes = 720 emails/day
**Rate**: Backup processing for consistency

### 4. Follow-up Campaigns (Every 2 Hours)
```bash
# Follow up with contacted leads
0 */2 * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/core/email_campaign_manager.py --production --status "Contacted" --max-emails 20 --hours-back 168 >> data/logs/followup_campaigns.log 2>&1
```

**Purpose**: Re-engage leads who haven't responded
**Expected Volume**: 20 emails every 2 hours = 240 emails/day
**Rate**: Nurture existing leads

### 5. System Health Monitoring (Daily at 8 AM)
```bash
# Daily system health and statistics
0 8 * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/core/workflow_integration.py --stats >> data/logs/daily_health.log 2>&1
```

**Purpose**: Monitor system performance and lead pipeline
**Output**: Daily statistics and health metrics

### 6. Database Backup (Every 8 Hours)
```bash
# Database backup for disaster recovery
0 */8 * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && cp data/database/leads.db data/backups/leads_$(date +%Y%m%d_%H%M).db && gzip data/backups/leads_$(date +%Y%m%d_%H%M).db
```

**Purpose**: Regular database backups
**Retention**: Keep compressed backups for disaster recovery

## Complete Crontab Configuration

```bash
# Modern AI Pro SDR Automation - Production Cron Jobs
# Edit with: crontab -e

# Facebook Lead Import - Every 5 minutes
*/5 * * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/facebook/import_leads.py >> data/logs/facebook_import.log 2>&1

# Primary Email Campaigns - Every 15 minutes (peak processing)
*/15 * * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/core/workflow_integration.py --production --max-emails 25 --hours-back 2 >> data/logs/primary_campaigns.log 2>&1

# Secondary Email Campaigns - Every 30 minutes (backup processing)  
*/30 * * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/core/email_campaign_manager.py --production --status "New" --max-emails 15 --hours-back 6 >> data/logs/secondary_campaigns.log 2>&1

# Follow-up Campaigns - Every 2 hours
0 */2 * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/core/email_campaign_manager.py --production --status "Contacted" --max-emails 20 --hours-back 168 >> data/logs/followup_campaigns.log 2>&1

# Daily Health Check - 8 AM
0 8 * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/core/workflow_integration.py --stats >> data/logs/daily_health.log 2>&1

# Database Backup - Every 8 hours
0 */8 * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && cp data/database/leads.db data/backups/leads_$(date +%Y%m%d_%H%M).db && gzip data/backups/leads_$(date +%Y%m%d_%H%M).db

# Log Rotation - Daily at midnight
0 0 * * * find /Users/<USER>/Code/modernaipro/mai-administrative/new/data/logs -name "*.log" -size +10M -exec gzip {} \;

# Cleanup old backups - Weekly on Sunday at 2 AM
0 2 * * 0 find /Users/<USER>/Code/modernaipro/mai-administrative/new/data/backups -name "*.gz" -mtime +30 -delete
```

## Scaling Configuration

### Phase 1: Initial Deployment (50-100 leads/day)
```bash
# Conservative settings for initial testing
*/30 * * * * cd /path/to/project && python scripts/core/workflow_integration.py --production --max-emails 10 --hours-back 2
0 */4 * * * cd /path/to/project && python scripts/core/email_campaign_manager.py --production --status "Contacted" --max-emails 5
```

### Phase 2: Moderate Scale (100-200 leads/day)
```bash
# Increase frequency and volume
*/20 * * * * cd /path/to/project && python scripts/core/workflow_integration.py --production --max-emails 15 --hours-back 2
0 */3 * * * cd /path/to/project && python scripts/core/email_campaign_manager.py --production --status "Contacted" --max-emails 10
```

### Phase 3: Full Scale (300-400 leads/day)
```bash
# Maximum production configuration (as shown above)
*/15 * * * * cd /path/to/project && python scripts/core/workflow_integration.py --production --max-emails 25 --hours-back 2
```

## Environment Variables for Cron

Create `/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/cron_env.sh`:

```bash
#!/bin/bash
# Environment setup for cron jobs

export PATH="/usr/local/bin:/usr/bin:/bin"
export PYTHONPATH="/Users/<USER>/Code/modernaipro/mai-administrative/new"
export OPENAI_API_KEY="your-openai-api-key"
export GMAIL_SERVICE_ACCOUNT="/Users/<USER>/Code/modernaipro/mai-administrative/new/config/modernaipro-b601d7749092.json"

# Source this file in cron jobs if needed
# source /path/to/cron_env.sh && python script.py
```

## Monitoring & Alerts

### Log Monitoring Commands
```bash
# Check recent campaign activity
tail -f data/logs/primary_campaigns.log

# Monitor error rates
grep -i error data/logs/*.log | tail -20

# Check email sending success
grep "sent successfully\|sending failed" data/logs/primary_campaigns.log | tail -10

# Monitor LLM vs template usage
tail -10 data/logs/email_generation_stats.log
```

### Daily Health Check Script
Create `scripts/monitoring/daily_health_check.sh`:

```bash
#!/bin/bash
# Daily health check for Modern AI Pro SDR automation

cd /Users/<USER>/Code/modernaipro/mai-administrative/new

echo "=== Modern AI Pro SDR Health Check - $(date) ==="

# 1. Check lead processing volume
echo "1. Lead Processing Stats:"
python scripts/core/lead_selector.py --action stats | grep -E "(total_leads|leads_24h|uncontacted_leads)"

# 2. Check recent email campaign success
echo -e "\n2. Recent Campaign Activity:"
tail -5 data/logs/workflow_runs.log

# 3. Check database size and growth
echo -e "\n3. Database Status:"
ls -lh data/database/leads.db
sqlite3 data/database/leads.db "SELECT COUNT(*) as total_leads FROM leads;"

# 4. Check disk space
echo -e "\n4. Disk Usage:"
df -h . | tail -1

# 5. Check for errors in logs
echo -e "\n5. Recent Errors:"
grep -i error data/logs/*.log | tail -5

echo -e "\n=== Health Check Complete ==="
```

Make it executable and add to cron:
```bash
chmod +x scripts/monitoring/daily_health_check.sh

# Add to crontab
0 7 * * * /Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/monitoring/daily_health_check.sh >> data/logs/health_checks.log 2>&1
```

## Performance Targets

### Key Metrics to Monitor

| Metric | Target | Warning | Critical |
|--------|--------|---------|----------|
| Daily Lead Volume | 300-400 | <200 | <100 |
| Email Success Rate | >95% | <90% | <80% |
| LLM Generation Rate | >70% | <50% | <30% |
| Response Time | <5 min | >10 min | >30 min |
| Database Size Growth | <50MB/day | >100MB/day | >200MB/day |

### Alert Thresholds
- **Email failures** > 10% in any hour
- **Lead import failures** > 3 consecutive attempts
- **Database errors** > 5 in any hour
- **Disk usage** > 80%

## Troubleshooting Cron Issues

### Common Problems

#### 1. Cron Jobs Not Running
```bash
# Check if cron service is running
ps aux | grep cron

# Check cron logs
tail -f /var/log/cron.log  # Linux
tail -f /var/log/system.log | grep cron  # macOS

# Verify crontab
crontab -l
```

#### 2. Environment Issues
```bash
# Test cron environment
*/5 * * * * env > /tmp/cron_env.log

# Compare with shell environment
env > /tmp/shell_env.log
diff /tmp/cron_env.log /tmp/shell_env.log
```

#### 3. Python Path Issues
```bash
# Use absolute Python path in cron
which python3
# Use: /usr/local/bin/python3 instead of just python

# Or set PATH in crontab
PATH=/usr/local/bin:/usr/bin:/bin
```

#### 4. Permission Issues
```bash
# Check file permissions
ls -la scripts/core/workflow_integration.py

# Make executable if needed
chmod +x scripts/core/workflow_integration.py

# Check log directory permissions
ls -la data/logs/
chmod 755 data/logs/
```

## Production Deployment Checklist

- [ ] Database backup strategy in place
- [ ] All log directories created with proper permissions
- [ ] Environment variables configured
- [ ] API keys and service accounts accessible
- [ ] Cron jobs tested in development
- [ ] Monitoring and alerting configured
- [ ] Error handling and notification setup
- [ ] Performance baselines established
- [ ] Escalation procedures documented
- [ ] Team access and responsibility assigned

## Security Considerations

1. **API Keys**: Store in environment variables, not in cron commands
2. **File Permissions**: Restrict access to sensitive configuration files
3. **Log Rotation**: Prevent log files from growing too large
4. **Backup Security**: Encrypt backups if they contain sensitive data
5. **Access Control**: Limit who can modify cron jobs

## Performance Optimization

### Resource Usage
- Monitor CPU and memory usage during peak processing
- Optimize database queries for large lead volumes
- Consider rate limiting for external API calls
- Implement connection pooling for database access

### Scaling Bottlenecks
- Gmail API quotas (250 quota units per user per second)
- OpenAI API rate limits (tier-dependent)
- Database performance with large datasets
- Disk I/O for logging and backups

This cron configuration will reliably scale your SDR automation from 50-60 to 400 leads/day while maintaining quality and providing comprehensive monitoring.
