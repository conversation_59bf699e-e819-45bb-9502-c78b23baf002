# Deployment Guide

## Prerequisites
- Azure VM with SSH access
- Domain name pointed to server IP
- Git repository access
- Facebook Developer App (for Ads API)
- Stripe Account (for payment processing)

## Local Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd mai-administrative
```

### 2. Environment Variables
Create `.env` file in `new/config/`:
```bash
FB_EXTENDED_TOKEN=your_facebook_extended_token
FB_APP_SECRET=your_facebook_app_secret
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
DATABASE_PATH=./dashboard/database/leads.db
```

### 3. Local Development
```bash
cd new/scripts
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

cd ../dashboard
npm install
npm run build
```

## Azure Production Deployment

### 1. Connect to Azure Server
```bash
# SSH config: Host modernai-main
# HostName **************
# IdentityFile ~/.ssh/kapi-key.pem
# User azureuser
ssh modernai-main
```

### 2. Install Dependencies
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt update
sudo apt install -y nodejs python3 python3-pip sqlite3 nginx
sudo npm install -g pm2
```

### 3. Deploy Application
```bash
cd /var/www
sudo git clone https://github.com/Kapi-IDE/modernai.git
sudo chown -R $USER:$USER mai-administrative
cd mai-administrative/new/config
cp .env.example .env
nano .env  # Add your API keys

cd ../scripts
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

cd utilities
python database_init.py

cd ../../dashboard
npm install
npm run build
```

### 4. Start Services with PM2
```bash
# Load environment variables from .env file
pm2 start npm --name "dashboard" -- start --env-file .env
cd ../scripts/stripe
pm2 start stripe_webhook_server.py --name "webhook" --interpreter python3
pm2 save && pm2 startup
```

**Alternative approach using ecosystem file:**
```bash
# Create ecosystem.config.js in dashboard directory
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'dashboard',
    script: 'npm',
    args: 'start',
    env_file: '.env',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
};
EOF

# Start with ecosystem file
pm2 start ecosystem.config.js
```

### 5. Configure Nginx (Optional)
```bash
sudo nano /etc/nginx/sites-available/modernaipro
```
```nginx
server {
    listen 80;
    server_name your-domain.com;
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
    }
}
```
```bash
sudo ln -s /etc/nginx/sites-available/modernaipro /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx
```

## Database Migration

⚠️ **NEVER commit .db files to Git** - Use secure transfer:

```bash
# From local machine
scp -i ~/.ssh/kapi-key.pem /Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db azureuser@**************:/var/www/modernai/new/data/database/
ssh azureuser@************** "chmod 644 /var/www/modernai/new/data/database/leads.db"
```

## Automation (Cron Jobs)
```bash
crontab -e
# Add:
0 9 * * * cd /var/www/mai-administrative/new/scripts/facebook && /var/www/mai-administrative/new/scripts/venv/bin/python fb_account_manager.py health-check
0 10 * * * cd /var/www/mai-administrative/new/scripts/stripe && /var/www/mai-administrative/new/scripts/venv/bin/python sync_stripe_payments.py --days 1
```

## Testing & Verification
```bash
# Check services
pm2 status
sudo systemctl status nginx

# Test endpoints
curl http://localhost:3000/api/health
curl http://your-domain.com/api/leads

# Test database
sqlite3 /var/www/mai-administrative/new/dashboard/database/leads.db "SELECT COUNT(*) FROM leads;"

# Test integrations
cd /var/www/mai-administrative/new/scripts/facebook
python fb_account_manager.py permissions
cd ../stripe
python sync_stripe_payments.py --dry-run --days 1
```

## Troubleshooting
| Issue | Solution |
|-------|----------|
| PM2 not starting | Check `pm2 logs` |
| Database errors | Verify permissions with `ls -la database/` |
| API not responding | Check port 3000: `netstat -tulpn \| grep 3000` |
| Nginx errors | `sudo nginx -t` and check `/var/log/nginx/error.log` |
| Facebook API 403/401 | Verify token permissions |
| Stripe webhook failure | Check webhook URL/secret |

## Backup Setup
```bash
# Daily backup script
echo '#!/bin/bash
cp /var/www/mai-administrative/new/dashboard/database/leads.db /home/<USER>/backups/leads_$(date +%Y%m%d).db
find /home/<USER>/backups/ -name "leads_*.db" -mtime +7 -delete' | sudo tee /usr/local/bin/backup-db.sh

sudo chmod +x /usr/local/bin/backup-db.sh
mkdir -p /home/<USER>/backups
echo "0 2 * * * /usr/local/bin/backup-db.sh" | crontab -
```

## Log Locations
- PM2: `pm2 logs dashboard` / `pm2 logs webhook`
- Facebook: `new/logs/fb_ads.log`
- Stripe: `new/logs/stripe_sync.log`
- Nginx: `/var/log/nginx/error.log`

---
**Estimated deployment time: 30-45 minutes**