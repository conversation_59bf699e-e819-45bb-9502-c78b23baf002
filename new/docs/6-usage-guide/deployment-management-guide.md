# Deployment Management Guide

**Comprehensive guide for managing and updating the production deployment on an ongoing basis**

---

## 🏗️ System Overview

**Remote Server:** ************** (Azure VM)  
**Remote Path:** `/var/www/modernai/new`  
**Local Path:** `/Users/<USER>/Code/modernaipro/mai-administrative/new`  
**SSH Key:** `~/.ssh/kapi-key.pem`  
**User:** `azureuser`

---

## 🚀 Quick Deployment Checklist

### ✅ Pre-Deployment Verification
```bash
# 1. Verify local changes work
cd /Users/<USER>/Code/modernaipro/mai-administrative/new
git status
git pull origin main

# 2. Test locally
cd dashboard
npm install
npm run build
npm run dev  # Test on localhost:3000

# 3. Test Python services
cd ../scripts
source venv/bin/activate
python3 tests/test_email_campaigns.py
python3 core/workflow_integration.py
```

### 🚢 Deploy to Production
```bash
# 1. Push changes to repository
git add .
git commit -m "Your deployment message"
git push origin main

# 2. Update remote server
ssh -i ~/.ssh/kapi-key.pem azureuser@**************
cd /var/www/modernai/new
git pull origin main

# 3. Update dashboard
cd dashboard
npm install
npm run build
pm2 restart dashboard

# 4. Update Python services
cd ../scripts
source venv/bin/activate
pip install -r requirements.txt
# Restart any Python-based PM2 services if needed

# 5. Verify deployment
pm2 status
pm2 logs dashboard --lines 50
```

---

## 📦 Database Management

### 🔄 Database Sync (Local → Remote)
```bash
# Secure database transfer (NEVER commit .db files to Git)
scp -i ~/.ssh/kapi-key.pem \
  /Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db \
  azureuser@**************:/var/www/modernai/new/data/database/

# Set proper permissions on remote
ssh -i ~/.ssh/kapi-key.pem azureuser@************** \
  "chmod 644 /var/www/modernai/new/data/database/leads.db && \
   chown azureuser:azureuser /var/www/modernai/new/data/database/leads.db"
```

### 🔄 Database Backup (Remote → Local)
```bash
# Download remote database for backup/analysis
scp -i ~/.ssh/kapi-key.pem \
  azureuser@**************:/var/www/modernai/new/data/database/leads.db \
  ~/backups/leads_$(date +%Y%m%d_%H%M%S).db
```

### 🗃️ Database Health Check
```bash
# Check database on remote server
ssh -i ~/.ssh/kapi-key.pem azureuser@************** << 'EOF'
cd /var/www/modernai/new
sqlite3 data/database/leads.db "
  SELECT 
    COUNT(*) as total_leads,
    COUNT(CASE WHEN status = 'New' THEN 1 END) as new_leads,
    COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as leads_with_email
  FROM leads;
"
EOF
```

---

## ⏰ Crontab Management

### 📋 Current Production Cron Jobs
Based on local configuration, here are the production cron jobs:

```bash
# View current cron jobs on remote server
ssh -i ~/.ssh/kapi-key.pem azureuser@************** "crontab -l"
```

### 🔧 Standard Cron Jobs Setup
```bash
# SSH to remote server
ssh -i ~/.ssh/kapi-key.pem azureuser@**************

# Edit crontab
crontab -e

# Add these entries (adjust paths for production):
# Lead collection every 10 minutes during business hours
*/10 9-21 * * * cd /var/www/modernai/new/scripts/integrations/facebook && /var/www/modernai/new/scripts/venv/bin/python fb_rate_limited_leads.py >> logs/rate_limited_leads_$(date +\%Y\%m\%d).log 2>&1

# Account health monitoring every 4 hours
0 2,6,10,14,18,22 * * * cd /var/www/modernai/new/scripts/integrations/facebook && /var/www/modernai/new/scripts/venv/bin/python fb_account_manager.py health-check >> logs/daily_health_$(date +\%Y\%m\%d).log 2>&1

# Stripe payment sync daily at 3 AM
0 3 * * * cd /var/www/modernai/new/scripts/integrations/stripe && /var/www/modernai/new/scripts/venv/bin/python sync_stripe_payments.py --days 1 >> logs/stripe_sync_$(date +\%Y\%m\%d).log 2>&1

# Email campaigns daily at 10 AM (production mode)
0 10 * * * cd /var/www/modernai/new/scripts && /var/www/modernai/new/scripts/venv/bin/python core/email_campaign_manager.py --production --max-emails 50 >> logs/email_campaigns_$(date +\%Y\%m\%d).log 2>&1

# Log cleanup weekly on Sunday at 2 AM
0 2 * * 0 find /var/www/modernai/new/scripts/logs -name "*.log" -mtime +7 -delete

# Database backup daily at 1 AM
0 1 * * * cp /var/www/modernai/new/data/database/leads.db /home/<USER>/backups/leads_$(date +\%Y\%m\%d).db
```

### 🔍 Monitor Cron Jobs
```bash
# Check cron service status
ssh -i ~/.ssh/kapi-key.pem azureuser@************** "systemctl status cron"

# View recent cron logs
ssh -i ~/.ssh/kapi-key.pem azureuser@************** "journalctl -u cron -f"

# Test specific cron job manually
ssh -i ~/.ssh/kapi-key.pem azureuser@************** << 'EOF'
cd /var/www/modernai/new/scripts/integrations/facebook
source ../venv/bin/activate
python3 fb_account_manager.py health-check
EOF
```

---

## 🖥️ PM2 Process Management

### 📊 PM2 Status & Control
```bash
# SSH to remote server
ssh -i ~/.ssh/kapi-key.pem azureuser@**************

# Check all processes
pm2 status

# View detailed process info
pm2 show dashboard
pm2 show webhook

# Restart services
pm2 restart dashboard
pm2 restart webhook
pm2 restart all

# View logs
pm2 logs dashboard --lines 100
pm2 logs webhook --lines 50
pm2 logs --lines 200

# Monitor in real-time
pm2 monit
```

### 🔄 Dashboard Deployment Process
```bash
# Complete dashboard update process
ssh -i ~/.ssh/kapi-key.pem azureuser@************** << 'EOF'
cd /var/www/modernai/new/dashboard

# Pull latest changes
git pull origin main

# Install dependencies (if package.json changed)
npm install

# Build production version
npm run build

# Restart PM2 service
pm2 restart dashboard

# Check if it started successfully
sleep 5
pm2 status
pm2 logs dashboard --lines 20
EOF
```

### 🆘 PM2 Troubleshooting
```bash
# If processes are stuck or not responding
ssh -i ~/.ssh/kapi-key.pem azureuser@************** << 'EOF'
# Stop all processes
pm2 stop all

# Delete all processes
pm2 delete all

# Restart from scratch
cd /var/www/modernai/new/dashboard
pm2 start npm --name "dashboard" -- start

cd ../scripts/integrations/stripe
pm2 start stripe_webhook_server.py --name "webhook" --interpreter python3

# Save configuration
pm2 save

# Check status
pm2 status
EOF
```

### 📋 PM2 Configuration
```bash
# Create/update ecosystem file for easier management
cat > /var/www/modernai/new/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'dashboard',
    cwd: '/var/www/modernai/new/dashboard',
    script: 'npm',
    args: 'start',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }, {
    name: 'webhook',
    cwd: '/var/www/modernai/new/scripts/integrations/stripe',
    script: 'stripe_webhook_server.py',
    interpreter: 'python3',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '500M'
  }]
}
EOF

# Use ecosystem file
pm2 start ecosystem.config.js
pm2 save
```

---

## 🔍 Monitoring & Troubleshooting

### 📊 System Health Checks
```bash
# Complete health check script
ssh -i ~/.ssh/kapi-key.pem azureuser@************** << 'EOF'
echo "🏥 System Health Check - $(date)"
echo "=================================="

# Check disk space
echo "💾 Disk Usage:"
df -h /var/www/modernai

# Check memory usage
echo -e "\n🧠 Memory Usage:"
free -h

# Check PM2 processes
echo -e "\n🖥️ PM2 Status:"
pm2 status

# Check database
echo -e "\n🗃️ Database Status:"
ls -la /var/www/modernai/new/data/database/leads.db
sqlite3 /var/www/modernai/new/data/database/leads.db "SELECT COUNT(*) as total_leads FROM leads;"

# Check logs
echo -e "\n📝 Recent Log Activity:"
echo "Dashboard logs (last 5 lines):"
tail -5 /var/www/modernai/new/scripts/logs/email_campaigns_$(date +%Y%m%d).log 2>/dev/null || echo "No email campaign logs today"

echo "Cron logs (last 5 lines):"
tail -5 /var/www/modernai/new/scripts/integrations/facebook/logs/rate_limited_leads_$(date +%Y%m%d).log 2>/dev/null || echo "No Facebook logs today"

# Check port availability
echo -e "\n🌐 Port Status:"
netstat -tulpn | grep -E ':3000|:4242' || echo "No services on ports 3000/4242"

echo -e "\n✅ Health check complete"
EOF
```

### 🚨 Emergency Recovery Procedures

#### 🔥 Dashboard Not Responding
```bash
# Emergency dashboard recovery
ssh -i ~/.ssh/kapi-key.pem azureuser@************** << 'EOF'
echo "🚨 Emergency Dashboard Recovery"

# Check what's using port 3000
netstat -tulpn | grep :3000

# Force kill processes on port 3000 if needed
fuser -k 3000/tcp 2>/dev/null || echo "No processes found on port 3000"

# Restart dashboard
cd /var/www/modernai/new/dashboard
pm2 delete dashboard 2>/dev/null || echo "Dashboard process not found"
pm2 start npm --name "dashboard" -- start

# Wait and verify
sleep 10
curl -f http://localhost:3000/api/health || echo "❌ Dashboard still not responding"
pm2 logs dashboard --lines 20
EOF
```

#### 🔥 Database Issues
```bash
# Database recovery procedures
ssh -i ~/.ssh/kapi-key.pem azureuser@************** << 'EOF'
echo "🗃️ Database Recovery Check"

DB_PATH="/var/www/modernai/new/data/database/leads.db"

# Check database file
ls -la $DB_PATH

# Check database integrity
sqlite3 $DB_PATH "PRAGMA integrity_check;"

# If integrity check fails, restore from backup
if [ $? -ne 0 ]; then
    echo "⚠️ Database integrity issues detected"
    LATEST_BACKUP=$(ls -t /home/<USER>/backups/leads_*.db 2>/dev/null | head -1)
    if [ -n "$LATEST_BACKUP" ]; then
        echo "📥 Restoring from backup: $LATEST_BACKUP"
        cp "$LATEST_BACKUP" $DB_PATH
        chmod 644 $DB_PATH
        echo "✅ Database restored from backup"
    else
        echo "❌ No backup found - manual intervention required"
    fi
fi
EOF
```

### 📈 Performance Monitoring
```bash
# Performance monitoring script
ssh -i ~/.ssh/kapi-key.pem azureuser@************** << 'EOF'
echo "📈 Performance Report - $(date)"
echo "================================"

# CPU usage
echo "💻 CPU Usage:"
top -bn1 | grep "Cpu(s)" | awk '{print $2 $3 $4 $5}'

# Memory usage by process
echo -e "\n🧠 Memory Usage by Process:"
ps aux --sort=-%mem | head -10

# Disk I/O
echo -e "\n💾 Disk I/O:"
iostat -x 1 1 2>/dev/null || echo "iostat not available"

# Network connections
echo -e "\n🌐 Active Connections:"
netstat -tuln | grep -E ':3000|:4242'

# PM2 resource usage
echo -e "\n🖥️ PM2 Resource Usage:"
pm2 monit --lines 5 2>/dev/null || pm2 status
EOF
```

---

## 🔧 Advanced Management

### 📝 Log Management
```bash
# Log rotation and cleanup script
ssh -i ~/.ssh/kapi-key.pem azureuser@************** << 'EOF'
# Create log management script
cat > /usr/local/bin/manage-logs.sh << 'SCRIPT'
#!/bin/bash

LOG_BASE="/var/www/modernai/new/scripts/logs"
BACKUP_BASE="/home/<USER>/log-archives"

echo "📝 Log Management - $(date)"

# Create backup directory
mkdir -p "$BACKUP_BASE/$(date +%Y%m)"

# Archive old logs (older than 7 days)
find "$LOG_BASE" -name "*.log" -mtime +7 -exec gzip {} \;
find "$LOG_BASE" -name "*.log.gz" -mtime +7 -exec mv {} "$BACKUP_BASE/$(date +%Y%m)/" \;

# Delete very old archives (older than 90 days)
find "$BACKUP_BASE" -name "*.log.gz" -mtime +90 -delete

echo "✅ Log management complete"
SCRIPT

chmod +x /usr/local/bin/manage-logs.sh
/usr/local/bin/manage-logs.sh
EOF
```

### 🔐 Security Updates
```bash
# Regular security maintenance
ssh -i ~/.ssh/kapi-key.pem azureuser@************** << 'EOF'
echo "🔐 Security Maintenance"

# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Node.js dependencies (check for vulnerabilities)
cd /var/www/modernai/new/dashboard
npm audit
npm audit fix

# Update Python packages
cd ../scripts
source venv/bin/activate
pip list --outdated
pip install --upgrade pip

# Check for suspicious processes
echo -e "\n🔍 Process Check:"
ps aux | grep -E '(wget|curl)' | grep -v grep

echo "✅ Security maintenance complete"
EOF
```

---

## 📚 Quick Reference Commands

### 🔗 Connection Commands
```bash
# SSH to server
ssh -i ~/.ssh/kapi-key.pem azureuser@**************

# SCP database (local to remote)
scp -i ~/.ssh/kapi-key.pem /Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db azureuser@**************:/var/www/modernai/new/data/database/

# SCP database (remote to local backup)
scp -i ~/.ssh/kapi-key.pem azureuser@**************:/var/www/modernai/new/data/database/leads.db ~/backups/leads_$(date +%Y%m%d).db
```

### 🖥️ PM2 Commands
```bash
pm2 status                    # Show all processes
pm2 restart dashboard         # Restart dashboard
pm2 logs dashboard --lines 50 # View dashboard logs
pm2 monit                     # Real-time monitoring
pm2 save                      # Save current process list
```

### 🏗️ Build Commands
```bash
# Dashboard build
cd /var/www/modernai/new/dashboard
npm install
npm run build
pm2 restart dashboard

# Python environment update
cd /var/www/modernai/new/scripts
source venv/bin/activate
pip install -r requirements.txt
```

### 🔍 Troubleshooting Commands
```bash
# Check ports
netstat -tulpn | grep -E ':3000|:4242'

# Check disk space
df -h /var/www/modernai

# Check database
sqlite3 /var/www/modernai/new/data/database/leads.db "SELECT COUNT(*) FROM leads;"

# View system logs
journalctl -u nginx -f
journalctl -u cron -f
```

---

## 🎯 Deployment Checklist

### Before Deployment
- [ ] Test changes locally
- [ ] Verify database backups exist
- [ ] Check PM2 process status
- [ ] Review recent logs for errors

### During Deployment
- [ ] Pull latest code
- [ ] Run npm install & build
- [ ] Restart PM2 services
- [ ] Verify services started correctly

### After Deployment
- [ ] Check application loads in browser
- [ ] Verify API endpoints respond
- [ ] Monitor logs for errors
- [ ] Test key functionality

### Emergency Contacts
- **Server Provider:** Azure Support
- **Domain/DNS:** (Your DNS provider)
- **Monitoring:** PM2 logs, system logs

---

**Last Updated:** January 2025  
**Estimated Setup Time:** 15-30 minutes per deployment  
**Monitoring Frequency:** Daily status checks recommended