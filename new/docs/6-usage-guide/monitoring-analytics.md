# Monitoring & Analytics Guide

## Overview

This guide covers monitoring, analytics, and performance tracking for the Modern AI Pro SDR automation system. Essential for maintaining 6-7% conversion rates while scaling to 400 leads/day.

## Key Performance Indicators (KPIs)

### Primary Metrics

| Metric | Current Target | Scale Target | Monitoring Frequency |
|--------|----------------|--------------|---------------------|
| Daily Lead Volume | 50-60 | 400 | Real-time |
| Lead-to-Email Conversion | 100% | 95%+ | Hourly |
| Email Delivery Success | 95%+ | 95%+ | Real-time |
| LLM Generation Success | 70%+ | 80%+ | Daily |
| Response Rate | 6-7% | 6-7% | Weekly |
| Lead Processing Time | <5 min | <5 min | Real-time |

### Secondary Metrics

| Metric | Target | Purpose |
|--------|--------|---------|
| Database Growth Rate | <50MB/day | Resource planning |
| API Cost per Lead | <$0.10 | Cost optimization |
| System Uptime | 99.5%+ | Reliability tracking |
| Error Rate | <2% | Quality assurance |

## Real-time Monitoring Commands

### System Health Dashboard
```bash
#!/bin/bash
# Real-time system overview

echo "=== Modern AI Pro SDR Automation Dashboard ==="
echo "Timestamp: $(date)"
echo ""

# 1. Lead Pipeline Status
echo "📊 LEAD PIPELINE:"
python scripts/core/lead_selector.py --action stats | head -10

# 2. Recent Campaign Performance
echo -e "\n📧 RECENT CAMPAIGNS:"
tail -5 data/logs/workflow_runs.log

# 3. Email Generation Performance
echo -e "\n🤖 EMAIL GENERATION:"
tail -3 data/logs/email_generation_stats.log

# 4. System Resources
echo -e "\n💾 SYSTEM RESOURCES:"
df -h data/database/leads.db
echo "Database size: $(ls -lh data/database/leads.db | awk '{print $5}')"

# 5. Recent Errors
echo -e "\n❌ RECENT ERRORS (last 1 hour):"
find data/logs -name "*.log" -mmin -60 -exec grep -l "ERROR\|error\|failed" {} \; | head -3
```

### Live Campaign Monitoring
```bash
# Monitor campaigns in real-time
tail -f data/logs/primary_campaigns.log | grep -E "(Processing lead|✅|❌)"

# Watch email generation methods
tail -f data/logs/email_campaigns.log | grep -E "(LLM|Template|SUCCESS|FAILED)"

# Monitor database activity
watch -n 30 'python scripts/core/lead_selector.py --action stats | grep -E "(total_leads|leads_24h|uncontacted_leads)"'
```

## Analytics Scripts

### 1. Daily Performance Report

Create `scripts/analytics/daily_report.py`:

```python
#!/usr/bin/env python3
"""
Daily Performance Report Generator
Analyzes campaign performance and generates actionable insights
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import json

def generate_daily_report(db_path):
    """Generate comprehensive daily performance report"""
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    
    # Date range for analysis
    today = datetime.now().date()
    yesterday = today - timedelta(days=1)
    
    print(f"📊 MODERN AI PRO - DAILY PERFORMANCE REPORT")
    print(f"Report Date: {today}")
    print(f"Analysis Period: {yesterday} to {today}")
    print("=" * 60)
    
    # 1. Lead Volume Analysis
    lead_query = """
    SELECT 
        DATE(created_time) as date,
        COUNT(*) as total_leads,
        COUNT(CASE WHEN status = 'New' THEN 1 END) as new_leads,
        COUNT(CASE WHEN status = 'Contacted' THEN 1 END) as contacted_leads,
        COUNT(CASE WHEN status = 'Customer' THEN 1 END) as customers
    FROM leads 
    WHERE DATE(created_time) >= ?
    GROUP BY DATE(created_time)
    ORDER BY date DESC
    """
    
    df_leads = pd.read_sql_query(lead_query, conn, params=[yesterday])
    
    print("\n📈 LEAD VOLUME ANALYSIS:")
    for _, row in df_leads.iterrows():
        print(f"  {row['date']}: {row['total_leads']} total | {row['new_leads']} new | {row['contacted_leads']} contacted | {row['customers']} customers")
    
    # 2. Email Campaign Performance
    email_query = """
    SELECT 
        DATE(interaction_date) as date,
        COUNT(*) as emails_sent,
        COUNT(CASE WHEN outcome = 'sent' THEN 1 END) as successful_sends,
        COUNT(CASE WHEN response_received = 1 THEN 1 END) as responses_received,
        user_email
    FROM lead_interactions 
    WHERE interaction_type = 'email_sent' 
    AND DATE(interaction_date) >= ?
    GROUP BY DATE(interaction_date), user_email
    ORDER BY date DESC, user_email
    """
    
    df_emails = pd.read_sql_query(email_query, conn, params=[yesterday])
    
    print("\n📧 EMAIL CAMPAIGN PERFORMANCE:")
    for _, row in df_emails.iterrows():
        success_rate = (row['successful_sends'] / row['emails_sent'] * 100) if row['emails_sent'] > 0 else 0
        response_rate = (row['responses_received'] / row['emails_sent'] * 100) if row['emails_sent'] > 0 else 0
        print(f"  {row['date']} | {row['user_email']}: {row['emails_sent']} sent ({success_rate:.1f}% success, {response_rate:.1f}% response)")
    
    # 3. Lead Source Analysis
    source_query = """
    SELECT 
        lead_source,
        campaign_name,
        COUNT(*) as lead_count,
        COUNT(CASE WHEN status = 'Customer' THEN 1 END) as conversions
    FROM leads 
    WHERE DATE(created_time) >= ?
    GROUP BY lead_source, campaign_name
    ORDER BY lead_count DESC
    """
    
    df_sources = pd.read_sql_query(source_query, conn, params=[yesterday])
    
    print("\n📊 LEAD SOURCE PERFORMANCE:")
    for _, row in df_sources.iterrows():
        conversion_rate = (row['conversions'] / row['lead_count'] * 100) if row['lead_count'] > 0 else 0
        print(f"  {row['lead_source']} - {row['campaign_name']}: {row['lead_count']} leads ({conversion_rate:.1f}% conversion)")
    
    # 4. Assignment Performance
    assignment_query = """
    SELECT 
        l.assigned_to,
        COUNT(*) as assigned_leads,
        COUNT(CASE WHEN li.lead_id IS NOT NULL THEN 1 END) as contacted_leads,
        COUNT(CASE WHEN l.status = 'Customer' THEN 1 END) as conversions
    FROM leads l
    LEFT JOIN lead_interactions li ON l.id = li.lead_id AND li.interaction_type = 'email_sent'
    WHERE DATE(l.created_time) >= ?
    GROUP BY l.assigned_to
    ORDER BY assigned_leads DESC
    """
    
    df_assignments = pd.read_sql_query(assignment_query, conn, params=[yesterday])
    
    print("\n👥 ASSIGNMENT PERFORMANCE:")
    for _, row in df_assignments.iterrows():
        contact_rate = (row['contacted_leads'] / row['assigned_leads'] * 100) if row['assigned_leads'] > 0 else 0
        conversion_rate = (row['conversions'] / row['assigned_leads'] * 100) if row['assigned_leads'] > 0 else 0
        sdr_name = "Manish" if row['assigned_to'] == 3 else "Mahalakshmi" if row['assigned_to'] == 9 else f"User#{row['assigned_to']}"
        print(f"  {sdr_name} (ID: {row['assigned_to']}): {row['assigned_leads']} assigned | {contact_rate:.1f}% contacted | {conversion_rate:.1f}% converted")
    
    conn.close()

if __name__ == "__main__":
    db_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    generate_daily_report(db_path)
```

### 2. Email Generation Analytics

Create `scripts/analytics/email_generation_analysis.py`:

```python
#!/usr/bin/env python3
"""
Email Generation Performance Analysis
Tracks LLM vs Template usage and success rates
"""

import os
import re
from datetime import datetime, timedelta
from collections import defaultdict

def analyze_email_generation():
    """Analyze email generation performance from logs"""
    
    log_file = "data/logs/email_generation_stats.log"
    
    if not os.path.exists(log_file):
        print("❌ Email generation stats log not found")
        return
    
    print("🤖 EMAIL GENERATION ANALYSIS")
    print("=" * 50)
    
    # Parse log file
    stats = []
    with open(log_file, 'r') as f:
        lines = f.readlines()
        
    for line in lines:
        if '|' in line and 'TIMESTAMP' not in line and '---' not in line:
            parts = [p.strip() for p in line.split('|')]
            if len(parts) >= 5:
                try:
                    timestamp = parts[0]
                    llm_count = int(parts[1])
                    template_count = int(parts[2])
                    total = int(parts[3])
                    llm_rate = float(parts[4].replace('%', ''))
                    
                    stats.append({
                        'timestamp': timestamp,
                        'llm_count': llm_count,
                        'template_count': template_count,
                        'total': total,
                        'llm_rate': llm_rate
                    })
                except ValueError:
                    continue
    
    if not stats:
        print("❌ No valid email generation data found")
        return
    
    # Recent performance (last 7 days)
    recent_stats = stats[-50:]  # Last 50 entries
    
    total_emails = sum(s['total'] for s in recent_stats)
    total_llm = sum(s['llm_count'] for s in recent_stats)
    total_template = sum(s['template_count'] for s in recent_stats)
    
    avg_llm_rate = (total_llm / total_emails * 100) if total_emails > 0 else 0
    
    print(f"📊 RECENT PERFORMANCE (Last {len(recent_stats)} batches):")
    print(f"  Total Emails Generated: {total_emails}")
    print(f"  LLM Generated: {total_llm} ({avg_llm_rate:.1f}%)")
    print(f"  Template Generated: {total_template} ({100-avg_llm_rate:.1f}%)")
    
    # Daily trends
    daily_stats = defaultdict(lambda: {'llm': 0, 'template': 0, 'total': 0})
    
    for stat in recent_stats:
        date = stat['timestamp'][:10]  # Extract date
        daily_stats[date]['llm'] += stat['llm_count']
        daily_stats[date]['template'] += stat['template_count']
        daily_stats[date]['total'] += stat['total']
    
    print(f"\n📈 DAILY TRENDS:")
    for date in sorted(daily_stats.keys())[-7:]:  # Last 7 days
        data = daily_stats[date]
        llm_rate = (data['llm'] / data['total'] * 100) if data['total'] > 0 else 0
        print(f"  {date}: {data['total']} emails | {llm_rate:.1f}% LLM rate")
    
    # Performance alerts
    print(f"\n⚠️  PERFORMANCE ALERTS:")
    if avg_llm_rate < 50:
        print(f"  🔴 Low LLM Success Rate: {avg_llm_rate:.1f}% (Target: >70%)")
    elif avg_llm_rate < 70:
        print(f"  🟡 Moderate LLM Success Rate: {avg_llm_rate:.1f}% (Target: >70%)")
    else:
        print(f"  🟢 Good LLM Success Rate: {avg_llm_rate:.1f}%")

if __name__ == "__main__":
    analyze_email_generation()
```

### 3. Conversion Funnel Analysis

Create `scripts/analytics/conversion_funnel.py`:

```python
#!/usr/bin/env python3
"""
Lead Conversion Funnel Analysis
Tracks leads through the entire pipeline from import to customer
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta

def analyze_conversion_funnel(db_path, days=7):
    """Analyze lead conversion funnel for specified time period"""
    
    conn = sqlite3.connect(db_path)
    
    # Date range
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days)
    
    print(f"🔄 CONVERSION FUNNEL ANALYSIS")
    print(f"Period: {start_date} to {end_date} ({days} days)")
    print("=" * 60)
    
    # Funnel stages
    funnel_query = """
    WITH funnel_data AS (
        SELECT 
            l.id,
            l.created_time,
            l.status,
            l.lead_source,
            CASE WHEN li.lead_id IS NOT NULL THEN 1 ELSE 0 END as contacted,
            CASE WHEN lr.lead_id IS NOT NULL THEN 1 ELSE 0 END as responded,
            CASE WHEN l.status = 'Customer' THEN 1 ELSE 0 END as converted
        FROM leads l
        LEFT JOIN (
            SELECT DISTINCT lead_id 
            FROM lead_interactions 
            WHERE interaction_type = 'email_sent'
        ) li ON l.id = li.lead_id
        LEFT JOIN (
            SELECT DISTINCT lead_id 
            FROM lead_interactions 
            WHERE response_received = 1
        ) lr ON l.id = lr.lead_id
        WHERE DATE(l.created_time) >= ?
        AND DATE(l.created_time) <= ?
    )
    SELECT 
        COUNT(*) as total_leads,
        SUM(contacted) as contacted_leads,
        SUM(responded) as responded_leads,
        SUM(converted) as converted_leads
    FROM funnel_data
    """
    
    df_funnel = pd.read_sql_query(funnel_query, conn, params=[start_date, end_date])
    
    if df_funnel.empty or df_funnel['total_leads'].iloc[0] == 0:
        print("❌ No leads found for the specified period")
        return
    
    # Calculate conversion rates
    total = df_funnel['total_leads'].iloc[0]
    contacted = df_funnel['contacted_leads'].iloc[0]
    responded = df_funnel['responded_leads'].iloc[0]
    converted = df_funnel['converted_leads'].iloc[0]
    
    contact_rate = (contacted / total * 100) if total > 0 else 0
    response_rate = (responded / contacted * 100) if contacted > 0 else 0
    conversion_rate = (converted / total * 100) if total > 0 else 0
    
    print(f"📊 FUNNEL METRICS:")
    print(f"  1. Total Leads: {total}")
    print(f"  2. Contacted: {contacted} ({contact_rate:.1f}%)")
    print(f"  3. Responded: {responded} ({response_rate:.1f}%)")
    print(f"  4. Converted: {converted} ({conversion_rate:.1f}%)")
    
    # Lead source breakdown
    source_query = """
    SELECT 
        lead_source,
        COUNT(*) as total_leads,
        COUNT(CASE WHEN status = 'Customer' THEN 1 END) as conversions
    FROM leads 
    WHERE DATE(created_time) >= ?
    AND DATE(created_time) <= ?
    GROUP BY lead_source
    ORDER BY total_leads DESC
    """
    
    df_sources = pd.read_sql_query(source_query, conn, params=[start_date, end_date])
    
    print(f"\n📈 CONVERSION BY SOURCE:")
    for _, row in df_sources.iterrows():
        source_conversion_rate = (row['conversions'] / row['total_leads'] * 100) if row['total_leads'] > 0 else 0
        print(f"  {row['lead_source']}: {row['total_leads']} leads → {row['conversions']} conversions ({source_conversion_rate:.1f}%)")
    
    # Daily trend
    daily_query = """
    SELECT 
        DATE(created_time) as date,
        COUNT(*) as leads,
        COUNT(CASE WHEN status = 'Customer' THEN 1 END) as conversions
    FROM leads 
    WHERE DATE(created_time) >= ?
    AND DATE(created_time) <= ?
    GROUP BY DATE(created_time)
    ORDER BY date
    """
    
    df_daily = pd.read_sql_query(daily_query, conn, params=[start_date, end_date])
    
    print(f"\n📅 DAILY TRENDS:")
    for _, row in df_daily.iterrows():
        daily_conversion_rate = (row['conversions'] / row['leads'] * 100) if row['leads'] > 0 else 0
        print(f"  {row['date']}: {row['leads']} leads → {row['conversions']} conversions ({daily_conversion_rate:.1f}%)")
    
    # Performance alerts
    print(f"\n⚠️  PERFORMANCE ALERTS:")
    if contact_rate < 90:
        print(f"  🔴 Low Contact Rate: {contact_rate:.1f}% (Target: >95%)")
    if response_rate < 5:
        print(f"  🔴 Low Response Rate: {response_rate:.1f}% (Target: >6%)")
    elif response_rate < 6:
        print(f"  🟡 Moderate Response Rate: {response_rate:.1f}% (Target: >6%)")
    if conversion_rate > 7:
        print(f"  🟢 Excellent Conversion Rate: {conversion_rate:.1f}%")
    elif conversion_rate > 6:
        print(f"  🟢 Good Conversion Rate: {conversion_rate:.1f}%")
    
    conn.close()

if __name__ == "__main__":
    db_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    analyze_conversion_funnel(db_path, days=7)
```

## Automated Monitoring Setup

### 1. Performance Alerts Script

Create `scripts/monitoring/performance_alerts.py`:

```python
#!/usr/bin/env python3
"""
Performance Monitoring with Automated Alerts
Checks key metrics and sends alerts when thresholds are exceeded
"""

import sqlite3
import os
from datetime import datetime, timedelta

def check_performance_alerts(db_path):
    """Check performance metrics and generate alerts"""
    
    alerts = []
    conn = sqlite3.connect(db_path)
    
    # Check 1: Email delivery success rate (last hour)
    email_check = """
    SELECT 
        COUNT(*) as total_emails,
        COUNT(CASE WHEN outcome = 'sent' THEN 1 END) as successful_emails
    FROM lead_interactions 
    WHERE interaction_type = 'email_sent'
    AND interaction_date >= datetime('now', '-1 hour')
    """
    
    cursor = conn.execute(email_check)
    result = cursor.fetchone()
    
    if result[0] > 0:  # If emails were sent
        success_rate = (result[1] / result[0]) * 100
        if success_rate < 80:
            alerts.append(f"🔴 CRITICAL: Email success rate is {success_rate:.1f}% (last hour)")
        elif success_rate < 90:
            alerts.append(f"🟡 WARNING: Email success rate is {success_rate:.1f}% (last hour)")
    
    # Check 2: Lead processing backlog
    backlog_check = """
    SELECT COUNT(*) as unprocessed_leads
    FROM leads 
    WHERE status = 'New'
    AND created_time < datetime('now', '-30 minutes')
    AND id NOT IN (
        SELECT DISTINCT lead_id 
        FROM lead_interactions 
        WHERE interaction_type = 'email_sent'
        AND lead_id IS NOT NULL
    )
    """
    
    cursor = conn.execute(backlog_check)
    backlog = cursor.fetchone()[0]
    
    if backlog > 50:
        alerts.append(f"🔴 CRITICAL: {backlog} leads unprocessed for >30 minutes")
    elif backlog > 20:
        alerts.append(f"🟡 WARNING: {backlog} leads unprocessed for >30 minutes")
    
    # Check 3: Database size growth
    db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB
    if db_size > 500:
        alerts.append(f"🟡 WARNING: Database size is {db_size:.1f}MB")
    
    # Check 4: Recent error rate
    error_logs = [
        "data/logs/primary_campaigns.log",
        "data/logs/secondary_campaigns.log",
        "data/logs/followup_campaigns.log"
    ]
    
    recent_errors = 0
    for log_file in error_logs:
        if os.path.exists(log_file):
            # Count errors in last hour
            one_hour_ago = datetime.now() - timedelta(hours=1)
            with open(log_file, 'r') as f:
                for line in f:
                    if 'ERROR' in line or 'failed' in line:
                        # Simple timestamp check (could be improved)
                        recent_errors += 1
    
    if recent_errors > 10:
        alerts.append(f"🔴 CRITICAL: {recent_errors} errors detected in last hour")
    elif recent_errors > 5:
        alerts.append(f"🟡 WARNING: {recent_errors} errors detected in last hour")
    
    conn.close()
    
    # Output alerts
    if alerts:
        print(f"⚠️  PERFORMANCE ALERTS - {datetime.now()}")
        print("=" * 50)
        for alert in alerts:
            print(alert)
        print()
    else:
        print(f"✅ All systems normal - {datetime.now()}")
    
    return alerts

if __name__ == "__main__":
    db_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    check_performance_alerts(db_path)
```

### 2. System Health Dashboard

Create `scripts/monitoring/dashboard.py`:

```python
#!/usr/bin/env python3
"""
Real-time System Health Dashboard
Provides comprehensive overview of system status
"""

import sqlite3
import os
import time
from datetime import datetime, timedelta

def display_dashboard(db_path):
    """Display real-time system dashboard"""
    
    # Clear screen
    os.system('clear' if os.name == 'posix' else 'cls')
    
    conn = sqlite3.connect(db_path)
    now = datetime.now()
    
    print("🚀 MODERN AI PRO SDR AUTOMATION DASHBOARD")
    print(f"Last Updated: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 1. Lead Pipeline Status
    pipeline_query = """
    SELECT 
        status,
        COUNT(*) as count
    FROM leads 
    WHERE DATE(created_time) >= DATE('now', '-7 days')
    GROUP BY status
    ORDER BY count DESC
    """
    
    print("📊 LEAD PIPELINE (Last 7 days):")
    cursor = conn.execute(pipeline_query)
    total_leads = 0
    for row in cursor.fetchall():
        print(f"  {row[0]}: {row[1]}")
        total_leads += row[1]
    print(f"  TOTAL: {total_leads}")
    
    # 2. Today's Activity
    today_query = """
    SELECT 
        COUNT(CASE WHEN DATE(created_time) = DATE('now') THEN 1 END) as leads_today,
        COUNT(CASE WHEN DATE(created_time) = DATE('now', '-1 day') THEN 1 END) as leads_yesterday
    FROM leads
    """
    
    cursor = conn.execute(today_query)
    today_data = cursor.fetchone()
    
    print(f"\n📈 DAILY VOLUME:")
    print(f"  Today: {today_data[0]} leads")
    print(f"  Yesterday: {today_data[1]} leads")
    
    # 3. Email Activity (Last 24 hours)
    email_query = """
    SELECT 
        COUNT(*) as total_emails,
        COUNT(CASE WHEN outcome = 'sent' THEN 1 END) as successful_emails,
        user_email
    FROM lead_interactions 
    WHERE interaction_type = 'email_sent'
    AND interaction_date >= datetime('now', '-24 hours')
    GROUP BY user_email
    """
    
    print(f"\n📧 EMAIL ACTIVITY (Last 24 hours):")
    cursor = conn.execute(email_query)
    total_sent = 0
    total_success = 0
    for row in cursor.fetchall():
        success_rate = (row[1] / row[0] * 100) if row[0] > 0 else 0
        print(f"  {row[2]}: {row[0]} sent ({success_rate:.1f}% success)")
        total_sent += row[0]
        total_success += row[1]
    
    overall_success = (total_success / total_sent * 100) if total_sent > 0 else 0
    print(f"  TOTAL: {total_sent} sent ({overall_success:.1f}% success)")
    
    # 4. System Resources
    print(f"\n💾 SYSTEM RESOURCES:")
    db_size = os.path.getsize(db_path) / (1024 * 1024)
    print(f"  Database Size: {db_size:.1f} MB")
    
    # Check log directory size
    log_dir = "data/logs"
    if os.path.exists(log_dir):
        log_size = sum(os.path.getsize(os.path.join(log_dir, f)) 
                      for f in os.listdir(log_dir) if f.endswith('.log'))
        log_size_mb = log_size / (1024 * 1024)
        print(f"  Log Files Size: {log_size_mb:.1f} MB")
    
    # 5. Recent Errors
    print(f"\n❌ RECENT ISSUES:")
    recent_errors = check_recent_errors()
    if recent_errors:
        for error in recent_errors[-3:]:  # Show last 3 errors
            print(f"  {error}")
    else:
        print("  No recent errors detected")
    
    conn.close()
    
    print(f"\n🔄 Auto-refresh in 30 seconds... (Ctrl+C to exit)")

def check_recent_errors():
    """Check for recent errors in log files"""
    errors = []
    log_files = [
        "data/logs/primary_campaigns.log",
        "data/logs/facebook_import.log",
        "data/logs/workflow_runs.log"
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            try:
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    for line in lines[-50:]:  # Check last 50 lines
                        if 'ERROR' in line or 'FAILED' in line or 'failed' in line:
                            timestamp = line.split('|')[0].strip() if '|' in line else 'Unknown time'
                            errors.append(f"{timestamp}: {log_file}")
            except:
                continue
    
    return errors

def run_dashboard():
    """Run the dashboard with auto-refresh"""
    db_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    
    try:
        while True:
            display_dashboard(db_path)
            time.sleep(30)  # Refresh every 30 seconds
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped.")

if __name__ == "__main__":
    run_dashboard()
```

## Log Analysis Scripts

### 1. Log Parser and Analyzer

Create `scripts/analytics/log_analyzer.py`:

```python
#!/usr/bin/env python3
"""
Log File Analysis Tool
Parses and analyzes system logs for performance insights
"""

import os
import re
from datetime import datetime, timedelta
from collections import defaultdict, Counter

def analyze_campaign_logs():
    """Analyze email campaign logs for performance patterns"""
    
    log_files = [
        "data/logs/email_campaigns.log",
        "data/logs/primary_campaigns.log",
        "data/logs/workflow_runs.log"
    ]
    
    print("📊 CAMPAIGN LOG ANALYSIS")
    print("=" * 50)
    
    for log_file in log_files:
        if not os.path.exists(log_file):
            print(f"⚠️  Log file not found: {log_file}")
            continue
            
        print(f"\n📄 Analyzing: {log_file}")
        
        # Parse log entries
        entries = []
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        # Skip header and separator lines
        data_lines = [line for line in lines if '|' in line and 'TIMESTAMP' not in line and '---' not in line]
        
        for line in data_lines:
            parts = [p.strip() for p in line.split('|')]
            if len(parts) >= 6:
                entry = {
                    'timestamp': parts[0],
                    'sender': parts[1],
                    'lead_id': parts[2],
                    'lead_name': parts[3],
                    'lead_email': parts[4],
                    'status': parts[5],
                    'method': parts[6] if len(parts) > 6 else 'Unknown'
                }
                entries.append(entry)
        
        if not entries:
            print(f"  No valid entries found")
            continue
        
        # Analysis
        total_entries = len(entries)
        success_entries = len([e for e in entries if e['status'] == 'SUCCESS'])
        success_rate = (success_entries / total_entries * 100) if total_entries > 0 else 0
        
        print(f"  Total Entries: {total_entries}")
        print(f"  Success Rate: {success_rate:.1f}%")
        
        # Method distribution
        methods = Counter(e['method'] for e in entries)
        print(f"  Methods Used:")
        for method, count in methods.most_common():
            print(f"    {method}: {count} ({count/total_entries*100:.1f}%)")
        
        # Sender distribution
        senders = Counter(e['sender'] for e in entries)
        print(f"  Sender Distribution:")
        for sender, count in senders.most_common():
            print(f"    {sender}: {count}")

def analyze_error_patterns():
    """Analyze error patterns across all logs"""
    
    print("\n🔍 ERROR PATTERN ANALYSIS")
    print("=" * 40)
    
    error_patterns = defaultdict(int)
    error_files = defaultdict(list)
    
    log_dir = "data/logs"
    if not os.path.exists(log_dir):
        print("❌ Log directory not found")
        return
    
    for filename in os.listdir(log_dir):
        if filename.endswith('.log'):
            filepath = os.path.join(log_dir, filename)
            
            with open(filepath, 'r') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                if 'ERROR' in line or 'error' in line or 'failed' in line.lower():
                    # Extract error type
                    if 'Database error' in line:
                        error_type = 'Database Error'
                    elif 'API error' in line:
                        error_type = 'API Error'
                    elif 'LLM' in line and 'error' in line:
                        error_type = 'LLM Error'
                    elif 'Email' in line and ('failed' in line or 'error' in line):
                        error_type = 'Email Error'
                    else:
                        error_type = 'General Error'
                    
                    error_patterns[error_type] += 1
                    error_files[error_type].append(f"{filename}:{line_num}")
    
    if error_patterns:
        print("Error Distribution:")
        for error_type, count in error_patterns.items():
            print(f"  {error_type}: {count} occurrences")
            # Show recent files
            recent_files = error_files[error_type][-3:]
            for file_ref in recent_files:
                print(f"    - {file_ref}")
    else:
        print("✅ No errors found in recent logs")

if __name__ == "__main__":
    analyze_campaign_logs()
    analyze_error_patterns()
```

## Automated Reporting

### Daily Report Cron Job
Add to crontab:

```bash
# Daily performance report at 9 AM
0 9 * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/analytics/daily_report.py >> data/logs/daily_reports.log 2>&1

# Weekly conversion funnel analysis on Monday at 10 AM
0 10 * * 1 cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/analytics/conversion_funnel.py >> data/logs/weekly_analysis.log 2>&1

# Performance alerts every 30 minutes
*/30 * * * * cd /Users/<USER>/Code/modernaipro/mai-administrative/new && python scripts/monitoring/performance_alerts.py >> data/logs/alerts.log 2>&1
```

## Key Monitoring Commands Reference

```bash
# Real-time dashboard
python scripts/monitoring/dashboard.py

# Daily performance report
python scripts/analytics/daily_report.py

# Conversion funnel analysis
python scripts/analytics/conversion_funnel.py

# Email generation performance
python scripts/analytics/email_generation_analysis.py

# Log analysis
python scripts/analytics/log_analyzer.py

# Performance alerts check
python scripts/monitoring/performance_alerts.py

# Quick system status
python scripts/core/lead_selector.py --action stats
python scripts/core/workflow_integration.py --stats
```

This comprehensive monitoring setup ensures you maintain high performance while scaling to 400 leads/day with complete visibility into system health and performance metrics.
