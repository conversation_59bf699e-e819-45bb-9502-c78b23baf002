# Monitoring Guide

## Overview
Comprehensive monitoring setup for ModernAI Pro administrative system to ensure optimal performance, early issue detection, and system reliability.

## Monitoring Architecture

```mermaid
graph TD
    A[System Components] --> B[Metrics Collection]
    B --> C[Monitoring Dashboard]
    C --> D[Alerting System]
    D --> E[Notification Channels]
    
    F[Dashboard APIs] --> B
    G[Facebook Scripts] --> B
    H[Stripe Integration] --> B
    I[Email Campaigns] --> B
    J[Database] --> B
    
    C --> K[Grafana/Custom Dashboard]
    E --> L[Email Alerts]
    E --> M[Slack/Discord]
    E --> N[SMS (Critical)]
    
    style A fill:#e1f5fe
    style C fill:#e8f5e8
    style D fill:#fff3e0
```

## Key Performance Indicators (KPIs)

### Business Metrics
| Metric | Target | Warning | Critical |
|--------|--------|---------|----------|
| Lead Generation Rate | >50/day | <30/day | <10/day |
| Payment Success Rate | >95% | <90% | <80% |
| Email Delivery Rate | >95% | <90% | <80% |
| Workshop Enrollment Rate | >60% | <40% | <20% |
| Customer Support Response | <2 hours | >4 hours | >8 hours |

### Technical Metrics
| Metric | Target | Warning | Critical |
|--------|--------|---------|----------|
| API Response Time | <200ms | >500ms | >1000ms |
| Database Query Time | <50ms | >200ms | >500ms |
| System Uptime | >99.5% | <99% | <95% |
| Error Rate | <1% | >3% | >5% |
| Disk Usage | <70% | >80% | >90% |

## System Monitoring

### Dashboard Application Monitoring

#### Health Check Endpoint
```javascript
// /api/health endpoint monitoring
{
  "status": "healthy",
  "database": "connected",
  "timestamp": "2024-08-08T15:30:00Z",
  "version": "1.0.0",
  "uptime": 86400,
  "metrics": {
    "total_leads": 1234,
    "active_campaigns": 5,
    "recent_payments": 23
  }
}
```

#### Monitoring Script
```bash
#!/bin/bash
# health-check.sh

API_URL="http://localhost:3000/api/health"
LOG_FILE="/var/log/modernaipro/health.log"

response=$(curl -s -w "%{http_code}" "$API_URL")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    echo "$(date): Dashboard API - OK" >> "$LOG_FILE"
else
    echo "$(date): Dashboard API - FAILED (HTTP: $http_code)" >> "$LOG_FILE"
    # Send alert
    ./send-alert.sh "Dashboard API Down" "HTTP Code: $http_code"
fi
```

### Database Monitoring

#### Performance Metrics
```sql
-- Database performance queries
SELECT 
    COUNT(*) as total_leads,
    COUNT(CASE WHEN payment_status = 'Paid' THEN 1 END) as paid_leads,
    COUNT(CASE WHEN created_time >= datetime('now', '-1 day') THEN 1 END) as daily_leads
FROM leads;

-- Check database size
SELECT 
    page_count * page_size / 1024 / 1024 as size_mb 
FROM pragma_page_count(), pragma_page_size();

-- Find slow queries (if query logging enabled)
SELECT sql, avg_time_ms FROM slow_queries ORDER BY avg_time_ms DESC;
```

#### Database Health Script
```bash
#!/bin/bash
# db-monitor.sh

DB_PATH="./dashboard/database/leads.db"
THRESHOLD_SIZE_MB=100
THRESHOLD_LEADS=50

# Check database accessibility
if sqlite3 "$DB_PATH" "SELECT 1;" > /dev/null 2>&1; then
    echo "Database: Accessible"
else
    echo "Database: ERROR - Cannot access"
    exit 1
fi

# Check database size
size=$(sqlite3 "$DB_PATH" "SELECT page_count * page_size / 1024 / 1024 FROM pragma_page_count(), pragma_page_size();")
if (( $(echo "$size > $THRESHOLD_SIZE_MB" | bc -l) )); then
    echo "WARNING: Database size ${size}MB exceeds threshold ${THRESHOLD_SIZE_MB}MB"
fi

# Check daily lead count
daily_leads=$(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM leads WHERE created_time >= datetime('now', '-1 day');")
if [ "$daily_leads" -lt "$THRESHOLD_LEADS" ]; then
    echo "WARNING: Low daily lead count: $daily_leads"
fi
```

## External Service Monitoring

### Facebook API Monitoring

#### Health Check Integration
```bash
#!/bin/bash
# fb-monitor.sh

cd scripts/facebook
python fb_account_manager.py status

# Parse output for alerts
if python fb_account_manager.py status | grep -q "CRITICAL"; then
    ./send-alert.sh "Facebook API Critical" "One or more Facebook accounts inaccessible"
elif python fb_account_manager.py status | grep -q "WARNING"; then
    ./send-alert.sh "Facebook API Warning" "Some Facebook accounts having issues"
fi
```

#### Campaign Performance Monitoring
```python
# fb-performance-monitor.py
def monitor_campaign_performance():
    manager = FacebookAccountManager()
    metrics = manager.fetch_all_campaign_metrics(days=1)
    
    alerts = []
    for account, data in metrics.items():
        # Check cost per lead
        if data['cost_per_lead'] > 50:  # Threshold: $50
            alerts.append(f"High CPL in {account}: ${data['cost_per_lead']:.2f}")
        
        # Check lead volume
        if data['total_leads'] == 0:
            alerts.append(f"No leads generated in {account}")
    
    if alerts:
        send_alert("Campaign Performance Alert", "\n".join(alerts))
```

### Stripe Payment Monitoring

#### Payment Processing Health
```python
# stripe-monitor.py
import stripe
from datetime import datetime, timedelta

def monitor_payments():
    # Check recent payment success rate
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=24)
    
    payments = stripe.PaymentIntent.list(
        created={'gte': int(start_time.timestamp())},
        limit=100
    )
    
    total = len(payments.data)
    successful = len([p for p in payments.data if p.status == 'succeeded'])
    
    success_rate = (successful / total * 100) if total > 0 else 0
    
    if success_rate < 90:
        send_alert("Low Payment Success Rate", 
                  f"Success rate: {success_rate:.1f}% ({successful}/{total})")
    
    return {
        'total_payments': total,
        'successful_payments': successful,
        'success_rate': success_rate
    }
```

#### Webhook Monitoring
```javascript
// webhook-monitor.js
const express = require('express');
const app = express();

let webhookStats = {
    received: 0,
    processed: 0,
    errors: 0,
    lastReceived: null
};

app.post('/api/webhooks/stripe', (req, res) => {
    webhookStats.received++;
    webhookStats.lastReceived = new Date();
    
    try {
        // Process webhook
        processStripeWebhook(req.body);
        webhookStats.processed++;
        res.status(200).send('OK');
    } catch (error) {
        webhookStats.errors++;
        console.error('Webhook error:', error);
        res.status(400).send('Error');
    }
});

// Health endpoint for webhook monitoring
app.get('/api/webhooks/health', (req, res) => {
    const errorRate = webhookStats.errors / webhookStats.received * 100;
    
    res.json({
        status: errorRate < 5 ? 'healthy' : 'degraded',
        stats: webhookStats,
        errorRate: errorRate
    });
});
```

## Log Monitoring

### Log Aggregation
```bash
#!/bin/bash
# log-aggregator.sh

LOG_DIR="/var/log/modernaipro"
ALERT_KEYWORDS=("ERROR" "CRITICAL" "FAILED" "TIMEOUT")

# Monitor all log files
for keyword in "${ALERT_KEYWORDS[@]}"; do
    matches=$(grep -r "$keyword" "$LOG_DIR"/*.log | wc -l)
    if [ "$matches" -gt 10 ]; then  # Threshold: 10 errors per check
        echo "High error rate detected: $matches instances of '$keyword'"
        ./send-alert.sh "High Error Rate" "$matches instances of '$keyword' found in logs"
    fi
done
```

### Real-time Log Monitoring
```bash
#!/bin/bash
# real-time-monitor.sh

# Monitor critical errors in real-time
tail -F logs/*.log | while read line; do
    if echo "$line" | grep -qE "(CRITICAL|FATAL|ERROR)"; then
        echo "ALERT: $line"
        # Send immediate notification for critical errors
        ./send-alert.sh "Critical Error Detected" "$line"
    fi
done
```

## Alerting System

### Alert Configuration
```yaml
# alerts.yaml
alerts:
  - name: "Database Connection Failed"
    condition: "database_status != 'connected'"
    severity: "critical"
    channels: ["email", "sms"]
    
  - name: "High API Response Time"
    condition: "api_response_time > 1000"
    severity: "warning" 
    channels: ["email"]
    
  - name: "Low Lead Generation"
    condition: "daily_leads < 10"
    severity: "warning"
    channels: ["email", "slack"]
    
  - name: "Payment Processing Down"
    condition: "payment_success_rate < 80"
    severity: "critical"
    channels: ["email", "sms", "slack"]
```

### Notification Script
```bash
#!/bin/bash
# send-alert.sh

SUBJECT="$1"
MESSAGE="$2"
SEVERITY="${3:-warning}"

# Email notification
echo "$MESSAGE" | mail -s "$SUBJECT" <EMAIL>

# Slack notification (if configured)
if [ -n "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"$SUBJECT: $MESSAGE\"}" \
        "$SLACK_WEBHOOK_URL"
fi

# SMS for critical alerts
if [ "$SEVERITY" = "critical" ] && [ -n "$TWILIO_PHONE" ]; then
    python scripts/utilities/send_sms.py "$TWILIO_PHONE" "$SUBJECT: $MESSAGE"
fi

# Log the alert
echo "$(date): ALERT [$SEVERITY] $SUBJECT - $MESSAGE" >> /var/log/modernaipro/alerts.log
```

## Dashboard and Visualization

### Custom Monitoring Dashboard
```html
<!-- monitoring-dashboard.html -->
<!DOCTYPE html>
<html>
<head>
    <title>ModernAI Pro - System Monitor</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="dashboard">
        <div class="metric-card">
            <h3>System Health</h3>
            <div id="system-status" class="status-indicator"></div>
        </div>
        
        <div class="metric-card">
            <h3>Daily Leads</h3>
            <canvas id="leads-chart"></canvas>
        </div>
        
        <div class="metric-card">
            <h3>Payment Success Rate</h3>
            <canvas id="payments-chart"></canvas>
        </div>
        
        <div class="metric-card">
            <h3>API Response Times</h3>
            <canvas id="response-times-chart"></canvas>
        </div>
    </div>
    
    <script>
        // Auto-refresh dashboard every 30 seconds
        setInterval(updateDashboard, 30000);
        
        function updateDashboard() {
            fetch('/api/monitoring/metrics')
                .then(response => response.json())
                .then(data => {
                    updateSystemStatus(data.systemHealth);
                    updateLeadsChart(data.dailyLeads);
                    updatePaymentsChart(data.paymentSuccess);
                    updateResponseTimesChart(data.apiResponseTimes);
                });
        }
    </script>
</body>
</html>
```

## Automated Monitoring Setup

### Cron Job Configuration
```bash
# /etc/cron.d/modernaipro-monitoring

# System health check every 5 minutes
*/5 * * * * /opt/modernaipro/scripts/monitoring/health-check.sh

# Database monitoring every 15 minutes  
*/15 * * * * /opt/modernaipro/scripts/monitoring/db-monitor.sh

# Facebook API monitoring every 30 minutes
*/30 * * * * /opt/modernaipro/scripts/monitoring/fb-monitor.sh

# Payment monitoring every hour
0 * * * * /opt/modernaipro/scripts/monitoring/stripe-monitor.sh

# Daily report generation
0 9 * * * /opt/modernaipro/scripts/monitoring/daily-report.sh

# Weekly summary
0 9 * * 1 /opt/modernaipro/scripts/monitoring/weekly-summary.sh
```

### Service Monitoring with systemd
```ini
# /etc/systemd/system/modernaipro-monitor.service
[Unit]
Description=ModernAI Pro Monitoring Service
After=network.target

[Service]
Type=simple
User=modernaipro
WorkingDirectory=/opt/modernaipro
ExecStart=/opt/modernaipro/scripts/monitoring/monitor-daemon.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## Performance Optimization Monitoring

### Resource Usage Tracking
```bash
#!/bin/bash
# resource-monitor.sh

# CPU Usage
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')

# Memory Usage
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.2f", ($3/$2) * 100.0)}')

# Disk Usage
DISK_USAGE=$(df / | grep -E "/" | awk '{print $(NF-1)}' | sed 's/%//')

# Log metrics
echo "$(date),CPU:$CPU_USAGE,Memory:$MEMORY_USAGE,Disk:$DISK_USAGE" >> /var/log/modernaipro/resources.log

# Alert if thresholds exceeded
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    ./send-alert.sh "High CPU Usage" "CPU usage: ${CPU_USAGE}%"
fi

if (( $(echo "$MEMORY_USAGE > 85" | bc -l) )); then
    ./send-alert.sh "High Memory Usage" "Memory usage: ${MEMORY_USAGE}%"
fi

if [ "$DISK_USAGE" -gt 90 ]; then
    ./send-alert.sh "High Disk Usage" "Disk usage: ${DISK_USAGE}%"
fi
```

## Compliance and Reporting

### Daily Status Report
```python
# daily-report.py
def generate_daily_report():
    report = {
        'date': datetime.now().strftime('%Y-%m-%d'),
        'system_uptime': get_system_uptime(),
        'leads_generated': get_daily_leads(),
        'payments_processed': get_daily_payments(),
        'api_performance': get_api_metrics(),
        'errors_detected': get_error_count(),
        'alerts_sent': get_alert_count()
    }
    
    # Generate report
    template = """
    Daily System Report - {date}
    
    System Status:
    - Uptime: {system_uptime}
    - Leads Generated: {leads_generated}
    - Payments Processed: {payments_processed}
    - API Average Response Time: {api_performance}ms
    
    Issues:
    - Errors Detected: {errors_detected}
    - Alerts Sent: {alerts_sent}
    """
    
    report_content = template.format(**report)
    
    # Email daily report
    send_email("Daily System Report", report_content)
    
    return report
```

This comprehensive monitoring guide provides the foundation for maintaining system reliability and performance. Regular monitoring ensures early detection of issues and helps maintain optimal system performance.