# Troubleshooting Guide

## Quick Diagnosis

### System Health Check
```bash
# System overview
python scripts/core/lead_selector.py --action stats

# Check recent errors
grep -i error data/logs/*.log | tail -5

# Database connection test
sqlite3 data/database/leads.db "SELECT COUNT(*) FROM leads;"
```

## Common Issues

### 1. No Leads Being Processed

**Symptoms**: Campaign shows "No leads found"

**Diagnosis**:
```bash
# Check lead statuses
python scripts/core/lead_selector.py --action status-summary

# Check recent leads
python scripts/core/lead_selector.py --action recent --hours 48
```

**Solutions**:
- Verify Facebook lead import is running: `tail data/logs/facebook_import.log`
- Check lead status filters in campaign commands
- Ensure leads aren't already contacted: leads with interactions are excluded

### 2. Email Sending Failures

**Symptoms**: "Email sending failed" in logs

**Diagnosis**:
```bash
# Check Gmail API authentication
ls -la config/modernaipro-b601d7749092.json

# Test email configuration
python -c "from scripts.core.email_library import EmailConfig; EmailConfig().sender_email"
```

**Solutions**:
- Verify service account file exists and has correct permissions
- Check Gmail API quotas (250 requests per user per second)
- Ensure sender email has domain delegation enabled

### 3. LLM Generation Failures

**Symptoms**: High template fallback rate (>50%)

**Diagnosis**:
```bash
# Check OpenAI API key
echo $OPENAI_API_KEY

# Review generation stats
tail -5 data/logs/email_generation_stats.log
```

**Solutions**:
- Verify OpenAI API key is set in environment
- Check API rate limits and billing status
- Review LLM prompts for errors

### 4. High Error Rate

**Symptoms**: Multiple errors in workflow logs

**Diagnosis**:
```bash
# Check error patterns
grep "ERROR\|failed" data/logs/primary_campaigns.log | tail -10

# Database integrity check
sqlite3 data/database/leads.db "PRAGMA integrity_check;"
```

**Solutions**:
- Review database connection issues
- Check disk space: `df -h .`
- Restart automation with smaller batch sizes

### 5. Performance Degradation

**Symptoms**: Slow processing, timeouts

**Diagnosis**:
```bash
# Check database size
ls -lh data/database/leads.db

# Monitor system resources
top | grep python
```

**Solutions**:
- Database cleanup: `sqlite3 data/database/leads.db "VACUUM;"`
- Reduce `--max-emails` parameter
- Implement rate limiting between campaigns

## Error Recovery Procedures

### Database Recovery
```bash
# Backup current database
cp data/database/leads.db data/database/leads_backup_$(date +%Y%m%d).db

# Restore from backup if needed
cp data/backups/leads_YYYYMMDD_HHMM.db.gz .
gunzip leads_YYYYMMDD_HHMM.db.gz
mv leads_YYYYMMDD_HHMM.db data/database/leads.db
```

### Reset Failed Campaigns
```bash
# Mark failed leads as "New" to retry
sqlite3 data/database/leads.db "
UPDATE leads 
SET status = 'New' 
WHERE id IN (
  SELECT lead_id FROM lead_interactions 
  WHERE outcome = 'failed' 
  AND interaction_date >= datetime('now', '-1 hour')
);"
```

### Clear Log Files
```bash
# Archive large log files
find data/logs -name "*.log" -size +50M -exec gzip {} \;

# Clear current logs (backup first)
for log in data/logs/*.log; do 
  cp "$log" "${log}.backup" && > "$log"
done
```

## Performance Optimization

### Scaling Issues
- **Slow processing**: Reduce batch size (`--max-emails 10`)
- **API timeouts**: Increase delays between requests
- **Database locks**: Implement connection pooling

### Memory Management
- **High memory usage**: Restart Python processes periodically
- **Log file growth**: Implement log rotation
- **Database bloat**: Regular VACUUM operations

## Emergency Procedures

### Stop All Automation
```bash
# Kill all running Python processes
pkill -f "python.*scripts/core"

# Disable cron jobs temporarily
crontab -l > cron_backup.txt
crontab -r
```

### Emergency Contact Processing
```bash
# Process high-priority leads only
python scripts/core/lead_selector.py --action criteria --location "US" --limit 5 | \
python scripts/core/email_campaign_manager.py --production --max-emails 5
```

### System Recovery
```bash
# Restore cron jobs
crontab cron_backup.txt

# Restart with minimal settings
python scripts/core/workflow_integration.py --production --max-emails 5 --hours-back 2
```

## Monitoring Commands

### Real-time Monitoring
```bash
# Watch campaign activity
tail -f data/logs/primary_campaigns.log

# Monitor system stats
watch -n 60 'python scripts/core/lead_selector.py --action stats'
```

### Performance Checks
```bash
# Daily health summary
python scripts/core/workflow_integration.py --stats

# Conversion rates
python scripts/core/lead_selector.py --action status-summary
```

## Support Escalation

### Before Escalating
1. Check error logs for specific error messages
2. Verify system resources (disk space, memory)
3. Test individual components in isolation
4. Document error patterns and timing

### Information to Provide
- Error messages from logs
- System configuration details
- Recent changes or deployments
- Performance metrics and trends
- Steps taken to resolve

### Emergency Contacts
- **System Issues**: Development team
- **API Problems**: Check service status pages
- **Database Issues**: Backup and restore procedures
- **Performance**: Scale down operations temporarily

Use this guide systematically - start with quick diagnosis, identify the specific issue category, then follow the appropriate solution path.
