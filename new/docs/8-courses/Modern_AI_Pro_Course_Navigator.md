# Modern AI Pro - Course Navigator & Notebook Mapping

> **Master Entry Point** for all Modern AI Pro courses and their associated notebooks

## Course Overview

| Course | Level | Duration | Focus Area | Target Audience |
|--------|-------|----------|------------|----------------|
| **AI Essentials** | Beginner | 2 days | Fundamentals & Theory | Managers, Executives |
| **AI Practitioner** | Intermediate | 3 days | Hands-on Implementation | Technical Professionals |
| **Agentic AI** | Advanced | 2 days | Autonomous Systems | Senior Engineers |
| **Vibe Coding** | Intermediate | 2 days | AI-Assisted Programming | Developers |

---

## 📚 Complete Notebook Mapping Table

| Notebook Title | Colab ID | Essentials | Practitioner | Agentic AI | Vibe Coding | Difficulty | Key Concepts | Time |
|----------------|----------|------------|--------------|------------|-------------|------------|--------------|------|
| **First LLM Call on Colab** | `14I39Cn...` | ✅ **Core** | ✅ Module 1 | ⚪ Intro | ⚪ Setup | Intermediate | LLM basics, Groq, API | 30min |
| **Simple AI Game** | `1gCjPm...` | ✅ **Core** | ❌ | ✅ **Core** | ❌ | Beginner | Interactive learning, Games | 45min |
| **First chatbot** | `1q14co...` | ✅ **Core** | ✅ Module 3 | ✅ **Core** | ⚪ Foundation | Intermediate | Chatbots, UI | 45min |
| **Local LLM with HuggingFace** | `1bz8eD...` | ⚪ Optional | ✅ Module 3 | ⚪ Reference | ✅ **Core** | Advanced | Local models, HF | 60min |
| **Multimodal chat** | `1EUu7l...` | ❌ | ✅ Module 3 | ✅ **Core** | ⚪ Optional | Intermediate | Vision, Images | 45min |
| **LLM with memory** | `1nIQbi...` | ⚪ Demo | ✅ Module 3 | ✅ **Core** | ⚪ Reference | Intermediate | Memory, State | 30min |
| **Types of Memory in LLMs** | `1AcyWG...` | ❌ | ⚪ Advanced | ✅ **Core** | ❌ | Advanced | Memory types | 60min |
| **Vector Similarity Search** | `1cQr0s...` | ❌ | ⚪ Advanced | ✅ **Core** | ⚪ Optional | Advanced | Vector DB, Embeddings | 60min |
| **LLM with search** | `1mK9R-...` | ⚪ Demo | ✅ Module 3 | ✅ **Core** | ⚪ Reference | Intermediate | Web search, Tools | 45min |
| **LLM with tools** | `16fm4M...` | ❌ | ✅ Module 3 | ✅ **Core** | ✅ **Core** | Intermediate | Function calling | 45min |
| **Agent with CrewAI** | `1ACt6T...` | ❌ | ⚪ Advanced | ✅ **Core** | ❌ | Advanced | CrewAI framework | 75min |
| **Agents talking to one another** | `1GKnxa...` | ❌ | ⚪ Advanced | ✅ **Core** | ❌ | Advanced | Multi-agent | 60min |
| **First agent with ReAct** | `1IK5Jv...` | ❌ | ✅ Module 3 | ✅ **Core** | ⚪ Optional | Advanced | ReAct, Reasoning | 75min |
| **Agents with Langgraph** | `15UuvE...` | ❌ | ✅ Module 3 | ✅ **Core** | ❌ | Advanced | Workflows, Graphs | 90min |
| **Advanced agents use case** | `1I2XOU...` | ❌ | ✅ Module 3 | ✅ **Core** | ❌ | Advanced | Real-world agents | 90min |
| **Q&A on documents** | `1icYUU...` | ✅ **Core** | ✅ Module 4 | ⚪ Reference | ⚪ Optional | Intermediate | RAG basics | 45min |
| **Analyzing Documents with LLM** | `1G-3Zl...` | ✅ **Core** | ✅ Module 4 | ⚪ Reference | ⚪ Optional | Intermediate | Document AI | 60min |
| **BrahmaSumm Simple** | `1Qt8-X...` | ✅ **Core** | ✅ Module 4 | ❌ | ❌ | Beginner | Summarization | 30min |
| **Table Analysis (Unstructured)** | `1YJ5px...` | ⚪ Demo | ✅ Module 4 | ❌ | ⚪ Optional | Advanced | Table extraction | 75min |
| **Multimodal Table & Image** | `1n2MZj...` | ❌ | ✅ Module 4 | ⚪ Reference | ⚪ Optional | Advanced | Vision, Tables | 90min |
| **Deep document analysis** | `1zwJZL...` | ❌ | ✅ Module 4 | ⚪ Reference | ❌ | Advanced | Complex docs | 90min |
| **SQLRAG** | `18ZyyC...` | ❌ | ✅ Module 7 | ⚪ Reference | ✅ **Core** | Advanced | Database + AI | 75min |
| **SQL RAG 2** | `1patli...` | ❌ | ✅ Module 7 | ❌ | ✅ **Core** | Advanced | Advanced SQL | 90min |
| **Advanced RAG for Finance** | `15ewza...` | ❌ | ✅ Module 7 | ⚪ Reference | ❌ | Advanced | Domain RAG | 120min |
| **GraphRAG** | `1AqYlc...` | ❌ | ✅ Module 7 | ⚪ Reference | ❌ | Advanced | Knowledge graphs | 120min |
| **Agentic RAG 1** | `1npEnR...` | ❌ | ⚪ Advanced | ✅ **Core** | ❌ | Advanced | Agentic retrieval | 90min |
| **RAG with authorization** | `1FarVX...` | ❌ | ⚪ Advanced | ⚪ Reference | ❌ | Advanced | Security, Auth | 90min |
| **Evaluating LLMs** | `1ohdsy...` | ⚪ Optional | ✅ Module 8 | ⚪ Reference | ⚪ Reference | Intermediate | Model evaluation | 60min |
| **Reading Excel data** | `1VWrfc...` | ✅ **Core** | ⚪ Utility | ❌ | ✅ **Core** | Beginner | Data processing | 30min |
| **DICOM data** | `14nOmr...` | ❌ | ⚪ Specialty | ❌ | ❌ | Advanced | Medical imaging | 60min |

---

## 🎯 Course-Specific Learning Paths

### 🌟 **AI Essentials** - *Management & Strategy*
**Target**: Executives, Managers, Business Leaders  
**Goal**: Comprehensive understanding of AI from foundations to cutting-edge applications

#### 6-Session Learning Path (12 hours total):
**Session 1**: ML Foundations (Intro Course, Linear Regression)
**Session 2**: Classification & Prediction (Home Prices, Income Classification)  
**Session 3**: Neural Networks & Industrial AI (Deep Learning, Fault Prediction)
**Session 4**: Data Science Tools (Visualization, Pandas, NumPy)
**Session 5**: Generative AI & LLMs (First LLM, Chatbot, Memory, Real-time Data)
**Session 6**: Advanced AI (Vector Search, Multimodal, RAG)

**Supplementary Deep-Dives**: 14 additional notebooks covering Decision Trees, Logistic Regression, Neural Networks, Video AI, Model Categories, and more

**Business Focus**: Hands-on experience with 31 different AI applications to enable strategic AI decision-making

---

### 🔧 **AI Practitioner** - *Technical Implementation*
**Target**: Engineers, Data Scientists, Technical Managers  
**Goal**: Build and deploy AI applications

#### Learning Path (3 Days):
**Day 1 - Foundation**
- Module 1: First LLM Call

**Day 2 - Agents & Intelligence** 
- Module 3: Chatbot → Memory → Tools → Agents → ReAct → Langgraph

**Day 3 - Documents & Production**
- Module 4: Document Q&A → Analysis → Tables → Multimodal
- Module 7: SQLRAG → GraphRAG → Advanced RAG
- Module 8: Evaluation

---

### 🤖 **Agentic AI** - *Autonomous Systems*
**Target**: Senior Engineers, AI Researchers  
**Goal**: Master autonomous AI agents and workflows

#### Core Sequence:
1. **Simple AI Game** → Interactive foundations
2. **First chatbot** → Agent foundations
3. **Multimodal chat** → Multi-input agents
4. **LLM with memory** → Stateful agents
5. **Memory types** → Advanced memory systems
6. **Vector Similarity Search** → Embedding foundations
7. **LLM with search** → Tool-using agents
8. **LLM with tools** → Function-calling agents
9. **Agent with CrewAI** → Framework-based agents
10. **Multi-agent systems** → Agent collaboration
11. **ReAct Framework** → Reasoning agents
12. **Langgraph** → Complex workflows
13. **Agentic RAG 1** → Advanced retrieval agents
14. **Advanced use cases** → Real-world deployment

---

### 💻 **Vibe Coding** - *AI-Assisted Development*
**Target**: Software Engineers, Full-stack Developers  
**Goal**: Integrate AI into development workflows

#### Core Sequence:
1. **Local LLM** → Development environment setup
2. **LLM with tools** → Code generation & assistance
3. **SQLRAG** → Database-driven applications
4. **SQL RAG 2** → Advanced database operations
5. **Reading Excel** → Data processing workflows

**Optional**: First chatbot, Memory systems, Evaluation

---

## 🔄 Notebook Dependencies & Prerequisites

```mermaid
graph TD
    A[First LLM Call] --> B[First chatbot]
    B --> C[LLM with memory]
    C --> D[Types of Memory]
    C --> E[LLM with search]
    E --> F[LLM with tools]
    F --> G[ReAct agents]
    G --> H[Langgraph]
    B --> I[Multimodal chat]
    
    J[Q&A documents] --> K[Document Analysis]
    K --> L[Table Analysis]
    L --> M[Multimodal Tables]
    
    N[SQLRAG] --> O[SQL RAG 2]
    J --> P[GraphRAG]
    
    Q[Reading Excel] --> N
```

---

## 📖 Quick Course Selection Guide

| **If you are...** | **Choose Course** | **Start with** |
|-------------------|------------------|----------------|
| Executive/Manager wanting AI overview | **AI Essentials** | First LLM Call |
| Engineer building AI apps | **AI Practitioner** | Module 1 sequence |
| Researcher/Senior dev building agents | **Agentic AI** | First chatbot |
| Developer integrating AI in code | **Vibe Coding** | Local LLM |

---

## 🛠️ Setup Requirements by Course

### Universal Setup:
- Colab account (Google)
- API Keys: Groq, HuggingFace
- Basic Python knowledge

### Course-Specific:
- **Essentials**: None additional
- **Practitioner**: Tavily, Kaggle, Langfuse keys
- **Agentic AI**: All APIs + advanced Python
- **Vibe Coding**: Local development environment

---

## 📊 Difficulty & Time Estimates

| **Level** | **Notebook Count** | **Total Time** | **Prerequisites** |
|-----------|-------------------|----------------|-------------------|
| **Essentials** | 31 notebooks (17 core + 14 supplementary) | ~12 hours | Business context |
| **Practitioner** | 27 notebooks | ~20 hours | Python basics |
| **Agentic AI** | 16 core notebooks | ~17 hours | Advanced Python |
| **Vibe Coding** | 8 notebooks | ~6 hours | Development experience |

---

*Last updated: August 2025*  
*Generated from: Modern AI Pro Practitioner extraction*