#  Comparative Analysis for Franklin Resources 10-K
Context 1: https://colab.research.google.com/drive/15ewzaL4odo0jVqZro65AqVkWDI-_8Y3f?usp=drive_link#scrollTo=-8hv-diccFQA

Context 2: https://colab.research.google.com/drive/1c3MTtyYFyleP-08O7qB8sWLAbDVdyIri#scrollTo=gtCj5zRHLBXZ


## Core Differences

|Aspect|Vector RAG|GraphRAG|
|---|---|---|
|**Strengths**|Finding information **about** entities|Understanding **relationships between** entities|
|**Reasoning Type**|Semantic similarity matching|Multi-hop relationship traversal|
|**Data Structure**|Flat document chunks|Connected entity graph|
|**Query Complexity**|Simple factual lookups|Complex relational analysis|

## Detailed Comparison Table

| Use Case                     | Vector RAG Challenge                                                                                                                                                     | GraphRAG Solution                                                                                                                                                                  | Example Query                                                                                                                                     |
| ---------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Organizational Hierarchy** | ❌ Finds mentions of "subsidiaries" but cannot construct multi-level hierarchies<br>❌ Cannot trace "Company X → owns → Subsidiary Y → manages → Fund Z"                   | ✅ Creates nodes: "Franklin Resources," "Subsidiary," "Investment Fund"<br>✅ Links with edges: `owns`, `manages`, `acquired`<br>✅ Direct graph traversal for hierarchical questions | _"What are all direct and indirect subsidiaries of Franklin Resources that manage alternative investment products, and their acquisition dates?"_ |
| **Risk Management**          | ❌ Identifies passages about "cybersecurity risks" and "regulatory compliance"<br>❌ Cannot link risks → business operations → responsible parties → mitigation strategies | ✅ Nodes: "Risk (Cybersecurity)," "Business Segment (IT)," "Person (CIO)," "Mitigation Strategy"<br>✅ Relationships: `affects`, `responsible_for`, `implements`                     | _"Identify all risks in Asia-Pacific operations and corresponding compliance measures with responsible personnel"_                                |
| **Acquisition Analysis**     | ❌ Finds separate info about acquisitions and financial figures<br>❌ Cannot connect acquisition terms → AUM impact → strategic priorities                                 | ✅ Nodes: "Acquisition (Putnam)," "Strategic Priority," "Financial Metric (AUM)," "Person (CFO)"<br>✅ Links financial terms to strategic outcomes                                   | _"What were Alcentra acquisition financial terms, AUM impact, and which executive discussed strategic benefits?"_                                 |
| **Legal Proceedings**        | ❌ Retrieves text about India Credit Fund matters<br>❌ Cannot extract/link entities (FTTS, SEBI), allegations, penalties, status                                          | ✅ Nodes: "Legal Case," "Party (FTTS, SEBI)," "Allegation," "Sanction," "Status"<br>✅ Explicit relationship mapping                                                                 | _"Detail India legal proceedings: entities involved, allegations, penalties imposed, current appeal status"_                                      |
| **Personnel Management**     | ❌ Lists job titles and names<br>❌ Cannot understand reporting lines, committee memberships, cross-departmental roles                                                     | ✅ Nodes: "Person (Jennifer Johnson)," "Role (CEO)," "Committee (Board)," "Asset Class"<br>✅ Relationships: `holds_role`, `serves_on`, `heads_department`                           | _"List executive officers who are also directors, their external boards, and internal responsibilities"_                                          |

## Technical Implementation Comparison

|Feature|Vector RAG|GraphRAG|
|---|---|---|
|**Data Processing**|Text chunking + embeddings|Entity extraction + relationship mapping|
|**Storage**|Vector database (ChromaDB, Pinecone)|Graph database (NetworkX, Neo4j)|
|**Retrieval Method**|Cosine similarity search|Graph traversal algorithms|
|**Query Processing**|Single-step similarity matching|Multi-hop relationship following|
|**Context Building**|Similar text chunks|Connected entity subgraphs|
|**Scalability**|High (parallel vector ops)|Medium (graph traversal complexity)|

## When to Use Which Approach

### Choose Vector RAG For:

- Simple factual questions
- Document similarity search
- Large-scale content retrieval
- When relationships are less important

### Choose GraphRAG For:

- Multi-entity relationship queries
- Complex reasoning across connections
- Hierarchical structure analysis
- Cross-functional impact assessment

## Implementation Considerations for Financial Documents

GraphRAG provides significant advantages for 10-K reports because they contain:

|Document Characteristic|GraphRAG Benefit|
|---|---|
|**Corporate hierarchies**|Natural parent-subsidiary mapping|
|**Risk interdependencies**|Multi-hop risk → impact → mitigation chains|
|**Financial relationships**|Acquisition → performance → strategy connections|
|**Legal complexity**|Party → allegation → outcome relationships|
|**Executive responsibilities**|Role → committee → function mappings|

## Bottom Line

**Vector RAG**: Best for finding relevant documents  
**GraphRAG**: Best for understanding how information connects