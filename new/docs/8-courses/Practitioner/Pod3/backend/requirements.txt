# Legal AI Pod - Backend Requirements
# Core Flask and web framework dependencies
flask==3.0.0
flask-cors==4.0.0
gunicorn==21.2.0
python-dotenv==1.0.0

# AI and Machine Learning dependencies
google-generativeai==0.3.2
openai==1.3.7
sentence-transformers==2.2.2
chromadb==0.4.18

# Security and encryption dependencies
cryptography==41.0.7
PyJWT==2.8.0
bcrypt==4.1.2

# Data processing dependencies
pandas==2.0.3
numpy==1.24.3
scikit-learn==1.3.0

# Legal document processing
PyPDF2==3.0.1
python-docx==1.1.0
nltk==3.8.1

# HTTP and utilities
requests==2.31.0
urllib3==2.1.0
Jinja2==3.1.2

# JSON processing
json5==0.9.14
jsonschema==4.20.0

# Legal-specific dependencies for compliance
reportlab==4.0.7  # For generating legal reports
openpyxl==3.1.2   # For Excel export functionality

# Email validation for attorney contact info
email-validator==2.1.0

# Production deployment
redis==5.0.1
celery==5.3.4

# Legal database seeding
faker==20.1.0  # For generating sample legal data

# Date and time handling
python-dateutil==2.8.2

# File handling and processing
pathlib2==2.3.7

# Validation libraries
marshmallow==3.20.1

# Background task processing
rq==1.15.1

# Legal text processing
spacy==3.7.2
transformers==4.36.1

# Citation parsing and legal document analysis
python-Levenshtein==0.23.0

# Development server
werkzeug==2.3.7

# API documentation
flask-restx==1.3.0

# Testing dependencies (for development)
pytest==7.4.3
pytest-cov==4.1.0

# Code quality (for development)
black==23.11.0
flake8==6.1.0

# Logging and monitoring
structlog==23.2.0

# Process management
psutil==5.9.6

# Memory profiling (for development)
memory-profiler==0.61.0

# HTTP client libraries
httpx==0.25.2