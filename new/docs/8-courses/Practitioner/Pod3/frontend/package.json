{"name": "legal-ai-pod-frontend", "version": "1.0.0", "description": "Legal AI Pod - Case Intelligence System Frontend", "main": "src/index.js", "homepage": ".", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "@mui/material": "^5.11.10", "@mui/icons-material": "^5.11.9", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/x-date-pickers": "^5.0.20", "@mui/lab": "^5.0.0-alpha.120", "axios": "^1.3.4", "date-fns": "^2.29.3", "lodash": "^4.17.21", "recharts": "^2.5.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.43.5", "react-query": "^3.39.3", "@hookform/resolvers": "^2.9.11", "yup": "^1.0.2", "react-beautiful-dnd": "^13.1.1", "react-pdf": "^6.2.2", "pdfjs-dist": "^3.4.120", "react-dropzone": "^14.2.3", "file-saver": "^2.0.5", "react-syntax-highlighter": "^15.5.0", "react-markdown": "^8.0.5", "prism-react-renderer": "^1.3.5", "react-hot-toast": "^2.4.0", "framer-motion": "^10.0.1", "react-intersection-observer": "^9.4.3", "react-virtualized-auto-sizer": "^1.0.15", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.8", "dayjs": "^1.11.7", "uuid": "^9.0.0", "web-vitals": "^3.1.1"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/node": "^18.14.2", "@types/lodash": "^4.14.191", "@types/uuid": "^9.0.1", "@types/file-saver": "^2.0.5", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "eslint": "^8.35.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.4", "typescript": "^4.9.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}", "type-check": "tsc --noEmit", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000", "keywords": ["legal-ai", "case-intelligence", "attorney-client-privilege", "legal-research", "document-review", "precedent-mining", "legal-ethics", "ai-compliance"], "author": "Legal AI Pod Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/balajivis/Legal-AI-Pod-Case-Intelligence-System.git"}, "bugs": {"url": "https://github.com/balajivis/Legal-AI-Pod-Case-Intelligence-System/issues"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}