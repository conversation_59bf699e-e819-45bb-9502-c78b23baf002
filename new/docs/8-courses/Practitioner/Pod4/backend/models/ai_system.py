#!/usr/bin/env python3
"""
AI System Model for AI Governance System
Represents AI systems with their governance metadata and status
"""

from enum import Enum
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field
import json


class SystemStatus(Enum):
    """AI System deployment status"""
    DEVELOPMENT = "development"
    TESTING = "testing" 
    STAGING = "staging"
    PRODUCTION = "production"
    MAINTENANCE = "maintenance"
    DEPRECATED = "deprecated"
    DECOMMISSIONED = "decommissioned"


class RiskCategory(Enum):
    """AI System risk category based on regulatory frameworks"""
    PROHIBITED = "prohibited"
    HIGH_RISK = "high_risk"
    LIMITED_RISK = "limited_risk"
    MINIMAL_RISK = "minimal_risk"
    UNKNOWN = "unknown"


class SystemType(Enum):
    """Types of AI systems"""
    MACHINE_LEARNING_MODEL = "machine_learning_model"
    DEEP_LEARNING_MODEL = "deep_learning_model"
    NATURAL_LANGUAGE_PROCESSING = "natural_language_processing"
    COMPUTER_VISION = "computer_vision"
    CONVERSATIONAL_AI = "conversational_ai"
    RECOMMENDATION_SYSTEM = "recommendation_system"
    DECISION_SUPPORT_SYSTEM = "decision_support_system"
    AUTONOMOUS_SYSTEM = "autonomous_system"
    OTHER = "other"


@dataclass
class AISystem:
    """
    AI System model representing a single AI system with governance metadata
    """
    system_id: str
    system_name: str
    system_type: SystemType
    deployment_status: SystemStatus = SystemStatus.DEVELOPMENT
    risk_category: RiskCategory = RiskCategory.UNKNOWN
    owner_team: str = ""
    business_unit: str = ""
    description: str = ""
    system_architecture: str = ""
    data_sources: str = ""
    model_details: str = ""
    deployment_environment: str = ""
    user_base_size: int = 0
    regulatory_scope: str = ""
    last_assessment_date: Optional[datetime] = None
    last_assessment_id: Optional[str] = None
    governance_score: float = 0.0
    compliance_status: str = "unknown"
    monitoring_status: str = "inactive"
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post-initialization processing"""
        # Ensure enums are properly set
        if isinstance(self.system_type, str):
            self.system_type = SystemType(self.system_type)
        if isinstance(self.deployment_status, str):
            self.deployment_status = SystemStatus(self.deployment_status)
        if isinstance(self.risk_category, str):
            self.risk_category = RiskCategory(self.risk_category)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert AI system to dictionary"""
        return {
            'system_id': self.system_id,
            'system_name': self.system_name,
            'system_type': self.system_type.value,
            'deployment_status': self.deployment_status.value,
            'risk_category': self.risk_category.value,
            'owner_team': self.owner_team,
            'business_unit': self.business_unit,
            'description': self.description,
            'system_architecture': self.system_architecture,
            'data_sources': self.data_sources,
            'model_details': self.model_details,
            'deployment_environment': self.deployment_environment,
            'user_base_size': self.user_base_size,
            'regulatory_scope': self.regulatory_scope,
            'last_assessment_date': self.last_assessment_date.isoformat() if self.last_assessment_date else None,
            'last_assessment_id': self.last_assessment_id,
            'governance_score': self.governance_score,
            'compliance_status': self.compliance_status,
            'monitoring_status': self.monitoring_status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AISystem':
        """Create AI system from dictionary"""
        # Handle datetime fields
        if data.get('last_assessment_date'):
            data['last_assessment_date'] = datetime.fromisoformat(data['last_assessment_date'])
        if data.get('created_at'):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data.get('updated_at'):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        # Handle enum fields
        if data.get('system_type'):
            data['system_type'] = SystemType(data['system_type'])
        if data.get('deployment_status'):
            data['deployment_status'] = SystemStatus(data['deployment_status'])
        if data.get('risk_category'):
            data['risk_category'] = RiskCategory(data['risk_category'])
        
        return cls(**data)
    
    def to_json(self) -> str:
        """Convert AI system to JSON string"""
        return json.dumps(self.to_dict(), indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'AISystem':
        """Create AI system from JSON string"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def update_governance_score(self, score: float) -> None:
        """Update governance score and timestamp"""
        self.governance_score = max(0.0, min(100.0, score))
        self.updated_at = datetime.now()
    
    def update_assessment_info(self, assessment_id: str, assessment_date: datetime = None) -> None:
        """Update latest assessment information"""
        self.last_assessment_id = assessment_id
        self.last_assessment_date = assessment_date or datetime.now()
        self.updated_at = datetime.now()
    
    def update_compliance_status(self, status: str) -> None:
        """Update compliance status"""
        valid_statuses = [
            'compliant', 'partially_compliant', 'non_compliant', 
            'requires_review', 'unknown', 'assessment_failed'
        ]
        if status in valid_statuses:
            self.compliance_status = status
            self.updated_at = datetime.now()
    
    def update_risk_category(self, risk_category: RiskCategory) -> None:
        """Update risk category"""
        self.risk_category = risk_category
        self.updated_at = datetime.now()
    
    def set_deployment_status(self, status: SystemStatus) -> None:
        """Update deployment status"""
        self.deployment_status = status
        self.updated_at = datetime.now()
    
    def add_metadata(self, key: str, value: Any) -> None:
        """Add metadata key-value pair"""
        self.metadata[key] = value
        self.updated_at = datetime.now()
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata value by key"""
        return self.metadata.get(key, default)
    
    def is_high_risk(self) -> bool:
        """Check if system is classified as high risk"""
        return self.risk_category in [RiskCategory.PROHIBITED, RiskCategory.HIGH_RISK]
    
    def is_production(self) -> bool:
        """Check if system is in production"""
        return self.deployment_status == SystemStatus.PRODUCTION
    
    def requires_compliance_review(self) -> bool:
        """Check if system requires compliance review"""
        return (
            self.compliance_status in ['non_compliant', 'requires_review', 'unknown'] or
            self.is_high_risk() or
            (self.last_assessment_date and 
             (datetime.now() - self.last_assessment_date).days > 90)  # 90 days without assessment
        )
    
    def get_governance_summary(self) -> Dict[str, Any]:
        """Get governance summary for dashboard display"""
        return {
            'system_id': self.system_id,
            'system_name': self.system_name,
            'system_type': self.system_type.value,
            'deployment_status': self.deployment_status.value,
            'risk_category': self.risk_category.value,
            'governance_score': self.governance_score,
            'compliance_status': self.compliance_status,
            'owner_team': self.owner_team,
            'business_unit': self.business_unit,
            'last_assessment_date': self.last_assessment_date.isoformat() if self.last_assessment_date else None,
            'requires_review': self.requires_compliance_review(),
            'is_high_risk': self.is_high_risk(),
            'is_production': self.is_production()
        }
    
    def validate(self) -> List[str]:
        """Validate AI system data and return list of validation errors"""
        errors = []
        
        if not self.system_id or not self.system_id.strip():
            errors.append("System ID is required")
        
        if not self.system_name or not self.system_name.strip():
            errors.append("System name is required")
        
        if not self.owner_team or not self.owner_team.strip():
            errors.append("Owner team is required")
        
        if self.governance_score < 0 or self.governance_score > 100:
            errors.append("Governance score must be between 0 and 100")
        
        if self.user_base_size < 0:
            errors.append("User base size cannot be negative")
        
        # Risk category validation
        if (self.deployment_status == SystemStatus.PRODUCTION and 
            self.risk_category == RiskCategory.UNKNOWN):
            errors.append("Production systems must have assessed risk category")
        
        # High-risk system validation
        if (self.is_high_risk() and self.deployment_status == SystemStatus.PRODUCTION and 
            not self.last_assessment_date):
            errors.append("High-risk production systems must have recent assessment")
        
        return errors
    
    def __str__(self) -> str:
        """String representation of AI system"""
        return f"AISystem(id={self.system_id}, name={self.system_name}, status={self.deployment_status.value})"
    
    def __repr__(self) -> str:
        """Detailed string representation"""
        return (f"AISystem(system_id='{self.system_id}', system_name='{self.system_name}', "
                f"system_type={self.system_type.value}, deployment_status={self.deployment_status.value}, "
                f"risk_category={self.risk_category.value}, governance_score={self.governance_score})")


# Factory functions for creating AI systems

def create_ai_system(system_id: str, system_name: str, system_type: SystemType,
                    owner_team: str, **kwargs) -> AISystem:
    """Factory function to create a new AI system with validation"""
    ai_system = AISystem(
        system_id=system_id,
        system_name=system_name,
        system_type=system_type,
        owner_team=owner_team,
        **kwargs
    )
    
    # Validate the created system
    errors = ai_system.validate()
    if errors:
        raise ValueError(f"AI system validation failed: {', '.join(errors)}")
    
    return ai_system


def create_ml_model_system(system_id: str, system_name: str, owner_team: str,
                          model_architecture: str, data_sources: str, **kwargs) -> AISystem:
    """Factory function to create ML model AI system"""
    return create_ai_system(
        system_id=system_id,
        system_name=system_name,
        system_type=SystemType.MACHINE_LEARNING_MODEL,
        owner_team=owner_team,
        system_architecture=model_architecture,
        data_sources=data_sources,
        **kwargs
    )


def create_conversational_ai_system(system_id: str, system_name: str, owner_team: str,
                                   language_model: str, knowledge_base: str, **kwargs) -> AISystem:
    """Factory function to create conversational AI system"""
    return create_ai_system(
        system_id=system_id,
        system_name=system_name,
        system_type=SystemType.CONVERSATIONAL_AI,
        owner_team=owner_team,
        model_details=language_model,
        data_sources=knowledge_base,
        **kwargs
    )


# Utility functions for AI system management

def filter_systems_by_risk(systems: List[AISystem], risk_categories: List[RiskCategory]) -> List[AISystem]:
    """Filter AI systems by risk categories"""
    return [system for system in systems if system.risk_category in risk_categories]


def filter_systems_by_status(systems: List[AISystem], statuses: List[SystemStatus]) -> List[AISystem]:
    """Filter AI systems by deployment status"""
    return [system for system in systems if system.deployment_status in statuses]


def get_high_risk_systems(systems: List[AISystem]) -> List[AISystem]:
    """Get all high-risk AI systems"""
    return filter_systems_by_risk(systems, [RiskCategory.PROHIBITED, RiskCategory.HIGH_RISK])


def get_production_systems(systems: List[AISystem]) -> List[AISystem]:
    """Get all production AI systems"""
    return filter_systems_by_status(systems, [SystemStatus.PRODUCTION])


def get_systems_requiring_review(systems: List[AISystem]) -> List[AISystem]:
    """Get AI systems that require governance review"""
    return [system for system in systems if system.requires_compliance_review()]


def calculate_average_governance_score(systems: List[AISystem]) -> float:
    """Calculate average governance score across systems"""
    if not systems:
        return 0.0
    
    scores = [system.governance_score for system in systems if system.governance_score > 0]
    return sum(scores) / len(scores) if scores else 0.0


def group_systems_by_business_unit(systems: List[AISystem]) -> Dict[str, List[AISystem]]:
    """Group AI systems by business unit"""
    grouped = {}
    for system in systems:
        unit = system.business_unit or "Unknown"
        if unit not in grouped:
            grouped[unit] = []
        grouped[unit].append(system)
    return grouped


def get_governance_statistics(systems: List[AISystem]) -> Dict[str, Any]:
    """Get comprehensive governance statistics for a list of systems"""
    if not systems:
        return {}
    
    total_systems = len(systems)
    
    # Status distribution
    status_counts = {}
    for status in SystemStatus:
        count = len(filter_systems_by_status(systems, [status]))
        if count > 0:
            status_counts[status.value] = count
    
    # Risk distribution
    risk_counts = {}
    for risk in RiskCategory:
        count = len(filter_systems_by_risk(systems, [risk]))
        if count > 0:
            risk_counts[risk.value] = count
    
    # Compliance distribution
    compliance_counts = {}
    for system in systems:
        status = system.compliance_status
        compliance_counts[status] = compliance_counts.get(status, 0) + 1
    
    return {
        'total_systems': total_systems,
        'status_distribution': status_counts,
        'risk_distribution': risk_counts,
        'compliance_distribution': compliance_counts,
        'high_risk_systems': len(get_high_risk_systems(systems)),
        'production_systems': len(get_production_systems(systems)),
        'systems_requiring_review': len(get_systems_requiring_review(systems)),
        'average_governance_score': calculate_average_governance_score(systems),
        'governance_score_distribution': {
            'excellent': len([s for s in systems if s.governance_score >= 90]),
            'good': len([s for s in systems if 70 <= s.governance_score < 90]),
            'needs_improvement': len([s for s in systems if 50 <= s.governance_score < 70]),
            'critical': len([s for s in systems if s.governance_score < 50])
        }
    }