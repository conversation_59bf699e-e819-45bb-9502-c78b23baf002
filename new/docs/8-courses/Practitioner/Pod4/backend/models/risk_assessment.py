#!/usr/bin/env python3
"""
Risk Assessment Model for AI Governance System
"""

from enum import Enum
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field
import json


class RiskLevel(Enum):
    """Risk assessment levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class RiskDimension(Enum):
    """Risk assessment dimensions"""
    TECHNICAL = "technical"
    ETHICAL = "ethical"
    REGULATORY = "regulatory"
    OPERATIONAL = "operational"


@dataclass
class RiskAssessment:
    """Risk Assessment model"""
    assessment_id: str
    system_id: str
    assessor_id: str
    risk_level: RiskLevel
    risk_score: float
    identified_risks: List[str] = field(default_factory=list)
    risk_dimensions: Dict[str, List[str]] = field(default_factory=dict)
    mitigation_recommendations: List[Dict[str, Any]] = field(default_factory=list)
    confidence_level: int = 7
    assessment_methodology: str = ""
    processing_time: float = 0.0
    assessment_date: datetime = field(default_factory=datetime.now)
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'assessment_id': self.assessment_id,
            'system_id': self.system_id,
            'assessor_id': self.assessor_id,
            'risk_level': self.risk_level.value,
            'risk_score': self.risk_score,
            'identified_risks': self.identified_risks,
            'risk_dimensions': self.risk_dimensions,
            'mitigation_recommendations': self.mitigation_recommendations,
            'confidence_level': self.confidence_level,
            'assessment_methodology': self.assessment_methodology,
            'processing_time': self.processing_time,
            'assessment_date': self.assessment_date.isoformat(),
            'created_at': self.created_at.isoformat()
        }