#!/usr/bin/env python3
"""
Monitoring Manager for AI Governance System
Provides comprehensive monitoring, metrics collection, and alerting for governance operations
"""

import logging
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading
import uuid
import statistics
import os

logger = logging.getLogger(__name__)

class GovernanceMonitoring:
    """
    Comprehensive monitoring system for AI governance operations
    Tracks performance, compliance metrics, system health, and governance trends
    """
    
    def __init__(self):
        """Initialize monitoring system with metrics collection and alerting"""
        try:\n            # Monitoring configuration\n            self.monitoring_config = {\n                'metrics_retention_hours': 168,  # 7 days\n                'alert_check_interval_seconds': 60,\n                'performance_threshold_seconds': 30,\n                'compliance_score_threshold': 70,\n                'bias_score_threshold': 7,\n                'system_health_check_interval_minutes': 15,\n                'dashboard_refresh_interval_seconds': 30\n            }\n            \n            # Metrics storage (in production, use time-series database)\n            self.metrics_store = {\n                'system_performance': deque(maxlen=10000),\n                'assessment_metrics': deque(maxlen=10000),\n                'compliance_metrics': deque(maxlen=10000),\n                'security_metrics': deque(maxlen=10000),\n                'user_activity': deque(maxlen=10000),\n                'error_metrics': deque(maxlen=10000),\n                'resource_usage': deque(maxlen=10000),\n                'governance_trends': deque(maxlen=10000)\n            }\n            \n            # Alert thresholds and rules\n            self.alert_rules = {\n                'high_response_time': {\n                    'threshold': 30,  # seconds\n                    'severity': 'warning',\n                    'enabled': True\n                },\n                'low_compliance_score': {\n                    'threshold': 70,  # percentage\n                    'severity': 'error',\n                    'enabled': True\n                },\n                'high_bias_risk': {\n                    'threshold': 7,  # risk score out of 10\n                    'severity': 'warning',\n                    'enabled': True\n                },\n                'system_error_rate': {\n                    'threshold': 5,  # percentage\n                    'severity': 'error',\n                    'enabled': True\n                },\n                'assessment_failure_rate': {\n                    'threshold': 10,  # percentage\n                    'severity': 'warning',\n                    'enabled': True\n                }\n            }\n            \n            # Active alerts tracking\n            self.active_alerts = {}\n            self.alert_history = deque(maxlen=1000)\n            \n            # Performance tracking\n            self.performance_tracker = {\n                'api_response_times': deque(maxlen=1000),\n                'agent_processing_times': defaultdict(lambda: deque(maxlen=100)),\n                'database_query_times': deque(maxlen=1000),\n                'concurrent_sessions': 0,\n                'total_assessments': 0,\n                'successful_assessments': 0,\n                'failed_assessments': 0\n            }\n            \n            # System health indicators\n            self.system_health = {\n                'overall_status': 'healthy',\n                'components': {\n                    'database': 'healthy',\n                    'ai_agents': 'healthy',\n                    'security': 'healthy',\n                    'knowledge_store': 'healthy',\n                    'api': 'healthy'\n                },\n                'last_health_check': datetime.now().isoformat(),\n                'uptime_start': datetime.now().isoformat()\n            }\n            \n            # Governance dashboard data cache\n            self.dashboard_cache = {\n                'data': {},\n                'last_updated': None,\n                'cache_duration_seconds': 300  # 5 minutes\n            }\n            \n            logger.info(\"GovernanceMonitoring initialized successfully\")\n            \n        except Exception as e:\n            logger.error(f\"Failed to initialize GovernanceMonitoring: {str(e)}\")\n            raise\n    \n    def record_metric(self, metric_type: str, metric_name: str, value: Any,\n                     tags: Dict[str, str] = None, timestamp: datetime = None) -> bool:\n        \"\"\"Record a metric with optional tags and timestamp\"\"\"\n        try:\n            if timestamp is None:\n                timestamp = datetime.now()\n            \n            metric_entry = {\n                'timestamp': timestamp.isoformat(),\n                'metric_name': metric_name,\n                'value': value,\n                'tags': tags or {},\n                'metric_id': f\"{metric_type}_{uuid.uuid4().hex[:8]}\"\n            }\n            \n            if metric_type in self.metrics_store:\n                self.metrics_store[metric_type].append(metric_entry)\n                \n                # Check for alert conditions\n                self._check_metric_alerts(metric_type, metric_name, value, tags)\n                \n                return True\n            else:\n                logger.warning(f\"Unknown metric type: {metric_type}\")\n                return False\n                \n        except Exception as e:\n            logger.error(f\"Failed to record metric {metric_name}: {str(e)}\")\n            return False\n    \n    def record_assessment_performance(self, assessment_type: str, processing_time: float,\n                                    success: bool, system_id: str = None,\n                                    error_details: str = None) -> None:\n        \"\"\"Record assessment performance metrics\"\"\"\n        try:\n            # Update performance tracker\n            self.performance_tracker['total_assessments'] += 1\n            if success:\n                self.performance_tracker['successful_assessments'] += 1\n            else:\n                self.performance_tracker['failed_assessments'] += 1\n            \n            # Record agent processing time\n            self.performance_tracker['agent_processing_times'][assessment_type].append(processing_time)\n            \n            # Record detailed metrics\n            tags = {\n                'assessment_type': assessment_type,\n                'success': str(success)\n            }\n            if system_id:\n                tags['system_id'] = system_id\n            \n            self.record_metric('assessment_metrics', 'processing_time', processing_time, tags)\n            self.record_metric('assessment_metrics', 'success_rate', 100 if success else 0, tags)\n            \n            if not success and error_details:\n                self.record_metric('error_metrics', 'assessment_error', 1, {\n                    **tags,\n                    'error_type': error_details[:100]  # Limit error message length\n                })\n                \n        except Exception as e:\n            logger.error(f\"Failed to record assessment performance: {str(e)}\")\n    \n    def record_api_performance(self, endpoint: str, response_time: float,\n                             status_code: int, user_id: str = None) -> None:\n        \"\"\"Record API endpoint performance\"\"\"\n        try:\n            self.performance_tracker['api_response_times'].append(response_time)\n            \n            tags = {\n                'endpoint': endpoint,\n                'status_code': str(status_code),\n                'success': str(200 <= status_code < 300)\n            }\n            if user_id:\n                tags['user_id'] = user_id\n            \n            self.record_metric('system_performance', 'api_response_time', response_time, tags)\n            \n            # Check for slow API responses\n            if response_time > self.monitoring_config['performance_threshold_seconds']:\n                self._generate_alert(\n                    alert_type='high_response_time',\n                    severity='warning',\n                    message=f\"Slow API response: {endpoint} took {response_time:.2f}s\",\n                    details={'endpoint': endpoint, 'response_time': response_time}\n                )\n                \n        except Exception as e:\n            logger.error(f\"Failed to record API performance: {str(e)}\")\n    \n    def record_compliance_metric(self, system_id: str, compliance_score: float,\n                               framework: str, status: str) -> None:\n        \"\"\"Record compliance-related metrics\"\"\"\n        try:\n            tags = {\n                'system_id': system_id,\n                'framework': framework,\n                'status': status\n            }\n            \n            self.record_metric('compliance_metrics', 'compliance_score', compliance_score, tags)\n            \n            # Check compliance score thresholds\n            if compliance_score < self.alert_rules['low_compliance_score']['threshold']:\n                self._generate_alert(\n                    alert_type='low_compliance_score',\n                    severity='error',\n                    message=f\"Low compliance score for system {system_id}: {compliance_score}%\",\n                    details={\n                        'system_id': system_id,\n                        'compliance_score': compliance_score,\n                        'framework': framework\n                    }\n                )\n                \n        except Exception as e:\n            logger.error(f\"Failed to record compliance metric: {str(e)}\")\n    \n    def record_bias_metric(self, system_id: str, bias_score: float, bias_level: str,\n                         protected_groups_affected: List[str] = None) -> None:\n        \"\"\"Record bias and fairness metrics\"\"\"\n        try:\n            tags = {\n                'system_id': system_id,\n                'bias_level': bias_level\n            }\n            \n            self.record_metric('compliance_metrics', 'bias_score', bias_score, tags)\n            \n            if protected_groups_affected:\n                for group in protected_groups_affected:\n                    self.record_metric('compliance_metrics', 'protected_group_impact', 1, {\n                        **tags,\n                        'protected_group': group\n                    })\n            \n            # Check bias score thresholds\n            if bias_score > self.alert_rules['high_bias_risk']['threshold']:\n                self._generate_alert(\n                    alert_type='high_bias_risk',\n                    severity='warning',\n                    message=f\"High bias risk detected for system {system_id}: {bias_score}/10\",\n                    details={\n                        'system_id': system_id,\n                        'bias_score': bias_score,\n                        'bias_level': bias_level,\n                        'protected_groups_affected': protected_groups_affected or []\n                    }\n                )\n                \n        except Exception as e:\n            logger.error(f\"Failed to record bias metric: {str(e)}\")\n    \n    def record_user_activity(self, user_id: str, activity_type: str, \n                           system_id: str = None, session_duration: float = None) -> None:\n        \"\"\"Record user activity metrics\"\"\"\n        try:\n            tags = {\n                'user_id': user_id,\n                'activity_type': activity_type\n            }\n            if system_id:\n                tags['system_id'] = system_id\n            \n            self.record_metric('user_activity', 'activity_count', 1, tags)\n            \n            if session_duration:\n                self.record_metric('user_activity', 'session_duration', session_duration, tags)\n                \n        except Exception as e:\n            logger.error(f\"Failed to record user activity: {str(e)}\")\n    \n    def update_concurrent_sessions(self, delta: int) -> None:\n        \"\"\"Update concurrent session count\"\"\"\n        try:\n            self.performance_tracker['concurrent_sessions'] = max(0, \n                self.performance_tracker['concurrent_sessions'] + delta)\n            \n            self.record_metric('system_performance', 'concurrent_sessions', \n                             self.performance_tracker['concurrent_sessions'])\n                             \n        except Exception as e:\n            logger.error(f\"Failed to update concurrent sessions: {str(e)}\")\n    \n    def _check_metric_alerts(self, metric_type: str, metric_name: str, value: Any,\n                           tags: Dict[str, str] = None) -> None:\n        \"\"\"Check metric against alert rules\"\"\"\n        try:\n            # Error rate monitoring\n            if metric_name == 'assessment_error':\n                recent_errors = self._count_recent_metrics('error_metrics', 'assessment_error', minutes=60)\n                recent_total = self._count_recent_metrics('assessment_metrics', 'processing_time', minutes=60)\n                \n                if recent_total > 0:\n                    error_rate = (recent_errors / recent_total) * 100\n                    if error_rate > self.alert_rules['assessment_failure_rate']['threshold']:\n                        self._generate_alert(\n                            alert_type='assessment_failure_rate',\n                            severity='warning',\n                            message=f\"High assessment failure rate: {error_rate:.1f}%\",\n                            details={'error_rate': error_rate, 'period': '1 hour'}\n                        )\n            \n            # Resource usage monitoring\n            if metric_name == 'api_response_time':\n                recent_times = self._get_recent_metric_values('system_performance', \n                                                            'api_response_time', minutes=15)\n                if recent_times and len(recent_times) > 10:\n                    avg_response_time = statistics.mean(recent_times)\n                    if avg_response_time > self.monitoring_config['performance_threshold_seconds']:\n                        self._generate_alert(\n                            alert_type='high_response_time',\n                            severity='warning',\n                            message=f\"High average response time: {avg_response_time:.2f}s\",\n                            details={'average_response_time': avg_response_time, 'period': '15 minutes'}\n                        )\n                        \n        except Exception as e:\n            logger.error(f\"Failed to check metric alerts: {str(e)}\")\n    \n    def _count_recent_metrics(self, metric_type: str, metric_name: str, minutes: int = 60) -> int:\n        \"\"\"Count recent occurrences of a specific metric\"\"\"\n        try:\n            cutoff_time = datetime.now() - timedelta(minutes=minutes)\n            count = 0\n            \n            if metric_type in self.metrics_store:\n                for metric in self.metrics_store[metric_type]:\n                    if (metric['metric_name'] == metric_name and\n                        datetime.fromisoformat(metric['timestamp']) > cutoff_time):\n                        count += 1\n            \n            return count\n            \n        except Exception as e:\n            logger.error(f\"Failed to count recent metrics: {str(e)}\")\n            return 0\n    \n    def _get_recent_metric_values(self, metric_type: str, metric_name: str, \n                                minutes: int = 60) -> List[float]:\n        \"\"\"Get recent values for a specific metric\"\"\"\n        try:\n            cutoff_time = datetime.now() - timedelta(minutes=minutes)\n            values = []\n            \n            if metric_type in self.metrics_store:\n                for metric in self.metrics_store[metric_type]:\n                    if (metric['metric_name'] == metric_name and\n                        datetime.fromisoformat(metric['timestamp']) > cutoff_time):\n                        if isinstance(metric['value'], (int, float)):\n                            values.append(float(metric['value']))\n            \n            return values\n            \n        except Exception as e:\n            logger.error(f\"Failed to get recent metric values: {str(e)}\")\n            return []\n    \n    def _generate_alert(self, alert_type: str, severity: str, message: str,\n                       details: Dict = None, system_id: str = None) -> None:\n        \"\"\"Generate monitoring alert\"\"\"\n        try:\n            alert_id = f\"alert_{uuid.uuid4().hex[:8]}\"\n            \n            alert = {\n                'alert_id': alert_id,\n                'alert_type': alert_type,\n                'severity': severity,\n                'message': message,\n                'details': details or {},\n                'system_id': system_id,\n                'timestamp': datetime.now().isoformat(),\n                'status': 'active',\n                'acknowledged': False\n            }\n            \n            # Store active alert\n            self.active_alerts[alert_id] = alert\n            self.alert_history.append(alert)\n            \n            # Log alert\n            logger.warning(f\"Governance Alert [{severity.upper()}]: {message}\")\n            \n            # In production, send to alerting system (email, Slack, etc.)\n            self._send_alert_notification(alert)\n            \n        except Exception as e:\n            logger.error(f\"Failed to generate alert: {str(e)}\")\n    \n    def _send_alert_notification(self, alert: Dict) -> None:\n        \"\"\"Send alert notification (placeholder for integration)\"\"\"\n        try:\n            # In production, integrate with notification systems\n            # For now, just log the alert\n            if alert['severity'] in ['error', 'critical']:\n                logger.error(f\"CRITICAL ALERT: {alert['message']}\")\n            elif alert['severity'] == 'warning':\n                logger.warning(f\"WARNING ALERT: {alert['message']}\")\n            else:\n                logger.info(f\"INFO ALERT: {alert['message']}\")\n                \n        except Exception as e:\n            logger.error(f\"Failed to send alert notification: {str(e)}\")\n    \n    def get_governance_dashboard_data(self) -> Dict[str, Any]:\n        \"\"\"Get comprehensive governance dashboard data\"\"\"\n        try:\n            # Check cache first\n            if (self.dashboard_cache['last_updated'] and \n                (datetime.now() - datetime.fromisoformat(\n                    self.dashboard_cache['last_updated'])).total_seconds() < \n                self.dashboard_cache['cache_duration_seconds']):\n                return self.dashboard_cache['data']\n            \n            # Generate fresh dashboard data\n            dashboard_data = {\n                'system_overview': self._get_system_overview_metrics(),\n                'performance_metrics': self._get_performance_metrics(),\n                'compliance_metrics': self._get_compliance_dashboard_metrics(),\n                'security_metrics': self._get_security_dashboard_metrics(),\n                'alert_summary': self._get_alert_summary(),\n                'system_health': self.system_health.copy(),\n                'trends': self._get_governance_trends(),\n                'last_updated': datetime.now().isoformat()\n            }\n            \n            # Update cache\n            self.dashboard_cache['data'] = dashboard_data\n            self.dashboard_cache['last_updated'] = datetime.now().isoformat()\n            \n            return dashboard_data\n            \n        except Exception as e:\n            logger.error(f\"Failed to get governance dashboard data: {str(e)}\")\n            return {'error': 'Failed to retrieve dashboard data'}\n    \n    def _get_system_overview_metrics(self) -> Dict[str, Any]:\n        \"\"\"Get system overview metrics for dashboard\"\"\"\n        try:\n            current_time = datetime.now()\n            hour_ago = current_time - timedelta(hours=1)\n            day_ago = current_time - timedelta(days=1)\n            \n            return {\n                'total_assessments': self.performance_tracker['total_assessments'],\n                'successful_assessments': self.performance_tracker['successful_assessments'],\n                'failed_assessments': self.performance_tracker['failed_assessments'],\n                'success_rate': (\n                    (self.performance_tracker['successful_assessments'] / \n                     max(1, self.performance_tracker['total_assessments'])) * 100\n                ),\n                'concurrent_sessions': self.performance_tracker['concurrent_sessions'],\n                'assessments_last_hour': self._count_recent_metrics(\n                    'assessment_metrics', 'processing_time', minutes=60\n                ),\n                'assessments_last_24h': self._count_recent_metrics(\n                    'assessment_metrics', 'processing_time', minutes=1440\n                )\n            }\n            \n        except Exception as e:\n            logger.error(f\"Failed to get system overview metrics: {str(e)}\")\n            return {}\n    \n    def _get_performance_metrics(self) -> Dict[str, Any]:\n        \"\"\"Get performance metrics for dashboard\"\"\"\n        try:\n            api_times = list(self.performance_tracker['api_response_times'])\n            \n            performance_data = {\n                'average_api_response_time': statistics.mean(api_times) if api_times else 0,\n                'median_api_response_time': statistics.median(api_times) if api_times else 0,\n                'p95_api_response_time': (\n                    statistics.quantiles(api_times, n=20)[18] if len(api_times) > 20 else 0\n                ),\n                'agent_performance': {}\n            }\n            \n            # Agent-specific performance\n            for agent_type, times in self.performance_tracker['agent_processing_times'].items():\n                if times:\n                    performance_data['agent_performance'][agent_type] = {\n                        'average_time': statistics.mean(times),\n                        'median_time': statistics.median(times),\n                        'total_runs': len(times)\n                    }\n            \n            return performance_data\n            \n        except Exception as e:\n            logger.error(f\"Failed to get performance metrics: {str(e)}\")\n            return {}\n    \n    def _get_compliance_dashboard_metrics(self) -> Dict[str, Any]:\n        \"\"\"Get compliance metrics for dashboard\"\"\"\n        try:\n            # Get recent compliance scores\n            recent_compliance = self._get_recent_metric_values(\n                'compliance_metrics', 'compliance_score', minutes=1440  # 24 hours\n            )\n            \n            # Get recent bias scores\n            recent_bias = self._get_recent_metric_values(\n                'compliance_metrics', 'bias_score', minutes=1440\n            )\n            \n            return {\n                'average_compliance_score': (\n                    statistics.mean(recent_compliance) if recent_compliance else 0\n                ),\n                'compliance_assessments_24h': len(recent_compliance),\n                'low_compliance_systems': len([\n                    score for score in recent_compliance \n                    if score < self.alert_rules['low_compliance_score']['threshold']\n                ]),\n                'average_bias_score': statistics.mean(recent_bias) if recent_bias else 0,\n                'high_bias_risk_systems': len([\n                    score for score in recent_bias \n                    if score > self.alert_rules['high_bias_risk']['threshold']\n                ]),\n                'bias_assessments_24h': len(recent_bias)\n            }\n            \n        except Exception as e:\n            logger.error(f\"Failed to get compliance dashboard metrics: {str(e)}\")\n            return {}\n    \n    def _get_security_dashboard_metrics(self) -> Dict[str, Any]:\n        \"\"\"Get security metrics for dashboard\"\"\"\n        try:\n            # Count recent security events\n            security_events_24h = self._count_recent_metrics(\n                'security_metrics', 'security_event', minutes=1440\n            )\n            \n            failed_access_24h = self._count_recent_metrics(\n                'security_metrics', 'access_denied', minutes=1440\n            )\n            \n            return {\n                'security_events_24h': security_events_24h,\n                'failed_access_attempts_24h': failed_access_24h,\n                'active_sessions': self.performance_tracker['concurrent_sessions'],\n                'security_alert_level': self._calculate_security_alert_level()\n            }\n            \n        except Exception as e:\n            logger.error(f\"Failed to get security dashboard metrics: {str(e)}\")\n            return {}\n    \n    def _get_alert_summary(self) -> Dict[str, Any]:\n        \"\"\"Get alert summary for dashboard\"\"\"\n        try:\n            active_alerts = list(self.active_alerts.values())\n            \n            alert_counts = {\n                'critical': len([a for a in active_alerts if a['severity'] == 'critical']),\n                'error': len([a for a in active_alerts if a['severity'] == 'error']),\n                'warning': len([a for a in active_alerts if a['severity'] == 'warning']),\n                'info': len([a for a in active_alerts if a['severity'] == 'info'])\n            }\n            \n            return {\n                'total_active_alerts': len(active_alerts),\n                'alert_counts_by_severity': alert_counts,\n                'recent_alerts': list(self.alert_history)[-10:],  # Last 10 alerts\n                'alert_trends': self._calculate_alert_trends()\n            }\n            \n        except Exception as e:\n            logger.error(f\"Failed to get alert summary: {str(e)}\")\n            return {}\n    \n    def _get_governance_trends(self) -> Dict[str, Any]:\n        \"\"\"Get governance trends analysis\"\"\"\n        try:\n            # Calculate trends over time periods\n            trends = {\n                'compliance_trend': self._calculate_metric_trend(\n                    'compliance_metrics', 'compliance_score', days=7\n                ),\n                'bias_trend': self._calculate_metric_trend(\n                    'compliance_metrics', 'bias_score', days=7\n                ),\n                'performance_trend': self._calculate_metric_trend(\n                    'system_performance', 'api_response_time', days=7\n                ),\n                'assessment_volume_trend': self._calculate_metric_trend(\n                    'assessment_metrics', 'processing_time', days=7, count_only=True\n                )\n            }\n            \n            return trends\n            \n        except Exception as e:\n            logger.error(f\"Failed to get governance trends: {str(e)}\")\n            return {}\n    \n    def _calculate_metric_trend(self, metric_type: str, metric_name: str, \n                              days: int = 7, count_only: bool = False) -> Dict[str, Any]:\n        \"\"\"Calculate trend for a specific metric over time period\"\"\"\n        try:\n            cutoff_time = datetime.now() - timedelta(days=days)\n            \n            if count_only:\n                # Count occurrences over time\n                daily_counts = defaultdict(int)\n                \n                if metric_type in self.metrics_store:\n                    for metric in self.metrics_store[metric_type]:\n                        if (metric['metric_name'] == metric_name and\n                            datetime.fromisoformat(metric['timestamp']) > cutoff_time):\n                            day_key = datetime.fromisoformat(metric['timestamp']).date().isoformat()\n                            daily_counts[day_key] += 1\n                \n                values = list(daily_counts.values())\n            else:\n                # Get actual values over time\n                values = self._get_recent_metric_values(metric_type, metric_name, minutes=days*1440)\n            \n            if len(values) < 2:\n                return {'trend': 'insufficient_data', 'direction': 'stable'}\n            \n            # Calculate simple trend (comparing first half with second half)\n            mid_point = len(values) // 2\n            first_half_avg = statistics.mean(values[:mid_point]) if values[:mid_point] else 0\n            second_half_avg = statistics.mean(values[mid_point:]) if values[mid_point:] else 0\n            \n            if second_half_avg > first_half_avg * 1.1:  # 10% increase\n                direction = 'increasing'\n            elif second_half_avg < first_half_avg * 0.9:  # 10% decrease\n                direction = 'decreasing'\n            else:\n                direction = 'stable'\n            \n            return {\n                'trend': 'available',\n                'direction': direction,\n                'first_period_avg': first_half_avg,\n                'second_period_avg': second_half_avg,\n                'change_percentage': (\n                    ((second_half_avg - first_half_avg) / max(first_half_avg, 0.01)) * 100\n                )\n            }\n            \n        except Exception as e:\n            logger.error(f\"Failed to calculate trend for {metric_name}: {str(e)}\")\n            return {'trend': 'error', 'direction': 'unknown'}\n    \n    def _calculate_security_alert_level(self) -> str:\n        \"\"\"Calculate overall security alert level\"\"\"\n        try:\n            active_security_alerts = [\n                alert for alert in self.active_alerts.values()\n                if 'security' in alert.get('alert_type', '').lower()\n            ]\n            \n            if any(alert['severity'] == 'critical' for alert in active_security_alerts):\n                return 'critical'\n            elif any(alert['severity'] == 'error' for alert in active_security_alerts):\n                return 'high'\n            elif any(alert['severity'] == 'warning' for alert in active_security_alerts):\n                return 'medium'\n            else:\n                return 'low'\n                \n        except Exception as e:\n            logger.error(f\"Failed to calculate security alert level: {str(e)}\")\n            return 'unknown'\n    \n    def _calculate_alert_trends(self) -> Dict[str, Any]:\n        \"\"\"Calculate alert trends over time\"\"\"\n        try:\n            recent_alerts = [\n                alert for alert in self.alert_history \n                if datetime.fromisoformat(alert['timestamp']) > \n                   (datetime.now() - timedelta(days=7))\n            ]\n            \n            if len(recent_alerts) < 2:\n                return {'trend': 'insufficient_data'}\n            \n            # Group alerts by day\n            daily_alerts = defaultdict(int)\n            for alert in recent_alerts:\n                day_key = datetime.fromisoformat(alert['timestamp']).date().isoformat()\n                daily_alerts[day_key] += 1\n            \n            daily_counts = list(daily_alerts.values())\n            \n            if len(daily_counts) >= 2:\n                recent_avg = statistics.mean(daily_counts[-3:])  # Last 3 days\n                earlier_avg = statistics.mean(daily_counts[:-3])  # Earlier days\n                \n                if recent_avg > earlier_avg * 1.2:  # 20% increase\n                    trend = 'increasing'\n                elif recent_avg < earlier_avg * 0.8:  # 20% decrease\n                    trend = 'decreasing'\n                else:\n                    trend = 'stable'\n            else:\n                trend = 'stable'\n            \n            return {\n                'trend': trend,\n                'total_alerts_7d': len(recent_alerts),\n                'average_daily_alerts': statistics.mean(daily_counts) if daily_counts else 0\n            }\n            \n        except Exception as e:\n            logger.error(f\"Failed to calculate alert trends: {str(e)}\")\n            return {'trend': 'error'}\n    \n    def get_governance_metrics(self) -> Dict[str, Any]:\n        \"\"\"Get comprehensive governance metrics for reporting\"\"\"\n        try:\n            return {\n                'system_performance': {\n                    'total_assessments': self.performance_tracker['total_assessments'],\n                    'success_rate': (\n                        (self.performance_tracker['successful_assessments'] / \n                         max(1, self.performance_tracker['total_assessments'])) * 100\n                    ),\n                    'average_response_time': (\n                        statistics.mean(self.performance_tracker['api_response_times'])\n                        if self.performance_tracker['api_response_times'] else 0\n                    ),\n                    'concurrent_sessions': self.performance_tracker['concurrent_sessions']\n                },\n                'compliance_performance': {\n                    'compliance_assessments': self._count_recent_metrics(\n                        'compliance_metrics', 'compliance_score', minutes=1440\n                    ),\n                    'bias_assessments': self._count_recent_metrics(\n                        'compliance_metrics', 'bias_score', minutes=1440\n                    ),\n                    'average_compliance_score': (\n                        statistics.mean(self._get_recent_metric_values(\n                            'compliance_metrics', 'compliance_score', minutes=1440\n                        )) if self._get_recent_metric_values(\n                            'compliance_metrics', 'compliance_score', minutes=1440\n                        ) else 0\n                    )\n                },\n                'alert_metrics': {\n                    'total_active_alerts': len(self.active_alerts),\n                    'critical_alerts': len([\n                        a for a in self.active_alerts.values() if a['severity'] == 'critical'\n                    ]),\n                    'alerts_24h': len([\n                        a for a in self.alert_history \n                        if datetime.fromisoformat(a['timestamp']) > \n                           (datetime.now() - timedelta(days=1))\n                    ])\n                },\n                'system_health': self.system_health,\n                'generated_at': datetime.now().isoformat()\n            }\n            \n        except Exception as e:\n            logger.error(f\"Failed to get governance metrics: {str(e)}\")\n            return {'error': 'Failed to retrieve metrics'}\n    \n    def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:\n        \"\"\"Acknowledge an active alert\"\"\"\n        try:\n            if alert_id in self.active_alerts:\n                self.active_alerts[alert_id]['acknowledged'] = True\n                self.active_alerts[alert_id]['acknowledged_by'] = acknowledged_by\n                self.active_alerts[alert_id]['acknowledged_at'] = datetime.now().isoformat()\n                \n                logger.info(f\"Alert {alert_id} acknowledged by {acknowledged_by}\")\n                return True\n            else:\n                logger.warning(f\"Alert {alert_id} not found\")\n                return False\n                \n        except Exception as e:\n            logger.error(f\"Failed to acknowledge alert {alert_id}: {str(e)}\")\n            return False\n    \n    def resolve_alert(self, alert_id: str, resolved_by: str, resolution_notes: str = '') -> bool:\n        \"\"\"Resolve an active alert\"\"\"\n        try:\n            if alert_id in self.active_alerts:\n                self.active_alerts[alert_id]['status'] = 'resolved'\n                self.active_alerts[alert_id]['resolved_by'] = resolved_by\n                self.active_alerts[alert_id]['resolved_at'] = datetime.now().isoformat()\n                self.active_alerts[alert_id]['resolution_notes'] = resolution_notes\n                \n                # Move to resolved alerts (remove from active)\n                resolved_alert = self.active_alerts.pop(alert_id)\n                \n                logger.info(f\"Alert {alert_id} resolved by {resolved_by}\")\n                return True\n            else:\n                logger.warning(f\"Alert {alert_id} not found\")\n                return False\n                \n        except Exception as e:\n            logger.error(f\"Failed to resolve alert {alert_id}: {str(e)}\")\n            return False\n    \n    def update_system_health(self, component: str, status: str, details: str = '') -> None:\n        \"\"\"Update system component health status\"\"\"\n        try:\n            if component in self.system_health['components']:\n                self.system_health['components'][component] = status\n                self.system_health['last_health_check'] = datetime.now().isoformat()\n                \n                # Update overall status\n                component_statuses = list(self.system_health['components'].values())\n                if 'unhealthy' in component_statuses:\n                    self.system_health['overall_status'] = 'unhealthy'\n                elif 'degraded' in component_statuses:\n                    self.system_health['overall_status'] = 'degraded'\n                else:\n                    self.system_health['overall_status'] = 'healthy'\n                \n                # Generate alert for unhealthy components\n                if status in ['unhealthy', 'degraded']:\n                    self._generate_alert(\n                        alert_type='component_health',\n                        severity='error' if status == 'unhealthy' else 'warning',\n                        message=f\"Component {component} is {status}\",\n                        details={'component': component, 'status': status, 'details': details}\n                    )\n                    \n        except Exception as e:\n            logger.error(f\"Failed to update system health for {component}: {str(e)}\")\n    \n    def health_check(self) -> bool:\n        \"\"\"Check if monitoring system is functioning properly\"\"\"\n        try:\n            # Test metric recording\n            test_metric_recorded = self.record_metric(\n                'system_performance', 'health_check_test', 1.0, {'test': 'true'}\n            )\n            \n            # Test dashboard data generation\n            dashboard_data = self.get_governance_dashboard_data()\n            \n            return test_metric_recorded and 'system_overview' in dashboard_data\n            \n        except Exception as e:\n            logger.error(f\"Monitoring health check failed: {str(e)}\")\n            return False"