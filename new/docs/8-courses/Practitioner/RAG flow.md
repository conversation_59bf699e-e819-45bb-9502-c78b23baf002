```mermaid
graph TB
    Start[User Query] --> QA[Query Agent]
    QA --> QR{Rewrite Query?}
    QR -->|Yes| Hyde[Generate HyDE]
    QR -->|Yes| Multi[Create Multi-Query]
    QR -->|No| Direct[Use Original]
    
    Hyde --> Retrieve
    Multi --> Retrieve
    Direct --> Retrieve
    
    subgraph "Retrieval Sources"
        Retrieve[Select Sources] --> Vec[Vector DB]
        Retrieve --> Graph[Knowledge Graph]
        Retrieve --> SQL[SQL/APIs]
        Retrieve --> Web[Web Search]
    end
    
    Vec --> Pool[Document Pool]
    Graph --> Pool
    SQL --> Pool
    Web --> Pool
    
    Pool --> Rank[Ranking Agent<br/>Cross-Encoder]
    Rank --> Compress[Compress Context]
    Compress --> Gen[Generation Agent]
    
    Gen --> Eval{Is Answer<br/>Relevant?}
    Eval -->|Yes| Citations[Add Citations]
    Citations --> Final[Final Response]
    
    Eval -->|No| Need{Need More<br/>Details?}
    Need -->|Yes| QA
    Need -->|No| Fallback[Try Different Source]
    Fallback --> Retrieve
    
    style QA fill:#ff9800
    style Rank fill:#ff9800  
    style Gen fill:#ff9800
    style Eval fill:#f44336
    style Need fill:#f44336
    style Final fill:#4caf50
    
```
