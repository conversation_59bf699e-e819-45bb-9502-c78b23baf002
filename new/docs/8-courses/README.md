# Modern AI Pro Course Assets

This directory contains all organized course content for Modern AI Pro programs.

## 📁 Directory Structure

```
courses/
├── Modern_AI_Pro_Course_Navigator.md          # Master course mapping across all programs
├── Modern_AI_Pro_Master_Study_Guide.md       # 100-day comprehensive study plan
├── Modern_AI_Pro_Study_Plan.docx            # Original Google Doc study plan
├── organized_content/
│   ├── downloaded_notebooks/                  # All Colab notebooks (51+ files)
│   │   ├── 14I39Cn..._First_LLM_Call_on_Colab.ipynb
│   │   ├── 1q14co..._First_chatbot.ipynb
│   │   ├── 1nIQbi..._LLM_with_memory.ipynb
│   │   ├── 16fm4M..._LLM_with_tools.ipynb
│   │   ├── 1IK5Jv..._First_agent_with_the_ReAct_Framework.ipynb
│   │   ├── 1ACt6T..._Agent_with_CrewAI.ipynb
│   │   ├── 15UuvE..._Agents_with_Langgraph.ipynb
│   │   ├── 18ZyyC..._SQLRAG.ipynb
│   │   ├── 15ewza..._Advanced_RAG_for_Finance_Domain.ipynb
│   │   ├── 1cQr0s..._Vector_Similarity_Search.ipynb
│   │   ├── 1npEnR..._Agentic_RAG_1.ipynb
│   │   ├── [Machine Learning Foundations]
│   │   │   ├── 1mL6nG..._Intro_Course.ipynb
│   │   │   ├── 1LpynadI..._Basic_Linear_Regression.ipynb
│   │   │   ├── 1CEgw0..._Home_Price_Prediction.ipynb
│   │   │   ├── 1KdOjC..._Income_Class_Prediction.ipynb
│   │   │   └── [Additional ML notebooks...]
│   │   └── [Course Structure Documents]
│   │       ├── 1sIGCk..._Essentials_India_July_2025.ipynb
│   │       ├── 1ByYYo..._Agentic_AI_North_America_May_2025.ipynb
│   │       └── Earlier_Practitioner_Notebook.ipynb
│   └── metadata/
│       └── practitioner_aug_2025_metadata.json
└── [Original Course Files]
    ├── Modern_AI_Pro_Practitioner_(India)_Aug_2025.ipynb
    └── Python for the Executive Mind.pdf
```

## 📚 Course Programs

### 1. **AI Essentials** (Business Leaders)
- **Target**: Executives, Managers, Business Leaders
- **Duration**: ~5 hours (7 core + 2 optional notebooks)
- **Focus**: AI understanding and business applications

### 2. **AI Practitioner** (Technical Implementation)
- **Target**: Engineers, Data Scientists, Technical Managers
- **Duration**: ~20 hours (27 notebooks across 3 days)
- **Focus**: Build and deploy AI applications

### 3. **Agentic AI** (Autonomous Systems)
- **Target**: Senior Engineers, AI Researchers
- **Duration**: ~17 hours (16 core notebooks)
- **Focus**: Master autonomous AI agents and workflows

### 4. **Vibe Coding** (AI-Assisted Development)
- **Target**: Software Engineers, Full-stack Developers
- **Duration**: ~6 hours (8 notebooks)
- **Focus**: Integrate AI into development workflows

## 🎯 Quick Access

### **Navigation Files**
- [`Modern_AI_Pro_Course_Navigator.md`](./Modern_AI_Pro_Course_Navigator.md) - Complete notebook mapping and course selection guide
- [`Modern_AI_Pro_Master_Study_Guide.md`](./Modern_AI_Pro_Master_Study_Guide.md) - 100-day structured learning plan

### **Notebook Categories**

#### **Generative AI & LLMs**
- First LLM Call (`14I39Cn...`)
- First Chatbot (`1q14co...`)
- LLM with Memory (`1nIQbi...`)
- Local LLM (`1bz8eD...`)

#### **AI Agents**
- LLM with Tools (`16fm4M...`)
- ReAct Framework (`1IK5Jv...`)
- CrewAI Agents (`1ACt6T...`)
- LangGraph (`15UuvE...`)

#### **RAG & Document AI**
- Q&A on Documents (`1icYUU...`)
- SQLRAG (`18ZyyC...`)
- Advanced RAG (`15ewza...`)
- Vector Search (`1cQr0s...`)

#### **Machine Learning Foundations**
- Intro Course (`1mL6nG...`)
- Linear Regression (`1LpynadI...`)
- Home Price Prediction (`1CEgw0...`)
- Income Classification (`1KdOjC...`)

## 📊 Content Statistics

- **Total Notebooks**: 51+ practical exercises
- **Course Programs**: 4 different learning tracks
- **Learning Hours**: 174+ hours of structured content
- **Skill Levels**: Beginner to Advanced
- **Languages**: Python, SQL, Natural Language

## 🔄 Content Updates

This content is regularly updated as new course materials are discovered and organized. The extraction and organization process is automated using scripts in `/scripts/gdocs_colab/`.

## 🚀 Getting Started

1. **Choose Your Path**: Use the [Course Navigator](./Modern_AI_Pro_Course_Navigator.md) to select appropriate course
2. **Follow the Plan**: Use the [Master Study Guide](./Modern_AI_Pro_Master_Study_Guide.md) for structured learning
3. **Access Notebooks**: All notebooks are organized in `organized_content/downloaded_notebooks/`
4. **Track Progress**: Follow week-by-week milestones in the study guide

---

*Last Updated: August 2025*  
*Content organized from Modern AI Pro courses by Balaji Viswanathan*