{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyNslyjw9MtPKRrxMRQMfgZQ"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "GJTj5x97Qa_I", "executionInfo": {"status": "ok", "timestamp": 1754051252537, "user_tz": -330, "elapsed": 12284, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "8c4009c5-cdd7-4942-d368-438b5ed5e53d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[?25l   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/131.1 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m131.1/131.1 kB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip install -U -q  langchain-groq"]}, {"cell_type": "code", "source": ["from langchain_groq import ChatGroq\n", "from google.colab import userdata\n", "llm_groq = ChatGroq(model_name=\"deepseek-r1-distill-llama-70b\", api_key=userdata.get(\"GROQ_API_KEY\"), temperature=0.7)"], "metadata": {"id": "KpHlb2CrQ0WM", "executionInfo": {"status": "ok", "timestamp": 1754051302070, "user_tz": -330, "elapsed": 3970, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["ai_mesg = llm_groq.invoke(\"When was shah rukh khan born\")\n", "print(ai_mesg.content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sk1FmeoOJqU7", "executionInfo": {"status": "ok", "timestamp": 1754051538111, "user_tz": -330, "elapsed": 3689, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "c7c118e4-c48d-4817-cc48-fd2d732d5c22"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<think>\n", "Alright, so I need to figure out when <PERSON> was born. I'm not entirely sure, but I think he's a pretty famous actor, maybe from Bollywood? I've heard his name before, but I don't remember the exact details. Let me try to recall or think through this.\n", "\n", "First, I know that <PERSON> is often called the \"King of Bollywood,\" which suggests he's been active for a while. Bollywood movies are from India, right? So he's an Indian actor. I think he's been in a lot of movies, maybe some that are really popular internationally as well.\n", "\n", "I'm trying to remember if I've seen any of his movies. I think one of his famous ones is \"Karan Arjun\" or maybe \"Kuch Kuch Hota Hai.\" Those sound familiar. I'm not exactly sure when those came out, but I think they were in the 90s. So if he was already an established actor in the 90s, he must have been born a few decades before that.\n", "\n", "Wait, another movie I've heard of is \"Dev<PERSON>.\" I think that was a big hit. I'm not sure when it was released, but I can look that up in my mind. I believe it was around 2002. So if he was starring in movies in the early 2000s, he was probably in his 30s or 40s then. That would mean he was born maybe in the late 60s or early 70s.\n", "\n", "I also remember hearing that he's one of the most popular stars, so he's likely been around for a while. Maybe he was born in the 60s. Let me think of other Bollywood actors I know. For example, <PERSON><PERSON><PERSON><PERSON> is a really famous one, and I think he was born in the early 40s, like 1942 or something. So <PERSON> is probably younger than that, maybe born in the 60s.\n", "\n", "I think I've heard his birthday is in November. Maybe the 2nd? So November 2nd, 1965? That sounds familiar. Let me see if that makes sense. If he was born in 1965, then in 1995, he would be 30, which seems about right for someone who started acting in the early 90s. Yeah, that seems plausible.\n", "\n", "Wait, another thought: I think he attended some university in Delhi. Maybe Hansraj College or something like that. So if he was born in the early 60s, he would have been in college in the late 80s, which fits with starting his acting career in the early 90s.\n", "\n", "I also remember that he's married to <PERSON><PERSON><PERSON>, and they have children together. I think their kids are teenagers now, so if he was born in 1965, he'd be in his late 50s, which makes sense for having teenage children.\n", "\n", "Putting it all together: <PERSON><PERSON>, born November 2nd, 1965. That seems right. I don't think I've heard any other birthdate associated with him, so I'm pretty confident that's correct.\n", "</think>\n", "\n", "<PERSON>, often referred to as the \"King of Bollywood,\" was born on November 2, 1965. This date aligns with his rise to fame in the early 1990s, his notable roles in films like \"Kuch Kuch Hota Hai\" and \"Dev<PERSON>,\" and his personal life, including his marriage and children.\n"]}]}, {"cell_type": "code", "source": ["ai_mesg = llm_groq.invoke(\"Write me a random COBOL code that would work in a dinosaur bank. Answer in 10 sentences\")\n", "print(ai_mesg.content)"], "metadata": {"id": "BF0-EoHaRS4W", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": -330, "elapsed": 2674, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "2ba9657d-29f6-43bf-9cec-3588cbccd2ec"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<think>\n", "Okay, so the user asked me to write a random COBOL code for a dinosaur bank and answer in 10 sentences. Hmm, COBOL is an old language, so I need to make sure the code is simple and fits the dinosaur theme. Let me think about what a bank might need. Maybe an account management system. \n", "\n", "First, I'll start with the necessary headers. I know COBOL programs begin with the IDENTIFICATION DIVISION. Then, the PROGRAM-ID should be something catchy, like \"DinoBank-Manager\". \n", "\n", "Next, the ENVIRONMENT DIVISION is needed to set up the input and output files. I'll include an INPUT-OUTPUT SECTION and assign a file to the user's console using the CONSOLE keyword. \n", "\n", "Moving on to the DATA DIVISION, I'll need to define the working storage for the account balance. Maybe a variable called WS-BALANCE with a PIC of 9(10).0(2) to handle decimal amounts. \n", "\n", "For the PROCEDURE DIVISION, I'll start with a PROGRAM-BEGIN paragraph to initialize the balance. Then, a DISPLAY statement to show the balance. Adding a paragraph for deposits and one for withdrawals makes sense. Each should read an amount, adjust the balance, and display a confirmation. \n", "\n", "Finally, the PROGRAM-DONE paragraph will stop the program. I should make sure the code flows logically and each step is clear. Keeping it within 10 sentences means each paragraph is concise. \n", "\n", "I think this covers a simple bank program with basic functionalities. It should be easy to understand and fit the dinosaur theme by naming it something related. Hopefully, this meets the user's requirements.\n", "</think>\n", "\n", "Here’s a simple COBOL program for a \"Dinosaur Bank\" system. This program manages a dinosaur-themed bank account with basic deposit and withdrawal functionality:\n", "\n", "```\n", "IDENTIFICATION DIVISION.\n", "PROGRAM-<PERSON><PERSON> DinoBank-Manager.\n", "\n", "ENVIRONMENT DIVISION.\n", "INPUT-OUTPUT SECTION.\n", "FILE-CONTROL.\n", "\n", "DATA DIVISION.\n", "WORKING-STORAG<PERSON> SECTION.\n", "01 WS-BALANCE PIC 9(10)0(2).\n", "\n", "PROCEDURE DIVISION.\n", "PROGRAM-BEGIN.\n", "    MOVE 1000.00 TO WS-BALANCE\n", "    DISPLAY \"Welcome to DinoBank! Your current balance is: \", WS-BALANCE\n", "\n", "DEPOSIT-PARAGRAPH.\n", "    DISPLAY \"Enter amount to deposit: \"\n", "    ACCEPT WS-DEPOSIT\n", "    ADD WS-DEPOSIT TO WS-BALANCE\n", "    DISPLAY \"Deposit successful! New balance: \", WS-BALANCE\n", "\n", "WITHDRAW-PARAGRAPH.\n", "    DISPLAY \"Enter amount to withdraw: \"\n", "    ACCEPT WS-WITHDRAW\n", "    SUBTRACT WS-WITHDRAW FROM WS-BALANCE\n", "    DISPLAY \"Withdrawal successful! New balance: \", WS-BALANCE\n", "\n", "PROGRAM-DONE.\n", "    STOP RUN.\n", "```\n", "\n", "This program initializes a balance, allows deposits, and processes withdrawals. It’s a fun, simple example of how COBOL might be used in a dinosaur-themed banking system!\n"]}]}, {"cell_type": "code", "source": ["ai_mesg.response_metadata[\"token_usage\"]\n"], "metadata": {"id": "rOID-IszS2-S", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": -330, "elapsed": 16, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "71cda875-6290-42c8-effb-1ff348008d6e"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'completion_tokens': 617,\n", " 'prompt_tokens': 24,\n", " 'total_tokens': 641,\n", " 'completion_time': 2.*********,\n", " 'prompt_time': 0.*********,\n", " 'queue_time': 0.*********,\n", " 'total_time': 2.*********}"]}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "code", "source": ["ai_mesg = llm_groq.invoke(\"Tell me what I asked earlier\")\n", "print(ai_mesg.content)"], "metadata": {"id": "I21-PEtSLYsb", "executionInfo": {"status": "ok", "timestamp": 1752299992535, "user_tz": -330, "elapsed": 437, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "4924d20b-ea1c-4e82-94b3-533e33865cfe", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<think>\n", "\n", "</think>\n", "\n", "It seems like this is the beginning of our conversation. I'm happy to help with any questions or topics you'd like to discuss!\n"]}]}]}