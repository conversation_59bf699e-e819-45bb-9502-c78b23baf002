{"cells": [{"cell_type": "markdown", "metadata": {"id": "ecXmrS1Be02S"}, "source": ["## Modern AI Pro: Agents with <PERSON><PERSON><PERSON>\n", "This script demonstrates a basic chat agent leveraging the integration of LangChain's LLM capabilities with a search tool.\n", "\n", "The agent is designed to handle conversational queries, such as asking about the weather, by utilizing both a memory component for maintaining conversation context and a search tool for retrieving relevant information.\n", "\n", "Key Components:\n", "- **LangchainMemory**: Initializes and manages the underlying language model, tailored for the Azure OpenAI deployment. The memory buffer allows the agent to retain context across interactions, improving the quality of responses over time.\n", "- **TavilySearchResults**: A search tool that fetches and returns search results, limited to a configurable maximum number of results. In this demo, it is set to return up to 2 results per query.\n", "- **MemorySaver**: A checkpoint component that saves and restores conversation state, ensuring continuity and consistency across different conversation threads.\n", "- **create_react_agent**: A utility that combines the LLM, search tools, and memory checkpointing into a coherent agent that can process user inputs and return intelligent, context-aware responses.\n", "- **Gradio Interface**: Provides a simple chat interface for interacting with the agent, where users can input messages and receive responses in real-time.\n", "\n", "Usage:\n", "- The `chat` function handles streaming responses from the agent. It processes the conversation in chunks to allow for partial responses, yielding the first message content from the agent.\n", "- The `config` object contains a hardcoded thread ID, which is used to manage and switch between different conversation threads.\n", "- The script ends by launching a Gradio interface where users can interact with the agent under the title \"Mitra Robot Buffer Memory Chat\".\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 36512, "status": "ok", "timestamp": 1746998054992, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "1Xi_6-hGesIb", "outputId": "73af16ff-6a7d-408b-acd0-80d85632ca60"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m43.5/43.5 kB\u001b[0m \u001b[31m2.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.5/2.5 MB\u001b[0m \u001b[31m34.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.1/54.1 MB\u001b[0m \u001b[31m16.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m322.9/322.9 kB\u001b[0m \u001b[31m13.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m151.2/151.2 kB\u001b[0m \u001b[31m10.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m95.2/95.2 kB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.5/127.5 kB\u001b[0m \u001b[31m8.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m42.3/42.3 kB\u001b[0m \u001b[31m2.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m47.6/47.6 kB\u001b[0m \u001b[31m3.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.4/44.4 kB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m11.5/11.5 MB\u001b[0m \u001b[31m58.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m72.0/72.0 kB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.5/62.5 kB\u001b[0m \u001b[31m4.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m194.8/194.8 kB\u001b[0m \u001b[31m10.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.9/50.9 kB\u001b[0m \u001b[31m3.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m223.6/223.6 kB\u001b[0m \u001b[31m13.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip install -U -q langchain_groq langchain_community gradio langgraph nasapy\n", "import os\n", "\n", "from langchain_groq import ChatGroq\n", "from google.colab import userdata\n", "llm_groq = ChatGroq(model_name=\"llama-3.3-70b-versatile\", api_key=userdata.get(\"GROQ_API_KEY\"))\n", "os.environ[\"TAVILY_API_KEY\"] = userdata.get(\"TAVILY_API_SEARCH\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"executionInfo": {"elapsed": 752, "status": "ok", "timestamp": 1746998341985, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "Tz63hXIyfXFb"}, "outputs": [], "source": ["from langchain_community.tools.tavily_search import TavilySearchResults\n", "from langchain_core.messages import HumanMessage\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.prebuilt import create_react_agent\n", "from datetime import datetime\n", "from nasapy import Nasa"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"executionInfo": {"elapsed": 12, "status": "ok", "timestamp": 1746998353457, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "Z0wbCEP6fkXo"}, "outputs": [], "source": ["from langchain_core.tools import tool\n", "@tool\n", "def get_hotel(location: str, at_time: datetime)->str:\n", "    '''Returns hotel options for reservation'''\n", "    return '''There is a beautiful Motel 6 available for that date'''\n", "\n", "search = TavilySearchResults(max_results=5)\n", "tools = [search, get_hotel]\n", "\n", "# Create the agent\n", "memory = MemorySaver()\n", "agent_executor = create_react_agent(llm_groq, tools, checkpointer=memory)\n", "config = {\"configurable\": {\"thread_id\": \"id111\"}} # Hardcoded -- this is actually to switch different threads of conversations"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"background_save": true, "base_uri": "https://localhost:8080/", "height": 680}, "id": "HdxYJI7Kef0F"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.11/dist-packages/gradio/chat_interface.py:338: UserWarning: The 'tuples' format for chatbot messages is deprecated and will be removed in a future version of Gradio. Please set type='messages' instead, which uses openai-style 'role' and 'content' keys.\n", "  self.chatbot = Chatbot(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["It looks like you are running Gradio on a hosted a Jupyter notebook. For the Gradio app to work, sharing must be enabled. Automatically setting `share=True` (you can turn this off by setting `share=False` in `launch()` explicitly).\n", "\n", "Colab notebook detected. This cell will run indefinitely so that you can see errors and logs. To turn off, set debug=False in launch().\n", "* Running on public URL: https://a8a7ade37707b1465a.gradio.live\n", "\n", "This share link expires in 1 week. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n"]}, {"data": {"text/html": ["<div><iframe src=\"https://a8a7ade37707b1465a.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gradio as gr\n", "def chat(message, history):\n", "    for chunk in agent_executor.stream(\n", "        {\"messages\": [HumanMessage(content=message)]}, config):\n", "        agent_data = chunk.get(\"agent\", None)\n", "\n", "        if agent_data is not None:\n", "            messages = agent_data.get(\"messages\", None)\n", "\n", "        if messages is not None and len(messages) > 0:\n", "            yield messages[0].content\n", "\n", "# Setup Gradio\n", "demo = gr.<PERSON><PERSON>(\n", "    fn=chat,\n", "    title=\"Mitra Robot Search Engine powered chat\",\n", ")\n", "demo.launch(debug=True)\n", "\n", "# You can ask something like:\n", "# What is today's weather in Boston?\n", "# Give me the local news there.\n", "# How would vibe coding change the industry?"]}], "metadata": {"colab": {"authorship_tag": "ABX9TyO6TWbXW82fo1qEBDV17f4A", "name": "", "version": ""}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}