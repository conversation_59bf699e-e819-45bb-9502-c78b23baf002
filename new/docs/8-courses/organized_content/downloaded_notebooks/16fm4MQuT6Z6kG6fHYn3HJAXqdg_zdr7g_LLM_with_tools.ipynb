{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 19613, "status": "ok", "timestamp": 1732358242396, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 480}, "id": "lmEwrE6KskQw", "outputId": "fef27f2c-efb2-482b-8c2e-13f3cd486dce"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m57.1/57.1 MB\u001b[0m \u001b[31m11.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m320.1/320.1 kB\u001b[0m \u001b[31m22.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m94.9/94.9 kB\u001b[0m \u001b[31m7.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m108.9/108.9 kB\u001b[0m \u001b[31m10.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m11.1/11.1 MB\u001b[0m \u001b[31m93.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m73.2/73.2 kB\u001b[0m \u001b[31m6.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m63.8/63.8 kB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m130.2/130.2 kB\u001b[0m \u001b[31m11.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip install -U -q langchain_groq gradio"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"executionInfo": {"elapsed": 7148, "status": "ok", "timestamp": 1732358249542, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 480}, "id": "OmHdfrFqsvMK"}, "outputs": [], "source": ["from langchain_groq import ChatGroq\n", "from google.colab import userdata\n", "llm_groq = ChatGroq(model_name=\"llama3-70b-8192\", api_key=userdata.get(\"GROQ_API_KEY\"))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"executionInfo": {"elapsed": 186, "status": "ok", "timestamp": 1732358569637, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 480}, "id": "4AdRribGx9We"}, "outputs": [], "source": ["import json\n", "import datetime\n", "def submit_complaint(complaint, name):\n", "    issues = []\n", "    try:\n", "        with open('issues.json', 'r') as f:\n", "            issues = json.load(f)\n", "    except FileNotFoundError:\n", "        pass  # If the file doesn't exist, we'll create it below\n", "\n", "    # Adding the new issue\n", "    issues.append({'name': name, 'issue': complaint, 'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})\n", "\n", "    # Saving the updated issues list\n", "    with open('issues.json', 'w') as f:\n", "        json.dump(issues, f, indent=4)\n", "\n", "    return json.dumps({\"success\": \"Issue submitted successfully.\"})\n", "\n", "def calculate(expression):\n", "    try:\n", "        # Prepare a local namespace to safely execute the expression\n", "        local_namespace = {}\n", "\n", "        # Execute the expression using exec\n", "        exec(expression, {}, local_namespace)\n", "\n", "        # Return the result if it's stored in a specific variable\n", "        return local_namespace.get('result', \"No result found\")\n", "\n", "    except Exception as e:\n", "        return f\"Error in calculation: {e}\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {"executionInfo": {"elapsed": 172, "status": "ok", "timestamp": 1732358731193, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 480}, "id": "iRuknIrys4gx"}, "outputs": [], "source": ["class Chatbot:\n", "    prompt = \"\"\"\n", "    You operate in a structured loop consisting of the following steps: Thought, Action, PAUSE, and Observation. At the end of this loop, you will output an Answer. Follow this process meticulously:\n", "\n", "\t1.\tThought: Use this step to articulate your reasoning about the question or task you’ve been asked to handle. Think through the problem and decide which action to take next.\n", "\t2.\tAction: Choose and execute one of the available actions based on your Thought. Once you initiate an action, return PAUSE to wait for further instructions.\n", "\t3.\tObservation: This will contain the outcome or result of the Action you took. Use this information to inform your next step.\n", "\t4.\tAnswer: After sufficient loops of Thought, Action, PAUSE, and Observation, when the task is completed, provide a final Answer to the user.\n", "\n", "Available Actions:\n", "\n", "\t•\tcalculate:\n", "\t•\tUsage: calculate:\n", "\t•\tParameter: expression — a mathematical expression in Python.\n", "\t•\tFunction: Evaluates the expression and returns the result. Make sure to use floating-point syntax if necessary.\n", "  •\timport math if it uses math functions. make sure the expression returns something like result = math.log(2 * math.sqrt(36))\n", "\t•\tsubmit_complaint:\n", "\t•\tUsage: submit_complaint\n", "\t•\tParameters:\n", "\t•\tcomplaint_text (str) — The content of the complaint.\n", "\t•\tperson_name (str) — The name of the person submitting the complaint.\n", "\t•\tFunction: Submits the complaint. If person_name is missing, ask for the person’s name and return PAUSE.\n", "\n", "Process Guidelines:\n", "\n", "\t•\tIf a user requests to submit a complaint but doesn’t provide their name, ask for their name, then return PAUSE.\n", "\t•\tIf the request or question does not align with any of the available actions, proceed with a normal conversation.\n", "\n", "Example Session:\n", "\n", "Question: Submit a complaint about Microsoft Office 365. My name is <PERSON><PERSON><PERSON>\n", "\n", "Thought: I should submit a complaint about Microsoft Office 365 with the person’s name, <PERSON><PERSON><PERSON>.\n", "\n", "Action: submit_complaint | complaint_text: Microsoft Office 365 | person_name: <PERSON><PERSON><PERSON>\n", "\n", "PAUSE\n", "\n", "On the next call, you will receive an Observation:\n", "\n", "Observation: The issue is submitted successfully.\n", "\n", "Answer: Your complaint about Microsoft Office 365 has been submitted successfully, Balaji.\n", "\n", "Important Notes:\n", "\n", "\t•\tFollow the loop of Thought → Action → PAUSE → Observation until the task is completed.\n", "\t•\tOnly provide the final Answer after processing all necessary steps.\n", "\t•\tIf the situation doesn’t match any of the defined actions, handle it as a normal conversation.\n", "\n", "            \"\"\"\n", "\n", "    def __init__(self):\n", "      self.messages = [] # setting up a basic memory\n", "      self.messages.append({\"role\": \"system\", \"content\": self.prompt})\n", "\n", "    def __call__(self, user_message):\n", "      self.messages.append({\"role\": \"user\", \"content\": user_message})\n", "      ai_message = llm_groq.invoke(self.messages)\n", "      self.messages.append({\"role\": \"assistant\", \"content\": ai_message.content})\n", "      return ai_message.content"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 71}, "executionInfo": {"elapsed": 645, "status": "ok", "timestamp": 1732358739147, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 480}, "id": "gUh2bMFTsaWP", "outputId": "fb6e3264-f691-45d7-c8a5-c8eebd0fea03"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["\"Thought: I should submit a complaint about Microsoft Office 365 with the person's name, <PERSON><PERSON><PERSON>.\\n\\nAction: submit_complaint | complaint_text: Microsoft Office 365 | person_name: <PERSON><PERSON><PERSON>\\n\\nPAUSE\""]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["bot = Chatbot()\n", "response = bot(\"submit a complaint about Microsoft Office 365. My name is <PERSON><PERSON><PERSON>\")\n", "response"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 142}, "executionInfo": {"elapsed": 809, "status": "ok", "timestamp": 1732358804995, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 480}, "id": "neOc2UB7Wmkw", "outputId": "f4fae325-6672-4a57-f4d8-dc6ab9931a5e"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["\"Thought: I understand that the user is seeking guidance and is unsure about their life's direction. This is a complex issue that requires a conversation rather than a specific action. I'll engage in a normal conversation to help the user clarify their thoughts.\\n\\nAction: Engage in conversation to understand the user's concerns and feelings.\\n\\nPAUSE\\n\\nPlease respond with your thoughts, and I'll continue to guide you. What's been bothering you lately, and what are your concerns about your life's direction?\""]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["bot = Chatbot()\n", "response = bot(\"I don't know what to do in life. HElp me with some guidance.\")\n", "response"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 53}, "executionInfo": {"elapsed": 626, "status": "ok", "timestamp": 1731884447229, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 480}, "id": "1PtRCeTD7CvW", "outputId": "ab11357e-e26b-4c2d-f416-c0db5bdb8fe8"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'Thought: You want me to calculate the logarithm of 2 times the square root of 36.\\n\\nAction: calculate | expression: import math; result = math.log(2 * math.sqrt(36))\\n\\nPAUSE'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["response = bot(\"find log of 2 times square root of 36\")\n", "response"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"executionInfo": {"elapsed": 183, "status": "ok", "timestamp": 1732358901708, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 480}, "id": "6YJ7p8m71uHa"}, "outputs": [], "source": ["import re\n", "# Extract the Action part\n", "def handle_action(response):\n", "    action_match = re.search(r'Action: ([^\\n]+)', response)\n", "    action = action_match.group(1).strip() if action_match else None\n", "\n", "    # Initialize empty parameters dictionary\n", "    params = {}\n", "\n", "    if action:\n", "        # Check if it's a 'calculate' action\n", "        if action.startswith(\"calculate:\"):\n", "            # Extract the full calculation expression after 'calculate:'\n", "            calculation_expression = action.split(\"calculate:\", 1)[1].strip()\n", "            params['expression'] = calculation_expression\n", "\n", "        # If action includes parameters (like in a complaint), split by '|'\n", "        elif '|' in action:\n", "            parts = action.split('|')\n", "            action = parts[0].strip()\n", "\n", "            for part in parts[1:]:\n", "                key_value = part.split(':', 1)  # Split only on the first ':'\n", "                if len(key_value) == 2:\n", "                    params[key_value[0].strip()] = key_value[1].strip()\n", "\n", "    print(f\"Action: {action}\")\n", "    print(f\"Parameters: {params}\")\n", "\n", "    if action == \"submit_complaint\":\n", "        complaint = params.get('complaint_text')\n", "        name = params.get('person_name')\n", "        if complaint and name:\n", "            action_response = submit_complaint(complaint=complaint, name=name)\n", "            return response + \"\\n\" + action_response\n", "        else:\n", "            return response + \"\\n\" + \"Error: Missing complaint_text or person_name\"\n", "\n", "    elif action.startswith(\"calculate\"):\n", "        expression = params.get('expression')\n", "        action_response = calculate(expression=expression)\n", "        return response + \"\\n\" + str(action_response)\n", "\n", "    else:\n", "        return response  # there is no action here, return the original response"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"background_save": true, "base_uri": "https://localhost:8080/", "height": 862}, "id": "jZxuYM_ctY57"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/gradio/components/chatbot.py:231: UserWarning: The 'tuples' format for chatbot messages is deprecated and will be removed in a future version of Gradio. Please set type='messages' instead, which uses openai-style 'role' and 'content' keys.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Running Gradio in a Colab notebook requires sharing enabled. Automatically setting `share=True` (you can turn this off by setting `share=False` in `launch()` explicitly).\n", "\n", "Colab notebook detected. This cell will run indefinitely so that you can see errors and logs. To turn off, set debug=False in launch().\n", "* Running on public URL: https://0239b505f762d747b0.gradio.live\n", "\n", "This share link expires in 72 hours. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n"]}, {"data": {"text/html": ["<div><iframe src=\"https://0239b505f762d747b0.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Action: calculate\n", "Parameters: {'expression': '125552 ** (1/3)'}\n", "Action: calculate\n", "Parameters: {'expression': '2224 ** 2'}\n", "Action: (no action, normal conversation)\n", "Parameters: {}\n", "Action: submit_complaint\n", "Parameters: {'complaint_text': 'Internet connection issue', 'person_name': '?'}\n", "Action: calculate\n", "Parameters: {'expression': '2 + 3'}\n"]}], "source": ["import gradio as gr\n", "bot = Chatbot()\n", "def chat(message, history):\n", "    response = bot(message)\n", "    return handle_action(response)\n", "\n", "demo = gr.<PERSON><PERSON>(\n", "    fn=chat,\n", "    title=\"Simple Chatbot\",\n", "    description=\"This is a chatbot built as part of Modern AI Pro Essentials program\",\n", ")\n", "demo.launch(debug=True)"]}], "metadata": {"colab": {"authorship_tag": "ABX9TyOfy7Wf+a3YkImT652h57CW", "name": "", "version": ""}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}