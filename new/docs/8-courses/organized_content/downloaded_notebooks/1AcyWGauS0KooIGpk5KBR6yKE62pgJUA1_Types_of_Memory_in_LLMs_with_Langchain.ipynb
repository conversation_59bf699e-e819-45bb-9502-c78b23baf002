{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["#Modern AI Pro: Types of memory\n", "We will use different types of memory to store our conversations."], "metadata": {"id": "RyNVRd6-6_U2"}}, {"cell_type": "code", "source": ["!pip install -U -q langchain-anthropic langchain gradio langchain-community"], "metadata": {"id": "4KR9qvMULGxg", "executionInfo": {"status": "ok", "timestamp": 1725450107941, "user_tz": 420, "elapsed": 34264, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "19da4e23-48de-436a-9714-03aff8c68f9d"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.4/50.4 kB\u001b[0m \u001b[31m969.9 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m16.8/16.8 MB\u001b[0m \u001b[31m48.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m318.7/318.7 kB\u001b[0m \u001b[31m13.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m42.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m891.5/891.5 kB\u001b[0m \u001b[31m36.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.4/76.4 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m396.4/396.4 kB\u001b[0m \u001b[31m19.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m288.5/288.5 kB\u001b[0m \u001b[31m14.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m141.9/141.9 kB\u001b[0m \u001b[31m7.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.3/10.3 MB\u001b[0m \u001b[31m58.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.8/62.8 kB\u001b[0m \u001b[31m3.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m93.5/93.5 kB\u001b[0m \u001b[31m5.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m3.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m318.9/318.9 kB\u001b[0m \u001b[31m14.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m2.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.4/71.4 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m130.2/130.2 kB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}]}, {"cell_type": "code", "source": ["from google.colab import userdata\n", "from os import environ\n", "from langchain_anthropic import ChatAnthropic\n", "\n", "environ[\"ANTHROPIC_API_KEY\"] = userdata.get('ANTHROPIC_API_KEY')\n", "llm = ChatAnthropic(temperature=0.7, model_name=\"claude-3-sonnet-20240229\")\n"], "metadata": {"id": "oi1ITOLQLDBE"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Basic: Conversation Buffer Memory"], "metadata": {"id": "LfkqRf7V7JIL"}}, {"cell_type": "code", "source": ["from langchain.memory import ConversationBufferMemory, ConversationSummaryMemory\n", "from langchain.chains import ConversationChain"], "metadata": {"id": "zgawEfmzOO3p"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.prompts.prompt import PromptTemplate\n", "template = \"\"\"\n", "Your name is <PERSON><PERSON>. You are an assistant for question-answering tasks for Mitra Robot customer support.\n", "Use three sentences maximum and keep the answer concise.\n", "Have a conversation related to robots and note their key details. Ask them about their key information to get started.\n", "\n", "Current conversation:\n", "{history}\n", "Human: {input}\n", "<PERSON><PERSON>:\"\"\"\n", "prompt = PromptTemplate(input_variables=[\"chat_history\", \"input\"], template=template)"], "metadata": {"id": "ovRiMygbk1sa"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["memory=ConversationBufferMemory()"], "metadata": {"id": "K1WIJTYSngeo"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["memory.save_context({\"input\": \"My name is <PERSON><PERSON><PERSON> and I live in Bengaluru\"}, {\"output\": \"Thank you!\"})\n", "memory.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ARiiopljn6ec", "executionInfo": {"status": "ok", "timestamp": 1724574807214, "user_tz": 420, "elapsed": 3, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "b645d01c-1f99-4d62-e4fc-a50dc9e3b37e"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': 'Human: My name is <PERSON><PERSON><PERSON> and I live in Bengaluru\\nAI: Thank you!'}"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "code", "source": ["conversation = Conversation<PERSON><PERSON><PERSON>(\n", "    llm=llm,\n", "    prompt=prompt,\n", "    memory=memory,\n", "    verbose=True,\n", ")"], "metadata": {"id": "jrRn9-z4MfCF", "executionInfo": {"status": "ok", "timestamp": 1724574807599, "user_tz": 420, "elapsed": 387, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "a3f470de-6c7b-4aa0-da28-d1820d9f825a"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/langchain_core/_api/deprecation.py:141: LangChainDeprecationWarning: The class `ConversationChain` was deprecated in LangChain 0.2.7 and will be removed in 1.0. Use RunnableWithMessageHistory: https://api.python.langchain.com/en/latest/runnables/langchain_core.runnables.history.RunnableWithMessageHistory.html instead.\n", "  warn_deprecated(\n"]}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"Hi, what is the name the customer gave?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 330}, "id": "DV97uHvvMnmi", "executionInfo": {"status": "ok", "timestamp": 1724574809800, "user_tz": 420, "elapsed": 2203, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "316cfa1f-1e01-4f7d-81d9-66e6ee767d10"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3m\n", "Your name is <PERSON><PERSON>. You are an assistant for question-answering tasks for Mitra Robot customer support.\n", "Use three sentences maximum and keep the answer concise.\n", "Have a conversation related to robots and note their key details. Ask them about their key information to get started.\n", "\n", "Current conversation:\n", "Human: My name is <PERSON><PERSON><PERSON> and I live in Bengaluru\n", "AI: Thank you!\n", "Human: Hi, what is the name the customer gave?\n", "Mitra:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"Mitra: The customer's name is <PERSON><PERSON><PERSON> and he lives in Bengaluru. To assist you better, could you please share some key details about the robot or the issue you need help with?\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 8}]}, {"cell_type": "code", "source": ["import gradio as gr\n", "def chat(message, history):\n", "    return conversation.predict(input=message)\n", "\n", "# Create a Gradio interface\n", "demo = gr.<PERSON><PERSON>(\n", "    fn=chat,\n", "    title=\"Mitra Robot Buffer Memory Chat\",\n", ")\n", "\n", "# Launch the Gradio interface\n", "demo.launch(share=True)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 610}, "id": "9pX4PlXDaAkz", "executionInfo": {"status": "ok", "timestamp": 1724574821566, "user_tz": 420, "elapsed": 11769, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "ac783f9f-a898-46fb-c444-93f98c8c1715"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Colab notebook detected. To show errors in colab notebook, set debug=True in launch()\n", "Running on public URL: https://7ea571ee6fc344917c.gradio.live\n", "\n", "This share link expires in 72 hours. For free permanent hosting and GPU upgrades, run `gradio deploy` from Terminal to deploy to Spaces (https://huggingface.co/spaces)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"https://7ea571ee6fc344917c.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": []}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "code", "source": ["memory.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AKTz_HQfoBM7", "executionInfo": {"status": "ok", "timestamp": 1724574821567, "user_tz": 420, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "deb3024d-0f43-4712-8bb6-a5588e3d64ac"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': \"Human: My name is <PERSON><PERSON><PERSON> and I live in Bengaluru\\nAI: Thank you!\\nHuman: <PERSON>, what is the name the customer gave?\\nAI: Mitra: The customer's name is <PERSON><PERSON><PERSON> and he lives in Bengaluru. To assist you better, could you please share some key details about the robot or the issue you need help with?\"}"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "markdown", "source": ["## Conversation Summary Memory"], "metadata": {"id": "0Q1Hqwu87T9G"}}, {"cell_type": "code", "source": ["conversation_with_summary = ConversationChain(\n", "    llm=llm,\n", "    memory=ConversationSummaryMemory(llm=llm),\n", "    verbose=True,\n", "    prompt=prompt\n", ")\n", "conversation_with_summary.predict(input=\"Hi, what's up?\")"], "metadata": {"id": "ObGL6CcK63Lr", "colab": {"base_uri": "https://localhost:8080/", "height": 312}, "executionInfo": {"status": "ok", "timestamp": 1724574825111, "user_tz": 420, "elapsed": 3547, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "e177fcca-e088-41dd-c95a-9ee0cb3d919c"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3m\n", "Your name is <PERSON><PERSON>. You are an assistant for question-answering tasks for Mitra Robot customer support.\n", "Use three sentences maximum and keep the answer concise.\n", "Have a conversation related to robots and note their key details. Ask them about their key information to get started.\n", "\n", "Current conversation:\n", "\n", "Human: Hi, what's up?\n", "Mitra:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"Hello! I'm <PERSON><PERSON>, an AI assistant for Mitra Robot customer support. Let's start with some key details about your robot - what model do you have and what questions can I help you with? I'll provide concise and helpful responses.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["def summary_chat(message, history):\n", "    return conversation_with_summary.predict(input=message)\n", "\n", "# Create a Gradio interface\n", "summary_demo = gr.ChatInterface(\n", "    fn=chat,\n", "    title=\"Mitra Robot Summary Memory Chat\",\n", ")\n", "\n", "# Launch the Gradio interface\n", "summary_demo.launch(share=True)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 610}, "id": "krLmkD7IiUPw", "executionInfo": {"status": "ok", "timestamp": 1724574827291, "user_tz": 420, "elapsed": 2183, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "668771f8-cbf7-455d-91c2-9d6e228a2f5f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Colab notebook detected. To show errors in colab notebook, set debug=True in launch()\n", "Running on public URL: https://55536c3417c3518d12.gradio.live\n", "\n", "This share link expires in 72 hours. For free permanent hosting and GPU upgrades, run `gradio deploy` from Terminal to deploy to Spaces (https://huggingface.co/spaces)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"https://55536c3417c3518d12.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": []}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["conversation_with_summary.memory.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "L2DNOVoFWEJe", "executionInfo": {"status": "ok", "timestamp": 1724574827291, "user_tz": 420, "elapsed": 3, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "52dcc148-c85e-4c27-b07b-812284e087d3"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': 'The human greeted the AI assistant, who introduced itself as <PERSON><PERSON>, an AI assistant for Mitra Robot customer support. <PERSON><PERSON> asked the human for their robot model and what questions they need help with, stating that it will provide concise and helpful responses.'}"]}, "metadata": {}, "execution_count": 13}]}]}