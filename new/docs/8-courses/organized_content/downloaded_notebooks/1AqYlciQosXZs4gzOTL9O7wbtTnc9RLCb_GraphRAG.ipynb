{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyNz/VFVBGKHCP6rnNvW2hFX"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"8f3647a9a3a84fed9375bcda0b2a6b3e": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ae5e9209cf3148e885d5d1215319e8fc", "IPY_MODEL_d56edcee9e374782bcd92cfbe1267c4f", "IPY_MODEL_657e281a21f9462d95c9f8ab3b88eee8"], "layout": "IPY_MODEL_68dd3113588f43678de605e5e9809bb5", "tabbable": null, "tooltip": null}}, "ae5e9209cf3148e885d5d1215319e8fc": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_66a9f45d63cc4584871f48ab9d74de4c", "placeholder": "​", "style": "IPY_MODEL_ce9fda28afb846969785baff7ecf63b9", "tabbable": null, "tooltip": null, "value": "modules.json: 100%"}}, "d56edcee9e374782bcd92cfbe1267c4f": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_be1893980d7d43999280a0f275ea2e86", "max": 349, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bd987f6a14f948328e74e064b6b258e7", "tabbable": null, "tooltip": null, "value": 349}}, "657e281a21f9462d95c9f8ab3b88eee8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_231d23864aee45f29c48e0dad70fe3b0", "placeholder": "​", "style": "IPY_MODEL_27803376b80c414eac2fd8e0a85e3872", "tabbable": null, "tooltip": null, "value": " 349/349 [00:00&lt;00:00, 16.9kB/s]"}}, "68dd3113588f43678de605e5e9809bb5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "66a9f45d63cc4584871f48ab9d74de4c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ce9fda28afb846969785baff7ecf63b9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "be1893980d7d43999280a0f275ea2e86": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bd987f6a14f948328e74e064b6b258e7": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "231d23864aee45f29c48e0dad70fe3b0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "27803376b80c414eac2fd8e0a85e3872": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "f494c751230c4559a35c9cd94be11872": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_18771b5eef3641c0ac55cb9dd94d87bf", "IPY_MODEL_62fd8fd1cdb64ef1b3843d9557fcbcb6", "IPY_MODEL_809a3848e52647e38b76e8e890354b76"], "layout": "IPY_MODEL_8bac16f3cf5d4b1eab93c7964ea7f5d6", "tabbable": null, "tooltip": null}}, "18771b5eef3641c0ac55cb9dd94d87bf": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_4febf90e8e394a0f95795c2a995c9ca6", "placeholder": "​", "style": "IPY_MODEL_5b095ae026cf43a4abaeb6b86a10b0b9", "tabbable": null, "tooltip": null, "value": "config_sentence_transformers.json: 100%"}}, "62fd8fd1cdb64ef1b3843d9557fcbcb6": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_930b8c056e094ee48161f6c1c64d5039", "max": 124, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7fe58f7247da40fab00a0e559762403f", "tabbable": null, "tooltip": null, "value": 124}}, "809a3848e52647e38b76e8e890354b76": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_af0e986b86ea451e83502f3705a813dd", "placeholder": "​", "style": "IPY_MODEL_e4468284e17e48e79dd6997f18d5a172", "tabbable": null, "tooltip": null, "value": " 124/124 [00:00&lt;00:00, 5.83kB/s]"}}, "8bac16f3cf5d4b1eab93c7964ea7f5d6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4febf90e8e394a0f95795c2a995c9ca6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5b095ae026cf43a4abaeb6b86a10b0b9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "930b8c056e094ee48161f6c1c64d5039": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7fe58f7247da40fab00a0e559762403f": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "af0e986b86ea451e83502f3705a813dd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e4468284e17e48e79dd6997f18d5a172": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "4542b886b7694c44b78083341e8b324e": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_52123676f38f46ab8d72b1d814c79439", "IPY_MODEL_3557dfcc681f44b59fa8ab9f7de60cd7", "IPY_MODEL_80884e4e74dc4ad893be2453824f5b3e"], "layout": "IPY_MODEL_b247836cd09c4c1f8f05bffd49833d5b", "tabbable": null, "tooltip": null}}, "52123676f38f46ab8d72b1d814c79439": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_66c527ed454845c186742eadeab6b503", "placeholder": "​", "style": "IPY_MODEL_4abcdaef73274731b99682af8d2a5f5d", "tabbable": null, "tooltip": null, "value": "README.md: 100%"}}, "3557dfcc681f44b59fa8ab9f7de60cd7": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_7fceb6b5a59e4707afb3a91e5bf32cac", "max": 94783, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a33ffdbf0fa34efda404f94d68d84ba7", "tabbable": null, "tooltip": null, "value": 94783}}, "80884e4e74dc4ad893be2453824f5b3e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_e44c8839bf2a4f259d6048c9a0996c63", "placeholder": "​", "style": "IPY_MODEL_10d8d6c1449d47639b71ce2d9c15f201", "tabbable": null, "tooltip": null, "value": " 94.8k/94.8k [00:00&lt;00:00, 4.01MB/s]"}}, "b247836cd09c4c1f8f05bffd49833d5b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "66c527ed454845c186742eadeab6b503": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4abcdaef73274731b99682af8d2a5f5d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "7fceb6b5a59e4707afb3a91e5bf32cac": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a33ffdbf0fa34efda404f94d68d84ba7": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e44c8839bf2a4f259d6048c9a0996c63": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "10d8d6c1449d47639b71ce2d9c15f201": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "ead080463b524f5592946f8879e6fec8": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e2ccea28b87242ada9e9a9a92dde89fb", "IPY_MODEL_7b85eb2d31a34c0fa2e6977be8003806", "IPY_MODEL_7a2188561a7b4781935dfe3074d7a739"], "layout": "IPY_MODEL_21ebba8cc8a444d1b8982036403d2bb8", "tabbable": null, "tooltip": null}}, "e2ccea28b87242ada9e9a9a92dde89fb": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_c71eacf24e5442a383a11130f5663b06", "placeholder": "​", "style": "IPY_MODEL_08beb5179fde491a8420c04a60e06749", "tabbable": null, "tooltip": null, "value": "sentence_bert_config.json: 100%"}}, "7b85eb2d31a34c0fa2e6977be8003806": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_4a85a9c86ecf47439c48b99f7916b723", "max": 52, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_000e1a3ec8db4ae294c62adad8dc36f4", "tabbable": null, "tooltip": null, "value": 52}}, "7a2188561a7b4781935dfe3074d7a739": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_866f468c4403424c91e0d80a09ae8c2f", "placeholder": "​", "style": "IPY_MODEL_cacf48971db24871aca6724cd7f01e9b", "tabbable": null, "tooltip": null, "value": " 52.0/52.0 [00:00&lt;00:00, 2.62kB/s]"}}, "21ebba8cc8a444d1b8982036403d2bb8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c71eacf24e5442a383a11130f5663b06": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "08beb5179fde491a8420c04a60e06749": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "4a85a9c86ecf47439c48b99f7916b723": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "000e1a3ec8db4ae294c62adad8dc36f4": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "866f468c4403424c91e0d80a09ae8c2f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cacf48971db24871aca6724cd7f01e9b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "74120e221b4944a99f3101ed96e2a1c6": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_cc90a0eb0c004dc99d325238868705e1", "IPY_MODEL_59f9eb3bf99e48ad80500af0bb0993a6", "IPY_MODEL_89dca5d71baa44728bfafdc46febbbe5"], "layout": "IPY_MODEL_9b1dbc80f7ef41a5929f456ef3add2a3", "tabbable": null, "tooltip": null}}, "cc90a0eb0c004dc99d325238868705e1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_ace7f4e7d53249f581d56a237ea03e3a", "placeholder": "​", "style": "IPY_MODEL_7d0e37e563a44bd89bf841086d36630b", "tabbable": null, "tooltip": null, "value": "config.json: 100%"}}, "59f9eb3bf99e48ad80500af0bb0993a6": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_f50c3ea98fae48ddb75abd2ee19b4d60", "max": 743, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_afd9f3fcff3748a8a354d3f25f2a5195", "tabbable": null, "tooltip": null, "value": 743}}, "89dca5d71baa44728bfafdc46febbbe5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_aedb33eef2ab44cf97d2b1cff39b046e", "placeholder": "​", "style": "IPY_MODEL_c01b0c0284814faeab12e4e99b65c4e5", "tabbable": null, "tooltip": null, "value": " 743/743 [00:00&lt;00:00, 38.7kB/s]"}}, "9b1dbc80f7ef41a5929f456ef3add2a3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ace7f4e7d53249f581d56a237ea03e3a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7d0e37e563a44bd89bf841086d36630b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "f50c3ea98fae48ddb75abd2ee19b4d60": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "afd9f3fcff3748a8a354d3f25f2a5195": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "aedb33eef2ab44cf97d2b1cff39b046e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c01b0c0284814faeab12e4e99b65c4e5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "446b175e38254c94b1f75f42ad45ae42": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e1036f1994cb4902b097e01dbad69c59", "IPY_MODEL_94f145cdaab04d1bac7fa19358bf9c8e", "IPY_MODEL_eb8b723017f045909d9675549273f964"], "layout": "IPY_MODEL_fb2bd2ef1815437b8f2c60dbba63af17", "tabbable": null, "tooltip": null}}, "e1036f1994cb4902b097e01dbad69c59": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_7f5504039b5943f6a5c8cf77f78326c8", "placeholder": "​", "style": "IPY_MODEL_e4f12a72b9c94687a5b9af19a6bec240", "tabbable": null, "tooltip": null, "value": "model.safetensors: 100%"}}, "94f145cdaab04d1bac7fa19358bf9c8e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_b96520e91a2b44229257553b8a0a7349", "max": 133466304, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_553d6bd4c835410eb289ad397fdbdd32", "tabbable": null, "tooltip": null, "value": 133466304}}, "eb8b723017f045909d9675549273f964": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_808cf91964a64ca0950f04b70d77ec53", "placeholder": "​", "style": "IPY_MODEL_0f7d7966a28b40fca36f71d2a34d56d9", "tabbable": null, "tooltip": null, "value": " 133M/133M [00:01&lt;00:00, 154MB/s]"}}, "fb2bd2ef1815437b8f2c60dbba63af17": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7f5504039b5943f6a5c8cf77f78326c8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e4f12a72b9c94687a5b9af19a6bec240": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "b96520e91a2b44229257553b8a0a7349": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "553d6bd4c835410eb289ad397fdbdd32": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "808cf91964a64ca0950f04b70d77ec53": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0f7d7966a28b40fca36f71d2a34d56d9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "405a4ad05ceb475cb5df3dcd0727261a": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_07dc0f5051814ea188042f9a83733774", "IPY_MODEL_41cc54e29e9c4f37be2e0030421fb462", "IPY_MODEL_8b3bd79b32e94d158914869a76d13a21"], "layout": "IPY_MODEL_f14b872d0c1b4d58a35b3fa4cfd1e5f3", "tabbable": null, "tooltip": null}}, "07dc0f5051814ea188042f9a83733774": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_4b394452665e45afa7208dbd74dc516e", "placeholder": "​", "style": "IPY_MODEL_94805313894b4e0b9bf0a431e7564832", "tabbable": null, "tooltip": null, "value": "tokenizer_config.json: 100%"}}, "41cc54e29e9c4f37be2e0030421fb462": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_ffa9bdf4aff84677adc73bcd76afe171", "max": 366, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b08208a18c764c26abc7a799e322b7df", "tabbable": null, "tooltip": null, "value": 366}}, "8b3bd79b32e94d158914869a76d13a21": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_0efc29723557412bb36cc1b4ad10b434", "placeholder": "​", "style": "IPY_MODEL_ab1e674fb2e743bdbf46ed2cbc38a658", "tabbable": null, "tooltip": null, "value": " 366/366 [00:00&lt;00:00, 14.9kB/s]"}}, "f14b872d0c1b4d58a35b3fa4cfd1e5f3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4b394452665e45afa7208dbd74dc516e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "94805313894b4e0b9bf0a431e7564832": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "ffa9bdf4aff84677adc73bcd76afe171": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b08208a18c764c26abc7a799e322b7df": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0efc29723557412bb36cc1b4ad10b434": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ab1e674fb2e743bdbf46ed2cbc38a658": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "f10fc8d395b943339d9a0984b3e4775b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_bd118cf75acc43b2bbf200bf01450325", "IPY_MODEL_be4f30d484104c458d94f85102d062ea", "IPY_MODEL_1c5147b303b04325b19a98f3ad1b53ec"], "layout": "IPY_MODEL_34675a62407d4d258dd773ac2134ab7d", "tabbable": null, "tooltip": null}}, "bd118cf75acc43b2bbf200bf01450325": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_b8a8267a2ccf4a89abb3b209cb9fa2e1", "placeholder": "​", "style": "IPY_MODEL_411c46e6721c4345825d0e50bdcbff0c", "tabbable": null, "tooltip": null, "value": "vocab.txt: 100%"}}, "be4f30d484104c458d94f85102d062ea": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_10b41fe2a04847c4be68a63041201f40", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_978739916e3a412ca370a14a93162251", "tabbable": null, "tooltip": null, "value": 231508}}, "1c5147b303b04325b19a98f3ad1b53ec": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_8b732de19b4843828782de3ac6ef9d6e", "placeholder": "​", "style": "IPY_MODEL_35fd4ecb1e3b49e7ad3cebef3f9ae950", "tabbable": null, "tooltip": null, "value": " 232k/232k [00:00&lt;00:00, 8.13MB/s]"}}, "34675a62407d4d258dd773ac2134ab7d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b8a8267a2ccf4a89abb3b209cb9fa2e1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "411c46e6721c4345825d0e50bdcbff0c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "10b41fe2a04847c4be68a63041201f40": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "978739916e3a412ca370a14a93162251": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8b732de19b4843828782de3ac6ef9d6e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "35fd4ecb1e3b49e7ad3cebef3f9ae950": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "db190a538fdd4038b528b4e0e199cfb2": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_09c15fcbcefc4689ac9f2eebb3f057d0", "IPY_MODEL_394cc3c2802840ce9cdf37de8b9fd930", "IPY_MODEL_14b068305ffa45b3a169aa9f0873cb21"], "layout": "IPY_MODEL_cedcec87194a4bf9abe1bff1eb3959ca", "tabbable": null, "tooltip": null}}, "09c15fcbcefc4689ac9f2eebb3f057d0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_223e194acc234a0c83b3bfe54c4b1852", "placeholder": "​", "style": "IPY_MODEL_64ddbf3c28864cd595be4d54d969a0a1", "tabbable": null, "tooltip": null, "value": "tokenizer.json: 100%"}}, "394cc3c2802840ce9cdf37de8b9fd930": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_7b5ede904934418387fe9e3211f463bc", "max": 711396, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_37d8f86266af4afebe632ebd31c0fc63", "tabbable": null, "tooltip": null, "value": 711396}}, "14b068305ffa45b3a169aa9f0873cb21": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_e07ccfe40565428c87ad28c76785bac0", "placeholder": "​", "style": "IPY_MODEL_391fe4f36d5941ef87d9af0e2c171dce", "tabbable": null, "tooltip": null, "value": " 711k/711k [00:00&lt;00:00, 20.6MB/s]"}}, "cedcec87194a4bf9abe1bff1eb3959ca": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "223e194acc234a0c83b3bfe54c4b1852": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "64ddbf3c28864cd595be4d54d969a0a1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "7b5ede904934418387fe9e3211f463bc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "37d8f86266af4afebe632ebd31c0fc63": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e07ccfe40565428c87ad28c76785bac0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "391fe4f36d5941ef87d9af0e2c171dce": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "d239ed7f50a249769a78183dc1c20b3e": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_becc9a2dac5c4b33b908aba9681388bc", "IPY_MODEL_cede7c023cd94fbf8f8bd600388d3802", "IPY_MODEL_0cc590e6e48344eda5bba69f600c2add"], "layout": "IPY_MODEL_4d5af1f0b3964d0a975e01333b197b3b", "tabbable": null, "tooltip": null}}, "becc9a2dac5c4b33b908aba9681388bc": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_eefc0e2ea7e6427abfb5db4b4fec4795", "placeholder": "​", "style": "IPY_MODEL_88eb6585fbfb4c3396e4a9b1f0c707f5", "tabbable": null, "tooltip": null, "value": "special_tokens_map.json: 100%"}}, "cede7c023cd94fbf8f8bd600388d3802": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_c6dd22c5fb6f4eca8cef06d2d10cdea2", "max": 125, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_59b0923702cf4daea8009754528ea574", "tabbable": null, "tooltip": null, "value": 125}}, "0cc590e6e48344eda5bba69f600c2add": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_14fd89f76a5646c6b274852d32e8d82e", "placeholder": "​", "style": "IPY_MODEL_91889f6b53994a9d9fd4fc1d1887134c", "tabbable": null, "tooltip": null, "value": " 125/125 [00:00&lt;00:00, 4.31kB/s]"}}, "4d5af1f0b3964d0a975e01333b197b3b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eefc0e2ea7e6427abfb5db4b4fec4795": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "88eb6585fbfb4c3396e4a9b1f0c707f5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "c6dd22c5fb6f4eca8cef06d2d10cdea2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "59b0923702cf4daea8009754528ea574": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "14fd89f76a5646c6b274852d32e8d82e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "91889f6b53994a9d9fd4fc1d1887134c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "1fc6588ca7354634adc4dba5da24ec9e": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2c2870867da84347b15503f54a5722d9", "IPY_MODEL_3ea79039aa294018be64f17ac3910e76", "IPY_MODEL_80c17782176b4353bc3c16d84ade733f"], "layout": "IPY_MODEL_94084ef86aaf47f48a650cd4130df069", "tabbable": null, "tooltip": null}}, "2c2870867da84347b15503f54a5722d9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_fecad938a0ea4c19ac274c656e38ae36", "placeholder": "​", "style": "IPY_MODEL_13632c2a37f04029aecd6fef7e60b29b", "tabbable": null, "tooltip": null, "value": "1_Pooling/config.json: 100%"}}, "3ea79039aa294018be64f17ac3910e76": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_bface19e57d64fc8872e5c34c3eca634", "max": 190, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c196458abf534b46ab33f1c3c3d991ce", "tabbable": null, "tooltip": null, "value": 190}}, "80c17782176b4353bc3c16d84ade733f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "2.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_79f0ed38a60e4abbb06458dab21fe565", "placeholder": "​", "style": "IPY_MODEL_72bffd1ac3874f16b455ff5a55c664b3", "tabbable": null, "tooltip": null, "value": " 190/190 [00:00&lt;00:00, 3.63kB/s]"}}, "94084ef86aaf47f48a650cd4130df069": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fecad938a0ea4c19ac274c656e38ae36": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "13632c2a37f04029aecd6fef7e60b29b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "bface19e57d64fc8872e5c34c3eca634": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c196458abf534b46ab33f1c3c3d991ce": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "79f0ed38a60e4abbb06458dab21fe565": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "72bffd1ac3874f16b455ff5a55c664b3": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLStyleModel", "model_module_version": "2.0.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}}}}, "cells": [{"cell_type": "markdown", "source": ["# Modern AI Pro: P<PERSON><PERSON><PERSON>er"], "metadata": {"id": "AbB35gucDCdu"}}, {"cell_type": "code", "source": ["!pip install --upgrade --quiet  langchain langchain-community  langchain-huggingface langchain-groq langchain-experimental neo4j wikipedia tiktoken yfiles_jupyter_graphs sentence-transformers chromadb\n"], "metadata": {"id": "LXBlWMxX1zVM", "executionInfo": {"status": "ok", "timestamp": 1732442601487, "user_tz": 480, "elapsed": 57482, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "9bc8874c-94f4-4c03-8fec-b1ebc0b23ed9"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.3/67.3 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m39.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.4/2.4 MB\u001b[0m \u001b[31m60.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m209.0/209.0 kB\u001b[0m \u001b[31m13.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m302.0/302.0 kB\u001b[0m \u001b[31m19.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m46.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.5/15.5 MB\u001b[0m \u001b[31m59.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m268.8/268.8 kB\u001b[0m \u001b[31m16.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m617.9/617.9 kB\u001b[0m \u001b[31m29.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.4/2.4 MB\u001b[0m \u001b[31m58.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m278.6/278.6 kB\u001b[0m \u001b[31m17.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m94.9/94.9 kB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m108.9/108.9 kB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m139.8/139.8 kB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.9/1.9 MB\u001b[0m \u001b[31m56.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m409.5/409.5 kB\u001b[0m \u001b[31m24.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m93.2/93.2 kB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.3/13.3 MB\u001b[0m \u001b[31m46.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m55.8/55.8 kB\u001b[0m \u001b[31m4.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.4/54.4 kB\u001b[0m \u001b[31m3.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.1/3.1 MB\u001b[0m \u001b[31m52.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m63.8/63.8 kB\u001b[0m \u001b[31m3.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m442.1/442.1 kB\u001b[0m \u001b[31m25.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.5/49.5 kB\u001b[0m \u001b[31m3.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m316.6/316.6 kB\u001b[0m \u001b[31m17.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m73.2/73.2 kB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.8/3.8 MB\u001b[0m \u001b[31m38.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m425.7/425.7 kB\u001b[0m \u001b[31m17.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m168.2/168.2 kB\u001b[0m \u001b[31m7.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m29.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m2.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.6/1.6 MB\u001b[0m \u001b[31m30.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for wikipedia (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Building wheel for pypika (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "tensorflow 2.17.1 requires protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.20.3, but you have protobuf 5.28.3 which is incompatible.\n", "tensorflow-metadata 1.13.1 requires protobuf<5,>=3.20.3, but you have protobuf 5.28.3 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0m"]}]}, {"cell_type": "code", "source": ["import os\n", "os.environ['USER_AGENT'] = 'mitra_summarizer'\n", "from IPython.display import HTML, display\n", "\n", "def set_css():\n", "  display(HTML('''\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  '''))\n", "get_ipython().events.register('pre_run_cell', set_css)"], "metadata": {"id": "-pp3XcVDOl7e", "colab": {"base_uri": "https://localhost:8080/", "height": 17}, "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": 480, "elapsed": 163, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "3a4002ed-f99c-40dc-b9d4-25645c1ba7af"}, "execution_count": 21, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["## 1. Model Setup"], "metadata": {"id": "Pd7XWvQaPvBV"}}, {"cell_type": "code", "source": ["import os\n", "from google.colab import userdata\n", "from langchain_groq import ChatGroq\n", "os.environ[\"GROQ_API_KEY\"] = userdata.get(\"GROQ_API_KEY\")\n", "llm_groq = ChatGroq(model_name=\"llama3-70b-8192\")"], "metadata": {"id": "gPmKocvN1wZL", "colab": {"base_uri": "https://localhost:8080/", "height": 17}, "executionInfo": {"status": "ok", "timestamp": 1732442607333, "user_tz": 480, "elapsed": 5856, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "72d6527b-908a-45ee-ffef-575de1b2a741"}, "execution_count": 3, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}]}, {"cell_type": "code", "source": ["os.environ[\"NEO4J_URI\"] = userdata.get(\"NEO4J_URI\")\n", "os.environ[\"NEO4J_USERNAME\"] = userdata.get(\"NEO4J_USERNAME\")\n", "os.environ[\"NEO4J_PASSWORD\"] = userdata.get(\"NEO4J_PASSWORD\")"], "metadata": {"id": "r_fniXc714VP", "colab": {"base_uri": "https://localhost:8080/", "height": 17}, "executionInfo": {"status": "ok", "timestamp": 1732442623842, "user_tz": 480, "elapsed": 1684, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "14c93c90-12ce-454e-ee07-c8d1fcacdd44"}, "execution_count": 4, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "EJ4Zqiwq1sHD", "executionInfo": {"status": "ok", "timestamp": 1732442625122, "user_tz": 480, "elapsed": 532, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 17}, "outputId": "dcfd5140-340c-46f2-9f0c-79feabfc7c9b"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}], "source": ["from langchain.docstore.document import Document\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_community.vectorstores import Neo4jVector"]}, {"cell_type": "code", "source": ["from langchain_huggingface import HuggingFaceEmbeddings\n", "\n", "embeddings = HuggingFaceEmbeddings(\n", "    model_name=\"BAAI/bge-small-en-v1.5\",\n", "    model_kwargs={'device': 'cpu'},\n", "    encode_kwargs={'normalize_embeddings': False}\n", ")"], "metadata": {"id": "Hmn1zFwC2QmE", "colab": {"base_uri": "https://localhost:8080/", "height": 17, "referenced_widgets": ["8f3647a9a3a84fed9375bcda0b2a6b3e", "ae5e9209cf3148e885d5d1215319e8fc", "d56edcee9e374782bcd92cfbe1267c4f", "657e281a21f9462d95c9f8ab3b88eee8", "68dd3113588f43678de605e5e9809bb5", "66a9f45d63cc4584871f48ab9d74de4c", "ce9fda28afb846969785baff7ecf63b9", "be1893980d7d43999280a0f275ea2e86", "bd987f6a14f948328e74e064b6b258e7", "231d23864aee45f29c48e0dad70fe3b0", "27803376b80c414eac2fd8e0a85e3872", "f494c751230c4559a35c9cd94be11872", "18771b5eef3641c0ac55cb9dd94d87bf", "62fd8fd1cdb64ef1b3843d9557fcbcb6", "809a3848e52647e38b76e8e890354b76", "8bac16f3cf5d4b1eab93c7964ea7f5d6", "4febf90e8e394a0f95795c2a995c9ca6", "5b095ae026cf43a4abaeb6b86a10b0b9", "930b8c056e094ee48161f6c1c64d5039", "7fe58f7247da40fab00a0e559762403f", "af0e986b86ea451e83502f3705a813dd", "e4468284e17e48e79dd6997f18d5a172", "4542b886b7694c44b78083341e8b324e", "52123676f38f46ab8d72b1d814c79439", "3557dfcc681f44b59fa8ab9f7de60cd7", "80884e4e74dc4ad893be2453824f5b3e", "b247836cd09c4c1f8f05bffd49833d5b", "66c527ed454845c186742eadeab6b503", "4abcdaef73274731b99682af8d2a5f5d", "7fceb6b5a59e4707afb3a91e5bf32cac", "a33ffdbf0fa34efda404f94d68d84ba7", "e44c8839bf2a4f259d6048c9a0996c63", "10d8d6c1449d47639b71ce2d9c15f201", "ead080463b524f5592946f8879e6fec8", "e2ccea28b87242ada9e9a9a92dde89fb", "7b85eb2d31a34c0fa2e6977be8003806", "7a2188561a7b4781935dfe3074d7a739", "21ebba8cc8a444d1b8982036403d2bb8", "c71eacf24e5442a383a11130f5663b06", "08beb5179fde491a8420c04a60e06749", "4a85a9c86ecf47439c48b99f7916b723", "000e1a3ec8db4ae294c62adad8dc36f4", "866f468c4403424c91e0d80a09ae8c2f", "cacf48971db24871aca6724cd7f01e9b", "74120e221b4944a99f3101ed96e2a1c6", "cc90a0eb0c004dc99d325238868705e1", "59f9eb3bf99e48ad80500af0bb0993a6", "89dca5d71baa44728bfafdc46febbbe5", "9b1dbc80f7ef41a5929f456ef3add2a3", "ace7f4e7d53249f581d56a237ea03e3a", "7d0e37e563a44bd89bf841086d36630b", "f50c3ea98fae48ddb75abd2ee19b4d60", "afd9f3fcff3748a8a354d3f25f2a5195", "aedb33eef2ab44cf97d2b1cff39b046e", "c01b0c0284814faeab12e4e99b65c4e5", "446b175e38254c94b1f75f42ad45ae42", "e1036f1994cb4902b097e01dbad69c59", "94f145cdaab04d1bac7fa19358bf9c8e", "eb8b723017f045909d9675549273f964", "fb2bd2ef1815437b8f2c60dbba63af17", "7f5504039b5943f6a5c8cf77f78326c8", "e4f12a72b9c94687a5b9af19a6bec240", "b96520e91a2b44229257553b8a0a7349", "553d6bd4c835410eb289ad397fdbdd32", "808cf91964a64ca0950f04b70d77ec53", "0f7d7966a28b40fca36f71d2a34d56d9", "405a4ad05ceb475cb5df3dcd0727261a", "07dc0f5051814ea188042f9a83733774", "41cc54e29e9c4f37be2e0030421fb462", "8b3bd79b32e94d158914869a76d13a21", "f14b872d0c1b4d58a35b3fa4cfd1e5f3", "4b394452665e45afa7208dbd74dc516e", "94805313894b4e0b9bf0a431e7564832", "ffa9bdf4aff84677adc73bcd76afe171", "b08208a18c764c26abc7a799e322b7df", "0efc29723557412bb36cc1b4ad10b434", "ab1e674fb2e743bdbf46ed2cbc38a658", "f10fc8d395b943339d9a0984b3e4775b", "bd118cf75acc43b2bbf200bf01450325", "be4f30d484104c458d94f85102d062ea", "1c5147b303b04325b19a98f3ad1b53ec", "34675a62407d4d258dd773ac2134ab7d", "b8a8267a2ccf4a89abb3b209cb9fa2e1", "411c46e6721c4345825d0e50bdcbff0c", "10b41fe2a04847c4be68a63041201f40", "978739916e3a412ca370a14a93162251", "8b732de19b4843828782de3ac6ef9d6e", "35fd4ecb1e3b49e7ad3cebef3f9ae950", "db190a538fdd4038b528b4e0e199cfb2", "09c15fcbcefc4689ac9f2eebb3f057d0", "394cc3c2802840ce9cdf37de8b9fd930", "14b068305ffa45b3a169aa9f0873cb21", "cedcec87194a4bf9abe1bff1eb3959ca", "223e194acc234a0c83b3bfe54c4b1852", "64ddbf3c28864cd595be4d54d969a0a1", "7b5ede904934418387fe9e3211f463bc", "37d8f86266af4afebe632ebd31c0fc63", "e07ccfe40565428c87ad28c76785bac0", "391fe4f36d5941ef87d9af0e2c171dce", "d239ed7f50a249769a78183dc1c20b3e", "becc9a2dac5c4b33b908aba9681388bc", "cede7c023cd94fbf8f8bd600388d3802", "0cc590e6e48344eda5bba69f600c2add", "4d5af1f0b3964d0a975e01333b197b3b", "eefc0e2ea7e6427abfb5db4b4fec4795", "88eb6585fbfb4c3396e4a9b1f0c707f5", "c6dd22c5fb6f4eca8cef06d2d10cdea2", "59b0923702cf4daea8009754528ea574", "14fd89f76a5646c6b274852d32e8d82e", "91889f6b53994a9d9fd4fc1d1887134c", "1fc6588ca7354634adc4dba5da24ec9e", "2c2870867da84347b15503f54a5722d9", "3ea79039aa294018be64f17ac3910e76", "80c17782176b4353bc3c16d84ade733f", "94084ef86aaf47f48a650cd4130df069", "fecad938a0ea4c19ac274c656e38ae36", "13632c2a37f04029aecd6fef7e60b29b", "bface19e57d64fc8872e5c34c3eca634", "c196458abf534b46ab33f1c3c3d991ce", "79f0ed38a60e4abbb06458dab21fe565", "72bffd1ac3874f16b455ff5a55c664b3"]}, "executionInfo": {"status": "ok", "timestamp": 1732442659219, "user_tz": 480, "elapsed": 32575, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "87a8a562-887a-474a-e065-4cb62cae8412"}, "execution_count": 6, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "8f3647a9a3a84fed9375bcda0b2a6b3e"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/124 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "f494c751230c4559a35c9cd94be11872"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["README.md:   0%|          | 0.00/94.8k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "4542b886b7694c44b78083341e8b324e"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["sentence_bert_config.json:   0%|          | 0.00/52.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ead080463b524f5592946f8879e6fec8"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/743 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "74120e221b4944a99f3101ed96e2a1c6"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/133M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "446b175e38254c94b1f75f42ad45ae42"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/366 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "405a4ad05ceb475cb5df3dcd0727261a"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "f10fc8d395b943339d9a0984b3e4775b"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/711k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "db190a538fdd4038b528b4e0e199cfb2"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/125 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d239ed7f50a249769a78183dc1c20b3e"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["1_Pooling/config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1fc6588ca7354634adc4dba5da24ec9e"}}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["## 2. Generate Vector Embeddings"], "metadata": {"id": "JNm8rgKcPyxK"}}, {"cell_type": "code", "source": ["loader = WebBaseLoader(\"https://www.cnn.com/2024/09/14/asia/india-nuclear-ballistic-missile-submarine-intl-hnk-ml/index.html\")\n", "\n", "documents = loader.load()\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)\n", "docs = text_splitter.split_documents(documents)\n", "len(docs)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "cFv-ACjl-Z3x", "executionInfo": {"status": "ok", "timestamp": 1732442675341, "user_tz": 480, "elapsed": 16129, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "1ecda8cd-30a3-4033-d2b9-c836b7b5545b"}, "execution_count": 7, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": ["69"]}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "code", "source": ["db = Neo4jVector.from_documents(docs,embeddings,url=os.environ[\"NEO4J_URI\"],username=os.environ[\"NEO4J_USERNAME\"],password=os.environ[\"NEO4J_PASSWORD\"],search_type='hybrid')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 17}, "id": "xoLvlVDW3aLe", "executionInfo": {"status": "ok", "timestamp": 1732442685349, "user_tz": 480, "elapsed": 10032, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "25d4beb5-fbc4-4173-f4a1-8a79090e1002"}, "execution_count": 8, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}]}, {"cell_type": "code", "source": ["query = \"Relationship between China and India\"\n", "docs_with_score = db.similarity_search_with_score(query, k=5)\n", "# combine documents\n", "results = [d[0].page_content for d in docs_with_score]\n", "combined_docs = \" \".join(results)\n", "combined_docs"], "metadata": {"id": "S8pL2YDQ2XzP", "executionInfo": {"status": "ok", "timestamp": 1732442726200, "user_tz": 480, "elapsed": 871, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 302}, "outputId": "a0117868-2f74-4b8c-a365-abcab86d4e89"}, "execution_count": 9, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["WARNING:neo4j.notifications:Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: CALL subquery without a variable scope clause is now deprecated. Use CALL () { ... }} {position: line: 1, column: 1, offset: 0} for query: 'CALL { CALL db.index.vector.queryNodes($index, $k, $embedding) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score UNION CALL db.index.fulltext.queryNodes($keyword_index, $query, {limit: $k}) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score } WITH node, max(score) AS score ORDER BY score DESC LIMIT $k RETURN node.`text` AS text, score, node {.*, `text`: Null, `embedding`: Null, id: Null } AS metadata'\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"The de facto border between India and China, known as the Line of Actual Control, has been a longtime flashpoint between the two. Troops most recently clashed there in 2022 and in 2020, when hand-to-hand fighting between the two sides resulted in the deaths of at least 20 Indian and four Chinese soldiers in Aksai Chin.\\n    \\n\\n        India developing second-strike capabilities Another regional rival\\n\\n\\n            It‚Äôs not just China that India is looking at with its sub development, according to <PERSON><PERSON><PERSON><PERSON>, a senior fellow at the Observer Research Foundation in Mumbai.\\n    \\n\\n\\n\\n\\n\\n\\n\\n\\n\\nPakistan's Foreign Secretary <PERSON> during an interview in Ankara, Turkey, on August 4, 2021.\\n\\nAli Balikci/Anadolu Agency/Getty Images\\n\\n\\n\\n\\nRelated article\\nPakistan¬†accuses India of extrajudicial killings on¬†its territory India and Pakistan have long been at odds in the disputed and heavily militarized region of Kashmir, which both countries claim in its entirety. A de facto border called the Line of Control divides it between New Delhi and Islamabad. The dispute has led to three wars between the two nations.\\n    \\n\\n            China remains one of Pakistan‚Äôs most important international backers and a major investor in the country.\\n    \\n\\n        Proliferation fears But India is still playing catch-up, at least compared with China, as the People‚Äôs Liberation Army grows its fleet ‚Äì as well as its land and air capabilities ‚Äì amid simmering tensions along their shared border. EyePress News/Reuters\\n\\n\\n\\n\\nRelated article\\nA high-altitude tunnel is latest flashpoint in India-China border tensions\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "code", "source": ["prompt = \"You are an advanced summarizing agent. Take the following context and answer the user query appropriately\"\n", "def vector_rag(query):\n", "  docs_with_score = db.similarity_search_with_score(query, k=5)\n", "  results = [d[0].page_content for d in docs_with_score]\n", "  combined_docs = \" \".join(results)\n", "\n", "  rag_query = prompt + \"The context is: \" + combined_docs + \"The question is: \" + query\n", "  return llm_groq.invoke(rag_query).content"], "metadata": {"id": "gUEaew2U5j6Q", "executionInfo": {"status": "ok", "timestamp": 1732442730883, "user_tz": 480, "elapsed": 169, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 17}, "outputId": "8ccf7642-cb40-4968-9c7c-2eeb57455c2f"}, "execution_count": 10, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}]}, {"cell_type": "code", "source": ["vector_rag(\"tell me about india's nuclear submarine\")"], "metadata": {"id": "_7BjjAu66HIs", "executionInfo": {"status": "ok", "timestamp": 1732442733688, "user_tz": 480, "elapsed": 897, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 249}, "outputId": "56cc9919-293a-4bcf-b007-a6096143a25a"}, "execution_count": 11, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["WARNING:neo4j.notifications:Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: CALL subquery without a variable scope clause is now deprecated. Use CALL () { ... }} {position: line: 1, column: 1, offset: 0} for query: 'CALL { CALL db.index.vector.queryNodes($index, $k, $embedding) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score UNION CALL db.index.fulltext.queryNodes($keyword_index, $query, {limit: $k}) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score } WITH node, max(score) AS score ORDER BY score DESC LIMIT $k RETURN node.`text` AS text, score, node {.*, `text`: Null, `embedding`: Null, id: Null } AS metadata'\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"According to the article, India has recently commissioned its second nuclear-capable ballistic missile submarine, called the Arighaat, which joins its naval fleet. The government has been secretive about the submarine's capabilities, but claims it is significantly more advanced than its predecessor, which was commissioned eight years ago. The Arighaat is part of India's efforts to establish a secure second-strike nuclear force, allowing it to target both Pakistani and Chinese targets. India has not released any pictures of the submarine since its commissioning on August 29.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "markdown", "source": ["## Moving to graphs"], "metadata": {"id": "ChdJDXsZ7chb"}}, {"cell_type": "code", "source": ["from langchain_community.graphs import Neo4jGraph\n", "graph = Neo4jGraph()"], "metadata": {"id": "TpOQZSVC7Sdt", "executionInfo": {"status": "ok", "timestamp": 1732442736379, "user_tz": 480, "elapsed": 1407, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 17}, "outputId": "2e6602e0-b944-4d9e-b57a-28b6cd2a7778"}, "execution_count": 12, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}]}, {"cell_type": "code", "source": ["from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "llm_transformer = LLMGraphTransformer(llm=llm_groq)\n", "graph_documents = llm_transformer.convert_to_graph_documents(docs[50:55])\n", "graph_documents"], "metadata": {"id": "WLk-x8kv7huP", "executionInfo": {"status": "ok", "timestamp": 1732442781364, "user_tz": 480, "elapsed": 43840, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "28226268-04da-4a30-fb89-1c55d6ab9a74"}, "execution_count": 13, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": ["[GraphDocument(nodes=[Node(id='India', type='Country', properties={}), Node(id='China', type='Country', properties={}), Node(id='Pakistan', type='Country', properties={}), Node(id='<PERSON><PERSON><PERSON><PERSON>', type='Person', properties={}), Node(id='Muhammad <PERSON>yrus <PERSON>', type='Person', properties={}), Node(id='Ankara', type='City', properties={}), Node(id='Turkey', type='Country', properties={}), Node(id='Mumbai', type='City', properties={}), Node(id='Observer Research Foundation', type='Organization', properties={})], relationships=[Relationship(source=Node(id='India', type='Country', properties={}), target=Node(id='China', type='Country', properties={}), type='RIVAL', properties={}), Relationship(source=Node(id='India', type='Country', properties={}), target=Node(id='Pakistan', type='Country', properties={}), type='RIVAL', properties={}), Relationship(source=Node(id='<PERSON><PERSON><PERSON><PERSON>', type='Person', properties={}), target=Node(id='Observer Research Foundation', type='Organization', properties={}), type='AFFILIATION', properties={}), Relationship(source=Node(id='Muhammad Syrus Sajjad Qazi', type='Person', properties={}), target=Node(id='Pakistan', type='Country', properties={}), type='DIPLOMAT', properties={}), Relationship(source=Node(id='Ankara', type='City', properties={}), target=Node(id='Turkey', type='Country', properties={}), type='PART_OF', properties={}), Relationship(source=Node(id='Mumbai', type='City', properties={}), target=Node(id='India', type='Country', properties={}), type='PART_OF', properties={})], source=Document(metadata={'source': 'https://www.cnn.com/2024/09/14/asia/india-nuclear-ballistic-missile-submarine-intl-hnk-ml/index.html', 'title': 'India has a new nuclear-capable ballistic missile submarine. But can it catch up with China? | CNN', 'description': 'India‚Äôs second nuclear-capable ballistic missile submarine joined its naval fleet late last month, a move the government says strengthens its nuclear deterrent as New Delhi casts a wary eye at both China and Pakistan.', 'language': 'en'}, page_content=\"Another regional rival\\n\\n\\n            It‚Äôs not just China that India is looking at with its sub development, according to Abhijit Singh, a senior fellow at the Observer Research Foundation in Mumbai.\\n    \\n\\n\\n\\n\\n\\n\\n\\n\\n\\nPakistan's Foreign Secretary Muhammad Syrus Sajjad Qazi during an interview in Ankara, Turkey, on August 4, 2021.\\n\\nAli Balikci/Anadolu Agency/Getty Images\\n\\n\\n\\n\\nRelated article\\nPakistan¬†accuses India of extrajudicial killings on¬†its territory\")),\n", " GraphDocument(nodes=[Node(id='India', type='Country', properties={}), Node(id='Pakistan', type='Country', properties={}), Node(id='China', type='Country', properties={}), Node(id='Indian Ocean', type='Ocean', properties={}), Node(id='Hindustan Times', type='Newspaper', properties={}), Node(id='Type 039B Attack Submarine', type='Submarine', properties={})], relationships=[Relationship(source=Node(id='India', type='Country', properties={}), target=Node(id='Indian Ocean', type='Ocean', properties={}), type='LOCATED_IN', properties={}), Relationship(source=Node(id='Pakistan', type='Country', properties={}), target=Node(id='China', type='Country', properties={}), type='ACQUIRING_FROM', properties={}), Relationship(source=Node(id='Pakistan', type='Country', properties={}), target=Node(id='Type 039B Attack Submarine', type='Submarine', properties={}), type='ACQUIRING', properties={}), Relationship(source=Node(id='Hindustan Times', type='Newspaper', properties={}), target=Node(id='Singh', type='Person', properties={}), type='WRITTEN_BY', properties={})], source=Document(metadata={'source': 'https://www.cnn.com/2024/09/14/asia/india-nuclear-ballistic-missile-submarine-intl-hnk-ml/index.html', 'title': 'India has a new nuclear-capable ballistic missile submarine. But can it catch up with China? | CNN', 'description': 'India‚Äôs second nuclear-capable ballistic missile submarine joined its naval fleet late last month, a move the government says strengthens its nuclear deterrent as New Delhi casts a wary eye at both China and Pakistan.', 'language': 'en'}, page_content='‚ÄúThe real impetus for India‚Äôs expansion of its second-strike capability is, in fact, the significant growth of the Pakistani and Chinese navies in the Indian Ocean,‚Äù Singh wrote in an op-ed for the Hindustan Times, adding that Islamabad is in the process of acquiring eight Chinese-designed Type 039B attack submarines as it modernizes its fleet.\\n    \\n\\n            ‚ÄúPakistan continues to narrow the sea-power differential with India,‚Äù Singh wrote.')),\n", " GraphDocument(nodes=[Node(id='India', type='Country', properties={}), Node(id='Pakistan', type='Country', properties={}), Node(id='Kashmir', type='Region', properties={}), Node(id='New Delhi', type='City', properties={}), Node(id='Islamabad', type='City', properties={}), Node(id='China', type='Country', properties={})], relationships=[Relationship(source=Node(id='India', type='Country', properties={}), target=Node(id='Pakistan', type='Country', properties={}), type='AT_ODDS', properties={}), Relationship(source=Node(id='India', type='Country', properties={}), target=Node(id='Kashmir', type='Region', properties={}), type='CLAIMS', properties={}), Relationship(source=Node(id='Pakistan', type='Country', properties={}), target=Node(id='Kashmir', type='Region', properties={}), type='CLAIMS', properties={}), Relationship(source=Node(id='India', type='Country', properties={}), target=Node(id='New Delhi', type='City', properties={}), type='CAPITAL', properties={}), Relationship(source=Node(id='Pakistan', type='Country', properties={}), target=Node(id='Islamabad', type='City', properties={}), type='CAPITAL', properties={}), Relationship(source=Node(id='China', type='Country', properties={}), target=Node(id='Pakistan', type='Country', properties={}), type='BACKER', properties={})], source=Document(metadata={'source': 'https://www.cnn.com/2024/09/14/asia/india-nuclear-ballistic-missile-submarine-intl-hnk-ml/index.html', 'title': 'India has a new nuclear-capable ballistic missile submarine. But can it catch up with China? | CNN', 'description': 'India‚Äôs second nuclear-capable ballistic missile submarine joined its naval fleet late last month, a move the government says strengthens its nuclear deterrent as New Delhi casts a wary eye at both China and Pakistan.', 'language': 'en'}, page_content='India and Pakistan have long been at odds in the disputed and heavily militarized region of Kashmir, which both countries claim in its entirety. A de facto border called the Line of Control divides it between New Delhi and Islamabad. The dispute has led to three wars between the two nations.\\n    \\n\\n            China remains one of Pakistan‚Äôs most important international backers and a major investor in the country.\\n    \\n\\n        Proliferation fears')),\n", " GraphDocument(nodes=[Node(id='Proliferation Fears', type='Concept', properties={})], relationships=[Relationship(source=Node(id='Proliferation Fears', type='Concept', properties={}), target=Node(id='Korda', type='Person', properties={}), type='EXPERT', properties={})], source=Document(metadata={'source': 'https://www.cnn.com/2024/09/14/asia/india-nuclear-ballistic-missile-submarine-intl-hnk-ml/index.html', 'title': 'India has a new nuclear-capable ballistic missile submarine. But can it catch up with China? | CNN', 'description': 'India‚<PERSON><PERSON>s second nuclear-capable ballistic missile submarine joined its naval fleet late last month, a move the government says strengthens its nuclear deterrent as New Delhi casts a wary eye at both China and Pakistan.', 'language': 'en'}, page_content='Proliferation fears\\n\\n\\n            <PERSON><PERSON>, the Federation of American Scientists expert, says it‚<PERSON><PERSON>s not the subs themselves that give him cause for worry, but the multiple-warhead missiles they carry.\\n    \\n\\n            That technology ‚Äì known as Multiple Independently targetable Reentry Vehicles (MIRV) ‚Äì also applies to land-based missiles and can be destabilizing, <PERSON><PERSON> argues.')),\n", " GraphDocument(nodes=[Node(id='India', type='Country', properties={}), Node(id='Pakistan', type='Country', properties={}), Node(id='China', type='Country', properties={}), Node(id='Us', type='Country', properties={}), Node(id='Uk', type='Country', properties={}), Node(id='France', type='Country', properties={}), Node(id='Russia', type='Country', properties={})], relationships=[Relationship(source=Node(id='India', type='Country', properties={}), target=Node(id='Agni-V', type='Missile', properties={}), type='DEVELOPS', properties={}), Relationship(source=Node(id='Pakistan', type='Country', properties={}), target=Node(id='Mirv', type='Technology', properties={}), type='CLAIMS', properties={}), Relationship(source=Node(id='China', type='Country', properties={}), target=Node(id='Mirv', type='Technology', properties={}), type='HAS', properties={})], source=Document(metadata={'source': 'https://www.cnn.com/2024/09/14/asia/india-nuclear-ballistic-missile-submarine-intl-hnk-ml/index.html', 'title': 'India has a new nuclear-capable ballistic missile submarine. But can it catch up with China? | CNN', 'description': 'India‚Äôs second nuclear-capable ballistic missile submarine joined its naval fleet late last month, a move the government says strengthens its nuclear deterrent as New Delhi casts a wary eye at both China and Pakistan.', 'language': 'en'}, page_content='‚ÄúIndia, Pakistan, and China are all developing missiles that can carry multiple warheads,‚Äù he says.\\n    \\n\\n            India announced to great fanfare in April that it had joined the MIRV club, which includes the US, UK, France, Russia and China, with a successful test of the domestically developed Agni-V intercontinental ballistic missile.\\n    \\n\\n            Pakistan has also claimed to have MIRV technology, but experts say the claim is unverified.'))]"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["graph.add_graph_documents(\n", "  graph_documents,\n", "  baseEntityLabel=True,\n", "  include_source=True\n", ")"], "metadata": {"id": "FOZXbY_y8PqC", "executionInfo": {"status": "ok", "timestamp": 1732442784227, "user_tz": 480, "elapsed": 2893, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 17}, "outputId": "9fbcfe0c-be82-4caa-c47a-4726e6ec181f"}, "execution_count": 14, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}]}, {"cell_type": "code", "source": ["from langchain_community.vectorstores.neo4j_vector import remove_lucene_chars\n", "graph.query(\"CREATE FULLTEXT INDEX entity IF NOT EXISTS FOR (e:__Entity__) ON EACH [e.id]\")\n", "def generate_full_text_query(input: str) -> str:\n", "    full_text_query = \"\"\n", "    words = [el for el in remove_lucene_chars(input).split() if el]\n", "    for word in words[:-1]:\n", "        full_text_query += f\" {word}~2 AND\"\n", "    full_text_query += f\" {words[-1]}~2\"\n", "    return full_text_query.strip()"], "metadata": {"id": "HfnkLuCxJIjp", "executionInfo": {"status": "ok", "timestamp": 1732442784227, "user_tz": 480, "elapsed": 9, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 17}, "outputId": "c0e15570-7181-4d0f-9dec-427ae6decd31"}, "execution_count": 15, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}]}, {"cell_type": "code", "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.prompts.prompt import PromptTemplate\n", "from langchain_core.pydantic_v1 import BaseModel, Field\n", "from typing import Tuple, List, Optional\n", "\n", "class Entities(BaseModel):\n", "    \"\"\"Identifying information about entities.\"\"\"\n", "\n", "    names: List[str] = Field(...,\n", "        description=\"All the person, organization, or business entities that \" \"appear in the text\",\n", "    )\n", "\n", "prompt = ChatPromptTemplate.from_messages([(\n", "            \"system\",\n", "            \"You are extracting organization and person entities from the text.\"),\n", "            (\n", "            \"human\",\n", "            \"Use the given format to extract information from the following\"\n", "            \"input: {question}\",\n", "        ),\n", "    ]\n", ")\n", "\n", "entity_chain = prompt | llm_groq.with_structured_output(Entities)\n", "entity_chain.invoke({\"question\": \"who is the rival of India?\"}).names"], "metadata": {"id": "keIVCsLNKser", "executionInfo": {"status": "ok", "timestamp": 1732442796728, "user_tz": 480, "elapsed": 12508, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 213}, "outputId": "652236ae-ff7b-49ef-e648-ee81555db075"}, "execution_count": 16, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/IPython/core/interactiveshell.py:3553: LangChainDeprecationWarning: As of langchain-core 0.3.0, LangChain uses pydantic v2 internally. The langchain_core.pydantic_v1 module was a compatibility shim for pydantic v1, and should no longer be used. Please update the code to import from Pydantic directly.\n", "\n", "For example, replace imports like: `from langchain_core.pydantic_v1 import BaseModel`\n", "with: `from pydantic import BaseModel`\n", "or the v1 compatibility namespace if you are working in a code base that has not been fully upgraded to pydantic 2 yet. \tfrom pydantic.v1 import BaseModel\n", "\n", "  exec(code_obj, self.user_global_ns, self.user_ns)\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["['India']"]}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "code", "source": ["def structured_retriever(question: str) -> str:\n", "    result = \"\"\n", "    entities = entity_chain.invoke({\"question\": question})\n", "    for entity in entities.names:\n", "        response = graph.query(\n", "            \"\"\"CALL db.index.fulltext.queryNodes('entity', $query,\n", "            {limit:2})\n", "            YIELD node,score\n", "            CALL {\n", "              MATCH (node)-[r:!MENTIONS]->(neighbor)\n", "              RETURN node.id + ' - ' + type(r) + ' -> ' + neighbor.id AS\n", "              output\n", "              UNION\n", "              MATCH (node)<-[r:!MENTIONS]-(neighbor)\n", "              RETURN neighbor.id + ' - ' + type(r) + ' -> ' +  node.id AS\n", "              output\n", "            }\n", "            RETURN output LIMIT 50\n", "            \"\"\",\n", "            {\"query\": generate_full_text_query(entity)},\n", "        )\n", "        result += \"\\n\".join([el['output'] for el in response])\n", "    return result\n", "\n", "print(structured_retriever(\"Who is the rival of India?\"))"], "metadata": {"id": "vne8THZBJbVH", "executionInfo": {"status": "ok", "timestamp": 1732442806433, "user_tz": 480, "elapsed": 9714, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 854}, "outputId": "3f272c40-a73a-446d-c528-26e7b33ffbf5"}, "execution_count": 17, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["WARNING:neo4j.notifications:Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: CALL subquery without a variable scope clause is now deprecated. Use CALL () { ... }} {position: line: 4, column: 13, offset: 116} for query: \"CALL db.index.fulltext.queryNodes('entity', $query,\\n            {limit:2})\\n            YIELD node,score\\n            CALL {\\n              MATCH (node)-[r:!MENTIONS]->(neighbor)\\n              RETURN node.id + ' - ' + type(r) + ' -> ' + neighbor.id AS\\n              output\\n              UNION\\n              MATCH (node)<-[r:!MENTIONS]-(neighbor)\\n              RETURN neighbor.id + ' - ' + type(r) + ' -> ' +  node.id AS\\n              output\\n            }\\n            RETURN output LIMIT 50\\n            \"\n"]}, {"output_type": "stream", "name": "stdout", "text": ["India - RIVAL -> China\n", "India - RIVAL -> Pakistan\n", "India - LOCATED_IN -> Indian Ocean\n", "India - AT_ODDS -> Pakistan\n", "India - CLAIMS -> Kashmir\n", "India - CAPITAL -> New Delhi\n", "India - DEVELOPS -> Agni-V\n", "China - BACKER -> Pakistan\n", "China - HAS -> Mirv\n", "Pakistan - ACQUIRING_FROM -> China\n", "Pakistan - ACQUIRING -> Type 039B Attack Submarine\n", "Pakistan - CLAIMS -> Kashmir\n", "Pakistan - CLAIMS -> Mirv\n", "Pakistan - CAPITAL -> Islamabad\n", "<PERSON><PERSON><PERSON><PERSON> - AFFILIATION -> Observer Research Foundation\n", "<PERSON> - DIPLOMAT -> Pakistan\n", "Ankara - PART_OF -> Turkey\n", "Mumbai - PART_OF -> India\n", "Hindustan Times - WRITTEN_BY -> <PERSON>\n", "Proliferation Fears - EXPERT -> <PERSON><PERSON>\n", "India - RIVAL -> China\n", "India - RIVAL -> Pakistan\n", "India - LOCATED_IN -> Indian Ocean\n", "India - AT_ODDS -> Pakistan\n", "India - CLAIMS -> Kashmir\n", "India - CAPITAL -> New Delhi\n", "India - DEVELOPS -> Agni-V\n", "China - BACKER -> Pakistan\n", "China - HAS -> Mirv\n", "Pakistan - ACQUIRING_FROM -> China\n", "Pakistan - ACQUIRING -> Type 039B Attack Submarine\n", "Pakistan - CLAIMS -> Kashmir\n", "Pakistan - CLAIMS -> Mirv\n", "Pakistan - CAPITAL -> Islamabad\n", "<PERSON><PERSON><PERSON><PERSON> - AFFILIATION -> Observer Research Foundation\n", "<PERSON> - DIPLOMAT -> Pakistan\n", "Ankara - PART_OF -> Turkey\n", "Mumbai - PART_OF -> India\n", "Hindustan Times - WRITTEN_BY -> <PERSON>\n", "Proliferation Fears - EXPERT -> <PERSON><PERSON>\n"]}]}, {"cell_type": "code", "source": ["def graph_retriever(question: str):\n", "    print(f\"Search query: {question}\")\n", "    structured_data = structured_retriever(question)\n", "    docs_with_score = db.similarity_search_with_score(question, k=7)\n", "    results = [d[0].page_content for d in docs_with_score]\n", "    combined_docs = \" \".join(results)\n", "\n", "    return f\"\"\"Structured data: {structured_data} Unstructured data: {combined_docs}\"\"\"\n", "\n", "graph_retriever(\"Who is the rival of India?\")"], "metadata": {"id": "YU6TXn8dLLqt", "executionInfo": {"status": "ok", "timestamp": 1732442815463, "user_tz": 480, "elapsed": 9034, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 445}, "outputId": "f41d0564-45f6-45a2-bd59-3d42a023e25f"}, "execution_count": 18, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Search query: Who is the rival of India?\n"]}, {"output_type": "stream", "name": "stderr", "text": ["WARNING:neo4j.notifications:Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: CALL subquery without a variable scope clause is now deprecated. Use CALL () { ... }} {position: line: 4, column: 13, offset: 116} for query: \"CALL db.index.fulltext.queryNodes('entity', $query,\\n            {limit:2})\\n            YIELD node,score\\n            CALL {\\n              MATCH (node)-[r:!MENTIONS]->(neighbor)\\n              RETURN node.id + ' - ' + type(r) + ' -> ' + neighbor.id AS\\n              output\\n              UNION\\n              MATCH (node)<-[r:!MENTIONS]-(neighbor)\\n              RETURN neighbor.id + ' - ' + type(r) + ' -> ' +  node.id AS\\n              output\\n            }\\n            RETURN output LIMIT 50\\n            \"\n", "WARNING:neo4j.notifications:Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: CALL subquery without a variable scope clause is now deprecated. Use CALL () { ... }} {position: line: 1, column: 1, offset: 0} for query: 'CALL { CALL db.index.vector.queryNodes($index, $k, $embedding) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score UNION CALL db.index.fulltext.queryNodes($keyword_index, $query, {limit: $k}) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score } WITH node, max(score) AS score ORDER BY score DESC LIMIT $k RETURN node.`text` AS text, score, node {.*, `text`: Null, `embedding`: Null, id: Null } AS metadata'\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"Structured data: India - RIVAL -> China\\nIndia - RIVAL -> Pakistan\\nIndia - LOCATED_IN -> Indian Ocean\\nIndia - AT_ODDS -> Pakistan\\nIndia - CLAIMS -> Kashmir\\nIndia - CAPITAL -> New Delhi\\nIndia - DEVELOPS -> Agni-V\\nChina - BACKER -> Pakistan\\nChina - HAS -> Mirv\\nPakistan - ACQUIRING_FROM -> China\\nPakistan - ACQUIRING -> Type 039B Attack Submarine\\nPakistan - CLAIMS -> Kashmir\\nPakistan - CLAIMS -> Mirv\\nPakistan - CAPITAL -> Islamabad\\nA<PERSON><PERSON><PERSON> - AFFILIATION -> Observer Research Foundation\\nMuhammad <PERSON>yr<PERSON> - DIPLOMAT -> Pakistan\\nAnkara - PART_OF -> Turkey\\nMumbai - PART_OF -> India\\nHindustan Times - WRITTEN_BY -> Singh\\nProliferation Fears - EXPERT -> Korda\\nIndia - RIVAL -> China\\nIndia - RIVAL -> Pakistan\\nIndia - LOCATED_IN -> Indian Ocean\\nIndia - AT_ODDS -> Pakistan\\nIndia - CLAIMS -> Kashmir\\nIndia - CAPITAL -> New Delhi\\nIndia - DEVELOPS -> Agni-V\\nChina - BACKER -> Pakistan\\nChina - HAS -> Mirv\\nPakistan - ACQUIRING_FROM -> China\\nPakistan - ACQUIRING -> Type 039B Attack Submarine\\nPakistan - CLAIMS -> Kashmir\\nPakistan - CLAIMS -> Mirv\\nPakistan - CAPITAL -> Islamabad\\nAbhijit Singh - AFFILIATION -> Observer Research Foundation\\nMuhammad Syrus Sajjad Qazi - DIPLOMAT -> Pakistan\\nAnkara - PART_OF -> Turkey\\nMumbai - PART_OF -> India\\nHindustan Times - WRITTEN_BY -> Singh\\nProliferation Fears - EXPERT -> Korda Unstructured data: Another regional rival\\n\\n\\n            It‚Äôs not just China that India is looking at with its sub development, according to Abhijit Singh, a senior fellow at the Observer Research Foundation in Mumbai.\\n    \\n\\n\\n\\n\\n\\n\\n\\n\\n\\nPakistan's Foreign Secretary Muhammad Syrus Sajjad Qazi during an interview in Ankara, Turkey, on August 4, 2021.\\n\\nAli Balikci/Anadolu Agency/Getty Images\\n\\n\\n\\n\\nRelated article\\nPakistan¬†accuses India of extrajudicial killings on¬†its territory India and Pakistan have long been at odds in the disputed and heavily militarized region of Kashmir, which both countries claim in its entirety. A de facto border called the Line of Control divides it between New Delhi and Islamabad. The dispute has led to three wars between the two nations.\\n    \\n\\n            China remains one of Pakistan‚Äôs most important international backers and a major investor in the country.\\n    \\n\\n        Proliferation fears CNN\\n        \\xa0—\\xa0\\n    \\n\\n\\n            India‚Äôs second nuclear-capable ballistic missile submarine joined its naval fleet late last month, a move the government says strengthens its nuclear deterrent as New Delhi casts a wary eye at both China and Pakistan. The de facto border between India and China, known as the Line of Actual Control, has been a longtime flashpoint between the two. Troops most recently clashed there in 2022 and in 2020, when hand-to-hand fighting between the two sides resulted in the deaths of at least 20 Indian and four Chinese soldiers in Aksai Chin.\\n    \\n\\n        India developing second-strike capabilities But India is still playing catch-up, at least compared with China, as the People‚Äôs Liberation Army grows its fleet ‚Äì as well as its land and air capabilities ‚Äì amid simmering tensions along their shared border. ‚ÄúThese submarines are a key piece of India‚Äôs broader efforts to establish a secure second-strike nuclear force, thus allowing India to hold both Pakistani and Chinese targets at risk, particularly with its eventual third and fourth submarines (which will have more missile tubes and longer-range missiles),‚Äù Korda said in an email to CNN. China\\n          \\n\\n\\n\\n            Europe\\n          \\n\\n\\n\\n            India\\n          \\n\\n\\n\\n            Middle East\\n          \\n\\n\\n\\n            United Kingdom\\n          \\n\\n\\n\\n\\n\\n        Politics\\n      \\n\\n\\n\\n            SCOTUS\\n          \\n\\n\\n\\n            Congress\\n          \\n\\n\\n\\n            Facts First\\n          \\n\\n\\n\\n            2024 Elections\\n          \\n\\n\\n\\n\\n\\n        Business\\n      \\n\\n\\n\\n            Tech\\n          \\n\\n\\n\\n            Media\\n          \\n\\n\\n\\n            Calculators\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["def graph_rag(graph_user_query):\n", "  rag_query = \"You are a smart agent who will help answer a question based on the user context.The context is: \" +\\\n", "                graph_retriever(graph_user_query) + \"The question is: \" + graph_user_query\n", "  return llm_groq.invoke(rag_query).content\n", "\n", "\n", "print(graph_rag(\"Who is the rival of India?\"))"], "metadata": {"id": "G625s3odAxgJ", "executionInfo": {"status": "ok", "timestamp": 1732442842770, "user_tz": 480, "elapsed": 27315, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 338}, "outputId": "9cbc992d-df51-4709-ad83-e6224f4fe7b9"}, "execution_count": 19, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Search query: Who is the rival of India?\n"]}, {"output_type": "stream", "name": "stderr", "text": ["WARNING:neo4j.notifications:Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: CALL subquery without a variable scope clause is now deprecated. Use CALL () { ... }} {position: line: 4, column: 13, offset: 116} for query: \"CALL db.index.fulltext.queryNodes('entity', $query,\\n            {limit:2})\\n            YIELD node,score\\n            CALL {\\n              MATCH (node)-[r:!MENTIONS]->(neighbor)\\n              RETURN node.id + ' - ' + type(r) + ' -> ' + neighbor.id AS\\n              output\\n              UNION\\n              MATCH (node)<-[r:!MENTIONS]-(neighbor)\\n              RETURN neighbor.id + ' - ' + type(r) + ' -> ' +  node.id AS\\n              output\\n            }\\n            RETURN output LIMIT 50\\n            \"\n", "WARNING:neo4j.notifications:Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: CALL subquery without a variable scope clause is now deprecated. Use CALL () { ... }} {position: line: 1, column: 1, offset: 0} for query: 'CALL { CALL db.index.vector.queryNodes($index, $k, $embedding) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score UNION CALL db.index.fulltext.queryNodes($keyword_index, $query, {limit: $k}) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score } WITH node, max(score) AS score ORDER BY score DESC LIMIT $k RETURN node.`text` AS text, score, node {.*, `text`: Null, `embedding`: Null, id: Null } AS metadata'\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Based on the structured data, India has two rivals: China and Pakistan. This is also supported by the unstructured data, which mentions that India is looking at China with its sub development, and also has a longtime rivalry with Pakistan over the disputed region of Kashmir.\n"]}]}, {"cell_type": "code", "source": ["print(graph_rag(\"what are india's key missiles\"))"], "metadata": {"id": "Kk9ZfyfHQ-t3", "executionInfo": {"status": "ok", "timestamp": 1732442862924, "user_tz": 480, "elapsed": 20162, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 409}, "outputId": "b0a8ce76-0019-4781-859d-316c98516ab7"}, "execution_count": 20, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Search query: what are india's key missiles\n"]}, {"output_type": "stream", "name": "stderr", "text": ["WARNING:neo4j.notifications:Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: CALL subquery without a variable scope clause is now deprecated. Use CALL () { ... }} {position: line: 4, column: 13, offset: 116} for query: \"CALL db.index.fulltext.queryNodes('entity', $query,\\n            {limit:2})\\n            YIELD node,score\\n            CALL {\\n              MATCH (node)-[r:!MENTIONS]->(neighbor)\\n              RETURN node.id + ' - ' + type(r) + ' -> ' + neighbor.id AS\\n              output\\n              UNION\\n              MATCH (node)<-[r:!MENTIONS]-(neighbor)\\n              RETURN neighbor.id + ' - ' + type(r) + ' -> ' +  node.id AS\\n              output\\n            }\\n            RETURN output LIMIT 50\\n            \"\n", "WARNING:neo4j.notifications:Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: CALL subquery without a variable scope clause is now deprecated. Use CALL () { ... }} {position: line: 1, column: 1, offset: 0} for query: 'CALL { CALL db.index.vector.queryNodes($index, $k, $embedding) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score UNION CALL db.index.fulltext.queryNodes($keyword_index, $query, {limit: $k}) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score } WITH node, max(score) AS score ORDER BY score DESC LIMIT $k RETURN node.`text` AS text, score, node {.*, `text`: Null, `embedding`: Null, id: Null } AS metadata'\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Based on the context, India's key missiles mentioned are:\n", "\n", "1. Agni-V: an intercontinental ballistic missile with a range of over 5000 kilometers, capable of carrying a 1000 kg nuclear warhead.\n", "2. Agni-5: a long-range nuclear-capable missile with a range of over 5000 kilometers, tested successfully for the second time.\n", "\n", "Note that these are the same missile, with Agni-V being the more general term and Agni-5 being a specific variant.\n"]}]}, {"cell_type": "code", "source": ["club = graph_rag(\"what club is India a member of?\")"], "metadata": {"id": "mdnNbCaHOY8f", "executionInfo": {"status": "ok", "timestamp": 1732443661981, "user_tz": 480, "elapsed": 1430, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 302}, "outputId": "f98dd913-052e-4adf-ad16-bc48851c36c4"}, "execution_count": 22, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Search query: what club is India a member of?\n"]}, {"output_type": "stream", "name": "stderr", "text": ["WARNING:neo4j.notifications:Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: CALL subquery without a variable scope clause is now deprecated. Use CALL () { ... }} {position: line: 4, column: 13, offset: 116} for query: \"CALL db.index.fulltext.queryNodes('entity', $query,\\n            {limit:2})\\n            YIELD node,score\\n            CALL {\\n              MATCH (node)-[r:!MENTIONS]->(neighbor)\\n              RETURN node.id + ' - ' + type(r) + ' -> ' + neighbor.id AS\\n              output\\n              UNION\\n              MATCH (node)<-[r:!MENTIONS]-(neighbor)\\n              RETURN neighbor.id + ' - ' + type(r) + ' -> ' +  node.id AS\\n              output\\n            }\\n            RETURN output LIMIT 50\\n            \"\n", "WARNING:neo4j.notifications:Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: CALL subquery without a variable scope clause is now deprecated. Use CALL () { ... }} {position: line: 1, column: 1, offset: 0} for query: 'CALL { CALL db.index.vector.queryNodes($index, $k, $embedding) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score UNION CALL db.index.fulltext.queryNodes($keyword_index, $query, {limit: $k}) YIELD node, score WITH collect({node:node, score:score}) AS nodes, max(score) AS max UNWIND nodes AS n RETURN n.node AS node, (n.score / max) AS score } WITH node, max(score) AS score ORDER BY score DESC LIMIT $k RETURN node.`text` AS text, score, node {.*, `text`: Null, `embedding`: Null, id: Null } AS metadata'\n"]}]}, {"cell_type": "code", "source": ["club"], "metadata": {"id": "It3LjkNnPCUZ", "executionInfo": {"status": "ok", "timestamp": 1732443671768, "user_tz": 480, "elapsed": 173, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 71}, "outputId": "1a059842-db7e-4f41-fdd5-0e3c74e7f281"}, "execution_count": 23, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": ["'Based on the context, India is a member of the MIRV (Multiple Independently targetable Reentry Vehicle) club, which includes countries such as the US, UK, France, Russia, and China. This is mentioned in the unstructured data: \"India announced to great fanfare in April that it had joined the MIRV club... with a successful test of the domestically developed Agni-V intercontinental ballistic missile.\"'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 23}]}]}