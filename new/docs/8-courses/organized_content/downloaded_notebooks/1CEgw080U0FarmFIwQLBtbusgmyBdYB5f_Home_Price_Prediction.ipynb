{"cells": [{"cell_type": "markdown", "metadata": {"id": "Py6mXF4M46JS"}, "source": ["#Modern AI Pro: Regression techniques to predict home prices\n", "We are going to use a simple dataset to learn linear regression. This is the foundation of machine learning. https://www.kaggle.com/competitions/house-prices-advanced-regression-techniques/data\n", "\n", "![chart.png](data:image/png;base64,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********************************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)"]}, {"cell_type": "markdown", "metadata": {"id": "ePm0LKSq6PrA"}, "source": ["## Step 1: Download the data from <PERSON><PERSON> and load into the datafame"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "2QYM0CMe2_rI", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1752239178495, "user_tz": -330, "elapsed": 13361, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "5b5ad105-feee-4fd8-894f-21c9e3850bc0"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Downloading house-prices-advanced-regression-techniques.zip to /content\n", "\r  0% 0.00/199k [00:00<?, ?B/s]\n", "\r100% 199k/199k [00:00<00:00, 278MB/s]\n", "Archive:  /content/house-prices-advanced-regression-techniques.zip\n", "  inflating: data_description.txt    \n", "  inflating: sample_submission.csv   \n", "  inflating: test.csv                \n", "  inflating: train.csv               \n"]}], "source": ["from google.colab import userdata\n", "from os import environ\n", "\n", "environ[\"KAGGLE_KEY\"]      = userdata.get('KAGGLE_KEY')\n", "environ[\"KAGGLE_USERNAME\"] = userdata.get('KAGGLE_USERNAME')\n", "\n", "! pip -q install kaggle\n", "! kaggle competitions download -c house-prices-advanced-regression-techniques\n", "! unzip /content/house-prices-advanced-regression-techniques.zip"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "VXkVxmD5hixL", "colab": {"base_uri": "https://localhost:8080/", "height": 444}, "executionInfo": {"status": "ok", "timestamp": 1752240329492, "user_tz": -330, "elapsed": 691, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "1827b521-307e-432c-990f-5b4031510693"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["        Id  MSSubClass MSZoning  LotFrontage  LotArea Street Alley LotShape  \\\n", "0        1          60       RL         65.0     8450   Pave   NaN      Reg   \n", "1        2          20       RL         80.0     9600   Pave   NaN      Reg   \n", "2        3          60       RL         68.0    11250   Pave   NaN      IR1   \n", "3        4          70       RL         60.0     9550   Pave   NaN      IR1   \n", "4        5          60       RL         84.0    14260   Pave   NaN      IR1   \n", "...    ...         ...      ...          ...      ...    ...   ...      ...   \n", "1455  1456          60       RL         62.0     7917   Pave   NaN      Reg   \n", "1456  1457          20       RL         85.0    13175   Pave   NaN      Reg   \n", "1457  1458          70       RL         66.0     9042   Pave   NaN      Reg   \n", "1458  1459          20       RL         68.0     9717   Pave   NaN      Reg   \n", "1459  1460          20       RL         75.0     9937   Pave   NaN      Reg   \n", "\n", "     LandContour Utilities  ... PoolArea PoolQC  Fence MiscFeature MiscVal  \\\n", "0            Lvl    AllPub  ...        0    NaN    NaN         NaN       0   \n", "1            Lvl    AllPub  ...        0    NaN    NaN         NaN       0   \n", "2            Lvl    AllPub  ...        0    NaN    NaN         NaN       0   \n", "3            Lvl    AllPub  ...        0    NaN    NaN         NaN       0   \n", "4            Lvl    AllPub  ...        0    NaN    NaN         NaN       0   \n", "...          ...       ...  ...      ...    ...    ...         ...     ...   \n", "1455         Lvl    AllPub  ...        0    NaN    NaN         NaN       0   \n", "1456         Lvl    AllPub  ...        0    NaN  MnPrv         NaN       0   \n", "1457         Lvl    AllPub  ...        0    NaN  GdPrv        Shed    2500   \n", "1458         Lvl    AllPub  ...        0    NaN    NaN         NaN       0   \n", "1459         Lvl    AllPub  ...        0    NaN    NaN         NaN       0   \n", "\n", "     MoSold YrSold  SaleType  SaleCondition  SalePrice  \n", "0         2   2008        WD         Normal     208500  \n", "1         5   2007        WD         Normal     181500  \n", "2         9   2008        WD         Normal     223500  \n", "3         2   2006        WD        Abnorml     140000  \n", "4        12   2008        WD         Normal     250000  \n", "...     ...    ...       ...            ...        ...  \n", "1455      8   2007        WD         Normal     175000  \n", "1456      2   2010        WD         Normal     210000  \n", "1457      5   2010        WD         Normal     266500  \n", "1458      4   2010        WD         Normal     142125  \n", "1459      6   2008        WD         Normal     147500  \n", "\n", "[1460 rows x 81 columns]"], "text/html": ["\n", "  <div id=\"df-df72e8ae-af93-471f-a9b5-d1afed570471\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Id</th>\n", "      <th>MSSubClass</th>\n", "      <th>MSZoning</th>\n", "      <th>LotFrontage</th>\n", "      <th>LotArea</th>\n", "      <th>Street</th>\n", "      <th>Alley</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>LandContour</th>\n", "      <th>Utilities</th>\n", "      <th>...</th>\n", "      <th>PoolArea</th>\n", "      <th>PoolQC</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>MiscFeature</th>\n", "      <th>MiscVal</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>YrSold</th>\n", "      <th>SaleType</th>\n", "      <th>SaleCondition</th>\n", "      <th>SalePrice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>65.0</td>\n", "      <td>8450</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>208500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>20</td>\n", "      <td>RL</td>\n", "      <td>80.0</td>\n", "      <td>9600</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>2007</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>181500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>68.0</td>\n", "      <td>11250</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>223500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>70</td>\n", "      <td>RL</td>\n", "      <td>60.0</td>\n", "      <td>9550</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2006</td>\n", "      <td>WD</td>\n", "      <td>Abnorml</td>\n", "      <td>140000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>84.0</td>\n", "      <td>14260</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1455</th>\n", "      <td>1456</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>62.0</td>\n", "      <td>7917</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>2007</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>175000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1456</th>\n", "      <td>1457</td>\n", "      <td>20</td>\n", "      <td>RL</td>\n", "      <td>85.0</td>\n", "      <td>13175</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>MnPrv</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2010</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>210000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1457</th>\n", "      <td>1458</td>\n", "      <td>70</td>\n", "      <td>RL</td>\n", "      <td>66.0</td>\n", "      <td>9042</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>GdPrv</td>\n", "      <td>Shed</td>\n", "      <td>2500</td>\n", "      <td>5</td>\n", "      <td>2010</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>266500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1458</th>\n", "      <td>1459</td>\n", "      <td>20</td>\n", "      <td>RL</td>\n", "      <td>68.0</td>\n", "      <td>9717</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2010</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>142125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1459</th>\n", "      <td>1460</td>\n", "      <td>20</td>\n", "      <td>RL</td>\n", "      <td>75.0</td>\n", "      <td>9937</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>147500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1460 rows × 81 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-df72e8ae-af93-471f-a9b5-d1afed570471')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-df72e8ae-af93-471f-a9b5-d1afed570471 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-df72e8ae-af93-471f-a9b5-d1afed570471');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-c45291f9-d6eb-49e6-8633-ad8b49fd4492\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-c45291f9-d6eb-49e6-8633-ad8b49fd4492')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-c45291f9-d6eb-49e6-8633-ad8b49fd4492 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "  <div id=\"id_8454282d-2057-453f-bb19-8c63c70c9e55\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_8454282d-2057-453f-bb19-8c63c70c9e55 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df"}}, "metadata": {}, "execution_count": 2}], "source": ["import pandas as pd\n", "df = pd.read_csv(\"train.csv\")\n", "df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "keuLd1xoiddL", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1752240500643, "user_tz": -330, "elapsed": 28, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "c71afd87-5fef-4ff6-c621-19460224af0a"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['Id',\n", " 'MSSubClass',\n", " 'MSZoning',\n", " 'LotFrontage',\n", " 'LotArea',\n", " 'Street',\n", " 'Alley',\n", " 'LotShape',\n", " 'LandContour',\n", " 'Utilities',\n", " 'LotConfig',\n", " 'LandSlope',\n", " 'Neighborhood',\n", " 'Condition1',\n", " 'Condition2',\n", " 'BldgType',\n", " 'HouseStyle',\n", " 'OverallQual',\n", " 'OverallCond',\n", " 'YearBuilt',\n", " 'YearRemodAdd',\n", " 'RoofStyle',\n", " 'RoofMatl',\n", " 'Exterior1st',\n", " 'Exterior2nd',\n", " 'MasVnrType',\n", " 'MasVnrArea',\n", " 'ExterQual',\n", " 'ExterCond',\n", " 'Foundation',\n", " 'BsmtQual',\n", " 'BsmtCond',\n", " 'BsmtExposure',\n", " 'BsmtFinType1',\n", " 'BsmtFinSF1',\n", " 'BsmtFinType2',\n", " 'BsmtFinSF2',\n", " 'BsmtUnfSF',\n", " 'TotalBsmtSF',\n", " 'Heating',\n", " 'HeatingQC',\n", " 'CentralAir',\n", " 'Electrical',\n", " '1stFlrSF',\n", " '2ndFlrSF',\n", " 'LowQualFinSF',\n", " 'GrLivArea',\n", " 'BsmtFullBath',\n", " 'BsmtHalfBath',\n", " 'FullBath',\n", " 'HalfBath',\n", " 'BedroomAbvGr',\n", " 'KitchenAbvGr',\n", " 'KitchenQual',\n", " 'TotRmsAbvGrd',\n", " 'Functional',\n", " 'Fireplaces',\n", " 'FireplaceQu',\n", " 'GarageType',\n", " 'GarageYrBlt',\n", " 'GarageF<PERSON>sh',\n", " 'GarageCars',\n", " 'GarageArea',\n", " 'GarageQual',\n", " 'GarageCond',\n", " 'PavedDrive',\n", " 'WoodDeckSF',\n", " 'OpenPorchSF',\n", " 'EnclosedPorch',\n", " '3SsnPorch',\n", " 'ScreenPorch',\n", " 'PoolArea',\n", " 'PoolQC',\n", " '<PERSON><PERSON>',\n", " 'MiscFeature',\n", " 'MiscVal',\n", " 'MoSold',\n", " 'Yr<PERSON><PERSON>',\n", " 'SaleType',\n", " 'SaleCondition',\n", " 'SalePrice']"]}, "metadata": {}, "execution_count": 3}], "source": ["df.columns.to_list()"]}, {"cell_type": "markdown", "metadata": {"id": "Ck7c4-LO6ac-"}, "source": ["## Step 2: Visualize the data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "l9cpBeWLiiRh", "executionInfo": {"status": "ok", "timestamp": 1752240519142, "user_tz": -330, "elapsed": 47, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["from matplotlib import pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "bR3cHOUWioQ0", "colab": {"base_uri": "https://localhost:8080/", "height": 447}, "executionInfo": {"status": "ok", "timestamp": 1752240690694, "user_tz": -330, "elapsed": 237, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "74004b53-14b3-48cf-f825-5a07d82f6df7"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<matplotlib.collections.PathCollection at 0x796594db5ad0>"]}, "metadata": {}, "execution_count": 8}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["plt.scatter(df['Utilities'],df['SalePrice'])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "NgbgTvYdi2u1", "colab": {"base_uri": "https://localhost:8080/", "height": 808}, "executionInfo": {"status": "ok", "timestamp": 1752241661200, "user_tz": -330, "elapsed": 937, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "20f1365f-1ede-4144-c395-eee608b9716e"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/tmp/ipython-input-9-163801837.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df[col].fillna(df[col].median(), inplace=True)\n", "/tmp/ipython-input-9-163801837.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df[col].fillna(df[col].median(), inplace=True)\n", "/tmp/ipython-input-9-163801837.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df[col].fillna(df[col].median(), inplace=True)\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["        Id  MSSubClass  MSZoning  LotFrontage  LotArea  Street  Alley  \\\n", "0        1          60         3         65.0     8450       1      2   \n", "1        2          20         3         80.0     9600       1      2   \n", "2        3          60         3         68.0    11250       1      2   \n", "3        4          70         3         60.0     9550       1      2   \n", "4        5          60         3         84.0    14260       1      2   \n", "...    ...         ...       ...          ...      ...     ...    ...   \n", "1455  1456          60         3         62.0     7917       1      2   \n", "1456  1457          20         3         85.0    13175       1      2   \n", "1457  1458          70         3         66.0     9042       1      2   \n", "1458  1459          20         3         68.0     9717       1      2   \n", "1459  1460          20         3         75.0     9937       1      2   \n", "\n", "      LotShape  LandContour  Utilities  ...  PoolArea  PoolQC  Fence  \\\n", "0            3            3          0  ...         0       3      4   \n", "1            3            3          0  ...         0       3      4   \n", "2            0            3          0  ...         0       3      4   \n", "3            0            3          0  ...         0       3      4   \n", "4            0            3          0  ...         0       3      4   \n", "...        ...          ...        ...  ...       ...     ...    ...   \n", "1455         3            3          0  ...         0       3      4   \n", "1456         3            3          0  ...         0       3      2   \n", "1457         3            3          0  ...         0       3      0   \n", "1458         3            3          0  ...         0       3      4   \n", "1459         3            3          0  ...         0       3      4   \n", "\n", "      MiscFeature  MiscVal  MoSold  YrSold  SaleType  SaleCondition  SalePrice  \n", "0               4        0       2    2008         8              4     208500  \n", "1               4        0       5    2007         8              4     181500  \n", "2               4        0       9    2008         8              4     223500  \n", "3               4        0       2    2006         8              0     140000  \n", "4               4        0      12    2008         8              4     250000  \n", "...           ...      ...     ...     ...       ...            ...        ...  \n", "1455            4        0       8    2007         8              4     175000  \n", "1456            4        0       2    2010         8              4     210000  \n", "1457            2     2500       5    2010         8              4     266500  \n", "1458            4        0       4    2010         8              4     142125  \n", "1459            4        0       6    2008         8              4     147500  \n", "\n", "[1460 rows x 81 columns]"], "text/html": ["\n", "  <div id=\"df-0a6a69f3-54db-475a-b342-2a807a1e9d3e\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Id</th>\n", "      <th>MSSubClass</th>\n", "      <th>MSZoning</th>\n", "      <th>LotFrontage</th>\n", "      <th>LotArea</th>\n", "      <th>Street</th>\n", "      <th>Alley</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>LandContour</th>\n", "      <th>Utilities</th>\n", "      <th>...</th>\n", "      <th>PoolArea</th>\n", "      <th>PoolQC</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>MiscFeature</th>\n", "      <th>MiscVal</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>YrSold</th>\n", "      <th>SaleType</th>\n", "      <th>SaleCondition</th>\n", "      <th>SalePrice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>60</td>\n", "      <td>3</td>\n", "      <td>65.0</td>\n", "      <td>8450</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2008</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>208500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>20</td>\n", "      <td>3</td>\n", "      <td>80.0</td>\n", "      <td>9600</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>2007</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>181500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>60</td>\n", "      <td>3</td>\n", "      <td>68.0</td>\n", "      <td>11250</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>2008</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>223500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>70</td>\n", "      <td>3</td>\n", "      <td>60.0</td>\n", "      <td>9550</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2006</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>140000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>60</td>\n", "      <td>3</td>\n", "      <td>84.0</td>\n", "      <td>14260</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>2008</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1455</th>\n", "      <td>1456</td>\n", "      <td>60</td>\n", "      <td>3</td>\n", "      <td>62.0</td>\n", "      <td>7917</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>2007</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>175000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1456</th>\n", "      <td>1457</td>\n", "      <td>20</td>\n", "      <td>3</td>\n", "      <td>85.0</td>\n", "      <td>13175</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2010</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>210000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1457</th>\n", "      <td>1458</td>\n", "      <td>70</td>\n", "      <td>3</td>\n", "      <td>66.0</td>\n", "      <td>9042</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2500</td>\n", "      <td>5</td>\n", "      <td>2010</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>266500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1458</th>\n", "      <td>1459</td>\n", "      <td>20</td>\n", "      <td>3</td>\n", "      <td>68.0</td>\n", "      <td>9717</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2010</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>142125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1459</th>\n", "      <td>1460</td>\n", "      <td>20</td>\n", "      <td>3</td>\n", "      <td>75.0</td>\n", "      <td>9937</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>2008</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>147500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1460 rows × 81 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-0a6a69f3-54db-475a-b342-2a807a1e9d3e')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-0a6a69f3-54db-475a-b342-2a807a1e9d3e button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-0a6a69f3-54db-475a-b342-2a807a1e9d3e');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-a0535428-c2b4-45c8-af1e-b481723006c6\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-a0535428-c2b4-45c8-af1e-b481723006c6')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-a0535428-c2b4-45c8-af1e-b481723006c6 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "  <div id=\"id_b040d193-ca39-475b-a9cb-b9a121dcbd21\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_b040d193-ca39-475b-a9cb-b9a121dcbd21 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df"}}, "metadata": {}, "execution_count": 9}], "source": ["from sklearn.preprocessing import LabelEncoder\n", "\n", "for col in df.columns:\n", "  if pd.api.types.is_numeric_dtype(df[col].dtype):\n", "    if df[col].isnull().any():\n", "      df[col].fillna(df[col].median(), inplace=True)\n", "  else:\n", "    df[col] = LabelEncoder().fit_transform(df[col])\n", "\n", "df"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "Z1IhJIPRn8Eq", "colab": {"base_uri": "https://localhost:8080/", "height": 858}, "outputId": "8b03df80-6426-47de-e2da-04ee3d219701", "executionInfo": {"status": "ok", "timestamp": 1752241749460, "user_tz": -330, "elapsed": 1702, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<Axes: >"]}, "metadata": {}, "execution_count": 10}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 500x1000 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["import seaborn as sns\n", "sns.set()\n", "\n", "top_features = df.corr()[['SalePrice']].sort_values(by=['SalePrice'],ascending=False)\n", "plt.figure(figsize=(5,10))\n", "sns.heatmap(top_features,cmap='rainbow', annot=True,annot_kws={\"size\": 16}, vmin=-1)\n"]}, {"cell_type": "markdown", "metadata": {"id": "5MDux_TA60RY"}, "source": ["## Step 3: Setup the Machine Learning"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "sGFzHa_LkonB", "colab": {"base_uri": "https://localhost:8080/", "height": 444}, "executionInfo": {"status": "ok", "timestamp": 1752242623751, "user_tz": -330, "elapsed": 60, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "1b11bb99-6407-43bf-cc47-57a38835ade7"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["        Id  MSSubClass  MSZoning  LotFrontage  LotArea  Street  Alley  \\\n", "0        1          60         3         65.0     8450       1      2   \n", "1        2          20         3         80.0     9600       1      2   \n", "2        3          60         3         68.0    11250       1      2   \n", "3        4          70         3         60.0     9550       1      2   \n", "4        5          60         3         84.0    14260       1      2   \n", "...    ...         ...       ...          ...      ...     ...    ...   \n", "1455  1456          60         3         62.0     7917       1      2   \n", "1456  1457          20         3         85.0    13175       1      2   \n", "1457  1458          70         3         66.0     9042       1      2   \n", "1458  1459          20         3         68.0     9717       1      2   \n", "1459  1460          20         3         75.0     9937       1      2   \n", "\n", "      LotShape  LandContour  Utilities  ...  ScreenPorch  PoolArea  PoolQC  \\\n", "0            3            3          0  ...            0         0       3   \n", "1            3            3          0  ...            0         0       3   \n", "2            0            3          0  ...            0         0       3   \n", "3            0            3          0  ...            0         0       3   \n", "4            0            3          0  ...            0         0       3   \n", "...        ...          ...        ...  ...          ...       ...     ...   \n", "1455         3            3          0  ...            0         0       3   \n", "1456         3            3          0  ...            0         0       3   \n", "1457         3            3          0  ...            0         0       3   \n", "1458         3            3          0  ...            0         0       3   \n", "1459         3            3          0  ...            0         0       3   \n", "\n", "      Fence  MiscFeature  MiscVal  MoSold  YrSold  SaleType  SaleCondition  \n", "0         4            4        0       2    2008         8              4  \n", "1         4            4        0       5    2007         8              4  \n", "2         4            4        0       9    2008         8              4  \n", "3         4            4        0       2    2006         8              0  \n", "4         4            4        0      12    2008         8              4  \n", "...     ...          ...      ...     ...     ...       ...            ...  \n", "1455      4            4        0       8    2007         8              4  \n", "1456      2            4        0       2    2010         8              4  \n", "1457      0            2     2500       5    2010         8              4  \n", "1458      4            4        0       4    2010         8              4  \n", "1459      4            4        0       6    2008         8              4  \n", "\n", "[1460 rows x 80 columns]"], "text/html": ["\n", "  <div id=\"df-ac6882a8-72c6-4194-9b57-d3193a0801b2\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Id</th>\n", "      <th>MSSubClass</th>\n", "      <th>MSZoning</th>\n", "      <th>LotFrontage</th>\n", "      <th>LotArea</th>\n", "      <th>Street</th>\n", "      <th>Alley</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>LandContour</th>\n", "      <th>Utilities</th>\n", "      <th>...</th>\n", "      <th>ScreenPorch</th>\n", "      <th>PoolArea</th>\n", "      <th>PoolQC</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>MiscFeature</th>\n", "      <th>MiscVal</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>YrSold</th>\n", "      <th>SaleType</th>\n", "      <th>SaleCondition</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>60</td>\n", "      <td>3</td>\n", "      <td>65.0</td>\n", "      <td>8450</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2008</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>20</td>\n", "      <td>3</td>\n", "      <td>80.0</td>\n", "      <td>9600</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>2007</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>60</td>\n", "      <td>3</td>\n", "      <td>68.0</td>\n", "      <td>11250</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>2008</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>70</td>\n", "      <td>3</td>\n", "      <td>60.0</td>\n", "      <td>9550</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2006</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>60</td>\n", "      <td>3</td>\n", "      <td>84.0</td>\n", "      <td>14260</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>2008</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1455</th>\n", "      <td>1456</td>\n", "      <td>60</td>\n", "      <td>3</td>\n", "      <td>62.0</td>\n", "      <td>7917</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>2007</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1456</th>\n", "      <td>1457</td>\n", "      <td>20</td>\n", "      <td>3</td>\n", "      <td>85.0</td>\n", "      <td>13175</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2010</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1457</th>\n", "      <td>1458</td>\n", "      <td>70</td>\n", "      <td>3</td>\n", "      <td>66.0</td>\n", "      <td>9042</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2500</td>\n", "      <td>5</td>\n", "      <td>2010</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1458</th>\n", "      <td>1459</td>\n", "      <td>20</td>\n", "      <td>3</td>\n", "      <td>68.0</td>\n", "      <td>9717</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2010</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1459</th>\n", "      <td>1460</td>\n", "      <td>20</td>\n", "      <td>3</td>\n", "      <td>75.0</td>\n", "      <td>9937</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>2008</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1460 rows × 80 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-ac6882a8-72c6-4194-9b57-d3193a0801b2')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-ac6882a8-72c6-4194-9b57-d3193a0801b2 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-ac6882a8-72c6-4194-9b57-d3193a0801b2');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-529de60a-fe82-42de-b702-b155bcb4ed26\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-529de60a-fe82-42de-b702-b155bcb4ed26')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-529de60a-fe82-42de-b702-b155bcb4ed26 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "  <div id=\"id_8dbc0bdd-49d6-429d-8653-3e7057acc9bd\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('X')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_8dbc0bdd-49d6-429d-8653-3e7057acc9bd button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('X');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "X"}}, "metadata": {}, "execution_count": 11}], "source": ["y = df[\"SalePrice\"]\n", "X = df.drop(\"SalePrice\",axis=\"columns\")\n", "X"]}, {"cell_type": "code", "source": ["y"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 458}, "id": "gG7kSA6IzPed", "executionInfo": {"status": "ok", "timestamp": 1752242656398, "user_tz": -330, "elapsed": 12, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "9af8bacd-11b7-47ef-86ca-14957991a187"}, "execution_count": 12, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0       208500\n", "1       181500\n", "2       223500\n", "3       140000\n", "4       250000\n", "         ...  \n", "1455    175000\n", "1456    210000\n", "1457    266500\n", "1458    142125\n", "1459    147500\n", "Name: <PERSON><PERSON><PERSON>, Length: 1460, dtype: int64"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SalePrice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>208500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>181500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>223500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>140000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1455</th>\n", "      <td>175000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1456</th>\n", "      <td>210000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1457</th>\n", "      <td>266500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1458</th>\n", "      <td>142125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1459</th>\n", "      <td>147500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1460 rows × 1 columns</p>\n", "</div><br><label><b>dtype:</b> int64</label>"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "GmoiHKpKk1lz", "colab": {"base_uri": "https://localhost:8080/", "height": 458}, "executionInfo": {"status": "ok", "timestamp": 1752243825745, "user_tz": -330, "elapsed": 13, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "16c2ae82-4c60-4657-e766-2f9864b38799"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["892     154500\n", "1105    325000\n", "413     115000\n", "522     159000\n", "1036    315500\n", "         ...  \n", "479      89471\n", "1361    260000\n", "802     189000\n", "651     108000\n", "722     124500\n", "Name: <PERSON><PERSON><PERSON>, Length: 292, dtype: int64"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SalePrice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>892</th>\n", "      <td>154500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1105</th>\n", "      <td>325000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>413</th>\n", "      <td>115000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>522</th>\n", "      <td>159000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1036</th>\n", "      <td>315500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>479</th>\n", "      <td>89471</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1361</th>\n", "      <td>260000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>802</th>\n", "      <td>189000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>651</th>\n", "      <td>108000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>722</th>\n", "      <td>124500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>292 rows × 1 columns</p>\n", "</div><br><label><b>dtype:</b> int64</label>"]}, "metadata": {}, "execution_count": 23}], "source": ["from sklearn.model_selection import train_test_split\n", "X_train, X_test, y_train, y_test = train_test_split(X,y,test_size=0.2, random_state=42)\n", "y_test"]}, {"cell_type": "code", "source": ["from sklearn.preprocessing import StandardScaler\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)"], "metadata": {"id": "wqidRJ9eBSsH", "executionInfo": {"status": "ok", "timestamp": 1752243584855, "user_tz": -330, "elapsed": 13, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["X_test_scaled"], "metadata": {"id": "4f2W8sDDKPvZ", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1752243589822, "user_tz": -330, "elapsed": 13, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "fe76a6c3-a1b4-442a-e326-d6c49000baf9"}, "execution_count": 17, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 0.38123238, -0.8667643 , -0.05479609, ..., -1.37548612,\n", "         0.31666197,  0.20177167],\n", "       [ 0.88218848,  0.07410996, -0.05479609, ...,  1.65006527,\n", "         0.31666197,  0.20177167],\n", "       [-0.74533088, -0.63154574,  1.58627662, ...,  1.65006527,\n", "         0.31666197,  0.20177167],\n", "       ...,\n", "       [ 0.16956079,  0.07410996, -0.05479609, ...,  0.13728958,\n", "         0.31666197,  0.20177167],\n", "       [-0.18557711,  0.30932853, -0.05479609, ...,  0.89367742,\n", "         0.31666197,  0.20177167],\n", "       [-0.01859174, -0.8667643 , -0.05479609, ...,  0.89367742,\n", "         0.31666197,  0.20177167]])"]}, "metadata": {}, "execution_count": 17}]}, {"cell_type": "code", "source": ["X_test"], "metadata": {"id": "sRGswuh-f2dG"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "V681Njz_lT67", "colab": {"base_uri": "https://localhost:8080/", "height": 80}, "executionInfo": {"status": "ok", "timestamp": 1752243421132, "user_tz": -330, "elapsed": 399, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "55e54966-2b65-44c9-f3f3-3d688d568521"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["LinearRegression()"], "text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LinearRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>LinearRegression</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.6/modules/generated/sklearn.linear_model.LinearRegression.html\">?<span>Documentation for LinearRegression</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\"><pre>LinearRegression()</pre></div> </div></div></div></div>"]}, "metadata": {}, "execution_count": 14}], "source": ["from sklearn.linear_model import LinearRegression\n", "trainer = LinearRegression()\n", "trainer.fit(X_train,y_train)"]}, {"cell_type": "markdown", "metadata": {"id": "7p3RSuJ66881"}, "source": ["## Step 4: Score and Understand"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "IYdoM5Frld_6", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1752243453954, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "a5cd1063-3336-47d7-ff18-0805d4658075"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0.8437676047326469"]}, "metadata": {}, "execution_count": 15}], "source": ["trainer.score(X_test,y_test)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"id": "L5loWiqHRG86", "executionInfo": {"status": "ok", "timestamp": 1752243996184, "user_tz": -330, "elapsed": 44, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["import pickle\n", "with open('model.pkl', 'wb') as file:\n", "    pickle.dump(trainer, file)"]}, {"cell_type": "code", "source": ["print(\"Coefficients (slopes) of the parameters:\")\n", "for feature, coef in zip(trainer.feature_names_in_, trainer.coef_):\n", "    print(f\"{feature}: {coef}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HaR9BwGQcmrx", "executionInfo": {"status": "ok", "timestamp": 1752244499311, "user_tz": -330, "elapsed": 46, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "f8913a50-26fc-4344-d093-9075a679a3ad"}, "execution_count": 25, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Coefficients (slopes) of the parameters:\n", "Id: -2.694494082492922\n", "MSSubClass: -132.06223627528993\n", "MSZoning: -2096.9419999110396\n", "LotFrontage: -257.14772982635475\n", "LotArea: 0.3586756601555354\n", "Street: 19024.521679558347\n", "Alley: 4500.218072045649\n", "LotShape: -1072.6486552612068\n", "LandContour: 3094.0074311101375\n", "Utilities: -56222.54698151979\n", "LotConfig: 81.38545584307275\n", "LandSlope: 10953.996750499822\n", "Neighborhood: 414.1843921451069\n", "Condition1: -801.4885662992119\n", "Condition2: -10890.935089723776\n", "BldgType: -2266.9871441793975\n", "HouseStyle: -880.8651430943066\n", "OverallQual: 10856.868166438828\n", "OverallCond: 5063.346814082684\n", "YearBuilt: 215.2636677782359\n", "YearRemodAdd: 47.591312219088195\n", "RoofStyle: 2550.6506037263257\n", "RoofMatl: 5551.666533061562\n", "Exterior1st: -1504.932314793248\n", "Exterior2nd: 832.6697177834455\n", "MasVnrType: 4678.487135731605\n", "MasVnrArea: 36.8342267119267\n", "ExterQual: -8423.239278007313\n", "ExterCond: 388.307433045068\n", "Foundation: 831.0597031809838\n", "BsmtQual: -9727.101029281384\n", "BsmtCond: 3315.5593758909818\n", "BsmtExposure: -3641.4786565650556\n", "BsmtFinType1: -419.0155256889757\n", "BsmtFinSF1: 2.0953702862179853\n", "BsmtFinType2: 1162.2436427837815\n", "BsmtFinSF2: 3.1269243725546403\n", "BsmtUnfSF: -5.209178360948499\n", "TotalBsmtSF: 0.013116619531501783\n", "Heating: -538.7504754923693\n", "HeatingQC: -524.591601825502\n", "CentralAir: -250.78002088532412\n", "Electrical: -382.67356372025785\n", "1stFlrSF: 26.635735208910774\n", "2ndFlrSF: 17.036849556912784\n", "LowQualFinSF: -20.872907341528844\n", "GrLivArea: 22.799677379402965\n", "BsmtFullBath: 7398.068281717085\n", "BsmtHalfBath: -3499.950663950733\n", "FullBath: 2533.1199572126575\n", "HalfBath: -881.0865408225275\n", "BedroomAbvGr: -1611.5474389937697\n", "KitchenAbvGr: -12252.39775981617\n", "KitchenQual: -9395.189536843158\n", "TotRmsAbvGrd: 3987.666298186096\n", "Functional: 4738.738725570192\n", "Fireplaces: 4231.136943496263\n", "FireplaceQu: -904.4257569542074\n", "GarageType: -115.14482581243192\n", "GarageYrBlt: -4.670511247941249\n", "GarageFinish: 921.3769416220566\n", "GarageCars: 12199.330691737881\n", "GarageArea: -1.956622458572383\n", "GarageQual: -122.21129743133861\n", "GarageCond: 2564.3254824303767\n", "PavedDrive: 624.8422092156652\n", "WoodDeckSF: 18.85442488404078\n", "OpenPorchSF: 0.7742860109974572\n", "EnclosedPorch: -11.081296861034389\n", "3SsnPorch: 46.56110841362079\n", "ScreenPorch: 45.69909518354325\n", "PoolArea: -345.45968744381435\n", "PoolQC: -104332.97154982225\n", "Fence: 487.25025851791906\n", "MiscFeature: 2071.9849584017543\n", "MiscVal: 0.6291679399273562\n", "MoSold: -189.43630703175586\n", "YrSold: -570.3855798393972\n", "SaleType: -549.3012785550122\n", "SaleCondition: 1985.8581014362328\n"]}]}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "R0Vda9wn7ozA", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1752244852436, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "ad6dc99f-ffdd-4fa5-e2ec-22be17ada027"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["1\n", "Actual price:  325000 Predicted Price:  [318334.31404784]\n"]}], "source": ["# Let's pick a random house and check the results\n", "import random\n", "my_choice = random.randrange(len(X_test))\n", "print(my_choice)\n", "print(\"Actual price: \", y_test.iloc[my_choice], \"Predicted Price: \", trainer.predict(X_test.iloc[[my_choice]]))"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}