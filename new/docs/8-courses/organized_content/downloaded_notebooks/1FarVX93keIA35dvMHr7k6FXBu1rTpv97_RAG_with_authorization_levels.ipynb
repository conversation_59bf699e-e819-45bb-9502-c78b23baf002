{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["#Modern AI Pro: Building Auth Levels on RAG Queries.\n", "How do you ensure that different users get different results based on their auth level?"], "metadata": {"id": "5vqnhLcagqJn"}}, {"cell_type": "markdown", "source": ["![image.png](data:image/png;base64,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****************************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)"], "metadata": {"id": "IhfOHN__hNED"}}, {"cell_type": "code", "source": ["!pip install -q chromadb\n", "import chromadb\n", "chroma_client = chromadb.Client()\n", "collection = chroma_client.create_collection(name=\"auth_testing2\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3lAHbOEDdEq4", "executionInfo": {"status": "ok", "timestamp": 1754109415033, "user_tz": -330, "elapsed": 32932, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "7a0d6436-5995-4631-a3f3-2be4582f2ede"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/67.3 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.3/67.3 kB\u001b[0m \u001b[31m3.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m19.5/19.5 MB\u001b[0m \u001b[31m80.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m284.2/284.2 kB\u001b[0m \u001b[31m18.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.9/1.9 MB\u001b[0m \u001b[31m65.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m103.1/103.1 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m16.5/16.5 MB\u001b[0m \u001b[31m91.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m65.6/65.6 kB\u001b[0m \u001b[31m4.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m72.5/72.5 kB\u001b[0m \u001b[31m4.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m120.0/120.0 kB\u001b[0m \u001b[31m7.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.6/201.6 kB\u001b[0m \u001b[31m10.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m105.4/105.4 kB\u001b[0m \u001b[31m7.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.4/71.4 kB\u001b[0m \u001b[31m4.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m459.8/459.8 kB\u001b[0m \u001b[31m26.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.0/4.0 MB\u001b[0m \u001b[31m79.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m453.1/453.1 kB\u001b[0m \u001b[31m24.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for pypika (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n"]}]}, {"cell_type": "markdown", "source": ["## We will use 3 levels of role authorization and build a sample DB with different type sof content."], "metadata": {"id": "kBp16YqRhSz0"}}, {"cell_type": "code", "source": ["# Document 1: Public financial summary (auth_level 1)\n", "collection.add(documents=[\"This is a public summary of the financial performance in Q1. Overall, the company has shown...\"],\n", "               metadatas =[{\"auth_level\": 1, \"category\": \"Summary\", \"quarter\": \"Q1\", \"year\": \"2024\", \"department\": \"sales/GCC\"}],\n", "               ids=\"id1\")\n", "\n", "# Document 2: Internal financial analysis (auth_level 2)\n", "collection.add(documents=[\"Internal analysis reveals that the Q1 performance, while positive, indicates potential risks...\"],\n", "               metadatas =[{\"auth_level\": 2, \"category\": \"Internal Analysis\", \"quarter\": \"Q1\", \"year\": \"2024\"}],\n", "               ids=\"id2\")\n", "\n", "# Document 3: Confidential investment strategy (auth_level 3)\n", "collection.add(documents=[\"Confidential: The investment strategy for Q2 involves focusing on emerging markets...\"],\n", "               metadatas =[{\"auth_level\": 3, \"category\": \"Investment Strategy\",  \"source\": \"mywebsite.com\", \"quarter\": \"Q2\", \"year\": \"2024\"}],\n", "               ids=\"id3\")"], "metadata": {"id": "xEShNjsDd6cj", "executionInfo": {"status": "ok", "timestamp": 1754109429508, "user_tz": -330, "elapsed": 4529, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "6a922734-114e-488a-b360-ccc94b6a17bb"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/root/.cache/chroma/onnx_models/all-MiniLM-L6-v2/onnx.tar.gz: 100%|██████████| 79.3M/79.3M [00:01<00:00, 70.6MiB/s]\n"]}]}, {"cell_type": "markdown", "source": ["## Query with auth levels"], "metadata": {"id": "5P18dTcchdEN"}}, {"cell_type": "code", "source": ["def vector_search_with_rbac(query_text, user_auth_level):\n", "    if not isinstance(user_auth_level, int) or user_auth_level < 0:\n", "        raise ValueError(\"Invalid user_auth_level. Must be a non-negative integer.\")\n", "\n", "    try:\n", "        results = collection.query(query_texts=[query_text],n_results=10, where={\"auth_level\": user_auth_level})\n", "        return results\n", "    except Exception as e:\n", "        # Handle or log the error appropriately\n", "        print(f\"An error occurred: {e}\")\n", "        return []"], "metadata": {"id": "1RshLhXuXzYj", "executionInfo": {"status": "ok", "timestamp": 1754109498758, "user_tz": -330, "elapsed": 16, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["vector_search_with_rbac(\"Tell me about the performance for this quarter\",1)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "whXyVK61fusz", "executionInfo": {"status": "ok", "timestamp": 1754109515874, "user_tz": -330, "elapsed": 474, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "f3979906-ed61-4e64-cf99-f73ddffb510f"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'ids': [['id1']],\n", " 'embeddings': None,\n", " 'documents': [['This is a public summary of the financial performance in Q1. Overall, the company has shown...']],\n", " 'uris': None,\n", " 'included': ['metadatas', 'documents', 'distances'],\n", " 'data': None,\n", " 'metadatas': [[{'year': '2024',\n", "    'auth_level': 1,\n", "    'quarter': 'Q1',\n", "    'category': 'Summary',\n", "    'department': 'sales/GCC'}]],\n", " 'distances': [[1.0514402389526367]]}"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["vector_search_with_rbac(\"Tell me about the performance for this quarter\",2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6RuQISvMfwP6", "executionInfo": {"status": "ok", "timestamp": 1754109549822, "user_tz": -330, "elapsed": 424, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "36c0465f-4cd2-4c79-bbe7-5aa062ebf55e"}, "execution_count": 5, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'ids': [['id2']],\n", " 'embeddings': None,\n", " 'documents': [['Internal analysis reveals that the Q1 performance, while positive, indicates potential risks...']],\n", " 'uris': None,\n", " 'included': ['metadatas', 'documents', 'distances'],\n", " 'data': None,\n", " 'metadatas': [[{'year': '2024',\n", "    'auth_level': 2,\n", "    'quarter': 'Q1',\n", "    'category': 'Internal Analysis'}]],\n", " 'distances': [[1.3275322914123535]]}"]}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "code", "source": ["vector_search_with_rbac(\"Tell me about the performance for this quarter\",3)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OSaDaRwafwtH", "executionInfo": {"status": "ok", "timestamp": 1754109557622, "user_tz": -330, "elapsed": 367, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "077f5ccc-768f-44cd-e3f0-8c7a5bc09e41"}, "execution_count": 6, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'ids': [['id3']],\n", " 'embeddings': None,\n", " 'documents': [['Confidential: The investment strategy for Q2 involves focusing on emerging markets...']],\n", " 'uris': None,\n", " 'included': ['metadatas', 'documents', 'distances'],\n", " 'data': None,\n", " 'metadatas': [[{'year': '2024',\n", "    'quarter': 'Q2',\n", "    'auth_level': 3,\n", "    'category': 'Investment Strategy',\n", "    'source': 'mywebsite.com'}]],\n", " 'distances': [[1.5121922492980957]]}"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "code", "source": [], "metadata": {"id": "pXo5xSgPa4QD"}, "execution_count": null, "outputs": []}]}