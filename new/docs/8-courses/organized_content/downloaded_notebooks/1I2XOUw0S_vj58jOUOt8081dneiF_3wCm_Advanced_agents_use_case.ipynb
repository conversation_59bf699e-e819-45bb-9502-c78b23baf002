{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyNU1u8F+rfdRhg8VIIhI4ki"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "source": ["!pip install -U -q langchain_groq langchain_community gradio langgraph nasapy polygon arxiv"], "metadata": {"id": "KFitQqx5g-dr", "executionInfo": {"status": "ok", "timestamp": 1741489613280, "user_tz": 480, "elapsed": 3642, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": 23, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "\n", "from langchain_groq import ChatGroq\n", "from google.colab import userdata\n", "llm_groq = ChatGroq(model_name=\"llama-3.3-70b-versatile\", api_key=userdata.get(\"GROQ_API_KEY\"))\n", "os.environ[\"TAVILY_API_KEY\"] = userdata.get(\"TAVILY_API_SEARCH\")\n", "os.environ[\"POLYGON_API_KEY\"] = userdata.get(\"POLYGON_API_KEY\")"], "metadata": {"id": "zdmDTQKTga8j", "executionInfo": {"status": "ok", "timestamp": 1741489614367, "user_tz": 480, "elapsed": 1089, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": 24, "outputs": []}, {"cell_type": "code", "source": ["from datetime import datetime\n", "import json\n", "from langchain_core.messages import HumanMessage\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.prebuilt import create_react_agent"], "metadata": {"id": "Zl6LuJ_dgebN", "executionInfo": {"status": "ok", "timestamp": 1741489614378, "user_tz": 480, "elapsed": 3, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": 25, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.tools import tool # For custom tools\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "from langchain_community.tools.arxiv import ArxivQueryRun\n", "from langchain_community.utilities.polygon import PolygonAPIWrapper\n", "from langchain_community.tools.polygon import PolygonFinancials, PolygonAggregates, PolygonTickerNews, PolygonLastQuote\n", "from langchain_community.tools.youtube.search import YouTubeSearchTool\n", "from nasapy import Nasa\n", "import polygon"], "metadata": {"id": "ORPMc5CjgpmE", "executionInfo": {"status": "ok", "timestamp": 1741489614391, "user_tz": 480, "elapsed": 12, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": 26, "outputs": []}, {"cell_type": "code", "execution_count": 27, "metadata": {"id": "QlVOyijTgWAq", "executionInfo": {"status": "ok", "timestamp": 1741489614464, "user_tz": 480, "elapsed": 70, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["\"\"\"\n", "We will first setup the tools needed to run by the agent. We will use search engine API call\n", "and one custom function that will at this point do nothing.\n", "\n", "You can find standard list of tools here: https://python.langchain.com/v0.2/docs/integrations/tools/\n", "A lot of them are noise, but some of them are useful.\n", "\"\"\"\n", "search = TavilySearchResults(max_results=2)\n", "paper = ArxivQueryRun()\n", "yt = YouTubeSearchTool()\n", "stocks_client = polygon.StocksClient(os.getenv(\"POLYGON_API_KEY\"))\n", "stock_financials = PolygonFinancials(api_wrapper = PolygonAPIWrapper())\n", "stock_ticker_news = PolygonTickerNews(api_wrapper = PolygonAPIWrapper())\n", "\n", "@tool\n", "def get_hotel(location: str, at_time: datetime)->str:\n", "    '''Returns hotel options for reservation'''\n", "    return '''There is a beautiful Motel 6 available for that date'''\n", "\n", "@tool\n", "def get_nasa_pic_of_day()->str:\n", "    \"\"\"Get NASA's Astronomy Picture of the Day today.\"\"\"\n", "    date = datetime.now().strftime(\"%Y-%m-%d\")\n", "    nasa = Nasa()\n", "    apod = nasa.picture_of_the_day(date, hd=True)\n", "    return apod['url']\n", "\n", "@tool\n", "def get_stock_price(ticker: str)->str:\n", "    '''Returns the latest stock price'''\n", "    return stocks_client.get_previous_close(ticker)"]}, {"cell_type": "code", "source": ["tools = [search, paper, stock_financials, yt, stock_ticker_news, get_hotel, get_nasa_pic_of_day, get_stock_price]\n", "prompt = \"\"\"We will be using tools to solve a variety of user problems. This will be displayed on Gradio.\n", "              The output has to be in markdown format to display properly. For weather and search you will use Tavily\"\"\"\n", "memory = MemorySaver()\n", "agent_executor = create_react_agent(llm_groq, tools, checkpointer=memory, state_modifier=prompt)\n", "config = {\"configurable\": {\"thread_id\": \"id111\"}} # Hardcoded -- this is actually to switch different threads of conversations"], "metadata": {"id": "URnJeDP3hpN4", "executionInfo": {"status": "ok", "timestamp": 1741489614759, "user_tz": 480, "elapsed": 294, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": 28, "outputs": []}, {"cell_type": "code", "source": ["# Setup the function to handle chat messages from the user\n", "def chat(message, history):\n", "    tokens_used = 0\n", "    tool_name = None\n", "    query = None\n", "    for chunk in agent_executor.stream(\n", "        {\"messages\": [HumanMessage(content=message)]}, config):\n", "\n", "        #print(chunk)\n", "        agent_data = chunk.get(\"agent\", None)\n", "\n", "        if agent_data is not None:\n", "            messages = agent_data.get(\"messages\", None)\n", "\n", "            if messages is not None and len(messages) > 0:\n", "                message = messages[0]  # Corrected to refer to the first message\n", "                tool_calls = message.additional_kwargs.get(\"tool_calls\", None)\n", "\n", "                if tool_calls is not None and len(tool_calls) > 0:\n", "                    tool_name = tool_calls[0].get(\"function\", {}).get(\"name\", None)\n", "                    arguments_str = tool_calls[0].get(\"function\", {}).get(\"arguments\", '{}')\n", "                    #print(tool_calls)\n", "                    arguments = json.loads(arguments_str)  # Parse the JSON string into a dictionary\n", "                    query = arguments.get(\"query\", None)  # Now safely access the query\n", "\n", "                tokens_used = message.response_metadata['token_usage']['total_tokens']\n", "                if tool_name is not None and query is not None:\n", "                    yield message.content + \"\\nTokens used so far: \" + str(tokens_used)+\"\\nTool used: \"+tool_name+\"\\nQuery: \"+query\n", "\n", "                elif tool_name is not None:\n", "                    yield message.content + \"\\nTokens used so far: \" + str(tokens_used)+\"\\nTool used: \"+tool_name\n", "\n", "                else:\n", "                    yield message.content"], "metadata": {"id": "GTzw22p3iGg8", "executionInfo": {"status": "ok", "timestamp": 1741489614760, "user_tz": 480, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["import gradio as gr\n", "demo = gr.<PERSON><PERSON>(\n", "    fn=chat,\n", "    title=\"Advanced chat agent\",\n", ")\n", "demo.launch(debug=True)\n", "\n", "# can you do hotel booking?\n", "# Get <PERSON><PERSON>'s picture of the day\n", "# Nutrition info or home made roti\n", "# how many Infosys stocks can i buy for $1000?\n", "# financials for AAPL\n", "# compare that against NVDA\n", "# which stock is better in comparison\n", "# get me latest news for MSFT ticker"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "62SH2Cg_iJ8Z", "outputId": "282379ff-b2cf-4ce5-ba0a-d1bf9445a4e9"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/gradio/chat_interface.py:334: UserWarning: The 'tuples' format for chatbot messages is deprecated and will be removed in a future version of Gradio. Please set type='messages' instead, which uses openai-style 'role' and 'content' keys.\n", "  self.chatbot = Chatbot(\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Running Gradio in a Colab notebook requires sharing enabled. Automatically setting `share=True` (you can turn this off by setting `share=False` in `launch()` explicitly).\n", "\n", "Colab notebook detected. This cell will run indefinitely so that you can see errors and logs. To turn off, set debug=False in launch().\n", "* Running on public URL: https://0344556cbc18ccd6a1.gradio.live\n", "\n", "This share link expires in 72 hours. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"https://0344556cbc18ccd6a1.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["Traceback (most recent call last):\n", "  File \"/usr/local/lib/python3.11/dist-packages/gradio/queueing.py\", line 715, in process_events\n", "    response = await route_utils.call_process_api(\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/gradio/route_utils.py\", line 322, in call_process_api\n", "    output = await app.get_blocks().process_api(\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/gradio/blocks.py\", line 2103, in process_api\n", "    result = await self.call_function(\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/gradio/blocks.py\", line 1662, in call_function\n", "    prediction = await utils.async_iteration(iterator)\n", "                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/gradio/utils.py\", line 735, in async_iteration\n", "    return await anext(iterator)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/gradio/utils.py\", line 840, in asyncgen_wrapper\n", "    response = await iterator.__anext__()\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/gradio/chat_interface.py\", line 911, in _stream_fn\n", "    async for response in generator:\n", "  File \"/usr/local/lib/python3.11/dist-packages/gradio/utils.py\", line 729, in __anext__\n", "    return await anyio.to_thread.run_sync(\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "           ^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/gradio/utils.py\", line 712, in run_sync_iterator_async\n", "    return next(iterator)\n", "           ^^^^^^^^^^^^^^\n", "  File \"<ipython-input-29-6e2befa9e20a>\", line 26, in chat\n", "    tokens_used = message.response_metadata['token_usage']['total_tokens']\n", "                  ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^\n", "KeyError: 'token_usage'\n"]}]}]}