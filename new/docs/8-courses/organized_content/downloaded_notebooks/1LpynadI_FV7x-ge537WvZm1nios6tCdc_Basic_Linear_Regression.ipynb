{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [{"file_id": "1i5uwJoF87KxEzmqb7_8OKQti6x_W8svN", "timestamp": 1704434147145}], "authorship_tag": "ABX9TyM+cRvy+hK9OGgKP+AAn3Jx"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["## Welcome to the basic linear regression tutorial"], "metadata": {"id": "0UNfVglGvzbz"}}, {"cell_type": "code", "source": ["import numpy as np\n", "import pandas as pd\n", "from matplotlib import pyplot as plt\n", "from sklearn import linear_model"], "metadata": {"id": "d9qH2SiWJkLU"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["File can be downloaded from: [Link](https://drive.google.com/file/d/1Wg-GKOIY8hXTNW3zq7pJ9bXD_vy_3le-/view?usp=sharing)"], "metadata": {"id": "jX9wqM-YNPDF"}}, {"cell_type": "code", "source": ["!pip install -q gdown"], "metadata": {"id": "_fYh8IrKNdw5"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["!gdown \"1Wg-GKOIY8hXTNW3zq7pJ9bXD_vy_3le-\""], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DneJo7aMNj3a", "executionInfo": {"status": "ok", "timestamp": 1706272665440, "user_tz": -330, "elapsed": 1020, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "4a7afc80-59f3-4c82-d16a-fe2aa70d2018"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Downloading...\n", "From: https://drive.google.com/uc?id=1Wg-GKOIY8hXTNW3zq7pJ9bXD_vy_3le-\n", "To: /content/homes.csv\n", "\r  0% 0.00/75.0 [00:00<?, ?B/s]\r100% 75.0/75.0 [00:00<00:00, 293kB/s]\n"]}]}, {"cell_type": "code", "source": ["df = pd.read_csv('homes.csv')"], "metadata": {"id": "Nva_S6LyKA8m"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["df"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "WBMbd0qWKGPB", "executionInfo": {"status": "ok", "timestamp": 1706273037959, "user_tz": -330, "elapsed": 15, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "22dbbfb5-c8be-4b3d-f3eb-6c70d6bb4662"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   area   price\n", "0  2600  550000\n", "1  3000  565000\n", "2  3200  610000\n", "3  3600  680000\n", "4  4000  725000"], "text/html": ["\n", "  <div id=\"df-ca1487b7-51ae-4c9c-a901-99f217fa02b4\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>area</th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2600</td>\n", "      <td>550000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3000</td>\n", "      <td>565000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3200</td>\n", "      <td>610000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3600</td>\n", "      <td>680000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4000</td>\n", "      <td>725000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-ca1487b7-51ae-4c9c-a901-99f217fa02b4')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-ca1487b7-51ae-4c9c-a901-99f217fa02b4 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-ca1487b7-51ae-4c9c-a901-99f217fa02b4');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-dadfb1b1-3973-45a8-9763-390ef4649fe3\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-dadfb1b1-3973-45a8-9763-390ef4649fe3')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-dadfb1b1-3973-45a8-9763-390ef4649fe3 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"]}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "code", "source": ["%matplotlib inline"], "metadata": {"id": "m5zsGWFxKG_o"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["plt.xlabel('area in sqft')\n", "plt.ylabel('price in USD')\n", "plt.scatter(df.area,df.price,color='red',marker='+')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 466}, "id": "SAkQhQw_KM8q", "executionInfo": {"status": "ok", "timestamp": 1704434805725, "user_tz": -330, "elapsed": 1456, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "c2730256-372d-4607-bfc1-9600c986493a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<matplotlib.collections.PathCollection at 0x7864b34b7ac0>"]}, "metadata": {}, "execution_count": 30}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["new_df = df.drop('price',axis='columns')\n", "# new_df\n", "area = df.area.to_frame()\n", "type(area)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DfEO6XZAKYuX", "executionInfo": {"status": "ok", "timestamp": 1704434809176, "user_tz": -330, "elapsed": 7, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "819db420-f7e5-4af1-fe84-b6003358c144"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["pandas.core.frame.DataFrame"]}, "metadata": {}, "execution_count": 31}]}, {"cell_type": "code", "source": ["price = df.price\n", "price"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "y1zz4FAdKksx", "executionInfo": {"status": "ok", "timestamp": 1704434811558, "user_tz": -330, "elapsed": 455, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "a2b63c45-a8c9-4df2-c570-e6b3b771d5a7"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    550000\n", "1    565000\n", "2    610000\n", "3    680000\n", "4    725000\n", "Name: price, dtype: int64"]}, "metadata": {}, "execution_count": 32}]}, {"cell_type": "code", "source": ["reg = linear_model.LinearRegression()\n", "reg.fit(area,price)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 75}, "id": "gFG1s7SaK2Hd", "executionInfo": {"status": "ok", "timestamp": 1704434814771, "user_tz": -330, "elapsed": 634, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "6b5c998d-4746-4119-a274-5df71e89bf3e"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["LinearRegression()"], "text/html": ["<style>#sk-container-id-2 {color: black;background-color: white;}#sk-container-id-2 pre{padding: 0;}#sk-container-id-2 div.sk-toggleable {background-color: white;}#sk-container-id-2 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-2 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-2 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-2 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-2 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-2 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-2 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-2 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-2 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-2 div.sk-item {position: relative;z-index: 1;}#sk-container-id-2 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-2 div.sk-item::before, #sk-container-id-2 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-2 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-2 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-2 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-2 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-2 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-2 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-2 div.sk-label-container {text-align: center;}#sk-container-id-2 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-2 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LinearRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LinearRegression</label><div class=\"sk-toggleable__content\"><pre>LinearRegression()</pre></div></div></div></div></div>"]}, "metadata": {}, "execution_count": 33}]}, {"cell_type": "code", "source": ["reg.predict([[3300]])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2EiWRZBjLDjp", "executionInfo": {"status": "ok", "timestamp": 1704434816389, "user_tz": -330, "elapsed": 3, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "d9042387-4992-4f15-ccbc-966a3dff9dd7"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/sklearn/base.py:439: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["array([628715.75342466])"]}, "metadata": {}, "execution_count": 34}]}, {"cell_type": "code", "source": ["reg.coef_"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "C7JfzkSBLLTv", "executionInfo": {"status": "ok", "timestamp": 1704434818413, "user_tz": -330, "elapsed": 610, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "5a380ea3-23ef-4b14-df29-5b761b852fc1"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([135.78767123])"]}, "metadata": {}, "execution_count": 35}]}, {"cell_type": "code", "source": ["reg.intercept_"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8eKbDW2tLUU_", "executionInfo": {"status": "ok", "timestamp": 1704434818888, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "35381f49-76eb-4301-f9f0-f67caca81940"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["180616.43835616432"]}, "metadata": {}, "execution_count": 36}]}, {"cell_type": "markdown", "source": ["### Y = m * X + b (m is coefficient and b is intercept)"], "metadata": {"id": "SPdgs8UDLb5_"}}, {"cell_type": "code", "source": ["3300*135.78767123 + 180616.43835616432"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jAl4u9hXLWQv", "executionInfo": {"status": "ok", "timestamp": 1704434820715, "user_tz": -330, "elapsed": 9, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "f580eae5-2ac1-4e84-f301-13036b153125"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["628715.7534151643"]}, "metadata": {}, "execution_count": 37}]}, {"cell_type": "code", "source": ["# Predict price of a home with area = 5000 sqr ft\n", "reg.predict([[5000]])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xSlz8EUuLjot", "executionInfo": {"status": "ok", "timestamp": 1704434821162, "user_tz": -330, "elapsed": 3, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "1a29968b-a220-43d7-eee1-a34131339407"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/sklearn/base.py:439: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["array([859554.79452055])"]}, "metadata": {}, "execution_count": 38}]}, {"cell_type": "code", "source": ["a = np.array([2000,2500,3000])"], "metadata": {"id": "Yvg1nEy1LnFs"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["a"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FgGOgv4VL_mi", "executionInfo": {"status": "ok", "timestamp": 1704434823720, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "2784eb8b-9cbe-4492-e5ec-af84499002fd"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([2000, 2500, 3000])"]}, "metadata": {}, "execution_count": 40}]}, {"cell_type": "code", "source": ["areas = pd.DataFrame(a,columns=['area'])"], "metadata": {"id": "6Mt0MF7jMARS"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["areas"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 143}, "id": "xPrUucT_MJiV", "executionInfo": {"status": "ok", "timestamp": 1704434825857, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "b8513b32-4c10-4214-eabb-da6fb173f7a4"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   area\n", "0  2000\n", "1  2500\n", "2  3000"], "text/html": ["\n", "  <div id=\"df-9018722a-a238-47f0-bead-e2fc5bc0da28\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>area</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-9018722a-a238-47f0-bead-e2fc5bc0da28')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-9018722a-a238-47f0-bead-e2fc5bc0da28 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-9018722a-a238-47f0-bead-e2fc5bc0da28');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-69e2c9d4-acb3-4a7c-a88c-40c36d8e7b00\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-69e2c9d4-acb3-4a7c-a88c-40c36d8e7b00')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-69e2c9d4-acb3-4a7c-a88c-40c36d8e7b00 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"]}, "metadata": {}, "execution_count": 42}]}, {"cell_type": "code", "source": ["#predict for a whole set of houses\n", "reg.predict(areas)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lCZK1S06MTDM", "executionInfo": {"status": "ok", "timestamp": 1704434827079, "user_tz": -330, "elapsed": 5, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "32d6bd38-559b-43bc-b234-0941afe5e3c9"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([452191.78082192, 520085.61643836, 587979.45205479])"]}, "metadata": {}, "execution_count": 43}]}, {"cell_type": "code", "source": ["p = reg.predict(areas)\n", "areas['prices'] = p\n", "areas"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 143}, "id": "ljcNRvDUMVuL", "executionInfo": {"status": "ok", "timestamp": 1704434828487, "user_tz": -330, "elapsed": 14, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "a64e1e4a-3ce3-4b51-eff9-803b3c592b6d"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   area         prices\n", "0  2000  452191.780822\n", "1  2500  520085.616438\n", "2  3000  587979.452055"], "text/html": ["\n", "  <div id=\"df-61d5a69b-19b4-46a4-8a59-e7e2ba3c7ac4\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>area</th>\n", "      <th>prices</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2000</td>\n", "      <td>452191.780822</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2500</td>\n", "      <td>520085.616438</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3000</td>\n", "      <td>587979.452055</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-61d5a69b-19b4-46a4-8a59-e7e2ba3c7ac4')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-61d5a69b-19b4-46a4-8a59-e7e2ba3c7ac4 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-61d5a69b-19b4-46a4-8a59-e7e2ba3c7ac4');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-1d2cb2c6-4b9b-49f0-a9b8-898949343a71\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-1d2cb2c6-4b9b-49f0-a9b8-898949343a71')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-1d2cb2c6-4b9b-49f0-a9b8-898949343a71 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"]}, "metadata": {}, "execution_count": 44}]}, {"cell_type": "code", "source": ["areas.to_csv('output.csv')"], "metadata": {"id": "7E-lkM80M9HC"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["plt.xlabel('area of home')\n", "plt.ylabel('home price')\n", "plt.scatter(df.area,df.price,color='red',marker='x')\n", "plt.plot(df.area,reg.predict(df[['area']]))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 466}, "id": "9fCbLjr3NDT5", "executionInfo": {"status": "ok", "timestamp": 1704434830612, "user_tz": -330, "elapsed": 842, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "c9993295-1723-4cd9-aed2-244371b3428a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7864b325fe80>]"]}, "metadata": {}, "execution_count": 46}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": [], "metadata": {"id": "pL2Oa8UtO21d"}, "execution_count": null, "outputs": []}]}