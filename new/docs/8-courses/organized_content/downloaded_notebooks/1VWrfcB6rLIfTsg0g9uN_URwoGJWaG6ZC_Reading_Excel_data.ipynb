{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyPAuEaohEvtqoM2pppbQHbK"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["#Modern AI Pro\n", "Boiler plate for processing excel.\n", "Note: You cannot run this code here. It requires a Windows environment. Copy and run it within your Win environment."], "metadata": {"id": "fGmqrXsiUUP4"}}, {"cell_type": "code", "source": ["!pip install pywin32 openpyxl"], "metadata": {"id": "U4HpVib_UN1O"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jq-rOfE6pNp6"}, "outputs": [], "source": ["# Extract charts\n", "from openpyxl import load_workbook\n", "\n", "# Load the workbook\n", "wb = load_workbook('excel_for_rag.xlsx')\n", "\n", "# Iterate through sheets\n", "for sheet in wb.sheetnames:\n", "    ws = wb[sheet]\n", "    # Extract data\n", "    data = ws.values\n", "    # Extract charts\n", "    charts = ws._charts\n", "    for chart in charts:\n", "        # Process chart (e.g., save or analyze)\n", "        pass"]}, {"cell_type": "code", "source": ["# Extract macros.\n", "import win32com.client\n", "# Initialize Excel application\n", "excel = win32com.client.Dispatch(\"Excel.Application\")\n", "workbook = excel.Workbooks.Open('macro_excel_file.xlsm')\n", "\n", "# Access VBA project\n", "vba_project = workbook.VBProject\n", "for component in vba_project.VBComponents:\n", "    if component.Type == 1:  # Standard module\n", "        code_module = component.CodeModule\n", "        code = code_module.Lines(1, code_module.CountOfLines)\n", "        # Process or save the extracted code\n", "        print(f\"Module: {component.Name}\")\n", "        print(code)\n", "\n", "# Close the workbook\n", "workbook.Close(SaveChanges=False)\n", "excel.Application.Quit()"], "metadata": {"id": "UbXp3vgrULEb"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "i0GQE16uUL2F"}, "execution_count": null, "outputs": []}]}