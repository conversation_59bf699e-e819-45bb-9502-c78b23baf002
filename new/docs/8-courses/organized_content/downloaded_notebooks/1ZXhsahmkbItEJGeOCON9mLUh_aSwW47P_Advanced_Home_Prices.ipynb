{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"background_save": true, "base_uri": "https://localhost:8080/"}, "id": "PiPVz1h_Ajke"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["house-prices-advanced-regression-techniques.zip: Skipping, found more recently modified local copy (use --force to force download)\n", "Archive:  /content/house-prices-advanced-regression-techniques.zip\n", "replace data_description.txt? [y]es, [n]o, [A]ll, [N]one, [r]ename: "]}], "source": ["from google.colab import userdata\n", "from os import environ\n", "\n", "environ[\"KAGGLE_KEY\"]      = userdata.get('KAGGLE_KEY')\n", "environ[\"KAGGLE_USERNAME\"] = userdata.get('KAGGLE_USERNAME')\n", "\n", "! pip -q install kaggle\n", "! kaggle competitions download -c house-prices-advanced-regression-techniques\n", "! unzip /content/house-prices-advanced-regression-techniques.zip"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "uBzt-8yUDOEu"}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.read_csv(\"train.csv\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "u4i5zbakOsS4"}, "outputs": [], "source": ["from sklearn.preprocessing import LabelEncoder\n", "for column in df.columns:\n", "    if not pd.api.types.is_numeric_dtype(df[column].dtype):\n", "      df[column] = LabelEncoder().fit_transform(df[column])\n", "\n", "    else:\n", "      if df[column].isnull().any():\n", "        df[column].fillna(df[column].median(),inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2UBuqVkLPpHK"}, "outputs": [], "source": ["y = df[\"SalePrice\"]\n", "X = df.drop(\"SalePrice\",axis=\"columns\")\n", "\n", "from sklearn.model_selection import train_test_split\n", "X_train, X_test, y_train, y_test = train_test_split(X,y,test_size=0.2,random_state=3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CuhAF1kT_QbQ"}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "vPqWWgHZQDIc"}, "outputs": [], "source": ["X_test.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "sjSuIaMSPgyi"}, "outputs": [], "source": ["import numpy as np\n", "\n", "def print_accuracy(model):\n", "  model.fit(X_train, y_train)\n", "  y_pred = model.predict(X_test)\n", "  model_name = model.__class__.__name__\n", "\n", "  from sklearn.metrics import mean_absolute_error,r2_score,mean_squared_error\n", "  print(\"Accuracy for model: \", model_name)\n", "  print(\"R Squared : \", r2_score(y_test, y_pred))\n", "  print(\"Mean Absolute Error :\", round(mean_absolute_error(y_test,y_pred)))\n", "  print(\"RMSE:\",round(np.sqrt(mean_squared_error(y_test, y_pred))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "PAgRuRfpPa0G"}, "outputs": [], "source": ["def run_popular_regression_models():\n", "  from sklearn.ensemble import RandomForestRegressor\n", "  model = RandomForestRegressor(n_estimators = 9, max_depth = 6, random_state = 0, )\n", "  print_accuracy(model)\n", "\n", "  from sklearn.linear_model import ElasticNet\n", "  model = ElasticNet()\n", "  print_accuracy(model)\n", "\n", "  from xgboost.sklearn import XGBRegressor\n", "  model = XGBRegressor(max_depth=7, random_state = 0,learning_rate=0.2)\n", "  print_accuracy(model)\n", "\n", "  from sklearn.ensemble import GradientBoostingRegressor\n", "  model = GradientBoostingRegressor()\n", "  print_accuracy(model)\n", "\n", "  from sklearn.linear_model import BayesianRidge\n", "  model = BayesianRidge()\n", "  print_accuracy(model)\n", "\n", "  from sklearn.linear_model import LinearRegression\n", "  model = LinearRegression()\n", "  print_accuracy(model)\n", "\n", "  from sklearn.neural_network import MLPRegressor\n", "  model = MLPRegressor(random_state=0,max_iter=1000)\n", "  print_accuracy(model)\n", "\n", "run_popular_regression_models()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "AKoGJFCVFMo6"}, "outputs": [], "source": ["from matplotlib import pyplot as plt\n", "import seaborn as sns\n", "\n", "top_features = df.corr()[['SalePrice']].sort_values(by=['SalePrice'],ascending=False).head(30)\n", "plt.figure(figsize=(5,10))\n", "sns.heatmap(top_features, cmap='rainbow',annot=True,annot_kws={\"size\": 16}, vmin=-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3k-A-3aBFOIY"}, "outputs": [], "source": ["#Let's load the data again\n", "df = pd.read_csv(\"train.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "giVayeEAE5ov"}, "outputs": [], "source": ["df['TotalSquareFeet'] = (df['BsmtFinSF1'] + df['BsmtFinSF2'] + df['1stFlrSF'] + df['2ndFlrSF'] + df['TotalBsmtSF'])\n", "\n", "df['TotalBath'] = (df['FullBath'] + (0.5 * df['HalfBath']) + df['BsmtFullBath'] + (0.5 * df['BsmtHalfBath']))\n", "\n", "df['TotalPorchArea'] = (df['OpenPorchSF'] + df['3SsnPorch'] + df['EnclosedPorch'] + df['ScreenPorch'] + df['WoodDeckSF'])\n", "\n", "df['SqFtPerRoom'] = df['GrLivArea'] / (df['TotRmsAbvGrd'] + df['FullBath'] + df['HalfBath'] + df['KitchenAbvGr'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 256}, "executionInfo": {"elapsed": 221, "status": "ok", "timestamp": 1727556821471, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "giUDXcfBDb4f", "outputId": "961bd238-7c93-483f-b267-b05fab596e04"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "X"}, "text/html": ["\n", "  <div id=\"df-36d570c6-ff41-4cd3-8fbd-88ab1f31b63a\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Id</th>\n", "      <th>MSSubClass</th>\n", "      <th>LotFrontage</th>\n", "      <th>LotArea</th>\n", "      <th>OverallQual</th>\n", "      <th>OverallCond</th>\n", "      <th>YearBuilt</th>\n", "      <th>YearRemodAdd</th>\n", "      <th>MasVnrArea</th>\n", "      <th>BsmtFinSF1</th>\n", "      <th>...</th>\n", "      <th>SaleType_ConLI</th>\n", "      <th>SaleType_ConLw</th>\n", "      <th>SaleType_New</th>\n", "      <th>SaleType_Oth</th>\n", "      <th>SaleType_WD</th>\n", "      <th>SaleCondition_AdjLand</th>\n", "      <th>SaleCondition_Alloca</th>\n", "      <th>SaleCondition_Family</th>\n", "      <th>SaleCondition_Normal</th>\n", "      <th>SaleCondition_Partial</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>60</td>\n", "      <td>65.0</td>\n", "      <td>8450</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>2003</td>\n", "      <td>2003</td>\n", "      <td>196.0</td>\n", "      <td>706</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>20</td>\n", "      <td>80.0</td>\n", "      <td>9600</td>\n", "      <td>6</td>\n", "      <td>8</td>\n", "      <td>1976</td>\n", "      <td>1976</td>\n", "      <td>0.0</td>\n", "      <td>978</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>60</td>\n", "      <td>68.0</td>\n", "      <td>11250</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>2001</td>\n", "      <td>2002</td>\n", "      <td>162.0</td>\n", "      <td>486</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>70</td>\n", "      <td>60.0</td>\n", "      <td>9550</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>1915</td>\n", "      <td>1970</td>\n", "      <td>0.0</td>\n", "      <td>216</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>60</td>\n", "      <td>84.0</td>\n", "      <td>14260</td>\n", "      <td>8</td>\n", "      <td>5</td>\n", "      <td>2000</td>\n", "      <td>2000</td>\n", "      <td>350.0</td>\n", "      <td>655</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 250 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-36d570c6-ff41-4cd3-8fbd-88ab1f31b63a')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-36d570c6-ff41-4cd3-8fbd-88ab1f31b63a button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-36d570c6-ff41-4cd3-8fbd-88ab1f31b63a');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-1ff9bd40-4cba-4c18-b4ba-c0f8219f92b8\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-1ff9bd40-4cba-4c18-b4ba-c0f8219f92b8')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-1ff9bd40-4cba-4c18-b4ba-c0f8219f92b8 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["   Id  MSSubClass  LotFrontage  LotArea  OverallQual  OverallCond  YearBuilt  \\\n", "0   1          60         65.0     8450            7            5       2003   \n", "1   2          20         80.0     9600            6            8       1976   \n", "2   3          60         68.0    11250            7            5       2001   \n", "3   4          70         60.0     9550            7            5       1915   \n", "4   5          60         84.0    14260            8            5       2000   \n", "\n", "   YearRemodAdd  MasVnrArea  BsmtFinSF1  ...  SaleType_ConLI  SaleType_ConLw  \\\n", "0          2003       196.0         706  ...               0               0   \n", "1          1976         0.0         978  ...               0               0   \n", "2          2002       162.0         486  ...               0               0   \n", "3          1970         0.0         216  ...               0               0   \n", "4          2000       350.0         655  ...               0               0   \n", "\n", "   SaleType_New  SaleType_Oth  SaleType_WD  SaleCondition_AdjLand  \\\n", "0             0             0            1                      0   \n", "1             0             0            1                      0   \n", "2             0             0            1                      0   \n", "3             0             0            1                      0   \n", "4             0             0            1                      0   \n", "\n", "   SaleCondition_Alloca  SaleCondition_Family  SaleCondition_Normal  \\\n", "0                     0                     0                     1   \n", "1                     0                     0                     1   \n", "2                     0                     0                     1   \n", "3                     0                     0                     0   \n", "4                     0                     0                     1   \n", "\n", "   SaleCondition_Partial  \n", "0                      0  \n", "1                      0  \n", "2                      0  \n", "3                      0  \n", "4                      0  \n", "\n", "[5 rows x 250 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["y = df[\"SalePrice\"]\n", "X = df.drop(\"SalePrice\",axis=\"columns\")\n", "\n", "##\n", "X = pd.get_dummies(df,drop_first=True, dtype=int)\n", "for col in X.columns:\n", "  if pd.api.types.is_numeric_dtype(X[col].dtype):\n", "    if X[col].isnull().any():\n", "      X[col].fillna(X[col].median(),inplace=True)\n", "\n", "X.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 854}, "executionInfo": {"elapsed": 1306, "status": "ok", "timestamp": 1727556891029, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "mzxQQyvkiLpa", "outputId": "55eca632-3078-4f93-a62e-851c4523d647"}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 500x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["top_features = X.corr()[['SalePrice']].sort_values(by=['SalePrice'],ascending=False).head(30)\n", "plt.figure(figsize=(5,10))\n", "sns.heatmap(top_features, cmap='rainbow',annot=True,annot_kws={\"size\": 16}, vmin=-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "g8AkMbYbQsOJ"}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = train_test_split(X,y,test_size=0.2,random_state=3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 9224, "status": "ok", "timestamp": 1727556905751, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "RbthWyXk9rET", "outputId": "3c40f00e-82d0-4cca-b071-62d638e2e301"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy for model:  RandomForestRegressor\n", "R Squared :  0.9996818653121553\n", "Mean Absolute Error : 759\n", "RMSE: 1253\n", "Accuracy for model:  ElasticNet\n", "R Squared :  0.9999999998549429\n", "Mean Absolute Error : 1\n", "RMSE: 1\n", "Accuracy for model:  XGBRegressor\n", "R Squared :  0.9935542345046997\n", "Mean Absolute Error : 949\n", "RMSE: 5642\n", "Accuracy for model:  GradientBoostingRegressor\n", "R Squared :  0.9998407915347217\n", "Mean Absolute Error : 628\n", "RMSE: 887\n", "Accuracy for model:  BayesianRidge\n", "R Squared :  -576890549365.3712\n", "Mean Absolute Error : 6942187654\n", "RMSE: 53371872828\n", "Accuracy for model:  LinearRegression\n", "R Squared :  1.0\n", "Mean Absolute Error : 0\n", "RMSE: 0\n", "Accuracy for model:  MLPRegressor\n", "R Squared :  0.9999937039708456\n", "Mean Absolute Error : 135\n", "RMSE: 176\n"]}], "source": ["run_popular_regression_models()"]}, {"cell_type": "markdown", "metadata": {"id": "m2kjSDvg_uZx"}, "source": ["![image.png](data:image/png;base64,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)"]}], "metadata": {"colab": {"authorship_tag": "ABX9TyM4xquL+DWS5NvvR2hwUMuV", "name": "", "version": ""}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}