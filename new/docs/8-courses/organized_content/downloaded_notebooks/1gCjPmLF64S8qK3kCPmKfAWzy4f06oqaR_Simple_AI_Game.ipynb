{"cells": [{"cell_type": "markdown", "metadata": {"id": "oyjiXjFyBIr_"}, "source": ["# Modern AI Pro\n", "Let's understand the foundation of LLMs."]}, {"cell_type": "markdown", "metadata": {"id": "jWQMgHRacdfu"}, "source": ["##0. Token prediction by humans"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "zDzAzGCndzTU", "executionInfo": {"status": "ok", "timestamp": 1752292225936, "user_tz": -330, "elapsed": 217, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["# Simple Next Token Prediction Game for Beginners\n", "import requests\n", "import random\n", "import re\n", "def get_book_text():\n", "    \"\"\"Load Alice in Wonderland from Project Gutenberg\"\"\"\n", "    url = \"https://www.gutenberg.org/files/11/11-0.txt\"\n", "    response = requests.get(url)\n", "    text = response.text\n", "\n", "    # Extract main content between markers\n", "    if \"*** START OF\" in text and \"*** END OF\" in text:\n", "        text = text.split(\"*** START OF\")[1].split(\"*** END OF\")[0]\n", "\n", "    return text\n", "\n", "def get_good_sentences(text, min_length=15, max_length=30):\n", "    \"\"\"Get sentences with appropriate length\"\"\"\n", "    # Find sentences ending with punctuation\n", "    sentences = re.findall(r'[A-Z][^.!?]*[.!?]', text)\n", "\n", "    # Filter by word count\n", "    good_sentences = []\n", "    for s in sentences:\n", "        s = s.strip()\n", "        words = s.split()\n", "        if min_length <= len(words) <= max_length:\n", "            good_sentences.append(s)\n", "\n", "    return good_sentences"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "rwRZdde3chD0", "executionInfo": {"status": "ok", "timestamp": 1752292229145, "user_tz": -330, "elapsed": 14, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["def play_continuous_game(sentences, num_sentences=3):\n", "    \"\"\"Play continuous prediction within each sentence\"\"\"\n", "    print(\"Welcome to Continuous Next Token Prediction!\")\n", "    print(\"After each prediction, I'll show the correct word\")\n", "    print(\"and ask you to predict the next one.\\n\")\n", "\n", "    score = 0\n", "    total_predictions = 0\n", "\n", "    for round_num in range(1, num_sentences+1):\n", "        # Select a sentence\n", "        sentence = sentences[round_num % len(sentences)]\n", "        words = sentence.split()\n", "\n", "        # Start with first 5 words\n", "        revealed_count = 5\n", "        revealed = words[:revealed_count]\n", "\n", "        print(f\"\\n--- Sentence {round_num}/{num_sentences} ---\")\n", "        print(f\"Starting context: \\\"{' '.join(revealed)}...\\\"\")\n", "\n", "        # Continue predicting until end of sentence\n", "        while revealed_count < len(words):\n", "            # Get prediction for next word\n", "            print(\"\\nPredict the next word:\")\n", "            prediction = input(\"> \").strip().lower()\n", "\n", "            # Clean prediction (first word only)\n", "            if \" \" in prediction:\n", "                prediction = prediction.split()[0]\n", "            prediction = re.sub(r'[^\\w\\']', '', prediction)\n", "\n", "            # Get actual next word\n", "            next_word = words[revealed_count]\n", "            clean_next = re.sub(r'[^\\w\\']', '', next_word.lower())\n", "\n", "            # Score\n", "            if prediction == clean_next:\n", "                result = \"CORRECT!\"\n", "                round_score = 1\n", "            else:\n", "                result = \"INCORRECT\"\n", "                round_score = 0\n", "\n", "            score += round_score\n", "            total_predictions += 1\n", "\n", "            # Show result\n", "            print(f\"{result} The next word is: \\\"{next_word}\\\"\")\n", "\n", "            # Reveal next word\n", "            revealed_count += 1\n", "            revealed = words[:revealed_count]\n", "            print(f\"Context now: \\\"{' '.join(revealed)}...\\\"\")\n", "\n", "            # If near end, show how many words remain\n", "            if len(words) - revealed_count <= 3:\n", "                print(f\"({len(words) - revealed_count} words remaining in this sentence)\")\n", "\n", "        # Show complete sentence\n", "        print(f\"\\nComplete sentence: \\\"{sentence}\\\"\")\n", "        print(f\"You made {total_predictions} predictions in this round.\")\n", "\n", "    # Final score\n", "    accuracy = (score / total_predictions) * 100 if total_predictions > 0 else 0\n", "    print(f\"\\nGame Over! You got {score} out of {total_predictions} correct.\")\n", "    print(f\"Accuracy: {accuracy:.1f}%\")\n", "\n", "    if accuracy > 20:\n", "        print(\"Excellent! You're showing strong language prediction skills.\")\n", "    elif accuracy > 10:\n", "        print(\"Good job! You're better than random guessing.\")\n", "    else:\n", "        print(\"Keep practicing! Next-token prediction is challenging.\")\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "SAHns28SdbF9", "colab": {"base_uri": "https://localhost:8080/", "height": 860}, "executionInfo": {"status": "error", "timestamp": 1752293197088, "user_tz": -330, "elapsed": 963774, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "6c8b270c-4ad2-4ae0-a9d3-22e6f335bc69"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Loading text...\n", "Found 373 good sentences!\n", "Welcome to Continuous Next Token Prediction!\n", "After each prediction, I'll show the correct word\n", "and ask you to predict the next one.\n", "\n", "\n", "--- Sentence 1/3 ---\n", "Starting context: \"There was nothing so _very_...\"\n", "\n", "Predict the next word:\n", "> funny\n", "INCORRECT The next word is: \"remarkable\"\n", "Context now: \"There was nothing so _very_ remarkable...\"\n", "\n", "Predict the next word:\n", "> about\n", "INCORRECT The next word is: \"in\"\n", "Context now: \"There was nothing so _very_ remarkable in...\"\n", "\n", "Predict the next word:\n", "> the\n", "INCORRECT The next word is: \"that;\"\n", "Context now: \"There was nothing so _very_ remarkable in that;...\"\n", "\n", "Predict the next word:\n", "> land\n", "INCORRECT The next word is: \"nor\"\n", "Context now: \"There was nothing so _very_ remarkable in that; nor...\"\n", "\n", "Predict the next word:\n"]}, {"output_type": "error", "ename": "KeyboardInterrupt", "evalue": "Interrupted by user", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipython-input-3-2771318689.py\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m \u001b[0;31m# Start the game with 5 rounds\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 8\u001b[0;31m \u001b[0mplay_continuous_game\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msentences\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m3\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m/tmp/ipython-input-2-3614832916.py\u001b[0m in \u001b[0;36mplay_continuous_game\u001b[0;34m(sentences, num_sentences)\u001b[0m\n\u001b[1;32m     24\u001b[0m             \u001b[0;31m# Get prediction for next word\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     25\u001b[0m             \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"\\nPredict the next word:\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 26\u001b[0;31m             \u001b[0mprediction\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0minput\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"> \"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mstrip\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlower\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     27\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     28\u001b[0m             \u001b[0;31m# Clean prediction (first word only)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/ipykernel/kernelbase.py\u001b[0m in \u001b[0;36mraw_input\u001b[0;34m(self, prompt)\u001b[0m\n\u001b[1;32m   1175\u001b[0m                 \u001b[0;34m\"raw_input was called, but this frontend does not support input requests.\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1176\u001b[0m             )\n\u001b[0;32m-> 1177\u001b[0;31m         return self._input_request(\n\u001b[0m\u001b[1;32m   1178\u001b[0m             \u001b[0mstr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mprompt\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1179\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_parent_ident\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"shell\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/ipykernel/kernelbase.py\u001b[0m in \u001b[0;36m_input_request\u001b[0;34m(self, prompt, ident, parent, password)\u001b[0m\n\u001b[1;32m   1217\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mKeyboardInterrupt\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1218\u001b[0m                 \u001b[0;31m# re-raise KeyboardInterrupt, to truncate traceback\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1219\u001b[0;31m                 \u001b[0;32mraise\u001b[0m \u001b[0mKeyboardInterrupt\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"Interrupted by user\"\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1220\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mException\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1221\u001b[0m                 \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlog\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwarning\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"Invalid Message:\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mexc_info\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: Interrupted by user"]}], "source": ["# Run the game\n", "print(\"Loading text...\")\n", "text = get_book_text()\n", "sentences = get_good_sentences(text)\n", "print(f\"Found {len(sentences)} good sentences!\")\n", "\n", "# Start the game with 5 rounds\n", "play_continuous_game(sentences, 3)\n"]}, {"cell_type": "markdown", "metadata": {"id": "Tb-8igsEBMom"}, "source": ["## 1. Testing tokenizers"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "1uArD-uTfIhY", "executionInfo": {"status": "ok", "timestamp": 1752293216043, "user_tz": -330, "elapsed": 16263, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["from transformers import AutoTokenizer"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "IsWh1F29fPGP", "colab": {"base_uri": "https://localhost:8080/", "height": 226, "referenced_widgets": ["d48d0ba367334a348856ae29953c1259", "05f28f7152584315a5a4e574357c8fd3", "ec92f3590823462292d4794365982780", "06a6e42627d94787918d655853ab0371", "3db5f3ed81604b5193af56c29da97bc0", "0f830998a27a4420801fd942b606711a", "fc074449b1894c65b12fc2abdbe76a45", "26384b05389f4491aa310bc3b5d926f6", "4b63110e6b2e47f39c6b0d9fa37da4d8", "462706d327c84f78aa86ff9b89ae15d2", "625f19303b324602813ba50237b3e18f", "ff3988961f14497695988d88d8f71390", "1ff19e6164cf4a48afdc5a0cbea0cb18", "174fa3200fd34fe288eade9dab2ba074", "23d695717eb94cb2ab4eaa9588b2994b", "3214ef43d58c4f10aa18a7c6a8c56c96", "65abda340e164ba2b66c09364a5e5605", "144ff1f8cb6a4efdaa4496d50b299985", "8e462679a2784e92a0d214131ce4df8f", "12d813e235cf40ac9b6cbf5ef9c8c2de", "3d1d7498201d451999e1779b9c09a532", "9f8c7c0817f74806aff3e84a1fe9c827", "897366d7f9ff4ef580ad213cd38d5fba", "fdda9e0618bf44e3a78b6b7611e8e1a5", "d1eda33bba8a4bb1af00e0f04e30f57f", "85b5f6340609434fbef308cbe701240d", "a572e53b52ad4679b7e647739384ff6d", "22e74d740437457c9e189677b49b10c9", "9f9c444803fd410d91ec5269c097026c", "05e16536c6344a34ad91b60ff3788d2c", "81a73be351c341eda46f2fb993ae6160", "115cbcc86dbf47f7b9907d4ad32a2d57", "2b216d66eaf549d0bf73e5755adb34b0", "fb9c923068df45a58b1c891e7bdd394b", "f749a542fa7c4ccaac23047db11ec145", "b4c574a57324437693761e5c18c205e5", "bd212936bfde45b3aa0da58a9236809b", "4e9d7fa2bf3242ea9851b483e009a5df", "5e308698c1674cd593e93697520b0df8", "f44d4617ea014ffc85de500f7b923a63", "c6ab8d1bfdea48c9885223b09c712051", "7e4a12d98deb4f6ba1bd82c8676ed6af", "c6d4f063a80f4a71a8161680057ec2c8", "1fcd0987e15247ae9ab8f1cba05be5f0", "9e27e1a757044596a15550bf1ba5618a", "9e3e6f735d294c73a0ccf4c832b38356", "c87b5a8051fb4dd8b5e9fe3f7695008f", "accde97a9c2c4e8fa7398beec6b2379c", "13ddc74b137349198e38e23badf4fed3", "b0ca3c61d16d4747905340f971b9d549", "13a4e9910aa1436ca3646c7b33941c7e", "859a6cd9b2244559a422a4ad4c9c003f", "dc718f683bc64df5b089393926a0cda3", "f93f041536ae48d9b2e1bd8ac8f861dd", "222f15061d5f4065a3acf4138a65f617", "34f63941bd5a4abd8129209067502fe9", "c664150e19844116825d386321339658", "f2b897b52f144196a39c3cf87776f5c5", "f3ee1b3207d041e1b0d001f64cbc3803", "c44d99682bcb4b66b7e012f9ac77159e", "175a990146a9425cad040857822c5c61", "77114b64f2674b3c95151402cdddad18", "f3ebadeb1448479092882798540c0779", "17e97b1138824f1c9a07cfd3707e8b8c", "22a9cb8838fc469b9aa5d9d41b9957eb", "0080318cb0b44526b3f6968b1d95be3a"]}, "executionInfo": {"status": "ok", "timestamp": 1752293229762, "user_tz": -330, "elapsed": 4731, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "111dc6c0-6500-491a-c024-00825cba17ce"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d48d0ba367334a348856ae29953c1259"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.json: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ff3988961f14497695988d88d8f71390"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["merges.txt: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "897366d7f9ff4ef580ad213cd38d5fba"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "fb9c923068df45a58b1c891e7bdd394b"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["added_tokens.json: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "9e27e1a757044596a15550bf1ba5618a"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/95.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "34f63941bd5a4abd8129209067502fe9"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["100352\n"]}], "source": ["model1 = \"deepseek-ai/DeepSeek-R1\"\n", "model2 = \"microsoft/phi-4\"\n", "model3 = \"NousResearch/Llama-2-7b-chat-hf\"\n", "tokenizer = AutoTokenizer.from_pretrained(model2)\n", "print(len(tokenizer))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "8rNhgMTeBrCe", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1752293275790, "user_tz": -330, "elapsed": 44, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "4ff86923-9439-4b20-83b8-16b8094aaf30"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[10254, 3035, 543, 333, 4193, 321, 4633, 4683, 532, 307, 78287]"]}, "metadata": {}, "execution_count": 6}], "source": ["tokenizer.encode(\"Supercallifragilisticexpialidocious\")"]}, {"cell_type": "markdown", "source": ["[0, 28671, 26606, 394, 3174, 321, 435, 722, 66562, 536, 329, 95790]"], "metadata": {"id": "OcOxTssEPQk4"}}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "87wawZXMByBT", "colab": {"base_uri": "https://localhost:8080/", "height": 35}, "executionInfo": {"status": "ok", "timestamp": 1752293312624, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "65cff680-7fd0-4b15-fba4-4cc90b73f225"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["' overweight'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 9}], "source": ["tokenizer.decode(50254)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "8xyeW0m4fR7I", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1752293439096, "user_tz": -330, "elapsed": 10, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "8f944adb-2525-4588-9a01-10ba257f94fd"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Encoded IDs: [49552, 15592, 1322, 23902, 3001]\n", "Begin decoding\n", "49552 Modern\n", "15592  AI\n", "1322  Pro\n", "23902  rocks\n", "3001 !!\n"]}], "source": ["sentence = \"Modern AI Pro rocks!!\"\n", "token_ids = tokenizer(sentence).input_ids\n", "print(\"Encoded IDs:\",token_ids)\n", "print(\"Begin decoding\")\n", "for id in token_ids:\n", "    print(id, tokenizer.decode(id))\n", "#CLS stands for classification"]}, {"cell_type": "markdown", "metadata": {"id": "skJ-61qmBifG"}, "source": ["## 2. Testing token prediction\n", "Autoregressive generation"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "qGxyLzlyCGAx", "executionInfo": {"status": "ok", "timestamp": 1752293537507, "user_tz": -330, "elapsed": 24282, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 273, "referenced_widgets": ["71bfef27b83d4f799ac73968efab21ea", "f24c5867b29b477dbeaa495903c065bf", "fd20bf68826d4b6a8ce2090e477c21fd", "128fffd564524add8d61c56eb6a0f8a3", "9fa6083700f54dc2a13b302fa4a05a7e", "55dc5317a5ac4e468f1cfa5b62d7b1d0", "98c8d44a75da4c1f87647a10c98c3d68", "185e3806190f4697ad56eff94fa95586", "dc4b0956871148199a8cf2cf364b0d01", "a354aadc194b4146a211b6f784d29b17", "62753cff558347f992bc5c6df28d201c", "a92ae03bf72b463289de6265dba34ca4", "0d266f88a8e944e1bb47031294548ac7", "136817b1a2a846c9b557052a1360f46e", "67b040f8f14842fd93b57bc243c37780", "b1183a1dc3b64b35b436e3b459fe20cb", "ef7c352ac80c4c5086f2dbdb79f47f39", "4928409e01134f10b843ec9867a0fe8e", "4856ce60e2564d2c9649f121f828a0b7", "d31cb673fc4e4470ae020b00332c85fb", "7c7aa683bd7e459b802448fcd6e863ed", "0db8341fdc464879b745b2b24c250a5c", "b927bf97c0a64cc28794486f618bd60c", "d3059f9f1ada4bddad1f6bf0e4cbf67d", "fdd21afbc805444aa15d7715fb340a0e", "3a99b43346f644c4999f7be983cc6f2d", "a870a52b49534b6a88fe34a298b770e7", "788e3699eb9c426c971d1089abe98a7f", "5884aa8d8af645c49cd2ff71283067f0", "17529bf4e5d9402a93010a47966b1d2e", "522eec1c7de540a7bfb755f4f65bf038", "6ad5b1651cf14fea8b66c9c0ba249f90", "3347e5c9107a48509308ae604b60b432", "c154ad11c6d84e639283a50d6b28b06f", "6a7d92f545a9430b9acc873fa27db0ff", "a037e220272f443ea93e884bf6c5dbc7", "c78980a4260f4bb59baad1392d4dee37", "c0def3fc63d24844a9dbd954e6122ccf", "32a8e45718c240feb476ccdbda597ee2", "a40da4744d98418b8b944fc4273d0e2c", "7e763ee611cf4683be27907aeb714e48", "d160523c0785401b94a6edae4c7de803", "8b86db93011c42da88485e302f0df92a", "315800104ac641538b58e010fb42ba38", "ad2ac0941cd542f9b9ffef692ec9a4f6", "11d986c994114b10802578348eb440db", "4363ef8693474d8bb0d0ceeb22817992", "90c1577e5a744a369311c67d643e88f6", "25031303c7314d4ea7d31153772dee2a", "7ba6e59fed114dc2885803e907d4ac65", "d8aaf9b882164f6eba2ee8c57efd36da", "dbae867e020d4c918dd7288851d1b6e3", "dabfbe333ebc4acd8aff106a5159d1cd", "76e3aef63de843169b458a100886d635", "e1bf33fefe394941b31346e384a5763c", "e986e3d765444f9bbeb29edcbcb7cbde", "95bf3a8d3e754042817105f15e767d09", "efc98b73927449b89884520e7ddb5b75", "fbcb51e2f815453ca073562c054b103d", "234fb3f43f9e49e6b2ef76f30a0b8c71", "155080796640406dba822aa1659aa234", "6934968002674abab015e71df42055c3", "df016e1767c545698aa1496fd4fcf0ca", "2bd942f256f04c05af69f0961536fde9", "ad27a3aa71f44dfb9390ea98d6550f29", "287c214f8dd94a41b1eb03eb912ebc39", "08653bbec56a45c4ab5524065a9868e4", "cd4e46e0d3684760a7e88b42dc72e281", "9bb894060326463b8083743f370e42bc", "dbe9f848181a4d64bd4dc46612f96290", "55b1be08b76d4881a48c30e1341f5077", "9759bb7b3b7546c0a0a85ddb9935a05a", "131f57df94c844a0a50269f44fb212a6", "77149a99e724420d8350a7c9cbaf0b7b", "48b87eedeea04710b0f9cfcbf6008489", "bcf56eddf1d7485481da64a0da2a6eec", "1e19579af7c5449cb802163ac43f86f6", "4b1cb32a43bd408a8de23cff8d420d82", "bb8826de96ab47fdb04647c0fd1d477c", "2d768916999a4fe7939bb9a290c7cb48", "c90cf977d0cc40949b0a5d01270899c0", "1d241be42cd84377a98687536b935b3e", "d884618d916e4552a81d90a6724eedec", "21855e8e0c7f4a41ae1c684d2ac94b36", "4773c957de434d68a250f87cc1f094a1", "7546dd3d5112491fbd0b0b87ad798700", "89347f2eaa584157a529cdd152f37007", "450185fc1c064caba72d012b732c7e1e"]}, "outputId": "b0dd2b2f-acb4-4e61-cc99-541dd684e536"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "71bfef27b83d4f799ac73968efab21ea"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.json: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a92ae03bf72b463289de6265dba34ca4"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["merges.txt: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b927bf97c0a64cc28794486f618bd60c"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "c154ad11c6d84e639283a50d6b28b06f"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/655 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ad2ac0941cd542f9b9ffef692ec9a4f6"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/861 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e986e3d765444f9bbeb29edcbcb7cbde"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/269M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "08653bbec56a45c4ab5524065a9868e4"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["generation_config.json:   0%|          | 0.00/132 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "4b1cb32a43bd408a8de23cff8d420d82"}}, "metadata": {}}], "source": ["from transformers import AutoModelForCausalLM\n", "model_name = \"HuggingFaceTB/SmolLM2-135M-Instruct\"\n", "tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "model = AutoModelForCausalLM.from_pretrained(model_name).to(\"cpu\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "Ejmd6KiiCZoz", "executionInfo": {"status": "ok", "timestamp": 1752293959404, "user_tz": -330, "elapsed": 11, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["import torch\n", "import torch.nn.functional as F\n", "def predict_next_token(text, num_tokens=5, temperature=0):\n", "    # Convert text to model format\n", "    tokens = tokenizer.encode(text, return_tensors=\"pt\")\n", "\n", "    # Get model prediction\n", "    output = model(tokens)\n", "\n", "    # Focus on the last position (next token)\n", "    next_token_scores = output.logits[0, -1, :]\n", "\n", "    #Apply temperature (higher = more random)\n", "    if temperature > 0:\n", "        next_token_scores = next_token_scores / temperature\n", "\n", "    # Convert scores to probabilities (0-1)\n", "    probabilities = F.softmax(next_token_scores, dim=-1)\n", "\n", "    # Get top predictions\n", "    top_probs, top_ids = torch.topk(probabilities, num_tokens)\n", "\n", "    # Show results\n", "    print(f\"After '{text}', the model predicts:\")\n", "    print(\"-\" * 80)\n", "    print(\"Top tokens possible: \",top_ids)\n", "    for i, (prob, token_id) in enumerate(zip(top_probs, top_ids)):\n", "        token_text = tokenizer.decode(token_id)\n", "        percentage = prob * 100\n", "        print(f\"{i+1}. '{token_text}',  - {percentage:.1f}%, id: {token_id}\")\n", "\n", "    # Sample the next token based on the probability distribution\n", "    next_token_id = torch.multinomial(probabilities, num_samples=1).item()\n", "    next_token_text = tokenizer.decode(next_token_id)\n", "    next_token_prob = probabilities[next_token_id].item() * 100\n", "\n", "    print(f\"FINAL PREDICTION: '{next_token_text}' ({next_token_prob:.1f}%)\")\n", "    return next_token_text"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"id": "3IHSpsO_CiVi", "colab": {"base_uri": "https://localhost:8080/", "height": 192}, "executionInfo": {"status": "ok", "timestamp": 1752294224258, "user_tz": -330, "elapsed": 318, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "29b58fd2-560f-4d15-fb7a-5006b36b8b8b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["After '<PERSON><PERSON><PERSON><PERSON> is a great player of the game', the model predicts:\n", "--------------------------------------------------------------------------------\n", "Top tokens possible:  tensor([ 30, 284,  28, 282, 617])\n", "1. '.',  - 61.2%, id: 30\n", "2. ' and',  - 18.1%, id: 284\n", "3. ',',  - 17.5%, id: 28\n", "4. ' of',  - 1.4%, id: 282\n", "5. ' who',  - 0.4%, id: 617\n", "FINAL PREDICTION: '.' (61.2%)\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 34}], "source": ["predict_next_token(\"<PERSON><PERSON><PERSON><PERSON> is a great player of the game\", temperature=0.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3mqsmQiTERHa", "colab": {"base_uri": "https://localhost:8080/", "height": 35}, "executionInfo": {"status": "ok", "timestamp": 1746911162227, "user_tz": 420, "elapsed": 3, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "7894ba5c-0284-4f81-82fb-ad54ea0b0103"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["' each'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 14}], "source": ["tokenizer.decode(971)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"id": "XM9x5V-sGJvf", "executionInfo": {"status": "ok", "timestamp": 1752294307961, "user_tz": -330, "elapsed": 7, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["def generate_text(text, max_length=100, top_k=5,temperature=1):\n", "    # Keep track of original input\n", "    original_text = text\n", "    generated_tokens = 0\n", "\n", "    # Generate tokens one by one (autoregressive)\n", "    for i in range(1, max_length + 1):\n", "        # Predict next token using our earlier function\n", "        new_token = predict_next_token(text, top_k, temperature)\n", "\n", "        text = text + new_token\n", "        generated_tokens += 1\n", "\n", "        # Show progress\n", "        print(f\"Token {i}: '{new_token}'\")\n", "        print(f\"Text so far: '{text}'\")\n", "        print(\"-\" * 40)\n", "\n", "        # Stop if we get an end marker\n", "        if new_token == \"<|endoftext|>\":\n", "            break\n", "\n", "    print(f\"\\nFINAL RESULT:\")\n", "    print(f\"Original prompt: '{original_text}'\")\n", "    print(f\"Generated {generated_tokens} new tokens\")\n", "    print(f\"Complete text: '{text}'\")\n", "\n", "    return text"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"id": "eS1CpZKoG9C0", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "executionInfo": {"status": "ok", "timestamp": 1752294381098, "user_tz": -330, "elapsed": 1766, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "01c2f530-6133-4386-ce7f-8eb324794c50"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["After 'The capital of Russia was', the model predicts:\n", "--------------------------------------------------------------------------------\n", "Top tokens possible:  tensor([7498, 3251,  260, 2268, 2837])\n", "1. ' founded',  - 17.6%, id: 7498\n", "2. ' established',  - 6.5%, id: 3251\n", "3. ' the',  - 6.5%, id: 260\n", "4. ' once',  - 4.8%, id: 2268\n", "5. ' built',  - 4.1%, id: 2837\n", "FINAL PREDICTION: ' established' (6.5%)\n", "Token 1: ' established'\n", "Text so far: 'The capital of Russia was established'\n", "----------------------------------------\n", "After 'The capital of Russia was established', the model predicts:\n", "--------------------------------------------------------------------------------\n", "Top tokens possible:  tensor([281, 335, 411, 347, 418])\n", "1. ' in',  - 57.1%, id: 281\n", "2. ' on',  - 16.2%, id: 335\n", "3. ' by',  - 9.5%, id: 411\n", "4. ' as',  - 6.0%, id: 347\n", "5. ' at',  - 1.8%, id: 418\n", "FINAL PREDICTION: ' in' (57.1%)\n", "Token 2: ' in'\n", "Text so far: 'The capital of Russia was established in'\n", "----------------------------------------\n", "After 'The capital of Russia was established in', the model predicts:\n", "--------------------------------------------------------------------------------\n", "Top tokens possible:  tensor([  216,   260,  4053, 10757, 15651])\n", "1. ' ',  - 86.4%, id: 216\n", "2. ' the',  - 5.0%, id: 260\n", "3. ' August',  - 0.5%, id: 4053\n", "4. ' <PERSON>',  - 0.5%, id: 10757\n", "5. ' Moscow',  - 0.4%, id: 15651\n", "FINAL PREDICTION: ' ' (86.4%)\n", "Token 3: ' '\n", "Text so far: 'The capital of Russia was established in '\n", "----------------------------------------\n", "After 'The capital of Russia was established in ', the model predicts:\n", "--------------------------------------------------------------------------------\n", "Top tokens possible:  tensor([33, 34, 41, 38, 35])\n", "1. '1',  - 97.5%, id: 33\n", "2. '2',  - 2.0%, id: 34\n", "3. '9',  - 0.2%, id: 41\n", "4. '6',  - 0.1%, id: 38\n", "5. '3',  - 0.1%, id: 35\n", "FINAL PREDICTION: '1' (97.5%)\n", "Token 4: '1'\n", "Text so far: 'The capital of Russia was established in 1'\n", "----------------------------------------\n", "After 'The capital of Russia was established in 1', the model predicts:\n", "--------------------------------------------------------------------------------\n", "Top tokens possible:  tensor([38, 41, 39, 40, 37])\n", "1. '6',  - 25.4%, id: 38\n", "2. '9',  - 24.0%, id: 41\n", "3. '7',  - 19.8%, id: 39\n", "4. '8',  - 14.3%, id: 40\n", "5. '5',  - 12.3%, id: 37\n", "FINAL PREDICTION: '7' (19.8%)\n", "Token 5: '7'\n", "Text so far: 'The capital of Russia was established in 17'\n", "----------------------------------------\n", "After 'The capital of Russia was established in 17', the model predicts:\n", "--------------------------------------------------------------------------------\n", "Top tokens possible:  tensor([41, 34, 32, 40, 38])\n", "1. '9',  - 35.2%, id: 41\n", "2. '2',  - 21.1%, id: 34\n", "3. '0',  - 11.9%, id: 32\n", "4. '8',  - 10.4%, id: 40\n", "5. '6',  - 7.5%, id: 38\n", "FINAL PREDICTION: '8' (10.4%)\n", "Token 6: '8'\n", "Text so far: 'The capital of Russia was established in 178'\n", "----------------------------------------\n", "After 'The capital of Russia was established in 178', the model predicts:\n", "--------------------------------------------------------------------------------\n", "Top tokens possible:  tensor([35, 36, 34, 41, 33])\n", "1. '3',  - 62.1%, id: 35\n", "2. '4',  - 22.9%, id: 36\n", "3. '2',  - 5.4%, id: 34\n", "4. '9',  - 5.2%, id: 41\n", "5. '1',  - 3.3%, id: 33\n", "FINAL PREDICTION: '3' (62.1%)\n", "Token 7: '3'\n", "Text so far: 'The capital of Russia was established in 1783'\n", "----------------------------------------\n", "After 'The capital of Russia was established in 1783', the model predicts:\n", "--------------------------------------------------------------------------------\n", "Top tokens possible:  tensor([ 30,  28, 284, 411, 347])\n", "1. '.',  - 23.9%, id: 30\n", "2. ',',  - 18.8%, id: 28\n", "3. ' and',  - 11.1%, id: 284\n", "4. ' by',  - 10.8%, id: 411\n", "5. ' as',  - 7.9%, id: 347\n", "FINAL PREDICTION: ' and' (11.1%)\n", "Token 8: ' and'\n", "Text so far: 'The capital of Russia was established in 1783 and'\n", "----------------------------------------\n", "After 'The capital of Russia was established in 1783 and', the model predicts:\n", "--------------------------------------------------------------------------------\n", "Top tokens possible:  tensor([  314,   260,   436,  3365, 22184])\n", "1. ' is',  - 12.1%, id: 314\n", "2. ' the',  - 9.6%, id: 260\n", "3. ' was',  - 9.0%, id: 436\n", "4. ' named',  - 5.1%, id: 3365\n", "5. ' renamed',  - 5.0%, id: 22184\n", "FINAL PREDICTION: ' comes' (0.0%)\n", "Token 9: ' comes'\n", "Text so far: 'The capital of Russia was established in 1783 and comes'\n", "----------------------------------------\n", "After 'The capital of Russia was established in 1783 and comes', the model predicts:\n", "--------------------------------------------------------------------------------\n", "Top tokens possible:  tensor([656, 351, 429, 288, 281])\n", "1. ' under',  - 56.2%, id: 656\n", "2. ' with',  - 21.6%, id: 351\n", "3. ' from',  - 4.1%, id: 429\n", "4. ' to',  - 3.5%, id: 288\n", "5. ' in',  - 3.1%, id: 281\n", "FINAL PREDICTION: ' under' (56.2%)\n", "Token 10: ' under'\n", "Text so far: 'The capital of Russia was established in 1783 and comes under'\n", "----------------------------------------\n", "\n", "FINAL RESULT:\n", "Original prompt: 'The capital of Russia was'\n", "Generated 10 new tokens\n", "Complete text: 'The capital of Russia was established in 1783 and comes under'\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'The capital of Russia was established in 1783 and comes under'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 36}], "source": ["generate_text(\"The capital of Russia was\",max_length=10, top_k=5, temperature = 1)"]}], "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyMJUj8qjL8Xaw3CuIu9VRh0"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"d48d0ba367334a348856ae29953c1259": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_05f28f7152584315a5a4e574357c8fd3", "IPY_MODEL_ec92f3590823462292d4794365982780", "IPY_MODEL_06a6e42627d94787918d655853ab0371"], "layout": "IPY_MODEL_3db5f3ed81604b5193af56c29da97bc0"}}, "05f28f7152584315a5a4e574357c8fd3": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0f830998a27a4420801fd942b606711a", "placeholder": "​", "style": "IPY_MODEL_fc074449b1894c65b12fc2abdbe76a45", "value": "tokenizer_config.json: "}}, "ec92f3590823462292d4794365982780": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_26384b05389f4491aa310bc3b5d926f6", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4b63110e6b2e47f39c6b0d9fa37da4d8", "value": 1}}, "06a6e42627d94787918d655853ab0371": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_462706d327c84f78aa86ff9b89ae15d2", "placeholder": "​", "style": "IPY_MODEL_625f19303b324602813ba50237b3e18f", "value": " 17.7k/? [00:00&lt;00:00, 926kB/s]"}}, "3db5f3ed81604b5193af56c29da97bc0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0f830998a27a4420801fd942b606711a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fc074449b1894c65b12fc2abdbe76a45": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "26384b05389f4491aa310bc3b5d926f6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "4b63110e6b2e47f39c6b0d9fa37da4d8": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "462706d327c84f78aa86ff9b89ae15d2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "625f19303b324602813ba50237b3e18f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ff3988961f14497695988d88d8f71390": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1ff19e6164cf4a48afdc5a0cbea0cb18", "IPY_MODEL_174fa3200fd34fe288eade9dab2ba074", "IPY_MODEL_23d695717eb94cb2ab4eaa9588b2994b"], "layout": "IPY_MODEL_3214ef43d58c4f10aa18a7c6a8c56c96"}}, "1ff19e6164cf4a48afdc5a0cbea0cb18": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_65abda340e164ba2b66c09364a5e5605", "placeholder": "​", "style": "IPY_MODEL_144ff1f8cb6a4efdaa4496d50b299985", "value": "vocab.json: "}}, "174fa3200fd34fe288eade9dab2ba074": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8e462679a2784e92a0d214131ce4df8f", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_12d813e235cf40ac9b6cbf5ef9c8c2de", "value": 1}}, "23d695717eb94cb2ab4eaa9588b2994b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3d1d7498201d451999e1779b9c09a532", "placeholder": "​", "style": "IPY_MODEL_9f8c7c0817f74806aff3e84a1fe9c827", "value": " 1.61M/? [00:00&lt;00:00, 10.7MB/s]"}}, "3214ef43d58c4f10aa18a7c6a8c56c96": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "65abda340e164ba2b66c09364a5e5605": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "144ff1f8cb6a4efdaa4496d50b299985": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8e462679a2784e92a0d214131ce4df8f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "12d813e235cf40ac9b6cbf5ef9c8c2de": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3d1d7498201d451999e1779b9c09a532": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f8c7c0817f74806aff3e84a1fe9c827": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "897366d7f9ff4ef580ad213cd38d5fba": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fdda9e0618bf44e3a78b6b7611e8e1a5", "IPY_MODEL_d1eda33bba8a4bb1af00e0f04e30f57f", "IPY_MODEL_85b5f6340609434fbef308cbe701240d"], "layout": "IPY_MODEL_a572e53b52ad4679b7e647739384ff6d"}}, "fdda9e0618bf44e3a78b6b7611e8e1a5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_22e74d740437457c9e189677b49b10c9", "placeholder": "​", "style": "IPY_MODEL_9f9c444803fd410d91ec5269c097026c", "value": "merges.txt: "}}, "d1eda33bba8a4bb1af00e0f04e30f57f": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_05e16536c6344a34ad91b60ff3788d2c", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_81a73be351c341eda46f2fb993ae6160", "value": 1}}, "85b5f6340609434fbef308cbe701240d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_115cbcc86dbf47f7b9907d4ad32a2d57", "placeholder": "​", "style": "IPY_MODEL_2b216d66eaf549d0bf73e5755adb34b0", "value": " 917k/? [00:00&lt;00:00, 10.4MB/s]"}}, "a572e53b52ad4679b7e647739384ff6d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "22e74d740437457c9e189677b49b10c9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f9c444803fd410d91ec5269c097026c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "05e16536c6344a34ad91b60ff3788d2c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "81a73be351c341eda46f2fb993ae6160": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "115cbcc86dbf47f7b9907d4ad32a2d57": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2b216d66eaf549d0bf73e5755adb34b0": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fb9c923068df45a58b1c891e7bdd394b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f749a542fa7c4ccaac23047db11ec145", "IPY_MODEL_b4c574a57324437693761e5c18c205e5", "IPY_MODEL_bd212936bfde45b3aa0da58a9236809b"], "layout": "IPY_MODEL_4e9d7fa2bf3242ea9851b483e009a5df"}}, "f749a542fa7c4ccaac23047db11ec145": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5e308698c1674cd593e93697520b0df8", "placeholder": "​", "style": "IPY_MODEL_f44d4617ea014ffc85de500f7b923a63", "value": "tokenizer.json: "}}, "b4c574a57324437693761e5c18c205e5": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c6ab8d1bfdea48c9885223b09c712051", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7e4a12d98deb4f6ba1bd82c8676ed6af", "value": 1}}, "bd212936bfde45b3aa0da58a9236809b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c6d4f063a80f4a71a8161680057ec2c8", "placeholder": "​", "style": "IPY_MODEL_1fcd0987e15247ae9ab8f1cba05be5f0", "value": " 4.25M/? [00:00&lt;00:00, 9.35MB/s]"}}, "4e9d7fa2bf3242ea9851b483e009a5df": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5e308698c1674cd593e93697520b0df8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f44d4617ea014ffc85de500f7b923a63": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c6ab8d1bfdea48c9885223b09c712051": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "7e4a12d98deb4f6ba1bd82c8676ed6af": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c6d4f063a80f4a71a8161680057ec2c8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1fcd0987e15247ae9ab8f1cba05be5f0": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9e27e1a757044596a15550bf1ba5618a": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9e3e6f735d294c73a0ccf4c832b38356", "IPY_MODEL_c87b5a8051fb4dd8b5e9fe3f7695008f", "IPY_MODEL_accde97a9c2c4e8fa7398beec6b2379c"], "layout": "IPY_MODEL_13ddc74b137349198e38e23badf4fed3"}}, "9e3e6f735d294c73a0ccf4c832b38356": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b0ca3c61d16d4747905340f971b9d549", "placeholder": "​", "style": "IPY_MODEL_13a4e9910aa1436ca3646c7b33941c7e", "value": "added_tokens.json: "}}, "c87b5a8051fb4dd8b5e9fe3f7695008f": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_859a6cd9b2244559a422a4ad4c9c003f", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_dc718f683bc64df5b089393926a0cda3", "value": 1}}, "accde97a9c2c4e8fa7398beec6b2379c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f93f041536ae48d9b2e1bd8ac8f861dd", "placeholder": "​", "style": "IPY_MODEL_222f15061d5f4065a3acf4138a65f617", "value": " 2.50k/? [00:00&lt;00:00, 166kB/s]"}}, "13ddc74b137349198e38e23badf4fed3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b0ca3c61d16d4747905340f971b9d549": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "13a4e9910aa1436ca3646c7b33941c7e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "859a6cd9b2244559a422a4ad4c9c003f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "dc718f683bc64df5b089393926a0cda3": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f93f041536ae48d9b2e1bd8ac8f861dd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "222f15061d5f4065a3acf4138a65f617": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "34f63941bd5a4abd8129209067502fe9": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c664150e19844116825d386321339658", "IPY_MODEL_f2b897b52f144196a39c3cf87776f5c5", "IPY_MODEL_f3ee1b3207d041e1b0d001f64cbc3803"], "layout": "IPY_MODEL_c44d99682bcb4b66b7e012f9ac77159e"}}, "c664150e19844116825d386321339658": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_175a990146a9425cad040857822c5c61", "placeholder": "​", "style": "IPY_MODEL_77114b64f2674b3c95151402cdddad18", "value": "special_tokens_map.json: 100%"}}, "f2b897b52f144196a39c3cf87776f5c5": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f3ebadeb1448479092882798540c0779", "max": 95, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_17e97b1138824f1c9a07cfd3707e8b8c", "value": 95}}, "f3ee1b3207d041e1b0d001f64cbc3803": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_22a9cb8838fc469b9aa5d9d41b9957eb", "placeholder": "​", "style": "IPY_MODEL_0080318cb0b44526b3f6968b1d95be3a", "value": " 95.0/95.0 [00:00&lt;00:00, 8.41kB/s]"}}, "c44d99682bcb4b66b7e012f9ac77159e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "175a990146a9425cad040857822c5c61": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "77114b64f2674b3c95151402cdddad18": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f3ebadeb1448479092882798540c0779": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "17e97b1138824f1c9a07cfd3707e8b8c": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "22a9cb8838fc469b9aa5d9d41b9957eb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0080318cb0b44526b3f6968b1d95be3a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "71bfef27b83d4f799ac73968efab21ea": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f24c5867b29b477dbeaa495903c065bf", "IPY_MODEL_fd20bf68826d4b6a8ce2090e477c21fd", "IPY_MODEL_128fffd564524add8d61c56eb6a0f8a3"], "layout": "IPY_MODEL_9fa6083700f54dc2a13b302fa4a05a7e"}}, "f24c5867b29b477dbeaa495903c065bf": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_55dc5317a5ac4e468f1cfa5b62d7b1d0", "placeholder": "​", "style": "IPY_MODEL_98c8d44a75da4c1f87647a10c98c3d68", "value": "tokenizer_config.json: "}}, "fd20bf68826d4b6a8ce2090e477c21fd": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_185e3806190f4697ad56eff94fa95586", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_dc4b0956871148199a8cf2cf364b0d01", "value": 1}}, "128fffd564524add8d61c56eb6a0f8a3": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a354aadc194b4146a211b6f784d29b17", "placeholder": "​", "style": "IPY_MODEL_62753cff558347f992bc5c6df28d201c", "value": " 3.76k/? [00:00&lt;00:00, 173kB/s]"}}, "9fa6083700f54dc2a13b302fa4a05a7e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "55dc5317a5ac4e468f1cfa5b62d7b1d0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "98c8d44a75da4c1f87647a10c98c3d68": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "185e3806190f4697ad56eff94fa95586": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "dc4b0956871148199a8cf2cf364b0d01": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a354aadc194b4146a211b6f784d29b17": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "62753cff558347f992bc5c6df28d201c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a92ae03bf72b463289de6265dba34ca4": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0d266f88a8e944e1bb47031294548ac7", "IPY_MODEL_136817b1a2a846c9b557052a1360f46e", "IPY_MODEL_67b040f8f14842fd93b57bc243c37780"], "layout": "IPY_MODEL_b1183a1dc3b64b35b436e3b459fe20cb"}}, "0d266f88a8e944e1bb47031294548ac7": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ef7c352ac80c4c5086f2dbdb79f47f39", "placeholder": "​", "style": "IPY_MODEL_4928409e01134f10b843ec9867a0fe8e", "value": "vocab.json: "}}, "136817b1a2a846c9b557052a1360f46e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4856ce60e2564d2c9649f121f828a0b7", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d31cb673fc4e4470ae020b00332c85fb", "value": 1}}, "67b040f8f14842fd93b57bc243c37780": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7c7aa683bd7e459b802448fcd6e863ed", "placeholder": "​", "style": "IPY_MODEL_0db8341fdc464879b745b2b24c250a5c", "value": " 801k/? [00:00&lt;00:00, 11.6MB/s]"}}, "b1183a1dc3b64b35b436e3b459fe20cb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ef7c352ac80c4c5086f2dbdb79f47f39": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4928409e01134f10b843ec9867a0fe8e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4856ce60e2564d2c9649f121f828a0b7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "d31cb673fc4e4470ae020b00332c85fb": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7c7aa683bd7e459b802448fcd6e863ed": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0db8341fdc464879b745b2b24c250a5c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b927bf97c0a64cc28794486f618bd60c": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d3059f9f1ada4bddad1f6bf0e4cbf67d", "IPY_MODEL_fdd21afbc805444aa15d7715fb340a0e", "IPY_MODEL_3a99b43346f644c4999f7be983cc6f2d"], "layout": "IPY_MODEL_a870a52b49534b6a88fe34a298b770e7"}}, "d3059f9f1ada4bddad1f6bf0e4cbf67d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_788e3699eb9c426c971d1089abe98a7f", "placeholder": "​", "style": "IPY_MODEL_5884aa8d8af645c49cd2ff71283067f0", "value": "merges.txt: "}}, "fdd21afbc805444aa15d7715fb340a0e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_17529bf4e5d9402a93010a47966b1d2e", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_522eec1c7de540a7bfb755f4f65bf038", "value": 1}}, "3a99b43346f644c4999f7be983cc6f2d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6ad5b1651cf14fea8b66c9c0ba249f90", "placeholder": "​", "style": "IPY_MODEL_3347e5c9107a48509308ae604b60b432", "value": " 466k/? [00:00&lt;00:00, 5.24MB/s]"}}, "a870a52b49534b6a88fe34a298b770e7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "788e3699eb9c426c971d1089abe98a7f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5884aa8d8af645c49cd2ff71283067f0": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "17529bf4e5d9402a93010a47966b1d2e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "522eec1c7de540a7bfb755f4f65bf038": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6ad5b1651cf14fea8b66c9c0ba249f90": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3347e5c9107a48509308ae604b60b432": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c154ad11c6d84e639283a50d6b28b06f": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6a7d92f545a9430b9acc873fa27db0ff", "IPY_MODEL_a037e220272f443ea93e884bf6c5dbc7", "IPY_MODEL_c78980a4260f4bb59baad1392d4dee37"], "layout": "IPY_MODEL_c0def3fc63d24844a9dbd954e6122ccf"}}, "6a7d92f545a9430b9acc873fa27db0ff": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_32a8e45718c240feb476ccdbda597ee2", "placeholder": "​", "style": "IPY_MODEL_a40da4744d98418b8b944fc4273d0e2c", "value": "tokenizer.json: "}}, "a037e220272f443ea93e884bf6c5dbc7": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7e763ee611cf4683be27907aeb714e48", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d160523c0785401b94a6edae4c7de803", "value": 1}}, "c78980a4260f4bb59baad1392d4dee37": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8b86db93011c42da88485e302f0df92a", "placeholder": "​", "style": "IPY_MODEL_315800104ac641538b58e010fb42ba38", "value": " 2.10M/? [00:00&lt;00:00, 17.3MB/s]"}}, "c0def3fc63d24844a9dbd954e6122ccf": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "32a8e45718c240feb476ccdbda597ee2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a40da4744d98418b8b944fc4273d0e2c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7e763ee611cf4683be27907aeb714e48": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "d160523c0785401b94a6edae4c7de803": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8b86db93011c42da88485e302f0df92a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "315800104ac641538b58e010fb42ba38": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ad2ac0941cd542f9b9ffef692ec9a4f6": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_11d986c994114b10802578348eb440db", "IPY_MODEL_4363ef8693474d8bb0d0ceeb22817992", "IPY_MODEL_90c1577e5a744a369311c67d643e88f6"], "layout": "IPY_MODEL_25031303c7314d4ea7d31153772dee2a"}}, "11d986c994114b10802578348eb440db": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7ba6e59fed114dc2885803e907d4ac65", "placeholder": "​", "style": "IPY_MODEL_d8aaf9b882164f6eba2ee8c57efd36da", "value": "special_tokens_map.json: 100%"}}, "4363ef8693474d8bb0d0ceeb22817992": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dbae867e020d4c918dd7288851d1b6e3", "max": 655, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_dabfbe333ebc4acd8aff106a5159d1cd", "value": 655}}, "90c1577e5a744a369311c67d643e88f6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_76e3aef63de843169b458a100886d635", "placeholder": "​", "style": "IPY_MODEL_e1bf33fefe394941b31346e384a5763c", "value": " 655/655 [00:00&lt;00:00, 47.9kB/s]"}}, "25031303c7314d4ea7d31153772dee2a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7ba6e59fed114dc2885803e907d4ac65": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d8aaf9b882164f6eba2ee8c57efd36da": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "dbae867e020d4c918dd7288851d1b6e3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dabfbe333ebc4acd8aff106a5159d1cd": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "76e3aef63de843169b458a100886d635": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e1bf33fefe394941b31346e384a5763c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e986e3d765444f9bbeb29edcbcb7cbde": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_95bf3a8d3e754042817105f15e767d09", "IPY_MODEL_efc98b73927449b89884520e7ddb5b75", "IPY_MODEL_fbcb51e2f815453ca073562c054b103d"], "layout": "IPY_MODEL_234fb3f43f9e49e6b2ef76f30a0b8c71"}}, "95bf3a8d3e754042817105f15e767d09": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_155080796640406dba822aa1659aa234", "placeholder": "​", "style": "IPY_MODEL_6934968002674abab015e71df42055c3", "value": "config.json: 100%"}}, "efc98b73927449b89884520e7ddb5b75": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_df016e1767c545698aa1496fd4fcf0ca", "max": 861, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2bd942f256f04c05af69f0961536fde9", "value": 861}}, "fbcb51e2f815453ca073562c054b103d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ad27a3aa71f44dfb9390ea98d6550f29", "placeholder": "​", "style": "IPY_MODEL_287c214f8dd94a41b1eb03eb912ebc39", "value": " 861/861 [00:00&lt;00:00, 75.1kB/s]"}}, "234fb3f43f9e49e6b2ef76f30a0b8c71": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "155080796640406dba822aa1659aa234": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6934968002674abab015e71df42055c3": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "df016e1767c545698aa1496fd4fcf0ca": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2bd942f256f04c05af69f0961536fde9": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ad27a3aa71f44dfb9390ea98d6550f29": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "287c214f8dd94a41b1eb03eb912ebc39": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "08653bbec56a45c4ab5524065a9868e4": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_cd4e46e0d3684760a7e88b42dc72e281", "IPY_MODEL_9bb894060326463b8083743f370e42bc", "IPY_MODEL_dbe9f848181a4d64bd4dc46612f96290"], "layout": "IPY_MODEL_55b1be08b76d4881a48c30e1341f5077"}}, "cd4e46e0d3684760a7e88b42dc72e281": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9759bb7b3b7546c0a0a85ddb9935a05a", "placeholder": "​", "style": "IPY_MODEL_131f57df94c844a0a50269f44fb212a6", "value": "model.safetensors: 100%"}}, "9bb894060326463b8083743f370e42bc": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_77149a99e724420d8350a7c9cbaf0b7b", "max": 269060552, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_48b87eedeea04710b0f9cfcbf6008489", "value": 269060552}}, "dbe9f848181a4d64bd4dc46612f96290": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bcf56eddf1d7485481da64a0da2a6eec", "placeholder": "​", "style": "IPY_MODEL_1e19579af7c5449cb802163ac43f86f6", "value": " 269M/269M [00:09&lt;00:00, 38.2MB/s]"}}, "55b1be08b76d4881a48c30e1341f5077": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9759bb7b3b7546c0a0a85ddb9935a05a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "131f57df94c844a0a50269f44fb212a6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "77149a99e724420d8350a7c9cbaf0b7b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "48b87eedeea04710b0f9cfcbf6008489": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bcf56eddf1d7485481da64a0da2a6eec": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1e19579af7c5449cb802163ac43f86f6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4b1cb32a43bd408a8de23cff8d420d82": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_bb8826de96ab47fdb04647c0fd1d477c", "IPY_MODEL_2d768916999a4fe7939bb9a290c7cb48", "IPY_MODEL_c90cf977d0cc40949b0a5d01270899c0"], "layout": "IPY_MODEL_1d241be42cd84377a98687536b935b3e"}}, "bb8826de96ab47fdb04647c0fd1d477c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d884618d916e4552a81d90a6724eedec", "placeholder": "​", "style": "IPY_MODEL_21855e8e0c7f4a41ae1c684d2ac94b36", "value": "generation_config.json: 100%"}}, "2d768916999a4fe7939bb9a290c7cb48": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4773c957de434d68a250f87cc1f094a1", "max": 132, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7546dd3d5112491fbd0b0b87ad798700", "value": 132}}, "c90cf977d0cc40949b0a5d01270899c0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_89347f2eaa584157a529cdd152f37007", "placeholder": "​", "style": "IPY_MODEL_450185fc1c064caba72d012b732c7e1e", "value": " 132/132 [00:00&lt;00:00, 3.36kB/s]"}}, "1d241be42cd84377a98687536b935b3e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d884618d916e4552a81d90a6724eedec": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "21855e8e0c7f4a41ae1c684d2ac94b36": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4773c957de434d68a250f87cc1f094a1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7546dd3d5112491fbd0b0b87ad798700": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "89347f2eaa584157a529cdd152f37007": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "450185fc1c064caba72d012b732c7e1e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "nbformat": 4, "nbformat_minor": 0}