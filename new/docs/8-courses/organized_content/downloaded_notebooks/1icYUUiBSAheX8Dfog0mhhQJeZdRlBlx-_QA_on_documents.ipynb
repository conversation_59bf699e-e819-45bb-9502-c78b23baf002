{"cells": [{"cell_type": "markdown", "metadata": {"id": "_jsJc17Tv1nB"}, "source": ["# Modern AI Prac: Enterprise chat over documents"]}, {"cell_type": "markdown", "metadata": {"id": "K2bBLP3Hv_wQ"}, "source": ["## 1. Basics"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 50602, "status": "ok", "timestamp": 1754211300614, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "1C8oqu0_9PiX", "outputId": "a9ce9345-2c1e-46f0-e854-61ed3ff27f4a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/981.5 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[91m━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m481.3/981.5 kB\u001b[0m \u001b[31m14.1 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m981.5/981.5 kB\u001b[0m \u001b[31m15.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.3/67.3 kB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.5/2.5 MB\u001b[0m \u001b[31m43.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m313.2/313.2 kB\u001b[0m \u001b[31m17.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.5/59.5 MB\u001b[0m \u001b[31m12.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m19.5/19.5 MB\u001b[0m \u001b[31m94.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m155.6/155.6 kB\u001b[0m \u001b[31m11.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m284.2/284.2 kB\u001b[0m \u001b[31m20.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m131.1/131.1 kB\u001b[0m \u001b[31m9.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.9/1.9 MB\u001b[0m \u001b[31m71.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m103.1/103.1 kB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m16.5/16.5 MB\u001b[0m \u001b[31m88.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m65.6/65.6 kB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m72.5/72.5 kB\u001b[0m \u001b[31m5.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m120.0/120.0 kB\u001b[0m \u001b[31m9.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.6/201.6 kB\u001b[0m \u001b[31m13.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m105.4/105.4 kB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.4/71.4 kB\u001b[0m \u001b[31m5.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m45.2/45.2 kB\u001b[0m \u001b[31m2.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m459.8/459.8 kB\u001b[0m \u001b[31m28.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.9/50.9 kB\u001b[0m \u001b[31m3.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.0/4.0 MB\u001b[0m \u001b[31m83.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m453.1/453.1 kB\u001b[0m \u001b[31m31.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m3.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for langdetect (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Building wheel for pypika (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n"]}], "source": ["! pip install -q -U langchain-groq langchain langchain-community langchain-text-splitters pypdf gradio chromadb langdetect indic-transliteration"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "VAcV_tvJwB9F", "executionInfo": {"status": "ok", "timestamp": 1754211300631, "user_tz": -330, "elapsed": 16, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["# We will use a simple utility to make the text wrap properly when printing.\n", "from IPython.display import HTML, display\n", "\n", "def set_css():\n", "  display(HTML('''\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  '''))\n", "get_ipython().events.register('pre_run_cell', set_css)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 17}, "executionInfo": {"elapsed": 9769, "status": "ok", "timestamp": 1754211310402, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "yFc7NMHPs9kc", "outputId": "9bf0dd8d-a933-490d-8729-853c448c5f11"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}], "source": ["from google.colab import userdata\n", "import os\n", "os.environ[\"GROQ_API_KEY\"] = userdata.get(\"GROQ_API_KEY\")\n", "from langchain_groq import ChatGroq\n", "llm_groq = ChatGroq(model_name=\"llama3-70b-8192\")"]}, {"cell_type": "markdown", "metadata": {"id": "7G8cbGvdwEi9"}, "source": ["## 2. <PERSON><PERSON> and <PERSON>age Data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 17}, "executionInfo": {"elapsed": 295, "status": "ok", "timestamp": 1754211310693, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "IyRutWDttv9T", "outputId": "f6d5e5a1-0b3e-4a17-d2d8-67f120b1c561"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}], "source": ["from langchain.document_loaders import PyPDFLoader\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "\n", "def load_and_chunk_pdf(pdf_url):\n", "    loader = PyPDFLoader(pdf_url)\n", "    documents = loader.load()\n", "    text_splitter = RecursiveCharacterTextSplitter(chunk_size=2000, chunk_overlap=100)\n", "    chunks = text_splitter.split_documents(documents)\n", "    print(len(chunks))\n", "    return chunks"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 17}, "executionInfo": {"elapsed": 1355, "status": "ok", "timestamp": 1754211312054, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "7DwccFVruh_n", "outputId": "dbef5bb5-b6c1-40df-ab10-418845f0c388"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}], "source": ["import chromadb\n", "#chroma_client = chromadb.Client()\n", "chroma_client = chromadb.PersistentClient(path=\"./philosophy\")\n", "collection = chroma_client.create_collection(name=\"philosophy7\")\n", "\n", "def add_chunks_to_vector_db(chunks):\n", "  id = 0\n", "  for chunk in chunks:\n", "      id += 1\n", "      if id % 10 != 0:\n", "        continue # For now, choose only 1 in 10 documents for sampling.\n", "      if id % 500 == 0:\n", "        print(f\"Added {id} embeddings\")\n", "      collection.add(\n", "          documents=[chunk.page_content],\n", "          metadatas=[{\"source\": chunk.metadata[\"source\"],\"page_no\": chunk.metadata[\"page\"]} ],\n", "          ids=[chunk.metadata[\"source\"]+\":\"+str(id)],\n", "      )\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"executionInfo": {"elapsed": 79701, "status": "ok", "timestamp": 1754211391758, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "nFZFKY_BlA5V", "colab": {"base_uri": "https://localhost:8080/", "height": 86}, "outputId": "51981e1e-3fab-4374-ba22-c016199f0883"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["1300\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/root/.cache/chroma/onnx_models/all-MiniLM-L6-v2/onnx.tar.gz: 100%|██████████| 79.3M/79.3M [00:01<00:00, 67.3MiB/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Added 500 embeddings\n", "Added 1000 embeddings\n"]}], "source": ["chunks = load_and_chunk_pdf('https://web.archive.org/web/20201224194654id_/http://www.bhagavatgita.ru/files/Bhagavad-gita_As_It_Is.pdf')\n", "add_chunks_to_vector_db(chunks)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"executionInfo": {"elapsed": 857, "status": "ok", "timestamp": 1754211392632, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "vNxvI_P3tIMZ", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "outputId": "587a2f34-48bd-43ac-efdf-64cd0aa58d97"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["0\n"]}], "source": ["chunks = load_and_chunk_pdf('https://files.alislam.cloud/pdf/Holy-Quran-Arabic.pdf')\n", "add_chunks_to_vector_db(chunks)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"executionInfo": {"elapsed": 61668, "status": "ok", "timestamp": 1754211454305, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "Z30GtBHTwxrW", "colab": {"base_uri": "https://localhost:8080/", "height": 52}, "outputId": "84673e11-334c-4c45-e225-a4da3e53864e"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["812\n", "Added 500 embeddings\n"]}], "source": ["chunks = load_and_chunk_pdf('https://www.churchofjesuschrist.org/bc/content/shared/content/english/pdf/language-materials/83291_eng.pdf')\n", "add_chunks_to_vector_db(chunks)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"executionInfo": {"elapsed": 2443, "status": "ok", "timestamp": 1754211456754, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "vRFpSBs4aXDY", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "outputId": "ce9de381-f604-4ef6-ec7f-2a24462bcbbd"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["28\n"]}], "source": ["chunks = load_and_chunk_pdf('https://cdn.centerforinquiry.org/wp-content/uploads/sites/29/1996/03/22165045/p28.pdf')\n", "add_chunks_to_vector_db(chunks)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"executionInfo": {"elapsed": 391, "status": "ok", "timestamp": 1754211457153, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "VPkw2AXFs4hB", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "59bee6e8-a26c-4484-9ac6-78974e291597"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Copyright © 1998 The Bhaktivedanta Book Trust Int'l. All Rights Reserved.\n", "thinking that the resultant ac tions will make them happy. They do not know\n", "that no kind of material body anywhere within the universe can give life\n", "without miseries. The miseries of life, namely birth, death, old age and diseases,\n", "are present everywhere within the material world. But o ne who understands\n", "his real constitutional position as the eternal servitor of the Lord, and thus\n", "knows the position of the Personality of <PERSON><PERSON>, engages himself in the\n", "transcendental loving service of the Lord. Consequently he becomes qualified\n", "to enter into the Vaikuëöha planets, where there is neither material, miserable\n", "life nor the influence of time and death. To know one’s constitutional position\n", "means to know also the sublime positi on of the Lord. One who wrongly thinks\n", "that the living entity’s position and the Lord’s position are on the same level is\n", "to be understood to be in darkness and therefore unable to engage himself in\n", "the devotional service of the Lord. He becomes a lord himself and thus paves\n", "the way for the repetition of birth and death. But one who, understanding that\n", "his position is to serve, transfers himself to the service of the Lord, at once\n", "becomes eligible for Vaikuëöhaloka. Service for the cause of the Lord is called\n", "karma-yoga or buddhi-yoga, or in plain words, devotional service to the Lord.\n", "TEXT  52\n", "<PERSON><PERSON>-il/l&/ buiÖVYaRiTaTairZYaiTa )\n", "Tada GaNTaaiSa iNaveRd& é[aeTaVYaSYa é[uTaSYa c )) 52 ))\n", "yadä te moha-kalila<PERSON>\n", "buddhir vya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "tadä gant<PERSON>si ni<PERSON>\n", "çrotavyasya çrutasya ca\n", "SYNONYMS\n", "yadä—when; te—your; moha—of illusion; kalilam—dense forest;\n", "buddhi<PERSON>—transcendental service with intelligence; vya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>—surpasses;\n", "tadä—at that time; gantä asi—you shall go; nirvedam—callousness;\n", "Copyright © 1998 The Bhaktivedanta Book Trust Int'l. All Rights Reserved.\n", "TRANSLATION\n", "Thus knowing oneself to be transcendental to the material senses, mind and\n", "intelligence, O mighty-armed <PERSON><PERSON><PERSON><PERSON>, one should steady the mind by deliberate\n", "spiritual intelligence [Kåñëa consciousness] and thus—by spiritual\n", "strength—conquer this insatiable ene my known as lust.\n", "PURPORT\n", "This Third Chapter of the Bhagavad-gétä is conclusively directive to Kåñëa\n", "consciousness by knowing oneself as the eternal servitor of the Supreme\n", "Personality of <PERSON><PERSON>, without considering impersonal voidness the ultimate\n", "end. In the material existence o f life, one is certainly influenced by\n", "propensities for lust and desire for dominating the resources of material\n", "nature. Desire for overlording and for sense gratification is the greatest enemy\n", "of the conditioned soul; but by the strength of Kåñëa consciou sness, one can\n", "control the material senses, the mind and the intelligence. One may not give\n", "up work and prescribed duties all of a sudden; but by gradually developing\n", "Kåñëa consciousness, one can be situated  in a transcendental position without\n", "being influenced by the material senses and the mind—by steady intelligence\n", "directed toward one’s pure identity. This is the sum total of this chapter. In the\n", "immature stage of material existence, philosophical speculations and artificial\n", "attempts to control the sens es by the so-called practice of yogic postures can\n", "never help a man toward spiritual life. He must be trained in Kåñëa\n", "consciousness by higher intelligence.\n", "Thus end the Bhaktivedanta Purports to the Third Chapter of the Çrémad\n", "Bhagavad-<PERSON><PERSON><PERSON>ä in the matter of Karma-yoga, or the Discharge of One’s Prescribed\n", "Duty in Kåñëa Consciousness.\n", "1 1 a tg Stranger.\n", " 2 a Jo\n", "hn 6:44 (44, 63–65).  \n", "tg Election.\n", "  b tg Foreordination;  \n", "<PERSON>, Fore\n", "knowledge of.\n", "  c tg Sanctification.\n", "  d tg Obedience.\n", "  e Isa. 52:\n", "15.\n", "  f Ex. 24:\n", "8 (5–8);  \n", "Heb. 12:24.\n", " 3 a tg <PERSON>, Mercy of.\n", "  b tg Holy Ghost,  \n", "Baptism of;\n", "  \n", "<PERSON>, New, Spiritually \n", "<PERSON><PERSON>.\n", "  c gr living.\n", "  d tg <PERSON>.\n", "  e 1 Co\n", "r. 15:20.\n", " 4 a Matt. 6:\n", "20;  \n", "1 Co\n", "r. 9:25 (24–25);  \n", "Col. 1:5.\n", " 5 a Ro\n", "m. 1:16.\n", "  b Heb. 10\n", ":22 (22–24);  \n", "1 Pet. 1\n", ":21 (21–22).\n", "  c gr prepared.\n", " 6 a tg Probation.\n", "  b gr trials, afflictions. \n", "tg Temptation.\n", " 7 a tg Opposition;  \n", "Te\n", "st.\n", "  b gr revelation.\n", " 8 a Jo\n", "hn 20:29.\n", " 9 a gr goal, purpose, \n", "co\n", "nsummation. \n", "jst 1 Pet. 1:9 . . . object of \n", "yo\n", "ur faith . . .\n", "  b D&C 76\n", ":51 (51–70).\n", "  c Heb. 10\n", ":36.\n", " 10 a tg Salvation.\n", "  b <PERSON><PERSON> 13:17\n", ";  \n", "<PERSON> 24:27 (26–27);  \n", "Heb. 11:13;  \n", "2 Pet. 3:\n", "2.\n", " 11 a Ja\n", "cob 4:4; 7:11 (11–12);  \n", "<PERSON><PERSON><PERSON> 13:33 (33–35);  \n", "D&C 20:26.\n", "  b Heb. 12\n", ":2.\n", "  c D&C 58\n", ":3 (3–4).\n", " 12 a tg Holy Ghost,  \n", "Mission of.\n", "  b tg Angels.\n", " 13 a tg Levi<PERSON>.\n", "  b gr perfectly, completely.\n", "   \n", "The firs T epis Tle Gener Al of  \n", "peTer\n", "CHAPTER 1\n", "The trial of our faith precedes salva­\n", "tion—<PERSON> was foreordained to be \n", "the Redeemer.\n", "P\n", "<PERSON><PERSON> , an apostle of Je sus \n", "<PERSON>, to the a strangers scat­\n", "tered throughout Pontus, Gala­\n", "tia, Cappadocia, Asia, and Bithynia,\n", "2 a Elect according to the b fore­\n", "knowledge of <PERSON> the Father, \n", "through \n", "c sanctification of the Spirit, \n", "unto d obedience and e sprinkling \n", "of the f blood of <PERSON> Christ: Grace \n", "unto you, and peace, be multiplied.\n", "3 Ble\n", "ssed be the God and Father \n", "of our Lord Jesus <PERSON>, which ac­\n", "cording to his abundant \n", "a mercy hath \n", "b begotten us again unto a c lively \n", "d hope by the resurrection of e <PERSON> \n", "Christ from the dead,\n", "4 To\n", " an a inheritance incorruptible, \n", "and undefiled, and that fadeth not \n", "away, reserved in heaven for you,\n", "5\n", " Who a\n", "re kept by the a power of \n", "God through b faith unto salvation \n", "c ready to be revealed in the last  \n", "time.\n", "6 Wherein ye g\n", "reatly rejoice,\n", "Copyright © 1998 The Bhaktivedanta Book Trust Int'l. All Rights Reserved.\n", "advance toward the supreme eternal atmosphere.\n", "PURPORT\n", "From the foregoing explanation of different types of sacrifice (namely\n", "sacrifice of one’s possessions, study of the Vedas or philosophical doctrines, and\n", "performance of the yoga system), it is found that  the common aim of all is to\n", "control the senses. Sens e gratification is the root cause of material existence;\n", "therefore, unless and until one is situ ated on a platform apart from sense\n", "gratification, there is no chance of be ing elevated to the eternal platform of\n", "full knowledge, full bliss and full life. <PERSON><PERSON> s platform is in the eternal\n", "atmosphere, or Brahman atmosphere. All the above-mentioned sacrifices help\n", "one to become cleansed of the sinful re actions of material existence. By this\n", "advancement in life, not only does on e become happy and opulent in this li fe,\n", "but also, at the end, he enters into the eternal kingdom of <PERSON>, either merging\n", "into the impersonal <PERSON><PERSON><PERSON> or assoc iating with the Supreme Personality of\n", "Godhead, Kåñëa.\n", "TEXT  31\n", "NaaYa& l/aek-ae_STYaYajSYa ku-<PERSON><PERSON>_NYa\" ku-åSataMa )) 31 ))\n", "näyaà loko ’sty ayajïasya\n", "kuto <PERSON>nya<PERSON> kuru-sattama\n", "SYNONYMS\n", "na—never; ayam—this; loka<PERSON>—planet; asti—there is; ayaj<PERSON><PERSON><PERSON>—for one who\n", "performs no sacrifice; kuta<PERSON>—where is; any<PERSON><PERSON>—the other; kuru-sat-tama—O\n", "best amongst the Kurus.\n", "TRANSLATION\n", "O best of the Kuru dynasty, without sacrifice one can never live happily on\n", "controller. Nothing could be manifested without being controlled. It is childish\n", "not to consider the controller. For instance, a child may think that an\n", "automobile is quite wonderful to be able to run without a horse or other animal\n", "pulling it, but a sane man kn ows the nature of the automobile’s engineering\n", "arrangement. He always knows that behind the machinery there is a man, a\n", "driver. Similarly, the Supreme Lord is the driver under whose direction\n", "everything is working. Now the jévas, or the living entities, have been accepted\n", "by the Lord, as we will note in the later chapters, as His parts and parcels. A\n", "particle of gold is also gold, a drop of water from the ocean is also salty, and\n", "similarly we the living entities, be ing part and parcel of the supreme\n"]}], "source": ["query_text = \"Duty in life\"\n", "results = collection.query(query_texts=[query_text],n_results=5)\n", "for result in results[\"documents\"][0]:\n", "    print(result)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"executionInfo": {"elapsed": 393, "status": "ok", "timestamp": 1754211457548, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "g591otsvxymG", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "363fa22e-29e9-42cf-f25e-4c8268b5cd45"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["1489 PHILIPPIANS 1:21–2:10\n", "expec tation and my a hope, that in \n", "nothing I shall be ashamed, but that \n", "with all boldness, as always, so now \n", "also Christ shall be b magnified in \n", "my body, whether it be by life, or \n", "by death.\n", "21\n", " For to me to live is Christ, and \n", "to die is gain.\n", "22 But if I live in the flesh, this is \n", "the fruit of my labour: yet what I \n", "shall choose I \n", "a wot not.\n", "23 For I am a in a strait betwixt two, \n", "having a desire to depart, and to \n", "be with <PERSON>; which is far better:\n", "24\n", " Nevertheless to a abide in the \n", "flesh is more needful for you.\n", "25 And having this confidence, I \n", "know that I shall abide and continue \n", "with you all for your furtherance \n", "and joy of faith;\n", "26\n", " That your rejoicing may be more \n", "abundant in <PERSON> Christ for me by \n", "my coming to you again.\n", "27\n", " Only let your conversation be \n", "as it becometh the gospel of Christ: \n", "that whether I come and see you, or \n", "else be absent, I may hear of your \n", "affairs, that ye \n", "a stand fast in one \n", "spirit, with b one c mind d striving \n", "together for the faith of the gospel;\n", "28 And in nothing terrified by your \n", "adversaries: a which is to them an \n", "evident token of perdition, but to \n", "you of salvation, and that of <PERSON>.\n", "29\n", " For unto you it is given in the be-\n", "half of Christ, not only to believe on \n", "him, but also to \n", "a suffer for his sake;\n", "30 Having the same conflict which \n", "ye saw in me, and now hear to be  \n", "in me.\n", "CHAPTER 2\n", "Saints should be of one mind and one \n", "spirit—Every knee will bow to <PERSON>—\n", "Saints must work out their salvation—\n", "<PERSON> faces martyrdom with joy.\n", "If there be therefore any consolation \n", "in Christ, if any comfort of love, if \n", "any fellowship of the Spirit, if any \n", "a bowels and mercies,\n", "2 Fulfil ye my joy, that ye be like-\n", "minded, having the same a love, being \n", "of b one c accord, of one mind.\n", "3 Let nothing be done through a strife \n", "or vainglory; but in lowliness of \n", "mind let each \n", "b esteem other better \n", "than themselves.\n", "4 Look not every man on his own \n", "things, but every man also on the \n", "things of others.\n", "14642 CORINTHIANS 4:14–5:15\n", "faith, according as it is written, I \n", "a believed, and therefore have I spo-\n", "ken; we also believe, and therefore \n", "b speak;\n", "14 Knowing that he which raised \n", "up the Lord Jesus shall raise up us \n", "also by <PERSON>, and shall present us  \n", "with you.\n", "15 For all things are for your sakes, \n", "that the abundant grace might \n", "through the \n", "a thanksgiving of many \n", "redound to the glory of God.\n", "16 For which cause we faint not; \n", "but though our outward man per-\n", "ish, yet the \n", "a inward man is renewed \n", "day by day.\n", "17 For our light a affliction, which \n", "is but for a moment, worketh for us \n", "a far more exceeding and \n", "b eternal \n", "c weight of glory;\n", "18  While we look not at the  \n", "things which are a seen, but at  \n", "the things which are not seen: for \n", "the things which are seen are tem-\n", "poral; but the things which are  \n", "not \n", "b seen are c eternal.\n", "CHAPTER 5\n", "Saints walk by faith and seek taber-\n", "nacles of immortal glory—The gospel \n", "reconciles man to <PERSON>—<PERSON>’s minis-\n", "ters carry the word of reconciliation \n", "to the world.\n", "For we know that if our earthly \n", "house of this  tabernacle were dis-\n", "solved, we have a building of God, \n", "an house not made with hands, \n", "eternal in the heavens.\n", "2\n", " For in this we groan, earnestly \n", "desiring to be a clothed upon with \n", "our house which is from heaven:\n", "3 If so be that being clothed we \n", "shall not be found naked.\n", "4 For we that are in this tabernacle \n", "do a groan, being burdened: not for \n", "that we would be unclothed, but \n", "clothed upon, that \n", "b mortality might \n", "be swallowed up of life.\n", "5 Now he that hath wrought us \n", "for the selfsame thing is <PERSON>, who \n", "also hath given unto us the earnest \n", "of the Spirit.\n", "6\n", " Therefore we are always a confi-\n", "dent, knowing that, whilst we are \n", "at home in the body, we are absent \n", "from the Lord:\n", "7\n", " (For we walk by a faith, not by \n", "b sight:)\n", "8 We are confident, I say, and will-\n", "ing rather to be absent from the body, \n", "and to be present with the Lord.\n", "9\n", " Wherefore we a labour, that, \n", "whether present or absent, we may\n", "1329 JOHN 3:15–33\n", "a serpent in the wilderness, even so \n", "must the b Son of man be lifted up:\n", "15 That whosoever believeth in \n", "him should not perish, but have \n", "eternal life.\n", "16 ¶ For a God so b loved the c world, \n", "that he d gave his e only begotten \n", "f Son, that whosoever g believeth in \n", "him should not perish, but have \n", "h everlasting i life.\n", "17 For God a sent not his Son into \n", "the world to b condemn the world; \n", "but that the world through him \n", "might be \n", "c saved.\n", "18 ¶ He that believeth on him is \n", "not condemned: but he that a be-\n", "<PERSON><PERSON>h not is condemned already, \n", "because he hath not believed in the \n", "b name of the only begotten c Son  \n", "of God.\n", "19 And this is the condemnation, \n", "that a light is come into the world, \n", "and men loved b darkness rather than \n", "light, because their c deeds were evil.\n", "20 For every one that doeth a evil \n", "b hateth the light, neither cometh \n", "to the light, lest his deeds should \n", "be reproved.\n", "21\n", " But he that a doeth b truth cometh \n", "to the c light, that his deeds may \n", "be made manifest, that they are \n", "wrought in <PERSON>.\n", "22\n", " ¶ After these things came Jesus \n", "and his disciples into the land of \n", "<PERSON><PERSON><PERSON><PERSON>; and there he tarried with \n", "them, and \n", "a baptized.\n", "23 ¶ And <PERSON> also was baptizing \n", "in Ænon near to Salim, because \n", "there was much \n", "a water there: and \n", "they came, and were b baptized.\n", "24 For <PERSON> was not yet cast into \n", "prison.\n", "25 ¶ Then there arose a question \n", "between some of <PERSON>’s disciples and \n", "the Jews about purifying.\n", "26 And they came unto John, and \n", "said unto him, Rabbi, he that was \n", "with thee beyond Jordan, to whom \n", "thou barest witness, behold, the \n", "same baptizeth, \n", "a and all men come  \n", "to him.\n", "27 <PERSON> answered and said, A man \n", "can a receive nothing, except it be \n", "given him from heaven.\n", "28 Ye yourselves bear me witness,  \n", "that I said, I am not the Christ,  \n", "but that I am sent before him.\n", "29 He that hath the bride is the \n", "bridegroom: but the friend of the \n", "bridegroom, which standeth and \n", "heareth him, rejoiceth greatly be-\n", "cause of the bridegroom’s voice: this\n", "1 1 a tg Stranger.\n", " 2 a Jo\n", "hn 6:44 (44, 63–65).  \n", "tg Election.\n", "  b tg Foreordination;  \n", "<PERSON>, Fore\n", "knowledge of.\n", "  c tg Sanctification.\n", "  d tg Obedience.\n", "  e Isa. 52:\n", "15.\n", "  f Ex. 24:\n", "8 (5–8);  \n", "Heb. 12:24.\n", " 3 a tg <PERSON>, Mercy of.\n", "  b tg Holy Ghost,  \n", "Baptism of;\n", "  \n", "<PERSON>, New, Spiritually \n", "<PERSON><PERSON>.\n", "  c gr living.\n", "  d tg <PERSON>.\n", "  e 1 Co\n", "r. 15:20.\n", " 4 a Matt. 6:\n", "20;  \n", "1 Co\n", "r. 9:25 (24–25);  \n", "Col. 1:5.\n", " 5 a Ro\n", "m. 1:16.\n", "  b Heb. 10\n", ":22 (22–24);  \n", "1 Pet. 1\n", ":21 (21–22).\n", "  c gr prepared.\n", " 6 a tg Probation.\n", "  b gr trials, afflictions. \n", "tg Temptation.\n", " 7 a tg Opposition;  \n", "Te\n", "st.\n", "  b gr revelation.\n", " 8 a Jo\n", "hn 20:29.\n", " 9 a gr goal, purpose, \n", "co\n", "nsummation. \n", "jst 1 Pet. 1:9 . . . object of \n", "yo\n", "ur faith . . .\n", "  b D&C 76\n", ":51 (51–70).\n", "  c Heb. 10\n", ":36.\n", " 10 a tg Salvation.\n", "  b <PERSON><PERSON> 13:17\n", ";  \n", "<PERSON> 24:27 (26–27);  \n", "Heb. 11:13;  \n", "2 Pet. 3:\n", "2.\n", " 11 a Ja\n", "cob 4:4; 7:11 (11–12);  \n", "<PERSON><PERSON><PERSON> 13:33 (33–35);  \n", "D&C 20:26.\n", "  b Heb. 12\n", ":2.\n", "  c D&C 58\n", ":3 (3–4).\n", " 12 a tg Holy Ghost,  \n", "Mission of.\n", "  b tg Angels.\n", " 13 a tg Levi<PERSON>.\n", "  b gr perfectly, completely.\n", "   \n", "The firs T epis Tle Gener Al of  \n", "peTer\n", "CHAPTER 1\n", "The trial of our faith precedes salva­\n", "tion—<PERSON> was foreordained to be \n", "the Redeemer.\n", "P\n", "<PERSON><PERSON> , an apostle of Je sus \n", "<PERSON>, to the a strangers scat­\n", "tered throughout Pontus, Gala­\n", "tia, Cappadocia, Asia, and Bithynia,\n", "2 a Elect according to the b fore­\n", "knowledge of <PERSON> the Father, \n", "through \n", "c sanctification of the Spirit, \n", "unto d obedience and e sprinkling \n", "of the f blood of <PERSON> Christ: Grace \n", "unto you, and peace, be multiplied.\n", "3 Ble\n", "ssed be the God and Father \n", "of our Lord Jesus <PERSON>, which ac­\n", "cording to his abundant \n", "a mercy hath \n", "b begotten us again unto a c lively \n", "d hope by the resurrection of e <PERSON> \n", "Christ from the dead,\n", "4 To\n", " an a inheritance incorruptible, \n", "and undefiled, and that fadeth not \n", "away, reserved in heaven for you,\n", "5\n", " Who a\n", "re kept by the a power of \n", "God through b faith unto salvation \n", "c ready to be revealed in the last  \n", "time.\n", "6 Wherein ye g\n", "reatly rejoice,\n", "1494COLOSSIANS 1:16–2:3\n", "16 For by him were all a things b cre-\n", "ated, that are in heaven, and that \n", "are in earth, visible and invisible, \n", "whether they be thrones, or domin-\n", "ions, or \n", "c principalities, or powers: \n", "all things were d created by him, \n", "and for him:\n", "17 And he is before all things, and \n", "by him all things a consist.\n", "18 And he is the a head of the body, \n", "the church: who is the beginning, \n", "the \n", "b firstborn from the dead; that \n", "in all things he might have the pre-\n", "eminence.\n", "19\n", " For it pleased the Father that in \n", "him should all a fulness dwell;\n", "20 And, having made peace through \n", "the blood of his cross, by him to \n", "a reconcile all things unto himself; \n", "by him, I say, whether they be things \n", "in earth, or things in heaven.\n", "21 And you, that were a sometime \n", "b alienated and enemies in your mind \n", "by wicked works, yet now hath he \n", "reconciled\n", "22\n", " In the body of his flesh through \n", "a death, to present you holy and \n", "b unblameable and unreproveable \n", "in his sight:\n", "23 If ye a continue in the faith \n", "b grounded and settled, and be not \n", "c moved away from the d hope of the \n", "gospel, which ye have heard, and \n", "which was preached to every crea-\n", "ture which is under heaven; whereof \n", "I <PERSON> am made a minister;\n", "24\n", " Who now rejoice in my suf-\n", "ferings for you, and fill up that \n", "which is behind of the \n", "a afflictions \n", "of Christ in my flesh for his body’s \n", "sake, which is the church:\n", "25\n", " Whereof I am made a minister, \n", "according to the a dispensation of \n", "God which is given to me for you, \n", "to fulfil the word of God;\n", "26\n", " Even the a mystery which hath \n", "been hid from ages and from gen-\n", "erations, but now is made \n", "b manifest \n", "to his saints:\n", "27 To whom God would make \n", "a known what is  the b riches of the  \n", "glory of this mystery among the \n", "Gentiles; which is Christ in you,  \n", "the hope of glory:\n", "28\n", " Whom we a preach, b warning \n", "every man, and teaching every man \n", "in all wisdom; that we may present \n", "every man perfect in <PERSON> Jesus:\n", "29\n", " Whereunto I also labour, striv-\n", "ing according to his working, which\n"]}], "source": ["query_text = \"Meaning of life\"\n", "results = collection.query(query_texts=[query_text],n_results=5,where={\"source\":\"https://www.churchofjesuschrist.org/bc/content/shared/content/english/pdf/language-materials/83291_eng.pdf\"})\n", "for result in results[\"documents\"][0]:\n", "    print(result)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"executionInfo": {"elapsed": 99, "status": "ok", "timestamp": 1754211457649, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "5YxV3E-8mYA6", "colab": {"base_uri": "https://localhost:8080/", "height": 140}, "outputId": "e41d79e6-99c8-4b46-9a3e-c1c8206f855f"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": ["\"[['1489 PHILIPPIANS 1:21–2:10\\\\nexpec tation and my a\\\\u200ahope, that in \\\\nnothing I shall be ashamed, but that \\\\nwith all boldness, as always, so now \\\\nalso Christ shall be b\\\\u200amagnified in \\\\nmy body, whether it be by life, or \\\\nby death.\\\\n21\\\\n For to me to live is Christ, and \\\\nto die is gain.\\\\n22 But if I live in the flesh, this is \\\\nthe fruit of my labour: yet what I \\\\nshall choose I \\\\na\\\\u200awot not.\\\\n23 For I am a\\\\u200ain a strait betwixt two, \\\\nhaving a desire to depart, and to \\\\nbe with <PERSON>; which is far better:\\\\n24\\\\n Nevertheless to a\\\\u200aabide in the \\\\nflesh is more needful for you.\\\\n25 And having this confidence, I \\\\nknow that I shall abide and continue \\\\nwith you all for your furtherance \\\\nand joy of faith;\\\\n26\\\\n That your rejoicing may be more \\\\nabundant in Jesus Christ for me by \\\\nmy coming to you again.\\\\n27\\\\n Only let your conversation be \\\\nas it becometh the gospel of Christ: \\\\nthat whether I come and see you, or \\\\nelse be absent, I may hear of your \\\\naffairs, that ye \\\\na\\\\u200astand fast in one \\\\nspirit, with b\\\\u200aone c\\\\u200amind d\\\\u200astriving \\\\ntogether for the faith of the gospel;\\\\n28 And in nothing terrified by your \\\\nadversaries: a\\\\u200awhich is to them an \\\\nevident token of perdition, but to \\\\nyou of salvation, and that of God.\\\\n29\\\\n For unto you it is given in the be-\\\\nhalf of Christ, not only to believe on \\\\nhim, but also to \\\\na\\\\u200asuffer for his sake;\\\\n30 Having the same conflict which \\\\nye saw in me, and now hear to be  \\\\nin me.\\\\nCHAPTER 2\\\\nSaints should be of one mind and one \\\\nspirit—Every knee will bow to Christ—\\\\nSaints must work out their salvation—\\\\nPaul faces martyrdom with joy.\\\\nIf there be therefore any consolation \\\\nin Christ, if any comfort of love, if \\\\nany fellowship of the Spirit, if any \\\\na\\\\u200abowels and mercies,\\\\n2 Fulfil ye my joy, that ye be like-\\\\nminded, having the same a\\\\u200alove, being \\\\nof b\\\\u200aone c\\\\u200aaccord, of one mind.\\\\n3 Let nothing be done through a\\\\u200astrife \\\\nor vainglory; but in lowliness of \\\\nmind let each \\\\nb\\\\u200aesteem other better \\\\nthan themselves.\\\\n4 Look not every man on his own \\\\nthings, but every man also on the \\\\nthings of others.', '14642 CORINTHIANS 4:14–5:15\\\\nfaith, according as it is written, I \\\\na\\\\u200abelieved, and therefore have I spo-\\\\nken; we also believe, and therefore \\\\nb\\\\u200aspeak;\\\\n14 Knowing that he which raised \\\\nup the Lord Jesus shall raise up us \\\\nalso by Jesus, and shall present us  \\\\nwith you.\\\\n15 For all things are for your sakes, \\\\nthat the abundant grace might \\\\nthrough the \\\\na\\\\u200athanksgiving of many \\\\nredound to the glory of God.\\\\n16 For which cause we faint not; \\\\nbut though our outward man per-\\\\nish, yet the \\\\na\\\\u200ainward man is renewed \\\\nday by day.\\\\n17 For our light a\\\\u200aaffliction, which \\\\nis but for a moment, worketh for us \\\\na far more exceeding and \\\\nb\\\\u200aeternal \\\\nc\\\\u200aweight of glory;\\\\n18  While we look not at the  \\\\nthings which are a\\\\u200aseen, but at  \\\\nthe things which are not seen: for \\\\nthe things which are seen are tem-\\\\nporal; but the things which are  \\\\nnot \\\\nb\\\\u200aseen are c\\\\u200aeternal.\\\\nCHAPTER 5\\\\nSaints walk by faith and seek taber-\\\\nnacles of immortal glory—The gospel \\\\nreconciles man to God—God’s minis-\\\\nters carry the word of reconciliation \\\\nto the world.\\\\nFor we know that if our earthly \\\\nhouse of this  tabernacle were dis-\\\\nsolved, we have a building of God, \\\\nan house not made with hands, \\\\neternal in the heavens.\\\\n2\\\\n For in this we groan, earnestly \\\\ndesiring to be a\\\\u200aclothed upon with \\\\nour house which is from heaven:\\\\n3 If so be that being clothed we \\\\nshall not be found naked.\\\\n4 For we that are in this tabernacle \\\\ndo a\\\\u200agroan, being burdened: not for \\\\nthat we would be unclothed, but \\\\nclothed upon, that \\\\nb\\\\u200amortality might \\\\nbe swallowed up of life.\\\\n5 Now he that hath wrought us \\\\nfor the selfsame thing is God, who \\\\nalso hath given unto us the earnest \\\\nof the Spirit.\\\\n6\\\\n Therefore we are always a\\\\u200aconfi-\\\\ndent, knowing that, whilst we are \\\\nat home in the body, we are absent \\\\nfrom the Lord:\\\\n7\\\\n (For we walk by a\\\\u200afaith, not by \\\\nb\\\\u200asight:)\\\\n8 We are confident, I say, and will-\\\\ning rather to be absent from the body, \\\\nand to be present with the Lord.\\\\n9\\\\n Wherefore we a\\\\u200alabour, that, \\\\nwhether present or absent, we may', '1329 JOHN 3:15–33\\\\na\\\\u200aserpent in the wilderness, even so \\\\nmust the b\\\\u200aSon of man be lifted up:\\\\n15 That whosoever believeth in \\\\nhim should not perish, but have \\\\neternal life.\\\\n16 ¶ For a\\\\u200aGod so b\\\\u200aloved the c\\\\u200aworld, \\\\nthat he d\\\\u200agave his e\\\\u200aonly begotten \\\\nf\\\\u200aSon, that whosoever g\\\\u200abelieveth in \\\\nhim should not perish, but have \\\\nh\\\\u200aeverlasting i\\\\u200alife.\\\\n17 For God a\\\\u200asent not his Son into \\\\nthe world to b\\\\u200acondemn the world; \\\\nbut that the world through him \\\\nmight be \\\\nc\\\\u200asaved.\\\\n18 ¶ He that believeth on him is \\\\nnot condemned: but he that a\\\\u200abe-\\\\nlieveth not is condemned already, \\\\nbecause he hath not believed in the \\\\nb\\\\u200aname of the only begotten c\\\\u200aSon  \\\\nof God.\\\\n19 And this is the condemnation, \\\\nthat a\\\\u200alight is come into the world, \\\\nand men loved b\\\\u200adarkness rather than \\\\nlight, because their c\\\\u200adeeds were evil.\\\\n20 For every one that doeth a\\\\u200aevil \\\\nb\\\\u200ahateth the light, neither cometh \\\\nto the light, lest his deeds should \\\\nbe reproved.\\\\n21\\\\n But he that a\\\\u200adoeth b\\\\u200atruth cometh \\\\nto the c\\\\u200alight, that his deeds may \\\\nbe made manifest, that they are \\\\nwrought in God.\\\\n22\\\\n ¶ After these things came Jesus \\\\nand his disciples into the land of \\\\nJudæa; and there he tarried with \\\\nthem, and \\\\na\\\\u200abaptized.\\\\n23 ¶ And John also was baptizing \\\\nin Ænon near to Salim, because \\\\nthere was much \\\\na\\\\u200awater there: and \\\\nthey came, and were b\\\\u200abaptized.\\\\n24 For John was not yet cast into \\\\nprison.\\\\n25 ¶ Then there arose a question \\\\nbetween some of John’s disciples and \\\\nthe Jews about purifying.\\\\n26 And they came unto John, and \\\\nsaid unto him, Rabbi, he that was \\\\nwith thee beyond Jordan, to whom \\\\nthou barest witness, behold, the \\\\nsame baptizeth, \\\\na\\\\u200aand all men come  \\\\nto him.\\\\n27 John answered and said, A man \\\\ncan a\\\\u200areceive nothing, except it be \\\\ngiven him from heaven.\\\\n28 Ye yourselves bear me witness,  \\\\nthat I said, I am not the Christ,  \\\\nbut that I am sent before him.\\\\n29 He that hath the bride is the \\\\nbridegroom: but the friend of the \\\\nbridegroom, which standeth and \\\\nheareth him, rejoiceth greatly be-\\\\ncause of the bridegroom’s voice: this', '1 1 a tg Stranger.\\\\n 2 a Jo\\\\nhn 6:44 (44, 63–65).  \\\\ntg Election.\\\\n  b tg Foreordination;  \\\\nGod, Fore\\\\nknowledge of.\\\\n  c tg Sanctification.\\\\n  d tg Obedience.\\\\n  e Isa. 52:\\\\n15.\\\\n  f Ex. 24:\\\\n8 (5–8);  \\\\nHeb. 12:24.\\\\n 3 a tg God, Mercy of.\\\\n  b tg Holy Ghost,  \\\\nBaptism of;\\\\n  \\\\nMan, New, Spiritually \\\\nReborn.\\\\n  c gr living.\\\\n  d tg Hope.\\\\n  e 1 Co\\\\nr. 15:20.\\\\n 4 a Matt. 6:\\\\n20;  \\\\n1 Co\\\\nr. 9:25 (24–25);  \\\\nCol. 1:5.\\\\n 5 a Ro\\\\nm. 1:16.\\\\n  b Heb. 10\\\\n:22 (22–24);  \\\\n1 Pet. 1\\\\n:21 (21–22).\\\\n  c gr prepared.\\\\n 6 a tg Probation.\\\\n  b gr trials, afflictions. \\\\ntg Temptation.\\\\n 7 a tg Opposition;  \\\\nTe\\\\nst.\\\\n  b gr revelation.\\\\n 8 a Jo\\\\nhn 20:29.\\\\n 9 a gr goal, purpose, \\\\nco\\\\nnsummation. \\\\njst 1 Pet. 1:9 . . . object of \\\\nyo\\\\nur faith . . .\\\\n  b D&C 76\\\\n:51 (51–70).\\\\n  c Heb. 10\\\\n:36.\\\\n 10 a tg Salvation.\\\\n  b Matt. 13:17\\\\n;  \\\\nLuke 24:27 (26–27);  \\\\nHeb. 11:13;  \\\\n2 Pet. 3:\\\\n2.\\\\n 11 a Ja\\\\ncob 4:4; 7:11 (11–12);  \\\\nMosiah 13:33 (33–35);  \\\\nD&C 20:26.\\\\n  b Heb. 12\\\\n:2.\\\\n  c D&C 58\\\\n:3 (3–4).\\\\n 12 a tg Holy Ghost,  \\\\nMission of.\\\\n  b tg Angels.\\\\n 13 a tg Levity.\\\\n  b gr perfectly, completely.\\\\n   \\\\nThe firs T epis Tle Gener Al of  \\\\npeTer\\\\nCHAPTER 1\\\\nThe trial of our faith precedes salva\\\\xad\\\\ntion—Christ was foreordained to be \\\\nthe Redeemer.\\\\nP\\\\nETER , an apostle of Je sus \\\\nChrist, to the a\\\\u200astrangers scat\\\\xad\\\\ntered throughout Pontus, Gala\\\\xad\\\\ntia, Cappadocia, Asia, and Bithynia,\\\\n2 a\\\\u200aElect according to the b\\\\u200afore\\\\xad\\\\nknowledge of God the Father, \\\\nthrough \\\\nc\\\\u200asanctification of the Spirit, \\\\nunto d\\\\u200aobedience and e\\\\u200asprinkling \\\\nof the f\\\\u200ablood of Jesus Christ: Grace \\\\nunto you, and peace, be multiplied.\\\\n3 Ble\\\\nssed be the God and Father \\\\nof our Lord Jesus Christ, which ac\\\\xad\\\\ncording to his abundant \\\\na\\\\u200amercy hath \\\\nb\\\\u200abegotten us again unto a c\\\\u200alively \\\\nd\\\\u200ahope by the resurrection of e\\\\u200aJesus \\\\nChrist from the dead,\\\\n4 To\\\\n an a\\\\u200ainheritance incorruptible, \\\\nand undefiled, and that fadeth not \\\\naway, reserved in heaven for you,\\\\n5\\\\n Who a\\\\nre kept by the a\\\\u200apower of \\\\nGod through b\\\\u200afaith unto salvation \\\\nc\\\\u200aready to be revealed in the last  \\\\ntime.\\\\n6 Wherein ye g\\\\nreatly rejoice,', '1494COLOSSIANS 1:16–2:3\\\\n16 For by him were all a\\\\u200athings b\\\\u200acre-\\\\nated, that are in heaven, and that \\\\nare in earth, visible and invisible, \\\\nwhether they be thrones, or domin-\\\\nions, or \\\\nc\\\\u200aprincipalities, or powers: \\\\nall things were d\\\\u200acreated by him, \\\\nand for him:\\\\n17 And he is before all things, and \\\\nby him all things a\\\\u200aconsist.\\\\n18 And he is the a\\\\u200ahead of the body, \\\\nthe church: who is the beginning, \\\\nthe \\\\nb\\\\u200afirstborn from the dead; that \\\\nin all things he might have the pre-\\\\neminence.\\\\n19\\\\n For it pleased the Father that in \\\\nhim should all a\\\\u200afulness dwell;\\\\n20 And, having made peace through \\\\nthe blood of his cross, by him to \\\\na\\\\u200areconcile all things unto himself; \\\\nby him, I say, whether they be things \\\\nin earth, or things in heaven.\\\\n21 And you, that were a\\\\u200asometime \\\\nb\\\\u200aalienated and enemies in your mind \\\\nby wicked works, yet now hath he \\\\nreconciled\\\\n22\\\\n In the body of his flesh through \\\\na\\\\u200adeath, to present you holy and \\\\nb\\\\u200aunblameable and unreproveable \\\\nin his sight:\\\\n23 If ye a\\\\u200acontinue in the faith \\\\nb\\\\u200agrounded and settled, and be not \\\\nc\\\\u200amoved away from the d\\\\u200ahope of the \\\\ngospel, which ye have heard, and \\\\nwhich was preached to every crea-\\\\nture which is under heaven; whereof \\\\nI Paul am made a minister;\\\\n24\\\\n Who now rejoice in my suf-\\\\nferings for you, and fill up that \\\\nwhich is behind of the \\\\na\\\\u200aafflictions \\\\nof Christ in my flesh for his body’s \\\\nsake, which is the church:\\\\n25\\\\n Whereof I am made a minister, \\\\naccording to the a\\\\u200adispensation of \\\\nGod which is given to me for you, \\\\nto fulfil the word of God;\\\\n26\\\\n Even the a\\\\u200amystery which hath \\\\nbeen hid from ages and from gen-\\\\nerations, but now is made \\\\nb\\\\u200amanifest \\\\nto his saints:\\\\n27 To whom God would make \\\\na\\\\u200aknown what is  the b\\\\u200ariches of the  \\\\nglory of this mystery among the \\\\nGentiles; which is Christ in you,  \\\\nthe hope of glory:\\\\n28\\\\n Whom we a\\\\u200apreach, b\\\\u200awarning \\\\nevery man, and teaching every man \\\\nin all wisdom; that we may present \\\\nevery man perfect in Christ Jesus:\\\\n29\\\\n Whereunto I also labour, striv-\\\\ning according to his working, which'], [{'page_no': 307, 'source': 'https://www.churchofjesuschrist.org/bc/content/shared/content/english/pdf/language-materials/83291_eng.pdf'}, {'page_no': 282, 'source': 'https://www.churchofjesuschrist.org/bc/content/shared/content/english/pdf/language-materials/83291_eng.pdf'}, {'page_no': 147, 'source': 'https://www.churchofjesuschrist.org/bc/content/shared/content/english/pdf/language-materials/83291_eng.pdf'}, {'page_no': 362, 'source': 'https://www.churchofjesuschrist.org/bc/content/shared/content/english/pdf/language-materials/83291_eng.pdf'}, {'page_no': 312, 'source': 'https://www.churchofjesuschrist.org/bc/content/shared/content/english/pdf/language-materials/83291_eng.pdf'}]]\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 12}], "source": ["combined_text = str(results['documents'] + results['metadatas'])\n", "combined_text"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"executionInfo": {"elapsed": 5, "status": "ok", "timestamp": 1754211457655, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "6_XRQhnXzEaz", "colab": {"base_uri": "https://localhost:8080/", "height": 17}, "outputId": "6dbf1e79-5e96-424f-8626-ff67cda11bc6"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}], "source": ["prompt = \"\"\"You are a helpful philosophy assistant. You will be given documents and will then use that to analyse. If there is Sanskrit\n", "or other languages transliterated, clean it up to provide the original language text (such as Sanskrit if they are present )\n", "as well as cleaned up English form. If foreign languages are not present in transliteration, it is fine to continue in English.\n", "Provide a nice analysis in a combination of languages as in the original text with proper markdown. At the end provide the citations\n", "from the source documents in a simplified, concise format\"\"\"\n", "\n", "def rag(query_text):\n", "    results = collection.query(query_texts=[query_text],n_results=5)\n", "    combined_sentence = \" \".join(result for result in results[\"documents\"][0])\n", "    query = prompt + \". The context is: \" + \\\n", "            combined_sentence + \"The question is :\" + \\\n", "            query_text\n", "\n", "    return llm_groq.invoke(query).content"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"executionInfo": {"elapsed": 2568, "status": "ok", "timestamp": 1754211460224, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "AXNHwm5Qhs3S", "colab": {"base_uri": "https://localhost:8080/", "height": 520}, "outputId": "078d164a-4f90-4978-d7eb-506c02999b8b"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["**Analysis**\n", "\n", "This document appears to be a collection of passages from various Christian and Hindu scriptures, including the Bible and the Bhagavad Gita. The passages are accompanied by Sanskrit transliterations and English translations.\n", "\n", "The Christian passages are from the New Testament, specifically from the First Epistle of Peter and other books. They discuss topics such as salvation, faith, and the nature of God.\n", "\n", "The Hindu passages are from the Bhagavad Gita, a Hindu scripture that is part of the Indian epic, the Mahabharata. They discuss topics such as the nature of the self, the importance of sacrifice, and the path to spiritual liberation.\n", "\n", "**Sanskrit Passages with English Translations**\n", "\n", "1. ** TEXT 31 **\n", "नाया लोकोऽस्त्यजस्य कुतोऽन्यः कुरुसत्तम (nāyā loko'sty ajasya kuto 'nyaḥ kuru-sattama)\n", "Translation: O best of the Kuru dynasty, without sacrifice one can never live happily on this planet.\n", "\n", "2. ** TEXT 20 **\n", "परस्तasmात्तु भावोऽन्योऽव्यक्तोऽव्यक्तात्सनातनः (paras tasmāt tu bhāvo 'nyaḥ avyakto 'vyaktāt sanātanaḥ)\n", "Translation: Yet there is another unmanifest nature, which is eternal and is transcendental to this manifested and unmanifested matter.\n", "\n", "3. ** TEXT 52 **\n", "यदा ते मोहकलिलं बुद्धिर्व्यतितरिण्याति तदा गन्तासि निर्वेदम् (yadā te mohakalilaṁ buddhir vyatitariñyati tadā gantāsi nirvedam)\n", "Translation: When your transcendental service with intelligence surpasses the dense forest of illusion, you shall go to a state of callousness.\n", "\n", "**Citations**\n", "\n", "* The Bible, First Epistle of <PERSON>, various verses\n", "* B<PERSON>ga<PERSON><PERSON> Gita, various verses (copyright 1998 The Bhaktivedanta Book Trust Int'l. All Rights Reserved.)\n"]}], "source": ["print(rag(\"Pillars of life\"))"]}, {"cell_type": "markdown", "metadata": {"id": "p_0_T_UBwLb1"}, "source": ["## 4. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 0}, "id": "6Su4XMVkvgnl", "outputId": "97612ffd-202f-4db6-a826-f039e2d3f669"}, "outputs": [{"data": {"text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.11/dist-packages/gradio/chat_interface.py:345: UserWarning: The 'tuples' format for chatbot messages is deprecated and will be removed in a future version of Gradio. Please set type='messages' instead, which uses openai-style 'role' and 'content' keys.\n", "  self.chatbot = Chatbot(\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["It looks like you are running Gradio on a hosted Jupyter notebook, which requires `share=True`. Automatically setting `share=True` (you can turn this off by setting `share=False` in `launch()` explicitly).\n", "\n", "Colab notebook detected. This cell will run indefinitely so that you can see errors and logs. To turn off, set debug=False in launch().\n", "* Running on public URL: https://0596dda52417f22fa3.gradio.live\n", "\n", "This share link expires in 1 week. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n"]}, {"data": {"text/html": ["<div><iframe src=\"https://0596dda52417f22fa3.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gradio as gr\n", "def chat(message, history):\n", "    return rag(message)\n", "\n", "demo = gr.<PERSON><PERSON>(\n", "    fn=chat,\n", "    title=\"Document chatbot\",\n", "    description=\"This is a chatbot built as part of Modern AI Pro Essentials program\",\n", ")\n", "demo.launch(debug=True)"]}], "metadata": {"colab": {"provenance": [{"file_id": "1OMYKRxbXARCn7oKlYLIZQK6GouUWAxXP", "timestamp": 1726102079582}]}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}}, "nbformat": 4, "nbformat_minor": 0}