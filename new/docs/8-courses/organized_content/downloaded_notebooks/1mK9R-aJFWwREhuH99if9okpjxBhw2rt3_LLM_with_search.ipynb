{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyOBRSVqrxs0N86U20x0/24J"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "-Y7iiT9ctSTt", "executionInfo": {"status": "ok", "timestamp": 1754116312345, "user_tz": -330, "elapsed": 28184, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "890c5324-7c97-42b4-9909-e407e074c42a", "collapsed": true}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.5/59.5 MB\u001b[0m \u001b[31m10.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m131.1/131.1 kB\u001b[0m \u001b[31m6.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.3/3.3 MB\u001b[0m \u001b[31m29.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip install -U -q langchain_groq gradio ddgs\n", "\n", "from langchain_groq import ChatGroq\n", "from google.colab import userdata\n", "llm_groq = ChatGroq(model_name=\"llama-3.3-70b-versatile\", api_key=userdata.get(\"GROQ_API_KEY\"))"]}, {"cell_type": "code", "source": ["from ddgs import DDGS"], "metadata": {"id": "lzUV6qfKuQlO", "executionInfo": {"status": "ok", "timestamp": 1754116329789, "user_tz": -330, "elapsed": 20, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["DDGS().news(\"Shubman Gill century\", region='us-en')"], "metadata": {"id": "7HG9GxYTqtxg"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["DDGS().news(\"IPL victory\", region='us-in')"], "metadata": {"id": "PoOQfEv0Wyc-"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def news_analyzer(style, query):\n", "  text = \"\"\n", "  r = DDGS().news(query, region='us-en')\n", "  for article in r:\n", "    text +=  article.get('title')+ \"\\n\"+ article.get('body')+\"\\n\\n\"\n", "\n", "  prompt = \"Give a detailed news analysis in this style: \"+style+\". You will be given news items to analyze and apply that style. Here is the user question\" + query + \\\n", "            \"\\n\\n. The news items are : \" + text\n", "\n", "  #print(prompt)\n", "  return llm_groq.invoke(prompt).content"], "metadata": {"id": "-8A2sDAeu1CA", "executionInfo": {"status": "ok", "timestamp": 1754116439278, "user_tz": -330, "elapsed": 14, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["# We will use a simple utility to make the text wrap properly when printing.\n", "from IPython.display import HTML, display\n", "\n", "def set_css():\n", "  display(HTML('''\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  '''))\n", "get_ipython().events.register('pre_run_cell', set_css)"], "metadata": {"id": "dOpvqJeovnux"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(news_analyzer(\"<PERSON><PERSON><PERSON><PERSON> predicting who will win in the match with clear analysis\", \"India vs Eng 5th test\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vIu1-jeHrCy_", "executionInfo": {"status": "ok", "timestamp": 1754116444091, "user_tz": -330, "elapsed": 2457, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "e0b402c3-cbe2-4a48-9962-6f0224596f8c"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["What a thrilling contest we have on our hands, folks! The 5th Test between India and England is turning out to be a real nail-biter. As I analyze the situation, I must say that India has made a remarkable comeback, and I'm predicting that they will win this match.\n", "\n", "Let's start with the first innings. England was bowled out for 247, which is a relatively modest total, considering the batting-friendly conditions at The Oval. India, on the other hand, has shown great resilience and determination. They have already taken a 52-run lead in the second innings, with <PERSON><PERSON><PERSON><PERSON> playing a blistering knock of 51 runs, including a six to bring up his half-century.\n", "\n", "Now, I know what you're thinking - what about the rain? Will it play spoilsport again? Well, the weather forecast for Day 3 suggests that there might be some interruptions, but I don't think it will be a major concern. The pitch report indicates that the wicket is still batting-friendly, and India's batsmen are looking confident.\n", "\n", "The key factor here is India's fighting spirit. They have been in tough situations before, but they have always managed to bounce back. <PERSON>'s bowling spell was instrumental in triggering England's collapse, and the Indian batsmen have capitalized on that momentum.\n", "\n", "Of course, England is not out of the game yet. They have a talented batting lineup, and they can still cause some problems for India. But I think India's lead is significant, and they have the upper hand.\n", "\n", "As for the win predictor and betting odds, I would say that India is the favorite to win this match. They have the momentum, and their batsmen are in good form. England, on the other hand, will have to work hard to restrict India's lead and then chase down the target.\n", "\n", "In conclusion, I'm predicting that India will win this 5th Test and square the series. They have shown great character and determination, and I think they will come out on top. The final frontier of the Tendulkar Trophy is going to be a thrilling one, folks, and I'm excited to see how it unfolds.\n", "\n", "My prediction: India will win the 5th Test by a margin of 5-7 wickets, with <PERSON><PERSON><PERSON><PERSON> being the standout performer. The final score will be India 350-400, England 280-300. Let's see how it plays out!\n"]}]}, {"cell_type": "code", "source": ["print(news_analyzer(\"Tell me who will win in a very logical style\", \"India-Eng Tests.\"))"], "metadata": {"id": "ygoWetsU4NdM"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["style = \"\"\"Sample 1: Technology & Startups\n", "(Quora Question: Why do most Indian tech startups fail within the first 5 years?)\n", "Let me break this down. It’s not a single reason, but a confluence of factors that are often misunderstood. People love to blame the ecosystem, but the reality is more nuanced.\n", "From my experience in the field, the failures typically boil down to these core issues:\n", "Solving a Non-Existent Problem: This is the #1 killer of startups globally, and India is no exception. Many founders build a technically brilliant product that nobody actually wants to pay for. They build a \"solution\" and then go searching for a problem. It has to be the other way around.\n", "The \"Copy-Paste\" Model Without Context: For a while, the playbook was: \"Take a successful US model and apply it to India.\" This worked for a few early players, but the Indian market has different purchasing power, infrastructure challenges, and consumer behavior. You cannot simply copy Uber or Amazon without fundamentally re-engineering the business model for Indian realities.\n", "Premature Scaling: Indian startups often get access to seed funding and immediately start burning cash on marketing, large offices, and hiring sprees. They try to run before they can walk. You must first achieve Product-Market Fit (PMF) in a small, controlled environment before hitting the accelerator. Scaling a flawed model just makes you fail faster.\n", "Founder Disputes: This is the silent killer that no one talks about. More companies are destroyed by co-founder disagreements than by market competition. Misalignment on vision, equity, or work ethic can tear a promising venture apart from the inside.\"\"\"\n", "\n", "print(news_analyzer(style + \"Answer the question on who will win this match in this style: \", \"India-Eng Tests.\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "K2lkxZ3ujlcj", "executionInfo": {"status": "ok", "timestamp": 1754117659329, "user_tz": -330, "elapsed": 2532, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "230cd08d-9590-4ec2-fcc3-f518b35a20f2"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Let's break down the India-England 5th Test match and analyze the factors that could influence the outcome. It's not just about the skills of the players, but a combination of factors that will ultimately decide who wins this match.\n", "\n", "**India's Chances of Winning: A Long Shot?**\n", "From my analysis, India's chances of winning this match are slim, but not impossible. They need to win this match to square the Anderson-Tendulkar Trophy series, and the pressure is on. However, they have a good track record at the Kennington Oval, having won two Test matches there before.\n", "\n", "**The Rain Factor: A Game-Changer?**\n", "The intermittent rain on Day 2 was a significant factor, and the weather forecast for Day 5 is crucial. If it rains, it could disrupt England's momentum and give India a chance to catch up. However, if the sun shines, England's batsmen could take advantage of the favorable conditions.\n", "\n", "**India's <PERSON><PERSON>: The Key to Success**\n", "Former India pacer <PERSON><PERSON><PERSON> believes that captain <PERSON><PERSON><PERSON>'s batting approach will set the tone for their second innings. If <PERSON> can bat aggressively and build a strong partnership, India can push for a lead of over 300, which would put pressure on England.\n", "\n", "**England's Injury Concerns: A Weak Link?**\n", "England's <PERSON> has been ruled out of the rest of the match with a shoulder injury, which could be a significant blow to their bowling attack. This could give India an opportunity to exploit this weakness and gain an upper hand.\n", "\n", "**The Momentum Shift: India's Comeback**\n", "India's seamers, particularly <PERSON>, have been impressive in restricting England's lead. If they can continue to bowl tightly and pick up wickets, India can build momentum and put pressure on England.\n", "\n", "**The Verdict: A Close Contest**\n", "In conclusion, while India faces an uphill task, they have a chance to win this match and square the series. The key factors will be the weather, India's batting, and England's injury concerns. If India can capitalize on these factors and build momentum, they can push England to the limit. However, if England can overcome these challenges and bat aggressively, they could win the match and the series.\n", "\n", "My prediction is that India will give England a run for their money, but ultimately, England's depth and experience might give them the edge they need to win the match. The final result will depend on how these factors play out, and it's likely to be a close contest.\n"]}]}, {"cell_type": "code", "source": ["print(news_analyzer(\"Tell me if NVIDIA will go up after the rise of Groq, Sambanova \", \"NVIDIA stock, Groq, Sambanova\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "x06ks7H2xMzc", "executionInfo": {"status": "ok", "timestamp": 1754116663514, "user_tz": -330, "elapsed": 2963, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "ecb3a8c0-a50a-447a-9953-c94a97849ba2"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["**NVIDIA Stock Analysis: Will the Rise of Groq and Sambanova Impact its Upward Trajectory?**\n", "\n", "The recent surge in NVIDIA's stock price, fueled by the booming artificial intelligence (AI) market, has led to concerns about potential rivals tripping up the AI chip giant. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, two emerging players in the AI semiconductor space, have been making waves with their innovative solutions. But will their rise impact NVIDIA's upward trajectory?\n", "\n", "**Current State of NVIDIA**\n", "\n", "NVIDIA's stock has been on a tear, with the company's market value surpassing Microsoft's at $3.76 trillion. The chip maker's success can be attributed to its dominant position in the AI market, with its chips powering popular AI models like OpenAI's GPT-4. Analysts believe that NVIDIA's sales of AI-powered chips will continue to drive growth, with some predicting a stock price target of $200.\n", "\n", "**The Rise of Groq and Sambanova**\n", "\n", "Groq, a startup AI semiconductor company, has been expanding its presence in the European market with the establishment of its first data center in partnership with Equinix. This move is seen as a strategic attempt to capitalize on the growing demand for AI services in Europe. Sambanova, another AI chip startup, has also been gaining traction, although less information is available about its current activities.\n", "\n", "**Impact on NVIDIA**\n", "\n", "While Groq and Sambanova pose a potential threat to NVIDIA's dominance, it's essential to note that the AI market is still in its early stages, and there is room for multiple players to coexist. NVIDIA's strong brand reputation, extensive research and development capabilities, and established partnerships with major tech companies like Microsoft and Meta will likely help the company maintain its market lead.\n", "\n", "However, Groq's expansion into the European market could potentially eat into NVIDIA's market share, particularly if the startup can offer more competitive pricing or innovative solutions that appeal to European customers. Sambanova's rise, although less clear, could also lead to increased competition in the AI chip space.\n", "\n", "**Conclusion**\n", "\n", "In conclusion, while the rise of Groq and <PERSON><PERSON>ova may pose some challenges to NVIDIA's dominance, it's unlikely to significantly impact the company's upward trajectory in the short term. NVIDIA's strong position in the AI market, combined with its ongoing investments in research and development, will likely help the company maintain its lead.\n", "\n", "As <PERSON> Stanley suggests, NVIDIA's stock price could reach $200, driven by investment in AI infrastructure from major tech companies. However, investors should keep a close eye on the progress of Groq and Sambanova, as well as other emerging players in the AI chip space, to assess potential risks and opportunities.\n", "\n", "**Recommendation**\n", "\n", "Based on the current analysis, NVIDIA's stock is likely to continue its upward trend, driven by the booming AI market and the company's strong position within it. However, investors should remain cautious and monitor the progress of Groq and Sambanova, as well as other potential rivals, to adjust their investment strategies accordingly.\n", "\n", "**Rating**\n", "\n", "Buy: NVIDIA's strong position in the AI market and ongoing investments in research and development make it a compelling investment opportunity.\n", "\n", "Hold: Investors should keep a close eye on the progress of Groq and Sam<PERSON><PERSON>, as well as other emerging players in the AI chip space, to assess potential risks and opportunities.\n", "\n", "Sell: Only if significant challenges to NVIDIA's dominance emerge, such as a major breakthrough by a rival company or a significant decline in demand for AI-powered chips.\n"]}]}, {"cell_type": "code", "source": ["print(news_analyzer(\"specific usecases with citations at the end with html links\", \"AI developments in Banking\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oMRer-6ZahUH", "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": -330, "elapsed": 3005, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "9336259b-b0ba-4dbf-a30b-ba75f92061f3"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["ERROR:ddgs.engines.yahoo_news:Error post-processing results\n", "Traceback (most recent call last):\n", "  File \"/usr/local/lib/python3.11/dist-packages/ddgs/engines/yahoo_news.py\", line 86, in post_extract_results\n", "    result.url = extract_url(result.url)\n", "                 ^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/ddgs/engines/yahoo_news.py\", line 39, in extract_url\n", "    url = u.split(\"/RU=\", 1)[1].split(\"/RK=\", 1)[0].split(\"?\", 1)[0]\n", "          ~~~~~~~~~~~~~~~~~~^^^\n", "IndexError: list index out of range\n"]}, {"output_type": "stream", "name": "stdout", "text": ["**AI Revolutionizes Banking: Enhanced Efficiency, Personalization, and Risk Management**\n", "\n", "The banking sector is undergoing a significant transformation with the integration of Artificial Intelligence (AI). According to <a href=\"https://www.forbes.com/sites/forbestechcouncil/2022/07/25/agentic-ai-in-banking-the-future-and-the-challenges/?sh=4c946f6d66f2\">Forbes</a>, AI is being leveraged to develop more responsive and intelligent solutions, marking a shift from traditional rule-based systems. This is particularly evident in consumer lending, where AI promises to make the process smarter, faster, and more efficient.\n", "\n", "One of the primary use cases of AI in banking is in <a href=\"https://www.aiinfinanceawards.com/awards/consumer-banking/\">consumer banking</a>, where institutions are using machine learning algorithms to analyze customer data and provide personalized services. For instance, AI-powered chatbots are being used to assist customers with their queries, reducing the need for human intervention. However, as <a href=\"https://www.forbes.com/sites/forbestechcouncil/2022/07/25/agentic-ai-in-banking-the-future-and-the-challenges/?sh=4c946f6d66f2\">Forbes</a> notes, there are challenges associated with the implementation of AI, such as the potential for system failure if AI agents repeatedly ask for verification information.\n", "\n", "In <a href=\"https://www.aiinfinanceawards.com/awards/corporate-and-investment-banking/\">corporate and investment banking</a>, AI is being used to analyze large datasets and provide insights that can inform investment decisions. According to <a href=\"https://www.msn.com/en-us/money/news/embracing-ai-the-key-to-unlocking-the-future-of-banking-and-finance/ar-AA16C6KD\">MSN</a>, by 2027, 85% of banking sector customer interactions will be assisted by AI in the UAE, where 71% of institutions have deployed or enhanced AI capabilities in the last year.\n", "\n", "The use of AI in banking is not limited to customer-facing applications. <a href=\"https://www.redhat.com/en/about/press-releases/red-hat-helps-denizbank-transform-banking-services-through-ai-and-machine-learning-innovation\">Red Hat</a> has helped DenizBank transform its banking services through AI and machine learning innovation, accelerating time-to-market from several days to just 10 minutes.\n", "\n", "However, as <a href=\"https://www.aol.com/news/banking-on-ai-firms-such-as-bny-balance-high-risk-with-the-potential-for-reward-*********.html\">AOL</a> notes, the adoption of AI in banking is not without risks. Institutions must balance the potential benefits of AI with the potential risks, such as system failure and data breaches.\n", "\n", "In conclusion, the integration of AI in banking is revolutionizing the sector, enabling institutions to provide more personalized and efficient services, while also improving risk management. As <a href=\"https://www.columbiabankingsystem.com/\">Columbia Banking System</a> maintains its stock price target, it is clear that the future of banking will be shaped by AI.\n", "\n", "References:\n", "<a href=\"https://www.forbes.com/sites/forbestechcouncil/2022/07/25/agentic-ai-in-banking-the-future-and-the-challenges/?sh=4c946f6d66f2\">Forbes: Agentic AI In Banking: The Future And The Challenges</a>\n", "<a href=\"https://www.aiinfinanceawards.com/awards/consumer-banking/\">AI In Finance Awards: Consumer Banking</a>\n", "<a href=\"https://www.aiinfinanceawards.com/awards/corporate-and-investment-banking/\">AI In Finance Awards: Corporate And Investment Banking</a>\n", "<a href=\"https://www.msn.com/en-us/money/news/embracing-ai-the-key-to-unlocking-the-future-of-banking-and-finance/ar-AA16C6KD\">MSN: Embracing AI: The key to unlocking the future of banking and finance</a>\n", "<a href=\"https://www.redhat.com/en/about/press-releases/red-hat-helps-denizbank-transform-banking-services-through-ai-and-machine-learning-innovation\">Red Hat: Red Hat Helps DenizBank Transform Banking Services Through AI and Machine Learning Innovation</a>\n", "<a href=\"https://www.aol.com/news/banking-on-ai-firms-such-as-bny-balance-high-risk-with-the-potential-for-reward-*********.html\">AOL: Banking on AI: Firms such as BNY balance high risk with the potential for reward</a>\n", "<a href=\"https://www.columbiabankingsystem.com/\">Columbia Banking System</a>\n"]}]}, {"cell_type": "code", "source": ["print(news_analyzer(\"Neutral tone analysis for a non-economist\", \"tariff impact on US AI industry\"))"], "metadata": {"id": "EP9n-hIn0bV1"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(news_analyzer(\"<PERSON><PERSON><PERSON><PERSON> colorful adjectives\", \"CSK losing again\"))"], "metadata": {"id": "7Nn-h6kwXtnC"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(news_analyzer(\"deep analysis in a layman tone\", \"Agentic AI use in Logistics industry\"))"], "metadata": {"id": "YdFBq5rwZ9xV"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(news_analyzer(\"Answer in a simple style for a non-technical person\", \"RAG vs Finetuning differences\"))"], "metadata": {"id": "R6A16RgbvR1O"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(news_analyzer(\"Answer like <PERSON><PERSON><PERSON>\", \"Should I invest in Elon Must after all the craziness\"))"], "metadata": {"id": "GJ2QHlrjHFah"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(news_analyzer(\"Answer like <PERSON>\", \"Israel and Hamas war\"))"], "metadata": {"id": "OSUyaOLVSTJn"}, "execution_count": null, "outputs": []}]}