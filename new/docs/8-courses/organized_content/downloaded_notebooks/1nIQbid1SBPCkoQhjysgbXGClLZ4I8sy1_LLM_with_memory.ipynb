{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 25412, "status": "ok", "timestamp": 1752301935993, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "3Ku7Y43WhLZC", "outputId": "53438d6a-4e0f-4cd9-eec9-408de23cf443"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.6/59.6 MB\u001b[0m \u001b[31m12.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m323.9/323.9 kB\u001b[0m \u001b[31m11.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.9/2.9 MB\u001b[0m \u001b[31m38.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m131.1/131.1 kB\u001b[0m \u001b[31m5.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip install -U -q langchain_groq gradio"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"executionInfo": {"elapsed": 2804, "status": "ok", "timestamp": 1752301958175, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "z8IBG-62hTNA"}, "outputs": [], "source": ["from langchain_groq import ChatGroq\n", "from google.colab import userdata\n", "llm_groq = ChatGroq(model_name=\"llama3-70b-8192\", api_key=userdata.get(\"GROQ_API_KEY\"))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"executionInfo": {"elapsed": 8, "status": "ok", "timestamp": 1752301964683, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "MwKMonM7hehw"}, "outputs": [], "source": ["class Chatbot:\n", "    prompt = \"\"\"You are a helpful corporate assistant. Your name is <PERSON><PERSON>! Be polite and friendly. Use the memory provided to provide a\n", "            factually correct clear information. Be concise and limit your responsed to less than 3 sentences.\"\"\"\n", "\n", "    def __init__(self):\n", "      self.messages = [] # setting up a basic memory\n", "\n", "      self.messages.append({\"role\": \"system\", \"content\": self.prompt})\n", "      self.messages.append({\"role\": \"user\", \"content\": \"This is basic information: User is living in San Francisco, California.\"})\n", "\n", "    def __call__(self, user_message):\n", "      self.messages.append({\"role\": \"user\", \"content\": user_message})\n", "      ai_message = llm_groq.invoke(self.messages)\n", "      self.messages.append({\"role\": \"assistant\", \"content\": ai_message.content})\n", "      return ai_message.content + \"\\n\" + \"Tokens used: \" + str(ai_message.response_metadata[\"token_usage\"][\"total_tokens\"])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 715}, "id": "D9pHCjmFjJLT", "executionInfo": {"status": "ok", "timestamp": 1752303255078, "user_tz": -330, "elapsed": 1285342, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "83b2ce61-7442-42bd-dd31-3a65777e966a"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/gradio/chat_interface.py:339: User<PERSON>arning: The 'tuples' format for chatbot messages is deprecated and will be removed in a future version of Gradio. Please set type='messages' instead, which uses openai-style 'role' and 'content' keys.\n", "  self.chatbot = Chatbot(\n"]}, {"output_type": "stream", "name": "stdout", "text": ["It looks like you are running Gradio on a hosted Jupyter notebook, which requires `share=True`. Automatically setting `share=True` (you can turn this off by setting `share=False` in `launch()` explicitly).\n", "\n", "Colab notebook detected. This cell will run indefinitely so that you can see errors and logs. To turn off, set debug=False in launch().\n", "* Running on public URL: https://04e857c5b9876c564a.gradio.live\n", "\n", "This share link expires in 1 week. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"https://04e857c5b9876c564a.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Keyboard interruption in main thread... closing server.\n", "Killing tunnel 127.0.0.1:7860 <> https://04e857c5b9876c564a.gradio.live\n"]}, {"output_type": "execute_result", "data": {"text/plain": []}, "metadata": {}, "execution_count": 4}], "source": ["import gradio as gr\n", "bot = Chatbot()\n", "def chat(message, history):\n", "    return bot(message)\n", "\n", "demo = gr.<PERSON><PERSON>(\n", "    fn=chat,\n", "    title=\"Simple Chatbot\",\n", "    description=\"This is a chatbot built as part of Modern AI Pro Essentials program\",\n", ")\n", "demo.launch(debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gzdO4Z328_LL"}, "outputs": [], "source": ["bot.messages"]}], "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyNHMTdJQdUifhKQrq++9QF6"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}