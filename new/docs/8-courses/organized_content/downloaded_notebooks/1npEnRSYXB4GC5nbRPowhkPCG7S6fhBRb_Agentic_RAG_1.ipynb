{"cells": [{"cell_type": "markdown", "metadata": {"id": "Ch4Qc1c-yXWO"}, "source": ["# Modern AI Pro: Advanced Chatbot on your enterprise content\n", "\n", "We will scrape the web content as well as our PDFs to answer user queries more effectively."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 167667, "status": "ok", "timestamp": 1752389107187, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "nh1ayWXLyvK_", "outputId": "391aee95-d2df-4324-9a62-9fe489ef27ae"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/67.3 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.3/67.3 kB\u001b[0m \u001b[31m5.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.5/2.5 MB\u001b[0m \u001b[31m29.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m19.5/19.5 MB\u001b[0m \u001b[31m55.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m232.6/232.6 kB\u001b[0m \u001b[31m13.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m470.2/470.2 kB\u001b[0m \u001b[31m27.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.6/59.6 MB\u001b[0m \u001b[31m14.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m323.9/323.9 kB\u001b[0m \u001b[31m15.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m284.2/284.2 kB\u001b[0m \u001b[31m18.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.9/2.9 MB\u001b[0m \u001b[31m46.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m131.1/131.1 kB\u001b[0m \u001b[31m8.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.9/1.9 MB\u001b[0m \u001b[31m14.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m101.6/101.6 kB\u001b[0m \u001b[31m4.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m16.5/16.5 MB\u001b[0m \u001b[31m59.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m65.6/65.6 kB\u001b[0m \u001b[31m3.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m72.5/72.5 kB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m119.4/119.4 kB\u001b[0m \u001b[31m6.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.6/201.6 kB\u001b[0m \u001b[31m11.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m105.4/105.4 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.2/71.2 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m45.2/45.2 kB\u001b[0m \u001b[31m2.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m67.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m56.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m37.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m1.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m9.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m6.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m87.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m459.8/459.8 kB\u001b[0m \u001b[31m28.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.9/50.9 kB\u001b[0m \u001b[31m3.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.0/4.0 MB\u001b[0m \u001b[31m101.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m453.1/453.1 kB\u001b[0m \u001b[31m28.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m3.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m6.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for pypika (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n"]}], "source": ["!pip install -U -q langchain langchain_groq langchain-community chromadb pypdf2 sentence-transformers gradio"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 1865, "status": "ok", "timestamp": 1752389109055, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "WsySZscCyrFB", "outputId": "8cecc3cc-9a73-40a2-902d-d796cd88ba81"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:langchain_community.utils.user_agent:USER_AGENT environment variable not set, consider setting it to identify your requests.\n"]}], "source": ["from langchain.schema.document import Document\n", "from langchain_community.embeddings import OllamaEmbeddings\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_community.vectorstores import Chroma\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from PyPDF2 import PdfReader"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 4892, "status": "ok", "timestamp": 1752389113948, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "wJPpVNvSy-tj", "outputId": "57b3052f-2fec-4583-9ad4-0ada2c425444"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading...\n", "From: https://drive.google.com/uc?id=1I8Ga_BK23OA8EEv3DtAmo5zw_Quud0ew\n", "To: /content/arso.pdf\n", "\r  0% 0.00/2.90M [00:00<?, ?B/s]\r100% 2.90M/2.90M [00:00<00:00, 91.2MB/s]\n"]}], "source": ["# 1. Get and Parse PDF\n", "!gdown 1I8Ga_BK23OA8EEv3DtAmo5zw_Quud0ew\n", "reader = PdfReader('arso.pdf')\n", "text = \"\"\n", "for i in range(0, len(reader.pages)):\n", "    page = reader.pages[i]\n", "    text += page.extract_text() + \" \""]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 140}, "executionInfo": {"elapsed": 341, "status": "ok", "timestamp": 1752389114297, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "B18ynKSo4Px4", "outputId": "28bcb9ec-56f8-4fd2-b656-7372f3a0b13d"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'an Seniors\\<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>and <PERSON>2\\nAbstract — Over the past 2 decades, Care robots (CR) have\\nbeen increasingly used to help seniors with a variety of tasks.\\nThe COVID-19 pandemic accelerated the use of CR causing\\nmixed reactions among seniors and caregivers. It is important\\nto understand how to make robots that seniors actually care\\nabout. How do we evaluate the kind of features and appearances\\ndesired from a care robot? In this paper we first present a\\nlikability scale to evaluate robots present and then use that\\nscale to survey of 31 seniors in Texas, USA about their attitude\\ntowards CR and the key tasks they expect from a robot. We\\ndiscuss the results and point to gender and age differences in the\\nresponses, as well as categories in which seniors take a stronger\\nposition. These seniors with an average age of 82, live in senior\\nliving or adult day cares. They have some exposure to robots to\\nprovide a more informed judgment. We find that the seniors,\\nespecially older ones and women have strong negative attitudes\\ntowards medical uses of care robots, while they have a overall\\npositive attitude towards care robots in general. Finally, we\\nuse the likability scale to score 6 robots – 5 commercial robots\\nalready in use for senior care and 1 non-senior care robot added\\nas a control element. We discuss the results on how particular\\nforms and designs increase likability.\\nI. INTRODUCTION\\nWorld is aging rapidly. By 2050, the number of seniors\\nabove 60 would double from the present figures to reach 2.1\\nbillion people. 426 million of these would be over 80 years\\nold [1]. This puts a strain on the care system across the\\nworld. The number of caregivers available to support seniors\\nis decreasing due to a variety of socioeconomic problems\\nthat cause a massive drop in the caregiver support ratio as\\nseen in Figure 1.\\nCare Robots (CR) are expected to fill the care gap and\\nthese are piloted across the world [2], [3]. These robots could\\nuse the state of the art in video recognition, conversation\\nand autonomous navigation to support the caregivers. If done\\nwell, they could both reduce cost and improve quality of care,\\nbringing the power of automation.\\nThere are a number of technical and social challenges\\nin achieving this goal. In our personal experience being in\\nthis industry for years, we have seen seniors figuratively and\\nsometimes literally pushing back the robots. Caregivers are\\nnervous if their jobs are in danger, while seniors often worry\\nabout being forced to give up human touch.\\nThere is a lack of research on what seniors actually expect\\nand desire from these robots. We are especially interested in\\n1Balaji Viswnathan, Mahalakshmi Radhakrushnun and Arvind Na-\\ngaraj are researchers at Invento Research Inc, Frisco, USA. balaji,\\nmahalakshmi, arvind @ mitrarobot.com\\n2Tim Oates is with the Department of Computer Science and\\nElectrical Engineering, University of Maryland, Baltimore County\\<EMAIL>\\nFig. 1: Caregiver support ratio in the USA. Infographic ©by\\nRIGHT ACCORD\\nthe attitudes of seniors over 80 years of age, who are the main\\ndemographic of senior care homes. It would be really helpful\\nto the robot designers as well as the various stakeholders\\n(governments, senior living homes, families, caregivers) to\\nunderstand how to build the right robot experience for the\\nseniors.\\nIn this paper, we address three key questions:\\n1) How do the seniors perceive robots, in general?\\n2) What use cases do they expect from the robots?\\n3) What kind of robot design do they like?\\nOur work is a first-of-kind in identifying attitudes toward\\ncare robots and evaluating likeability for this particular\\ndemographic.\\nThe 31 seniors we picked have some first-hand experience\\nwith robots, having observed them in their senior living\\ncenters. The survey participants knew that their responses\\nare critical to fine-tune their care. Therefore, you can see\\nstrong responses for some of the vital questions. We feel\\nthat the finer responses from this survey can help shape the\\nfuture of human-robot interaction.\\nII. RELATED WORK\\nIn the recent years, there has been a lot of attention on\\ncare robots. [2], [3], [4] provide a survey of recent advances\\nin care robots. There are number of ethical issues in the\\ndeployment of these care robots that are covered in [5]. While\\nmany of the works believe that the likability of robots need\\nto be seen different from other systems such as tablets and\\nlaptops, there are some [6] who argue that such perception\\ndifferences might be overestimated.\\nSeveral questions have been raised about attitudes that\\nprevent/limit the adoption of robots. [7], [8], [9] explore the\\nperceptions and attitudes of caregivers and support personnel\\ntoward robots. (a) Senior playing harmonica with the robot\\n(b) An expressive senior engaged with the robot\\nFig. 2: Some of the seniors working with our robots.\\nA limited number of works have surveyed the elderly\\npopulation, usually in a trade fair or an academic setting.\\nThese have produced mixed results in terms of acceptance.\\nAmong recent works, [10] points to a mostly negative\\nattitude of European seniors towards the adoption of robots.\\nAnother survey, done in Switzerland [11] point to a more\\npositive attitude in terms of acceptance. A work done in\\nCanada [12] also points to more positive attitudes among\\nseniors.\\nThe number of surveys are limited in scope and these also\\nhave not gone to seniors living in assisted living centers with\\nsome exposure to using robots. This could make some of\\nthe senior responses more speculative than seeing the robots\\nas something real that could serve them today. More work\\nis needed to understand the senior attitudes towards these\\nrobots.\\nOver the years, several works have looked at developing\\nlikability for robots as part of the broader Human-Robot\\nInteraction [13], [14], [15], [16].\\nThere are key differences in how seniors perceive the\\nrobots from how younger population see them [17]. This\\nmakes it imperative to survey the seniors who would end up\\nusing the robots.\\nIII. LIKABILITY INDEX\\nA. How do you see yourself using a care robot?\\nThis is the first part of the likability index. The goal is to\\nunderstand the general attitude of seniors to robots towards\\nrobots and identify what particular applications they envision\\nfor these robots.We picked a five-point scale, as a larger, finer grained\\nsurvey might be quite hard for seniors with cognitive ability\\nissues. A 5-point scoring provides reasonable granularity\\nwithout placing a lot of cognitive load on seniors.\\n1) Very Strongly Agree\\n2) Strongly Agree\\n3) Neutral Agree\\n4) Strongly Disagree\\n5) Very Strongly Disagree\\nWe selected eight key areas from our literature analysis\\nwhere people might require care robots. These were based\\non our observations working with seniors and their caregivers\\nin senior living homes over the past three years.\\n1) Bring water\\n2) Bring medicines\\n3) Call My Family\\n4) Entertainment\\n5) Connect to my Doc\\n6) Bring Food\\n7) Get Help\\n8) Play a Game\\nThree of these activities – Connect to my Doc, Bring\\nMedicines, Get Help are related to health, two for direct\\nassistance – Bring water, Bring Food while other three are\\nrelated to engagement. We believe there could be variations\\nin people’s attitude towards using robots for healthcare\\nactivities versus those for pure engagement.\\nThose who consistently gave low responses to most of the\\nactivities can be considered as more negative towards robots\\nin general and vice versa. This can be used to normalise the\\nlikability scores to compare the robots.\\nB. What kind of robot suit you better?\\nThis is the heart of the likability score. We want to\\nunderstand how end-users perceive robots and how to build\\nthe right kind of robot. We show the seniors pictures of\\nrobots, adjusting for similar resolution, picture quality and\\nhave them give their first impressions of what they like.\\nThis survey can be given quickly and can become a\\ngreat resource for designers to objectively evaluate between\\nmultiple robot design. The likability scores can also be used\\nby buyers and geriatric researchers.\\nWe picked 12 questions for this survey that primarily deal\\nwith perceptions and attitudes.\\n1) This robot is friendly in appearance\\n2) This robot looks creepy\\n3) This robot seems approachable\\n4) This robot is fun to look at\\n5) The robot looks to be smart\\n6) This robot might respond to what I say\\n7) The robot looks scary\\n8) I would trust this robot to be in caregiving team\\n9) I am open to engaging this robot in addition to my\\ncaregivers\\n10) This robot looks dumb\\n11) The size seems appropriate to me\\n12) I can see myself using the robot Fig. 3: Age distribution of the seniors in the survey.\\nC. Qualitative questions\\n1) Why do you feel positively or negatively about the\\nrobot? What makes it likeable or not?\\n2) Have you ever interacted with robots?\\n3) How comfortable are you with technology in general?\\n4) What devices are you most comfortable with?\\n5) What roles do you think robots can/should play in\\nhealthcare settings?\\nIV. METHOD\\nWe selected 31 seniors, 17 men, and 14 women. Majority\\nof them were in their 80s while some of them were in their\\n90s. They were in 2 locations – an adult day care center in\\nSan Antonio and a memory care center in Westover Hills.\\nBoth are in the state of Texas, USA. Their caregivers were\\npresent during the interview. Their age distribution can be\\nseen in Figure 3.\\nEach interview was about 90 minute long, where we\\nfirst introduced the idea of care robots then asked questions\\nrelated to their perceptions of robots in general and finally\\nfollowed up with questions related to likability of particular\\nmodels. The quantitative questions were followed by quali-\\ntative questions.\\nWe picked six robots for this survey: Pepper®, Mitra®,\\nTEMI®, Cruzr®, Atlas®, Mini Mitra®. The selected robots\\ncan be seen in Figure 4.\\nThree of these are humanoids (Pepper, Atlas, Mitra), while\\nthe other three are non-humanoids. Two of the robots were\\nabout five feet tall (Mitra and Atlas), two about four feet\\n(Cruzr, Pepper), while the other two (Mini Mitra and Temi)\\nwere under four feet. Five of these are already used in\\nseniorcare, while Atlas is an outlier. We use Atlas as a\\nbaseline to evaluate others. Also, Atlas-like robot design\\nmight have been seen in news or movies by seniors. This\\nallows us a chance to explore robot designs that are typically\\nnot used for senior care. We have commercial interests in 2\\nof these robots, but we took adequate care in designing the\\nrandomizing the question order to avoid interviewer bias.\\nFor the likability questions, they were shown one picture\\nof a robot and then asked them to rate it with our likability\\nscale. We did the same for all the 5 robots and randomised\\nthe order to reduce biases.Use case Score (0-5 with 5 being highest)\\nPlay a Game 4.30\\nEntertainment 3.80\\nGet Help 3.80\\nCall My Family 3.63\\nBring water 3.43\\nBring Food 3.34\\nConnect to my Doc 3.21\\nBring Medicine 2.73\\nMean 3.53\\nTABLE I: Attitudes towards key use cases.\\nV. RESULTS AND DISCUSSIONS\\nA. General attitude towards robots\\nTable I summarizes the senior attitudes towards the key\\nuse cases. The key thing we found is that seniors prefer\\nrobots more for engagement-related activities (Play a Game\\nor Entertainment) than for healthcare-related activities (Bring\\nMedicine or Connect to my Doc). This result was also\\ncorroborated in the qualitative interviews where the seniors\\nwere apprehensive about using a novel technology for a\\ncritical life and death situation. The seniors preferred the\\nrobots to provide additional activities to what they already\\nreceive rather than potentially replacing an existing service.\\nThere is also an interesting dynamic that can be seen in\\nFigure 5. We see that the healthcare use cases generate a\\nstrong response from the seniors (either 1 or 5) with some\\nvery strongly for it and others strongly against. There are\\nvery few people who walk in the middle. On other other\\nhand, in engagement related use cases there are some who\\ndon’t hold very strong opinion in either direction.\\nThe result gives hope to robot designers because the\\naverage acceptance score across the eight categories is about\\n3.5/5. This indicates that the seniors are overall positive about\\nthe use of care robots. However, more work is needed to\\nconvince seniors that robots can be used in more medical\\napplications. We recommend that industry and government\\ntake confidence building activities to prepare seniors to\\naccept medical care from robots when technology matures. (a) Atlas®\\n (b) Mini Mitra®\\n (c) Mitra®\\n (d) TEMI®\\n (e) Pepper®\\n (f) Cruzr®\\nFig. 4: The robots used for this survey.\\nFig. 5: Score distribution of key use cases.\\nB. Gender-wise attitude differences\\nFig. 6: Gender difference in getting help (men on left, women\\non right).\\nWe noticed certain attitude differences between males and\\nfemales, even though their mean attitude is comparable (3.55\\nfor males vs 3.51 for females). For instance, in engagement-\\nrelated activities and connecting activities there was a sub-\\nstantial difference between the genders. The males were very\\nbullish on engagement activities such as asking the robotUse case Males Females\\nPlay a Game 4.41 4.15\\nEntertainment 3.94 3.62\\nGet Help 4.00 3.54\\nCall My Family 3.35 4.00\\nBring water 3.41 3.46\\nBring Food 3.25 3.46\\nConnect to my Doc 3.12 3.31\\nBring Medicine 2.88 2.54\\nMean 3.55 3.51\\nTABLE II: Gender differences in attitudes towards key use\\ncases.\\nto play a game, while the females were more positive on\\nactivities such as calling the family.\\nYou can see the distribution for 2 selected use cases in\\nFigure 6. We can notice that the females were strongly for\\nthe ”Call my family” use case, while the males were divided\\nwith strong opinions (1 or 5). In the case of the ”Get help”\\nuse case, males were strongly bullish, while female opinions\\nwere quite distributed even if they were overall more positive\\nfor this use case.\\nC. Age-wise attitude differences\\nWe divided the population into 2 groups, ≤80 years old\\nand those over >80. There were 14 people in the former\\nbucket and 17 people in the latter. There were noticeable\\ndifferences between the 2 groups.\\nSeniors below 80 years were far more positive about robots\\nthan those over 80 years of age. The biggest difference can be\\nseen in the healthcare use cases, such as ”Bring Medicine”,\\nand engagement cases, such as ”Entertainment”. The one\\ncategory that bucked this trend is the ”Call my family” where Use case Seniors ≤80 years Seniors >80years\\nPlay a Game 4.43 4.19\\nEntertainment 4.14 3.50\\nGet Help 4.14 3.50\\nCall My Family 3.57 3.69\\nBring water 3.50 3.38\\nBring Food 3.62 3.12\\nConnect to my Doc 3.31 3.12\\nBring Medicine 3.29 2.25\\nMean 3.75 3.34\\nTABLE III: Age differences in attitudes towards key use\\ncases.\\nthe older populace were slightly more positive than their\\nyounger counterparts.\\nFigure 7 shows the histograms for a couple of use cases.\\nIn the ”Bring Medicine” use case, the majority of seniors\\nover 80 were giving a strong response (1) while those in the\\nyounger cohort were evenly divided. An inverse dynamic\\nis seen for ”Get Help” where the younger cohort decided\\nstrongly for this, while the older cohort is evenly divided.\\nFig. 7: Age differences in attitudes toward specific activities.\\nFig. 8: Likability of various robots.\\nD. Likability of different robots\\nFigures 8 and 9 show the results of the likability survey.\\nWe have results in 10 categories. Note that the survey have\\nboth positive and negative questions. A score of 1 in an\\nnegative category ”Dumb” means that the users strongly\\ndisagreed with the statement and vice versa.\\nPepper®and Mini Mitra®tended to rank highest in many\\ncategories, while predictably Atlas came a few spots lower in\\nmost categories. Given that the Atlas robot was not designed\\nfor senior care use case, such a result was expected before\\nthe survey. The presence of Atlas serves as a control group\\nhere, enabling us to objectively look at the survey results.\\nPepper scored the best in the ”Creepy” and ”Scary”\\ncategories, as nearly everyone agreed that it is neither creepy\\nnor scary, while Mini Mitra topped the category in approach-\\nability. Both Mitra and Mini Mitra scored high on Fun metric.\\nSeniors tended to prefer more well-rounded bodies rather\\nthan designs with exposed metal. And there is a bias towards\\nhumanoid form factors. Based on the limited sample, we\\nbelieve that robots of about 4 feet tall suited well for this\\naudience (older Americans). There are likely cultural factors\\ntoo and the height and humanoid preferences may vary for\\nother demographics.\\nOverall, the robots used for senior care tended to score\\nabove average on all the positive categories, giving hope that Fig. 9: Likability of various robots (contd).\\nthe designers have taken some care on acceptance.\\nVI. CONCLUSIONS\\nFor the past 20 years, the industry has been deploying Care\\nRobots (CR). However, we have not had enough usability\\nstudies with end users, seniors who would be using robots.\\nWe not only need to know their overall attitudes, but also\\nneed to have fine-grained details on what kind of use cases\\nthey prefer and the kind of designs that suit well for the\\nseniors.\\nWe have presented our likability index that could be used\\nto evaluate a range of robots, and we have also used the index\\nto survey 31 people in senior living homes. The results were\\nvery promising for future robot designers and we have also\\nhighlighted key areas of improvement – such as in improving\\nconfidence that the robots can be used in critical healthcare\\napplications.\\nJust as we noted patterns based on age and gender, we\\nalso believe that there could be critical differences in attitudes\\nbased on culture/ethnicity and income levels. We are working\\non these areas as part of the future plan.\\nREFERENCES\\n[1] Ageing and health. [Online]. Available: https://www.who.int/news-\\nroom/fact-sheets/detail/ageing-and-health[2] A. Khan and Y . Anwar, “Robots in healthcare: A survey,” in Advances\\nin Computer Vision , ser. Advances in Intelligent Systems and Comput-\\ning, K. Arai and S. Kapoor, Eds. Springer International Publishing,\\npp. 280–292.\\n[3] K. K. Santhanaraj, R. M.M., and D. D., “A survey of assistive\\nrobots and systems for elderly care,” vol. 15, no. 1, pp. 66–\\n72, publisher: Emerald Publishing Limited. [Online]. Available:\\nhttps://doi.org/10.1108/JET-10-2020-0043\\n[4] P. Asgharian, A. M. Panchea, and F. Ferland, “A review on the use of\\nmobile service robots in elderly care,” vol. 11, no. 6, p. 127, number:\\n6 Publisher: Multidisciplinary Digital Publishing Institute. [Online].\\nAvailable: https://www.mdpi.com/2218-6581/11/6/127\\n[5] J. P. Boada, B. R. Maestre, and C. T. Gen ´ıs, “The\\nethical issues of social assistive robotics: A critical\\nliterature review,” vol. 67, p. 101726. [Online]. Available:\\nhttps://www.sciencedirect.com/science/article/pii/S0160791X21002013\\n[6] B. Leichtmann and V . Nitsch, “Is the social desirability effect\\nin human–robot interaction overestimated? a conceptual replication\\nstudy indicates less robust effects,” vol. 13, no. 5, pp. 1013–1031.\\n[Online]. Available: https://doi.org/10.1007/s12369-020-00688-z\\n[7] Z. Britel and A. Cherkaoui, “Understanding professional care\\nproviders readiness towards the adoption of care robots for elderly\\ncare,” in Robot Intelligence Technology and Applications 6 , ser.\\nLecture Notes in Networks and Systems, J. Kim, B. Englot, H.-W.\\nPark, H.-L. Choi, H. Myung, J. Kim, and J.-H. Kim, Eds. Springer\\nInternational Publishing, pp. 531–542.\\n[8] E. Na, Y . Jung, and S. Kim, “How do care service\\nmanagers and workers perceive care robot adoption in\\nelderly care facilities?” vol. 187, p. 122250. [Online]. Available:\\nhttps://www.sciencedirect.com/science/article/pii/S0040162522007715\\n[9] T. Rantanen, P. Lehto, P. Vuorinen, and K. Coco, “The adoption\\nof care robots in home care—a survey on the attitudes of finnish\\nhome care personnel,” vol. 27, no. 9, pp. 1846–1859, eprint:\\nhttps://onlinelibrary.wiley.com/doi/pdf/10.1111/jocn.14355. [Online].\\nAvailable: https://onlinelibrary.wiley.com/doi/abs/10.1111/jocn.14355\\n[10] R.-M. Johansson-Pajala, V . Zander, C. Gustafsson, and A. Gusdal,\\n“No thank you to humanized robots: attitudes to care robots in\\nelder care services,” vol. 41, no. 1, pp. 40–53, publisher: Taylor\\n& Francis eprint: https://doi.org/10.1080/01621424.2022.2052221.\\n[Online]. Available: https://doi.org/10.1080/01621424.2022.2052221\\n[11] S. Lehmann, E. Ruf, and S. Misoch, “Robot use for older adults –\\nattitudes, wishes and concerns. first results from switzerland,” in HCI\\nInternational 2020 - Posters , ser. Communications in Computer and\\nInformation Science, C. Stephanidis and M. Antona, Eds. Springer\\nInternational Publishing, pp. 64–70.\\n[12] J. A. Dosso, E. Bandari, A. Malhotra, G. K. Guerra, J. Hoey,\\nF. Michaud, T. J. Prescott, and J. M. Robillard, “User perspectives\\non emotionally aligned social robots for older adults and\\npersons living with dementia,” vol. 9, p. 20556683221108364,\\npublisher: SAGE Publications Ltd STM. [Online]. Available:\\nhttps://doi.org/10.1177/20556683221108364\\n[13] N. L. Robinson, T.-N. Hicks, G. Suddrey, and D. J. Kavanagh, “The\\nrobot self-efficacy scale: Robot self-efficacy, likability and willingness\\nto interact increases after a robot-delivered tutorial,” in 2020 29th\\nIEEE International Conference on Robot and Human Interactive\\nCommunication (RO-MAN) , pp. 272–277, ISSN: 1944-9437.\\n[14] Castro-Gonz ´alez, H. Admoni, and B. Scassellati,\\n“Effects of form and motion on judgments of\\nsocial robots animacy, likability, trustworthiness and\\nunpleasantness,” vol. 90, pp. 27–38. [Online]. Available:\\nhttps://www.sciencedirect.com/science/article/pii/S107158191600032X\\n[15] R. Oliveira, P. Arriaga, M. Axelsson, and A. Paiva, “Humor–robot\\ninteraction: A scoping review of the literature and future\\ndirections,” vol. 13, no. 6, pp. 1369–1383. [Online]. Available:\\nhttps://doi.org/10.1007/s12369-020-00727-9\\n[16] E. B. Sandoval, J. Brandstatter, U. Yalcin, and C. Bartneck,\\n“Robot likeability and reciprocity in human robot interaction:\\nUsing ultimatum game to determinate reciprocal likeable robot\\nstrategies,” vol. 13, no. 4, pp. 851–862. [Online]. Available:\\nhttps://doi.org/10.1007/s12369-020-00658-5\\n[17] W. Khaksar, M. Neggers, E. Barakova, and J. Torresen, “Generation\\ndifferences in perception of the elderly care robot,” in 2021 30th IEEE\\nInternational Conference on Robot & Human Interactive Communi-\\ncation (RO-MAN) , pp. 551–558, ISSN: 1944-9437. '"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["text[100:]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424}, "executionInfo": {"elapsed": 70810, "status": "ok", "timestamp": 1752389185122, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "tuFVRsJRzxhl", "outputId": "95c1fd99-632d-4061-eb82-bed7382c566e"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipython-input-5-755650907.py:3: LangChainDeprecationWarning: The class `HuggingFaceEmbeddings` was deprecated in LangChain 0.2.2 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-huggingface package and should be used instead. To use it run `pip install -U :class:`~langchain-huggingface` and import as `from :class:`~langchain_huggingface import HuggingFaceEmbeddings``.\n", "  embeddings = HuggingFaceEmbeddings(model_name=\"sentence-transformers/all-mpnet-base-v2\", model_kwargs={\"device\": \"cpu\"})\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a56da2fbcec343fd9397a28bf99919bb", "version_major": 2, "version_minor": 0}, "text/plain": ["modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dc81cd9b75ef429990101ba63eebaa4e", "version_major": 2, "version_minor": 0}, "text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/116 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7b14c265ba334e038ba06577e979ccd5", "version_major": 2, "version_minor": 0}, "text/plain": ["README.md: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a24219c797a24e5e88577858978f67b9", "version_major": 2, "version_minor": 0}, "text/plain": ["sentence_bert_config.json:   0%|          | 0.00/53.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "42c65e5e0d304213803ade6b011a047b", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/571 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9858706cc41645188ad78ba58f809a3c", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/438M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "62f2a6c70d4947ee8fe3897e1b1b5d32", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/363 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a958b44ff0ed4665bce77fa8b6da632f", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.txt: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a00857bda6574175a6e863e96bccc0a5", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b835cbbc57df4eac88ab18022f9d28ec", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/239 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f267af1376904d58bedcd395f4f58e63", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#2. Get the embeddings.\n", "from langchain.embeddings import HuggingFaceEmbeddings\n", "embeddings = HuggingFaceEmbeddings(model_name=\"sentence-transformers/all-mpnet-base-v2\", model_kwargs={\"device\": \"cpu\"})"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"executionInfo": {"elapsed": 30518, "status": "ok", "timestamp": 1752389215642, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "eMInyINxzjMJ"}, "outputs": [], "source": ["# 3. Split document & store it in db\n", "documents = [Document(page_content=text, metadata={\"source\": \"local\"})]\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=200, chunk_overlap=40)\n", "all_splits = text_splitter.split_documents(documents)\n", "\n", "collection = Chroma.from_documents(\n", "    documents=all_splits, embedding=embeddings, persist_directory=\"doc_vectors\")\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"executionInfo": {"elapsed": 6200, "status": "ok", "timestamp": 1752389221845, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "pVF6ynKz0OCC"}, "outputs": [], "source": ["# 4. Add more items to the collection\n", "loader = WebBaseLoader(web_path=(\"https://mitrarobot.com/\"))\n", "docs = loader.load()\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=200, chunk_overlap=40)\n", "splits = text_splitter.split_documents(docs)\n", "\n", "for s in splits:\n", "    collection.add_documents([s])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"executionInfo": {"elapsed": 4525, "status": "ok", "timestamp": 1752389226369, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "GUj6bbf00tb9"}, "outputs": [], "source": ["# 5. Setup the LLM\n", "from langchain_groq import ChatGroq\n", "from google.colab import userdata\n", "llm3 = ChatGroq(model_name=\"llama-3.3-70b-versatile\", api_key=userdata.get(\"GROQ_API_KEY\"))\n", "llm2 = ChatGroq(model_name=\"llama-3.1-8b-instant\", api_key=userdata.get(\"GROQ_API_KEY\"))\n", "llm1 = ChatGroq(model_name=\"meta-llama/llama-4-scout-17b-16e-instruct\", api_key=userdata.get(\"GROQ_API_KEY\"))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 226}, "executionInfo": {"elapsed": 46384, "status": "ok", "timestamp": 1752389272752, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "lwVJeh8-Zm1q", "outputId": "b064f580-e5cf-42e8-b36b-43d2cd53c2cd"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "98cc547e0d2e467a92aa22ec7088ada2", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8702ce916f9d4f5284e0be4cde139e90", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/1.63G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d7d24b8dc8f8441f88d867b80d8c5d13", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/26.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6ae1886f5d884ec697f54d5d8e674109", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8c8796e0641a43449bee485a277c3c1a", "version_major": 2, "version_minor": 0}, "text/plain": ["merges.txt: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d26bad9d08764c37a047afe203802e61", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Device set to use cpu\n"]}], "source": ["from transformers import pipeline\n", "roberta = pipeline(\"zero-shot-classification\",model=\"facebook/bart-large-mnli\", device=\"cpu\")\n", "\n", "def llm_router(message):\n", "  candidate_labels = ['billing','new sales','customer support', 'other']\n", "  output = roberta(message, candidate_labels)\n", "  max_score_index = output['scores'].index(max(output['scores']))\n", "\n", "   # Retrieve the corresponding label\n", "  print(output['labels'][max_score_index])\n", "\n", "  if output['labels'][max_score_index] == 'new sales':\n", "    return llm1\n", "  elif output['labels'][max_score_index] == 'customer support':\n", "    return llm2\n", "  else:\n", "    return llm3\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 4516, "status": "ok", "timestamp": 1752389277271, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "AuX60msV6nO3", "outputId": "c40db205-2c89-4718-c6a9-0c6f5951a6ed"}, "outputs": [{"data": {"text/plain": ["{'sequence': 'new sales',\n", " 'labels': ['other', 'product', 'purchase', 'complaints'],\n", " 'scores': [0.3986864686012268,\n", "  0.32544460892677307,\n", "  0.2675513029098511,\n", "  0.00831760186702013]}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["output = roberta(\"new sales\", [\"complaints\", \"purchase\", \"product\", \"other\"])\n", "output"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 3209, "status": "ok", "timestamp": 1752389280481, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "JKl-xrFkbRz7", "outputId": "72cd27aa-e392-4e14-d2c5-f673b35b7b3e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["new sales\n"]}, {"data": {"text/plain": ["AIMessage(content=\"As a conversational AI, I don't have a sales team or direct connections with external teams. I exist solely as a digital entity, and my purpose is to provide information and assist with inquiries. However, I can certainly help you explore ways to connect with potential customers or clients for new sales!\\n\\nHere are some general suggestions:\\n\\n1. **Social Media**: Leverage social media platforms like LinkedIn, Twitter, or Facebook to reach out to potential customers, share valuable content, and engage with your target audience.\\n2. **Networking Events**: Attend industry conferences, trade shows, or networking events to connect with people in your industry and build relationships that can lead to new sales opportunities.\\n3. **Referrals and Word-of-Mouth**: Encourage happy customers to refer your business to their network. Offer incentives, such as discounts or rewards, for successful referrals.\\n4. **Content Marketing**: Create informative and engaging content (blog posts, videos, podcasts, etc.) that showcases your expertise and provides value to potential customers.\\n5. **Email Marketing**: Build an email list and send targeted campaigns to potential customers, highlighting your products or services and offering special promotions or discounts.\\n6. **Partnerships and Collaborations**: Partner with complementary businesses to expand your reach and offer joint solutions to potential customers.\\n7. **Sales Outreach**: Utilize sales outreach tools like CRM software, sales automation platforms, or email outreach tools to streamline your sales process and connect with potential customers.\\n\\nIf you're looking for more specific advice or have a particular industry or market in mind, feel free to share more details, and I'll do my best to provide more tailored guidance!\", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 330, 'prompt_tokens': 20, 'total_tokens': 350, 'completion_time': 0.654276025, 'prompt_time': 0.002371157, 'queue_time': 0.194372086, 'total_time': 0.656647182}, 'model_name': 'meta-llama/llama-4-scout-17b-16e-instruct', 'system_fingerprint': 'fp_37da608fc1', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--1ca132b6-ba17-4df6-89e1-f17d48add32b-0', usage_metadata={'input_tokens': 20, 'output_tokens': 330, 'total_tokens': 350})"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"Can you connect with your sales team for new sales\"\n", "llm = llm_router(message)\n", "llm.invoke(message)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"executionInfo": {"elapsed": 32, "status": "ok", "timestamp": 1752389280522, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "WqWmxbtp08Tw"}, "outputs": [], "source": ["#6. Setup the Memory\n", "import time\n", "from typing import List, Dict, Any, Optional\n", "from dataclasses import dataclass\n", "import logging\n", "\n", "# EL<PERSON> (Logstash, Kibana), <PERSON><PERSON>\n", "logger = logging.getLogger(__name__)\n", "\n", "@dataclass\n", "class ChatSummary:\n", "    timestamp: float\n", "    content: str\n", "    topics: List[str]  # Key topics in this conversation segment\n", "\n", "class Chatbot:\n", "    prompt = \"\"\"\n", "      Your name is <PERSON><PERSON>. Where ever the reference come, replace with our company. You are an assistant for question-answering tasks for Mitra Robot customer support.\n", "      You need to use the following pieces of retrieved context to answer the question.\n", "      Use 5-7 sentences maximum and keep the answer reasonably concise. Answer in a professional tone.\n", "      Modern AI Pro and Young AI Pro are not robots, but our AI courses if people ask for it.\n", "      Answer only robot or AI related questions and nothing else. If they ask something like <PERSON><PERSON><PERSON> bring them back to the conversation\n", "      If there is a contact detail or anything you are offering make sure it exits.\n", "      If there is a sales enquiry, point <NAME_EMAIL>. You don't have to repeat it if already given.\n", "      \"\"\"\n", "\n", "    # Configuration parameters\n", "    MAX_RECENT_MESSAGES = 10\n", "    MAX_SUMMARIES = 5  # Keep only the N most recent summaries\n", "    RELEVANT_DOCS_COUNT = 10  # Number of relevant documents to fetch\n", "\n", "    def __init__(self, collection, llm_router, logger=None):\n", "        # Dependencies\n", "        self.collection = collection\n", "        self.llm_router = llm_router\n", "        self.logger = logger or logging.getLogger(__name__)\n", "\n", "        # Message storage\n", "        self.messages = []  # Recent messages\n", "        self.summaries = []  # Historical summaries\n", "        self.user_info = {}  # Store user-specific information\n", "\n", "        # Initialize with system messages\n", "        self.messages.append({\"role\": \"system\", \"content\": self.prompt})\n", "        self.messages.append({\"role\": \"user\", \"content\": \"This is basic information: User is living in San Francisco, California.\"})\n", "\n", "        # Extract user location from initial message\n", "        self.user_info[\"location\"] = \"San Francisco, California\"\n", "\n", "    def _get_relevant_documents(self, query: str) -> str:\n", "        \"\"\"Retrieve relevant documents with error handling\"\"\"\n", "        try:\n", "            docs = self.collection.similarity_search(\n", "                query,\n", "                k=self.RELEVANT_DOCS_COUNT,\n", "                filter=self._get_search_filters()  # Apply filters based on user context\n", "            )\n", "            logger.info(f\"Retrieved {len(docs)} relevant documents.\")\n", "            retrieved_string = \"\\n\\n\".join(doc.page_content for doc in docs)\n", "            return retrieved_string\n", "        except Exception as e:\n", "            self.logger.error(f\"Error retrieving documents: {str(e)}\")\n", "            return \"Error retrieving context information.\"\n", "\n", "    def _get_search_filters(self) -> Dict[str, Any]:\n", "        \"\"\"Generate search filters based on user context\"\"\"\n", "        filters = {}\n", "        if \"location\" in self.user_info:\n", "            # Add location-based filtering if available\n", "            filters[\"location\"] = self.user_info[\"location\"]\n", "        return filters\n", "\n", "    def _extract_topics(self, llm, content: str) -> List[str]:\n", "        \"\"\"Extract key topics from a conversation segment\"\"\"\n", "        try:\n", "            prompt = f\"Extract 3-5 key topics from this conversation as a comma-separated list. Focus on product names, issues, and requests:\\n\\n{content}\"\n", "            response = llm.invoke(prompt)\n", "            topics = [topic.strip() for topic in response.content.split(',')]\n", "            return topics[:5]  # Limit to 5 topics max\n", "        except Exception as e:\n", "            self.logger.error(f\"Error extracting topics: {str(e)}\")\n", "            return []\n", "\n", "    def _get_summary(self, llm, messages_to_summarize: List[Dict[str, str]]) -> str:\n", "        \"\"\"Summarize a set of messages with error handling\"\"\"\n", "        try:\n", "            conv_history = '\\n'.join([f\"{msg['role']}: {msg['content']}\" for msg in messages_to_summarize])\n", "            summary_prompt = \"\"\"Summarize these conversations keeping only the most relevant details for customer support context, including:\n", "            1. Specific product inquiries\n", "            2. Issues or problems mentioned\n", "            3. User requests or intentions\n", "            4. Any promised follow-ups\n", "\n", "            Summary:\n", "            \"\"\" + conv_history\n", "            summary = llm.invoke(summary_prompt)\n", "            return summary.content\n", "        except Exception as e:\n", "            self.logger.error(f\"Error generating summary: {str(e)}\")\n", "            return \"Previous conversation about Mitra Robot products.\"\n", "\n", "    def _archive_old_messages(self, llm):\n", "        \"\"\"Summarize old messages and archive them when we exceed MAX_RECENT_MESSAGES\"\"\"\n", "        if len(self.messages) > self.MAX_RECENT_MESSAGES + 2:  # +2 for system and initial user messages\n", "            # Get messages to summarize (excluding system and initial messages)\n", "            to_summarize = self.messages[2:-self.MAX_RECENT_MESSAGES]\n", "\n", "            # Create summary\n", "            summary_content = self._get_summary(llm, to_summarize)\n", "            topics = self._extract_topics(llm, summary_content)\n", "\n", "            # Store structured summary\n", "            summary = ChatSummary(\n", "                timestamp=time.time(),\n", "                content=summary_content,\n", "                topics=topics\n", "            )\n", "            self.summaries.append(summary)\n", "\n", "            # Limit number of stored summaries\n", "            if len(self.summaries) > self.MAX_SUMMARIES:\n", "                self.summaries = self.summaries[-self.MAX_SUMMARIES:]\n", "\n", "            # Keep only system message, initial user message, and recent messages\n", "            self.messages = self.messages[:2] + self.messages[-self.MAX_RECENT_MESSAGES:]\n", "\n", "    def _extract_user_info(self, message: str) -> None:\n", "        \"\"\"Extract and update user information from messages\"\"\"\n", "        # Simple rule-based extraction for demo purposes\n", "        # In a real implementation, you'd use NER or other NLP techniques\n", "        if \"my email is\" in message.lower():\n", "            import re\n", "            email_match = re.search(r'[\\w.+-]+@[\\w-]+\\.[\\w.-]+', message)\n", "            if email_match:\n", "                self.user_info[\"email\"] = email_match.group(0)\n", "\n", "        # Extract location information\n", "        location_indicators = [\"I'm in\", \"I am in\", \"I live in\", \"located in\"]\n", "        for indicator in location_indicators:\n", "            if indicator in message:\n", "                parts = message.split(indicator, 1)\n", "                if len(parts) > 1:\n", "                    potential_location = parts[1].strip().split(\".\")[0].strip()\n", "                    if len(potential_location) > 3 and len(potential_location) < 50:\n", "                        self.user_info[\"location\"] = potential_location\n", "\n", "    def _get_relevant_summaries(self, query: str) -> List[ChatSummary]:\n", "        \"\"\"Find the most relevant previous summaries based on the current query\"\"\"\n", "        if not self.summaries:\n", "            return []\n", "\n", "        # Simple relevance scoring based on topic overlap\n", "        relevant_summaries = []\n", "        for summary in self.summaries:\n", "            # Check if any topics from this summary match the current query\n", "            relevance_score = sum(1 for topic in summary.topics if topic.lower() in query.lower())\n", "            if relevance_score > 0:\n", "                relevant_summaries.append(summary)\n", "\n", "        # Return the most relevant summaries, newest first\n", "        return sorted(relevant_summaries, key=lambda x: x.timestamp, reverse=True)[:2]\n", "\n", "    def __call__(self, user_message: str) -> str:\n", "        # Extract user information from message\n", "        self._extract_user_info(user_message)\n", "\n", "        # Get appropriate LLM based on message complexity or other factors\n", "        llm = self.llm_router(user_message)\n", "\n", "        # Archive old messages if needed\n", "        self._archive_old_messages(llm)\n", "\n", "        # Get relevant documents\n", "        context = self._get_relevant_documents(user_message)\n", "\n", "        # Get relevant previous summaries\n", "        relevant_summaries = self._get_relevant_summaries(user_message)\n", "\n", "        # Build historical context from relevant summaries\n", "        historical_context = \"\"\n", "        if relevant_summaries:\n", "            historical_context = \"Prior relevant conversations:\\n\" + \"\\n\".join(\n", "                [f\"- {s.content}\" for s in relevant_summaries]\n", "            ) + \"\\n\\n\"\n", "\n", "        # Get recent conversation\n", "        recent_context = \"Recent messages:\\n\" + '\\n'.join(\n", "            [f\"{msg['role']}: {msg['content']}\" for msg in self.messages[2:]]\n", "        )\n", "\n", "        # Include user information\n", "        user_context = \"\"\n", "        if self.user_info:\n", "            user_context = \"User information:\\n\" + \"\\n\".join(\n", "                [f\"- {key}: {value}\" for key, value in self.user_info.items()]\n", "            ) + \"\\n\\n\"\n", "\n", "        # Format complete query\n", "        query = (f\"{self.prompt}\\n\"\n", "                 f\"| {historical_context}\\n\"\n", "                 f\"| {recent_context}\\n\"\n", "                 f\"| The context for RAG is: {context}\\n\"\n", "                 f\"| The user question is: {user_message}\")\n", "\n", "        try:\n", "            # Get AI response\n", "            ai_message = llm.invoke(query)\n", "\n", "            # Store messages\n", "            self.messages.append({\"role\": \"user\", \"content\": user_message})\n", "            self.messages.append({\"role\": \"assistant\", \"content\": ai_message.content})\n", "\n", "            return ai_message.content\n", "        except Exception as e:\n", "            self.logger.error(f\"Error generating response: {str(e)}\")\n", "            return \"I'm sorry, I'm having trouble processing your request right now. Please try again shortly.\"\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 547, "status": "ok", "timestamp": 1752389281068, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "aHEypBif1zJ7", "outputId": "93f313c8-db2e-4d36-edaf-9595de0d1141"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["# Test the retrieval\n", "bot = Chatbot(collection,llm_router=llm_router)\n", "print(bot._get_relevant_documents(\"tell me about modern ai pro\"))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2594, "status": "ok", "timestamp": 1752389283663, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "5NlZGiOD4fCf", "outputId": "172205b7-e537-44f7-ffd7-895eaaa0526a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["other\n", "Hello, I'm <PERSON><PERSON>, your assistant for Mitra Robot customer support. Mitra Robot is a cutting-edge robotic solution designed to provide innovative and efficient services. Our robots are equipped with advanced AI technology to perform various tasks. If you're interested in learning more about our AI capabilities, we also offer AI courses, including Modern AI Pro and Young AI Pro. For more information or sales inquiries, please reach <NAME_EMAIL>. How can I assist you further with Mitra Robot today?\n"]}], "source": ["print(bot(\"Tell me more about Mitra Robot\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"background_save": true, "base_uri": "https://localhost:8080/", "height": 732}, "id": "tZm2K29B1IOA"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.11/dist-packages/gradio/chat_interface.py:339: User<PERSON>arning: The 'tuples' format for chatbot messages is deprecated and will be removed in a future version of Gradio. Please set type='messages' instead, which uses openai-style 'role' and 'content' keys.\n", "  self.chatbot = Chatbot(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["It looks like you are running Gradio on a hosted Jupyter notebook, which requires `share=True`. Automatically setting `share=True` (you can turn this off by setting `share=False` in `launch()` explicitly).\n", "\n", "Colab notebook detected. This cell will run indefinitely so that you can see errors and logs. To turn off, set debug=False in launch().\n", "* Running on public URL: https://92434b11949e3a98ae.gradio.live\n", "\n", "This share link expires in 1 week. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n"]}, {"data": {"text/html": ["<div><iframe src=\"https://92434b11949e3a98ae.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["other\n", "other\n", "billing\n"]}], "source": ["# 7. The chatbot\n", "import gradio as gr\n", "\n", "def chat(message, history):\n", "    return bot(message)\n", "\n", "demo = gr.<PERSON><PERSON>(\n", "    fn=chat,\n", "    title=\"Simple Chatbot\",\n", "    description=\"This is a chatbot built as part of Modern AI Pro Essentials program\",\n", ")\n", "demo.launch(debug=True)"]}], "metadata": {"colab": {"authorship_tag": "ABX9TyOLmG0TWOEM1A5SqgSIRA9L", "name": "", "version": ""}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"0040cdb3edbf4ea79f752a08514307d5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "052cf5279f5c4072bf0eaf02fb239a8f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "05d7b458abcb4784a009248b209345ca": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0701250f11d2441284e9e4e1a2938a0c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "08578af1d1ce4e3fb063f07f91188355": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "087776396b67437eaa74680d0ba8d15e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6c8fc2c4b411403aa4089928ec83be41", "max": 349, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9cea86e505304d11a4222db4ff941c09", "value": 349}}, "0a9628f6bf6845ae85f4be7f49c5d8bf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0b4dad16f1b3410fb63b9497ee175d4f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_88bae502bfe24786b32d2d49f182ced7", "placeholder": "​", "style": "IPY_MODEL_412b73537fff498c8549ad666038dfca", "value": "tokenizer_config.json: 100%"}}, "0e33a5d10fe44125b0e2a0ad5fc763aa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "124862d8e1b6428dbe6fce7bfab7d527": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bf689db62eb0427cb25e10d506adf9ad", "max": 363, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_42d5c8d3276f4be6bdf5fe316983cefc", "value": 363}}, "13517fca861046eeb61c56a83b01e60d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f05b7c2606a14cdfa8ec7bf7ac5aa525", "max": 190, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0a9628f6bf6845ae85f4be7f49c5d8bf", "value": 190}}, "138bb3be280a4685abcd68d038f4d683": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "170aefd09d6141e794dfb16ed4b93785": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cf07c2422f604d479e6be79468922133", "placeholder": "​", "style": "IPY_MODEL_232e88844be041efb0815b3dcbfa8996", "value": "config.json: 100%"}}, "1a20361b0d5443fabecd892b8b82f9e5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1c6ef0ac685348d38e1ce3fa9a648eec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1ca39a3fdb12468d9478cd6f0f7e484e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1e4eb79111f6470aaa154cf3beec584a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "203a1856b8ba4321967bb2dd7e634fcb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2299c4c3af3d4677a03b3a30fda0d20c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "232e88844be041efb0815b3dcbfa8996": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "28482b5cf2794906a2f86d53ff3eee71": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "29433101213a4029882c970575d40647": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ba302fe53d0e47a7b4151ed330d46a6f", "placeholder": "​", "style": "IPY_MODEL_73765d87dd50483286e5f66741889a81", "value": " 116/116 [00:00&lt;00:00, 6.35kB/s]"}}, "2981832b403243ad8fcd71283de3bc3a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2c0f7d4b9e3943e595ef7eb270db4ad2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2dbd826cef654832ade0a01469295068": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "304fb01e3e7f427aa2ced28ec6f6335e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_05d7b458abcb4784a009248b209345ca", "max": 53, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_52d839ecd7b2463d93db29d2c9ff9788", "value": 53}}, "329e88a5fab64f559114623c90a95aa2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "372a976120384aa49ff820a02d258a0e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "37a8dca1408848e592bc60ff098b0396": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "38ef76ef44a745dd8e437fc6b123a504": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fd7f31a5d6a848cabd9948277591dece", "placeholder": "​", "style": "IPY_MODEL_1ca39a3fdb12468d9478cd6f0f7e484e", "value": " 10.4k/? [00:00&lt;00:00, 481kB/s]"}}, "3c489763552d4489b06f4198a39c96bb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3c6c5ea2fd044de18734617d318dacf2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_40805e3dbafb41e493c49a47ffbe8da0", "max": 437971872, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0701250f11d2441284e9e4e1a2938a0c", "value": 437971872}}, "3ccce43769324a9ab8e82e067df9d249": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3df306094546404b99b4949bb3f3b89e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3f26e5ca9c7a4c00b6b4c938280df527": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_daed5ce305844568ade8c3b50b81ff70", "placeholder": "​", "style": "IPY_MODEL_b5630d7feac942859ecd463bd63baefd", "value": " 53.0/53.0 [00:00&lt;00:00, 4.39kB/s]"}}, "3fefb55aa76e49faa477126fdd4193f8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c3cddc51c8f747db97074c56d20f2454", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_203a1856b8ba4321967bb2dd7e634fcb", "value": 1}}, "40805e3dbafb41e493c49a47ffbe8da0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4080ad4eac8a4d638b2e9f23d84d9d72": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e6f9fa80add24489b02aa4afa749f942", "placeholder": "​", "style": "IPY_MODEL_6c10cab49fb94ce3b25f4cbaf21825d1", "value": " 239/239 [00:00&lt;00:00, 16.7kB/s]"}}, "412b73537fff498c8549ad666038dfca": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4145c118b6144178a9490b132b79863a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "428a257e98d04183b4f6094c90475823": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "429b9a1d38954d35904d7e6f229cb997": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cca0a11cb6b048068cdc0bd74801c102", "placeholder": "​", "style": "IPY_MODEL_52d5924c893c434dac147f634575adc2", "value": " 26.0/26.0 [00:00&lt;00:00, 1.86kB/s]"}}, "42c65e5e0d304213803ade6b011a047b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_170aefd09d6141e794dfb16ed4b93785", "IPY_MODEL_bc042dab306140c8af8c8dd72b9c853e", "IPY_MODEL_93eea6139f664ed0aec9590ac693a384"], "layout": "IPY_MODEL_bad50ae5cf78439d9eb0114197487727"}}, "42d5c8d3276f4be6bdf5fe316983cefc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "431d69f738ce414ba95a311655be4d9a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_54c3aadeecb240939eda52cdf039c7f1", "placeholder": "​", "style": "IPY_MODEL_372a976120384aa49ff820a02d258a0e", "value": "config.json: "}}, "4392bbd281474667969c1765d48eadad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "439aab90e8cf4b4ca102b00318c34efc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "471505c1163e4a49a4b21ca8cdab9d63": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8bae94a2e8084449b11bdcc57c6b0e45", "max": 1629437147, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_844094dced6f4057aaab61754d8c9f9e", "value": 1629437147}}, "4cda3a8575b2403dbaf2902a32cfe6e3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4f514d4094c340a89a11522b02b0f124": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cd4feb45fbec4a949b0973a8cbd9dac7", "max": 116, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_57f9abf678ef4238bad67ac1c58d1ca1", "value": 116}}, "520a54c3d02f4ece8efed5a392a84af2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6bf1d50f810a464b86055223f2a415ff", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_3ccce43769324a9ab8e82e067df9d249", "value": 1}}, "526939ebf3714edfb79b753e655df0dd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6cd7624476494c5cad69ef927db0c514", "placeholder": "​", "style": "IPY_MODEL_ec164148d14f47c7af86ad2a4098366b", "value": " 1.63G/1.63G [00:42&lt;00:00, 108MB/s]"}}, "52d5924c893c434dac147f634575adc2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "52d839ecd7b2463d93db29d2c9ff9788": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5302a2934f6744a99136cfc8e74cf51a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "54c3aadeecb240939eda52cdf039c7f1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5620319c17c9412aa999c20d9e6449bd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "57f9abf678ef4238bad67ac1c58d1ca1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "596cbbad36284780a9e0ccf301423329": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "59b585c66ea7489a882d3835776504f8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c6846c9f9af640a697bf7760cce4829a", "placeholder": "​", "style": "IPY_MODEL_e351a4ac2a8b428ca8c0fda1b1e54526", "value": "model.safetensors: 100%"}}, "5b735a644f764b0daf4d771b04b7cac4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "5bc175ad7f424bca9cd4ff147f699488": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "607f19bcf6b343ca80d076c3ad0a6028": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b7cf0ea9d28b42108b0bac1b017d4099", "placeholder": "​", "style": "IPY_MODEL_b09dc0ffe6154a2b95b0403b7cea72f8", "value": "tokenizer.json: "}}, "62f2a6c70d4947ee8fe3897e1b1b5d32": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0b4dad16f1b3410fb63b9497ee175d4f", "IPY_MODEL_124862d8e1b6428dbe6fce7bfab7d527", "IPY_MODEL_7e746ccf203b45a889529ccdc6a1c36a"], "layout": "IPY_MODEL_9a5d0f613b134edfaf22e4309145f307"}}, "62f3817e9f984d27905c042dda162069": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6ceb76dd140e4b61bd80ec465c6a9392", "max": 26, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f2495d6ecd1147059cd605647f76c497", "value": 26}}, "63a046033a884ceabd87d633626e535a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "66e21148db694579a3e8266d23176207": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "69be30c402ad49729cbbbcfc95ec25fb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6ae1886f5d884ec697f54d5d8e674109": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_bb7c0809574f450b8a8e2ff242507039", "IPY_MODEL_3fefb55aa76e49faa477126fdd4193f8", "IPY_MODEL_bcc9528451354590a9bfed021ac0164c"], "layout": "IPY_MODEL_c0f73dfb28d6483e88ebc1a581c2208b"}}, "6bf1d50f810a464b86055223f2a415ff": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "6c10cab49fb94ce3b25f4cbaf21825d1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6c13e53f285f4b5387b3408c1daa2ae1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6c8fc2c4b411403aa4089928ec83be41": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6cd7624476494c5cad69ef927db0c514": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6ceb76dd140e4b61bd80ec465c6a9392": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6d44187246f54d3199c58f51321ac538": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7037bb4f414d4da09438c35b845a1d2c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "728bfb1fb7a34561bcda18550ea1f09a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "73765d87dd50483286e5f66741889a81": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7b14c265ba334e038ba06577e979ccd5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b671f22557bf4ccf8ea0058b1d6ba4b8", "IPY_MODEL_c41efebe8401445bac79d517c09af2f3", "IPY_MODEL_38ef76ef44a745dd8e437fc6b123a504"], "layout": "IPY_MODEL_f8e0e6aba06e4e7eab19625aa0a50f9a"}}, "7b4271b0771f455e9776211fe1ef93e5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7e746ccf203b45a889529ccdc6a1c36a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e64a4d04bf064bceade5e3857217e3c0", "placeholder": "​", "style": "IPY_MODEL_1a20361b0d5443fabecd892b8b82f9e5", "value": " 363/363 [00:00&lt;00:00, 21.2kB/s]"}}, "7f5fb9242b97425f9c19958d1d4ad6de": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8092516f87be4755bca6e90f96664cac": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "844094dced6f4057aaab61754d8c9f9e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8530155d1e35462ca0144bfdccca776a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "85a43bbbd5f044a981b9385e39a5fd0b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d46d9f6706084edb925db8a867a63dc6", "placeholder": "​", "style": "IPY_MODEL_c48c944735594f80a71c659c775f7e60", "value": "config_sentence_transformers.json: 100%"}}, "86c1e90a374f4947987cfba5a0837406": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8702ce916f9d4f5284e0be4cde139e90": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ecc2956e8e7948368d40d21503158a83", "IPY_MODEL_471505c1163e4a49a4b21ca8cdab9d63", "IPY_MODEL_526939ebf3714edfb79b753e655df0dd"], "layout": "IPY_MODEL_fb77f0e3e59c4a2a93950b416324eedd"}}, "88bae502bfe24786b32d2d49f182ced7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8bae94a2e8084449b11bdcc57c6b0e45": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8c8796e0641a43449bee485a277c3c1a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e9e91108a8b7494e87403b9ebec74082", "IPY_MODEL_520a54c3d02f4ece8efed5a392a84af2", "IPY_MODEL_d72abdec80c54153ae9fac0697fd4c0b"], "layout": "IPY_MODEL_2c0f7d4b9e3943e595ef7eb270db4ad2"}}, "8e6240e51fbc415d865236daa0eeea06": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "91557e4607894f6bb4732df66897361b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4cda3a8575b2403dbaf2902a32cfe6e3", "placeholder": "​", "style": "IPY_MODEL_6d44187246f54d3199c58f51321ac538", "value": "tokenizer_config.json: 100%"}}, "9368c61bf59f4ed5aa280c375985ffd1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "93eea6139f664ed0aec9590ac693a384": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7037bb4f414d4da09438c35b845a1d2c", "placeholder": "​", "style": "IPY_MODEL_9b88cc69acd1401983509f5973861f4c", "value": " 571/571 [00:00&lt;00:00, 38.4kB/s]"}}, "9843b42fd31846aa9284938dd50d74ab": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9858706cc41645188ad78ba58f809a3c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_59b585c66ea7489a882d3835776504f8", "IPY_MODEL_3c6c5ea2fd044de18734617d318dacf2", "IPY_MODEL_fc09c85b246f477583ec565bcb649bf0"], "layout": "IPY_MODEL_37a8dca1408848e592bc60ff098b0396"}}, "98b2cadb2ccd483b902f025b8db78d05": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "98cc547e0d2e467a92aa22ec7088ada2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_431d69f738ce414ba95a311655be4d9a", "IPY_MODEL_e3b87ea4e65d4217889d69e9268f6c67", "IPY_MODEL_cbb69d2bf09c4a989df19fc4ba2e7c59"], "layout": "IPY_MODEL_5302a2934f6744a99136cfc8e74cf51a"}}, "9a5d0f613b134edfaf22e4309145f307": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9b88cc69acd1401983509f5973861f4c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9bb0b772373040fd97f29e6db755e330": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9cea86e505304d11a4222db4ff941c09": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9d7379ed468b4e06b76118b798da95fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a00857bda6574175a6e863e96bccc0a5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d9dc859d1b1643b7a4417d6845a34347", "IPY_MODEL_a8dee2801f4a41cca3741878af2b1ec7", "IPY_MODEL_ba4fde11ed7842b194d6ea8f97495f2c"], "layout": "IPY_MODEL_2299c4c3af3d4677a03b3a30fda0d20c"}}, "a24219c797a24e5e88577858978f67b9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c2077cbf88224940b9a15acc00f5daa3", "IPY_MODEL_304fb01e3e7f427aa2ced28ec6f6335e", "IPY_MODEL_3f26e5ca9c7a4c00b6b4c938280df527"], "layout": "IPY_MODEL_28482b5cf2794906a2f86d53ff3eee71"}}, "a3a04be215844096b61354a997bc939e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a4de79851078420ea8ce0d88ac4cec1b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_66e21148db694579a3e8266d23176207", "max": 239, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_08578af1d1ce4e3fb063f07f91188355", "value": 239}}, "a56da2fbcec343fd9397a28bf99919bb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c7abccff216747289f6a428d883987bf", "IPY_MODEL_087776396b67437eaa74680d0ba8d15e", "IPY_MODEL_bf0683943184413eb923ebd2a2a4003a"], "layout": "IPY_MODEL_63a046033a884ceabd87d633626e535a"}}, "a7b6d44e4721407fb7776559a41da9db": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a8dee2801f4a41cca3741878af2b1ec7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fce9ebde22474ebda9105606747c94e8", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d44d9ea65d70415e98003d4a07dfe91f", "value": 1}}, "a958b44ff0ed4665bce77fa8b6da632f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e8da633362914967a784ca66d20659c8", "IPY_MODEL_d6acb23ccd05415cb6eb392825c42435", "IPY_MODEL_fc4b2e2f0d724677822ce0e1c872fbac"], "layout": "IPY_MODEL_329e88a5fab64f559114623c90a95aa2"}}, "b097208d0d72417db157493ed07f2758": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b09dc0ffe6154a2b95b0403b7cea72f8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b1bbfc5374ad4d8d97631a4736fb0171": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b5630d7feac942859ecd463bd63baefd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b5dd1e6f56834ddc8d02f32930693e1d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b671f22557bf4ccf8ea0058b1d6ba4b8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2dbd826cef654832ade0a01469295068", "placeholder": "​", "style": "IPY_MODEL_b097208d0d72417db157493ed07f2758", "value": "README.md: "}}, "b7cf0ea9d28b42108b0bac1b017d4099": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b835cbbc57df4eac88ab18022f9d28ec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d847eb632e0340dd90282e7f7ca846dc", "IPY_MODEL_a4de79851078420ea8ce0d88ac4cec1b", "IPY_MODEL_4080ad4eac8a4d638b2e9f23d84d9d72"], "layout": "IPY_MODEL_6c13e53f285f4b5387b3408c1daa2ae1"}}, "ba302fe53d0e47a7b4151ed330d46a6f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ba47beb85921458db86e9b0ccaff45b1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ba4fde11ed7842b194d6ea8f97495f2c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_052cf5279f5c4072bf0eaf02fb239a8f", "placeholder": "​", "style": "IPY_MODEL_439aab90e8cf4b4ca102b00318c34efc", "value": " 466k/? [00:00&lt;00:00, 6.07MB/s]"}}, "bad50ae5cf78439d9eb0114197487727": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bb7c0809574f450b8a8e2ff242507039": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dc8c9fae01b0493eab07e087e76570ed", "placeholder": "​", "style": "IPY_MODEL_9843b42fd31846aa9284938dd50d74ab", "value": "vocab.json: "}}, "bc042dab306140c8af8c8dd72b9c853e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bd767b02a30340ae99db9d523a2ccad1", "max": 571, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4392bbd281474667969c1765d48eadad", "value": 571}}, "bcc9528451354590a9bfed021ac0164c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f9a4af28a62a4db480a32498fbc844dc", "placeholder": "​", "style": "IPY_MODEL_e5a57ef0fee94c299cdef8f2099d41f3", "value": " 899k/? [00:00&lt;00:00, 28.4MB/s]"}}, "bd767b02a30340ae99db9d523a2ccad1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf0683943184413eb923ebd2a2a4003a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3df306094546404b99b4949bb3f3b89e", "placeholder": "​", "style": "IPY_MODEL_c5ba1bd0665143fb9c35c8c18f62df44", "value": " 349/349 [00:00&lt;00:00, 30.2kB/s]"}}, "bf689db62eb0427cb25e10d506adf9ad": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c07dc555271247d980d6fde2f829a650": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c08ec3d69d15404595e12d339e136d65": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c0f73dfb28d6483e88ebc1a581c2208b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c2077cbf88224940b9a15acc00f5daa3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_69be30c402ad49729cbbbcfc95ec25fb", "placeholder": "​", "style": "IPY_MODEL_a7b6d44e4721407fb7776559a41da9db", "value": "sentence_bert_config.json: 100%"}}, "c3bc438862ae4458b27e5e4b5c07fe7e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c3cddc51c8f747db97074c56d20f2454": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "c41efebe8401445bac79d517c09af2f3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fb898583dcaf41a09983ead66d337695", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e21618d132f8472588071e687b03e1ae", "value": 1}}, "c48c944735594f80a71c659c775f7e60": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c5ba1bd0665143fb9c35c8c18f62df44": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c6846c9f9af640a697bf7760cce4829a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c7abccff216747289f6a428d883987bf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8e6240e51fbc415d865236daa0eeea06", "placeholder": "​", "style": "IPY_MODEL_e216e23c8f6c4c34829f5a99aac2fcf2", "value": "modules.json: 100%"}}, "cbb69d2bf09c4a989df19fc4ba2e7c59": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e9aad147f8934f23a2b80141a3260edb", "placeholder": "​", "style": "IPY_MODEL_8530155d1e35462ca0144bfdccca776a", "value": " 1.15k/? [00:00&lt;00:00, 15.8kB/s]"}}, "cca0a11cb6b048068cdc0bd74801c102": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cd4feb45fbec4a949b0973a8cbd9dac7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cf07c2422f604d479e6be79468922133": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d26bad9d08764c37a047afe203802e61": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_607f19bcf6b343ca80d076c3ad0a6028", "IPY_MODEL_eab5541e92a44f11a40570b0035b808b", "IPY_MODEL_d5c9015c14f3430381b673ff48aada9d"], "layout": "IPY_MODEL_9368c61bf59f4ed5aa280c375985ffd1"}}, "d44d9ea65d70415e98003d4a07dfe91f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d46d9f6706084edb925db8a867a63dc6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d5c9015c14f3430381b673ff48aada9d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fb344066417c42d0bea4c2fa1a033bb5", "placeholder": "​", "style": "IPY_MODEL_2981832b403243ad8fcd71283de3bc3a", "value": " 1.36M/? [00:00&lt;00:00, 33.3MB/s]"}}, "d6acb23ccd05415cb6eb392825c42435": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8092516f87be4755bca6e90f96664cac", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_428a257e98d04183b4f6094c90475823", "value": 1}}, "d72abdec80c54153ae9fac0697fd4c0b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7b4271b0771f455e9776211fe1ef93e5", "placeholder": "​", "style": "IPY_MODEL_3c489763552d4489b06f4198a39c96bb", "value": " 456k/? [00:00&lt;00:00, 16.5MB/s]"}}, "d72ea6a59cef40128de7a5b40be1f3b1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d7d24b8dc8f8441f88d867b80d8c5d13": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_91557e4607894f6bb4732df66897361b", "IPY_MODEL_62f3817e9f984d27905c042dda162069", "IPY_MODEL_429b9a1d38954d35904d7e6f229cb997"], "layout": "IPY_MODEL_1e4eb79111f6470aaa154cf3beec584a"}}, "d847eb632e0340dd90282e7f7ca846dc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d72ea6a59cef40128de7a5b40be1f3b1", "placeholder": "​", "style": "IPY_MODEL_0040cdb3edbf4ea79f752a08514307d5", "value": "special_tokens_map.json: 100%"}}, "d9dc859d1b1643b7a4417d6845a34347": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_98b2cadb2ccd483b902f025b8db78d05", "placeholder": "​", "style": "IPY_MODEL_4145c118b6144178a9490b132b79863a", "value": "tokenizer.json: "}}, "daed5ce305844568ade8c3b50b81ff70": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dc81cd9b75ef429990101ba63eebaa4e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_85a43bbbd5f044a981b9385e39a5fd0b", "IPY_MODEL_4f514d4094c340a89a11522b02b0f124", "IPY_MODEL_29433101213a4029882c970575d40647"], "layout": "IPY_MODEL_596cbbad36284780a9e0ccf301423329"}}, "dc8c9fae01b0493eab07e087e76570ed": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "df35a576fa0e49bab84fc769d773a7de": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7f5fb9242b97425f9c19958d1d4ad6de", "placeholder": "​", "style": "IPY_MODEL_1c6ef0ac685348d38e1ce3fa9a648eec", "value": " 190/190 [00:00&lt;00:00, 16.2kB/s]"}}, "e21618d132f8472588071e687b03e1ae": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e216e23c8f6c4c34829f5a99aac2fcf2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e351a4ac2a8b428ca8c0fda1b1e54526": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e3b87ea4e65d4217889d69e9268f6c67": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_728bfb1fb7a34561bcda18550ea1f09a", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ba47beb85921458db86e9b0ccaff45b1", "value": 1}}, "e589a5c1452f4cce8d7be3be6021d60f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e5a57ef0fee94c299cdef8f2099d41f3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e64a4d04bf064bceade5e3857217e3c0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e6f9fa80add24489b02aa4afa749f942": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e8da633362914967a784ca66d20659c8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b5dd1e6f56834ddc8d02f32930693e1d", "placeholder": "​", "style": "IPY_MODEL_e589a5c1452f4cce8d7be3be6021d60f", "value": "vocab.txt: "}}, "e9aad147f8934f23a2b80141a3260edb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e9e91108a8b7494e87403b9ebec74082": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_138bb3be280a4685abcd68d038f4d683", "placeholder": "​", "style": "IPY_MODEL_0e33a5d10fe44125b0e2a0ad5fc763aa", "value": "merges.txt: "}}, "eab5541e92a44f11a40570b0035b808b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5b735a644f764b0daf4d771b04b7cac4", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b1bbfc5374ad4d8d97631a4736fb0171", "value": 1}}, "ec164148d14f47c7af86ad2a4098366b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ecc2956e8e7948368d40d21503158a83": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5620319c17c9412aa999c20d9e6449bd", "placeholder": "​", "style": "IPY_MODEL_c08ec3d69d15404595e12d339e136d65", "value": "model.safetensors: 100%"}}, "f05b7c2606a14cdfa8ec7bf7ac5aa525": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f2495d6ecd1147059cd605647f76c497": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f267af1376904d58bedcd395f4f58e63": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f671ede1eb764dcfada005e5f78a56aa", "IPY_MODEL_13517fca861046eeb61c56a83b01e60d", "IPY_MODEL_df35a576fa0e49bab84fc769d773a7de"], "layout": "IPY_MODEL_c07dc555271247d980d6fde2f829a650"}}, "f671ede1eb764dcfada005e5f78a56aa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9bb0b772373040fd97f29e6db755e330", "placeholder": "​", "style": "IPY_MODEL_9d7379ed468b4e06b76118b798da95fa", "value": "config.json: 100%"}}, "f8e0e6aba06e4e7eab19625aa0a50f9a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f9a4af28a62a4db480a32498fbc844dc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fb344066417c42d0bea4c2fa1a033bb5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fb77f0e3e59c4a2a93950b416324eedd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fb898583dcaf41a09983ead66d337695": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "fc09c85b246f477583ec565bcb649bf0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c3bc438862ae4458b27e5e4b5c07fe7e", "placeholder": "​", "style": "IPY_MODEL_5bc175ad7f424bca9cd4ff147f699488", "value": " 438M/438M [00:08&lt;00:00, 78.9MB/s]"}}, "fc4b2e2f0d724677822ce0e1c872fbac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a3a04be215844096b61354a997bc939e", "placeholder": "​", "style": "IPY_MODEL_86c1e90a374f4947987cfba5a0837406", "value": " 232k/? [00:00&lt;00:00, 5.68MB/s]"}}, "fce9ebde22474ebda9105606747c94e8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "fd7f31a5d6a848cabd9948277591dece": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}}}, "nbformat": 4, "nbformat_minor": 0}