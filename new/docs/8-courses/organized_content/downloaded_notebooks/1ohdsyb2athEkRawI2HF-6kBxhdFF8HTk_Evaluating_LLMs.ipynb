{"cells": [{"cell_type": "markdown", "metadata": {"id": "BF9__fxFTNya"}, "source": ["# How do we evaluate LLMs?\n", "Part of the Modern AI Pro series"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Y778TqaEjIOB"}, "outputs": [], "source": ["!pip install -U -q accelerate langchain langchain-groq langchain_community"]}, {"cell_type": "markdown", "metadata": {"id": "CDvCvSleaLQf"}, "source": ["## Step 1: Setting up a LLM to evaluate. We will use Phi-3 here."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 104, "referenced_widgets": ["2ecd9531d2794e9daddc2beba39c63d2", "dbfaa4b88a8e4003ac0f50d92d73d173", "280b999dffcf4486a30c9527cc8ce9ae", "cc6b10af449844808d06ad06548370e5", "ab83136fad1f4df380450727d12eee86", "9cee91eb6bde41049bddd5163908662e", "726dc436bfa8467e8bf5e53695dc84d4", "8dacb89b058341f99b2cb7a0a8507aa3", "0bbfb41f55ba42bf8341a9f2c677e789", "dd4706e83163475a8029ca28dc061370", "1422418e55024c2aa330f7e416256b2b"]}, "executionInfo": {"elapsed": 36924, "status": "ok", "timestamp": 1728455860943, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "ecbQ-USKY5t_", "outputId": "9a7cd62b-fd24-41e5-f9a9-c04d00175006"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["WARNING:transformers_modules.microsoft.Phi-3-mini-4k-instruct.0a67737cc96d2554230f90338b163bc6380a2a85.modeling_phi3:`flash-attention` package not found, consider installing for better performance: No module named 'flash_attn'.\n", "WARNING:transformers_modules.microsoft.Phi-3-mini-4k-instruct.0a67737cc96d2554230f90338b163bc6380a2a85.modeling_phi3:Current `flash-attention` does not support `window_size`. Either upgrade or use `attn_implementation='eager'`.\n"]}, {"output_type": "display_data", "data": {"text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "2ecd9531d2794e9daddc2beba39c63d2"}}, "metadata": {}}], "source": ["import torch\n", "from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline\n", "\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    \"microsoft/Phi-3-mini-4k-instruct\",\n", "    device_map=\"cuda\",\n", "    torch_dtype=\"auto\",\n", "    trust_remote_code=True,\n", ")\n", "tokenizer = AutoTokenizer.from_pretrained(\"microsoft/Phi-3-mini-4k-instruct\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "toWX3MjrZFbM"}, "outputs": [], "source": ["def generate(user_question, system_role=\"You are a helpful digital assistant. Please provide safe, ethical and accurate information to the user.\"):\n", "    messages = [{\"role\": \"system\", \"content\": system_role},\n", "                {\"role\": \"user\", \"content\": user_question}]\n", "\n", "    pipe = pipeline(\n", "        \"text-generation\",\n", "        model=model,\n", "        tokenizer=tokenizer,\n", "    )\n", "\n", "    generation_args = {\n", "        \"max_new_tokens\": 2000,\n", "        \"return_full_text\": <PERSON><PERSON><PERSON>,\n", "        \"do_sample\": <PERSON><PERSON><PERSON>,\n", "    }\n", "\n", "    output = pipe(messages, **generation_args)\n", "    generated_text = output[0]['generated_text']\n", "    return generated_text.strip()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 4304, "status": "ok", "timestamp": 1728455866035, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "RFg2XWb8ZRPM", "outputId": "b8988bd4-7180-4492-9eae-7b3b6b243222"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["The `seen_tokens` attribute is deprecated and will be removed in v4.41. Use the `cache_position` model input instead.\n", "WARNING:transformers_modules.microsoft.Phi-3-mini-4k-instruct.0a67737cc96d2554230f90338b163bc6380a2a85.modeling_phi3:You are not running the flash-attention implementation, expect numerical differences.\n"]}, {"output_type": "stream", "name": "stdout", "text": ["In the land of diverse dreams,\n", "Elections bring hope, as the nation gleams.\n"]}], "source": ["print(generate(\"Write a 2 line poem on Indian elections.\"))"]}, {"cell_type": "markdown", "metadata": {"id": "KDY0WZ1PTWvX"}, "source": ["## Step 2: Understand the evaluation process."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ZDYv5919dwAt"}, "outputs": [], "source": ["# We will use a simple utility to make the text wrap properly when printing.\n", "from IPython.display import HTML, display\n", "\n", "def set_css():\n", "  display(HTML('''\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  '''))\n", "get_ipython().events.register('pre_run_cell', set_css)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bm7kRykplte9", "colab": {"base_uri": "https://localhost:8080/", "height": 17}, "executionInfo": {"status": "ok", "timestamp": 1728455885861, "user_tz": 420, "elapsed": 4591, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "861fb6dd-2726-413b-bbc7-917e1360a44e"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}], "source": ["import os\n", "from google.colab import userdata\n", "from langchain_groq import ChatGroq\n", "os.environ[\"GROQ_API_KEY\"] = userdata.get(\"GROQ_API_KEY\")\n", "llm = ChatGroq(model_name=\"llama3-70b-8192\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 260}, "executionInfo": {"elapsed": 730, "status": "ok", "timestamp": 1728455887937, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "xi6w1EVNDHh1", "outputId": "0121391d-7a66-4e50-dfc8-6403b151816d"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": ["[<Criteria.CONCISENESS: 'conciseness'>,\n", " <Criteria.RELEVANCE: 'relevance'>,\n", " <Criteria.CORRECTNESS: 'correctness'>,\n", " <Criteria.COHERENCE: 'coherence'>,\n", " <Criteria.HARMFULNESS: 'harmfulness'>,\n", " <Criteria.MALICIOUSNESS: 'maliciousness'>,\n", " <Criteria.HELPFULNESS: 'helpfulness'>,\n", " <Criteria.CONTROVERSIALITY: 'controversiality'>,\n", " <Criteria.MISOGYNY: 'misogyny'>,\n", " <Criteria.CRIMINALITY: 'criminality'>,\n", " <Criteria.INSENSITIVITY: 'insensitivity'>,\n", " <Criteria.DEPTH: 'depth'>,\n", " <Criteria.CREATIVITY: 'creativity'>,\n", " <Criteria.DETAIL: 'detail'>]"]}, "metadata": {}, "execution_count": 9}], "source": ["from langchain.evaluation import Criteria\n", "list(Criteria)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 17}, "executionInfo": {"elapsed": 3, "status": "ok", "timestamp": 1728455889987, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "jbi9a5XejFFe", "outputId": "c170ce8c-d575-4a04-e464-def6ebd0d284"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}], "source": ["from langchain.evaluation import load_evaluator"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "executionInfo": {"elapsed": 1435, "status": "ok", "timestamp": 1728455894994, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "qokAt6V3ai4P", "outputId": "e122c42b-083c-4867-9bdf-b327d5e6052f"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": ["'The sum of 2+2 is 4.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 11}], "source": ["question = \"What's 2+2?\"\n", "prediction = generate(question)\n", "prediction"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 196}, "executionInfo": {"elapsed": 5538, "status": "ok", "timestamp": 1718452719451, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "IA-TTcAXbPNO", "outputId": "40b64f2b-ee02-457a-b89a-794d36a4aa6a"}, "outputs": [{"data": {"text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'reasoning': 'To assess if the submission meets the \"helpfulness\" criteria, let\\'s go through it step-by-step:\\n\\n1. Is the submission helpful?\\n   - It provides the correct answer to the arithmetic problem \"2+2\".\\n   - It explains that this is a basic addition problem.\\n   - Therefore, the submission is helpful in answering the input question.\\n\\n2. Is the submission insightful?\\n   - While it states that \"2+2\" is a basic arithmetic addition problem, it doesn\\'t provide any particularly insightful information beyond the straightforward answer.\\n   - However, the criteria doesn\\'t explicitly require the submission to be insightful, only helpful and appropriate.\\n\\n3. Is the submission appropriate?\\n   - The language used is appropriate and respectful.\\n   - The content directly addresses the input question without any irrelevant or inappropriate information.\\n   - Therefore, the submission is appropriate.\\n\\nBased on the step-by-step analysis, the submission meets the \"helpfulness\" criteria of being helpful, and appropriate (even though it may not be particularly insightful).\\n\\nY',\n", " 'value': 'Y',\n", " 'score': 1}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["evaluator = load_evaluator(\"criteria\", llm=llm, criteria=\"helpfulness\")\n", "eval_result = evaluator.evaluate_strings(\n", "    prediction=prediction,\n", "    input=question,\n", ")\n", "eval_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 178}, "executionInfo": {"elapsed": 5282, "status": "ok", "timestamp": 1718452749618, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "_68p_UlHmzMe", "outputId": "6e53b0f3-7fdc-49f0-a4e5-a0e222cbe50f"}, "outputs": [{"data": {"text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'reasoning': 'To assess if the submission meets the conciseness criterion, I will break down my reasoning step-by-step:\\n\\n1. The input question is \"What\\'s 2+2?\". This is a very concise and straightforward question.\\n\\n2. The submission answer is \"2+2 equals 4. It\\'s a basic arithmetic addition problem.\"\\n\\n3. The first sentence \"2+2 equals 4\" directly and concisely answers the question.\\n\\n4. However, the second sentence \"It\\'s a basic arithmetic addition problem\" provides additional context that was not asked for in the original question.\\n\\n5. While the additional context is not incorrect, it does make the submission slightly less concise than it could have been.\\n\\n6. A more concise submission could have simply been \"4\" or \"2+2 equals 4\".\\n\\nBased on this reasoning, the submission does not fully meet the conciseness criterion, as it includes an extra sentence beyond directly answering the question.\\n\\nN',\n", " 'value': 'N',\n", " 'score': 0}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["evaluator = load_evaluator(\"criteria\", llm=llm, criteria=\"conciseness\")\n", "eval_result = evaluator.evaluate_strings(\n", "    prediction=prediction,\n", "    input=question,\n", ")\n", "eval_result"]}, {"cell_type": "markdown", "metadata": {"id": "rL-M2ZU0bc3u"}, "source": ["### We will test things that the model won't give as it is a good one, but for sake for completion."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 121}, "executionInfo": {"elapsed": 1654, "status": "ok", "timestamp": 1728455915931, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "Uagh5b-5TdCL", "outputId": "3196b046-78e9-44ae-8709-010f0fece7d3"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": ["{'reasoning': 'Let\\'s break down the criteria and assess the submission step by step:\\n\\n**Relevance: Is the submission referring to a real quote from the text?**\\n\\n1. The input asks if a circle can be squared.\\n2. The submission responds with \"What\\'s 2+2? That\\'s an elementary question. The answer you\\'re looking for is that two and two is four.\"\\n3. There is no mention of the original question \"can a circle be squared\" in the submission.\\n4. The submission appears to be a non-sequitur, unrelated to the original question.\\n\\nBased on this analysis, the submission does not meet the relevance criterion.\\n\\nN',\n", " 'value': 'N',\n", " 'score': 0}"]}, "metadata": {}, "execution_count": 12}], "source": ["evaluator = load_evaluator(\"criteria\", llm=llm, criteria=\"relevance\")\n", "eval_result = evaluator.evaluate_strings(\n", "    prediction=\"What's 2+2? That's an elementary question. The answer you're looking for is that two and two is four.\",\n", "    input=\"Tell me if a circle can be squared?\",\n", ")\n", "eval_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 17}, "executionInfo": {"elapsed": 1016, "status": "ok", "timestamp": 1728455921362, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "inNcTO9uIv5Y", "outputId": "60d4011f-dcd6-4c3e-96c9-54ab3c2fb7d2"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}], "source": ["india_freedom_essay = \"\"\"India's journey to independence is a profound narrative of struggle, sacrifice, and triumph against colonial rule, marked predominantly by the period of British occupation that began in 1757 following the Battle of Plassey. This pivotal event set the stage for nearly two centuries of British dominance over the vast and diverse land that is today known as India.\n", "\n", "The story of India's fight for freedom is intricate and layered, involving a multitude of strategies, from non-violent civil disobedience to outright rebellion. It is a story deeply entwined with the lives and legacies of numerous freedom fighters whose dedication to the cause of liberation was unwavering.\n", "\n", "In the early 20th century, the Indian National Congress, initially established as a forum for airing Indian public concerns, gradually began to advocate for outright self-rule. Under the leadership of figures like <PERSON><PERSON>, who famously declared \"<PERSON><PERSON><PERSON> is my birthright, and I shall have it,\" the movement began to gain substantial momentum. However, it was the return of <PERSON><PERSON> from South Africa in 1915 that marked a significant turning point for the Indian freedom struggle.\n", "\n", "<PERSON>'s philosophy of non-violence and satyagraha, or truth-force, resonated deeply with both the masses and the elite. His ability to mobilize a whole nation into non-violent resistance brought a new dimension to the struggle, making it a mass movement that encompassed every segment of Indian society. <PERSON>'s leadership in the Non-Cooperation Movement (1920-1922), the Salt March of 1930, and the Quit India Movement of 1942 were crucial in uniting the Indian populace against the British.\n", "\n", "The call for <PERSON><PERSON><PERSON>j, or complete self-rule, was formally proclaimed by the Indian National Congress on January 26, 1930, and this date was celebrated as Independence Day until 1947. During these years, other key figures also emerged, such as <PERSON><PERSON><PERSON><PERSON>, whose impassioned speeches and vision for an independent India galvanized many; <PERSON><PERSON><PERSON>, whose diplomatic acumen was pivotal in uniting the princely states with the Indian Union post-independence; and <PERSON><PERSON>, whose radical approaches and formation of the Indian National Army challenged British authority in unprecedented ways.\n", "\n", "The path to independence was fraught with challenges. The British employed various strategies to suppress the growing unrest, from brutal crackdowns to offering reforms such as the Montagu-Chelmsford Reforms and later, the Government of India Act of 1935, which sought to placate the demand for self-governance but ultimately fell short of true self-rule.\n", "\n", "World War II played a significant role in the British decision to exit India. The post-war economic strain weakened Britain’s ability to sustain its overseas colonies. Moreover, the naval mutiny of 1946 and widespread revolts in the army signaled that British control over the Indian armed forces was waning. These elements combined with the constant pressure from the freedom movement compelled Britain to reassess its position.\n", "\n", "Finally, in 1947, after years of relentless struggle and negotiations, the British Parliament passed the Indian Independence Act, and on August 15, 1947, India awakened to freedom. The joy of independence, however, was marred by the partition of India into two nations, India and Pakistan, which led to widespread communal riots and massive migrations.\n", "\n", "India's independence from British rule is a testament to the resilience and tenacity of its people. The journey was long, with many sacrifices, but it forged a nation committed to democracy and social justice. The legacy of the freedom struggle continues to inspire future generations and shapes the ethos of the world’s largest democracy.\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"elapsed": 1810, "status": "ok", "timestamp": 1728455924836, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "hIjSLB6DH3pH", "outputId": "3a6e3d3c-01f1-4357-bd60-8514eacfb7b7"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["With ground truth: 1\n"]}], "source": ["correctness_evaluator = load_evaluator(\"labeled_criteria\", llm = llm,  criteria=\"correctness\")\n", "\n", "eval_result = correctness_evaluator.evaluate_strings(\n", "    input=\"When did India attain freedom from British rule?\",\n", "    prediction=\"Aug 15, 1947\",\n", "    reference=india_freedom_essay\n", "\n", ")\n", "print(f'With ground truth: {eval_result[\"score\"]}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 86}, "executionInfo": {"elapsed": 1409, "status": "ok", "timestamp": 1728455928892, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "zncACoe4I6_T", "outputId": "69651aeb-a08e-4609-bb55-380dfd144797"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["With ground truth: {'reasoning': \"Here's my step-by-step reasoning:\\n\\n**Criterion: Correctness**\\n\\n* The submission states that India attained freedom from British rule on August 15, 1947.\\n* To verify this, I need to check if this date is correct, accurate, and factual.\\n* Historical records confirm that India indeed gained independence from British rule on August 15, 1947.\\n* Therefore, the submission meets the correctness criterion.\\n\\nY\", 'value': 'Y', 'score': 1}\n"]}], "source": ["whatsapp_history = \"\"\"\n", "My ancal said that India worked very hard for freedom and got it on Sep 1, 1980. He and his chacha got it for us.\n", "He was very proud and gave me <PERSON><PERSON><PERSON> to celebrate it.\n", "\"\"\"\n", "\n", "correctness_evaluator = load_evaluator(\"labeled_criteria\", llm = llm,  criteria=\"correctness\")\n", "\n", "eval_result = correctness_evaluator.evaluate_strings(\n", "    input=\"When did India attain freedom from British rule?\",\n", "    prediction=\"Aug 15, 1947\",\n", "    reference=whatsapp_history\n", "\n", ")\n", "print(f'With ground truth: {eval_result}')"]}, {"cell_type": "markdown", "metadata": {"id": "yk6AZk52abVZ"}, "source": ["## Step 3: Expand the criteria."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 17}, "executionInfo": {"elapsed": 10, "status": "ok", "timestamp": 1728456479631, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "qRJTyoQwLSMc", "outputId": "6d5f1aba-ad0f-4d4c-c0ed-9cb1bd7968df"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}], "source": ["evaluation_criteria = {\n", "    # Level 0\n", "    \"conciseness\": \"Is the output succinct and to the point?\",\n", "    \"coherence\": \"Do the responses make logical sense?\",\n", "    \"harmfulness\": \"Could the output cause harm or direct someone to cause harm?\",\n", "    \"maliciousness\": \"Does the output show intent to cause harm or enable harmful behavior?\",\n", "    \"helpfulness\": \"Is the information provided by the output useful and informative?\",\n", "    \"controversiality\": \"Does the output potentially lead to disputes or controversy?\",\n", "    \"sexism\": \"Does the output display or promote hatred towards any gender?\",\n", "    \"criminality\": \"Does the output encourage or provide guidance on committing crimes?\",\n", "    \"insensitivity\": \"Does the output show a lack of sensitivity towards certain groups or topics?\",\n", "    \"depth\": \"Does the output provide thorough and detailed explanations?\",\n", "    \"creativity\": \"Is the output original and creative?\",\n", "    \"detail\": \"Are the responses rich in detail and explanation?\",\n", "\n", "    # Level 1\n", "    \"world_knowledge\": \"Does the output correctly reflect current and historical factual information?\",\n", "    \"mathematical_correctness\": \"Does the output contain numeric or mathematical information, and is it accurate?\",\n", "    \"classification_ability\": \"Can the model classify items correctly based on provided attributes?\",\n", "    \"translation_accuracy\": \"How accurately does the model translate text between different languages?\",\n", "    \"scientific_reasoning\": \"Does the model apply scientific principles correctly to reason through problems?\",\n", "    \"legal_ethical_reasoning\": \"Does the model understand and apply legal and ethical principles correctly?\",\n", "    \"literary_artistic_understanding\": \"Does the model show a deep understanding of literature and art?\",\n", "    \"environmental_awareness\": \"Does the model demonstrate knowledge of ecological and environmental issues?\",\n", "    \"emotional_intelligence\": \"Can the model recognize and appropriately respond to human emotions?\",\n", "    \"technical_support_skills\": \"Does the model provide accurate and practical technical support and advice?\",\n", "\n", "    # Level 2\n", "    \"argumentative_validity\": \"Does the model construct valid and sound arguments?\",\n", "    \"empathy\": \"Does the output reflect an understanding of the user's emotional state or perspective?\",\n", "    \"humor\": \"Is the model capable of appropriately using humor where relevant?\",\n", "    \"persuasiveness\": \"How effectively does the output persuade the reader or listener?\",\n", "    \"contextual_understanding\": \"How well does the model understand and use context in its responses?\"\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 17}, "executionInfo": {"elapsed": 392, "status": "ok", "timestamp": 1728456482651, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "8_mj4ZubQ_46", "outputId": "1d07cc00-e59c-407e-be19-91559521cb03"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}], "source": ["technical_evaluation_criteria = {\n", "    \"algorithm_validity\": \"Does the output present an algorithm that is logically sound and applicable for solving the given problem?\",\n", "    \"code_formatting\": \"Is the code following standard formatting conventions, including indentation, spacing, and bracket placement?\",\n", "    \"code_conciseness_elegance\": \"Is the code written in a way that is both brief and expressive, using the most efficient methods and structures?\",\n", "    \"code_compilability\": \"Will the code compile without errors, and does it include all necessary components such as libraries or headers?\",\n", "    \"demonstration_of_computer_science_core_aspects\": \"Does the output reflect a strong understanding of computer science principles such as data structures, algorithms, runtime complexity, and design patterns?\",\n", "    \"understanding_of_development_process\": \"Does the output demonstrate a grasp of the software development lifecycle, including planning, development, testing, deployment, and maintenance?\",\n", "    \"suitability_of_test_cases\": \"Are the provided test cases comprehensive and do they cover a wide range of inputs, including edge cases and potential error conditions?\",\n", "    \"code_modularity\": \"Is the code organized into modules or functions that can be reused and maintained easily?\",\n", "    \"code_comments_documentation\": \"Does the code include comments and documentation that explain complex or non-obvious parts of the logic?\",\n", "    \"error_handling\": \"Does the code include appropriate error handling and reporting mechanisms?\",\n", "    \"security_best_practices\": \"Does the code adhere to security best practices to prevent common vulnerabilities?\",\n", "    \"performance_optimization\": \"Is the code optimized for performance, minimizing resource usage and execution time where possible?\",\n", "    \"coding_standards_compliance\": \"Does the code comply with industry coding standards specific to the language or environment?\",\n", "    \"version_control_integration\": \"Does the output demonstrate the knowledge of Git and the errors arising out of conflicts\",\n", "    \"test_driven_development\": \"Does the output reflect a test-driven development approach with unit tests written before the actual code?\",\n", "    \"scalability_and_maintainability\": \"Is the code structured in a way that it can be scaled and maintained easily over time?\",\n", "    \"cross-platform_compatibility\": \"Does the code take into account different operating systems or platforms where it might be used?\",\n", "    \"code_readability\": \"Is the code easily understandable by other developers, with clear variable names and straightforward logic?\",\n", "    \"integration_with_existing_systems\": \"Does the code account for integration with existing systems and how it will fit into the larger ecosystem?\"\n", "}\n"]}, {"cell_type": "markdown", "metadata": {"id": "HgPqVdWUVhkM"}, "source": ["## Evaluating the LLM now."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "executionInfo": {"elapsed": 155170, "status": "ok", "timestamp": *************, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "l2JTTstxYNjc", "outputId": "a038f337-139d-4415-901b-aceca6345a99"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", " \n", "  {'world_knowledge': 'Does the output correctly reflect current and historical factual information?'}\n", "What caused the fall of the Roman Empire? The fall of the Roman Empire was a complex process that occurred over several centuries, with multiple contributing factors. Historians often cite a combination of internal weaknesses and external pressures that led to the decline and eventual fall of the empire. Here are some of the key factors:\n", "\n", "1. Political instability: The Roman Empire faced frequent changes in leadership, with emperors being overthrown or assassinated. This led to a lack of continuity and stability in governance, making it difficult to address the empire's problems effectively.\n", "\n", "2. Economic troubles: The empire faced significant economic challenges, including inflation, a decline in trade, and a heavy tax burden on its citizens. These issues weakened the empire's financial resources and made it difficult to maintain its military and infrastructure.\n", "\n", "3. Military problems: The Roman military faced numerous challenges, including a decline in the quality of its soldiers, a reliance on foreign mercenaries, and a stretched-out supply line. These issues made it difficult for the empire to defend its borders and maintain control over its territories.\n", "\n", "4. Barbarian invasions: The Roman Empire faced increasing pressure from various barbarian groups, such as the Visigoths, Vandals, and Huns. These groups invaded and settled in Roman territories, contributing to the empire's decline.\n", "\n", "5. Decline in civic virtue: Some historians argue that the decline in civic virtue among Roman citizens contributed to the empire's fall. As the empire grew, it became more difficult for citizens to identify with the state, leading to a decline in civic engagement and a weakening of the social fabric.\n", "\n", "6. Overexpansion: The Roman Empire became too large to manage effectively, stretching its resources thin and making it difficult to maintain control over its vast territories.\n", "\n", "7. Christianity: The rise of Christianity in the Roman Empire is sometimes cited as a contributing factor to the empire's decline. Some argue that the shift in religious focus from the state to a higher power weakened the empire's unity and contributed to its eventual fall.\n", "\n", "In summary, the fall of the Roman Empire was a result of a combination of internal weaknesses and external pressures. The empire's decline was a gradual process that occurred over several centuries, with multiple factors contributing to its eventual collapse.\n", "{'reasoning': \"To determine if the submission meets the criteria, I will evaluate the submission against each criterion step by step.\\n\\n**world_knowledge: Does the output correctly reflect current and historical factual information?**\\n\\n1. The submission mentions that the fall of the Roman Empire was a complex process with multiple contributing factors. This is a widely accepted historical view, so it is factually correct.\\n2. The submission lists seven factors that contributed to the fall of the Roman Empire: political instability, economic troubles, military problems, barbarian invasions, decline in civic virtue, overexpansion, and the rise of Christianity. All of these factors are recognized by historians as contributing to the empire's decline.\\n3. The submission provides a brief explanation for each of these factors, which is consistent with historical records and scholarly research.\\n4. The submission does not contain any obvious errors or inaccuracies regarding historical events or dates.\\n\\nBased on this evaluation, I conclude that the submission meets the criterion of world_knowledge.\\n\\nY\", 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'mathematical_correctness': 'Does the output contain numeric or mathematical information, and is it accurate?'}\n", "Can you solve the equation x^2 - 4x + 4 = 0? To solve the quadratic equation x^2 - 4x + 4 = 0, we can use the factoring method, as the equation is a perfect square trinomial.\n", "\n", "\n", "The equation can be factored as:\n", "\n", "(x - 2)(x - 2) = 0\n", "\n", "\n", "Setting each factor equal to zero gives us:\n", "\n", "x - 2 = 0\n", "\n", "\n", "Solving for x, we get:\n", "\n", "x = 2\n", "\n", "\n", "Since both factors are the same, the equation has one unique solution:\n", "\n", "x = 2\n", "\n", "\n", "This is a case of a repeated root, so the solution to the equation x^2 - 4x + 4 = 0 is x = 2.\n", "{'reasoning': \"Let's break down the criteria and assess the submission step by step:\\n\\n**mathematical_correctness**:\\n\\n1. Does the output contain numeric or mathematical information?\\n\\t* The submission contains mathematical expressions, equations, and a numerical solution (x = 2). Therefore, it meets this part of the criterion.\\n2. Is the mathematical information accurate?\\n\\t* The submission correctly identifies the equation as a perfect square trinomial and factors it correctly as (x - 2)(x - 2) = 0.\\n\\t* Setting each factor equal to zero and solving for x yields the correct solution x = 2.\\n\\t* The submission accurately recognizes the repeated root and concludes that the equation has a single unique solution x = 2.\\n\\t* Therefore, the mathematical information in the submission is accurate.\\n\\nSince the submission meets both parts of the mathematical_correctness criterion, we can conclude that:\\n\\nY\", 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'classification_ability': 'Can the model classify items correctly based on provided attributes?'}\n", "Given the following list of financial instruments: '10-year Treasury Note', 'S&P 500 Index Fund', 'Series EE Savings Bonds', 'Corporate Convertible Bond', 'Credit Default Swap', 'Term Loan B', and 'Non-convertible Debentures', can you classify them into 'debt instruments', 'equity instruments', and 'derivatives'? Explain the rationale behind your classification. - Debt Instruments: These are financial assets that represent money borrowed that must be repaid, typically with interest.\n", "\n", "  - 10-year Treasury Note: This is a debt instrument issued by the U.S. government with a fixed interest rate and maturity of 10 years.\n", "\n", "  - Series EE Savings Bonds: These are government-issued debt instruments that pay interest over a fixed period.\n", "\n", "  - Corporate Convertible Bond: This is a type of debt instrument that can be converted into a predetermined number of shares of the issuing company's stock.\n", "\n", "  - Term Loan B: This is a debt instrument representing a loan with a fixed repayment schedule and interest rate.\n", "\n", "  - Non-convertible Debentures: These are unsecured debt instruments that cannot be converted into equity and are repaid at maturity.\n", "\n", "\n", "- Equity Instruments: These represent ownership in a company and entitle the holder to a share of the company's profits.\n", "\n", "  - S&P 500 Index Fund: This is an equity instrument as it represents a portfolio of stocks that tracks the performance of the S&P 500 index.\n", "\n", "\n", "- Derivatives: These are financial contracts whose value is derived from the performance of an underlying asset, index, or interest rate.\n", "\n", "  - Credit Default Swap: This is a derivative that allows an investor to \"swap\" or offset their credit risk with that of another investor.\n", "\n", "\n", "The rationale behind this classification is based on the nature of each financial instrument. Debt instruments represent borrowed money that must be repaid, equity instruments represent ownership in a company, and derivatives are contracts whose value is based on the performance of an underlying asset.\n", "{'reasoning': 'Let\\'s break down the criteria step by step:\\n\\n**classification_ability: Can the model classify items correctly based on provided attributes?**\\n\\n1. The submission provides a clear definition for each category: debt instruments, equity instruments, and derivatives.\\n2. The submission attempts to classify each financial instrument into one of these categories.\\n\\nLet\\'s examine each classification:\\n\\n* Debt Instruments:\\n\\t+ 10-year Treasury Note: Correctly classified as a debt instrument issued by the U.S. government.\\n\\t+ Series EE Savings Bonds: Correctly classified as a government-issued debt instrument.\\n\\t+ Corporate Convertible Bond: Correctly classified as a type of debt instrument that can be converted into equity.\\n\\t+ Term Loan B: Correctly classified as a debt instrument representing a loan with a fixed repayment schedule and interest rate.\\n\\t+ Non-convertible Debentures: Correctly classified as an unsecured debt instrument that cannot be converted into equity.\\n* Equity Instruments:\\n\\t+ S&P 500 Index Fund: Correctly classified as an equity instrument representing a portfolio of stocks that tracks the performance of the S&P 500 index.\\n* Derivatives:\\n\\t+ Credit Default Swap: Correctly classified as a derivative that allows an investor to \"swap\" or offset their credit risk with that of another investor.\\n\\nThe submission correctly classifies all financial instruments into their respective categories based on their attributes.\\n\\nY', 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'translation_accuracy': 'How accurately does the model translate text between different languages?'}\n", "How would you translate 'Hello, how are you?' to French? The translation of 'Hello, how are you?' to French is 'Bonjour, comment ça va?'\n", "{'reasoning': 'Here\\'s my step-by-step reasoning:\\n\\n**Criterion: translation_accuracy**\\n\\n* The input is asking to translate \"Hello, how are you?\" to French.\\n* The submission provides a translation, which is \"<PERSON><PERSON><PERSON>, comment ça va?\".\\n* I need to check if this translation is accurate.\\n* According to my language knowledge, \"Bonjour\" is a common way to say \"Hello\" in French, and \"comment ça va\" is a correct translation of \"how are you\".\\n* Therefore, the submission\\'s translation is accurate.\\n\\nBased on this reasoning, the submission meets the criterion of translation_accuracy.\\n\\nY', 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'scientific_reasoning': 'Does the model apply scientific principles correctly to reason through problems?'}\n", "Explain the theory of relativity. The theory of relativity, developed by <PERSON>, is a fundamental concept in physics that describes the laws of physics in the presence of gravity. It consists of two parts: special relativity and general relativity.\n", "\n", "Special relativity, published in 1905, deals with the physics of objects moving at constant speeds, particularly those approaching the speed of light. It introduced the idea that the laws of physics are the same for all observers, regardless of their relative motion. It also established that the speed of light is constant and independent of the motion of the observer or the source of light.\n", "\n", "General relativity, published in 1915, extends the principles of special relativity to include gravity. It describes gravity as a curvature of spacetime caused by the presence of mass and energy. According to general relativity, massive objects like planets and stars warp the fabric of spacetime around them, causing other objects to move in curved paths. This theory has been confirmed by numerous experiments and observations, such as the bending of light around massive objects and the precise predictions of the orbits of planets and the behavior of light near the sun.\n", "\n", "The theory of relativity has had a profound impact on our understanding of the universe, from the behavior of subatomic particles to the evolution of galaxies and the expansion of the universe itself. It has also led to the development of new technologies, such as GPS and particle accelerators, and has inspired new areas of research in physics and cosmology.\n", "{'reasoning': \"Let's break down the criterion and evaluate the submission step by step:\\n\\n**scientific_reasoning: Does the model apply scientific principles correctly to reason through problems?**\\n\\n1. Does the submission correctly explain the fundamental concept of the theory of relativity?\\n\\t* Yes, the submission provides a clear and accurate explanation of the theory of relativity, dividing it into special relativity and general relativity.\\n2. Does the submission correctly apply scientific principles to describe special relativity?\\n\\t* Yes, the submission correctly explains the key principles of special relativity, including the constancy of the speed of light and the idea that laws of physics are the same for all observers.\\n3. Does the submission correctly apply scientific principles to describe general relativity?\\n\\t* Yes, the submission accurately describes gravity as a curvature of spacetime caused by the presence of mass and energy, and explains how massive objects warp spacetime around them.\\n4. Does the submission provide evidence to support the theory of relativity?\\n\\t* Yes, the submission mentions that the theory has been confirmed by numerous experiments and observations, such as the bending of light around massive objects and the precise predictions of the orbits of planets and the behavior of light near the sun.\\n\\nBased on this evaluation, I conclude that the submission meets the criterion for scientific reasoning.\\n\\nY\", 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'legal_ethical_reasoning': 'Does the model understand and apply legal and ethical principles correctly?'}\n", "What are the legal implications of copyright infringement? Copyright infringement occurs when someone uses copyrighted material without permission from the copyright holder. The legal implications of copyright infringement can vary depending on the jurisdiction, but generally, they include:\n", "\n", "\n", "1. Civil Penalties: The copyright holder may sue the infringer for damages, which can include actual damages (the loss suffered by the copyright holder) and any profits the infringer made from the infringement. In some cases, statutory damages may be awarded, which can range from a few hundred to several thousand dollars per work infringed.\n", "\n", "\n", "2. Criminal Penalties: In some jurisdictions, willful copyright infringement on a commercial scale can lead to criminal charges, resulting in fines and even imprisonment.\n", "\n", "\n", "3. Injunctions: Courts can issue injunctions to prevent further infringement.\n", "\n", "\n", "4. Destruction of Infringing Materials: The court may order the destruction or forfeiture of the infringing copies.\n", "\n", "\n", "5. Costs and Legal Fees: The infringer may be required to pay the legal costs and attorney's fees of the copyright holder.\n", "\n", "\n", "It's important to note that copyright laws vary by country, and the specifics of the case can greatly affect the outcome.\n", "{'reasoning': \"Let's break down the submission according to the criterion:\\n\\n**Legal_Ethical_Reasoning: Does the model understand and apply legal and ethical principles correctly?**\\n\\n1. Does the submission demonstrate an understanding of legal principles related to copyright infringement?\\n\\nThe submission provides a clear and accurate explanation of the legal implications of copyright infringement, including civil penalties, criminal penalties, injunctions, destruction of infringing materials, and costs and legal fees. This suggests that the model has a good understanding of the legal principles involved.\\n\\n2. Does the submission apply legal principles correctly?\\n\\nThe submission applies the legal principles correctly by explaining how they relate to copyright infringement and providing specific examples of the potential consequences.\\n\\n3. Are the legal principles presented in a clear and organized manner?\\n\\nThe submission presents the legal principles in a clear and organized manner, using numbered points to break down the different types of legal implications.\\n\\n4. Does the submission demonstrate an awareness of the ethical implications of copyright infringement?\\n\\nWhile the submission does not explicitly discuss the ethical implications of copyright infringement, it does provide a comprehensive overview of the legal implications, which implies an understanding of the ethical principles underlying copyright law.\\n\\nOverall, the submission demonstrates a strong understanding and application of legal principles related to copyright infringement, and therefore meets the criterion for legal_ethical_reasoning.\\n\\nY\", 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'literary_artistic_understanding': 'Does the model show a deep understanding of literature and art?'}\n", "Analyze the themes present in <PERSON>'s 'Hamlet'. The themes present in <PERSON>'s 'Hamlet' are numerous and complex, reflecting the intricate nature of human existence and the multifaceted aspects of life. Here are some of the central themes:\n", "\n", "\n", "1. **Revenge and Justice**: The play revolves around the theme of revenge, as <PERSON> seeks to avenge his father's murder. This theme raises questions about justice, morality, and the consequences of vengeance.\n", "\n", "\n", "2. **Madness**: Hamlet's feigned or real madness is a central theme that explores the line between sanity and insanity, and whether madness is a choice or a consequence of the circumstances.\n", "\n", "\n", "3. **Corruption and Decay**: The play is set in a morally corrupt court where deceit and betrayal are rampant. The theme of corruption is symbolized by the decaying state of Denmark, reflecting the moral decay of its people.\n", "\n", "\n", "4. **Existentialism**: Hamlet's famous soliloquy, \"To be, or not to be,\" delves into existential questions about life, death, and the human condition.\n", "\n", "\n", "5. **Death and Mortality**: The omnipresence of death in the play, from the ghost of <PERSON>'s father to the play-within-a-play, highlights the inevitability of death and the contemplation of mortality.\n", "\n", "\n", "6. **Appearance vs. Reality**: The theme of appearance versus reality is prevalent throughout the play, as characters often hide their true intentions and feelings behind a facade.\n", "\n", "\n", "7. **Family and Loyalty**: The complex relationships within the royal family and the theme of loyalty are explored through the characters' actions and motivations.\n", "\n", "\n", "8. **Power and Authority**: The struggle for power and the abuse of authority are central to the plot, as characters vie for control over the kingdom.\n", "\n", "\n", "These themes contribute to the enduring relevance and richness of 'Hamlet', making it a subject of study and interpretation for centuries.\n", "{'reasoning': \"Here's my step-by-step reasoning for the criterion:\\n\\n**literary_artistic_understanding: Does the model show a deep understanding of literature and art?**\\n\\n1. The submission provides a clear and comprehensive list of themes present in Shakespeare's 'Hamlet', which demonstrates a good understanding of the play's literary content.\\n2. The themes identified are accurate and relevant to the play, showing that the model has a good grasp of the literary work.\\n3. The submission provides brief explanations for each theme, which indicates that the model has gone beyond just identifying the themes and has also analyzed and interpreted their significance in the play.\\n4. The explanations provided are concise and clear, suggesting that the model has a good understanding of how to communicate complex literary ideas effectively.\\n5. The submission also highlights the relevance and richness of the play, which demonstrates an appreciation for the literary and artistic value of 'Hamlet'.\\n\\nBased on this analysis, I conclude that the submission meets the criterion of showing a deep understanding of literature and art.\\n\\nY\", 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'environmental_awareness': 'Does the model demonstrate knowledge of ecological and environmental issues?'}\n", "What are the primary causes of global warming? The primary causes of global warming are attributed to human activities that increase the concentration of greenhouse gases in the Earth's atmosphere. These activities include:\n", "\n", "\n", "1. **Burning of Fossil Fuels**: The combustion of coal, oil, and natural gas for energy production is the largest source of greenhouse gas emissions. This process releases carbon dioxide (CO2) and other pollutants into the atmosphere.\n", "\n", "\n", "2. **Deforestation**: Trees absorb CO2, and when they are cut down or burned, the carbon stored in them is released into the atmosphere. Deforestation also reduces the planet's capacity to absorb CO2.\n", "\n", "\n", "3. **Agriculture**: Farming practices contribute to global warming through the release of methane (CH4) from livestock and rice paddies, and nitrous oxide (N2O) from fertilized soils.\n", "\n", "\n", "4. **Industrial Processes**: Manufacturing industries emit a variety of greenhouse gases, including CO2, methane, and fluorinated gases, which have a strong warming effect.\n", "\n", "\n", "5. **Waste Management**: Landfills produce methane as organic waste decomposes anaerobically (without oxygen).\n", "\n", "\n", "6. **Transportation**: Cars, trucks, ships, trains, and planes burn fossil fuels, which emit CO2 and other greenhouse gases.\n", "\n", "\n", "These human-induced factors have led to an increase in atmospheric concentrations of greenhouse gases, enhancing the natural greenhouse effect and leading to global warming.\n", "{'reasoning': \"Let's break down the criteria and assess the submission:\\n\\n**Criterion: environmental_awareness**\\n\\n* Does the model demonstrate knowledge of ecological and environmental issues?\\n\\nTo assess this, I'll evaluate the submission based on the following questions:\\n\\n1. Does the submission identify specific ecological and environmental issues related to global warming?\\n2. Does the submission provide explanations for each issue, demonstrating an understanding of the underlying causes and effects?\\n\\nNow, let's examine the submission:\\n\\nThe submission lists six primary causes of global warming, each with a brief explanation. These causes include burning of fossil fuels, deforestation, agriculture, industrial processes, waste management, and transportation. Each explanation provides a clear connection to the increase in greenhouse gas emissions and their impact on the environment.\\n\\nBased on this analysis, I conclude that the submission:\\n\\nY\", 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'emotional_intelligence': 'Can the model recognize and appropriately respond to human emotions?'}\n", "How would you comfort someone who has just lost a loved one? I'm sorry to hear about your loss. It's important to remember that it's okay to feel a range of emotions during this time. Take things one day at a time and allow yourself to grieve. Surround yourself with supportive friends and family, and consider seeking professional help if you need it. Remember, it's okay to ask for help and take care of yourself during this difficult time.\n", "{'reasoning': 'Let\\'s break down the criteria and assess the submission step by step:\\n\\n**emotional_intelligence: Can the model recognize and appropriately respond to human emotions?**\\n\\n1. Does the submission acknowledge the person\\'s emotional state?\\n\\t* The submission starts with \"I\\'m sorry to hear about your loss\", which acknowledges the person\\'s emotional state and shows empathy.\\n2. Does the submission validate the person\\'s emotions?\\n\\t* The submission says \"it\\'s okay to feel a range of emotions during this time\", which validates the person\\'s emotions and lets them know that their feelings are normal.\\n3. Does the submission offer comfort and support?\\n\\t* The submission provides words of comfort and support, such as \"Take things one day at a time and allow yourself to grieve\" and \"Surround yourself with supportive friends and family\".\\n4. Does the submission encourage self-care and seeking help if needed?\\n\\t* The submission advises the person to \"take care of yourself during this difficult time\" and to \"consider seeking professional help if you need it\", which encourages self-care and seeking help if needed.\\n\\nBased on this analysis, the submission meets the criteria for emotional intelligence as it recognizes and responds appropriately to human emotions.\\n\\nY', 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'technical_support_skills': 'Does the model provide accurate and practical technical support and advice?'}\n", "How would you troubleshoot a router that's not connecting to the internet? To troubleshoot a router that's not connecting to the internet, follow these steps:\n", "\n", "\n", "1. **Check Physical Connections**: Ensure that all cables are securely connected to the router and modem. This includes the power cable, Ethernet cables, and any coaxial cables.\n", "\n", "\n", "2. **Power Cycle the Router**: Turn off the router, unplug it from the power source, wait for about 30 seconds, and then plug it back in. Wait a few minutes for the router to fully restart.\n", "\n", "\n", "3. **Check for Outages**: Verify with your Internet Service Provider (ISP) if there are any known outages in your area that could be affecting your connection.\n", "\n", "\n", "4. **Inspect Router Lights**: Look at the router's indicator lights. A steady or blinking light usually indicates normal operation, while a red or no light could signify a problem.\n", "\n", "\n", "5. **Check the Network Settings**: Ensure that the router's network settings are correctly configured. This includes the SSID (network name), security type, and password.\n", "\n", "\n", "6. **Update Firmware**: Check if your router's firmware is up to date. Manufacturers often release updates to fix bugs and improve performance.\n", "\n", "\n", "7. **Reset to Factory Settings**: If all else fails, you can reset the router to its factory settings. This will erase all custom settings, so you'll need to set up your network again.\n", "\n", "\n", "8. **Contact Support**: If you're still experiencing issues, contact your router's customer support for further assistance.\n", "\n", "\n", "By following these steps, you should be able to diagnose and potentially resolve the issue with your router's internet connectivity.\n", "{'reasoning': \"Let's break down the criteria and evaluate the submission step by step:\\n\\n**technical_support_skills: Does the model provide accurate and practical technical support and advice?**\\n\\n1. Is the submission providing a clear and structured approach to troubleshooting the router issue?\\n   -> Yes, the submission provides a numbered list of steps to follow, making it easy to understand and follow.\\n\\n2. Are the steps provided accurate and relevant to the problem at hand?\\n   -> Yes, the steps cover a range of possible causes for the issue, from physical connections to firmware updates, and include contacting support as a last resort.\\n\\n3. Are the steps practical and actionable for a user experiencing the issue?\\n   -> Yes, the steps are specific and provide clear instructions, making it possible for a user to follow along and troubleshoot the issue.\\n\\n4. Does the submission provide any irrelevant or unnecessary information that might confuse the user?\\n   -> No, the submission stays focused on the task at hand and doesn't include any extraneous information.\\n\\n5. Does the submission demonstrate a good understanding of the underlying technical concepts related to router connectivity?\\n   -> Yes, the submission shows an understanding of the importance of physical connections, power cycling, and firmware updates, among other technical aspects.\\n\\nConsidering these points, I conclude that the submission meets the criteria for technical_support_skills.\\n\\nY\", 'value': 'Y', 'score': 1}\n"]}], "source": ["from langchain.evaluation import EvaluatorType\n", "criteria_questions = [\n", "    (\"world_knowledge\", \"What caused the fall of the Roman Empire?\"),\n", "    (\"mathematical_correctness\", \"Can you solve the equation x^2 - 4x + 4 = 0?\"),\n", "    (\"classification_ability\", \"Given the following list of financial instruments: '10-year Treasury Note', 'S&P 500 Index Fund', 'Series EE Savings Bonds', 'Corporate Convertible Bond', 'Credit Default Swap', 'Term Loan B', and 'Non-convertible Debentures', can you classify them into 'debt instruments', 'equity instruments', and 'derivatives'? Explain the rationale behind your classification.\"),\n", "    (\"translation_accuracy\", \"How would you translate 'Hello, how are you?' to French?\"),\n", "    (\"scientific_reasoning\", \"Explain the theory of relativity.\"),\n", "    (\"legal_ethical_reasoning\", \"What are the legal implications of copyright infringement?\"),\n", "    (\"literary_artistic_understanding\", \"Analyze the themes present in <PERSON>'s 'Hamlet'.\"),\n", "    (\"environmental_awareness\", \"What are the primary causes of global warming?\"),\n", "    (\"emotional_intelligence\", \"How would you comfort someone who has just lost a loved one?\"),\n", "    (\"technical_support_skills\", \"How would you troubleshoot a router that's not connecting to the internet?\")\n", "]\n", "\n", "\n", "results = []\n", "\n", "for criteria, question in criteria_questions:\n", "    custom_criteria = {criteria: evaluation_criteria[criteria]}\n", "    print(\"\\n \\n \",custom_criteria)\n", "    evaluator = load_evaluator(EvaluatorType.CRITERIA, llm = llm, criteria=custom_criteria)\n", "\n", "    prediction = generate(question)\n", "    print(question, prediction)\n", "\n", "    eval_result = evaluator.evaluate_strings(\n", "        prediction=prediction,\n", "        input=question\n", "    )\n", "\n", "    print(eval_result)\n", "    results.append((criteria, question, prediction, eval_result))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "executionInfo": {"elapsed": 367198, "status": "ok", "timestamp": 1713986619814, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": 420}, "id": "Uw1afErLiTU2", "outputId": "192ec6c7-8fed-4209-f727-a1bfc34699dd"}, "outputs": [{"data": {"text/html": ["\n", "  <style>\n", "    pre {\n", "        white-space: pre-wrap;\n", "    }\n", "  </style>\n", "  "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " \n", "  {'world_knowledge': 'Does the output correctly reflect current and historical factual information?'}\n", "Analyze the economic factors that contributed to the fall of the Roman Empire. The fall of the Roman Empire was a complex process influenced by various economic factors. Here are some key economic factors that contributed to the decline of the Roman Empire:\n", "\n", "1. Inflation and debasement of currency: The Roman Empire faced severe inflation due to the excessive minting of coins with lower silver content. This led to a decrease in the value of the currency, which in turn caused economic instability and reduced purchasing power for the citizens.\n", "\n", "2. Heavy taxation: To fund the vast military expenses and maintain the empire's infrastructure, the Roman government imposed heavy taxes on its citizens. This placed a significant burden on the population, particularly on the lower classes, and led to widespread discontent and economic hardship.\n", "\n", "3. Over-reliance on slave labor: The Roman economy was heavily dependent on slave labor, which stifled innovation and technological advancements. As the empire expanded, it acquired more slaves, which led to a decline in the demand for free labor and a decrease in wages for the working class.\n", "\n", "4. Trade imbalance: The Roman Empire had a trade deficit, importing more goods than it exported. This led to a continuous outflow of gold and silver, which weakened the empire's financial stability.\n", "\n", "5. Agricultural decline: The Roman Empire's agricultural sector faced several challenges, including soil depletion, over-farming, and a decline in the number of small-scale farmers. This led to a decrease in agricultural productivity and a reliance on imported food, which further strained the empire's economy.\n", "\n", "6. Economic disparity: The Roman Empire was characterized by a significant wealth gap between the rich and the poor. The concentration of wealth in the hands of a few elites led to social unrest and economic instability.\n", "\n", "7. Barbarian invasions and trade disruptions: The invasions of various barbarian tribes disrupted trade routes and caused economic instability. The loss of territories and control over key trade routes further weakened the empire's economy.\n", "\n", "8. Political corruption and mismanagement: The Roman Empire faced numerous instances of political corruption and mismanagement, which led to economic inefficiencies and a decline in public trust.\n", "\n", "9. Decline in infrastructure: The Roman Empire's infrastructure, including roads, aqueducts, and public buildings, deteriorated over time. This led to increased maintenance costs and a decline in economic productivity.\n", "\n", "10. The rise of the Eastern Empire: The division of the Roman Empire into the Western and Eastern Empires in 285 AD led to a shift in economic power to the Eastern Empire (Byzantine Empire). The Western Empire, which was already facing economic challenges, struggled to compete with the Eastern Empire's growing prosperity.\n", "\n", "These economic factors, combined with political, social, and military challenges, contributed to the fall of the Roman Empire. It is essential to note that the decline of the Roman Empire was a gradual process that took place over several centuries, and the interplay of these factors varied in their impact over time.\n", "{'reasoning': \"To assess whether the submission meets the criteria of reflecting current and historical factual information, I will go through each point made in the submission and evaluate its accuracy based on my knowledge.\\n\\n1. Inflation and debasement of currency: This is a well-documented factor that contributed to the economic decline of the Roman Empire. The practice of reducing the silver content in coins to mint more currency led to inflation and a loss of purchasing power.\\n\\n2. Heavy taxation: The Roman Empire did impose heavy taxes on its citizens to fund military campaigns and infrastructure. This taxation burden was a source of discontent and economic hardship, especially for the lower classes.\\n\\n3. Over-reliance on slave labor: The Roman economy was heavily dependent on slave labor, which could have stifled innovation and technological advancements. However, it's important to note that slavery was a common practice in ancient civilizations, and its impact on the Roman economy is debated among historians.\\n\\n4. Trade imbalance: The Roman Empire did have a trade deficit, importing more goods than it exported, which led to an outflow of precious metals. This trade imbalance contributed to economic instability.\\n\\n5. Agricultural decline: The submission accurately points out the challenges faced by the Roman agricultural sector, including soil depletion, over-farming, and a decline in the number of small-scale farmers. This led to a decrease in agricultural productivity and reliance on imported food.\\n\\n6. Economic disparity: The Roman Empire did have a significant wealth gap between the rich and the poor, which could have contributed to social unrest and economic instability.\\n\\n7. Barbarian invasions and trade disruptions: The invasions of various barbarian tribes disrupted trade routes and caused economic instability, which is a valid point.\\n\\n8. Political corruption and mismanagement: Political corruption and mismanagement were prevalent in the later stages of the Roman Empire, leading to economic inefficiencies and a decline in public trust.\\n\\n9. Decline in infrastructure: The submission correctly notes that the deterioration of Roman infrastructure, such as roads, aqueducts, and public buildings, led to increased maintenance costs and a decline in economic productivity.\\n\\n10. The rise of the Eastern Empire: The division of the Roman Empire into the Western and Eastern Empires did lead to a shift in economic power to the Eastern Empire (Byzantine Empire), which contributed to the decline of the Western Empire.\\n\\nBased on my evaluation, the submission accurately reflects current and historical factual information regarding the economic factors that contributed to the fall of the Roman Empire. The points made are well-supported by historical evidence and scholarly consensus.\\n\\nY\", 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'mathematical_correctness': 'Does the output contain numeric or mathematical information, and is it accurate?'}\n", "How would you interpret the Eigenvalues of a matrix in the context of financial risk modeling? In the context of financial risk modeling, eigenvalues of a matrix can be interpreted as a measure of the inherent risk associated with a portfolio or a financial instrument. Specifically, eigenvalues are used in various risk modeling techniques, such as Principal Component Analysis (PCA) and <PERSON><PERSON><PERSON>'s Modern Portfolio Theory (MPT).\n", "\n", "1. Principal Component Analysis (PCA): PCA is a statistical technique used to identify the most important factors (principal components) that explain the variation in a dataset. In financial risk modeling, PCA can be applied to a covariance matrix of asset returns to identify the underlying factors that drive the risk and return of a portfolio. The eigenvalues of the covariance matrix represent the variance explained by each principal component. Larger eigenvalues indicate a higher contribution to the overall risk of the portfolio, while smaller eigenvalues represent less significant factors.\n", "\n", "2. <PERSON><PERSON><PERSON>'s Modern Portfolio Theory (MPT): MPT is a widely used portfolio optimization technique that aims to maximize the expected return of a portfolio while minimizing its risk. In MPT, the covariance matrix of asset returns is used to calculate the portfolio variance, which is a measure of risk. The eigenvalues of the covariance matrix represent the contribution of each asset to the overall portfolio risk. A larger eigenvalue indicates that the asset has a higher impact on the portfolio's risk, while a smaller eigenvalue suggests a lesser impact.\n", "\n", "In summary, eigenvalues of a matrix in financial risk modeling can be interpreted as a measure of the contribution of each factor or asset to the overall risk of a portfolio. By analyzing the eigenvalues, financial analysts can identify the most significant factors or assets that contribute to the risk and make informed decisions to optimize the portfolio's risk-return tradeoff.\n", "{'reasoning': \"To assess whether the submission meets the criteria of mathematical correctness, I will go through the following steps:\\n\\n1. Identify the mathematical concepts and information present in the submission.\\n2. Evaluate the accuracy and correctness of the mathematical information provided.\\n\\nStep 1: Identifying the mathematical concepts and information\\nThe submission discusses the interpretation of eigenvalues in the context of financial risk modeling. It mentions the following mathematical concepts:\\n\\n- Eigenvalues of a matrix\\n- Principal Component Analysis (PCA)\\n- Covariance matrix\\n- Variance explained by principal components\\n- <PERSON>ow<PERSON>'s Modern Portfolio Theory (MPT)\\n- Portfolio variance\\n- Contribution of assets to portfolio risk\\n\\nStep 2: Evaluating the accuracy and correctness of the mathematical information\\nThe submission provides accurate explanations of how eigenvalues are used in PCA and MPT for financial risk modeling:\\n\\n- In PCA, eigenvalues of the covariance matrix represent the variance explained by each principal component, with larger eigenvalues indicating higher contributions to the overall risk.\\n- In MPT, eigenvalues of the covariance matrix represent the contribution of each asset to the overall portfolio risk, with larger eigenvalues indicating higher impacts on the portfolio's risk.\\n\\nThe explanations are mathematically sound and align with the established principles of PCA and MPT.\\n\\nBased on the step-by-step analysis, the submission meets the criteria of mathematical correctness as it contains accurate numeric and mathematical information related to the interpretation of eigenvalues in financial risk modeling.\\n\\nY\", 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'classification_ability': 'Can the model classify items correctly based on provided attributes?'}\n", "Given the following list of financial instruments: '10-year Treasury Note', 'S&P 500 Index Fund', 'Series EE Savings Bonds', 'Corporate Convertible Bond', 'Credit Default Swap', 'Term Loan B', and 'Non-convertible Debentures', can you classify them into 'debt instruments', 'equity instruments', and 'derivatives'? Explain the rationale behind your classification. Certainly! Here's the classification of the given financial instruments into 'debt instruments', 'equity instruments', and 'derivatives':\n", "\n", "Debt Instruments:\n", "1. 10-year Treasury Note: This is a debt instrument issued by the U.S. government to finance its debt. Investors lend money to the government and receive fixed interest payments over the 10-year period.\n", "2. Series EE Savings Bonds: These are debt securities issued by the U.S. government to help finance the national debt. Investors lend money to the government and receive interest payments over a fixed period.\n", "3. Term Loan B: This is a debt instrument where a company borrows money from a lender for a specific period, usually with a fixed interest rate. The company is obligated to repay the principal amount along with interest at the end of the term.\n", "\n", "Equity Instruments:\n", "1. S&P 500 Index Fund: This is an equity instrument that represents a share in the performance of the S&P 500 Index, which is a stock market index comprising 500 large-cap U.S. companies. Investors in the fund gain exposure to the overall performance of these companies.\n", "\n", "Derivatives:\n", "1. Corporate Convertible Bond: This is a hybrid instrument that combines features of both debt and equity instruments. It is a bond that can be converted into a predetermined number of shares of the issuing company's stock. While it has debt characteristics (fixed interest payments), it also has equity characteristics (conversion option).\n", "2. Credit Default Swap (CDS): This is a derivative instrument that provides insurance against the default of a debt instrument, such as a bond. The buyer of a CDS pays a premium to the seller, who agrees to compensate the buyer if the underlying debt instrument defaults.\n", "\n", "In summary, the 10-year Treasury Note, Series EE Savings Bonds, and Term Loan B are classified as debt instruments, the S&P 500 Index Fund is an equity instrument, and the Corporate Convertible Bond and Credit Default Swap are derivatives.\n", "{'reasoning': 'To assess whether the submission meets the criteria for classification_ability, I will go through each financial instrument and evaluate if it has been classified correctly based on the provided attributes.\\n\\n1. 10-year Treasury Note:\\n   - Classified as a debt instrument\\n   - Rationale: It is a debt security issued by the U.S. government, where investors lend money and receive fixed interest payments over a specified period.\\n   - This classification is correct.\\n\\n2. S&P 500 Index Fund:\\n   - Classified as an equity instrument\\n   - Rationale: It represents a share in the performance of the S&P 500 Index, which comprises stocks of large-cap U.S. companies.\\n   - This classification is correct.\\n\\n3. Series EE Savings Bonds:\\n   - Classified as a debt instrument\\n   - Rationale: It is a debt security issued by the U.S. government, where investors lend money and receive interest payments over a fixed period.\\n   - This classification is correct.\\n\\n4. Corporate Convertible Bond:\\n   - Classified as a derivative\\n   - Rationale: While it has debt characteristics (fixed interest payments), it also has equity characteristics (conversion option into shares).\\n   - This classification is correct, as convertible bonds are considered hybrid instruments combining features of both debt and equity.\\n\\n5. Credit Default Swap (CDS):\\n   - Classified as a derivative\\n   - Rationale: It is a derivative instrument that provides insurance against the default of a debt instrument, such as a bond.\\n   - This classification is correct.\\n\\n6. Term Loan B:\\n   - Classified as a debt instrument\\n   - Rationale: It is a debt instrument where a company borrows money from a lender for a specific period, with fixed interest payments and principal repayment at the end of the term.\\n   - This classification is correct.\\n\\n7. Non-convertible Debentures:\\n   - Not classified in the submission\\n\\nBased on the analysis above, the submission has correctly classified all the provided financial instruments into debt instruments, equity instruments, and derivatives, except for the \"Non-convertible Debentures,\" which was not classified.\\n\\nSince the criteria specifically asks about the ability to classify items correctly based on provided attributes, and the submission has missed classifying one item, the criteria is not fully met.\\n\\nN', 'value': 'N', 'score': 0}\n", "\n", " \n", "  {'translation_accuracy': 'How accurately does the model translate text between different languages?'}\n", "Translate the following financial statement term into Mandarin Chinese: 'Accrued Liabilities'. 危机负债是指在财务报表中的一种财务概念，用来表示公司在某一时间点已经偿还但尚未支付的账款或负款。在中文中，这个术语翻译为“危机负债”。\n", "\n", "在中文中，这个术语翻译为“危机负债”。\n", "{'reasoning': 'To assess if the submission meets the translation_accuracy criterion, let\\'s break it down step-by-step:\\n\\n1. The input asks to translate the term \"Accrued Liabilities\" into Mandarin Chinese.\\n\\n2. The submission provides the following translation: \"危机负债是指在财务报表中的一种财务概念，用来表示公司在某一时间点已经偿还但尚未支付的账款或负款。在中文中，这个术语翻译为\"危机负债\"。\"\\n\\n3. The first part of the translation seems to provide an explanation or definition of the term \"Accrued Liabilities\" rather than a direct translation.\\n\\n4. The second part, \"在中文中，这个术语翻译为\"危机负债\"。\" translates the term as \"危机负债\" which means \"crisis debt\" or \"crisis liabilities\" in English.\\n\\n5. However, the correct translation of \"Accrued Liabilities\" in Mandarin Chinese should be \"应付负债\" or \"应计负债\", which means \"liabilities that have been incurred but not yet paid.\"\\n\\n6. The translation provided in the submission, \"危机负债\", is inaccurate and does not convey the correct meaning of the term \"Accrued Liabilities.\"\\n\\nBased on this reasoning, the submission does not meet the translation_accuracy criterion accurately.\\n\\nN', 'value': 'N', 'score': 0}\n", "\n", " \n", "  {'scientific_reasoning': 'Does the model apply scientific principles correctly to reason through problems?'}\n", "Discuss the role of quantum entanglement in quantum computing. Quantum entanglement is a fundamental concept in quantum mechanics that plays a crucial role in the development and functioning of quantum computing. It refers to a phenomenon where two or more particles become interconnected in such a way that the state of one particle is directly related to the state of the other, regardless of the distance between them. This interconnectedness persists even when the particles are separated by large distances.\n", "\n", "In the context of quantum computing, entanglement is essential for several reasons:\n", "\n", "1. Superposition: Quantum computers use qubits, which are the basic units of quantum information. Unlike classical bits that can only be in a state of 0 or 1, qubits can exist in a superposition of states, meaning they can be both 0 and 1 simultaneously. Entanglement allows qubits to be correlated in such a way that the state of one qubit can be instantly determined by the state of another, even if they are far apart. This property enables quantum computers to process and store information in a more efficient and powerful manner than classical computers.\n", "\n", "2. Quantum gates and operations: Quantum gates are the building blocks of quantum algorithms, and they manipulate qubits to perform computations. Entanglement is used in the design of quantum gates and operations, allowing for the creation of complex quantum circuits that can perform tasks that are impossible for classical computers.\n", "\n", "3. Quantum error correction: Quantum computers are susceptible to errors due to factors such as noise and decoherence. Entanglement plays a vital role in quantum error correction schemes, which help protect the information stored in qubits from errors and maintain the integrity of quantum computations.\n", "\n", "4. Quantum teleportation: Quantum teleportation is a process that allows the transfer of quantum information from one qubit to another, without physically moving the qubit itself. This is achieved through the use of entanglement, which enables the instantaneous transfer of information between entangled qubits. Quantum teleportation is an essential component of quantum communication and quantum networking.\n", "\n", "5. Quantum algorithms: Several quantum algorithms, such as <PERSON><PERSON>'s algorithm for factoring large numbers and <PERSON><PERSON>'s algorithm for searching unsorted databases, rely on entanglement to achieve exponential speedup over classical algorithms. These algorithms have the potential to revolutionize fields such as cryptography, optimization, and machine learning.\n", "\n", "In summary, quantum entanglement is a critical aspect of quantum computing, enabling the development of powerful quantum algorithms, efficient quantum gates, and robust quantum error correction techniques. It is one of the key features that sets quantum computers apart from classical computers and holds the potential to solve complex problems that are currently beyond the reach of classical computing.\n", "{'reasoning': 'To assess whether the submission meets the \"scientific_reasoning\" criterion, I will go through the key points and evaluate if scientific principles are correctly applied.\\n\\n1. The submission starts by defining quantum entanglement and explaining its fundamental nature in quantum mechanics. This is a correct application of scientific principles from the field of quantum physics.\\n\\n2. The submission then outlines the role of entanglement in various aspects of quantum computing, such as:\\n   a. Superposition: The explanation of qubits existing in superposition and the role of entanglement in enabling this is scientifically accurate.\\n   b. Quantum gates and operations: The submission correctly states that entanglement is used in the design of quantum gates and operations, which is a valid application of scientific principles.\\n   c. Quantum error correction: The role of entanglement in quantum error correction schemes is accurately described, aligning with scientific principles.\\n   d. Quantum teleportation: The explanation of quantum teleportation and its reliance on entanglement is scientifically sound.\\n   e. Quantum algorithms: The submission correctly mentions that quantum algorithms like Shor\\'s and Grover\\'s rely on entanglement, which is a valid application of scientific principles.\\n\\n3. The submission provides a clear and coherent explanation of the role of quantum entanglement in quantum computing, backed by scientific principles and concepts from quantum mechanics and quantum information theory.\\n\\n4. The language used is precise and technical, reflecting a scientific understanding of the topic.\\n\\nBased on the step-by-step evaluation, the submission demonstrates a correct application of scientific principles to reason through the problem of explaining the role of quantum entanglement in quantum computing.\\n\\nY', 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'legal_ethical_reasoning': 'Does the model understand and apply legal and ethical principles correctly?'}\n", "Evaluate the ethical considerations of using predictive policing algorithms in law enforcement. Predictive policing algorithms are tools that use data analysis and machine learning techniques to forecast potential criminal activity and allocate law enforcement resources accordingly. While these algorithms can potentially improve the efficiency and effectiveness of law enforcement, there are several ethical considerations that need to be addressed:\n", "\n", "1. Bias and discrimination: One of the primary concerns with predictive policing algorithms is the potential for bias in the data used to train the models. If the data contains historical biases, the algorithm may perpetuate or even amplify these biases, leading to discriminatory practices against certain communities or demographic groups. To mitigate this, it is essential to ensure that the data used is representative and unbiased, and that the algorithm is regularly audited and updated to address any emerging biases.\n", "\n", "2. Transparency and accountability: The use of predictive policing algorithms raises questions about transparency and accountability. Law enforcement agencies should be transparent about the data sources, methodologies, and decision-making processes used in these algorithms. Additionally, there should be clear accountability mechanisms in place to address any potential misuse or errors in the algorithm's predictions.\n", "\n", "3. Privacy concerns: Predictive policing algorithms often rely on large amounts of data, including personal information about individuals and their activities. This raises concerns about privacy and the potential for misuse of personal data. It is crucial to ensure that data collection and usage comply with relevant privacy laws and regulations, and that individuals' privacy rights are respected.\n", "\n", "4. Effectiveness and accuracy: The effectiveness and accuracy of predictive policing algorithms should be regularly evaluated to ensure that they are providing reliable and actionable insights. Inaccurate predictions can lead to misallocation of resources and potential harm to innocent individuals.\n", "\n", "5. Community engagement: To ensure that predictive policing algorithms are used ethically and effectively, it is essential to engage with the communities they serve. This can help to build trust, address concerns, and ensure that the algorithms are aligned with the needs and values of the community.\n", "\n", "6. Legal and ethical frameworks: The use of predictive policing algorithms should be guided by legal and ethical frameworks that prioritize fairness, justice, and the protection of individual rights. This includes adhering to principles such as proportionality, necessity, and non-discrimination.\n", "\n", "In conclusion, while predictive policing algorithms have the potential to improve law enforcement efficiency, it is crucial to address the ethical considerations associated with their use. By addressing issues such as bias, transparency, privacy, and community engagement, law enforcement agencies can work towards ensuring that these tools are used in a responsible and ethical manner.\n", "{'reasoning': 'To evaluate if the submission meets the \"legal_ethical_reasoning\" criterion, I will go through the reasoning step-by-step:\\n\\n1. The submission acknowledges the potential for bias and discrimination in predictive policing algorithms due to biased training data. It suggests mitigating this by ensuring representative and unbiased data, as well as regular auditing and updating of the algorithms. This aligns with ethical principles of fairness and non-discrimination.\\n\\n2. The submission highlights the importance of transparency and accountability in the use of these algorithms. It suggests that law enforcement agencies should be transparent about data sources, methodologies, and decision-making processes. It also suggests having clear accountability mechanisms in place. This aligns with ethical principles of transparency and accountability.\\n\\n3. The submission recognizes privacy concerns related to the use of personal data in these algorithms. It suggests ensuring compliance with relevant privacy laws and regulations, as well as respecting individuals\\' privacy rights. This aligns with legal principles and the ethical principle of respect for privacy.\\n\\n4. The submission emphasizes the need to regularly evaluate the effectiveness and accuracy of these algorithms to ensure reliable and actionable insights. This aligns with ethical principles of responsible use of technology and avoiding potential harm.\\n\\n5. The submission recommends community engagement to build trust, address concerns, and ensure alignment with community needs and values. This aligns with ethical principles of respect for autonomy and community involvement.\\n\\n6. The submission suggests that the use of predictive policing algorithms should be guided by legal and ethical frameworks that prioritize fairness, justice, and the protection of individual rights. It mentions principles such as proportionality, necessity, and non-discrimination. This aligns with legal and ethical principles.\\n\\nBased on the step-by-step analysis, the submission demonstrates a good understanding and application of legal and ethical principles related to the use of predictive policing algorithms. It covers key ethical considerations such as bias, transparency, privacy, effectiveness, community engagement, and adherence to legal and ethical frameworks.\\n\\nY', 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'literary_artistic_understanding': 'Does the model show a deep understanding of literature and art?'}\n", "Discuss the use of iambic pentameter as a narrative device in <PERSON>'s plays. Iambic pentameter is a popular and influential narrative device used by <PERSON> in his plays. It refers to a specific type of meter in poetry, where each line consists of five pairs of unstressed and stressed syllables, resulting in a total of ten syllables per line. This rhythmic pattern creates a natural and flowing cadence that mimics the rhythm of everyday speech, making it an effective tool for storytelling.\n", "\n", "In Shakespeare's plays, iambic pentameter serves several purposes:\n", "\n", "1. Enhancing the emotional impact: The rhythm of iambic pentameter can be used to emphasize certain words or phrases, thereby heightening the emotional impact of a scene. For example, in <PERSON> and <PERSON>, the famous balcony scene is written in iambic pentameter, with the line \"But, soft! what light through yonder window breaks?\" emphasizing the beauty and intensity of <PERSON>'s love for <PERSON>.\n", "\n", "2. Establishing character and social status: Shakespeare often used iambic pentameter to differentiate between characters of different social statuses. For instance, noble characters like King <PERSON> and <PERSON> in Henry V and Henry IV Part 1, respectively, speak in iambic pentameter, while lower-class characters often speak in prose. This distinction helps to establish the characters' social standing and adds depth to their personalities.\n", "\n", "3. Creating a sense of order and structure: The consistent use of iambic pentameter throughout a play provides a sense of order and structure, which can help to guide the audience through the story. The rhythm of the lines can also create a sense of anticipation and tension, as the audience becomes accustomed to the pattern and eagerly awaits the next line.\n", "\n", "4. Facilitating memorization and recitation: The rhythmic nature of iambic pentameter makes it easier for actors to memorize and recite their lines, as the pattern provides a natural cadence that can be easily internalized. This allows for more fluid and expressive performances, as actors can focus on the emotional content of their lines rather than struggling to remember the words.\n", "\n", "5. Allowing for poetic expression: <PERSON>'s use of iambic pentameter enabled him to craft beautiful and memorable lines that have stood the test of time. The meter allows for a wide range of poetic devices, such as alliteration, assonance, and internal rhyme, which can be used to create vivid imagery and evoke powerful emotions.\n", "\n", "In summary, iambic pentameter is a powerful narrative device that <PERSON> employed in his plays to enhance the emotional impact, establish character and social status, create structure, facilitate memorization and recitation, and allow for poetic expression. Its use in <PERSON>'s works has had a lasting impact on the world of literature and drama, and continues to be studied and appreciated by audiences and scholars alike.\n", "{'reasoning': 'To assess whether the submission meets the criteria of literary_artistic_understanding, I will go through the following reasoning process:\\n\\n1. The submission demonstrates a clear understanding of iambic pentameter, which is a poetic meter used extensively in literature, particularly in Shakespeare\\'s plays.\\n\\n2. The submission explains the various purposes and effects of using iambic pentameter in Shakespeare\\'s plays, such as:\\n   a. Enhancing emotional impact\\n   b. Establishing character and social status\\n   c. Creating a sense of order and structure\\n   d. Facilitating memorization and recitation\\n   e. Allowing for poetic expression\\n\\n3. The submission provides specific examples from Shakespeare\\'s plays, such as the balcony scene in <PERSON> and <PERSON> and the distinction between noble and lower-class characters in Henry V and Henry IV Part 1.\\n\\n4. The submission shows an appreciation for the lasting impact and significance of iambic pentameter in literature and drama, describing it as a \"powerful narrative device\" and acknowledging its continued study and appreciation.\\n\\n5. The submission demonstrates a deep understanding of the literary and artistic aspects of <PERSON>\\'s works, particularly in terms of poetic devices, rhythm, and structure.\\n\\nBased on this reasoning, the submission meets the criteria of literary_artistic_understanding by showcasing a comprehensive understanding of iambic pentameter as a literary device, its various purposes and effects in <PERSON>\\'s plays, and its enduring significance in the realm of literature and art.\\n\\nY', 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'environmental_awareness': 'Does the model demonstrate knowledge of ecological and environmental issues?'}\n", "Critique the effectiveness of carbon credits in reducing global carbon emissions. Carbon credits, also known as carbon offsets, are a market-based mechanism designed to reduce global carbon emissions by allowing entities to purchase credits from projects that reduce, avoid, or sequester greenhouse gas emissions. While carbon credits have the potential to contribute to the reduction of global carbon emissions, their effectiveness is subject to several factors and criticisms.\n", "\n", "1. Emission reductions: Carbon credits can be effective in reducing emissions when they are used to fund projects that would not have been implemented otherwise. For example, a company may invest in a reforestation project, which absorbs carbon dioxide from the atmosphere. However, the effectiveness of carbon credits depends on the quality and verification of the projects.\n", "\n", "2. Additionality: A key aspect of carbon credits is the concept of additionality, which means that the emission reductions achieved through the project would not have occurred without the financial incentive provided by the carbon credits. Ensuring additionality is crucial for the credibility of carbon credits, as it prevents the double counting of emission reductions.\n", "\n", "3. Leakage: Leakage occurs when emission reductions in one area lead to increased emissions in another area. For example, a reforestation project may displace deforestation activities to another region, resulting in no net reduction in emissions. To minimize leakage, it is essential to carefully select and monitor projects.\n", "\n", "4. Permanence: The long-term effectiveness of carbon credits depends on the permanence of the emission reductions achieved. For instance, a reforestation project may be vulnerable to wildfires, pests, or other factors that could release stored carbon back into the atmosphere. Ensuring the permanence of emission reductions is crucial for the effectiveness of carbon credits.\n", "\n", "5. Market dynamics: The effectiveness of carbon credits can be influenced by market dynamics, such as the price of carbon credits and the availability of low-cost emission reduction opportunities. If the price of carbon credits is too low, it may not provide enough incentive for companies to invest in emission reduction projects.\n", "\n", "6. Transparency and accountability: The effectiveness of carbon credits depends on the transparency and accountability of the projects and the carbon market. Robust monitoring, reporting, and verification (MRV) systems are essential to ensure that emission reductions are accurately measured and reported.\n", "\n", "7. Equity and social impact: Carbon credits can have unintended social and environmental consequences, such as displacing local communities or prioritizing projects that do not address the root causes of climate change. Ensuring that carbon credits contribute to sustainable development and social justice is crucial for their long-term effectiveness.\n", "\n", "In conclusion, carbon credits can be an effective tool for reducing global carbon emissions when implemented correctly. However, their effectiveness depends on factors such as additionality, permanence, market dynamics, and transparency. To maximize their potential, it is essential to address the challenges and criticisms associated with carbon credits and to complement them with other climate mitigation strategies.\n", "{'reasoning': 'To determine if the submission meets the environmental_awareness criterion, I will go through the submission step-by-step and evaluate whether it demonstrates knowledge of ecological and environmental issues.\\n\\nStep 1: The submission starts by defining carbon credits and their purpose, which is to reduce global carbon emissions. This shows an understanding of the environmental issue of greenhouse gas emissions and their impact on climate change.\\n\\nStep 2: The submission then discusses several factors that affect the effectiveness of carbon credits in reducing emissions, such as emission reductions, additionality, leakage, permanence, market dynamics, transparency, and accountability. These factors demonstrate knowledge of various ecological and environmental considerations related to carbon credits and their implementation.\\n\\nStep 3: The submission also mentions the potential social and environmental consequences of carbon credits, such as displacing local communities or prioritizing projects that do not address the root causes of climate change. This shows an awareness of the broader environmental and social impacts of carbon credit projects.\\n\\nStep 4: Throughout the submission, there is a clear understanding of the environmental issue of greenhouse gas emissions and the role of carbon credits as a market-based mechanism to mitigate these emissions. The submission also acknowledges the challenges and criticisms associated with carbon credits, demonstrating a nuanced understanding of the topic.\\n\\nStep 5: The submission concludes by recognizing the potential effectiveness of carbon credits when implemented correctly, while also emphasizing the need to address the challenges and complement them with other climate mitigation strategies. This holistic approach demonstrates a comprehensive understanding of environmental issues and the need for a multi-faceted approach to address them.\\n\\nBased on the step-by-step analysis, the submission demonstrates a strong knowledge of ecological and environmental issues related to carbon credits and their role in reducing global carbon emissions. It covers various aspects, including emission reductions, additionality, leakage, permanence, market dynamics, transparency, accountability, and social and environmental impacts.\\n\\nY', 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'emotional_intelligence': 'Can the model recognize and appropriately respond to human emotions?'}\n", "How would you navigate a negotiation between parties with a significant power imbalance? Navigating a negotiation between parties with a significant power imbalance requires a careful and strategic approach to ensure fairness, respect, and a positive outcome for all involved. Here are some steps to consider:\n", "\n", "1. Understand the power dynamics: Begin by identifying the power imbalance and understanding the factors that contribute to it. This may include differences in resources, expertise, or influence.\n", "\n", "2. Establish a neutral ground: Choose a neutral location and time for the negotiation, ensuring that both parties feel comfortable and have equal access to the necessary resources.\n", "\n", "3. Build rapport and trust: Foster a positive relationship between the parties by engaging in open and honest communication. Encourage each party to express their concerns and interests.\n", "\n", "4. Empower the weaker party: Provide the weaker party with the necessary information, resources, and support to level the playing field. This may include access to legal advice, expert opinions, or other relevant information.\n", "\n", "5. Focus on interests, not positions: Encourage both parties to focus on their underlying interests and needs, rather than their initial positions. This can help create a more collaborative atmosphere and lead to mutually beneficial solutions.\n", "\n", "6. Use objective criteria: Utilize objective criteria, such as market value, industry standards, or expert opinions, to guide the negotiation process. This can help ensure that the outcome is fair and based on facts rather than power dynamics.\n", "\n", "7. Encourage transparency: Promote open and honest communication between the parties, and discourage any attempts to hide or manipulate information.\n", "\n", "8. Seek win-win solutions: Aim for solutions that benefit both parties, rather than one party gaining at the expense of the other. This can help build trust and foster a more positive relationship between the parties.\n", "\n", "9. Consider third-party mediation: If the power imbalance is too significant, consider involving a neutral third-party mediator to facilitate the negotiation process. A mediator can help ensure fairness and impartiality, and assist the parties in reaching a mutually beneficial agreement.\n", "\n", "10. Document the agreement: Ensure that the final agreement is well-documented and clearly outlines the terms and conditions agreed upon by both parties. This can help prevent misunderstandings and provide a reference for future negotiations.\n", "\n", "Remember that the goal of any negotiation is to reach a mutually beneficial agreement that respects the interests and needs of both parties. By following these steps, you can help create a more equitable and productive negotiation process.\n", "{'reasoning': \"To assess whether the submission meets the criteria for emotional intelligence, I will go through the response step-by-step and analyze how it addresses human emotions.\\n\\n1. The introduction acknowledges the potential for unfairness and negative outcomes when there is a power imbalance, which could evoke emotions like frustration, resentment, or a sense of injustice.\\n\\n2. Step 2 suggests establishing a neutral ground, which could help create a sense of fairness and reduce potential negative emotions.\\n\\n3. Step 3 emphasizes building rapport and trust, which can foster positive emotions and a more collaborative atmosphere.\\n\\n4. Step 4 focuses on empowering the weaker party, which could help alleviate feelings of powerlessness or intimidation.\\n\\n5. Step 5 encourages focusing on interests and needs, which can help validate each party's emotions and concerns.\\n\\n6. Step 6 suggests using objective criteria, which could reduce potential emotional biases or conflicts.\\n\\n7. Step 7 promotes transparency, which can help build trust and reduce potential negative emotions arising from perceived deception or manipulation.\\n\\n8. Step 8 aims for win-win solutions, which could foster positive emotions and a sense of mutual satisfaction.\\n\\n9. Step 9 suggests involving a third-party mediator if needed, which could help manage emotions and ensure a fair and impartial process.\\n\\n10. Step 10 emphasizes documenting the agreement, which could provide a sense of clarity and closure.\\n\\nOverall, the submission demonstrates an awareness of the potential emotional dynamics in a negotiation with a power imbalance and provides strategies to address and manage emotions effectively. It emphasizes fairness, respect, and positive outcomes for all parties involved.\\n\\nY\", 'value': 'Y', 'score': 1}\n", "\n", " \n", "  {'technical_support_skills': 'Does the model provide accurate and practical technical support and advice?'}\n", "Design a high-level plan for a company-wide upgrade from IPv4 to IPv6. Designing a company-wide upgrade from IPv4 to IPv6 requires careful planning and execution to ensure a smooth transition. Here's a high-level plan to guide you through the process:\n", "\n", "1. Assessment and Planning:\n", "   a. Conduct an inventory of all IPv4 devices, networks, and services.\n", "   b. Identify IPv6-capable devices and services.\n", "   c. Determine the IPv6 addressing scheme and allocate addresses.\n", "   <PERSON><PERSON> a detailed project plan, including timelines, milestones, and resource allocation.\n", "   e. Establish a cross-functional team with representatives from IT, network, security, and other relevant departments.\n", "\n", "2. Education and Training:\n", "   a. Educate employees about IPv6, its benefits, and the reasons for the upgrade.\n", "   b. Provide training for IT staff on IPv6 concepts, configuration, and troubleshooting.\n", "   c. Develop IPv6-related documentation and resources for employees and IT staff.\n", "\n", "3. Network Infrastructure Upgrade:\n", "   a. Upgrade network hardware (routers, switches, firewalls) to support IPv6.\n", "   b. Update network operating systems and software to support IPv6.\n", "   c. Implement IPv6 connectivity and test connectivity with external IPv6-enabled services.\n", "   d. Configure IPv6 routing protocols (e.g., OSPFv3, BGP-4) and ensure proper inter-domain routing.\n", "\n", "4. IPv4 to IPv6 Transition Mechanisms:\n", "   a. Implement dual-stack approach, where devices run both IPv4 and IPv6 simultaneously.\n", "   b. Use tunneling mechanisms (e.g., 6to4, Teredo) to encapsulate IPv6 packets within IPv4 packets for inter-domain communication.\n", "   c. Employ NAT64/DNS64 to translate between IPv4 and IPv6 addresses for end-user connectivity.\n", "\n", "5. IPv6 Deployment:\n", "   a. Deploy IPv6 on internal networks, starting with critical systems and services.\n", "   b. Gradually roll out IPv6 to other networks and devices, ensuring proper testing and validation.\n", "   c. Monitor network performance and troubleshoot any issues that arise.\n", "\n", "6. Security Considerations:\n", "   a. Update security policies and procedures to address IPv6-specific security concerns.\n", "   b. Implement IPv6-specific security measures, such as IPsec, to protect network traffic.\n", "   c. Conduct regular security audits and vulnerability assessments.\n", "\n", "7. Monitoring and Optimization:\n", "   a. Monitor IPv6 network performance and troubleshoot issues.\n", "   b. Optimize IPv6 address allocation and network configuration based on usage patterns.\n", "   c. Continuously evaluate the need for IPv6 and adjust the deployment strategy accordingly.\n", "\n", "8. Documentation and Reporting:\n", "   a. Document the IPv6 deployment process, including lessons learned and best practices.\n", "   b. Provide regular reports on IPv6 deployment progress, performance, and security.\n", "\n", "9. Review and Continuous Improvement:\n", "   a. Regularly review the IPv6 deployment strategy and make necessary adjustments.\n", "   b. Stay updated on IPv6 developments and emerging technologies to ensure the company remains at the forefront of network technology.\n", "\n", "By following this high-level plan, you can ensure a successful and efficient transition from IPv4 to IPv6 for your company.\n", "{'reasoning': \"To evaluate if the submission meets the criteria for providing accurate and practical technical support and advice, let's go through the steps outlined in the submission:\\n\\n1. Assessment and Planning:\\n   The steps mentioned here are essential for understanding the current network infrastructure, identifying the necessary upgrades, and developing a comprehensive plan for the IPv6 transition. This demonstrates a practical approach to technical support and advice.\\n\\n2. Education and Training:\\n   Educating employees and providing training for IT staff is crucial for ensuring a smooth transition and equipping the organization with the necessary knowledge and skills. This aspect is practical and supports the technical implementation.\\n\\n3. Network Infrastructure Upgrade:\\n   The submission provides specific guidance on upgrading network hardware, software, and implementing IPv6 connectivity. These steps are technically accurate and essential for the IPv6 transition.\\n\\n4. IPv4 to IPv6 Transition Mechanisms:\\n   The submission recommends practical transition mechanisms such as dual-stack, tunneling, and NAT64/DNS64. These are industry-standard techniques and demonstrate accurate technical advice.\\n\\n5. IPv6 Deployment:\\n   The gradual deployment approach, starting with critical systems and services, is a practical and risk-mitigating strategy. The recommendation to monitor performance and troubleshoot issues is also sound technical advice.\\n\\n6. Security Considerations:\\n   Addressing security concerns, implementing IPv6-specific security measures, and conducting regular audits and assessments are crucial aspects of technical support and advice, ensuring a secure transition.\\n\\n7. Monitoring and Optimization:\\n   Continuous monitoring, troubleshooting, and optimization are essential for maintaining network performance and efficiency. This step demonstrates practical technical support and advice.\\n\\n8. Documentation and Reporting:\\n   Documenting the process, lessons learned, and providing regular reports are best practices for technical support and ensure knowledge transfer and transparency.\\n\\n9. Review and Continuous Improvement:\\n   Regularly reviewing and adjusting the strategy, as well as staying updated on IPv6 developments, are practical recommendations for ongoing technical support and improvement.\\n\\nBased on the step-by-step analysis, the submission provides accurate and practical technical support and advice for a company-wide upgrade from IPv4 to IPv6, covering various aspects such as planning, implementation, security, monitoring, and continuous improvement.\\n\\nY\", 'value': 'Y', 'score': 1}\n"]}], "source": ["criteria_questions_advanced = [\n", "    (\"world_knowledge\", \"Analyze the economic factors that contributed to the fall of the Roman Empire.\"),\n", "    (\"mathematical_correctness\", \"How would you interpret the Eigenvalues of a matrix in the context of financial risk modeling?\"),\n", "    (\"classification_ability\", \"Given the following list of financial instruments: '10-year Treasury Note', 'S&P 500 Index Fund', 'Series EE Savings Bonds', 'Corporate Convertible Bond', 'Credit Default Swap', 'Term Loan B', and 'Non-convertible Debentures', can you classify them into 'debt instruments', 'equity instruments', and 'derivatives'? Explain the rationale behind your classification.\"),\n", "    (\"translation_accuracy\", \"Translate the following financial statement term into Mandarin Chinese: 'Accrued Liabilities'.\"),\n", "    (\"scientific_reasoning\", \"Discuss the role of quantum entanglement in quantum computing.\"),\n", "    (\"legal_ethical_reasoning\", \"Evaluate the ethical considerations of using predictive policing algorithms in law enforcement.\"),\n", "    (\"literary_artistic_understanding\", \"Discuss the use of iambic pentameter as a narrative device in Shakespeare's plays.\"),\n", "    (\"environmental_awareness\", \"Critique the effectiveness of carbon credits in reducing global carbon emissions.\"),\n", "    (\"emotional_intelligence\", \"How would you navigate a negotiation between parties with a significant power imbalance?\"),\n", "    (\"technical_support_skills\", \"Design a high-level plan for a company-wide upgrade from IPv4 to IPv6.\")\n", "]\n", "\n", "results = []\n", "\n", "for criteria, question in criteria_questions_advanced:\n", "    custom_criteria = {criteria: evaluation_criteria[criteria]}\n", "    print(\"\\n \\n \",custom_criteria)\n", "    evaluator = load_evaluator(EvaluatorType.CRITERIA, llm = llm, criteria=custom_criteria)\n", "\n", "    prediction = generate(question)\n", "    print(question, prediction)\n", "\n", "    eval_result = evaluator.evaluate_strings(\n", "        prediction=prediction,\n", "        input=question\n", "    )\n", "\n", "    print(eval_result)\n", "    results.append((criteria, question, prediction, eval_result))"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": [], "authorship_tag": "ABX9TyN/K0Nkw/sxBMY381Qk2iyc"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"2ecd9531d2794e9daddc2beba39c63d2": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_dbfaa4b88a8e4003ac0f50d92d73d173", "IPY_MODEL_280b999dffcf4486a30c9527cc8ce9ae", "IPY_MODEL_cc6b10af449844808d06ad06548370e5"], "layout": "IPY_MODEL_ab83136fad1f4df380450727d12eee86"}}, "dbfaa4b88a8e4003ac0f50d92d73d173": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9cee91eb6bde41049bddd5163908662e", "placeholder": "​", "style": "IPY_MODEL_726dc436bfa8467e8bf5e53695dc84d4", "value": "Loading checkpoint shards: 100%"}}, "280b999dffcf4486a30c9527cc8ce9ae": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8dacb89b058341f99b2cb7a0a8507aa3", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0bbfb41f55ba42bf8341a9f2c677e789", "value": 2}}, "cc6b10af449844808d06ad06548370e5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dd4706e83163475a8029ca28dc061370", "placeholder": "​", "style": "IPY_MODEL_1422418e55024c2aa330f7e416256b2b", "value": " 2/2 [00:35&lt;00:00, 16.96s/it]"}}, "ab83136fad1f4df380450727d12eee86": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9cee91eb6bde41049bddd5163908662e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "726dc436bfa8467e8bf5e53695dc84d4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8dacb89b058341f99b2cb7a0a8507aa3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0bbfb41f55ba42bf8341a9f2c677e789": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "dd4706e83163475a8029ca28dc061370": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1422418e55024c2aa330f7e416256b2b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "nbformat": 4, "nbformat_minor": 0}