{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 22674, "status": "ok", "timestamp": 1752300230897, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "6snp6QNuf18y", "outputId": "d247bc74-0d8d-493a-f19d-80d8c0c1fa39"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.6/59.6 MB\u001b[0m \u001b[31m9.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m323.9/323.9 kB\u001b[0m \u001b[31m21.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.9/2.9 MB\u001b[0m \u001b[31m53.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m131.1/131.1 kB\u001b[0m \u001b[31m10.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip install -U -q langchain_groq gradio"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"executionInfo": {"elapsed": 7204, "status": "ok", "timestamp": 1752300241518, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "2SdY2wYNf6la"}, "outputs": [], "source": ["from langchain_groq import ChatGroq\n", "from google.colab import userdata\n", "llm_groq = ChatGroq(model_name=\"gemma2-9b-it\", api_key=userdata.get(\"GROQ_API_KEY\"))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 715}, "id": "PYKTm4m8fnDZ", "executionInfo": {"status": "ok", "timestamp": 1752301033721, "user_tz": -330, "elapsed": 764212, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}, "outputId": "f5bf70ac-04bc-47f7-ba66-89ef6ca5f011"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/gradio/chat_interface.py:339: User<PERSON>arning: The 'tuples' format for chatbot messages is deprecated and will be removed in a future version of Gradio. Please set type='messages' instead, which uses openai-style 'role' and 'content' keys.\n", "  self.chatbot = Chatbot(\n"]}, {"output_type": "stream", "name": "stdout", "text": ["It looks like you are running Gradio on a hosted Jupyter notebook, which requires `share=True`. Automatically setting `share=True` (you can turn this off by setting `share=False` in `launch()` explicitly).\n", "\n", "Colab notebook detected. This cell will run indefinitely so that you can see errors and logs. To turn off, set debug=False in launch().\n", "* Running on public URL: https://02eb36c17138978dc6.gradio.live\n", "\n", "This share link expires in 1 week. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"https://02eb36c17138978dc6.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Keyboard interruption in main thread... closing server.\n", "Killing tunnel 127.0.0.1:7860 <> https://02eb36c17138978dc6.gradio.live\n"]}, {"output_type": "execute_result", "data": {"text/plain": []}, "metadata": {}, "execution_count": 3}], "source": ["import gradio as gr\n", "def chat(message, history):\n", "    return llm_groq.invoke(message).content\n", "\n", "demo = gr.<PERSON><PERSON>(\n", "    fn=chat,\n", "    title=\"Simple Chatbot\",\n", "    description=\"This is a chatbot built as part of Modern AI Pro Essentials program\",\n", ")\n", "demo.launch(debug=True)"]}], "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyN3L/K3ub74OVTtZgtG3gMG"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}