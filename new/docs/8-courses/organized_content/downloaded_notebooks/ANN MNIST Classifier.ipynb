{"cells": [{"cell_type": "code", "source": ["%matplotlib inline\n", "# required to get plots on the <PERSON><PERSON><PERSON> notebook"], "metadata": {"id": "FOk_IAR_0S4s"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# **A friendly introduction to Deep Learning with Keras and TensorFlow**\n", "\n", "**Resources: [<PERSON> (UT Southwestern Medical Center)](https://github.com/AviatorMoser),[<PERSON>](https://github.com/wxs/keras-mnist-tutorial), [<PERSON><PERSON>](https://github.com/yashk2810/MNIST-Keras)**\n", "\n", "**Adapted by**: <PERSON> (Ex.Invento- IIT Madras) <br>\n", "![image.png](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEIAAABWCAYAAAB7E0BlAAAYcGlDQ1BJQ0MgUHJvZmlsZQAAWIWVWQc8le3fv+6zz7HPsffeZO+9994kHOtYccxQIskqUSFKJZmVSqESkYZSRg9JEsmoFCoqI+9t1PO8z//9vO/nvT6f676+53f9rt+61v07NwBcnb6RkWEIRgDCI2KoDqYG/G7uHvzYaYAC7IAeyANlX3J0pL6dnRWAy+/2v5elQQBttM9lNmT9Z///Woj+AdFkACAvGPv5R5PDYXwXAFQaOZIaAwDGCKYLxcdEbuBgGDNTYQNhnLyBg7bw0Q3st4UrNnmcHAxh3AwAjtbXlxoEAH0nTOePIwfBcujfwX3ECH9KBMz6A8Y65GBffwC41GAe6fDw3RsYrkAc5o+EcS6M1fz+ITPov8n3+yPf1zfoD97ya7PgjCjRkWG+e/6fofm/S3hY7G8donClDaaaOWz4D8dwKHS35QamhfFshJ+N7UasYfyD4r8VdwAQhOBYM+ctfgQ3OdoQjh9ghbGcv6+RJYy5YWwSEWZjtU33C6SYmMMYXi2IBEqMudP22MyAaGPHbZmnqLsdbH/jQKqh/vbYy77UTb0b/J2xoc762/KHggPMf8v/lhjs5ApjAgBIQhzFxQbG9DBmjg51tNziQQomBhva/Oahxjps2C8MY7WACFODLflIr0CqicM2f2R49G9/kRnBFHObbVwcE+xkthUfZC3Zd9N+dhg3BkToO/+WExDtZvXbF/8AI+Mt35HPAiKct/1FjkbGGDhsj52PDLPb5kfhAsJMN+iCMOaMjnPcHovSioEX55Z8lFVkjJ3Tlp0onxBfC7ste1BxwAoYAiPAD2Lh6gd2gxBAeTbbNAv/2uoxAb6ACoJAAJDZpvwe4brZEwE/HUEi+ASjABD9Z5zBZm8AiIPpa3+oW08ZELjZG7c5IhRMwTgcWIIw+Hfs5qiIP9pcwDuYQvkP7WTY1jC4bvT9J00fplhtU2J/y+Vn+M2JMcYYYcwwJhgJFCdKB6WJsoKfenBVQKmh1H9b+zc/egrdh36LHkCPoV/uoqRR/2WLNRiD5Ztse+z3T49RorBMZZQBShuWDktGsaI4gQxKCdajj9KFNSvDVMNtuzd85/8f/PzjwT9ivs2Hl8Mj8Gx4Pbz4v0fSS9Ir/5GyEdF/xmfLVr8/UTX80/Nv/Yb/iLM/3Fr+mxOZiWxAPkS2I7uQLcgmwI9sQzYju5F3NvCfNfRucw391uawaU8oLIfyH/p8t3VuRDJark7uvdzqdh+ICUiI2dhghrsj91ApQcEx/PrwLRDAbx5BlpXmV5BTUARg407ZOqa+OmzeFRBrz9808kEAVOcBwC//TQv/CsAVeI/zW/9NE/GGtxkGgOopciw1bouG2nig4dOAAd5RHIAXCAFx2CMFoAI0gR4wBhbAFjgBd+ANxzkYXs9UEA+SQSrIADngKDgBSsAZcB5Ug0vgGmgCLaAdPABPQC8YAK/g9TMJPoJ5sARWIAjCQnQQCeKA+CARSApSgNQgHcgYsoIcIHfIBwqCIqBYKBk6AOVABVAJdA6qga5CN6F2qAvqg15C49B76Au0jEAiaBHMCB6EKGIHQg2hj7BEOCF2IoIQUYhERDriCKIYUY64iGhEtCOeIAYQY4iPiEUkQNIgWZECSBmkGtIQaYv0QAYiqch9yGxkIbIceRl5C57p58gx5CzyJwqDIqH4UTLwGjZDOaPIqCjUPlQuqgRVjWpEdaKeo8ZR86hfaDo0N1oKrYE2R7uhg9Dx6Ax0IboSfQN9H95Nk+glDAbDihHDqMK70R0TgknC5GJOY+oxdzF9mAnMIhaL5cBKYbWxtlhfbAw2A3sSexHbhu3HTmJ/4GhwfDgFnAnOAxeBS8MV4mpxrbh+3DRuBc+IF8Fr4G3x/vg9+Dx8Bf4Wvgc/iV8hMBHECNoEJ0IIIZVQTLhMuE8YIXyloaERpFGnsaeh0OynKaa5QvOIZpzmJy2RVpLWkNaLNpb2CG0V7V3al7Rf6ejoROn06DzoYuiO0NXQ3aMbpftBT6KXpTen96dPoS+lb6Tvp//MgGcQYdBn8GZIZChkaGDoYZhlxDOKMhoy+jLuYyxlvMn4gnGRicQkz2TLFM6Uy1TL1MU0Q8QSRYnGRH9iOvE88R5xgoQkCZEMSWTSAVIF6T5pkhnDLMZszhzCnMN8ifkZ8zwLkUWJxYUlgaWU5Q7LGCuSVZTVnDWMNY/1Gusg6zIbD5s+WwBbFttltn627+xc7HrsAezZ7PXsA+zLHPwcxhyhHPkcTRyvOVGckpz2nPGcZZz3OWe5mLk0uchc2VzXuIa5EdyS3A7cSdznubu5F3l4eUx5InlO8tzjmeVl5dXjDeE9ztvK+56PxKfDR+E7ztfG94GfhV+fP4y/mL+Tf16AW8BMIFbgnMAzgRVBMUFnwTTBesHXQgQhNaFAoeNCHULzwnzC1sLJwnXCwyJ4ETWRYJEikYci30XFRF1FD4k2ic6IsYuZiyWK1YmNiNOJ64pHiZeL/yWBkVCTCJU4LdEriZBUlgyWLJXskUJIqUhRpE5L9UmjpdWlI6TLpV/I0Mroy8TJ1MmMy7LKWsmmyTbJft4hvMNjR/6Ohzt+ySnLhclVyL2SJ8pbyKfJ35L/oiCpQFYoVfhLkU7RRDFFsVlxQUlKKUCpTGlImaRsrXxIuUN5TUVVhapyWeW9qrCqj+op1RdqzGp2arlqj9TR6gbqKeot6j81VDRiNK5pzGnKaIZq1mrOaIlpBWhVaE1oC2r7ap/THtPh1/HROaszpiug66tbrvtWT0jPX69Sb1pfQj9E/6L+ZwM5A6rBDYPvhhqGew3vGiGNTI2yjZ4ZE42djUuMR00ETYJM6kzmTZVNk0zvmqHNLM3yzV6Y85iTzWvM5y1ULfZadFrSWjpalli+tZK0olrdskZYW1gfsx6xEbGJsGmyBbbmtsdsX9uJ2UXZ3bbH2NvZl9pPOcg7JDs8dCQ57nKsdVxyMnDKc3rlLO4c69zhwuDi5VLj8t3VyLXAdcxth9tetyfunO4U92YPrIeLR6XHoqex5wnPSS9lrwyvwZ1iOxN2dnlzeod539nFsMt3V4MP2sfVp9Zn1dfWt9x30c/c75TfPNmQXET+6K/nf9z/fYB2QEHAdKB2YEHgTJB20LGg98G6wYXBsxRDSgllIcQs5EzI91Db0KrQ9TDXsPpwXLhP+M0IYkRoROdu3t0Ju/sipSIzIseiNKJORM1TLamV0VD0zujmGGb45b07Vjz2YOx4nE5cadyPeJf4hgSmhIiE7j2Se7L2TCeaJF5IQiWRkzqSBZJTk8f36u89tw/a57evI0UoJT1lcr/p/upUQmpo6tM0ubSCtG8HXA/cSudJ358+cdD0YF0GfQY148UhzUNnMlGZlMxnWYpZJ7N+ZftnP86RyynMWc0l5z4+LH+4+PD6kcAjz/JU8sqOYo5GHB3M182vLmAqSCyYOGZ9rPE4//Hs499O7DrRVahUeKaIUBRbNFZsVdx8Uvjk0ZOrJcElA6UGpfWnuE9lnfp+2v90f5le2eUzPGdyziyfpZwdOmd6rrFctLzwPOZ83PmpCpeKhxfULtRUclbmVK5VRVSNVTtUd9ao1tTUctfm1SHqYuveX/S62HvJ6FLzZZnL5+pZ63OugCuxVz5c9bk6eM3yWkeDWsPl6yLXT90g3chuhBr3NM43BTeNNbs39920uNlxS/PWjduyt6taBFpK77DcyWsltKa3rrclti3ejbw72x7UPtGxq+PVPbd7f3Xadz67b3n/0QOTB/ce6j9se6T9qKVLo+vmY7XHTU9UnjR2K3ffeKr89MYzlWeNPao9zb3qvbf6tPpa+3X7258bPX/wl/lfTwZsBvoGnQeHXni9GBvyH5p5GfZyYThueOXV/hH0SPZrxteFo9yj5W8k3tSPqYzdGTca737r+PbVBHni47vod6uT6VN0U4XTfNM1MwozLe9N3vd+8Pww+THy48psxiemT6c+i3++Pqc31z3vNj+5QF1Y/5L7leNr1Telbx2LdoujS+FLK9+zf3D8qP6p9vPhsuvy9Er8Kna1eE1i7dYvy18j6+Hr65G+VN/NVwEkXBGBgQB8qQKAzh0AUi+cJnhu5XzbBQm/fCDg1gWShT4i0uEbtQeVgTbBIDFPsMW4CLwVQYIGSzNL20/XRF/FUMlYz9RM7CA9Ye5lGWJ9wzbD/pFjgXOZa40HwYvlI/DTCRAFiUKswuwibKLsYtziPBL8kvxSgtLCMqKyYjuk5eTkFRVUFDWUdJWNVcxVzdVM1E00TDQNtfS1tXQ0dJX0ZPVFDXgMmY0IRuvGX02mTF+adZu3WFRbHrNKsQ6xcbM1tlO2F3PgcmR0wjkjXSBXhBvKHe/B6MnhJbxTxltil7APny+nHwuZ5E8MIAWyBnEFC1KkQ1RDTcJcwikRybsLIiuizlKLo/NjcmOz4rLjjyQU76lObE16tRfsk07Ztf9k6qsDgum7D7YfwmQKZSlkG+Q45gYeTjySn1d99G7+cMHicaYTMoUWRYHFB06Wldws7T/17vTiGexZjnOS5VrnbSv8LsRUHqwqrK6uuVn7uG744odLP+txV9iuil/TbXC/HnUjq/F0U31z282uWz23e1ue3OlovdpWejelfVeHxj3ivanOm/drH5x6mPMoocvvsfkT2W767tmn95+d6onsNegj9U30X3ue+pf9gMggavD9i+6h+pcFwzGvXEbUXnO+Xh0dfdM+dmE86+********************************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)"], "metadata": {"id": "sR_bdVsqsfpL"}}, {"cell_type": "markdown", "metadata": {"id": "EY4oAj08ovz_"}, "source": ["\n", "# **Dense Fully Connected Network(DFCN)**\n"]}, {"cell_type": "markdown", "metadata": {"id": "goVXuQLBov0B"}, "source": ["A beginner's introduction to the fundamentals of deep learning, this demo will walk through the basic steps of building **a toy model** for **classifying handwritten numbers** with accuracies surpassing **95%**. This first model will be a basic **fully-connected** neural network. In a future notebook we will be seeing a deeper network that introduces the concepts of **convolution** and pooling that performs significantly better at this task."]}, {"cell_type": "markdown", "metadata": {"id": "WJTFQ1TEov0C"}, "source": ["## **Our task for the AI**\n", "\n", "*  What we have? *A large corpus of handwritten images and their corresponding labels*\n", "*  Our Goal? *Make an AI to automatically learn the patterns and features from the data so that it will be capabale of correctly labelling future handwritten images*\n", "*  Dataset source: *Handwritten digits from the MNIST database. 60k Training and 10K test images.*\n", "\n", "We will use the **Keras Python API** with TensorFlow as the backend.\n", "\n", "**Why <PERSON><PERSON>?** *Keras is the most easy to use the library for machine learning for beginners. Being simple helps it to bring machine learning from imaginations to reality. It provides an infrastructure that can be learned in very less time. Using Keras, you will be able to stack layers like experts.*"]}, {"cell_type": "markdown", "metadata": {"id": "-HSEekYCov0C"}, "source": ["<img src=\"https://github.com/AviatorMoser/keras-mnist-tutorial/blob/master/mnist.png?raw=1\" >"]}, {"cell_type": "markdown", "metadata": {"id": "Xvb1F4Yrov0D"}, "source": ["## **Importing the Necessary Python Modules**\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JjotXrD6ov0D"}, "outputs": [], "source": ["import numpy as np                   # Optimized scientific computing library\n", "import matplotlib.pyplot as plt      # For plotting\n", "import random                        # for generating random numbers\n", "from keras import models             # used for loading the saved model\n", "\n", "from keras.datasets import mnist     # importing the dataset\n", "from keras.models import Sequential  # Model type to be used\n", "\n", "from keras.layers import Dense, Dropout, Activation # Types of layers to be used in our model\n", "from keras import utils as np_utils                         # NumPy related tools"]}, {"cell_type": "markdown", "metadata": {"id": "s6xsB9L1ov0E"}, "source": ["## **Loading Training Data**\n", "\n", "The MNIST dataset is conveniently bundled within Keras, and we can easily analyze some of its features in Python."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "flMgYB25ov0F", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1663156156264, "user_tz": -330, "elapsed": 617, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "27f4b3c2-0f69-4dce-ee6f-bf1239d495d2"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Downloading data from https://storage.googleapis.com/tensorflow/tf-keras-datasets/mnist.npz\n", "11493376/11490434 [==============================] - 0s 0us/step\n", "11501568/11490434 [==============================] - 0s 0us/step\n", "Printing the shapes for validation. Format for shape : (n_elements, n_rows, n_cols)\n", "X_train :  (60000, 28, 28), y_train :  (60000,)\n", "X_test  :  (10000, 28, 28), y_test  :  (10000,)\n"]}], "source": ["# The MNIST data is split between 60,000 28 x 28 pixel training images and 10,000 28 x 28 pixel images\n", "train_data, test_data = mnist.load_data()\n", "X_train, y_train = train_data\n", "X_test, y_test = test_data\n", "\n", "print(\"Printing the shapes for validation. Format for shape : (n_elements, n_rows, n_cols)\")\n", "print(f\"X_train :  {X_train.shape}, y_train :  {y_train.shape}\")\n", "print(f\"X_test  :  {X_test.shape}, y_test  :  {y_test.shape}\")"]}, {"cell_type": "markdown", "metadata": {"id": "DmY3hJ8Cov0F"}, "source": ["**Let us plot some sample images from the training set, using Matplotlib.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "f6Xb1VCwov0G", "colab": {"base_uri": "https://localhost:8080/", "height": 657}, "executionInfo": {"status": "ok", "timestamp": 1663156163547, "user_tz": -330, "elapsed": 2845, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "1b3819a1-f705-46a3-eea6-ffb02552fad0"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 648x648 with 9 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}], "source": ["plt.rcParams['figure.figsize'] = (9,9) # Changing these values to Make the figures a bit bigger (\"rc\" params are like configuration parameters)\n", "\n", "#Plotting some 9 samples in a 3x3 grid for data visualization\n", "for i in range(9):\n", "    plt.subplot(3,3,i+1)\n", "    index = random.randint(0, len(X_train)) # random image index in the valid range\n", "    plt.imshow(X_train[index], cmap='gray')  # color map -> gray as it is a black and white image\n", "    plt.title(f\"Class Label: {y_train[index]}\")\n", "\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {"id": "vYOQ7Xdoov0G"}, "source": ["Now that we have visualized how the training samples look like,\n", "\n", "### **Let's examine a single digit a little closer, and print out the array representing the digit.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "RijN3ogtov0G", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1663156163548, "user_tz": -330, "elapsed": 8, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "560d7f77-d4c6-4845-e36b-0f3bc4045ff1"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0 102 102  41   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0 132 253 254 253 254 253 255 253  62   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0  41 213 252 253 252 253 252 253 252 223 122   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0  31 132 123 123 254 253 254 253 203 203 214 253 255 192   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0 152 252 141 101 131  50  50  50   0   0  10  50 112  91   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0  51 193 254 131   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0  41 233 252 253 212 183 102  41   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0 254 253 254 253 254 253 254 172   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0  50 212 253 252 253 252 253 252 123   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0 102 102 102 142 234 253 163   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0 152 252 203   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0  11 132  41   0   0   0   0   0   0  41 254 253 123   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0 132 252 223  20   0   0   0   0  21 183 253 252 122   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0 193 253 254 253  92  51 152 152 254 253 254 213  41   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0  92 232 253 252 253 252 253 252 253 252 253 130   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0  41 214 253 255 253 254 253 254 233 102  20   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0  31 192 253 212 151 111  50  30   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]]\n"]}], "source": ["np.set_printoptions(linewidth=120)  # Making line width large so that the entire first row is printed as one row!\n", "\n", "print(np.matrix(X_train[index]))    # Let us examine the pixel values of the last number from above"]}, {"cell_type": "markdown", "metadata": {"id": "Y8Zv8aPtov0H"}, "source": ["### **Comments:**\n", "* This array is what your computer receives and operates with.\n", "\n", "* If we follow the non-zero pixels we can see the number!\n", "\n", "* This is a black and white image. Here the pixel values correspond to light * intensities. Each pixel is an 8-bit integer from 0-255. 0 is full black, while  255 is full white.\n", "\n", "\n", "* This is what we call a single-channel pixel. It's called monochrome.\n", "\n", "* *Fun-fact! Your computer screen has three channels for each pixel: red, green, blue. Each of these channels also takes an 8-bit integer. 3 channels -- 24 bits total -- 16,777,216 possible colors!*"]}, {"cell_type": "markdown", "metadata": {"id": "94Apikgpov0H"}, "source": ["## **Formatting the input data layer**\n", "\n", "* Instead of a 28 x 28 matrix, we build our network to accept a 784-length vector.\n", "\n", "* Each image needs to be then reshaped (or flattened) into a vector. We'll also normalize the inputs to be in the range [0-1] rather than [0-255]. Normalizing inputs is generally recommended, so that any additional dimensions (for other network architectures) are of the same scale.\n", "\n", "**Note** : *By flattening the the matrix, we loose the connectivity information of the input pixels. i.e: The model has no idea that the pixels at (1,1) and (2,1) are adjacent pixels(vertically connected). The model we build here, makes no use of the information that adjacent pixels (horizontal/vertical/diagonal/across any direction) are similar and contain a piece of informaiton if grouped together. Keep this fact at the back of your mind, we will come back to this, when we solve the same problem using* **Convolutional Neural Networks**!"]}, {"cell_type": "markdown", "metadata": {"id": "RkVd9Svfov0H"}, "source": ["<img src='https://github.com/AviatorMoser/keras-mnist-tutorial/blob/master/flatten.png?raw=1' >"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xgIb_xFIov0I", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1663156170562, "user_tz": -330, "elapsed": 399, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "68f19ad8-8ca9-4949-b70b-68b9dbe0c3f0"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Training matrix shape now:  (60000, 784)\n", "Testing matrix shape now: (10000, 784)\n"]}], "source": ["X_train = X_train.reshape(60000, 784) # reshape 60,000 28 x 28 matrices into 60,000 784-length vectors.\n", "X_test = X_test.reshape(10000, 784)   # reshape 10,000 28 x 28 matrices into 10,000 784-length vectors.\n", "\n", "X_train = X_train.astype('float32')   # change integers to 32-bit floating point numbers\n", "X_test = X_test.astype('float32')\n", "\n", "X_train /= 255                        # normalize each value for each pixel for the entire vector for each input\n", "X_test /= 255\n", "\n", "print(f\"Training matrix shape now:  {X_train.shape}\")\n", "print(f\"Testing matrix shape now: {X_test.shape}\")"]}, {"cell_type": "markdown", "metadata": {"id": "bRziX-pJov0I"}, "source": ["**We then modify our classes (unique digits) to be in the one-hot format, i.e.**\n", "\n", "```\n", "0 -> [1, 0, 0, 0, 0, 0, 0, 0, 0]\n", "1 -> [0, 1, 0, 0, 0, 0, 0, 0, 0]\n", "2 -> [0, 0, 1, 0, 0, 0, 0, 0, 0]\n", "etc.\n", "```\n", "\n", "**If the final output of our network is very close to one of these classes, then it is most likely that class. For example, if the final output is:**\n", "\n", "```\n", "[0, 0.94, 0, 0, 0, 0, 0.06, 0, 0]\n", "```\n", "**then it is most probable that the image is that of the digit `1`**.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "3U7heW-Fov0I"}, "outputs": [], "source": ["# Converting the given y-labels to ONE HOT ENCODED VECTORS form\n", "n_classes = 10 # number of unique digits/classes\n", "\n", "Y_train = np_utils.to_categorical(y_train, n_classes)\n", "Y_test = np_utils.to_categorical(y_test, n_classes)"]}, {"cell_type": "markdown", "metadata": {"id": "ZChS57efov0I"}, "source": ["## **Building a 3-layer fully connected network (FCN)**\n", "\n", "<img src=\"https://github.com/AviatorMoser/keras-mnist-tutorial/blob/master/figure.png?raw=1\" />"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "zve7enOkov0J"}, "outputs": [], "source": ["# The Sequential model is a linear stack of layers and is very common.\n", "model = Sequential()"]}, {"cell_type": "markdown", "metadata": {"id": "fVWbZ606ov0J"}, "source": ["### **The first hidden layer**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "zmBnNM5Lov0J"}, "outputs": [], "source": ["# The first hidden layer is a set of 512 nodes (artificial neurons).\n", "# Each node will receive an element from each input vector and apply some weight and bias to it.\n", "\n", "model.add(Dense(512, input_shape=(784,))) #(784,) is not a typo -- that represents a 784 length vector!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "l-WhCBvyov0K"}, "outputs": [], "source": ["# An \"activation\" is a non-linear function applied to the output of the layer above.\n", "# It checks the new value of the node, and decides whether that artifical neuron has fired.\n", "# The Rectified Linear Unit (ReLU) converts all negative inputs to nodes in the next layer to be zero.\n", "# Those inputs are then not considered to be fired.\n", "# Positive values of a node are unchanged.\n", "\n", "model.add(Activation('relu'))\n", "# It is this RELU FUNCTION that brings in non-linearity to the network, which then unlocks the model to be able to generalize to literally any function,\n", "# given a large enough network!"]}, {"cell_type": "markdown", "metadata": {"id": "9tyOupm9ov0K"}, "source": ["$$f(x) = max (0,x)$$\n", "<img src = 'https://github.com/AviatorMoser/keras-mnist-tutorial/blob/master/relu.jpg?raw=1' >"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "ncr9HGU_ov0L"}, "outputs": [], "source": ["# Dropout zeroes a selection of random outputs (i.e., disables their activation)\n", "# Dropout helps protect the model from memorizing or \"overfitting\" the training data.\n", "model.add(Dropout(0.2))"]}, {"cell_type": "markdown", "source": ["<img src = \"https://d3i71xaburhd42.cloudfront.net/34f25a8704614163c4095b3ee2fc969b60de4698/2-Figure1-1.png\" >\n", "\n", "#### **Intuition:**\n", "If some of the nodes are randomly not available to the model for making its prediction during training phase, it means that the other nodes are forced to be able to make the right prediction, even in the absence of the missing nodes' information. This means, the information is forced to be uniformly spread across all the nodes of the network, and the neural network cannot cheat by using the information contained in one or two nodes alone for making the predictions."], "metadata": {"id": "sd-h2zU9V8BW"}}, {"cell_type": "markdown", "metadata": {"id": "IgcHvR3Nov0L"}, "source": ["### **Adding the second hidden layer**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "GX2IjnDRov0L"}, "outputs": [], "source": ["# The second hidden layer appears identical to our first layer.\n", "# However, instead of each of the 512-node receiving 784-inputs from the input image data,\n", "# they receive 512 inputs from the output of the first 512-node layer.\n", "\n", "model.add(<PERSON><PERSON>(512))\n", "model.add(Activation('relu'))\n", "model.add(Dropout(0.2))"]}, {"cell_type": "markdown", "metadata": {"id": "JEfJqe-Tov0L"}, "source": ["### **The Final Output Layer**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "SwiCshfzov0L"}, "outputs": [], "source": ["# The final layer of 10 neurons in fully-connected to the previous 512-node layer.\n", "# The final layer of a FCN should be equal to the number of desired classes (10 in this case).\n", "model.add(<PERSON><PERSON>(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "6JEiJ-xRov0L"}, "outputs": [], "source": ["# The \"softmax\" activation represents a probability distribution over K different possible outcomes.\n", "# Its values are all non-negative and sum to 1.\n", "\n", "model.add(Activation('softmax'))"]}, {"cell_type": "markdown", "source": ["\n", "\n", "<img src = \"https://www.researchgate.net/profile/<PERSON><PERSON>-<PERSON>-2/publication/349662206/figure/fig3/AS:995882686246913@1614448343589/Working-principles-of-softmax-function.jpg\" >\n", "\n", "**Intuition**:\n", "It is really a neat way of converting The activation values of Output layer into Probabilities of the image belonging to each of the classes!"], "metadata": {"id": "kovH8ifJXVw1"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ttn4IUrjov0L", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1663156181234, "user_tz": -330, "elapsed": 398, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "56af703d-671e-4785-fb71-e80153625688"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model: \"sequential\"\n", "_________________________________________________________________\n", " Layer (type)                Output Shape              Param #   \n", "=================================================================\n", " dense (Dense)               (None, 512)               401920    \n", "                                                                 \n", " activation (Activation)     (None, 512)               0         \n", "                                                                 \n", " dropout (Dropout)           (None, 512)               0         \n", "                                                                 \n", " dense_1 (<PERSON><PERSON>)             (None, 512)               262656    \n", "                                                                 \n", " activation_1 (Activation)   (None, 512)               0         \n", "                                                                 \n", " dropout_1 (Dropout)         (None, 512)               0         \n", "                                                                 \n", " dense_2 (<PERSON><PERSON>)             (None, 10)                5130      \n", "                                                                 \n", " activation_2 (Activation)   (None, 10)                0         \n", "                                                                 \n", "=================================================================\n", "Total params: 669,706\n", "Trainable params: 669,706\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["# Summarize the built model\n", "\n", "model.summary()"]}, {"cell_type": "markdown", "metadata": {"id": "XhUq6Nm6ov0M"}, "source": ["### **Compiling the model**\n", "\n", "* Keras is built on top of Theano and TensorFlow. Both packages allow you to define a *computation graph* in Python, which then compiles and runs efficiently on the CPU or GPU without the overhead of the Python interpreter.\n", "\n", "* When compiing a model, <PERSON><PERSON> asks you to specify your **loss function** and your **optimizer**. The loss function we'll use here is called *categorical cross-entropy*, and is a loss function well-suited to comparing two probability distributions.\n", "\n", "* Our predictions are probability distributions across the ten different digits (e.g. \"we're 80% confident this image is a 3, 10% sure it's an 8, 5% it's a 2, etc.\"), and the target is a probability distribution with 100% for the correct category, and 0 for everything else. The cross-entropy is a measure of how different your predicted distribution is from the target distribution. [More detail at Wikipedia](https://en.wikipedia.org/wiki/Cross_entropy)\n", "\n", "* The optimizer helps determine how quickly the model learns through **gradient descent**. The rate at which descends a gradient is called the **learning rate**."]}, {"cell_type": "markdown", "metadata": {"id": "Wd5yXIL-ov0M"}, "source": ["<img src = \"https://mlfromscratch.com/content/images/2019/12/gradient-descent-optimized--1-.gif\" >\n", "This is a beautiful illustration of gradient descent.\n", "Have a close look at this awesome GIF.\n", "\n", "This is literally where all the **MAGIC** of **learning patterns** from **data** happens!\n", "\n", "What **gradient descent** does is, find the direction in which each of the parameters need to be tweaked, so that, the loss will become lesser in the next iteration!\n", "\n", "*Put very bluntly, Gradient descent is when there is an error between the expected value and the predicted value,\n", "Each parameter, interacts with the model's loss value manually, and try to improve them for the next iteration!*\n", "\n", "**Parameter**:  \"Ohh, Sorry, for the error/mistake, may I know what changes I need to make to myself, so that I can hopefully reduce the error, the next time around?\"\n", "\n", "\n", "**Model**: Sure, if you slightly increase/decrease your value, it might reduce the total Loss of our company.\n", "\n", "\n", "**Parameter**: Thanks, will work on it, and let's see how it goes, next time!\n", "\n", "If all the parameters do this at the same time, and they do it in appropriate step sizes, for long enough, everything will Converge to the Global Minimum. One more nuance is that They change in proportion to their gradient value. i.e: If some parameter has a large impact on the Change in loss value, it will be changed more at this time-step, so that we are leveraging its ability to reduce the loss faster.   \n", "\n", "* **But first**, our main task is make sure that the **LOSS FUNCTION** actually corresponds to some scalar that is a measure of **how well/bad** our **model** is **performing** at the task we intend it to perform. In this case, our loss function captures, how confidently our model is able to predict the right numbers, which is the behaviour we incentivise our Model by rewarding it when it is good. And punishing with heavy fines when the loss is bad!\n", "\n", "* So, if we are able to automatically incentivise(define it once, sit back, relax!) Good behaviour and automatically punish bad behaviour using appropriate metrics (**LOSS FUNCTIONS**) that are **true** to the **essence** of **\"what We want to achieve**\", then\n", "\n", "* **Just providing correctly labelled data is enough for the model to automatically learn how to get Good at that particular task**, by trying to predict the labels of the training data with randomly initialized weights ->  failing -> automatically tweaking it's weights to be better suited to do it in the next iteration, using Gradient Descent!\n", "*  Eventually, successfully learning the underlying features of the data that we want it to learn!\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "nhqzfOjtov0M"}, "source": ["<img src = \"https://www.jeremyjordan.me/content/images/2018/02/Screen-Shot-2018-02-24-at-11.47.09-AM.png\" >"]}, {"cell_type": "markdown", "metadata": {"id": "ab9wkUT-ov0M"}, "source": ["***So are smaller learning rates better? Not quite! It's important for an optimizer not to get stuck in local minima while neglecting the global minimum of the loss function. Sometimes that means trying a larger learning rate to jump out of a local minimum.***"]}, {"cell_type": "markdown", "metadata": {"id": "-TGh1hPoov0M"}, "source": ["<img src = \"https://github.com/AviatorMoser/keras-mnist-tutorial/blob/master/complicated_loss_function.png?raw=1\" >"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "FYUWmKDAov0M"}, "outputs": [], "source": ["# Let's use the Adam optimizer for learning\n", "model.compile(loss='categorical_crossentropy', optimizer='adam', metrics=['accuracy'])\n", "#If you want to understand how categorical Cross entropy works check out this cool video from StatQuest : https://youtu.be/6ArSys5qHAU\n"]}, {"cell_type": "markdown", "source": ["#### **Comments:**\n", "* We use **Adam optimizer** for tuning weights, The results of the <PERSON> optimizer are generally better than every other optimization algorithms, have faster computation time, and require fewer parameters for tuning.\n", "\n", "* **Categorical Cross Entropy**: Non linearly penalizes Low Confidence on the correct label, with a large loss, and has very low loss if the confidence is close to 1.\n", "\n", "* The tuning of weights happen based on the Gradients of the loss function and not the accuracy! This is because, the accuracy(highest probability class) remains the same if the confidence of the model in predicting a class is 60% or 90%. But losses, depend on the how confident the model is able to classify a particular image to its own class! So, we have the Loss function and the weight updation happening based on the values of the loss function. This means, the model is asked to learn features that enable it to classify images to its own class as confidently as possible!\n"], "metadata": {"id": "vTOs8PaqxsHH"}}, {"cell_type": "markdown", "metadata": {"id": "jiZPEIK2ov0N"}, "source": ["## **Train the model!**\n", "*This is the fun part!*"]}, {"cell_type": "markdown", "metadata": {"id": "94_WxQdqov0N"}, "source": ["* The batch size determines over how much data per step is used to compute the loss function, gradients, and back propagation. Large batch sizes allow the network to complete it's training faster; however, there are other factors beyond training speed to consider.\n", "\n", "* Too large of a batch size smoothes the local minima of the loss function, causing the optimizer to settle in one because it thinks it found the global minimum.\n", "\n", "* Too small of a batch size creates a very noisy loss function, and the optimizer may never find the global minimum.\n", "\n", "* So a good batch size may take some trial and error to find!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "grYjmImKov0N", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1663156213077, "user_tz": -330, "elapsed": 21689, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "a4808b0e-a0df-4bf2-d09d-6f7647a552ce"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Epoch 1/5\n", "469/469 [==============================] - 5s 4ms/step - loss: 0.2515 - accuracy: 0.9239 - val_loss: 0.1050 - val_accuracy: 0.9666\n", "Epoch 2/5\n", "469/469 [==============================] - 2s 5ms/step - loss: 0.1005 - accuracy: 0.9686 - val_loss: 0.0859 - val_accuracy: 0.9722\n", "Epoch 3/5\n", "469/469 [==============================] - 3s 6ms/step - loss: 0.0727 - accuracy: 0.9767 - val_loss: 0.0818 - val_accuracy: 0.9737\n", "Epoch 4/5\n", "469/469 [==============================] - 2s 4ms/step - loss: 0.0568 - accuracy: 0.9815 - val_loss: 0.0670 - val_accuracy: 0.9790\n", "Epoch 5/5\n", "469/469 [==============================] - 2s 4ms/step - loss: 0.0452 - accuracy: 0.9852 - val_loss: 0.0710 - val_accuracy: 0.9801\n"]}], "source": ["# Now let's train the model\n", "history = model.fit(X_train, Y_train,validation_data=(X_test, Y_test),\n", "          batch_size=128, epochs=5, verbose=1)\n", "#EPOCH - In terms of artificial neural networks, an epoch refers to one cycle through the full training dataset. Usually, training a neural network takes more than a few epochs.\n", "#In other words, if we feed a neural network the training data for more than one epoch in different patterns, we hope for a better generalization when given a new \"unseen\" input (test data)."]}, {"cell_type": "markdown", "source": ["* The two numbers, in order, represent the value of the loss function of the network on the training set, and the overall accuracy of the network on the training data.\n", "\n", "* We see that The loss goes down and the accuracy improves with time.\n", "\n", "* But how does it do on data it did not train on?\n", "\n", "### **Let us plot the losses and see!**"], "metadata": {"id": "5MoBCg5KsxeA"}}, {"cell_type": "code", "source": ["# Function to plot loss\n", "def plot_loss(history):\n", "    plt.plot(history.history['loss'], label='loss')\n", "    plt.plot(history.history['val_loss'], label='val_loss')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('<PERSON><PERSON><PERSON> (Loss)')\n", "    plt.legend()\n", "    plt.grid(True)\n", "\n", "plot_loss(history)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 551}, "id": "doC2YA-Zh2V1", "executionInfo": {"status": "ok", "timestamp": 1663156213080, "user_tz": -330, "elapsed": 14, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "7ef34649-c252-4fa3-a091-86ef9a5fb1cc"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 648x648 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "metadata": {"id": "Xj7kve2Oov0N"}, "source": ["## **Evaluate Model's Accuracy on Test Data**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "wKHnSbtXov0N", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1663156214504, "user_tz": -330, "elapsed": 1430, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "f6ceec9d-3c54-4567-ea6c-11d89d73627b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["313/313 [==============================] - 1s 2ms/step - loss: 0.0710 - accuracy: 0.9801\n", "Test score: 0.07095347344875336\n", "Test accuracy: 0.9800999760627747\n"]}], "source": ["score = model.evaluate(X_test, Y_test)\n", "print('Test score:', score[0])\n", "print('Test accuracy:', score[1])"]}, {"cell_type": "markdown", "metadata": {"id": "HPBSWbU8ov0N"}, "source": ["### **Inspecting the output**\n", "\n", "It's always a good idea to inspect the output and make sure everything looks sane. Here we'll look at some examples it gets right, and some examples it gets wrong."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "OFYeFH1xov0N"}, "outputs": [], "source": ["# The predict_classes function outputs the highest probability class\n", "# according to the trained classifier for each input example.\n", "predicted_classes = np.argmax(model.predict(X_test), axis=-1) # we get probabilities for each of the class, and the argmax corresponds the class index with the highest probability\n", "\n", "# Check which items we got right / wrong\n", "correct_indices = np.nonzero(predicted_classes == y_test)[0]\n", "\n", "incorrect_indices = np.nonzero(predicted_classes != y_test)[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "S2eaZFInov0O", "colab": {"base_uri": "https://localhost:8080/", "height": 657}, "executionInfo": {"status": "ok", "timestamp": 1663156219806, "user_tz": -330, "elapsed": 1402, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "35ded92c-16a4-4b0c-83de-8e1efbb3fbf3"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 648x648 with 9 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}], "source": ["# Let us examine the Correctly labelled images\n", "plt.rcParams['figure.figsize'] = (9,9) # Change these values to Make the figures bigger/smaller\n", "plt.figure()\n", "for i, correct in enumerate(correct_indices[:9]):\n", "    plt.subplot(3,3,i+1)\n", "    plt.imshow(X_test[correct].reshape(28,28), cmap='gray', interpolation='none')\n", "    plt.title(f\"Predicted {predicted_classes[correct]}, Class {y_test[correct]}\")\n", "\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "source": ["**Let us exanine a few of incorrectly labelled images and think see if they the model's predictions are reasonable!**"], "metadata": {"id": "r1PqnNOvagjj"}}, {"cell_type": "code", "source": ["# Let us examine the InCorrectly  labelled images\n", "plt.figure()\n", "for i, incorrect in enumerate(incorrect_indices[:9]):\n", "    plt.subplot(3,3,i+1)\n", "    plt.imshow(X_test[incorrect].reshape(28,28), cmap='gray', interpolation='none')\n", "    plt.title(f\"Predicted {predicted_classes[incorrect]}, Class {y_test[incorrect]}\")\n", "\n", "plt.tight_layout()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 657}, "id": "sO69jzSWaNzj", "executionInfo": {"status": "ok", "timestamp": 1663156220718, "user_tz": -330, "elapsed": 921, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "39b8bb93-b75e-4422-f14e-e0cf836fcce8"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 648x648 with 9 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["**Some of these images are not clear enough, and we can have some Excuse for the model in misclassifying these!**"], "metadata": {"id": "MBOpYdANawpS"}}, {"cell_type": "markdown", "metadata": {"id": "TpV5MsOLov0O"}, "source": ["## **Trying experimenting with the batch size, Number of layers, number of nodes in each of the layers etc.!**\n", "\n", "* How does increasing the batch size to 10,000 affect the training time and test accuracy?\n", "\n", "* How about a batch size of 32?"]}, {"cell_type": "markdown", "source": ["## **Now that The model has been trained, we would like to save it, download it, load it and use it for future inferences.**"], "metadata": {"id": "fOhudF81iGYC"}}, {"cell_type": "code", "source": ["# Save model\n", "model.save(\"trained_model.h5\")\n", "\n", "# Load the saved model\n", "saved_model = models.load_model('trained_model.h5')\n"], "metadata": {"id": "fKmv0MvviZWS"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["predicted_classes = np.argmax(model.predict(X_test), axis=-1) # we get probabilities for each of the class, and the argmax corresponds the class index with the highest probability\n", "predicted_classes_from_saved = np.argmax(saved_model.predict(X_test), axis=-1)\n", "(predicted_classes == predicted_classes_from_saved).all()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ESilARHMjTQq", "executionInfo": {"status": "ok", "timestamp": 1663156221714, "user_tz": -330, "elapsed": 998, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "343a1c3a-fa77-494b-90f2-edfa2dca10ca"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["True"]}, "metadata": {}, "execution_count": 24}]}, {"cell_type": "markdown", "source": ["***We can see that the Saved model's predictions match the Original model's prediction, confirming that we have been able to properly save and retrieve our model and it it works identically to the model trained on Google Colab!***"], "metadata": {"id": "eoJ70nQo06pt"}}], "metadata": {"kernelspec": {"display_name": "Tensorflow (GPU)", "language": "python", "name": "py3.6-tfgpu"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.6"}, "colab": {"provenance": [{"file_id": "1H99BrhYqHrsPItTrNebarXEyO_lXz6J0", "timestamp": 1711776639802}, {"file_id": "1nC516I1Ea91yQ92QEJEl4RFpRJ5rviDB", "timestamp": 1663156054387}, {"file_id": "1MBXM2tXyQLh9mmm1ooAY74F6IyRYtFlV", "timestamp": 1649764909605}, {"file_id": "https://github.com/AviatorMoser/keras-mnist-tutorial/blob/master/MNIST%20in%20Keras.ipynb", "timestamp": 1649664369333}], "collapsed_sections": ["sd-h2zU9V8BW", "vTOs8PaqxsHH", "TpV5MsOLov0O"]}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}