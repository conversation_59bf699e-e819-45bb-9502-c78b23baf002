{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "Copy of Logistic_Regression.ipynb", "provenance": [{"file_id": "1fL0Pc0nwjJSUjqji79xVhS-xvDe5LAOb", "timestamp": 1649670113590}]}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "source": ["#@title\n", "from sklearn.metrics import confusion_matrix \n", "import seaborn as sb\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "\n", "def draw_graph(x_intercept=0, lim=1, x_line=False, loss=1):\n", "  x=[]\n", "  y=[]\n", "  i = 0\n", "  while(i<lim):\n", "    if(loss):\n", "      x.append(i)\n", "      y.append(-np.log(i))\n", "      i+=0.01  \n", "    else:\n", "      x.append(i)\n", "      y.append(-np.log(1-i))\n", "      i+=0.01\n", "  plt.plot(x,y)\n", "  if(x_line):\n", "    plt.axvline(x=x_intercept, color='Red')\n", "  axes = plt.gca()\n", "  x_vals = np.array(axes.get_xlim())\n", "  y_vals = 0 + 0 * x_vals\n", "  plt.plot(x_vals, y_vals, '--')\n", "  plt.xlabel('SIGMOID_RESULT')\n", "  plt.ylabel('LOG (RESULT)')\n", "  plt.show()"], "metadata": {"cellView": "form", "id": "b4gyHQucehoC", "executionInfo": {"status": "ok", "timestamp": 1649666990250, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}}, "execution_count": 3, "outputs": []}, {"cell_type": "markdown", "source": ["#Logistic regression:\n", "- Why Logistic regression?\n", "- The sigmoid function\n", "- Logistic regression loss function\n"], "metadata": {"id": "eTUG72CGkvJ2"}}, {"cell_type": "markdown", "source": ["Imagine you were given a task to classify a patient as either covid +ve or -ve based on their body temperature (A severely flawed attribute, but, should do fine for the example). "], "metadata": {"id": "1iM0GPZcSoXT"}}, {"cell_type": "markdown", "source": ["Let us try to plot a graph where the X-axis represents the temperature, and the Y-axis represents wether the result is either is +ve or -ve"], "metadata": {"id": "XpaGkGqDuesg"}}, {"cell_type": "code", "source": ["#@title\n", "x=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]\n", "y=[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1]\n", "\n", "plt.scatter(x, y)\n", "plt.xlabel('(TEMPERATURE - 98.5) * 10')\n", "plt.ylabel('NEGATIVE                                  POSITIVE')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 278}, "id": "A6gBWo7jnGdd", "executionInfo": {"status": "ok", "timestamp": 1649666993170, "user_tz": -330, "elapsed": 1470, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "16d02365-ae87-42d5-92fe-d7658063d6ec"}, "execution_count": 4, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["The classes can either be 0, or 1. Let us take 0.5 as our threshold value. Anything below 0.5 can be considered 0, and anything equal to or above 0.5 can be considered 1. \n", "\\\n", "\\\n", "Let us use Linear regression to FIT a line to the data."], "metadata": {"id": "eWIx6reinODL"}}, {"cell_type": "code", "source": ["#@title\n", "\n", "x=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 12, 13, 14, 15, 16]\n", "y=[0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1]\n", "\n", "plt.scatter(x, y)\n", "plt.xlabel('(TEMPERATURE - 98.5) * 10')\n", "plt.ylabel('NEGATIVE                                  POSITIVE')\n", "axes = plt.gca()\n", "x_vals = np.array(axes.get_xlim())\n", "y_vals = 0 + 0.06 * x_vals\n", "plt.plot(x_vals, y_vals, '--')\n", "y_vals = 0.5 + 0 * x_vals\n", "plt.plot(x_vals, y_vals, color='Orange')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 278}, "id": "wz9CuDRuhlLU", "executionInfo": {"status": "ok", "timestamp": 1649667039739, "user_tz": -330, "elapsed": 1132, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "5e3853e2-cd97-4614-f612-64c494a6e5de"}, "execution_count": 5, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["\n", "We see that the model performs reasonably well for classifying the data.(Anything above the ORANGE line is POSITIVE, anything below the ORANGE line is NEGATIVE)\n", "\\\n", "\\\n", "Now, let us assume that our dataset consisted of JUST one extra value representing a HIGH temperature."], "metadata": {"id": "9n_4jsFsJTXp"}}, {"cell_type": "code", "source": ["#@title\n", "\n", "x=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 12, 13, 14, 15, 16, 30]\n", "y=[0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]\n", "\n", "plt.scatter(x, y)\n", "plt.xlabel('(TEMPERATURE - 98.5) * 10')\n", "plt.ylabel('NEGATIVE                                  POSITIVE')\n", "axes = plt.gca()\n", "x_vals = np.array(axes.get_xlim())\n", "y_vals = 0 + 0.03 * x_vals\n", "plt.plot(x_vals, y_vals, '--')\n", "y_vals = 0.5 + 0 * x_vals\n", "plt.plot(x_vals, y_vals, color='Orange')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 278}, "id": "yhwYwtnRovTS", "executionInfo": {"status": "ok", "timestamp": 1649667094169, "user_tz": -330, "elapsed": 1218, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "5a7eec5a-9431-4e8f-b3d2-8802cafb4303"}, "execution_count": 6, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["We see that Linear regression is EASILY swayed by the data points. The slope of the line is drastically affected by this, which in turn shifts the threshold. The new line WRONGLY classifies MOST of the data points.\n", "\\\n", "\\\n", "Also, it is visible that the regression line stretches to infinity. Even though our labels can ONLY be either 0 or 1, we are not placing a cap on the values that the regression algorithm produces. \n", "\\\n", "\\\n", "For a temperature value beyond 30, you can imagine the regression output to be more than 1.0. Although you can argue that as long as the output is more than 0.5, it is always labeled as POSITIVE, it simply makes little sense to NOT limit the regression output.\n", "\\\n", "\\\n", "How would we solve these problems? Can we simply rely on the quality of data alone and proceed with Linear Regression? No. \n", "\\\n", "\\\n", "We need something better that can address the problems stated above.\n", "- An algorithm that isn't as sensitive to outlier data as Linear regression is\n", "- An algorithm that stretches ONLY between 0 - 1.\n", "\n", "**NOTE**: In binary classification problems, the POSITIVE class is usually labeled as 1.\n", "\n", "**Example**: If we are designing a model to detect heart patients, the patient with heart disease is labeled as 1. The patients without heart disease are labeled as 0.\n"], "metadata": {"id": "-vFLX6VxMl1C"}}, {"cell_type": "markdown", "source": ["**NOTE**: Think of 0-1 as the range of probabilty, Higher the probability score, more confident is the prediction. \n", "\\\n", "**Example**: For a particualr body temperature, if the corrsponding Y-axis value is 0.7, it means that the model is 70% sure that the paitent is COVID +ve"], "metadata": {"id": "NBsAFIdqonNp"}}, {"cell_type": "markdown", "source": ["# LOGISTIC REGRESSION !!\n", "Now, if we were going to use the logistic regression model to solve the above problem, it would look like this:"], "metadata": {"id": "SX4Ir5GIPuE6"}}, {"cell_type": "code", "source": ["#@title\n", "\n", "import math\n", "\n", "def sigmoid(x):\n", "    a = []\n", "    for item in x:\n", "        a.append(1/(1+math.exp(-item)))\n", "    return a\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "x=[-10, -9.5, -9, -8.6, -8, -7.4, -7, -6.6, -6, 3.5, 4, 4.5, 5, 6, 6.5, 7, 7.2, 7.5, 10]\n", "y=[0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]\n", "\n", "plt.scatter(x, y, color='Red')\n", "\n", "x = np.arange(-10., 10., 0.2)\n", "sig = sigmoid(x)\n", "plt.plot(x,sig)\n", "frame1 = plt.gca()\n", "frame1.axes.get_xaxis().set_ticks([])\n", "frame1.axes.get_yaxis().set_ticks([])\n", "plt.xlabel('TEMPRETURE')\n", "plt.ylabel('NEGATIVE                                  POSITIVE')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 262}, "cellView": "form", "id": "wTVQz9LYsE_a", "executionInfo": {"status": "ok", "timestamp": 1649636038344, "user_tz": 420, "elapsed": 352, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "00918871627688614982"}}, "outputId": "aae51b82-8fc6-439c-cd43-10cd0ff40861"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["It is evident that logistic regression solves both of our problems: \n", "- It provides good results even in the presence of outliers\n", "- The results of the model range from 0 to 1.\n", "\n", "The main question should be, how are we getting the curvy line?\n", "\\\n", "\\\n", "We use the Sigmoid function to squeeze the line to fit between 0 and 1. Imagine taking a long iron rod and bending the ends to face away from each other."], "metadata": {"id": "_CbwSwq3WSOl"}}, {"cell_type": "markdown", "source": ["![image.png](data:image/png;base64,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)"], "metadata": {"id": "e0Lsp3SP1319"}}, {"cell_type": "markdown", "source": ["What we have learned so far:\n", "- classification problems cannot be modeled accurately using good old Linear regression\n", "- Linear regression is HIGHLY SENSITIVE to outlier data. The slope of the line is easily swayed towards extremes.\n", "- Linear regression line extends from NEGATIVE INFINITY to POSITIVE INFINITY, while classification results vary only between 0 and 1.\n", "- In classification problems, POSITIVE OUTCOME (For which the model was designed) is usually labeled as 1.\n", "- Given the input data, the output of the classification model provides us with the probability of the outcome being 1. (between 0 to 1)\n", "- We can use Logistic Regression to model a classification problem. \n", "- Logistic regression uses the Sigmoid function, which ensures that the results range between 0-1 and that it isn't as sensitive to outliers."], "metadata": {"id": "Inv37AOFfUQ4"}}, {"cell_type": "markdown", "source": ["**VERY IMPORTANT NOTE**: \n", "\\\n", "The output of the logistic regression model **ALWAYS** gives us the confidence of the input sample being labeled 1.\n", "\\\n", "\\\n", "EXAMPLE: Let us consider the model for predicting COVID patients. For a patient with temperature x degrees, if the model outputs a result of 0.3\n", "\\\n", "It means that the MODEL is 30% sure that the patient is COVID +ve\n", "\\\n", "OR\n", "\\\n", "It can also be interpreted as the model being 70% sure that the patient is COVID -ve."], "metadata": {"id": "ciLgXOa9XWB8"}}, {"cell_type": "markdown", "source": ["Recall Linear regression. If we were to FIT a LINE to this data, what would the equation look like?\n", "\\\n", "\\\n", "Y = Slope * X + Intercept\n", "\\\n", "\\\n", "In the above equation, the parameters to be modified are the intercept and the slope. X represents the input data, which in this case is the temperature.\n", "\\\n", "\\\n", "More generally, it can be written as:\n", "\\\n", "\\\n", "Y = P1 + P2 * X\n", "\\\n", "\\\n", "Where P1 and P2 are the parameters to be tuned, and X is the input.\n", "\\\n", "\\\n", "Now, imagine that if I were to tell you that to better identify if a person is COVID +ve or not, I will also provide you with the immunity levels of the patient.\n", "\\\n", "\\\n", "Hence, the equation can be modified to:\n", "\\\n", "\\\n", "Y = P1 + P2 * X1 + P3 * X2\n", "\\\n", "\\\n", "X1 = tempreture\n", "\\\n", "X2 = immunity levels\n", "\\\n", "\\\n", "As the number of input parameters increases, so do the model parameters."], "metadata": {"id": "fm3DPgAWgol2"}}, {"cell_type": "markdown", "source": ["What we described above also applies to Logistic regression, but, unlike Linear Regression, we need to squeeze it to fit within 0 and 1.\n", "\\\n", "\\\n", "Imagine you lived in a 2BHK house. You own a TV set, a SOFA, and 16 pairs of clothes. Because of the soaring rent prices, you decide to move into a smaller apartment. You were able to successfully FIt your TV set, SOFA, and the 16 pairs of clothes in a smaller home. Essentially, even with the added advantage of lower rent prices, and maintenance, you are still PRESERVING the contents of the bigger 2BHK home.\n", "\\\n", "\\\n", "You can imagine the logistic function of doing something similar. At its core, it is still a regression algorithm, but, it uses the sigmoid function as an extra step, to restrict its output between 0-1. That is, shrinking it while preserving its meaning. The sigmoid function is called a non-linearity."], "metadata": {"id": "Q8-JldRWt9RX"}}, {"cell_type": "markdown", "source": ["For our COVID prediction example given the tempreture, the equation would be:\n", "\\\n", "Y = P1 + P1*X\n", "\\\n", "\\\n", "We need to feed this into the sigmoid function in order to shrink it."], "metadata": {"id": "bJS8v9bDwutr"}}, {"cell_type": "markdown", "source": ["Visually, the sigmoid function can be represented as:\n", "\\\n", "![sig.png](data:image/png;base64,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)"], "metadata": {"id": "1nqS-20g0JYA"}}, {"cell_type": "markdown", "source": ["Here, Z is P1 + P2*X\n", "\\\n", "\\\n", "The value of the sigmoid function is plotted on the Y-AXIS, for corresponding X-AXIS values for the input Z.\n", "\\\n", "\\\n", "Key takeaways:\n", "- It is clear that when Z is 0, the output of the sigmoid is 0.5 (or 50%).\n", "- For values of Z greater than 0, the sigmoid function outputs a confidence score greater than 0.5 (>50%)\n", "- For values of Z less than 0, the sigmoid function outputs a confidence score less than 0.5 (<50%)\n", "\n", "Based on your threshold score, you can classify the output to be either POSITIVE or NEGATIVE\n", "\\\n", "\\\n", "**NOTE**: Please refer to the notebook on ML_METRICS for a better intuition as to how you can choose your threshold values.\n", "\n", "\n", "---\n", "\n"], "metadata": {"id": "40mgMJTN1FOm"}}, {"cell_type": "markdown", "source": ["# LOSS FUNCTION:\n", "Just like Linear regression, the next steps to building a logistic regression model are:\n", "- Formulate the loss function.\n", "- For each of the parameters, find the value which minimizes the loss function. That is, the value where the slope is equal to 0 (By taking partial derivatives)\n", "\n", "Let us tackle the first step.\n", "\\\n", "\\\n", "Can we reuse the same loss function as we did for Linear regression (least-squares error)?\n", "\\\n", "\\\n", "Well, why fix something that isn't broken? "], "metadata": {"id": "G_KpHFYq2FXK"}}, {"cell_type": "markdown", "source": ["Let us go back to the house relocation example.\n", "\\\n", "\\\n", "You had decided to move to a smaller 1BHK apartment so that you can minimize your rental expenditure. Imagine that you wanted to relocate to MG Road. There are 7 different apartments in that area. You decide to pick one of the 7 apartments and begin to search for a 1BHK with the lowest cost. Sure enough, you find the lowest costing 1BHK in that apartment. The movers constantly keep pestering you to give the drop-off address, so that they can move your furniture and finish the job. This annoys you and you decide to halt your search after just one single apartment. You definitely got a really good deal RELATIVE to the prices of other flats in that apartment.\n", "\\\n", "\\\n", "But, can you be sure that this is the best deal? What if apartment number 2, right across the street was offering a 1BHK at a much lower price than what you settled for? If so, then we were unable to find the optimum price.\n", "\\\n", "\\\n", "What you just learned was the principle of LOCAL and GLOBAL OPTIMUM.\n", "\\\n", "**LOCAL OPTIMUM** - The optimum solution within your limited search area. In this example, it is the cheapest 1BHK flat within Apartment 1.\n", "\\\n", "**GLOBAL OPTIMUM** - The optimum solution globally. In this example, you can imagine the global optimum to be the cheapest 1BHK flat in that area (Inclusive of all seven apartments)\n", "\\\n", "\\\n", "\\\n", "Now, imagine the same scenario where the movers are constantly bugging you. Nothing changes, you still decide to halt your search after just one apartment.\n", "\\\n", "But, let us assume that the 1 single apartment has the best features of ALL the SEVEN APARTMENTS. In such a case can you be sure that you are getting the best deal?\n", "\\\n", "\\\n", "YES! In the second case, where the single apartment is the best of ALL THE SEVEN APARTMENTS, there is EXACTLY ONE OPTIMUM SOLUTION. That is the LOCAL OPTIMUM = GLOBAL OPTIMUM. \n", "\\\n", "\\\n", "**Global optimum leaves no room for ambiguity regarding the presence of a better solution.**\n", "\\\n", "\\\n", "What we just learn was the main difference between **CONVEX** and **NON-CONVEX optimization**."], "metadata": {"id": "-PRGU-Gr5okp"}}, {"cell_type": "markdown", "source": ["# NON-CONVEX optimization:\n", "![local_minima.png](data:image/png;base64,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)\n", "\\\n", "We know that optimizers such as gradient descent STOP once they encounter the point where the SLOPE is 0. In the above graph, it is clear that there are THREE different points where the slope is equal to 0. Of the THREE only ONE is the **GLOBAL OPTIMUM** ( The lowest DIP). But, gradient descent has no information regarding the existence of multiple minimums. It just STOPS once it encounters a 0 slope point.\n", "\\\n", "\\\n", "There is absolutely no guarantee if the solution provided by the gradient descent algorithm is the LOCAL or GLOBAL OPTIMUM."], "metadata": {"id": "NtLy0sCC-gXq"}}, {"cell_type": "markdown", "source": ["# CONVEX OPTIMIZATION:\n", "![global.png](data:image/png;base64,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*******************************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)"], "metadata": {"id": "BdqnsK86_xUS"}}, {"cell_type": "markdown", "source": ["In CONVEX optimization, there is EXACTLY ONE OPTIMUM SOLUTION, which happens to be the global optimum solution."], "metadata": {"id": "U0CXFLTPCfy-"}}, {"cell_type": "markdown", "source": ["\\\n", "\\\n", "\\\n", "Now, returning to our choice of LOSS FUNCTION for Logistic Regression, we CAN NOT use Least Squares Loss because, for logistic regression, it produces a NON-CONVEX optimization curve. There is NO guarantee that we will land on the best solution.\n", "\\\n", "\\\n", "Why? without getting into the details, you can see that the LSE was designed for regression outputs that extend from -Infinity, to +Infinity. But, the outputs of the LOGISTIC REGRESSION vary ONLY from 0 - 1. It simply does not make sense for us to port the loss functions without expecting a dip in performance. Almost like trying to fit your bicycle tires onto a car. \n", "\n", "\n", "---\n", "\n"], "metadata": {"id": "equ6z-aRCuzS"}}, {"cell_type": "markdown", "source": ["**LOGISTIC REGRESSION LOSS FUNCTION FOR CONVEX OPTIMIZATION:**\n", "\\\n", "\\\n", "\\\n", "For logistic regression, the classes can either be 0, or 1.\n", "\\\n", "\\\n", "**LOSS When the actual label is 1:**\n", "\\\n", "\\\n", "Z = P1 + P2 * X\n", "\\\n", "Result = Sigmoid(Z) (**NOTE**: Result ALLWAYS varies from 0 - 1)\n", "\\\n", "**LOSS = -Log(Result)**\n", "\\\n", "\\\n", "Let us demystify the above equation.\n", "\n"], "metadata": {"id": "19Ujes<PERSON><PERSON>_qwi"}}, {"cell_type": "code", "source": ["#@title\n", "\n", "x=[]\n", "y=[]\n", "i = 0.01\n", "while(i<6):\n", "  x.append(i)\n", "  y.append(np.log(i))\n", "  i+=0.01\n", "plt.plot(x,y)\n", "axes = plt.gca()\n", "x_vals = np.array(axes.get_xlim())\n", "y_vals = 0 + 0 * x_vals\n", "plt.plot(x_vals, y_vals, '--')\n", "plt.xlabel('SIGMOID_RESULT')\n", "plt.ylabel('LOG (RESULT)')\n", "plt.show()"], "metadata": {"id": "j_fhVPHDL5gt", "colab": {"base_uri": "https://localhost:8080/", "height": 281}, "executionInfo": {"status": "ok", "timestamp": 1649668448611, "user_tz": -330, "elapsed": 421, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "6e3d384f-5075-4182-bfe4-e6abd4fce294"}, "execution_count": 8, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["**IMPORTANT NOTE:**\n", "The terms COST and LOSS are used interchangeably. They both represent the Y-axis value, which we want to minimize.\n", "\\\n", "\\\n", "The above graph represents the LOG function = Log(Result). \n", "\\\n", "\\\n", "**Takeaways:**\n", "- The LOG function tends to -∞ when the Result nears 0.\n", "- The LOG function crosses over to +ve when the result is equal to 1 and remains relatively flat.\n", "\n", "We are ONLY interested in the section of the result between 0 and 1. Why? Because the LOGISTIC REGRESSION only produces results in that range.\n", "\\\n", "\\\n", "The graph filtered between 0-1"], "metadata": {"id": "3ygeq1H3-_sS"}}, {"cell_type": "code", "source": ["#@title\n", "\n", "x=[]\n", "y=[]\n", "i = 0.01\n", "while(i<1):\n", "  x.append(i)\n", "  y.append(np.log(i))\n", "  i+=0.01\n", "plt.plot(x,y)\n", "axes = plt.gca()\n", "x_vals = np.array(axes.get_xlim())\n", "y_vals = 0 + 0 * x_vals\n", "plt.plot(x_vals, y_vals, '--')\n", "plt.xlabel('SIGMOID_RESULT')\n", "plt.ylabel('LOG (RESULT)')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 280}, "id": "7-16WnjoQJ6N", "executionInfo": {"status": "ok", "timestamp": 1649668596361, "user_tz": -330, "elapsed": 427, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "8fd130c0-7535-439b-f1b2-8db9cbb38bcc"}, "execution_count": 9, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["The above graph represents the LOG(Result) when the results are between 0 and 1.\n", "\\\n", "\\\n", "But, we want the -LOG(Result). Hence, we flip the above graph along the X-AXIS:"], "metadata": {"id": "2iXnzbQuegX_"}}, {"cell_type": "code", "source": ["#@title\n", "\n", "draw_graph(lim=1, loss=1)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 315}, "id": "Wa756G0YQ0YK", "executionInfo": {"status": "ok", "timestamp": 1649627604427, "user_tz": 420, "elapsed": 379, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "00918871627688614982"}}, "outputId": "bebcaa7d-9e2f-418f-bee8-7b165a66208c"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.7/dist-packages/ipykernel_launcher.py:8: RuntimeWarning: divide by zero encountered in log\n", "  \n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["The Y-AXIS represents the loss for the inputs with label 1.\n", "\\\n", "\\\n", "Let us take an example to better understand how the LOSS works.\n", "\\\n", "\\\n", "If we were to classify a patient who is COVID +ve with a score of 0.2 (In other words, the model is 20% sure that the patient is indeed COVID +VE)\n", "\\\n", "Then:\n"], "metadata": {"id": "wQyCnw3cXKG7"}}, {"cell_type": "code", "source": ["#@title\n", "\n", "draw_graph(lim=1, loss=1, x_line=True, x_intercept=0.2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 315}, "id": "e2e3YaezTOO-", "executionInfo": {"status": "ok", "timestamp": 1649627658040, "user_tz": 420, "elapsed": 291, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "00918871627688614982"}}, "outputId": "97053d01-1276-4da0-f9fa-709dbaf7cdb2"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.7/dist-packages/ipykernel_launcher.py:8: RuntimeWarning: divide by zero encountered in log\n", "  \n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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**************************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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["The LOSS would be 1.8.\n", "\\\n", "\\\n", "Now, let us see what would be the loss if our model labeled the same example with higher confidence of 0.8 (80%)"], "metadata": {"id": "pCvZMTsjTxEP"}}, {"cell_type": "code", "source": ["#@title\n", "\n", "draw_graph(lim=1, loss=1, x_line=True, x_intercept=0.8)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 315}, "cellView": "form", "id": "SVuL4RbkTRSw", "executionInfo": {"status": "ok", "timestamp": 1649627666446, "user_tz": 420, "elapsed": 440, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "00918871627688614982"}}, "outputId": "dd3efe98-9ff7-41bb-e1c4-90ef7884ba23"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.7/dist-packages/ipykernel_launcher.py:8: RuntimeWarning: divide by zero encountered in log\n", "  \n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["We see that the corresponding LOSS is very low (~0.3)\n", "\\\n", "\\\n", "**Takeaways:**\n", "- When a sample is indeed +ve, and if the model predicts with a LOW confidence score, then it will be penalized with a HIGH COST.\n", "\n", "- the COST/LOSS will be 0 When the model predicts a POSITIVE input with a confidence score of 1(100%) (prediction result = Actual label = 1).\n", "- Similarly,the COST will be the HIGHEST when the model predicts a POSITIVE input with a confidence score of 0."], "metadata": {"id": "98S8ty7bUJKG"}}, {"cell_type": "markdown", "source": ["**LOSS When the actual label is 0:**\n", "\\\n", "Z = P1 + P2 * X\n", "\\\n", "Result = Sigmoid(Z) (**NOTE**: Result ALLWAYS varies from 0 - 1)\n", "\\\n", "LOSS = Log(1 - Result)\n", "\\\n", "\\\n", "We can see that the LOSS function is VERY similar to the loss when the label was 1. Except, because we are taking the Log(1 - Result), we need to flip it along the Y-AXIS\n"], "metadata": {"id": "U4zzPm-5VJ28"}}, {"cell_type": "code", "source": ["#@title\n", "\n", "draw_graph(lim=1, loss=0)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 280}, "cellView": "form", "id": "XB49I0ITUFhP", "executionInfo": {"status": "ok", "timestamp": 1649627719134, "user_tz": 420, "elapsed": 692, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "00918871627688614982"}}, "outputId": "635212d4-8f54-41f0-9f7f-04c0c32700aa"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["Let us take an example to better understand how the LOSS works when the label is 0.\n", "\\\n", "\\\n", "If we classify a patient who is COVID -ve with a score of 0.9 (90%), then this will result in a very HIGH loss. The model is highly confident that the patient is COVID +ve when in reality the patient is -ve."], "metadata": {"id": "8g4I6qzYWQSe"}}, {"cell_type": "code", "source": ["#@title\n", "\n", "draw_graph(lim=1, loss=0, x_line=True, x_intercept=0.9)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 280}, "cellView": "form", "id": "PHpSZTYlYU6r", "executionInfo": {"status": "ok", "timestamp": 1649627737498, "user_tz": 420, "elapsed": 516, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "00918871627688614982"}}, "outputId": "898d443b-af63-4a79-9b28-4a0f5b87c388"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["The LOSS is around 2.2\n", "\\\n", "\\\n", "Now, let us see what would be the loss if our model labeled the same example with lower confidence of 0.2 (20%)"], "metadata": {"id": "PFj6vm-xYm9p"}}, {"cell_type": "code", "source": ["#@title\n", "\n", "draw_graph(lim=1, loss=0, x_line=True, x_intercept=0.2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 280}, "cellView": "form", "id": "wcvpsCjeYvfD", "executionInfo": {"status": "ok", "timestamp": 1649627750478, "user_tz": 420, "elapsed": 360, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "00918871627688614982"}}, "outputId": "f7091db3-b79f-41e4-d529-bdb06174817a"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["We see that the corresponding LOSS if very low (~0.3)\n", "\\\n", "\\\n", "**Takeaways:**\n", "- When a input is -ve, and if the model predicts with a HIGH confidence score, then it will be penalized with a HIGH COST.\n", "\n", "- the COST/LOSS will be 0 When the model predicts a NEGATIVE input with a confidence score of 0(0%) (prediction result = Actual label = 0).\n", "- Similarly,the COST will be the HIGHEST when the model predicts a NEGATIVE input with a confidence score of 1."], "metadata": {"id": "AdemMP7xY7Cz"}}, {"cell_type": "markdown", "source": ["**Combining both positive and negative cases in a single loss function**\n", "\n", "![image.png](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAn0AAAIUCAIAAADg6g0IAAAYcGlDQ1BJQ0MgUHJvZmlsZQAAWIWVWQc8le3fv+6zz7HPsffeZO+9994kHOtYccxQIskqUSFKJZmVSqESkYZSRg9JEsmoFCoqI+9t1PO8z//9vO/nvT6f676+53f9rt+61v07NwBcnb6RkWEIRgDCI2KoDqYG/G7uHvzYKQABHoAHdEDZlxwdqW9nZwXg8rv972VpEOaGy3OZDVn/2f+/FqJ/QDQZAMgLxn7+0eRwGN8FAJVGjqTGAIAxgulC8TGRGzgYxsxU2EAYJ2/goC18dAP7beGKTR4nB0MYNwOAo/X1pQYBQN8J0/njyEGwHPp3cB8xwp8SAbP+gLEOOdjXHwAuNZhHOjx89waGKxCH+SNhnAtjNb9/yAz6b/L9/sj39Q36g7f82iw4I0p0ZJjvnv9naP7vEh4W+1uHKFxpg6lmDhv+wzEcCt1tuYFpYTwb4WdjuxFrGP+g+G/FHQAEITjWzHmLH8FNjjaE4wdYYSzn72tkCWNuGJtEhNlYbdP9Aikm5jCGVwsigRJj7rQ9NjMg2thxW+Yp6m4H2984kGqovz32si91U+8Gf2dsqLP+tvyh4ADz3/K/JQY7ucKYAACSEEdxsYExPYyZo0MdLbd4kIKJwYY2v3mosQ4b9gvDWC0gwtRgSz7SK5Bq4rDNHxke/dtfZEYwxdxmGxfHBDuZbcUHWUv23bSfHcaNARH6zr/lBES7Wf32xT/AyHjLd+SzgAjnbX+Ro5ExBg7bY+cjw+y2+VG4gDDTDbogjDmj4xy3x6K0YuDFuSUfZRUZY+e0ZSfKJ8TXwm7LHlQcsAKGwAjwg1i4+oHdIARQns02zcK/tnpMgC+ggiAQAGS2Kb9HuG72RMBPR5AIPsEoAET/GWew2RsA4mD62h/q1lMGBG72xm2OCAVTMA4HliAM/h27OSrijzYX8A6mUP5DOxm2NQyuG33/SdOHKVbblNjfcvkZfnNijDFGGDOMCUYCxYnSQWmirOCnHlwVUGoo9d/W/s2PnkL3od+iB9Bj6Je7KGnUf9liDcZg+SbbHvv902OUKCxTGWWA0oalw5JRrChOIINSgvXoo3Rhzcow1XDb7g3f+f8HP/948I+Yb/Ph5fAIPBteDy/+75H0kvTKf6RsRPSf8dmy1e9PVA3/9Pxbv+E/4uwPt5b/5kRmIhuQD5HtyC5kC7IJ8CPbkM3IbuSdDfxnDb3bXEO/tTls2hMKy6H8hz7fbZ0bkYyWq5N7L7e63QdiAhJiNjaY4e7IPVRKUHAMvz58CwTwm0eQZaX5FeQUFADYuFO2jqmvDpt3BcTa8zeNfBAA1XkA8Mt/08K/AnAF3uP81n/TRLzhbYYBoHqKHEuN26KhNh5o+DRggHcUB+AFQkAc9kgBqABNoAeMgQWwBU7AHXjDcQ6G1zMVxINkkAoyQA44Ck6AEnAGnAfV4BK4BppAC2gHD8AT0AsGwCt4/UyCj2AeLIEVCIKwEB1EgjggPkgEkoIUIDVIBzKGrCAHyB3ygYKgCCgWSoYOQDlQAVQCnYNqoKvQTagd6oL6oJfQOPQe+gItI5AIWgQzggchitiBUEPoIywRToidiCBEFCIRkY44gihGlCMuIhoR7YgniAHEGOIjYhEJkDRIVqQAUgaphjRE2iI9kIFIKnIfMhtZiCxHXkbegmf6OXIMOYv8icKgSCh+lAy8hs1QzigyKgq1D5WLKkFVoxpRnajnqHHUPOoXmg7NjZZCa6DN0W7oIHQ8OgNdiK5E30Dfh3fTJHoJg8GwYsQwqvBudMeEYJIwuZjTmHrMXUwfZgKziMViObBSWG2sLdYXG4PNwJ7EXsS2Yfuxk9gfOBocH04BZ4LzwEXg0nCFuFpcK64fN41bwTPiRfAaeFu8P34PPg9fgb+F78FP4lcITAQxgjbBiRBCSCUUEy4T7hNGCF9paGgEadRp7GkoNPtpimmu0DyiGaf5SUuklaQ1pPWijaU9QltFe5f2Je1XOjo6UTo9Og+6GLojdDV09+hG6X7Qk+hl6c3p/elT6EvpG+n76T8z4BlEGPQZvBkSGQoZGhh6GGYZ8YyijIaMvoz7GEsZbzK+YFxkIjHJM9kyhTPlMtUydTHNELFEUaIx0Z+YTjxPvEecICFJQiRDEpl0gFRBuk+aZMYwizGbM4cw5zBfYn7GPM9CZFFicWFJYCllucMyxopkFWU1Zw1jzWO9xjrIuszGw6bPFsCWxXaZrZ/tOzsXux57AHs2ez37APsyBz+HMUcoRz5HE8drThSnJKc9ZzxnGed9zlkuZi5NLjJXNtc1rmFuBLcktwN3Evd57m7uRR5eHlOeSJ6TPPd4ZnlZefV4Q3iP87byvucj8enwUfiO87XxfeBn4dfnD+Mv5u/knxfgFjATiBU4J/BMYEVQTNBZME2wXvC1EEFITShQ6LhQh9C8MJ+wtXCycJ3wsAheRE0kWKRI5KHId1ExUVfRQ6JNojNi7GLmYolidWIj4nTiuuJR4uXif0lgJNQkQiVOS/RKIiSVJYMlSyV7pBBSKlIUqdNSfdJoaXXpCOly6RcytDL6MnEydTLjsqyyVrJpsk2yn3cI7/DYkb/j4Y5fcspyYXIVcq/kifIW8mnyt+S/KEgqkBVKFf5SpFM0UUxRbFZcUJJSClAqUxpSJilbKx9S7lBeU1FVoapcVnmvKqzqo3pK9YUas5qdWq7aI3W0uoF6inqL+k8NFY0YjWsac5oymqGatZozWmJaAVoVWhPagtq+2ue0x3T4dXx0zuqM6Qro+uqW677VE9Lz16vUm9aX0A/Rv6j/2UDOgGpww+C7oYbhXsO7RkgjU6Nso2fGRGNn4xLjURNBkyCTOpN5U2XTJNO7ZmgzS7N8sxfmPOZk8xrzeQtVi70WnZa0lo6WJZZvrSStqFa3rBHWFtbHrEdsRGwibJpsga257THb13ZidlF2t+0x9nb2pfZTDvIOyQ4PHUmOuxxrHZecDJzynF45izvHOne4MLh4udS4fHc1ci1wHXPb4bbX7Yk7pzvFvdkD6+HiUemx6GnsecJz0kvZK8NrcKfYzoSdXd6c3mHed3Yx7PLd1eCD9nH1qfVZ9bX1Lfdd9DP3O+U3TzYkF5E/+uv5H/d/H6AdUBAwHagdWBA4E6QddCzofbBucGHwLMWQUkJZCDELORPyPdQ2tCp0Pcw1rD4cF+4TfjOCGBEa0bmbd3fC7r5IqciMyLEojagTUfNUS2plNBS9M7o5hhl+ee+OFY89GDsepxNXGvcj3iW+IYEpISKhe4/knqw904kmiReSUEnkpI5kgeTU5PG9+nvP7YP2+e3rSBFKSU+Z3G+6vzqVkBqa+jRNLq0g7dsB1wO30nnS96dPHDQ9WJdBn0HNeHFI89CZTFQmJfNZlmLWyaxf2f7Zj3PkcgpzVnPJuY8Pyx8uPrx+JPDIszyVvLKjmKMRRwfzdfOrC5gKEgsmjlkfazzOfzz7+LcTu050FSoVnikiFMUWjRVbFTefFD559ORqSXDJQKlBaf0p7lNZp76f9j/dX6ZXdvkMz5mcM8tnKWeHzpmeaywXLS88jzkfd36qwqXi4QW1CzWVnJU5lWtVEVVj1Q7VnTWqNTW13LV5dYi62Lr3F70u9l4yutR8WebyuXrW+pwr4ErslQ9Xfa4OXrO81tGg1nD5usj1UzdIN7IbocY9jfNNwU1jze7NfTctbnbc0rx147bs7aoWgZbSOyx38loJremt622JbYt3I+/Otge1T3Ts6nh1z+3eX532nc/uW95/9MDkwb2H+g/bHmk/aunS6Lr5WO1x0xOVJ43dyt03nio/vfFM5Vljj2pPc696760+rb7Wft3+9udGzx/8Zf7XkwGbgb5B58GhF14vxob8h2Zehr1cGI4bXnm1fwQ9kv2a8XXhKPdo+RuJN/VjKmN3xo3Gu986vn01QZ74+C763epk+hTdVOE033TNjMJMy3uT970fPD9Mfoz8uDKb8Ynp06nP4p+vz+nNdc+7zU8uUBfWv+R+5fha9U3pW8ei3eLoUvjSyvfsHxw/qn+q/Xy47Lo8vRK/il0tXpNYu/XL8tfIevj6eqQv1XfzVQAJV0RgIABfqgCgcweA1AunCZ5bOd92QcIvHwi4dYFkoY+IdPhG7UFloE0wSMwTbDEuAm9FkKDB0szS9tM10VcxVDLWMzUTO0hPmHtZhljfsM2wf+RY4FzmWuNB8GL5CPx0AkRBohCrMLsImyi7GLc4jwS/JL+UoLSwjKis2A5pOTl5RQUVRQ0lXWVjFXNVczUTdRMNE01DLX1tLR0NXSU9WX1RAx5DZiOC0brxV5Mp05dm3eYtFtWWx6xSrENs3GyN7ZTtxRy4HBmdcM5IF8gV4YZyx3swenJ4Ce+U8ZbYJezD58vpx0Im+RMDSIGsQVzBghTpENVQkzCXcEpE8u6CyIqos9Ti6PyY3NisuOz4IwnFe6oTW5Ne7QX7pFN27T+Z+uqAYPrug+2HMJlCWQrZBjmOuYGHE4/k51UfvZs/XLB4nOmETKFFUWDxgZNlJTdL+0+9O714BnuW45xkudZ52wq/CzGVB6sKq6trbtY+rhu++OHSz3rcFbar4td0G9yvR93IajzdVN/cdrPrVs/t3pYndzpar7aV3k1p39WhcY94b6rz5v3aB6ce5jxK6PJ7bP5Etpu+e/bp/WeneiJ7DfpIfRP9156n/mU/IDKIGnz/onuo/mXBcMwrlxG115yvV0dH37SPXRjPert7wvmd1qQwvMqWpv+auf6+6EPKx7BZ8ify58i5nPkbC3Nf9b6dWyJ9L/4ptfxsNeWXxvr6P+ZfATmDKkBbYlgwr7ENuFx8EMGIRpKWgXaVbpp+iGGI8Q3TO+In0lfmJZY11hW2NfZfHGucS1xfued4pnhH+Pr57wvcFKwUyhEOE7ESlRTDi30Q75KokcyWokhbysjI0snO7eiTuy5fpJCsSFayVzZQUVAVUCOqrat/1hjR7NJq1C7XydWN1/PRtzBQMOQ0Qhi9N35mcsU03yza3MlCxZLNcsXqjfU9m1rbfLsk+0AHR0d9J3lnAReSK9Z12e2j+4hHt+cdr/qdZ72P7Trkk+xL9aOQff09ApwC7YNsgi0pliFmoZphsuECESy7aSIRkatRP6g/o9di0XHEeKEEjT1OidFJhckte6dSaPbzpcqkaR+wSfc7GJ9x+FBlZlvWcPb3XObDCkfs8yKOHs6vK3h07N3x9ULOIuViu5OhJQdLz5xqPt1bNnPm1znmconz2hV2F8iVsVWHqovhc667bu4S8bJiveOVqKt51+oaOq+P3PjShGnmuCl5S+O2RYvbncDWmLaUu6ntBzoO3svoPHQ/80H2w9xHh7sOPz785HB37tOcZ1k9h3rT+1L79z6P+ytqYPdg5IuYoaSXB4ePvSofaXj9YPTlm0/j4C1xQvCd/KTOlPm038zZ958+Ks8mfWr9/GtecyHuy+Wv7xbZlyy/p/xo+Dm9wr3qsJb9q3N7/o0R+sgdyM+odvQhjCNWHLuAu4nPIDjQcNOM0p6nC6dXZ0AwtDOmM1kQGYi9pKPMtiwMLE9Zs9lM2CH2Zo4ITiHOIa4cbh3uTzylvGa83/jK+M34PwscF9QQHBHaK8wv3CriLbIqWiymJNYtHiC+KnFMUkqyTcpRako6VUZEZkg2d4fBjm9yVfKeCnQKbYqRSgJK/cppKgoq46p5atpqn9RLNcw1FjXPa9lr/dKu03HXxere0CPrE/XvGkQa8hv2GqUZKxlPm5SY2sLvHbfNoyykLN5Zlll5WLNaP7cpsHWwI9kN2p908HYUdvzgdNU50cXYlcF12K3SPdrDwJPWc9DrzM5gbwXvlV33ffJ9vfwk/JbInf7HAnwDFYNQQYPBtZSUEKdQ6TB02JvwWxHFu+MjXaM0qLzRqOjZmIHY9rj6+LKEvD2pifFJocn+e3fuc0tx2u+Qap9mf8Ah3emge8bOQwGZoVnR2Sk5mbkFh8uO1OQ1Hr2X31cweuzzCVShRJFX8dGT90tWTsme9is7cebx2dVyhfMBFSUXeqpQ1Vo18bX1dR8vSV4Oqa+9MndNpWH/9e5Gjqaw5s5bfLdTWt62WrW1tMt3XOyUun/1ocGj4ccJ3XxPe3sO9zk9Fx0Agx+H3g1/eA3eiIzvmqidQs8kfgSfKubJX3WX1H46rxZvzP/Wf38bBaMCwIlDAGz8n+NQA0DuRQDE9gDABueednQAOKkDhIApgBY7AGSh/ef+gODEkwBIgAdIAjVgBueXYXBOWQTqQReYAGsQO6QMOULR0AmoGXoN53zSCBdEKqIeMYpkQBog45GXkFNwluaFKkW9gjMxH/QF9CeMCiYV8wzLgw3HtuFIOAquHc+Jj8X3ExQJRYRVGjLNU1p12mo6drpcegR9Ev13hliGJcZEJogpm8hKrCCpkXqZQ1iwLBdYjVmn2DLZpdh7OWI4OThbufy5abiv8XjwInkv8XnCGUGfQJ6grRCT0HPhIhFPUQHRKbGL4tESGpKQZJdUvrQnvDrnZft3tMhVyhco7FOkKDkqa6jwqUKqY2ot6sc1QjS1tei1RrRrdGJ0dfVwen36DQbXDZuMbhm3mtwz7TLrMR+0GLWctlqwXrHF2bHaizqoOVo5kZ2TXYpdW91mPEie+l6RO8u9B3wIvvp+SeRm/++BakFJwe0hhFDnsIrwxd1mkWVRc9FaMTmxo/FKCUf3LCS5Jj/Yp53SmmqZNpGelaGdCbL6cq4cPpVXkG92DHn8fmF+cUCJ4SnpMsGzIuVKFTaVUdWltU8ugXrVqzYN7jeCm5Jvnrh97U5/21IHb6fZg5hHZx8/617rkenb+fzIwN0h0jB55NLo7Dj3hNqk3rT8e/oPL2aPfN4x175g9qXzm8JiydLyD/ufF5YXVjXWUn7d3Tw/tuafCM+/BFAFJsAVhIB94DioA51gFPyASJAcZANFQEehBuglAiAk4Cw/DXEV8RbO462Q6cg25ApKG3UA1Y1mRweiGzF4jDemEcuIDcM+wUnj8nCLeC/8A4IsoYgGSRNFM07rTPuYzpCulV6L/g6cxT5itGcchfPUdeJxkizpKXMEnHk2s/qy0bA1swdysHI85NzDJc01zl3EY8uL4+3g289vIIAReCpYKOQrLCu8KtItWiYWJW4swSXxRfKx1HnpFBlPWc0dEnLs8nj5VYU5xQmlF8qPVW6rXlQrUT+kQdX01DLUltRh1FnUHdZr1a83uGrYYNRkfNukzbTT7LF5r8ULyzdW09YLNit2OHtWBzFHdSdrZ3+Xva4lbjfdhz3WvAR3WnjH7Drr0+MHkVX8IwJqAqeCRSkhIVdDl8NNIwp3z0RpUfdGt8Wi4qziixKmEtWTjiRP7zNOqU6lT9tzYBo+T3ozLbIe5pjldh9xyBvLTznGe/xuYWAx/cnmUv/TpLIHZ/eWq5z/cuFqVWyNVh3m4sDlC1eSr3ldV2mkb5q4ef32gTs2bex3xztqOqkPtB5huwaf1Dzd3+PVp/NcZIBp8NGQ88vJV4mvmUevjTmNr05UT7pPM8x0fcictfzMOPdi4ezXkEWV74gfPculq0G/FLfnHwkwgHbzBBAHKvAKcAPh4CA4A26DYXj/C0IWUCxUAQ0iaBBG8M7vQOKQ9sgzyC8oC1QVGo+mot9gnODdboMdwJFxP/GFBHXCJM1JWj3aEboken76LoZ4RknGCaYzRD+SBOk780OWMtYkNk92PQ4pTnYuGm4E9yrPMu8qPxDAwm+gPMKyItqiDmJB4vslTkregPPueVnGHQpyrvL7FCoUe5RWVCRU3dUK1Ps1mbXctSt05vS09Q8bvDFSNM4xGTfTMi+0+GJlZ33JltYuzP6xo6RTjvMHVwu3Wg+8J8XrobforoM+k36G5MoAZKB/0D2KaEhG6Ey4VUR9JEtUAnUsxij2cjx7wr49H5Pc4H2qklKVypF2JB11MDnjS6ZH1tXs9Vynw1VHlo865l8+RjhOOfGgSKo49+RcqeupO2WiZ/Lhs9//fPcFzcqqaqaaxNqpi46XWupFr+RdXWrwvv6gUabpaPP8Lfvbl+8QWgPbWtuJHQH3Gu+jHtg9LH008VjiCaW78ul4D2evfd/B/uvP3w4QBuVeOAxRXx4Zrnl1b2Tg9dTowpvVcegtdgLzDjMJJpenPk2Pzjx93/yh/GPmbMQn689Sc9i5N/PNC1lfPL5KfP3yrWUxbcnoO+Z754+Un5o/F5YvrHisElYb18i/6H5dW3ffmP/oQEWFzesDojUAAD26vv5VFABsAQBr+evrK+Xr62vn4WRjBIC7YVvfkzbvGkYAznJtoL4rXz7/+1vO1remf+Qx/27B5k20qRW+iTZb+FYC/wXfpN+m9W0WhwAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAACfaADAAQAAAABAAACFAAAAADLWsucAABAAElEQVR4Ae2dz8sd15nnXzUiNI0JssyIEBwTW6YXIfTClnvhrW3IsgdkiWEWwzDEbjK9HRkxzKoXRlo3TNv5B8a2YLIcRjbMJt5YEkwwTQ9EcROCIab9A9M0TRat+Vw/0nG99etW3Vs/TlV9Di9S3apTp875nLr3W89znnPqzC9/+cvPPvvspJB+9atf/dmf/Vlhx27zxRdfvHDhQmmnHyXwxhtvCEECEoDA66+//swzz4hCAmUCZ86cXLt2cuNG2n/mwYMH6UNsXLx4kR/T1157rbTfjxKoEjjDLWWSgAROTm7fvv3yyy9LQgJlAhXdPVvK8fbbb//mN7+5ceOGulsi48cmAteuXeOGaTrqfglsgYAPoFvo5aHa+EelguIHFOlFgEuH/CgBCUhAAhKQwJEETuluGLtRohbMkWQ9XQISkIAEJFAlcEp3i1qryVuF5R4JSEACEpDAkQS+1d2isRuFFmX4yMt4ugQkIAEJSEACEPhWd0Nln3vuOfb+5Cc/OXfunCavt4gEJCABCUhgWAIPdTeMXQJT7969ywWYv/vBBx+gwZq8w+K2NAlIQAIS2DiBh7r71ltvvffee0WVRXSRXqajGdi88VvE5ktAAhKQwIAEHs7fRXSrK63gakaP8TYPeD2LkoAEJCABCWyZwEN7tyq6CUrLoZTHDQlIQAISkIAEuhD4Nq6qS27zSEACEpCABCTQj8DFi8X86m6RhtsS2C6Be/fuDfKWC14PQFHb5WjLJVAlcPqFGepulZB7JLA5Aijlq6++evXq1eNbju5SlNJ7PElLWCsBdXetPWu7siZAuOKtW7cyqSIa+dJLLxFcGdP3W2pFzr3VjvmHFKj0tpD00JYJqLtb7n3bPikBpuRhC77yyiu8u4a3bb755puTXr7hYl999RUaef369SbRxfmM/RrVfv755z/66KOGkr7dffnyZV5o9tOf/pTCv93rlgQ2SKBuQpC6u8EbwSbPQ+DKlSsIWJogkMm7WqnSpUuXWDOnCQrT+n/+85+n+YQvvPBCU87ifoQc0c3k2aJYMbclMCkBdXdS3F5MAqcJMCEercXSjd1YkKePz/AJp/H777+/N5yKmvd9XOAUir158yblz9AwLymBjAlo72bcOVZtjQSSnzYHexdppBpdahL2LjkR1I7dgqsZtS6ugtfxRLNJYN0E1N1196+ty45AxCV1kbqxqx6rsuNn3nshIqRCd5vGgJtKYKAXe1eTt4mP+7dJQN3dZr/b6nkIJAXqK2BjVJcAZoxXrNK9hadq951oRBwZhXOhvZcwgwS2Q0Dd3U5f29L5Cdy+fTsq0VfABq869itq2tHsDt84It33cQE/M6dgWBvYPHgPWuByCai7y+07a748AjGl9QABG7yp4e7uGJx8jG88pD1ZzIM3xAIlsDgCD99HtLh6W2EJLJFAyE9HKxOTFMHD1gxjkfhnfMItYU3k5wViabUKDE3cvCkOuYQrTNgu9muSzKJIp2tF5FRLrSJ+m8sx1luqgx8lsE0C2rvb7HdbPQOBsBq5cFHAauuBqqGyLFLBUebO4p3mI7HHTz/9dJLV4okIMxKLwqGRZCM//+LdZQ8bkYr52e7+BJB840k4KZm6xRwhttsjs5gczOVqq12qkh8lsBUCDyqJljOJvrLbHRKoIeDdUgOlYVdam+Lu3bsNWXa7sVkxH0mlbBEAxf4vv/yyeDofw2zl3+KhdLn4LSudwk5M1eLOpu2wzlNmqseFEOP79+8nY7rp3NhfrUB7/iUepY0wWWLNrfO4BLgrTk4enL43tHfjN8F/JTA6gTAxEc4W7y5GJJYr9usHH3xQyhbrbHAIE7NYV8zNsCYjPrl4KLZDD4r779y5w8ekmsVDpW0uV7SMuTRPA9QNMU6n06LSWbUfMeJr97tTAlsj4Pju1np80vYiEPfvT3rFjhdjqaiXX+6Yd5hsCFioI6tFNpWIquET5iimakl02ZnkDR1Ntixlhi7iBE5CWCqfPB1HlEsn8jEKZwPHMtuILvZuZENHQ0r3Fk4GziVzUw2r13WPBFZMQN1dcefO3zTmbWa7SuDEulsUsNqOQZZitiviVLvGE8pdPfGdd96JndUx4xBFjlbVDuWuFlW7J8KvOIR2Ur3iTNzUouqla4typwQkEATUXe+EEQl0/nkfsQ4HFI3AJNE64HQcwskeTacXBSztLG6E6LInTN7iodhOJVR1lAwl+xiRTmFcTfZo6ZTqFdkT4kpOHgVi4Dllw/aN7abyU043JCCBIgHHd4s03JbAjsAXX3xxDIjPP/+8enoIGJJZq5rJXYwzuWkBqXBTU3J6swLbTzzxRPVa7EkvAuIJIDmoa3O27Ey+cTa4aKnm7777Lueys4t+t1zFQxJYP4Fnnim2UXu3SMNtCewIFL2pgxDBeg7VbDINk7u4ZfQ3+XXTfB7qhkgjsSGQqXCuFbFXHK11WUej9tr0oayRuWTBcwkuyqF00cjmvxKQQA0BdbcGirskMCaBJJkxJbd6qZQhgparGVIMM1JatDuxZYku5g3zqC/bHEJNw1ON4pbEMhUbI7IhnGlndSN5kquu770VrpbmHglIIAho73onSGB0AknAmszZ5ENu8tkmE7wqgTH4ymwiJtSSaAxLbWCGtriXWw4VWYS4krnq+k6DzUXju3hucbv7tKXiWW5LYK0E1N219qztyohACBgCmQQPq7Rotqa61u7k9CgB+7WaAVM4Io27SGBcKApp9zNzNDLUPitE0Fa6IqZzalpqS9oIw7pa85TBDQmslkDdtHXjqlbb3TYsEwJJwNJQKDZrSFqqYYtokSfGaGsHa9G/CITuPjWIApFArliqQ6pMbITSs111fadDyV+No7t0evoYV2my41M2NySwTgJ1Kxiou+vsa1uVD4GSSsVIbdLgqGeyKataiEhTAqKbFqwoNi3lp9jHH38cDU7Th4rZqtuxbHKqWzVDEvJSVcmZDoWa4uK+fv16tYTYEy70uFxTHvdLYFME1N1NdbeNnYFAjLlyYTSSZSAZ663GGCOuYfKWxJX8JNzLpf2pGRxKpiTuXNQXFUSAOSvlqd0IKzaNK1fzhCTXjhMnsUeAuRwp1aFaTowENwWUVfO7RwKrJ6Durr6LbeDMBNJyTogrU3trFRTHbyx6jF4iY8gnmZFGRKtWp6NJ6B95rl69yusQKLY42srpaSGO2vaHFZvCo0p50jShWkFNI7XUk6tXo66KpYV+J4O+eMhtCWyUQPVVDIDgIbq63z0SqBLwbqkyqe5BO9FFDN/qodIechK6jEGMKVl8uVApGx/JiYlMscVDXIIvb/otKx0t5mQbTaWE0s6OH6ne3hpSFE2gMjwQdCx2udloJkCWW39rPhYBxJT3EZ1OxjOn3yg3JDAWARSO1KX0jjmxR1966SWMyJKtiSWKZmODchS3M0pQylCsAwYxifHgZCgXj7ZvVwd9a/NjELOfq9QedacEtklAP/M2+91WL5sAhiyyike6thmId8htTOCpzcNOZDss5qYMx++nnjwKdBTp4y9nCRJYBIFOusuryc6cWURzrKQENkEgRTY1tTbWcG43shFdjGPGX2MItqmog/dj7FJPpPfgEjxRAqsk0El3V9lyGyWB5RKIaOS0iFW1IRxCVvc6eDGLMUar8dXVAg/YQ7F4sDV2D0DnKesm0El3X3hhB6Hu7Z/rhmPrJJApAfQSWxaDklSqIr5l5JalGQmQToHHpTzFj9ijZO4467d4Yvs2AdW81okVK9uzeVQC6ydQie3oFFd17tyOzJ07vHtk/YhsoQTyJ4Ati6zyLgTkDeFMNiWii9MYK/OTTz4hT5eGoM2oIwtOsdHul+5SWuQh7IsHAmrYsQ7dSzanBBZG4N69k/PnS3XupLulc/woAQnMTgBJw5FLKo7Onj9//oDxVHQa2xTpHUQpEV0CvihqKBWfHbUVkMCwBDrp7qVLu4vWLe88bGUsTQIS6E0gGbu9zyycgOMazQ7rubD7kE0KYXRZ0T2EneesjwDCeflyqVmddDf8VXXLO5dK86MEJLBUAli9pONrz6Th4wuxBAmshECdwdoprmol7bcZEpCABCQggbkJdNVdIqrqZHvu6nt9CUhAAhKQQJ4ECKoivfJKqXZddZfTnEdUYudHCUhAAhKQQCOBL76oPdRVd7tNSai9hDslIAEJSEACWyVQmUfUVXefeWY3f9ckAQlIQAISkEAnAuFnrqyb0VV3uYZ+5k6gzSQBCUhAAhKAwOef12LoqrtPPFF7ujslIAEJSEACEmggUDdG21V3w1B+//2Got0tAQksnADrTLHkxcIb0aP6rGJNk3ucYFYJ9CXADRbLTp0+savunj7LTxKQwKoIxOKOV69eXVWrWhuD7rKepdLbCsmDoxDoqrvEVZkkIIGhCPBi2sFfAXRw3dCel156qfvijrx94eBrTXMiLdqLl5UsWd2ahiu903TKFq/CPKI67eynuy4At8VbxzYPRIBX9GBj8ercM2fO8F563iY0UMFHFYOIoj3Xr1/vsqIy72Cg/piJR11ynJNxklOxwPv8889/9NFHe6/DupgsTM0LIfJ/ktjbFjPkSAA/8zHjuzk2yTpJYFEErly5gjCkd+IO8j6D4wFQpUuXLl27dq29qFBcVK34+qP2UyY+ivHKCw1xJMR1X4jXhu+rBA8ciG4mz0D7KuvxlRDo9F6EaGudubwSCjZDAhMQ4M19aG3yaqJhE1y0/RI4Y9HR9jcZkAFJo5xsFTe1EcI81oT0dnys4RQM5fBDdDwlXc4NCbQRiOGYuue/rn5mSkd3jf5ro+wxCXQgkPyfOfzKIzlUo70myBjCTNprE3do/ehZkugiqB0vhqsZtY5ni46nmE0C+wnEUlN192EP3d1/GXNIQAL7CES8T7vU7StjmOOMN6NSewdrkaVhrjd+KfgSQne7jFUXq8NAL9Z8/gZ9sc5uL5dAD91lCm/DIs/Lbb41l8CkBNIve19hGKOWBDBjFC5IVvdCSHj7TojCz0zhANl7CTNIoCuBCDU4fv6ufuauxM0ngToCeGtjd19hqCvsqH3YhahUDmb3Uc04fXL48HmY6PtYg5+ZU3AAGNh8mqifjiBw//7uZP3MRyD0VAkMQCCCqg4QhgGufbqIcHd3DPo9fWq+n47x4ccjSLKY822kNVs+gR7xzBGWhelsYPPy+90WzEMgftY7WpmYpAgJNlwYYcQ/4xNuCRci/1tvvZXipTHgcJ+maUulBodp2NcuLBWS1cckmcWHicQkIqda6DGjmuaAhbHerNplZZZKALF8+eXayvcY3w1z+dHsuNrS3CkBCTQSCGuMw0VhqM2NWqCyLP7AUeak4p3mI7HHTz/9dJLV4okIMxKLcqA9ZCM//+I1ZQ8bkYr52e71BFA6N8+PyYefhBMCMLx58yaNZbs9goxJzLSrFm+e7bVWuRNoXtath72beyOtnwTyJhAmJnVst3dRCJSSbB988EGyR5nDc//+fQ6xttQnn3xStNsQ3VjskMycEoe4BMYxkkMKKsV5MpxCajKF86bYWLuQTBoV7YIV1j+hUnzkqYVHmWQQ1xYRqNvz1J7oTgk0Eqgb3CVzD3s3LGZDqxoRe0ACrQTiNx1dTGpazY5MYrkiikXRjWyIBxscQlGKJ2LGheREfHLxUGxjCD548KC4/843MwtDn4r7l7sNlsAbzzQgunv3Lgz5mJpZfFhpaSkK3XLUQxLoSoBvWcOgbG97t+E9vl1rYr5tEeAxLc/JZ3wfGr4SI3UQwhDqyGqRTZdALcLSxbqtanOSDXQ0LWFBmaE3OFeTwJTKJ0+7hV3Kv8SPyU7Fscw2oouxGw1BR0NK90IgA+eSuYnkEslY59kI6GeeDf3GL4y/NM+XNrMc8TdrH07WP0VhqL0oP/cxi5Qf/aJPOGVGudN22njnnXdiuzpmHGLD0aqKpKHQVM7SN4o+fDAWZ+Im8lVES2+19c+dwBNP1Nawn72Lq1kfTC1Hd9YTQNuytXfra7zbyw93Eq3mXI1HcAgnezRlKgpD2lncCNFlT5i8xUOxnUqo6igZSvYxIp3CuJrsvNIp1SsOtWcMnqW6hbjSIh5ZsHSTb4Bs2L6RuYlDqSg/SmAAAmFvsNpUXeqnu5RQ98xdV7D7JACBhtsuczZfHPes8HndYEwIA5JZq5rJXYxgNC0gFW5q0MWMl2D4RMMDdXrBDk8ARRGahfwYPIsNST58NoBTIvzuu++SmZ2TPWcU6+a2BKoE+ukuwVnqbhWie1ZGoOilHKRpWM+hmk0mV3IXt4z+Jn9pmidD3RBpJDaEJxXOtSL2iqO1Luto1DE2fS8sg/MsXT2UNXaWPA2gAA6HEpzSuX6UwIgEGoJI+ukuhTyalTBiVS1aAisjkCQzpuRWW5cyRNByNUOKYUZKi/YctixRu7y5HfVlm0OoaXiqUdySCKViY6QzBCntXO5G8iRXXfR7wS631dY8awKxIuwgupt1O62cBHIlkIShyZxNPuQmX2gyGavSEoOazCZigi8JBiy1gXnX4l5uOZQrwrZ6hbjSqKqLPg2KF50ETWWtb3pVU0vdPy+BfvbuNyupzVthry6B5REIYUAgk+BhlRbN1tSk2p2cHiVgv1YzYApHBG8XaYkLRSGT+ZlT68bYoBXRkNpnmgguS2Qw8VMXVCsTDoAq4WpO90hgP4Hm6JYe62ZwmTCa85wYsp+COSQwB4EkDGmIEZs1pCJVp0UMyBNjtLWDtehKBEL3mhqEtHDFUh1SZZa1EU8k1Lnqok+Hkl8dh3xT64JGk7+h6Sz3S6CeAEsXnD9ff6jXelVNRbhfAhJoIVD69Y+R2qTBcWKy1apaiEhTAqKbFoIoXivlp9jHH38cDU7Th4rZqtuxHHGqWzXDUvakB44SUuqfDoWa4oq/fv16U7vC1R9YmvK4XwKDEDjE3jWkeRD0FrIRAjHmSmPRSJaBZKy3GmOMuIbJWxLX3fLKN2/iXi7tT+g4lEw03KSoL+qCAHNWylO7EdZhGleuzVPamTLTkHxisuLRoXY8Oz2UIMBgISVWpabxMUaCmwLfqvndI4E2Aqx0Mayf+aOP2i7nMQlIoEggLZOEuDK1t1ZBcfzGYsLoJfKAfJIZaUQManU6ykdXyHP16tUvv/ySYoujmJwe/udiTYrbYR2msKPiodI2laGoeNlRHOK66BM7uUop88QfeRSIJ4BaQU0jtTQBStWoq2JtQ7+T46F4yG0J9CbQvsIUC6aXEhfgIbq0M308OXnQfDDlcmMrBNrvlq1Q2NdOtBNdxPDdl/EBOQldxiDGRENNW/KTExOZYot5uARf3vQbUTpazMk2WkUJpZ0r+wjGvSRpMqiBxoPLwc3ndC508OmeuDYCrUrZL56Ze6vZdE5fdjckIIFTBFA40qldDR865sTO491/GGclGw4LD83GtuMohiBKUMpQvCwGK4nx4GQoF4+uY7s66FvbLgxi9kOj9qg7JdCPQBi737xArPbEfuO7FEGIlq8CrEXpTglMRgBDFlnFI117RcQ75LZ9FBbZDou5tpBN7YQnjywdRXpTZGzsIQTanczGMx/C1HMkMDeBFDHUVJFYw7ndyEZ0MY4Z14yhzaaiVr8fYxeeSO/qW2oDMyHQ297FW7ZPyzNpmtWQwGoJRDRyWsSq2k4OIat7HaeYxRh51fjqaoEr3kPz8bRr7K64i6duWviEeX9fQ+qtu5Sj7jbAdLcEJiKAXmLLYqiRSpfEt4zcsuQhAdIpoLeUp/gRO4/MHWf9Fk9cxzYh2bwuiZU119EcW5EFgbo3khUr1juuqniy2xKQwCwEsGWRVd6FgGwgnMlWQ3RxGmO9ffLJJ+TpUje0GdVhISc22v3SXUpbVh7C03hwgWRHVstqnbWdk0Drt6+37hKixYx8TN6GFy3M2VKvLYHtEEAqcJCSiqOz58+fP2CcEp3G5kN6N6VAiC6BaTR5a08b2/mOzNZS/MyXLrVcvbfuRlnqbgtTD0lgSgLJ2D3mojiu0eywno8pZ0Hn0lhGwRXdBXXZaqp6oO6upv02RAISCAJYvaTt0GBy83Yaa0unJtDqEO4dVxUhWk7hnboXvZ4EJCABCSyCAO/sax3f7a270ep94VqLYGMlJSABCUhAAlMTOER3W4V86gZ4PQlIQAISkEAuBOKFfRcvttTnEN0lUEs/cwtTD0lAAhKQwEYJ3Lmza/iw47sbRWmzJSABCUhAAkMQOMTebRXyISplGRKQgAQkIIElEogFHVvn7x6iu4zvEq5lkoAEJCABCUjgFIH793cfW8OgnL97ipgfDiBw85t0wImeIgEJSGCDBA7R3QjUImirVdE3CHOLTb527doWm22bJVAh0OUtFJWT3LE6Akhj85uIorWH6G6M7xK0ta/w1QG1QRUCrA9c2ecOCUhAAlsl0OGFfYeM724Vp+2WgAQkIAEJHEvg7IcffvjZZ5+Vivn1r3/9i1/8orjzxRdfvHDhQuyJQK2YHFzM47YEJCABCUhg0wRwBb/2WjuBs88+++xTTz1VyvS9733v0ukw6OL7KWNY96OPTra0iHqJkB8lIAEJSEACFQIdTNKzyYotnv3YY489+eSTxT1uS0ACEpCABCSwn8ATT7TnOXB897nnTjqIevulPSoBCUhAAhJYEYFY2gKBbE0H6u758ycdgrZar+xBCUhAAhKQwPYIHKi72wNliyUgAQlIQAIdCOxbS/lA3cWM1t7tgN8sEpCABCSwGQK3b++aOpLuUrK6u5lbyYZKQAISkMBgBA60dwe7vgVJQAISkIAEVkNgn7FLQw/U3Vde2UG6d281qGyIBCQgAQlI4DgCiOJ4uhtV++KL46ro2RKQgAQkIIGNETjQ3g1Fd4h3Y3eLzZWABCQggWYCiOK+ybucfJTuxvt9m6vgEQlIQAISkMBmCHQzRg/UXSg6lWgzt5INlYAEJCCBfQQi4umFF/blO9TepVyWrHKpyL18zSABCUhAApsgEMbuqHFV2LuxFOUmgNpICUhAAhKQQAsBXtJHGm98l8IvXtxdops3e5fTJAEJSEACElgtATzAHYxdmn/4+K4hzau9e2yYBCQgAQn0JYAZOrbuXrq0q5RLZ/TtGvNLQAISkMAKCdy508XJTMMPt3fPndtx+/zzFdKzSRKQgAQkIIEeBHAy87fvjfdR4OG6y/kvv6y926NfzCoBCUhAAuskgLFL6hBURa6jdBdXtktFrvMeslUSkIAEJNCdQOdJRBR5lO7ianZ8t3u/mFMCEpCABNZJIJZvHDuuCnaxLofSu87byFZJQAISkEBHAti73ZzMlHesvUsRupo79ovZJCABCUhgnQQ6TyKi+UfpLnFVpNu314nRVklAAhKQgAQ6EcDx283JTGlH6S7nd75Qp5qbSQISkIAEJLAwAhFUFYs4dqj6ALrr+G4HzmaRgAQkIIGVEugTzAyCY3WXgeSYtrRSnDZLAhKQgAQk0EogrM9YxLE1Yxw8VncpxbcBduBsFglIQAISWCkBFm5kWm0s4tihicfq7iuv7C7iCwE7oDaLBCQgAQmskQD2bmdjl/Yfq7vnz+8gavKu8VayTRKQgAQk0IFAn0lEFHes7sZE4Xjdb4famUUCEpCABCSwLgLobmcnMy0/VncpAumNYK51gbQ1EpCABCQggX0EIqgqlm/clzeOD6C7uJr1M3ejbS4JSEACElgXgZ6TiGj8ALqLvWtc1bruI1sjAQlIQALdCMQ4a4y5djtjAN2NF/1q8nYDbi4JSEACElgRAcSv58KNA+huyLyrZ6zoPrIpEpCABCTQjUDPYGYKHUB3Y9qSq0V26yJzSUACEpDAighgdPZxMtPyAXQ3wqdZr8MkAQlIQAIS2BABnMz8xWhr52YPoLtcixcCau92Zm5GCUhAAhJYBYEYYZ3e3gUeJu8XX6wCoo2QgAQkIAEJdCTQfxIRBQ9j7xLMpb3bsZvMJgEJSEACKyFw//6uIdPHM3PRWKlD6V3JnWQzJCABCUigCwHs3Z5OZkodxt6N0CpdzV26yTwSkIAEJLASAv0nEdHwYXSXuCrS7dsrIWkzJCABCUhAAvsJ4Obt6WSmzGF0l4L6vIxhf1vMIQEJSEACEsiaQARVXbzYt5KD6S6rZzi+25e++SUgAQlIYKkEDgpmprGD6S5Dy1GHpRK03hKQgAQkIIHuBMLWjCUbu581oO5yUXW3D3nzSkACEpDAkgmwTCMjrP0HWQezd195ZYfPFwIu+Say7hKQgAQk0JkA9m5/Y5fSB9Pd8+d3dfVtgJ17zIwSkIAEJLBkAvh4+wcz0+DBdDemDscLgJcM0rpLQAISkIAEOhBAd/s7mSl3MN2lLIRfe7dDX5lFAhKQgAQWTiCCqmKxxp5NGVJ3MXkd3+3J3+wSkIAEJLBAAvEmonn9zGCjApjdmrwLvIOssgQkIAEJ9CHAGxFwMvdfnJlrDGnvRkhzPAT0qb55JSABCUhAAosicGgwM40cUnddpXlRd42VlYAEJCCBQwkwqnqQscv1htRdiqMarp5xaDd6ngQkIAEJLIFAhDIdFFRF8wbWXeYQG1q1hLvGOkpAAhKQwKEEIpg5fLz9yxhYd3kxA3FVmrz9O8IzJCABCUhgIQQIqiKQ+KDJu7RwYN0N+Y9HgYXws5oSkIAEJCCBPgSIHz50cJfLDKy7URNXrerTgeaVgAQkIIHlEMCpi3F50MzdaOTAukuhmLzau8u5g6ypBCQgAQn0IRCTZWPibJ/zUt7hdReT19CqxNcNCUhAAhJYFYHbt3fNOTSoilOH111Cq0iavDsKJglIQAISWBkBIoePGNwFxvC6Gw8Brlq1sjvN5khAAhKQwI4AHt2DXrub6A2vuxFcTZS1SQISkIAEJLAqAvESgvDrHtqw4XWXmrh6xqHd4XkSkIAEJJAxgRhDPWJwl7aNoru4vh3fzfjGsWoSkIAEJHAQgZgmm9v4Lk2JRSuNaj6oVz1JAhKQgARyJYBNeZyxS8NGsXejVpq8ud441ksCEpCABA4igEF5nLHLVUfRXRatJLrK0KqDetWTJCABCUggSwJhTR4XVEXDRtFdyuWBQD9zljeOlZKABCQggYMIxATZPP3MNAh7N8KtD2qcJ0lAAhKQgAQyI4AXN9y5x9VrLHs3lq509YzjesezJSABCUggGwJHr5gRLRlLd8MQj2Uss2FmRSQgAQlIQAKHEmB89+igKq49lu5SNNXD1WySgAQkIAEJLJ5AhCwd8RqiRGBE3XXVqkTZDQlIQAISWDaBCGY+bmXmIDCi7hJrzeuBNXmXfatZewlIQAISgABBVfH6gaNpjKi7rp5xdO9YgAQkIAEJ5EFgiBUzoiUj6m4MP8dilnlgsxYSkIAEJCCB/gTCeRtrIPc/u3TGiLrLlTB5XS2yRNyPEpCABCSwMAIxKXaIYGYaPq7uUklXrVrY7WV1JSABCUigRCAmxR69UlWUOq7uhlGuyVvqQT9KQAISkMCSCAw0czeaPK7uxsPBO+8sCa91lYAEJCABCZwigOd2IGOXYsfVXVayxNWsvXuq//wgAQlIQAILInDr1q6yQ6yYEY0eV3e5Bo8IPCgQC2aSgAQkIAEJLI9ATMtZir0L33hEMLpqebeaNZaABCQgAQggYJcvD0hiCnsXb7OzeAfsM4uSgAQkIIGJCLDmImOlA83cjTqPrrtcBus83OMTYfIyEpCABCQggUEIhLd2OCczlZpCd3lQ4InBhZoHuQcsRAISkIAEpiNw9+5uWeaBVsyIak+hu+EYd4h3uhvFK0lAAhKQwCAE3n13wBlEUaMpdJdnBf5iuY9BOFiIBCQgAQlIYHQCjOwyG+f554e90BS6S41jNtGwVbc0CUhAAhKQwIgEwk975cqwl5hId3lc4KHBBTSG7TxLk4AEJCCBEQngp2Vklzk5g6aJdDceF1wwctC+szAJSEACEhiTAPbuoJHMUdeJdNcFI8e8NSxbAhKQgASGJhDzXweduRtVnEh3uVgM8bpg5NC3huVJQAISkMAIBGK9p0FXqopaTqe7Lhg5wn1hkRKQgAQkMA6BcZzM1HU63Q0nuQtGjnODWKoEJCABCQxHIJaHHO4dRMWaTae7XBV73QUji/TdloAEJCCBHAnEDKIRgqpo7KS664KROd5e1kkCEpCABEoEWB4y4oFL+4f4OKnuxqNDPEYMUXnLkIAEJCABCYxAgOUhh14uI9VyUt1l/rELRib0bkhAAhKQQI4ExlkeMrX07Mcff/z111+nz7Hx6aeffvjhh8WdP/rRj84NsWYHJi+PESYJSEACEpBApgTGHNylyWcvXLhQFdTvfve7Tz31VJHIn/zJnxQ/HrzNgpFvv71bMHLQtyodXB1PlIAEJCABCZwmEMtD4p4dJ+10t1ryY4899uSTT1b3H78Hh/nrr5/wMKHuHg/TEiQgAQlIYGACrO6ERF27NnCxheImHd/luhEg5kLNhS5wUwISkIAEsiEQQ6FXr45Xoal1l5bQHPzMTEo2SUACEpCABPIigJMZD/OYLtkZdDdWu3QBjbxuNWsjAQlIQAI4mRGnEdZkLqKdQXfjSUJXc7Eb3JaABCQggfkJjO9kpo0z6C5X1dU8/+1lDSQgAQlIoERgfCczF5xHd3U1l/rajxKQgAQkMDOBSZzMtHEe3dXVPPPt5eUlIAEJSKBEYBInM9ecR3e5sK7mUo/7UQISkIAE5iQwiZOZBs6mu7qa57y9vLYEJCABCRQJTOVk5pqz6a6u5mKPuy0BCUhAAnMSmMrJTBtn012urat5zpvMa0tAAhKQQCIwlZOZC86pu7qaU4+7IQEJSEACsxGY0MlMG+fUXV3Ns91kXlgCEpCABBKBCZ3MXHNO3eXyuppTv7shAQlIQALzEJjQyUwDZ9ZdXc3z3GReVQISkIAEgsC0TmauObPu6mr2zpeABCQggTkJTOtkpqUz6y410NU85w3ntSUgAQlsnMC0TmZgz6+7upo3fs/bfAlIQAKzEZjcyUxL59fdcDW/9dZs2L2wBCQgAQlslMDkTmY4z6+7VOL1109+85uTe/c22u82WwISkIAE5iHw3nsnYfxNePksdPfKlZNz5040eSfsdy8lAQlIYPMEMPjef/8kBjsnhJGF7iK6SC/mPp52kwQkIAEJSGAKAmHt4XGdNmWhuzSZhiO64WmfloBXk4AEJCCBTRJ4++2dsYufedqUi+4+99yu7bqap+19ryYBCUhgqwRu3dpZe8xknTzlors0HJOX0Cqjqya/B7ygBCQgge0ReOednbU3+eAuoDPS3dde23W8Ju/2bn9bLAEJSGBaAkRUYe/OIbq0MyPdJboK6TW6atq7z6tJQAIS2B6BmSKqAnRGukuFXn3V6KrtfQFssQQkIIGJCWDsvvzy9BFV0cq8dDc4MI/ZJAEJSEACEhiFAKKLnxk7b6aUl+4Cgegq5jHDxCQBCUhAAhIYngARVTGuOXzRnUrMTndjnNvoqk69ZyYJSEACEuhFICKqIo6314nDZc5OdyOum9nMJglIQAISkMDABHAykyZfo6rYiux0l8oxj5nZzEpvsZ/cloAEJCCBAQjgTZ0voirqn6PuxrpdRlcNcIdZhAQkIAEJJAIRPTRfRFVUJEfdpWZIr9FV6VZxQwISkIAEBiCAPTdrRFU0IVPdDd/7jRsDcLYICUhAAhKQwG6eDOOXs0ZURS9kqrtEV7l2ld8TCUhAAhIYjMCsa1QVW5Gp7lLFN97YRVe9+Waxtm5LQAISkIAE+hOIYF3sOay6uVO+ugscgs6Map77DvH6EpCABJZPABsO6Z11+lCCmK/uUsUweZXe1FtuSEACEpDAIQRiQWbe9J5Bylp3Y5KV0VUZ3CdWQQISkMBiCWC9EVSFJZdHylp3QQSoWNUrD1zWQgISkIAElkYA6y1GLvOoeO66G6PgLtecx91iLSQgAQksjUCsBZGNsQu+3HWXKsYaGqAzSUACEpCABPoRwNjNYK2MYp0XoLvXr++guWxksdvcloAEJCCB/QTu3dutfZjBWhnFqi5Ad+NJJcbFi1V3WwISkIAEJNBGgEFKJATrLae0AN0Fl8tG5nTPWBcJSEACSyAQC0NeubKT3pzSMnTXZSNzumesiwQkIIElEIiI3JwiqoLaMnSXumLyumzkEu506ygBCUggAwI5LQxZwrEY3WWZkVg2EpgmCUhAAhKQQBuBWBhy7lft1tZwMbpL7fEWaPLW9qI7JSABCUjgWwJh7DIJFXMtv7Qk3QUgGAls1uTN70ayRhKQgASyIRDGbq6LDC9Jd+lSMGryZnNrWxEJSEAC+REgjPnmzd2c3Qxe+VdLZ2G6G4HNzuWt7Ut3SkACEpDAzj4j5RfGnLpmYbobMDF5c/UfJLBuSEACEpDA5ARizm7Gxi5Elqe7mryT38heUAISkMBCCGRv7MJxebpLpcN/oMm7kO+B1ZSABCQwCYElGLuAWKTuavJOcgt7EQlIQAKLIrAEYxegi9Rd6q3Ju6hvg5WVgAQkMDKBMHavXcs2jDm1f6m6q8mbutANCUhAAhLYRdvm9+qh2n5Zqu7SmIDsKG9tv7pTAhKQwIYI8JJdJpgSxpzZq4dqu2DBugteIIMa4CYJSEACEtgugeUYu/TRgnWX2vMyY9RXk3e7XzZbLgEJSODWrZ35tRBjl+5atu6G6IaDwXtPAhKQgAS2SIA4W0J+lmOBLVt3ucN4xOEVgWD3ZQlb/L7ZZglIYOME+PUnkjlecb8QFIvXXTjzlOPLEhZyv1lNCUhAAsMR4KefGB/eVZfl+/6a2rkG3Y33A/L+CR56TBKQgAQksBUC4epclLFL16xBd2lGOPbpApMEJCABCWyCQIT2LGGhjFJ3rER3GVMHfgS1lVroRwlIQAISWCEB7C1ia5nWsrS0Et0FO/BRX03epd2B1lcCEpBAfwKxdEPMJe1/9rxnrEd3ee5BdO/d242ymyQgAQlIYLUE4h3s4edcYCPXo7vAZ04RMVYx0L7AvrDKEpCABCTQgcCbby5u7lCxVavSXRoWokunmCQgAQlIYIUE4r1Dly8va+5QsSPWprvYuwRYMafIRZuL3ey2BCQggZUQeP31XUOWszpVFfvadJcWxkB7dE21we6RgAQkIIGlEgijKsJol9qGtczfLfInwOrnP985/41tLmJxWwISkMCyCcTChKwMjFdzyWmF9i7dgeefPx6MCG82SUACEpDAGgj89Ke7NYGxqxae1qm7dErMqNbkXfj9afUlIAEJfEOAmB2WRsLSxd5deFqt7jKziyEAegqr1yQBCUhAAgsmgJlLzE78rC+4GQ+rvlrdpX08GBHeHBO9lt9TtkACEpDAVgmkCbvE7yw/rVl36R28zTwn6W1e/o1qCyQgga0SCL9lrIu0CgYr190IfGNQgD+TBCQgAQksjwCWE2bukifslpivXHdpLZ2F+kYcXKnxfpSABCQggawJILrMSyGGeRUe5kC9ft2lnXQZ3mak1yQBCUhAAoshgOISGRsTQxdT6f0V3YTuJm+zsc377whzSEACEsiBANbSq6/uzNzlT9gt4dyE7tLm8DYb21zqfj9KQAISyJRA/F6/996aPMyBeiu6S2vpPpLrNkfH+68EJCCBfAkQCot/MiaD5lvLA2u2Id1NK2k4rejAm8XTJCABCUxAIOJxGCBk8aM1pg3pLt3Hw5PrNq/xNrZNEpDAigikdZhXFMNc7J5t6S4tj3B0pxUVbwK3JSABCeRCAPfyWtZhbkK6Od2N4Dii0xmzN0lAAhKQQEYEeIErP80s8LuiVTKqeDenuyDA1cyKY/FQVSXiHglIQAISmIcAE4dIb701z9WnuuoWdRe2aRErnq5MEpCABCQwP4FYmopfZ4JgV502qrtpKnY8Xa26i22cBCQggewJxMQhXJH8rT1tVHfpVmLUea5ioNdpRWu/yW2fBCSQNwF+iAl2jR/lvGs6SO22q7vgi0crB3oHuZMsRAISkMAhBNLq+WtcmqoWyKZ1FyIO9NbeFu6UgAQkMBGB9MahtQ/rJp5b110HetOt4IYEJCCBqQm8/fYJf7Gk0dTXnu16W9ddwDOmwGIajC+4dPNst6EXloAENkgg4ms2M6ybeljd3aFgRi/PW/HgldC4IQEJSEACYxEoDuuOdY1My1V3H3ZMDPTGQEOmfWW1JCABCayGAAHM2Ls4GzczrJu6Tt1NKE4++GC3/dJLJzyHmSQgAQlIYCwCmDhM2MXcwdm4vXT2448//vrrr0sN//TTTz/88MPizh/96EfnVvpqiNRM2of0orv83b2bdrshAQlIQALDEWBIj+mbzONkeG+TSXv3VLenxTSMsTrFxQ8SkIAEBiGw1ViqIryzP/7xj4ufY/v73//+iy++WN2/hT08hN2/v3sau3hxs09jW+hn2ygBCUxOgDE83IkkXItrd6C2wD3bcmyzhxh04H0JDEBg/vJCKpMEJCABCQxAIMJnGMbbsOiCUT9z/b1EkB2iy1sTcIqYJCABCUjgWAKM3vF7yjv++G3ddlJ36/ufpzGkl0Ssu+HN9YzcKwEJSKAjAYbuYl2qDbxuaC8SdbcREc9kLNPN81m4RhrzeUACEpCABFoIoLiM2zFliDE8k37m9nuAwV2cIhF/157ToxKQgAQkUEOAH1A8zNgx4UKsybG5XcZV7enyFN5MPjTYJAEJSEACXQmEw5AVqbYdwFzCpe6WgNR8xDXCEC+ekuef3031NklAAhKQwH4C/G4Sm0razIt19zP5Joe62wkUli4zi2IxDaW3EzIzSUACWyYQU3X53WTW0OYDmEs3gnFVJSCNH3li4+bxxQmNgDwgAQlIIBGI1x44aygBKWyouwUYrZuxevP587vwZsYsTBKQgAQkUE8A3yCvPUB0dQ/WAVJ366g07EN6sXpJSm8DIXdLQAKbJ4DoEg6D4iq6DfeCutsApmE3ruZ4XaDraTQQcrcEJLBhAgzFheg6/aP5LlB3m9k0HAnpJVzA9TQaCLlbAhLYJAEUN17wp+i29r+624qn4SDSy+SimJnmKpINkNwtAQlsiQCii4c5FhvaUrsPaKu6ewC03SmMXPBIp/QeiM/TJCCBNREI0cUiiRCYNTVthLaou4dDTdLLWK9JAhKQwEYJvP/+w5UgXZSq2x2g7nbj1JArpJeA+VhSoyGXuyUgAQmslABOPxalirCXbb9Vt3sHu15Vd1b1OSNUPnTXYIJ6Ru6VgARWSSBG2lx+uWfnqrs9gdVlV3rrqLhPAhJYNQFF99DuVXcPJXf6PKX3NA8/SUACqyag6B7RveruEfBOn6r0nubhJwlIYKUEFN3jOlbdPY7f6bOV3tM8/CQBCayOgKJ7dJequ0cjPF2A0nuah58kIIEVEVB0h+hM5xENQfF0GUgvgc3MIye63tWsTrPxkwQksFgC/Kg9//yJ0ctHd6D27tEI6wpIVi/LODuVvI6Q+yQggUURSCtS+Yt2dL9p7x6NsKGAsHrDK6PV2wDJ3RKQwBIIKLqD9pK6OyjO04Upvad5+EkCElggAUV36E5Td4cmerq8kN54aSC2r0kCEpDAkgiwFB9//JDpXh6u29Td4Vg2lBR3rNLbgMfdEpBArgRQXIzdsB5ce3m4XlJ3h2PZXFKsGc7xl17avTrQJAEJSCBrAsSkFEU367our3Lq7kR9FtJ7/vwuDp8nSJMEJCCBTAkgupgI/E5du7abE2kamoC6OzTR5vKQ3rt3d+/LiufI5owekYAEJDATgRBd/HIo7o0bM1Vi5ZdVdyftYIZIiE54+eWd9PJnkoAEJJARAeT26adPiEZ5773dsK5pHAKumzEO1+ZSkd7bt781efXiNKPyiAQkMCEBRBf3MgnjAL+caTQC2rujoW0tGLll6IQBFIZ7XVWjFZUHJSCB8QnEj1GsAanojsxb3R0ZcHPxDJ2gvvGIyb8mCUhAAvMQeOONnQsOudXSnaQD1N1JMDdchAEUfM5O7W3A424JSGBkAjFf6OZNV8YYGfSp4tXdUzim/0CMFY+Yzi+anrxXlMDWCZTmC7kyxlQ3hLo7Fenm6+DdSfOL8PeYJCABCYxOgMEtokv41/lCo7MuX8B45jKRWT7zoIn0MsKCv4dnUIZ+ffScpSO8qAQ2QeDWrZOf/nTX0njk30SbM2qk9m5GnZGCnAnmZ9DXJAEJSGB4AnjVXn119/p6RXd4uJ1KVHc7YZosE5YuE9YRXTxA778/2WW9kAQksAECKYrq8uVdXAnSa5qDgLo7B/XWa8Y3gkirV17ZuZ1NEpCABAYgEBMnYtVlnu4dyhqA6YFFqLsHghv1tIi0ItQ5ptXxkGqSgAQkcDgBvGf40JBeFNdVlw/nOMyZ6u4wHAcvhYdRpvbGmlYO9w6O1wIlsCEC+M3wnsVaVPjTTHMTUHfn7oHW68eaVg73tkLyoAQk0EAAXxkhVPjNYvjKBSAbOE28W92dGHjvy7GmVQRA8MDq7N7e+DxBApslEIvQMmUIv5kDujndBupuTr3RUJdYNpUH1nAXOdzbwMndEpDAIwLET8UAFeNVDug+opLJ/+puJh2xpxoM90Y8BOERvB/T9yjs4eVhCWyWQEwWYhWemKFLfKYpMwLqbmYd0lod3EXMdI/FnJ1i1IrKgxLYJIHiZCF+LJyhm+VdoO5m2S3NlSpOMSJgQp9zMyqPSGBjBOIduk4Wyr7b1d3su6hSwTTFiIAJfM4ua1Uh5A4JbIxAybfsZKG8+1/dzbt/mmtHqET4nI1zbobkEQlsgEDELcdCVPqWl9Dh6u4SeqmhjuFzTnHOuJdMEpDAtggQ6OFCVEvrcnV3aT12ur4pzvnOnd23D8+zSQIS2AQBfMuxJgYRy598slsZw7QQAuruQjqqtZrEOcfaGnwNmT5gsFUrLQ9KYPkEIrgj1sRghi4P4KblEFB3l9NXrTUNn3Os5+w7BFtReVACSybAY3W8QJcJhYzmuibGAjtT3V1gpzVXme8gz74kg62aIXlEAoslECFUjOnGXH4et00LJKDuLrDTWqvMWA8PwRFsheHrylattDwogeUQwMwthlDpW15O15Vqqu6WgKzhYwRbsa5krF3jylZr6FTbsGUCPD6juHyTeaA2hGr5d4K6u/w+bGgB31AM30uXdoNBuJ2dZdTAyd0SyJtAminEMJKvFcq7rzrWTt3tCGqR2VicNV5GErOMNHwX2YtWerMEeFiOSI0YPWJM17QKAuruKrqxtRERgaHh2wrJgxLIjECYuTwyR7SkbzjIrH+OqY66ewy9xZyr4buYrrKiEkhmLg/LjBVp5q7ullB3V9elzQ0qGb6GOjej8ogEZiJAOMbFiyeauTPhn+ay6u40nHO5Ssnw5TtukoAEsiCQgpYdzc2iP0ashLo7Itxsiw7D1zm+2XaQFdsWgViCKs3NJRjS0dxV3wHq7qq7t7lxfK+ZkhBzfPm+Y/i6qnMzLY9IYDQCvEDbubmj0c2zYHU3z36ZqFYxC/+113Yz8vnu+zqjibh7GQlAgEdd3mTCTCESNq5zczdzV6i7m+nqhoayuNVbbz1c1ZkfAf5cYaMBlbslMBwBHnWffnr3qBujPozpmjZDQN3dTFe3NpRv/f37u18AfgfC6dWa3YMSkMChBIifitUwYpoQ03NdaflQlgs9T91daMeNUm1+AVDfWGED9WXgySQBCQxGIMVPpWlCvlBoMLhLKkjdXVJvTVDXmGgU8VY8lL/+uvFWE1D3Ehsg8PbbO8cy7mXiKXi3Ac4l01YJqLtb7fnWdke8Fb8M6beiNbsHJSCBZgLhWOYZNp5qiafQsdxMawtH1N0t9PIhbeSXAbdzeqORbudDIHrOxgmUHMt8nYyf2vgt8U3z1V3vgjYCDD8xwYEH9Fgy1mjnNlgek0CRQHIW6VguYnH75ETd9S7YTyD9bkS0s4ts7Edmji0TICIxgiN0LG/5Nmhuu7rbzMYjBQLhdibaGT9ZLLLB07xJAhI4RQC/EOO4iC4Ry7iJdCyfouOHhwTUXW+FHgRidUk8z6T4efGlRj3wmXXFBNJQLg+kRCQSsYybyCSBOgLqbh0V97USiEU2iLrimZ54KwTYJa5agXlw7QTQ2lhuhsnvOIVcCmPtHX5k+9TdIwFu9/R4po+5RvzmOOi73Vthyy2Ptxrw7MlIDI4g/nAKmSTQSkDdbcXjwVYCpUFfVgVw0LcVmAdXRAAnT7zVgA2HclfUsRM0Rd2dAPLKL5EGffGx8dx/8aILTK68x7fePIZy043uUO7W74ZD2q/uHkLNc6oEGPSNmb4cIpyTP5d3rlJyz7IJRPBUOHYImyJc2aHcZffoPLVXd+fhvtar8lsU7zUi5ArpdZ2NtXb0FtvFIEossBzBU/iWHcrd4n0wQJvV3QEgWkSJADZALPyOyYvb2YDnEh8/LowAihv3MYpr8NTCOi/H6qq7OfbKCuqUlnfGAo5JFgY8r6BbN9eEFK5My3lLF6LrAsubuwmGb7C6OzxTS0wE8MPhjcPzfOXKbpUrvHSqb4LjRtYEUNyIU2BMN25i3tJlksAQBNTdIShaRiuBUN94tVFS39YzPCiB+QgkxY230/PY6MpT8/XGKq+s7q6yW3NsVLzaCEcdY2SoL+NlTvbNsZ+2XKc0JRfFTevCbBmIbR+HgLo7DldLbSAQ041QX4zgmAOp+jagcveEBFDc0pRcJwhNiH9rl1J3t9bjWbRX9c2iG6wEBJLivvvuQxtXxfXGGJmAujsyYItvJpDUlyzavs2cPDIOARV3HK6WupeAursXkRnGJYD6ErlCxChJ9R2XtaUHARXXO2FWAururPi9+CMCsdAVMyQd932ExP9HIMD7ouPhTq/yCHQtsiMBdbcjKLNNQYAZkmk5oPh5dL7vFNy3cI2YHcQbK1XcLXR33m1Ud/Pun03WLo37Yvum+b6sXmCSwCEEivNxY3aQkVOHcPScwQiou4OhtKBhCST1jfm+rnU1LN5NlHbr1sM1p9J8XBV3Ex2feyPV3dx7aOP1S+obK00+/vhueI6wGJME2gjEmwzifVjE7PGaDhW3jZfHJiWg7k6K24sdRgD1jSVy4y0L8W4YQmRMEjhFgNGIWAuNpzNSuml4TYdJAtkQUHez6Qorso9ArPMc7/clOIYQGRauZ/DOJIGT9EZ6IvHSjeK6yt4ZWRJQd7PsFivVTIAfVVyG8X5fhu3inTEuNtkMbO1HYjJueiN9BMSruGvv9kW378zvf//7P/zhD8U2/OAHP/jZz352/fr14s4LFy585zvfKe5xWwKzE8DIwfBFhvntRY9xLvJ7q09x9n6ZqAL4OpjxHc9cdHxYuhNd28tI4HACZz/77LOvv/66VAB7fvvb3xZ3PvbYY+puEYjbORBAYvm95Y/fXn6B+eF9883dRwQYGTatlkD0N7rLHcDUIPt7tT29zoadefDgQallZ86cuXbt2g2MCJMEFkWA32EiaZg8Qgr15eWDpvUQ0L+xnr7cdEsc391096+s8YQ9Y/XGe8pT4JVDv2voZQYS8GYwiItpi40bgcpYug4qrKF3N9cGdXdzXb76Bkc0awReRcwN846YXYKxZFoeAZwYaG10IZO4CZu6e3fnzTBJYLEE1N3Fdp0VbyWAIcRQSbzpCCVOxhJKbFoGATwVMVcsVlSOvsSnYZLAwgmouwvvQKu/jwCmUUwtwVjilxzDialHMQa871SPz0EgXMqxMhk+ipg0xr9Gys3RG15zDALq7hhULTM7AmnFK8YEmfXLAoIIMDKs8zmjriq6lNNYvYO4GfWQVRmGgLo7DEdLWQQBTCYMpy+/3MXl4Ihm3DAidXQ+z9l9PPtUXcoEyPFWSJME1khA3V1jr9qmfQRwPhOdg/+56Hzmx980KYFwKcezD+rL01C8wECX8qTd4MWmJqDuTk3c6+VDIJzPmL/4MpGAwZl4yAAAEcZJREFUCJslAkvzd/Q+YoA9fP0EmkeUckz/cl7Q6Oi9wPwE1N35+8AazEsgRT7j2sTQivfZIAqMNpoGJhAGLkPrwZfnHaOUB0ZscQsgoO4uoJOs4jQEGE/E84wQIAeILmHPMWvU2KsB+Bdjpni6waWMn8Eo5QHIWsTyCKi7y+szazwqgWLsFdu4nWNKi+bvIdh5ZgkHAk8xaRouTzcufHEITc9ZCQF1dyUdaTMGJxATf2NxJCRD87cfYUZwGTDnmYUnFw3cfuzMvXIC6u7KO9jmHUmANytEmC3/Fs1fV96oB1scwdXArWfk3q0TUHe3fgfY/i4EiL0K8zdGfxGUiMY1+PlbesUQZQ3cb7m4JYEyAXW3TMTPEmghgKCklTfYTsHP2537GwYu/mSeRO7d28Wk8WziCG7LPeShzRNQdzd/CwjgIAJF8xe5SUOZbG8ipUWmIuY7LetoiPImut9GHkVA3T0KnydvnECYvxh4zP1l+QfMX96gwx8bq519FDOCiu8tiPa7rOPGvww2vzMBdbczKjNKoJkAopOmpJIrZh/heV1P+FUKmIoZQdj7hHrHcDdPHyYJSKAzAXW3MyozSmAfAcKvGN9Ej/iLxTeQ3phKs1T/c/iT0yQqJDaFdxPqbZKABPoTOPPgwYPSWWfOnLl27doNxmlMEpDAcQSwd99556HVi05dvbp7y84y7EOqTnhUBIxRY0awF1P14/rMsyUwMgHt3ZEBW/y2CSBVDP3Gmwchgf85FidGzjIdAMYwT7VkvpT+5G3fwLZ+DALau2NQtUwJ1BNgkBQ3LZYkGzilCcXCg4s2z5+oUNjm4RCnTtQM0TVJQAJDE9DeHZqo5UmgmQD+WgZwYoIroltcf2OeAeDi8C1mLinCwzDSFd3mfvSIBI4hoO4eQ89zJXAggfTqX2SOcd+YgIQLGu3D8pwiYd1G0BcDt1wy1ruI1aixxE0SkMBoBPQzj4bWgiXQmQBmJ7YvRma89WjECKyIluJiXDJWvyTWy8jkzj1lRgkcT0DdPZ6hJUhgMAK1w6x4pI81QfFiE1cd0Vx5DSwPhs6CJLAUAuruUnrKem6LAAKcIrBoecQ59RbgkNuI44pSYibTtljaWgnkRUDdzas/rI0ESgSq0rk/0Lj2nN6iXaqIHyUggWEIqLvDcLQUCYxNYL+reCwn9dgts3wJbIvA2W0119ZKYLEECH7ij2lIKTSK4VrGan/2k3/4d3/07o///p3da/hIkQnH9DKWxVpsf1hxCRxKQN09lJznSWAmAkgqf2+98ZuP/9ut7/7vd576Hzu5/dXZ537z5zf++D9c+cnPfjhTvbysBCTQiYC62wmTmSSQC4HC2O2PqRPW7X+58b8eu/I//+8Pd5OD/vPJuf/6cBkspggfGwWdS5uthwRWRUDdXVV32pjVEijI7a6Np53JPzk54S/in3mXAQIcrzM4MAp6tRBtmASyIGBcVRbdYCUkUE+gJLedhZQx4I8+ergQNCV3Pq++Fu6VgAQGJKDuDgjToiQwEIEUOhUvLTpCNkvCjfM53sRg0NVAXWUxEuhNQN3tjcwTJDAKASSWVSLDTRyLODLj9vnnd6O1Q4zThgBzBTZIOKpZQgMZZsMkAQlMScDx3Slpey0JVAggsYzHIrfYuKTRFnFEX0Ni0yzfeP8Qhi/mtIs0VzrGHRIYi4D27lhkLVcCbQSS+oX5GeqHCxgLdKpEFcLADsWnCskLPVUVvI4EtkhA3d1ir9vm2QiUhluxQNG6uY3NZHIjw+HhDgEeyMM9G2wvLIE8Cai7efaLtVoRAaSsOHBLy3DsvvDC7t/8opsioov6Yg2TeDCINynkV9MV3SE2ZWMEHN/dWIfb3MkIlNy4MXA7XJzUSO3gYYA/UorDYhiYvzkc4SM10WIlMDMBdXfmDvDyayOQ9CoN3F67tpu7M+HA7SBIS3FYzAa+eXP3t5Dnh0EYWIgERiGgn3kUrBa6LQJVT3IMkPJvxBCvAke1leGFXlcrV9FVNiJvAtq7efePtcuZQMQkMwWI4VDS2i1B2hdeaBakTFZ9mowUTxrho86506ybBGYnoO7O3gVWYGkECD0qrsGI0bdMT/Ix3Ite6BQ0lhaFzjVo7JgWe64EBiOgn3kwlBa0ZgJh2obcRjux7GLU1kjfRx1feiCJUKzQ4EdZ/F8CEjjR3vUmkEAzgaqSYNqqJA3Awgt948ZuDlKQIw4rEoc0ghuwuXtzBLR3N9flNngPgSQaSEckRWMPsrbD1UeXhLPtNI9JYL0EtHfX27e2rDuBFKqbFozASapp2x1gc869RrDh0M3wPLJOAuruOvvVVnUiEFG5/JsCkhEBInT511HbTgR7ZIonmTghjGCoh0+BQxEOzb9ETZsksG4C6u66+9fWVQjgRo4AXP7FzCVtMiC5wmXSHWEEc8nUG7yTKcKh6Y2kwZPWyYtJYCoCju9ORdrrzEgg3MjFyT9hYWW/auOMzKa/dK33gWgsZBgxNklgNQS0d1fTlTakQiDs2uRG5jh21uuv+0NeIZXFjjQnuPiYVHRE+5iURT9ZiaMJaO8ejdACsiIQKotpW3Qj67jMqo/6VCY5okv96WSuPhTNmxcB7d28+sPaHEIgfpvv3t1pLdsk3Mi8PDbWtTBQ5xCmuZxDT7722u6PFM9UrMuZpgWHC3qBb53IBa/1mIWA9u4s2L3o0QSKvsjQ2rQ8Mj/G/FqbVk0gxhD4FzEm0fl0u4PBq+7z9TROe3c9fbn+liStLf3cOmS7/r4vtxCV5Y9UvCliMDhpMKP5PoCVwfk5AwLauxl0glVoJxCmTTE8il9cgnB0L7Zz297R2gEHbhYCsvhXDd7eHZFpi7V3M+2YrVerVmu399qfrd8GPdsfg8FxUtLgNDOYo2pwT6JmH4WA9u4oWC30EAJVrY01FAxdPYSm53xLoBrkrgZ/S8etyQlo706O3AsWCdRqbSyMjG1iKHKRlduHEkgzgykgabB28KE4Pe9YAurusQQ9vzcBtbY3Mk8YjIAaPBhKCzqUgLp7KDnP60VAre2Fy8yTEFCDJ8HsRcoE1N0yET8PQ6A4vSOmWFIurmN9yMPwtZSBCdRqMI+L8baGNDeJW5icJgkcQ0DdPYae554mQAgpEhtrNJa01jk/p1H5KWcCRQ1OcdFocHF+MJFZ3tQ5d2LOdVN3c+6dJdQNfb1z56S4RmOYBtgF/iwtoQOtYzuB6tyk+/d3C5KiwbFcZVjA3OyXLhkI2M7Sow8JqLveCv0J8KuT7Fr8ySR+nLARXDeqP0vPWBCBogangRS+CghwaDBfAmSYiW9skNkkgVoC6m4tFneeJlD8jUF0I/HTwrsHXAroNCo/bYQAbh3WoeQvEl8LXtgQGhx70F00+OLF3b98V0wSSATU3YTCjdME0rgWbmR+TiLxE2Jg1GlOfpIABPhm8BeJrwsyHHEOxbAslNixF+8WCKi73gYFAlUHsoO1BTxuSqALAazbZODG42vtkLDu6C4wV5lH3V1lt3ZuVIpAjkf0OI/H8nAgEyiSfj86F2lGCUggEeDLFC8PZk9xuCa9Qlh3dGK1nQ11dzt9/ailIbHxBI7uRtKB/AiP/0tgJAKlIeGqO5rr8kXkWTdeJEx+0yoJqLur7NbTjSo+ZqeoKB6z+X4bgXwalZ8kMBkBvn/JnVR0PGkKT9YFc11I3Z2L/MjXbTdq+bqjuyYJSCAPAnwd+StGR/MNLkZmUU1N4Tz6aoBaqLsDQMyiiOIDc9WoRWhTtGUW1bUSEpBAIwG+rOn7WvxmF01hvtMRmZVyNhbngcwIqLuZdUiv6qCvzBmM7yX/RuJbGFN9NGp7wTSzBLIkUGsKpwDpqHI8V8dcYT1ZWXbjqUqpu6dw5P4B31MsylicU1v8zrFtkoAE1kugaAoXIzeYKMxHEtFYMRHBpSuzvQvOPHjwoFS5M2fOXLt27caNG6X9fpyBQNHHlLzHMaeWx1q/WDN0iZeUQKYE4rE8TGG2I/E7gVRjCsfzeaZV31i1tHcz6/DiEyxGbTzBUke9x5l1lNWRQG4EUNaiw4sH9WpwVqgvMuzk/Bm7T92dEf6jS6fvB9+SNEzL98PFKx4R8n8JSKAvgb0eaQokD780rpzVl+2R+dXdIwEedHoILf6g4jCt/qCDWHqSBCSwl0BpyQ4e7/kR4heIR/0UI50Ghl21Yy/PIzOou0cC7HY6dzcSG7c593ukuM2JPXaYthtFc0lAAoMQ4CE/rV5JgfH7xEu0+ZVKMkweTGH+9fdpEObFQtTdIo3httuFVrfOcKQtSQISOJIA+spfSkV/3K1bD5VYGU58jt9Qd49n+E0JCu1AIC1GAhKYl0BxYJiaKMODd4e6eyhShfZQcp4nAQksiEBVhmO1HgaJtYYP60d1tzM3nvq40ZrGaHUddwZpRglIYLkEqjKMDRJBospwx25Vd5tBFd0r3FmRUjCUQttMziMSkMBGCHSR4WKkdMRqbQROUzPV3UdkWKGCYL7kQElCSzgBf654/IiT/0tAAhJoItAiwylSOsnwZpfv2LDu4jRGXHnVVmzwb6SI2+P2MXy+6bvlfglIQAIdCJRkmF/cNKEyyTDFkA07+Ikndv+yvfq0Jd0t9jmdn5ZgjK6OBUxZPI2HMZMEJCABCQxNgN9a/lLiJxl7B9uHjfReB46SJ36J12r7rPq9CGmAlr5lO6V4uEJoMW238HCVGu6GBCQggVwJhOex1gUZXsjVBNWsyN5t6jQjoXL9mlkvCUhAAokA4srf5csPd0TIDaZwBEuvyS+9ZN0tTuyp+o2R27U6KdJ96oYEJCCBlRLgJxx3ZNEjWRwqLPmlQ7P5yY+NzJEsR3cxZ/mrxhuHOctio/qNM7/XrJ4EJCCBIwiUhodLmpBmD4cmkDlbTchYd1vMWR5peApayrPNEfeZp0pAAhKQQC2BMG2LBnGE9Hz++an3LHEuGpyVQZyN7raPzmrO1t537pSABCQggUcESn7poqqwnQxispMTMZ5r5lK97v7rv/7ro4aM839xxBwexWDjcCUwtr49c/arb6Y2ncNLYpqWwB/+8Id//ud/lvy01B9e7bPPPrtw4cIsl974RX/3u989+eSTK4YQNm4K1KKlRS9qcYQ4ciI+hEyzzcaoqV53+Q0a+KrF5hZjoKK5rAaFJ54ZW2M3d+BWDVzc3/3d31Hiiy++OHC5FrePAD/9v/3tbyW/j9Moxz/88MO/+Iu/GKVoC20l8Itf/OKv/uqvWrOs7WDRKU3bwgAkaig2iiHTaFGaQxwyNSCLet099gJpOnTY+fybEu3GaRzmvYtUJCxuSEACEpDAtARqQ6bRq1jKI6zFJMZF13QX7bp58+Zrr71W60UbQnepJn/VSGMI8sxAZWnc9pzG094/Xk0CEpCABI4lgGTx18U1vTdqGsV9+umnkd7r16+X1Le/7iaVxTJnuzg0G35xhHYaH/mxhD1fAhKQgAQk0EYAQSum8Ejj0q1GTYc7GtmO+Uso7t27d7F633777ZL67tPddpXlOjE0y0apdsWaui0BCUhAAhJYPoGqa7okkskvTVt/+MP//t3v/qevvvo/N29+9Ld/+2//8i//PGzfM7///e8J5izS+I8/+MG/v3Dhxe98509/97u0/4tz5/j79Hvfi43/99RT6ZAbQxH49NNPKer73//+UAVaTkcC//RP//T1119LviOuYbP9+te/fvbZZ4ct09K6ELh3795zWGemoQl8/fW/+eqr8//wD8/+y7/88e9+971//Mdv5fLs2V89++wfnfnlL39JMGfxuh9cv/7Xf//3d05OeNX759/8W3ilQDGj2xKQgAQkIAEJtBE4f/78F1/88OTkmZOTF/j37Nk/rXkfEWH9Tz311LrndbVBmu8Y5Lm4s1mm7wEmMjqPaHrscUVmsziPaBb4f/M3f7O1eUTTc2Z894033ojrEl0VA737xnenr6ZXlIAEJCABCSyfAJ78EN2kuBHYrO4uv29tgQQkIAEJZEaA9QdfffXVkuJGHdXdzPrK6khAAhKQwPIJYOlevny5OnmXlqm7y+9eWyABCUhAApkRuHHjRniVq/X6/2BMw8PfifATAAAAAElFTkSuQmCC)"], "metadata": {"id": "oK5I6X_5_jDn"}}, {"cell_type": "markdown", "source": ["The LOSS function can then be boiled down to:\n", "\\\n", "\\\n", "\\\n", "\\\n", "**LOSS/COST = - (Y * Log(Prediction) + (1-Y) * Log(1-Prediction) )**\n", "\\\n", "\\\n", "Y = Actual_label\n", "\\\n", "\\\n", "When Y=0, we eliminate the first term, and select the second term.\n", "\\\n", "When Y=1, we eliminate the second term, and select only the first term."], "metadata": {"id": "twL_QStMewPl"}}, {"cell_type": "markdown", "source": ["We can then use **gradient descent** to optimize the loss function."], "metadata": {"id": "CH_LvhIRfogj"}}, {"cell_type": "code", "source": [""], "metadata": {"id": "GmZvMwbPABjY"}, "execution_count": null, "outputs": []}]}