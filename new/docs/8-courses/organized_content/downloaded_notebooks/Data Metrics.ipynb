{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "collapsed_sections": ["0jWKVoOvSpcf", "dxEzRM8eSvu-", "hSthrInZS2tM", "G3v2O6EKTE8U"], "authorship_tag": "ABX9TyO8cd8CZIp65DVfH2yeMiZS"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# Import Libraries"], "metadata": {"id": "J2RvC_T6SjVK"}}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "RJ2uWbqnR3m-", "executionInfo": {"status": "ok", "timestamp": 1689487132129, "user_tz": -330, "elapsed": 7, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}}, "outputs": [], "source": ["import numpy as np\n"]}, {"cell_type": "markdown", "source": ["# Create data"], "metadata": {"id": "0jWKVoOvSpcf"}}, {"cell_type": "code", "source": ["# Create y array (ground truth)\n", "y     = np.array([1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 1])\n", "\n", "# Create y_hat array (predicted values)\n", "y_hat = np.array([1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 0])\n", "len(y)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4cqVQNZwR6TX", "executionInfo": {"status": "ok", "timestamp": 1689487702318, "user_tz": -330, "elapsed": 380, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "1309664d-09cd-4a2c-9f12-daf3daed0707"}, "execution_count": 15, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["20"]}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "markdown", "source": ["# Metrics"], "metadata": {"id": "4nLmDTu-Ss63"}}, {"cell_type": "markdown", "source": ["### Accuracy"], "metadata": {"id": "dxEzRM8eSvu-"}}, {"cell_type": "code", "source": ["y == y_hat"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CUw63fV2R9Ac", "executionInfo": {"status": "ok", "timestamp": 1689487707745, "user_tz": -330, "elapsed": 770, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "55c2129e-09ed-4fd2-a037-98939371a05c"}, "execution_count": 16, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ True,  True,  True,  True, False,  True, False, False,  True,\n", "        True, False,  True,  True, False, False, False, False,  True,\n", "       False, False])"]}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "code", "source": ["(y == y_hat).sum()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VxcafVpNSEJx", "executionInfo": {"status": "ok", "timestamp": 1689487709510, "user_tz": -330, "elapsed": 5, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "fe63ddeb-9191-4859-d602-79bfa0da6023"}, "execution_count": 17, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["10"]}, "metadata": {}, "execution_count": 17}]}, {"cell_type": "code", "source": ["accuracy = 10 / len(y)\n", "accuracy"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "b8hKagVzSJ_r", "executionInfo": {"status": "ok", "timestamp": 1689487716362, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "65726996-63c6-4166-f686-8625ba4328be"}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0.5"]}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["10 / 20"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WCLeUUZ1SYP4", "executionInfo": {"status": "ok", "timestamp": 1689487719633, "user_tz": -330, "elapsed": 5, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "c351db3a-02cf-4b8f-9c4d-76830c02e682"}, "execution_count": 19, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0.5"]}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "markdown", "source": ["### True and False positives"], "metadata": {"id": "hSthrInZS2tM"}}, {"cell_type": "code", "source": ["def calculate_true_positives_negatives(y_true, y_pred):\n", "    true_positives = np.sum((y_true == 1) & (y_pred == 1))\n", "    true_negatives = np.sum((y_true == 0) & (y_pred == 0))\n", "    return true_positives, true_negatives\n", "\n", "# Call the function with the given arrays\n", "tp, tn = calculate_true_positives_negatives(y, y_hat)\n", "\n", "print(\"True Positives:\", tp)\n", "print(\"True Negatives:\", tn)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "K2PrNPcFShA1", "executionInfo": {"status": "ok", "timestamp": 1689487729256, "user_tz": -330, "elapsed": 367, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "0923e02d-e9be-420f-bb97-1c1ca9ca5449"}, "execution_count": 20, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["True Positives: 5\n", "True Negatives: 5\n"]}]}, {"cell_type": "markdown", "source": ["### Sensitivity and Specificity"], "metadata": {"id": "G3v2O6EKTE8U"}}, {"cell_type": "code", "source": ["# Calculate false positives and false negatives\n", "fp = np.sum((y == 0) & (y_hat == 1))\n", "fn = np.sum((y == 1) & (y_hat == 0))\n", "\n", "print(\"False Positives:\", fp)\n", "print(\"False Negatives:\", fn)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CKTefkqZTAn3", "executionInfo": {"status": "ok", "timestamp": 1689487810247, "user_tz": -330, "elapsed": 7, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "ed6451c3-0163-4731-8f8e-e1143bf503cd"}, "execution_count": 26, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["False Positives: 6\n", "False Negatives: 4\n"]}]}, {"cell_type": "code", "source": ["def calculate_sensitivity_specificity(tp, tn, fp, fn):\n", "    sensitivity = tp / (tp + fn)\n", "    specificity = tn / (tn + fp)\n", "    return sensitivity, specificity"], "metadata": {"id": "5LrNvESyTsS0", "executionInfo": {"status": "ok", "timestamp": 1689487811710, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}}, "execution_count": 27, "outputs": []}, {"cell_type": "code", "source": ["# Call the function with the given values\n", "sensitivity, specificity = calculate_sensitivity_specificity(tp, tn, fp, fn)\n", "\n", "print(\"Sensitivity:\", sensitivity.round(2))\n", "print(\"Specificity:\", specificity.round(3))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M0OrexxQT7Z6", "executionInfo": {"status": "ok", "timestamp": 1689487813828, "user_tz": -330, "elapsed": 350, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "ce8066a4-766e-4e75-9fbe-0a6f06829f43"}, "execution_count": 28, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Sensitivity: 0.56\n", "Specificity: 0.455\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "XlSbobOpT_lE"}, "execution_count": null, "outputs": []}]}