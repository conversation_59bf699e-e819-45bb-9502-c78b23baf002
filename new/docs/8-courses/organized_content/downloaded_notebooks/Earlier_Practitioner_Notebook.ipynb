{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "collapsed_sections": ["W-AeecK0A-Wa"], "authorship_tag": "ABX9TyNG60CkPFRhRW8GUpZVLudl"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# Welcome to the course: Modern AI Pro\n", "We welcome you for this amazing journery. There is a lot of cool things we would do from here. From essentials we are moving to the practitioner level!\n", "\n", "\n", "\n", "\n", "Repo 1: https://github.com/balajivis/modernaipro\n", "\n", "Repo 2: https://github.com/balajivis/mai-prac\n", "\n", "Theory slides: [LINK](https://drive.google.com/drive/folders/1f14KbNQ_5gZqcRjHrhMTDiZx35Xr7Jxd)\n", "\n", "Practice slides: [LINK](https://drive.google.com/drive/folders/1ZopmqyUNxA9g0V0_g78fj57dPPEE_3Im?usp=sharing)"], "metadata": {"id": "_cD_V_YTe8xE"}}, {"cell_type": "markdown", "source": ["## Schedule"], "metadata": {"id": "CfLIkN7AMVBc"}}, {"cell_type": "markdown", "source": ["###Friday\n", "(All times in PST)\n", "\n", "**5-6:30pm**: Module 1: Business considerations in AI\n", "\n", "###Saturday:\n", "**8:30-10:30am**: Mo<PERSON>le 2: Building up the deep learning theory basics\n", "\n", "**10:30 - 10:40am**: Break\n", "\n", "**10:40 - 12:10pm**: Moving from chabots to agents\n", "\n", "**12:10 - 12:50pm**: Lunch Break\n", "\n", "**12:50 - 2:45pm**: Module 4: Deep document management with LLM\n", "\n", "**2:45 - 3:30**: Breakout session: Uncovering enterprise challenges\n", "\n", "**3:30 - 4:30pm**: Module 5: Latest developments in RAG\n", "\n", "###Sunday:\n", "**8:30-10:30am**: Mo<PERSON><PERSON> 6: Understanding theory of Finetuning and model building\n", "\n", "**10:30 - 10:40am**: Break\n", "\n", "**10:40 - 12:10pm**: Module 7: Building complex agents\n", "\n", "**12:10 - 12:50pm**: Lunch Break\n", "\n", "**12:50 - 2:45pm**: Module 8: Document intelligence & Knowledge graphs\n", "\n", "**2:45 - 3:30**: Breakout session: Project idea discussion\n", "\n", "**3:30 - 4:30pm**: <PERSON><PERSON><PERSON> 9: Eva<PERSON>ating LLMs"], "metadata": {"id": "a1dQozjRMQxF"}}, {"cell_type": "markdown", "source": ["## Basic setup\n"], "metadata": {"id": "W-AeecK0A-Wa"}}, {"cell_type": "markdown", "source": ["1. [Install Miniconda](https://docs.anaconda.com/miniconda/miniconda-install/)\n", "2. [Setup the path in Windows](https://eduand-alvarez.medium.com/setting-up-anaconda-on-your-windows-pc-6e39800c1afb)  || on Mac do **export PATH=\"\\$HOME/miniconda3/bin:$PATH\"** on the terminal\n", "3. [Setup split screen in Windows](https://support.lenovo.com/us/en/solutions/ht513508) || on Mac, use the spaces feature.\n", "4. [Install Ollama](https://ollama.com/download)\n", "5. [Install VSCode](https://code.visualstudio.com/download)\n", "6. [Install Git](https://git-scm.com/book/en/v2/Getting-Started-Installing-Git) and setup its path\n", "7. *In your terminal, do the following:* **conda create -n modernai python==3.11**\n", "8. **conda activate modernai**\n", "9. **ollama pull gemma2:2b**\n", "10. **git clone https://github.com/balajivis/modernaipro**\n", "11. **cd modernaipro**\n", "12. **pip install -r requirements.txt** [don't worry about uvloop]\n", "13. Sign up & get API keys/token at **<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>**"], "metadata": {"id": "L9IhruWPfami"}}, {"cell_type": "markdown", "source": ["## **Module 1**: Business considerations in AI\n", "\n", "[Slides](https://docs.google.com/presentation/d/1spbvGJV5en_DN_dwUUlGaM1tEu1eOILd2dH4lr5EpTc/edit#slide=id.g1e2ee25a6cc_0_0)\n", "\n"], "metadata": {"id": "J-77etH5fSwC"}}, {"cell_type": "markdown", "source": ["## **Module 2**: Building up the deep learning theory basics\n", "Revisiting Deep learning, CNN, Transformer foundations\n", "\n", "[Slides](https://drive.google.com/drive/folders/1f14KbNQ_5gZqcRjHrhMTDiZx35Xr7Jxd)"], "metadata": {"id": "8QShhVpkMkTl"}}, {"cell_type": "markdown", "source": ["## **Module 3**: Moving from chabots to agents\n", "Understanding memory, tools and agent foundation.\n", "[Slides](https://docs.google.com/presentation/d/1bYrhKzT6TuU4v6ebeWz9uq1pZP12PA0Ur1pxpA05ulA/edit?usp=sharing)\n", "\n", "\n", "1. [LLM with tools](https://colab.research.google.com/drive/16fm4MQuT6Z6kG6fHYn3HJAXqdg_zdr7g?usp=sharing)\n", "4. [Agents talking to one another](https://colab.research.google.com/drive/*********************************#scrollTo=NQ9hO4O85SeW)\n", "4. [First agent with the ReAct Framework](https://colab.research.google.com/drive/1IK5JvjczY5QsTbqC7SWy206ZGFC4k5nw?usp=sharing)\n", "5. [Agent with CrewAI](https://colab.research.google.com/drive/1ACt6TPg8SdhtXD1xOQNpIWS4RjrIQKfW)\n", "6. [Agents with Langgraph](https://colab.research.google.com/drive/15UuvEmi955OsIMTknxwh3pn3nE-XJptr?usp=sharing)\n", "7. [Advanced agents use case](https://colab.research.google.com/drive/1I2XOUw0S_vj58jOUOt8081dneiF_3wCm?usp=sharing)\n", "\n", "Project 1: Building a Perplexity clone\n", "\n", "Project 2: Building a top notch agent that can do deep stock analysis."], "metadata": {"id": "YyvM1_whOOim"}}, {"cell_type": "markdown", "source": ["## **Module 4**: Deep document management with LLM\n", "\n", "Summarization, analysis, table extraction, chatting with documents, kb creation.\n", "\n", "1. [Q&A on documents](https://colab.research.google.com/drive/1icYUUiBSAheX8Dfog0mhhQJeZdRlBlx-#scrollTo=vRFpSBs4aXDY)\n", "6. [Analyzing and Querying a Document with LLM Notebook](https://colab.research.google.com/drive/1G-3Zl8-eyxR_kP7B5gJSVCxcli0hsKoe)\n", "3. [Deep document analysis with BrahmaSumm](https://colab.research.google.com/drive/1zwJZLN63f6thjlhmZYiocIyUe_ViEYzl)\n", "[Simple notebook](https://colab.research.google.com/drive/1Qt8-XbXODbWblnJns2qTZB5H72EJmGJ5#scrollTo=cLxzKp55yUa-)\n", "7. [Table Analysis with Unstructured IO Notebook](https://colab.research.google.com/drive/1YJ5pxuESgwcc107pNVIxh7uD2SWxRPDY?usp=sharing) -- 1 hour\n", "8. [Multimodal advanced table and image analysis](https://colab.research.google.com/drive/1n2MZj5CVlr1bXSq7JiKabbVU-RM1hO8s#scrollTo=IV018jkcRen5)\n"], "metadata": {"id": "7UDEAdI-QPzU"}}, {"cell_type": "markdown", "source": ["## **Module 5**: Improving RAG\n", "[Slides](https://docs.google.com/presentation/d/1Lp6OrpJzi0tVd_BM9Vw6dAa6eNGgk84kOsFQ_sHgHiM/edit#slide=id.g317e09258e3_0_0)\n", "\n", "1. [SQLRAG](https://colab.research.google.com/drive/18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi#scrollTo=XEvzlbS7Zi4E)\n", "2. [SQL RAG 2](https://colab.research.google.com/drive/1patliw_XCH-3eKJVrfVcjF8nLyPy1Mb7)\n", "2. [Advanced RAG for Finance Domain](https://colab.research.google.com/drive/15ewzaL4odo0jVqZro65AqVkWDI-_8Y3f?usp=drive_link)\n", "5. [RAG with authorization levels](https://colab.research.google.com/drive/1FarVX93keIA35dvMHr7k6FXBu1rTpv97?usp=sharing)\n", "\n", "Explore further: https://github.com/NirDiamant/RAG_Techniques/tree/main/all_rag_techniques\n"], "metadata": {"id": "XuenV9veYhJy"}}, {"cell_type": "markdown", "source": ["## **Module 6**: Understanding Transformer Architecture\n"], "metadata": {"id": "G9x0G6L7Ys-W"}}, {"cell_type": "markdown", "source": ["## **Module 7**: Enterprise best practices\n"], "metadata": {"id": "0uJ6p9iMVQeO"}}, {"cell_type": "markdown", "source": ["1. Significance of Document Intelligence\n", "2. [Generative AI for PMs](https://arxiv.org/abs/2407.17478)\n", "3. [Generative AI in Education](https://www.deakin.edu.au/students/study-support/study-resources/artificial-intelligence)\n", "4. [Lessons in Document management](https://arxiv.org/pdf/2402.07483)\n", "5. [Case study in Finance for compliance](https://go.prolego.com/llm-rag-study)\n", "6. [Case study in clinical use case](https://arxiv.org/pdf/2402.01733)\n", "7. [Fraud detection at JPM](https://medium.com/@jeyadev_needhi/how-ai-transformed-financial-fraud-detection-a-case-study-of-jp-morgan-chase-f92bbb0707bb)\n", "8. [Ideas in finance and more](https://www.assemblyai.com/blog/llm-use-cases/)\n", "\n", "**Special notebooks:**\n", "\n", "1. [Reading Excel data](https://colab.research.google.com/drive/1VWrfcB6rLIfTsg0g9uN_URwoGJWaG6ZC?usp=sharing)\n", "2. [DICOM data](https://colab.research.google.com/drive/14nOmrtFXMvqxXI7lia67rQnwGf_BJUFN)"], "metadata": {"id": "cuF29ecsZ6ec"}}, {"cell_type": "markdown", "source": ["### Issues with jailbreaking and attacks\n", "1. Dissemination of Harmful Content\n", "Generation of instructions for illegal activities (e.g., making explosives or hacking systems).\n", "Production of misinformation, propaganda, or conspiracy theories.\n", "Creation of content promoting violence, hate speech, or discrimination.\n", "2. Circumvention of Ethical Guidelines\n", "Exploitation of models to bypass ethical safeguards put in place by developers.\n", "Use of adversarial prompts to produce outputs that violate safety guidelines.\n", "Subversion of systems designed to refuse harmful or sensitive queries.\n", "3. Privacy and Security Breaches\n", "Extraction of sensitive or private information through adversarial queries.\n", "Discovery and exploitation of backdoors (Trojan attacks) in models.\n", "Facilitation of cyberattacks, such as phishing schemes or social engineering.\n", "4. Amplification of Social and Political Harm\n", "Automation of large-scale misinformation campaigns.\n", "Generation of biased or manipulative content that can skew public opinion.\n", "Support for coordinated malicious activities, such as election interference.\n", "5. Undermining of Trust in AI\n", "Erosion of public trust in AI systems due to their misuse.\n", "Difficulty in distinguishing between safe and compromised models.\n", "Potential legal and reputational risks for organizations deploying these models.\n", "6. Weaponization of AI Models\n", "Utilization of LLMs for criminal enterprises, such as fraud or identity theft.\n", "Development of malicious software or tools via AI assistance.\n", "Enhanced capabilities for adversaries in cyber and physical security contexts.\n", "7. Economic and Legal Implications\n", "Misuse leading to financial losses or liabilities for AI providers and users.\n", "Legal ramifications if models inadvertently aid in unlawful activities.\n", "Increased costs for deploying and maintaining robust defenses against such attacks.\n", "8. Ethical Dilemmas and Abuse\n", "Encouragement of unethical experimentation using compromised models.\n", "Risk of fostering harmful ideologies by providing tailored content.\n", "Exploitation of AI for unethical research or experimentation.\n", "9. <PERSON><PERSON><PERSON><PERSON> of AI Reliability\n", "Vulnerabilities in LLMs could lead to unintended outputs in benign applications.\n", "Reduced effectiveness in real-world deployments due to tampering.\n", "Degradation of model quality as safety measures are bypassed."], "metadata": {"id": "Nu3_up3m7R5Y"}}, {"cell_type": "markdown", "source": ["## **Module 8**: Evaluating LLMs"], "metadata": {"id": "1GUXEeLxYuYa"}}, {"cell_type": "markdown", "source": ["1. [Understanding the LLM Arena and scoring methods](https://huggingface.co/spaces/lmarena-ai/chatbot-arena-leaderboard)\n", "2. [Hands on LLM evaluation](https://colab.research.google.com/drive/1ohdsyb2athEkRawI2HF-6kBxhdFF8HTk?usp=sharing)\n", "3. Understanding RAGAs"], "metadata": {"id": "ph_D_u0jZaOF"}}]}