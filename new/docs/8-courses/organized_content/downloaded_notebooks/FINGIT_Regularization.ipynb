{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["\n", "#<font color='red'>***REGULARIZATION***</font>\n", "\n", "\n", "Notebook written by <font color='red'>***<PERSON>ranav <PERSON>***.</font>"], "metadata": {"id": "-GaGQ9fV5sjT"}}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "w5SzHEgGDSRr", "executionInfo": {"status": "ok", "timestamp": 1752756065721, "user_tz": -330, "elapsed": 8, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "02261019787453967379"}}}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np"]}, {"cell_type": "markdown", "source": ["# Contents\n", "- Why regularization\n", "- How does regularization work?\n", "- Is too much of a good thing a bad thing?\n", "- Lasso regularization"], "metadata": {"id": "I6skYqsdDUyP"}}, {"cell_type": "markdown", "source": ["Regularization in essence is a method using which the performance of a Machine Learning algorithm can be boosted. But, internally, regularization can be thought of as a method to reduce overfitting.\n", "\\\n", "\\\n", "\\\n", "Please refer to the notebook on ML_Metrics for an in-depth explanation of what overfitting is.\n", "\\\n", "\\\n", "In short, overfitting is the scenario where a model specializes itself to the training data. That is, it achieves a very LOW BIAS on the training data while leading to a very HIGH VARIANCE on the testing dataset.\n", "\n", "\n", "---\n", "\n", "\\\n", "Earlier we said that Regularization increases the performance of the model. Let us consider an example to understand the sentence better:\n", "\\\n", "Imagine that you are in a room with 5 Televisions. Each TV is playing a certain channel.\n", "\\\n", "TV 1 - ESPN\n", "\\\n", "Tv 2 - Disney\n", "\\\n", "TV 3 - MSNBC\n", "\\\n", "TV 4 - HBO\n", "\\\n", "TV 5 - AXN\n", "\\\n", "But, you are ONLY interested in watching ESPN and HBO. The noise from the other 3 TVs is a disturbance, and it starts to ruin your viewing experience. However, you do have access to a remote (with only volume controls). What would you do now?\n", "\\\n", "\\\n", "The best way to deal with the problem would be to use the remote to reduce the volumes of all the TVs not playing HBO and ESPN. Although the TVs are still present, this ensures that they contribute to zero disturbance as they are producing almost no sound. Solved?\n", "\\\n", "\\\n", "In an ideal world, things would go exactly as planned. But, in the real world, there are always drawbacks, albeit small. The catch is that the remote in our example is faulty. That is when you try to decrease the sound produced by the other 3 TVs, the TVs of interest (HBO and ESPEN) are also affected. The faulty remote also reduces the sound of the HBO and ESPN TVs, but, at a very small rate when compared to the other TVs. There is a trade-off here. But, you can imagine that the disturbance caused when you have all TVs at full volume is much much greater than when you have the 3 TVs reduced by a large extent, while also reducing the volumes of HBO and ESPN by a relatively small amount.\n", "\\\n", "\\\n", "What we just described above are the core principles of Regularization.\n", "\\\n", "\\\n", "To rephrase, Regularization ensures that the model does not overfit to the training dataset, while also reducing the effects of attributes that do not contribute to increasing the accuracy of the predictions.\n", "\\\n", "\\\n", "\\\n", "**In Machine learning lingo, Regularization increases the Bias (Does not overfit the training data), while drastically reducing the variance of the model.**\n", "\n"], "metadata": {"id": "aRv8yyWwDawx"}}, {"cell_type": "markdown", "source": ["# How does regularization work?\n", "\n", "\n", "\n", "Let us try to understand what regularization is doing internally, by taking an example of linear regression\n", "\\\n", "\\\n", "Imagine that we are asked to predict the price of a house, given its built_up_area.\n", "\\\n", "\\\n", "The Training data consists of THREE examples.\n", "\\\n", "Training dataset - **RED**\n", "\\\n", "Testing dataset - **BLUE**"], "metadata": {"id": "lzTeKcXVSUvs"}}, {"cell_type": "markdown", "source": ["Now, if we were to fit a line using the Least Squaresd Error method on the training set, we would get:\n"], "metadata": {"id": "_82UFX3sbtJX"}}, {"cell_type": "code", "source": [], "metadata": {"cellView": "form", "id": "MAyXQsnqZ4Ga", "executionInfo": {"status": "ok", "timestamp": 1752756058682, "user_tz": -330, "elapsed": 9, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "02261019787453967379"}}}, "execution_count": 1, "outputs": []}, {"cell_type": "markdown", "source": ["We can see that the model performs extremely well on the training data. The fit has a very LOW BIAS (BIAS, in this case, is 0). But, what about the model's performance on the testing set? It performs very poorly on the testing set. The model overfits to the training set. Let us see how regularization can be used to increase performance.\n", "\\\n", "\\\n", "From our previous examples, we have seen that regularization reduces the effects that the attributes have on the result. In the case of the graph above, what does that mean?\n", "\\\n", "\\\n", "If the built-up area increases from 0 - 1, we see that the resulting price increases from 0 - 1.5. If you recall, this is the same definition as that of the **SLOPE** of a line.\n", "\\\n", "\\\n", "The equation of the line can then be represented as:\n", "\\\n", "\\\n", "Y = M * X + C.\n", "\\\n", "\\\n", "M = 1.5\n", "\\\n", "X = Built-up-area.\n", "\\\n", "\\\n", "From this example, slope then represents the effect that the attribute Built-up-area has on the final value. Regularization works to reduce this effect. What does this mean for the line?\n", "\\\n", "\\\n", "Reducing the \"effect\" in this scenario means reducing the slope. After applying regularization, the fit will look like this:"], "metadata": {"id": "wD9Jh_pQcNfZ"}}, {"cell_type": "code", "source": [], "metadata": {"cellView": "form", "id": "6TooV2f_vC4r", "executionInfo": {"status": "ok", "timestamp": 1752756127847, "user_tz": -330, "elapsed": 15, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "02261019787453967379"}}}, "execution_count": 2, "outputs": []}, {"cell_type": "markdown", "source": ["The **KEY OBSERVATION** here is that the slope of the line has been decreased.\n", "\\\n", "\\\n", "Although regularization introduces an error in the training set accuracy (BIAS), we see that the overall performance on the testing data-set (UNSEEN data) has increased.\n", "\\\n", "\\\n", "\\\n", "**NOTE**: IF we were to increase the number of attributes for predicting the final value then the number of \"slopes\" would also proportionally increase.\n", "\\\n", "If we use:\n", "\\\n", "attribute_1 (X1)  = Built-up-area\n", "\\\n", "attribute_2 (X2) = Number_of_rooms\n", "\\\n", "\\\n", "The updated equation:\n", "\\\n", "Y = M1 * X1 + M2 * X2 + C\n", "\\\n", "\\\n", "Let us use a more generic name for the term M. We will be calling the Xs as the **ATTRIBUTES** and the Ms as the **PARAMETERS - P**\n", "\\\n", "Y = P1 * X1 + P2 * X2 + C\n", "\n"], "metadata": {"id": "_Mtfb4x2vYIA"}}, {"cell_type": "markdown", "source": ["Linear regression using Least squared error approach - without regularization:"], "metadata": {"id": "57IovKaZGPNW"}}, {"cell_type": "markdown", "source": ["![new_reg-1.jpg](data:image/jpeg;base64,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)"], "metadata": {"id": "rrNBmQMTGQVV"}}, {"cell_type": "markdown", "source": ["Linear regression using Least squared error approach - with regularization"], "metadata": {"id": "LKyz25riGlIH"}}, {"cell_type": "markdown", "source": ["![new_reg-2.jpg](data:image/jpeg;base64,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)"], "metadata": {"id": "QVGAjUohGlyX"}}, {"cell_type": "markdown", "source": ["N = total number of examples in our training set (2 in this example)\n", "\\\n", "M = total number of attributes used (1 in this example - Built_up_area)"], "metadata": {"id": "DjXlAX92G7l9"}}, {"cell_type": "markdown", "source": ["The first term represents the **LSE** (square of the difference between the ground truth and the predicted values).\n", "\\\n", "\\\n", "The second term represents the **Regularization term** (Sum of the squares of the parameters times the Lambda parameter)\n", "\\\n", "\\\n", "**NOTE**: If we were to divide the LSE term by the number of examples N, then it would become the MSE (Mean Squared Error). Do not be confused by it. We are trying to understand the effects caused by the presence of the Regularization term and not the difference between LSE and MSE.\n", "\n", "\n", "---\n", "\n", "\n", "Let us try to demystify the above formula:\n", "\\\n", "\\\n", "\\\n", "The formula is a representation of how the human mind works. Think about it, most of our actions are motivated by eventual rewards. Getting closer to a certain reward would require us to avoid losses. If we realize that a certain action is taking us further away from our reward, then we rectify our actions. You might be motivated to go to the gym, which in turn requires you to sleep early. Hence, by sleeping late, you decrease the chances of waking up early, and thus failing to go to the gym. To achieve the reward, you try your best to rectify your actions and thus try to NOT sleep late.\n", "\\\n", "\\\n", "The above formula is trying to accomplish similar things. By adding an extra term to our cost function, you are motivating the algorithm to change/tune its parameters in a way that reduces the total error.\n", "\\\n", "\\\n", "In our case, we know that regularization tries to reduce the effect the individual attributes might have on the overall result (i.e, SLOPE). The extra penalty is thus a function of the SLOPE.\n", "\\\n", "\\\n", "**The model then tries to minimize: Sum of Least squared errors + extra penalty term (function of slope).**\n", "\\\n", "\\\n", "Let us go back to our linear regression example:"], "metadata": {"id": "vdiyr4hWwow2"}}, {"cell_type": "code", "source": [], "metadata": {"cellView": "form", "id": "Y3z_jwyZvEyy", "executionInfo": {"status": "ok", "timestamp": 1752756264898, "user_tz": -330, "elapsed": 8, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "02261019787453967379"}}}, "execution_count": 2, "outputs": []}, {"cell_type": "markdown", "source": ["**NOTE**: <PERSON><PERSON> decides the trade-off between LSE and regularization term. It is very similar to how you would prepare for the mid-term, versus a class test. Because the mid-term carries more weight, you put in more effort to maximize your mid-term marks. Now, if I were to tell you that for subject X, your class tests carry twice the marks that your mid-term exam does, then would you still distribute your efforts the same way? No! You would then try to prioritize your class tests over the mid-term. <PERSON><PERSON> works in the same manner. **For our example, let us consider LAMBA to be 1**\n", "\n", "**When the slope of the line = 1.45**: (This is the best fit line according to Linear regression)\n", "\\\n", "\\\n", "The LSE = 0. The line fits the training data perfectly.\n", "\\\n", "Regularization term error: 1 * 1.45^2\n", "\\\n", "\\\n", "total error: 0 + 1 * 1.45^2  =  2.1025\n", "\\\n", "NOTE: If we were using JUST LSE to model our fit, then this would be the BEST fit. (overfit)\n", "\n", "\n", "---\n", "\n", "\n", "**When the slope of the line = 1**:\n", "\\\n", "\\\n", "The LSE = 0.7.\n", "\\\n", "Regularization term error: 1 * 1^2\n", "\\\n", "\\\n", "total error: some_value + 0.7 * 1^2  =  1.7"], "metadata": {"id": "hpZFhOq60iV4"}}, {"cell_type": "markdown", "source": ["We can also observe that the line with slope 1 performs better on the testing data than the line with slope 1.45.\n", "\\\n", "\\\n", "Thus, by adding an extra penalty, we are ensuring that the model tunes its parameter so that it performs better on unseen data.\n"], "metadata": {"id": "rfc2jndu36oc"}}, {"cell_type": "markdown", "source": ["\n", "\n", "---\n", "\n", "\n", "\n", "Knowing that regularization is trying to minimize the effect the attributes have on the final value i.e slope, you might be tempted to think:\n", "\\\n", "\\\n", "\"If all that regularization does is shrink the slope, why don't I simply skip to the best solution? Manually SET the slope to be 0. This would be considered the BEST solution that any regularization method could provide, correct?\"\n", "\\\n", "\\\n", "**Remember that the regularization term is ONLY one of TWO terms in the loss function.**\n", "\\\n", "\\\n", "If we were to set the slope to ZERO then:"], "metadata": {"id": "U-OEV1wV5JQD"}}, {"cell_type": "code", "source": [], "metadata": {"cellView": "form", "id": "iRK2GG3-6CP7", "executionInfo": {"status": "ok", "timestamp": 1752756360352, "user_tz": -330, "elapsed": 8, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "02261019787453967379"}}}, "execution_count": 2, "outputs": []}, {"cell_type": "markdown", "source": ["LSE = 4.02\n", "\\\n", "Regularization term error: 1 * 0^2\n", "\\\n", "\\\n", "total error: 4.02 + 0 = 4.02\n", "\\\n", "\\\n", "\\\n", "The error produced by LSE overtakes the error that is being reduced by setting the slope to 0. This solution is very bad, and leads to a very HIGH BIAS and HIGH VARIANCE. (The worst combination!)\n", "\n", "---\n"], "metadata": {"id": "JVNTOnMx6SAw"}}, {"cell_type": "markdown", "source": ["\n", "# Is too much of a good thing a bad thing?\n", "Now, let us look at the same scenario, but where the scaling factor for the regularization term (Lambda) is set to a very HIGH value.\n", "\\\n", "Lambda = 1000\n", "\n", "\n", "\n", "\n", "**When the slope of the line = 1.45**:\n", "\\\n", "\\\n", "The LSE = 0. The line fits the training data perfectly.\n", "\\\n", "Regularization term error: 1000 * 1.45^2\n", "\\\n", "\\\n", "total error: 0 +  1000* 1.45^2 = 2,102.5\n", "\n", "---\n", "**When the slope of the line = 1**:\n", "\\\n", "\\\n", "The LSE = 0.7\n", "\\\n", "Regularization term error: 1000 * 1^2\n", "\\\n", "\\\n", "total error: 0.7 +  1000* 1^2 = 2,000.7\n", "\n", "---\n", "**When the slope of the line = 0**:\n", "\\\n", "\\\n", "The LSE = 4.02\n", "\\\n", "Regularization term error: 1000 * 0^2\n", "\\\n", "\\\n", "total error: 4.02 +  1000* 0^2 = 4.02\n", "\n", "---\n"], "metadata": {"id": "DvSR9uWYCvoJ"}}, {"cell_type": "markdown", "source": ["By setting a VERY HIGH value on the LAMBA term, you are motivating the algorithm to pick the slope as 0. Although this minimizes our loss function, you can see that it performs poorly on the testing dataset. In our previous example, we saw how setting the slope to 0 only aggravates the problem\n", "\\\n", "\\\n", "\\\n", "Hence, we need to pick the value of LAMBA such that it achieves the right balance between LSE and Regularization error.\n", "\n", "\n", "---\n", "\n"], "metadata": {"id": "B9du8n2g8rr8"}}, {"cell_type": "markdown", "source": ["Now, if we were to use three parameters to detect the price of the house: Built_up_area, Number_of_rooms, and the average temperature.\n", "\\\n", "\\\n", "The average temperature should not affect the price. Some people are on the lookout for sunny places, some cloudy. When we use regularization, it brings down the value of the parameter P3 to a very small value.\n", "\\\n", "\\\n", "You can imagine regularization to function as follow:\n", "- Pick a smaller value for P1. Check if the total error decreases. If yes, keep it, else, move to P2.\n", "- Pick a smaller value for P2. Check if the total error decreases. If yes, keep it, else, move to P3.\n", "- Pick a smaller value for P3. Check if the total error decreases. If yes, keep it.\n", "- Keep repeating this till no further decrease in error can be achieved.\n"], "metadata": {"id": "Onx_FqqeD4ul"}}, {"cell_type": "markdown", "source": ["We know that P1 and P2 directly effect the price of the home. If we decrease them, then the regularization term error might decrease, but the LSE will shoot up. But, we know that P3 does not effect the price. Hence, the algorithm will try to bring the value of P3 very close to 0. This would bring down the regularization term error, without increasing the LSE.\n", "\\\n", "\\\n", "\\\n", "**VERY IMPORTANT NOTE**\n", "\\\n", "Although the parameters are pushed very close to 0, they **NEVER** are equal to ZERO."], "metadata": {"id": "2oNw4ez4IcRA"}}, {"cell_type": "markdown", "source": ["**bold text**#Lasso Regularization:\n", "The regularization method we discussed above is known as **Ridge Regularization**\n", "\\\n", "\\\n", "\\\n", "We saw how the parameters that do not increase the accuracy of the predictions are pushed towards 0 but are never equal to zero.\n", "\\\n", "\\\n", "Let us go back to our house prediction example. Imagine that we used three different attributes to predict the price of the home:\n", "\\\n", "Built_up_area (X1)\n", "\\\n", "Number_of_rooms (X2)\n", "\\\n", "Number_of_soil_particles_in_the_backyard (X3)\n", "\\\n", "\\\n", "Y = P1 * X1 + P2 * X2 + P3 * X3 + C.\n", "\\\n", "\\\n", "Now, right off the back, you can tell that **Number_of_soil_particles_in_the_backyard** is not a helpful attribute to use. Some homes might not have a backyard, some might have a very small backyard. There is no underlying pattern that can help us predict house prices. Imagine if we used Ridge regression to train our fit. P3 would be a very small number ~0, but, you can imagine **Number_of_soil_particles_in_the_backyard** to be a value in the order of billions. even if P3 was a really small value - Let's say 0.0000001, P3 * X3 would still be a large value. Hence, we need a method that SETS the value of P3 to ZERO, rather than asymptotically making it ZERO."], "metadata": {"id": "rDSjW4STEjkX"}}, {"cell_type": "markdown", "source": ["Enter **LASSO REGULARIZATION**:\n"], "metadata": {"id": "tbZ6s1r5Gybq"}}, {"cell_type": "markdown", "source": ["![new_reg-3.jpg](data:image/jpeg;base64,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)"], "metadata": {"id": "M7WvdphZGzHG"}}, {"cell_type": "markdown", "source": ["The formula is very similar to Ridge regression. The only real difference is that instead of taking the Squares of the slopes, we are only interested in the magnitude of the slope. i.e, its absolute value."], "metadata": {"id": "BT1_yGFR1u-9"}}, {"cell_type": "code", "source": [], "metadata": {"id": "QQXCXWfasF4a"}, "execution_count": null, "outputs": []}]}