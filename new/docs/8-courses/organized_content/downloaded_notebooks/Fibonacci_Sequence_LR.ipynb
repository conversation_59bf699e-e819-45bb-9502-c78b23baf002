{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "Fibonacci_Sequence_LR.ipynb", "provenance": [], "collapsed_sections": [], "authorship_tag": "ABX9TyOshpwPQegR3x5UsbltZfwW"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# Let us see if Linear regression can learn to predict the next fibon<PERSON>ci number given just the previous fib number (1D input)"], "metadata": {"id": "3e3HA_UvQqAd"}}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "uBHH4NyhnILx", "executionInfo": {"status": "ok", "timestamp": 1649523681676, "user_tz": -330, "elapsed": 385, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}}, "outputs": [], "source": ["import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression"]}, {"cell_type": "code", "source": ["X = np.array([0, 1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610])\n", "y = np.array([1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987])\n", "X,y"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QFtwS8VJnZBp", "executionInfo": {"status": "ok", "timestamp": 1649523681677, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "a7db0d00-e69b-43ea-9d57-62c80a5bc736"}, "execution_count": 2, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(array([  0,   1,   1,   2,   3,   5,   8,  13,  21,  34,  55,  89, 144,\n", "        233, 377, 610]),\n", " array([  1,   1,   2,   3,   5,   8,  13,  21,  34,  55,  89, 144, 233,\n", "        377, 610, 987]))"]}, "metadata": {}, "execution_count": 2}]}, {"cell_type": "code", "source": ["X_train, X_test, y_train, y_test = train_test_split(X, y, test_size = 0.25,shuffle=True)\n", "X_train = X_train.reshape(-1, 1)\n", "X_test = X_test.reshape(-1, 1)  \n", "\n", "regr = LinearRegression()\n", "  \n", "regr.fit(X_train, y_train)\n", "print(regr.score(X_test, y_test))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ze9OM5h_n62L", "executionInfo": {"status": "ok", "timestamp": 1649523682137, "user_tz": -330, "elapsed": 464, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "57b4f549-a7f7-43e6-a86c-63b1076e74cf"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["0.9999561540000893\n"]}]}, {"cell_type": "code", "source": ["test = np.array([10946]).reshape(-1,1)\n", "regr.predict(test)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xc0TJHyDpI4f", "executionInfo": {"status": "ok", "timestamp": 1649523682138, "user_tz": -330, "elapsed": 41, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "6b259117-07cc-4ebf-ebe5-4d08341932d5"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([17707.39277275])"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["test = np.array([317811]).reshape(-1,1)\n", "regr.predict(test)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7VApiH6TpVo5", "executionInfo": {"status": "ok", "timestamp": 1649523682139, "user_tz": -330, "elapsed": 38, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "d9c831d6-5dfc-47ec-94c8-7f55539adb1f"}, "execution_count": 5, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([514120.43599196])"]}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "markdown", "source": ["First 100 terms of Fibonacci series are :- 0 1 1 2 3 5 8 13 21 34 55 89 144 233 377 ************ 2584 4181 6765 10946 17711 28657 46368 75025 121393 196418 317811 514229 832040 1346269 2178309 3524578 5702887 9227465 14930352 24157817 39088169 63245986 102334155 165580141 267914296 433494437 701408733 1134903170"], "metadata": {"id": "pm-BV-NQrtyH"}}, {"cell_type": "markdown", "source": ["# Let's try with 2D input. The model should be able to learn this linear relationship - and predict the next fib number given the previous 2 fib numbers."], "metadata": {"id": "42mNN7rxQZ1S"}}, {"cell_type": "code", "source": ["X1 = np.array([0, 1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610])\n", "X2 = np.array([1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987])\n", "X = np.vstack((X1,X2))\n", "y = np.array([1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987, 1597])"], "metadata": {"id": "JZ5LwxK5roNK", "executionInfo": {"status": "ok", "timestamp": 1649523732477, "user_tz": -330, "elapsed": 568, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["X"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8sZSkm8izSdv", "executionInfo": {"status": "ok", "timestamp": 1649523732980, "user_tz": -330, "elapsed": 7, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "27b6d152-5b92-4e2f-da1e-83f225d41d47"}, "execution_count": 14, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[  0,   1,   1,   2,   3,   5,   8,  13,  21,  34,  55,  89, 144,\n", "        233, 377, 610],\n", "       [  1,   1,   2,   3,   5,   8,  13,  21,  34,  55,  89, 144, 233,\n", "        377, 610, 987]])"]}, "metadata": {}, "execution_count": 14}]}, {"cell_type": "code", "source": ["X_train, X_test, y_train, y_test = train_test_split(X.T, y, test_size = 0.25,shuffle=True)\n", "regr = LinearRegression()\n", "  \n", "regr.fit(X_train, y_train)\n", "print(regr.score(X_test, y_test))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "U_2QsgzhyU1O", "executionInfo": {"status": "ok", "timestamp": 1649523733349, "user_tz": -330, "elapsed": 11, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "08b67f68-3197-49a5-ce1f-e5a7f4728aa0"}, "execution_count": 15, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["1.0\n"]}]}, {"cell_type": "code", "source": ["test = np.array([196418,317811]).reshape(1,2)\n", "regr.predict(test) # expected: 514229"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "frwk2mXSzNQf", "executionInfo": {"status": "ok", "timestamp": 1649523733350, "user_tz": -330, "elapsed": 8, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "2d0da059-1078-42e7-edef-2b24f7a1149c"}, "execution_count": 16, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([514229.])"]}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "code", "source": ["test = np.array([102334155,165580141]).reshape(1,2)\n", "regr.predict(test).astype(int) # expected: 267914296"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6BZZnRGN0cFK", "executionInfo": {"status": "ok", "timestamp": 1649523733762, "user_tz": -330, "elapsed": 9, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "9e195ce7-bfa6-42df-8bc8-6057234a3a91"}, "execution_count": 17, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([267914296])"]}, "metadata": {}, "execution_count": 17}]}, {"cell_type": "code", "source": ["test = np.array([433494437,701408733]).reshape(1,2)\n", "regr.predict(test).astype(int) # expected: 1134903170"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2fMdF9yH1ShA", "executionInfo": {"status": "ok", "timestamp": 1649523733764, "user_tz": -330, "elapsed": 9, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "cd13cd43-4ca8-4315-8800-015e83261acd"}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([1134903170])"]}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["test = np.array([27777890035288,44945570212853]).reshape(1,2)\n", "regr.predict(test).astype(int) # expected: 72723460248141"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "q415sJ-X4LlP", "executionInfo": {"status": "ok", "timestamp": 1649523736861, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "301fedd1-bce0-46f6-d5b8-34b06d885493"}, "execution_count": 19, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([72723460248141])"]}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "markdown", "source": ["int64 supports upto 19 digits. Try even larger numbers. [List of Fibonacci Numbers - LINK.](https://blog.abelotech.com/posts/first-500-fibonacci-numbers/)"], "metadata": {"id": "qdKIp5yL9nwV"}}, {"cell_type": "code", "source": [""], "metadata": {"id": "6ClNFmw69SBK", "executionInfo": {"status": "ok", "timestamp": 1649523682144, "user_tz": -330, "elapsed": 22, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}}, "execution_count": 12, "outputs": []}]}