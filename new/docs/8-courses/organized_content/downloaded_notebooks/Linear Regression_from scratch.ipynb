{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [{"file_id": "1br0hM79ORTVNXUpVgkV5t4o4AigGxfwk", "timestamp": 1689270163185}]}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["In this tutorial, we are going to implement a linear regression model to predict california housing prices. We will build the model from scratch using numpy. This will be a great approach to begin understanding regression based models.\n", "\n", "After completing this tutorial the learner is expected to know the basic building blocks of a linear regression model. The learner is also expected to know the pipeline of reading and transforming data for machine learning workflows.\n", "\n"], "metadata": {"id": "m41136O7L5bV"}}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "Pni17h4R8v8a", "executionInfo": {"status": "ok", "timestamp": 1689408225453, "user_tz": -330, "elapsed": 1245, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}}, "outputs": [], "source": ["## Import the usual libraries\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.datasets import fetch_california_housing\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "source": ["# Importing the dataset\n", "\n", "The real-world dataset can be obtained by the function `fetch_california_housing` that downloads the dataset for us.\n", "\n", "The `as_frame` parameter returns a pandas dataframe which is a library useful for viewing contents of the data.\n", "\n"], "metadata": {"id": "DjyJUfczL4zX"}}, {"cell_type": "code", "source": ["# Fetch the data using sklearn function\n", "bunch = fetch_california_housing(download_if_missing=True, as_frame=True)\n", "\n", "# Load the dataframe and view\n", "df = bunch.frame\n", "df.head()"], "metadata": {"id": "aOXxbywahC5X", "executionInfo": {"status": "ok", "timestamp": 1689408234428, "user_tz": -330, "elapsed": 5394, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 206}, "outputId": "0b89b98a-4476-4b18-aa30-11e7dd740911"}, "execution_count": 2, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   MedInc  HouseAge  AveRooms  AveBedrms  Population  AveOccup  Latitude  \\\n", "0  8.3252      41.0  6.984127   1.023810       322.0  2.555556     37.88   \n", "1  8.3014      21.0  6.238137   0.971880      2401.0  2.109842     37.86   \n", "2  7.2574      52.0  8.288136   1.073446       496.0  2.802260     37.85   \n", "3  5.6431      52.0  5.817352   1.073059       558.0  2.547945     37.85   \n", "4  3.8462      52.0  6.281853   1.081081       565.0  2.181467     37.85   \n", "\n", "   Longitude  MedHouseVal  \n", "0    -122.23        4.526  \n", "1    -122.22        3.585  \n", "2    -122.24        3.521  \n", "3    -122.25        3.413  \n", "4    -122.25        3.422  "], "text/html": ["\n", "\n", "  <div id=\"df-c5b21f03-7603-4b6b-b1b1-79254e2ac81a\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MedInc</th>\n", "      <th>HouseAge</th>\n", "      <th>AveRooms</th>\n", "      <th>AveBedrms</th>\n", "      <th>Population</th>\n", "      <th>AveOccup</th>\n", "      <th>Latitude</th>\n", "      <th>Longitude</th>\n", "      <th>MedHouseVal</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>8.3252</td>\n", "      <td>41.0</td>\n", "      <td>6.984127</td>\n", "      <td>1.023810</td>\n", "      <td>322.0</td>\n", "      <td>2.555556</td>\n", "      <td>37.88</td>\n", "      <td>-122.23</td>\n", "      <td>4.526</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>8.3014</td>\n", "      <td>21.0</td>\n", "      <td>6.238137</td>\n", "      <td>0.971880</td>\n", "      <td>2401.0</td>\n", "      <td>2.109842</td>\n", "      <td>37.86</td>\n", "      <td>-122.22</td>\n", "      <td>3.585</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>7.2574</td>\n", "      <td>52.0</td>\n", "      <td>8.288136</td>\n", "      <td>1.073446</td>\n", "      <td>496.0</td>\n", "      <td>2.802260</td>\n", "      <td>37.85</td>\n", "      <td>-122.24</td>\n", "      <td>3.521</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5.6431</td>\n", "      <td>52.0</td>\n", "      <td>5.817352</td>\n", "      <td>1.073059</td>\n", "      <td>558.0</td>\n", "      <td>2.547945</td>\n", "      <td>37.85</td>\n", "      <td>-122.25</td>\n", "      <td>3.413</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3.8462</td>\n", "      <td>52.0</td>\n", "      <td>6.281853</td>\n", "      <td>1.081081</td>\n", "      <td>565.0</td>\n", "      <td>2.181467</td>\n", "      <td>37.85</td>\n", "      <td>-122.25</td>\n", "      <td>3.422</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-c5b21f03-7603-4b6b-b1b1-79254e2ac81a')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "\n", "\n", "\n", "    <div id=\"df-2b383e50-1fa0-4f0f-9e68-e3ebea08e91c\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-2b383e50-1fa0-4f0f-9e68-e3ebea08e91c')\"\n", "              title=\"Suggest charts.\"\n", "              style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "    </div>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "    background-color: #E8F0FE;\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: #1967D2;\n", "    height: 32px;\n", "    padding: 0 0 0 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: #E2EBFA;\n", "    box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: #174EA6;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "    background-color: #3B4455;\n", "    fill: #D2E3FC;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart:hover {\n", "    background-color: #434B5C;\n", "    box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "    filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "    fill: #FFFFFF;\n", "  }\n", "</style>\n", "\n", "    <script>\n", "      async function quickchart(key) {\n", "        const containerElement = document.querySelector('#' + key);\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      }\n", "    </script>\n", "\n", "      <script>\n", "\n", "function displayQuickchartButton(domScope) {\n", "  let quickchartButtonEl =\n", "    domScope.querySelector('#df-2b383e50-1fa0-4f0f-9e68-e3ebea08e91c button.colab-df-quickchart');\n", "  quickchartButtonEl.style.display =\n", "    google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "}\n", "\n", "        displayQuickchartButton(document);\n", "      </script>\n", "      <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-c5b21f03-7603-4b6b-b1b1-79254e2ac81a button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-c5b21f03-7603-4b6b-b1b1-79254e2ac81a');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n"]}, "metadata": {}, "execution_count": 2}]}, {"cell_type": "markdown", "source": ["For this dataset, our target variable is the median house value for California districts, expressed in hundreds of thousands of dollars ($100,000).\n", "\n", "We can take a closer look at the various statistical parameters of the dataset using pandas. The `describe` function will help us."], "metadata": {"id": "KUeU_jLylTx7"}}, {"cell_type": "code", "source": ["df.describe()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 364}, "id": "eD4BpBHClgDc", "executionInfo": {"status": "ok", "timestamp": 1689408237221, "user_tz": -330, "elapsed": 746, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "2b0b0f49-0d5e-436a-d24a-d77a3ec0b4a9"}, "execution_count": 3, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["             MedInc      HouseAge      AveRooms     AveBedrms    Population  \\\n", "count  20640.000000  20640.000000  20640.000000  20640.000000  20640.000000   \n", "mean       3.870671     28.639486      5.429000      1.096675   1425.476744   \n", "std        1.899822     12.585558      2.474173      0.473911   1132.462122   \n", "min        0.499900      1.000000      0.846154      0.333333      3.000000   \n", "25%        2.563400     18.000000      4.440716      1.006079    787.000000   \n", "50%        3.534800     29.000000      5.229129      1.048780   1166.000000   \n", "75%        4.743250     37.000000      6.052381      1.099526   1725.000000   \n", "max       15.000100     52.000000    141.909091     34.066667  35682.000000   \n", "\n", "           AveOccup      Latitude     Longitude   MedHouseVal  \n", "count  20640.000000  20640.000000  20640.000000  20640.000000  \n", "mean       3.070655     35.631861   -119.569704      2.068558  \n", "std       10.386050      2.135952      2.003532      1.153956  \n", "min        0.692308     32.540000   -124.350000      0.149990  \n", "25%        2.429741     33.930000   -121.800000      1.196000  \n", "50%        2.818116     34.260000   -118.490000      1.797000  \n", "75%        3.282261     37.710000   -118.010000      2.647250  \n", "max     1243.333333     41.950000   -114.310000      5.000010  "], "text/html": ["\n", "\n", "  <div id=\"df-86c88a1a-7fd0-43cc-b447-488fa67dd7e2\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MedInc</th>\n", "      <th>HouseAge</th>\n", "      <th>AveRooms</th>\n", "      <th>AveBedrms</th>\n", "      <th>Population</th>\n", "      <th>AveOccup</th>\n", "      <th>Latitude</th>\n", "      <th>Longitude</th>\n", "      <th>MedHouseVal</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>20640.000000</td>\n", "      <td>20640.000000</td>\n", "      <td>20640.000000</td>\n", "      <td>20640.000000</td>\n", "      <td>20640.000000</td>\n", "      <td>20640.000000</td>\n", "      <td>20640.000000</td>\n", "      <td>20640.000000</td>\n", "      <td>20640.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>3.870671</td>\n", "      <td>28.639486</td>\n", "      <td>5.429000</td>\n", "      <td>1.096675</td>\n", "      <td>1425.476744</td>\n", "      <td>3.070655</td>\n", "      <td>35.631861</td>\n", "      <td>-119.569704</td>\n", "      <td>2.068558</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1.899822</td>\n", "      <td>12.585558</td>\n", "      <td>2.474173</td>\n", "      <td>0.473911</td>\n", "      <td>1132.462122</td>\n", "      <td>10.386050</td>\n", "      <td>2.135952</td>\n", "      <td>2.003532</td>\n", "      <td>1.153956</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.499900</td>\n", "      <td>1.000000</td>\n", "      <td>0.846154</td>\n", "      <td>0.333333</td>\n", "      <td>3.000000</td>\n", "      <td>0.692308</td>\n", "      <td>32.540000</td>\n", "      <td>-124.350000</td>\n", "      <td>0.149990</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2.563400</td>\n", "      <td>18.000000</td>\n", "      <td>4.440716</td>\n", "      <td>1.006079</td>\n", "      <td>787.000000</td>\n", "      <td>2.429741</td>\n", "      <td>33.930000</td>\n", "      <td>-121.800000</td>\n", "      <td>1.196000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3.534800</td>\n", "      <td>29.000000</td>\n", "      <td>5.229129</td>\n", "      <td>1.048780</td>\n", "      <td>1166.000000</td>\n", "      <td>2.818116</td>\n", "      <td>34.260000</td>\n", "      <td>-118.490000</td>\n", "      <td>1.797000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>4.743250</td>\n", "      <td>37.000000</td>\n", "      <td>6.052381</td>\n", "      <td>1.099526</td>\n", "      <td>1725.000000</td>\n", "      <td>3.282261</td>\n", "      <td>37.710000</td>\n", "      <td>-118.010000</td>\n", "      <td>2.647250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>15.000100</td>\n", "      <td>52.000000</td>\n", "      <td>141.909091</td>\n", "      <td>34.066667</td>\n", "      <td>35682.000000</td>\n", "      <td>1243.333333</td>\n", "      <td>41.950000</td>\n", "      <td>-114.310000</td>\n", "      <td>5.000010</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-86c88a1a-7fd0-43cc-b447-488fa67dd7e2')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "\n", "\n", "\n", "    <div id=\"df-3582f4b4-0914-4084-8d34-9d160eaf52c4\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-3582f4b4-0914-4084-8d34-9d160eaf52c4')\"\n", "              title=\"Suggest charts.\"\n", "              style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "    </div>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "    background-color: #E8F0FE;\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: #1967D2;\n", "    height: 32px;\n", "    padding: 0 0 0 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: #E2EBFA;\n", "    box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: #174EA6;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "    background-color: #3B4455;\n", "    fill: #D2E3FC;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart:hover {\n", "    background-color: #434B5C;\n", "    box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "    filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "    fill: #FFFFFF;\n", "  }\n", "</style>\n", "\n", "    <script>\n", "      async function quickchart(key) {\n", "        const containerElement = document.querySelector('#' + key);\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      }\n", "    </script>\n", "\n", "      <script>\n", "\n", "function displayQuickchartButton(domScope) {\n", "  let quickchartButtonEl =\n", "    domScope.querySelector('#df-3582f4b4-0914-4084-8d34-9d160eaf52c4 button.colab-df-quickchart');\n", "  quickchartButtonEl.style.display =\n", "    google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "}\n", "\n", "        displayQuickchartButton(document);\n", "      </script>\n", "      <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-86c88a1a-7fd0-43cc-b447-488fa67dd7e2 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-86c88a1a-7fd0-43cc-b447-488fa67dd7e2');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n"]}, "metadata": {}, "execution_count": 3}]}, {"cell_type": "markdown", "source": ["As we can see the data in each of the columns is on different scales. For example, the average bedroom value is around 1 and the average population is around 1425.\n", "\n", "Generally, machine learing models do not work well when the data is on different scales. Thus, we have to normalize our data in the range [-1,1]. The module [StandardScalar](https://scikit-learn.org/stable/modules/generated/sklearn.preprocessing.StandardScaler.html) will help us in this.\n", "\n", "The training data should always be normalized. The testing data should be normalized using the values of the training data."], "metadata": {"id": "6S6WG0Bejxc2"}}, {"cell_type": "code", "source": ["df = bunch.frame\n", "x = df.iloc[:,:-1] # Select all the columns, except the last column\n", "y = df.iloc[:,-1:] # Select the last column\n", "x_train, x_test, y_train, y_test = train_test_split(x, y, test_size = 0.33, random_state = 1)\n", "\n", "input_scalar = StandardScaler()\n", "output_scalar = StandardScaler()\n", "\n", "x_train = input_scalar.fit_transform(x_train).T # Normalize train data\n", "x_test = input_scalar.transform(x_test).T # Only transform test data using values of train data\n", "\n", "y_train = output_scalar.fit_transform(y_train).reshape(-1)\n", "y_test = output_scalar.transform(y_test).reshape(-1)\n", "\n", "dataset_copy = [ x_train.copy(), x_test.copy(),  y_train.copy(),  y_test.copy()]"], "metadata": {"id": "pkaOgN44iQLN", "executionInfo": {"status": "ok", "timestamp": 1689408237824, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}}, "execution_count": 4, "outputs": []}, {"cell_type": "markdown", "source": ["# Linear Regression Model\n", "\n", "Now we define our linear regression model from scratch.\n", "\n", "A linear regression model is of the form:\n", "\n", "$y = a_1 x_1 + a_2 x_2 + \\dots + a_nx_n + a_{n+1}$\n", "  \n", "The above can be rewritten using matrix multiplication as\n", "\n", "$ y = w^T x $\n", "\n", "where\n", "\n", "$ w = [a_1, a_2, \\dots,  a_n, a_{n+1}]^T $\n", "\n", "$ x = [x_1, x_2, \\dots,  x_n]^T $\n"], "metadata": {"id": "mylVXZDk96a2"}}, {"cell_type": "code", "source": ["class LinearRegression():\n", "  def __init__(self, dim, lr = 0.1):\n", "    assert isinstance\n", "    self.lr = lr\n", "    self.w = np.zeros((dim))\n", "    self.grads = {\"dw\": np.zeros((dim)) +5}\n", "\n", "  def forward(self, x):\n", "    y = self.w.T @ x\n", "    return y\n", "\n", "  def backward(self, x, y_hat, y):\n", "    assert y_hat.shape == y.shape\n", "    self.grads[\"dw\"] = (1 / x.shape[1]) * ((y_hat - y) @ x.T).T\n", "    assert self.grads[\"dw\"].shape == self.w.shape\n", "\n", "    # print(self.grads[\"dw\"])\n", "\n", "  def optimize(self):\n", "    self.w = self.w - self.lr * self.grads[\"dw\"]"], "metadata": {"id": "iJViSowz9nah", "executionInfo": {"status": "ok", "timestamp": 1689409449956, "user_tz": -330, "elapsed": 5, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}}, "execution_count": 5, "outputs": []}, {"cell_type": "markdown", "source": ["# Loss\n", "\n", "For linear regression, various loss functions such as the mean absolute error, mean squared error, or root mean squared error can be used.\n", "\n", "In this example, we will use the mean squared error (MSE) loss.\n", "\n", "The MSE loss is given by\n", "\n", "$ error = \\frac{1}{m} Σ_{i=1}^{m} (y_{true}^{i} - y_{pred}^{i})^2 $\n", "\n", "where $i$ denotes the particular obseration/row in the dataset and $m$ is the total number of obserations.\n", "\n", "To ensure our model is correct, the loss should decrease over each epoch.\n"], "metadata": {"id": "6UOy32LoqCrL"}}, {"cell_type": "code", "source": ["num_epochs = 1000\n", "train_loss_history = []\n", "test_loss_history = []\n", "w_history = []\n", "dim = x_train.shape[0]\n", "num_train = x_train.shape[1]\n", "num_test = x_test.shape[1]\n", "\n", "\n", "model = LinearRegression(dim = dim, lr = 0.1)\n", "for i in range(num_epochs):\n", "  y_hat = model.forward(x_train)\n", "  train_loss = 1/(2 * num_train) * ((y_train - y_hat) ** 2).sum()\n", "\n", "  w_history.append(model.w)\n", "  model.backward(x_train,y_hat,y_train)\n", "  model.optimize()\n", "\n", "  y_hat = model.forward(x_test)\n", "  test_loss = 1/(2 * num_test) * ((y_test - y_hat) ** 2).sum()\n", "\n", "  train_loss_history.append(train_loss)\n", "  test_loss_history.append(test_loss)\n", "\n", "  if i % 20 == 0:\n", "    print(f\"Epoch {i} | Train Loss {train_loss} | Test Loss {test_loss}\")\n", "\n", "plt.plot(range(num_epochs), train_loss_history, label = \"Training\")\n", "plt.plot(range(num_epochs), test_loss_history, label = \"Test\")\n", "plt.legend()\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "mgNn3oGjjbxX", "executionInfo": {"status": "ok", "timestamp": 1689409453798, "user_tz": -330, "elapsed": 893, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "c788c53e-395d-4c90-c327-55778767ad08"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Epoch 0 | Train Loss 0.49999999999999983 | Test Loss 0.43712002508129305\n", "Epoch 20 | Train Loss 0.2359010824314295 | Test Loss 0.2325611904818386\n", "Epoch 40 | Train Loss 0.22188755162559423 | Test Loss 0.2217635127918686\n", "Epoch 60 | Train Loss 0.21474640752415047 | Test Loss 0.2152613227580797\n", "Epoch 80 | Train Loss 0.2095989567021037 | Test Loss 0.21037193610067245\n", "Epoch 100 | Train Loss 0.20581761895152345 | Test Loss 0.20673038702760732\n", "Epoch 120 | Train Loss 0.20303294882659725 | Test Loss 0.20402527473733864\n", "Epoch 140 | Train Loss 0.20097918345162274 | Test Loss 0.20201418597478873\n", "Epoch 160 | Train Loss 0.19946210112703647 | Test Loss 0.20051638978054404\n", "Epoch 180 | Train Loss 0.19833950070910428 | Test Loss 0.19939846923590557\n", "Epoch 200 | Train Loss 0.19750719045004836 | Test Loss 0.19856209982480624\n", "Epoch 220 | Train Loss 0.1968887724916989 | Test Loss 0.1979347869242644\n", "Epoch 240 | Train Loss 0.19642818272324772 | Test Loss 0.19746302071881933\n", "Epoch 260 | Train Loss 0.19608424065206406 | Test Loss 0.19710724843033994\n", "Epoch 280 | Train Loss 0.19582666642954677 | Test Loss 0.19683818623685323\n", "Epoch 300 | Train Loss 0.19563316903289557 | Test Loss 0.19663411372213185\n", "Epoch 320 | Train Loss 0.19548731666143612 | Test Loss 0.19647888801910168\n", "Epoch 340 | Train Loss 0.19537697849050284 | Test Loss 0.19636048509166582\n", "Epoch 360 | Train Loss 0.19529318388491884 | Test Loss 0.19626992725672532\n", "Epoch 380 | Train Loss 0.19522928672715056 | Test Loss 0.19620049386222657\n", "Epoch 400 | Train Loss 0.19518035283239818 | Test Loss 0.19614713968570663\n", "Epoch 420 | Train Loss 0.19514271054504922 | Test Loss 0.1961060658296224\n", "Epoch 440 | Train Loss 0.19511362075549118 | Test Loss 0.19607440266698017\n", "Epoch 460 | Train Loss 0.19509103436032973 | Test Loss 0.196049975197568\n", "Epoch 480 | Train Loss 0.19507341379189 | Test Loss 0.1960311290809374\n", "Epoch 500 | Train Loss 0.195059601524607 | Test Loss 0.1960166013981983\n", "Epoch 520 | Train Loss 0.19504872305387622 | Test Loss 0.19600542443105118\n", "Epoch 540 | Train Loss 0.19504011519474806 | Test Loss 0.19599685384985982\n", "Epoch 560 | Train Loss 0.19503327299733605 | Test Loss 0.19599031497728384\n", "Epoch 580 | Train Loss 0.19502781036651573 | Test Loss 0.19598536246238987\n", "Epoch 600 | Train Loss 0.19502343078313097 | Test Loss 0.19598164992474956\n", "Epoch 620 | Train Loss 0.19501990548217418 | Test Loss 0.19597890702760307\n", "Epoch 640 | Train Loss 0.19501705714492468 | Test Loss 0.1959769221005947\n", "Epoch 660 | Train Loss 0.1950147476759042 | Test Loss 0.1959755289194148\n", "Epoch 680 | Train Loss 0.19501286901218934 | Test Loss 0.19597459660841743\n", "Epoch 700 | Train Loss 0.1950113361889526 | Test Loss 0.19597402189697258\n", "Epoch 720 | Train Loss 0.1950100820879975 | Test Loss 0.19597372315589145\n", "Epoch 740 | Train Loss 0.1950090534451732 | Test Loss 0.19597363578501353\n", "Epoch 760 | Train Loss 0.1950082078022795 | Test Loss 0.19597370863036628\n", "Epoch 780 | Train Loss 0.19500751116991094 | Test Loss 0.19597390118903935\n", "Epoch 800 | Train Loss 0.19500693622732843 | Test Loss 0.19597418141927686\n", "Epoch 820 | Train Loss 0.19500646092952323 | Test Loss 0.19597452401759977\n", "Epoch 840 | Train Loss 0.19500606742426427 | Test Loss 0.1959749090579219\n", "Epoch 860 | Train Loss 0.19500574120612196 | Test Loss 0.19597532091250944\n", "Epoch 880 | Train Loss 0.19500547045245514 | Test Loss 0.19597574739336301\n", "Epoch 900 | Train Loss 0.19500524549975556 | Test Loss 0.1959761790667557\n", "Epoch 920 | Train Loss 0.19500505842876395 | Test Loss 0.19597660870438502\n", "Epoch 940 | Train Loss 0.1950049027342804 | Test Loss 0.1959770308427642\n", "Epoch 960 | Train Loss 0.19500477306123667 | Test Loss 0.1959774414287147\n", "Epoch 980 | Train Loss 0.19500466499285687 | Test Loss 0.19597783753361098\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["# Results\n", "\n", "Before viewing the results, we need to reverse the transformations applied on the output variable y.\n", "\n", "The `inverse_transform` method of the StandardScaler object will help us."], "metadata": {"id": "ldFMBPuvr0l0"}}, {"cell_type": "code", "source": ["from sklearn.metrics import mean_squared_error\n", "y_test = output_scalar.inverse_transform(y_test[np.newaxis,:])\n", "y_hat  = output_scalar.inverse_transform(y_hat[np.newaxis,:])\n", "error = (((y_test - y_hat) ** 2).sum() / num_test )\n", "print(\"Test Set Error\", error)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZycI4aExMsoC", "executionInfo": {"status": "ok", "timestamp": 1689334133385, "user_tz": -330, "elapsed": 382, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "83f9b75c-c862-4e14-ff32-90f0692b70fd"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Test Set Error 0.5263663693241227\n"]}]}, {"cell_type": "markdown", "source": ["# Libraries\n", "\n", "Instead of coding everything from scratch, i.e the model, loss functions, and gradient calculations, there are many libaries that have implemented many machine learning algorithms for us.\n", "\n", "These libraries will generally be faster and more optimized. We can use the LinearRegression and SGD regressor module from scikit learn to compare our model"], "metadata": {"id": "UoYobRS9uBIv"}}, {"cell_type": "code", "source": ["from sklearn.linear_model import SGDRegressor\n", "\n", "\n", "x_train, x_test, y_train, y_test = dataset_copy\n", "sgd = SGDRegressor()\n", "sgd.fit(x_train.T, y_train)\n", "y_hat = sgd.predict(x_test.T)\n", "y_test = output_scalar.inverse_transform(y_test[np.newaxis,:])\n", "y_hat  = output_scalar.inverse_transform(y_hat[np.newaxis,:])\n", "error = mean_squared_error(y_test, y_hat, squared = True)\n", "print(\"Test Set Error\", error)"], "metadata": {"id": "txWBY_0eoNN_", "executionInfo": {"status": "ok", "timestamp": 1689334149451, "user_tz": -330, "elapsed": 374, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "fc456c6a-54ed-4db6-f3c4-0782da1afe91"}, "execution_count": 10, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Test Set Error 0.5283990996392857\n"]}]}, {"cell_type": "code", "source": ["from sklearn.linear_model import LinearRegression as LR\n", "\n", "x_train, x_test, y_train, y_test = dataset_copy\n", "lr = LR()\n", "lr.fit(x_train.T, y_train)\n", "y_hat = lr.predict(x_test.T)\n", "y_test = output_scalar.inverse_transform(y_test[np.newaxis,:])\n", "y_hat  = output_scalar.inverse_transform(y_hat[np.newaxis,:])\n", "error = mean_squared_error(y_test, y_hat, squared = True)\n", "print(\"Test Set Error\", error)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9CaqphG8TG7V", "executionInfo": {"status": "ok", "timestamp": 1689334153875, "user_tz": -330, "elapsed": 380, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "b431f7d2-24e8-4202-c3f0-272571154646"}, "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Test Set Error 0.5263803029005857\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "g_dOsp3mWi2L"}, "execution_count": null, "outputs": []}]}