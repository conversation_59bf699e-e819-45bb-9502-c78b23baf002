{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyNhkLlFNHdg7z+xQLpie6ok"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["## Welcome to the basic tutorial on logistic regression"], "metadata": {"id": "nvjlXItSwxq4"}}, {"cell_type": "markdown", "source": ["File download link: [insurance_data.csv](https://drive.google.com/file/d/1dbF6I_HfX8KHW7wFwXMSNg3CUFx4cJWE/view?usp=sharing)"], "metadata": {"id": "L5EtGnrBSary"}}, {"cell_type": "code", "source": ["!pip install -q gdown"], "metadata": {"id": "qpdnJXnISkmk"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["!gdown \"1dbF6I_HfX8KHW7wFwXMSNg3CUFx4cJWE\""], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9RocV0z1SqRH", "executionInfo": {"status": "ok", "timestamp": 1704435839508, "user_tz": -330, "elapsed": 1760, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "07ec8316-06b9-46ef-86d5-83d058ddf79d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Downloading...\n", "From: https://drive.google.com/uc?id=1dbF6I_HfX8KHW7wFwXMSNg3CUFx4cJWE\n", "To: /content/insurance_data.csv\n", "\r  0% 0.00/155 [00:00<?, ?B/s]\r100% 155/155 [00:00<00:00, 648kB/s]\n"]}]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_WJmk32iTqZO"}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from matplotlib import pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "source": ["df = pd.read_csv(\"insurance_data.csv\")\n", "df.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "iZFt4OBzT9Fe", "executionInfo": {"status": "ok", "timestamp": 1704435845383, "user_tz": -330, "elapsed": 553, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "4bf3bd13-2635-4418-8548-258017e0761e"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   age  bought_insurance\n", "0   22                 0\n", "1   25                 0\n", "2   47                 1\n", "3   52                 0\n", "4   46                 1"], "text/html": ["\n", "  <div id=\"df-9f89036a-a526-4fcf-a379-41a038706f8a\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>bought_insurance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>22</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>25</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>47</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>52</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>46</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-9f89036a-a526-4fcf-a379-41a038706f8a')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-9f89036a-a526-4fcf-a379-41a038706f8a button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-9f89036a-a526-4fcf-a379-41a038706f8a');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-f44b68c5-2371-4ad9-8e39-c6ae5bd929f8\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-f44b68c5-2371-4ad9-8e39-c6ae5bd929f8')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-f44b68c5-2371-4ad9-8e39-c6ae5bd929f8 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["plt.scatter(df.age,df.bought_insurance,marker='+',color='red')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 447}, "id": "M4iuN7YRT-me", "executionInfo": {"status": "ok", "timestamp": 1704435849076, "user_tz": -330, "elapsed": 530, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "6f75f162-bf0a-49f9-8a02-e56793f96e26"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<matplotlib.collections.PathCollection at 0x7a722260b430>"]}, "metadata": {}, "execution_count": 5}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["from sklearn.model_selection import train_test_split\n", "X_train, X_test, y_train, y_test = train_test_split(df.age.to_frame(),df.bought_insurance,train_size=0.8)"], "metadata": {"id": "O4cpYFP9UA23"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["X_test"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 238}, "id": "W8E3kUfBUEso", "executionInfo": {"status": "ok", "timestamp": 1704435856129, "user_tz": -330, "elapsed": 536, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "270e10f5-c422-4579-de2f-7e160a8faa24"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    age\n", "2    47\n", "15   55\n", "8    62\n", "5    56\n", "18   19\n", "7    60"], "text/html": ["\n", "  <div id=\"df-f4563450-b93f-432a-a2d7-bf60f5be1bbf\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>47</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>60</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-f4563450-b93f-432a-a2d7-bf60f5be1bbf')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-f4563450-b93f-432a-a2d7-bf60f5be1bbf button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-f4563450-b93f-432a-a2d7-bf60f5be1bbf');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-f4a59ea0-6cc1-46f9-be71-51823cc2e98f\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-f4a59ea0-6cc1-46f9-be71-51823cc2e98f')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-f4a59ea0-6cc1-46f9-be71-51823cc2e98f button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"]}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "code", "source": ["from sklearn.linear_model import LogisticRegression\n", "model = LogisticRegression()"], "metadata": {"id": "o2jQH8VTUGZI"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["model.fit(X_train, y_train)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 75}, "id": "1H5JXAk5UKvy", "executionInfo": {"status": "ok", "timestamp": 1704435859933, "user_tz": -330, "elapsed": 9, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "55e7419d-d540-4e91-e75b-d4cd04b6356a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["LogisticRegression()"], "text/html": ["<style>#sk-container-id-1 {color: black;background-color: white;}#sk-container-id-1 pre{padding: 0;}#sk-container-id-1 div.sk-toggleable {background-color: white;}#sk-container-id-1 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-1 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-1 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-1 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-1 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-1 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-1 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-1 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-1 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-1 div.sk-item {position: relative;z-index: 1;}#sk-container-id-1 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-1 div.sk-item::before, #sk-container-id-1 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-1 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-1 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-1 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-1 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-1 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-1 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-1 div.sk-label-container {text-align: center;}#sk-container-id-1 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-1 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LogisticRegression</label><div class=\"sk-toggleable__content\"><pre>LogisticRegression()</pre></div></div></div></div></div>"]}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "code", "source": ["# make predictions\n", "y_predicted = model.predict(X_test)\n", "y_predicted"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_KcoZLqIUMjq", "executionInfo": {"status": "ok", "timestamp": 1704435864019, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "23b679f7-a5be-4b02-af87-8530a72b1778"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([1, 1, 1, 1, 0, 1])"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["# let's look at the prediction probabilities\n", "model.predict_proba(X_test)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "W2CarjlEUR8b", "executionInfo": {"status": "ok", "timestamp": 1704435866475, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "0783d67b-7a81-42a0-fe03-183a14d21df0"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[0.31830857, 0.68169143],\n", "       [0.15609117, 0.84390883],\n", "       [0.07600578, 0.92399422],\n", "       [0.14144252, 0.85855748],\n", "       [0.92270023, 0.07729977],\n", "       [0.09394552, 0.90605448]])"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["model.score(X_test,y_test)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7JbkHs3rUey7", "executionInfo": {"status": "ok", "timestamp": 1704435868576, "user_tz": -330, "elapsed": 5, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "bfcc0fb7-9909-4428-e247-0c786c0b7f5a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["1.0"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["model.coef_ , model.intercept_"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fnM2eetIUgtr", "executionInfo": {"status": "ok", "timestamp": 1704435871800, "user_tz": -330, "elapsed": 732, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "45a8080d-5ece-470b-ccf5-9b804110d8b4"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(array([[0.11575604]]), array([-4.67897824]))"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "markdown", "source": ["## Building the Logistic regression model by hand"], "metadata": {"id": "Bnnh5prTUsvC"}}, {"cell_type": "code", "source": ["import math\n", "def sigmoid(x):\n", "  return 1 / (1 + math.exp(-x))"], "metadata": {"id": "7F2EkMnKUy8G"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def prediction_function(age):\n", "    z = 0.113 * age -4.52\n", "    y = sigmoid(z)\n", "    return y"], "metadata": {"id": "JkUsbnseU1JJ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["age = 35\n", "prediction_function(age)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bFMcwde5VVV9", "executionInfo": {"status": "ok", "timestamp": 1704435878217, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "c6c82f4b-624c-4a70-fb10-ce69a83f4e42"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0.36239134737807505"]}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "code", "source": ["threshold = 0.5"], "metadata": {"id": "jqVWp1rcVYBW"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["age = 35\n", "print(\"Will buy insurance:\", prediction_function(age) > threshold)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aOnITZknVf9-", "executionInfo": {"status": "ok", "timestamp": 1704435880763, "user_tz": -330, "elapsed": 8, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "5b851f30-ac2b-4e45-89df-0b10e3dde8fa"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Will buy insurance: False\n"]}]}, {"cell_type": "code", "source": ["age = 45\n", "print(\"Will buy insurance:\", prediction_function(age) > threshold)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "158P0VyTVlDW", "executionInfo": {"status": "ok", "timestamp": 1704435882040, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "16470514012612640143"}}, "outputId": "1a1bbeab-b81e-4244-8c42-47c5313ea97b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Will buy insurance: True\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "UQQyATjlGl7R"}, "execution_count": null, "outputs": []}]}