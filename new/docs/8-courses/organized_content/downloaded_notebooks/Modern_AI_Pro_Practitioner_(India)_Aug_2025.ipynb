{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# Welcome to the course: Modern AI Pro\n", "We welcome you for this amazing journery. There is a lot of cool things we would do from here.\n", "\n", "From essentials we are moving to the practitioner level!\n", "\n", "Repo 1: https://github.com/balajivis/modernaipro\n", "\n", "Repo 2: https://github.com/balajivis/mai-prac\n", "\n", "Theory slides: [LINK](https://drive.google.com/drive/folders/1f14KbNQ_5gZqcRjHrhMTDiZx35Xr7Jxd)\n", "\n", "Practice slides: [LINK](https://drive.google.com/drive/folders/1ZopmqyUNxA9g0V0_g78fj57dPPEE_3Im?usp=sharing)"], "metadata": {"id": "_cD_V_YTe8xE"}}, {"cell_type": "markdown", "source": ["## Basic setup\n", "\n", "1. [Setup split screen in Windows](https://support.lenovo.com/us/en/solutions/ht513508) || on Mac, use the spaces feature.\n", "4. [Install Ollama](https://ollama.com/download)\n", "5. [Install VSCode](https://code.visualstudio.com/download)\n", "6. [Install Git](https://git-scm.com/book/en/v2/Getting-Started-Installing-Git) and setup its path\n", "9. **ollama run gemma3:1b** or **ollama run deepseek-r1:1.5b**\n", "\n", "Optional:\n", "1. [Install Miniconda](https://docs.anaconda.com/miniconda/miniconda-install/)\n", "2. [Setup the path in Windows](https://eduand-alvarez.medium.com/setting-up-anaconda-on-your-windows-pc-6e39800c1afb)  || on Mac do **export PATH=\"\\$HOME/miniconda3/bin:$PATH\"** on the terminal\n", "3. *In your terminal, do the following:* **conda create -n modernai python==3.11**\n", "4. **conda activate modernai**\n", "10. **git clone https://github.com/balajivis/modernaipro**\n", "11. **cd modernaipro**\n", "12. **pip install -r requirements.txt** [don't worry about uvloop]\n", "13. Sign up & get API keys/token at **<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>**"], "metadata": {"id": "W-AeecK0A-Wa"}}, {"cell_type": "markdown", "source": ["### **Module 1**: Strengthening the application foundation\n", "Friday (4-6pm), Aug 1, 2025\n", "\n", "\n", "[Slides 0](https://docs.google.com/presentation/d/1aVakLkyuHwE3vt4wdTRqZrpRGeItqdbNAg0s5ycjs8Q/edit#slide=id.g1e2ee25a6cc_0_0)\n", "[Slides 1](https://docs.google.com/presentation/d/1spbvGJV5en_DN_dwUUlGaM1tEu1eOILd2dH4lr5EpTc/edit#slide=id.g1e2ee25a6cc_0_0)\n", "\n", "\n", "0. Practising LLMs on VSCode (0, 2, 3 folders)\n", "1. [First LLM Call on Colab](https://colab.research.google.com/drive/14I39CnLNSZYekRVzArEuE2Q1Sryhov2i#scrollTo=BF0-EoHaRS4W)\n", "\n", "\n", "We have covered these in basics and thus we will be zipping through this.\n"], "metadata": {"id": "3vxTrkQRNGxX"}}, {"cell_type": "markdown", "source": ["### **Module 2**: Building up the deep learning theory basics\n", "Friday (6 - 8pm IST), Aug 1, 2025\n", "\n", "Revisiting Deep learning, CNN, Transformer foundations"], "metadata": {"id": "Xosrkx1ZNveD"}}, {"cell_type": "markdown", "source": ["### **Module 3**: Moving from chabots to agents\n", "Saturday (9am - 12:10pm), Aug 2, 2025\n", "\n", "Understanding memory, tools and agent foundation.\n", "[Slides](https://docs.google.com/presentation/d/1bYrhKzT6TuU4v6ebeWz9uq1pZP12PA0Ur1pxpA05ulA/edit?usp=sharing)\n", "\n", "\n", "1. [First chatbot](https://colab.research.google.com/drive/1q14coEFNdJ6rgk8Nd0sZ4ymnufbDlqaq?usp=sharing)\n", "3. [Local LLM with HuggingFace](https://colab.research.google.com/drive/1bz8eDPHFVX9nNLGGFkMMp_-PZDn8BBQr?usp=sharing)\n", "0. [Multimodal chat](https://colab.research.google.com/drive/1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6#scrollTo=s3kha54YQN3g)\n", "1. [LLM with memory](https://colab.research.google.com/drive/1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1?usp=sharing)\n", "   Additional work: [Types of Memory in LLMs with Langchain](https://colab.research.google.com/drive/1AcyWGauS0KooIGpk5KBR6yKE62pgJUA1#scrollTo=-WM4x4obAbOw)\n", "2. [LLM with search](https://colab.research.google.com/drive/1mK9R-aJFWwREhuH99if9okpjxBhw2rt3?usp=sharing)\n", "1. [LLM with tools](https://colab.research.google.com/drive/16fm4MQuT6Z6kG6fHYn3HJAXqdg_zdr7g?usp=sharing)\n", "4. [Agents talking to one another](https://colab.research.google.com/drive/1GKnxasdBqh81YooV8oCqekTwB3YYTXuY#scrollTo=NQ9hO4O85SeW)\n", "4. [First agent with the ReAct Framework](https://colab.research.google.com/drive/1IK5JvjczY5QsTbqC7SWy206ZGFC4k5nw?usp=sharing)\n", "6. [Agents with Langgraph](https://colab.research.google.com/drive/15UuvEmi955OsIMTknxwh3pn3nE-XJptr?usp=sharing)\n", "7. [Advanced agents use case](https://colab.research.google.com/drive/1I2XOUw0S_vj58jOUOt8081dneiF_3wCm?usp=sharing)\n", "\n", "AI deployment: https://github.com/balajivis/mai-deploy-frontend\n", "Backend: https://github.com/balajivis/mai-deploy\n", "\n", "Project 1: Building a Perplexity clone\n", "\n", "Project 2: Building a top notch agent that can do deep stock analysis."], "metadata": {"id": "YyvM1_whOOim"}}, {"cell_type": "markdown", "source": ["### Applications of Agents across sectors\n", "\n", "1. **Automated Customer Support:**\n", "   - Deploy AI agents to handle customer inquiries, providing instant, accurate responses and escalating complex issues to human agents when necessary.\n", "\n", "2. **Intelligent Document Processing:**\n", "   - Utilize AI to automate the extraction, classification, and analysis of data from documents, streamlining workflows and reducing manual errors.\n", "\n", "3. **Predictive Maintenance:**\n", "   - Implement AI agents to monitor equipment performance, predict potential failures, and schedule maintenance proactively, minimizing downtime.\n", "\n", "4. **Dynamic Supply Chain Management:**\n", "   - Employ AI to analyze market trends, predict demand fluctuations, and optimize inventory levels, ensuring efficient supply chain operations.\n", "\n", "5. **Personalized Marketing Campaigns:**\n", "   - Leverage AI to analyze customer behavior and preferences, crafting targeted marketing strategies that enhance engagement and conversion rates.\n", "\n", "6. **Fraud Detection and Prevention:**\n", "   - Use AI agents to monitor transactions in real-time, identify suspicious activities, and prevent fraudulent actions, safeguarding company assets.\n", "\n", "7. **Human Resources Automation:**\n", "   - Apply AI to streamline recruitment by screening resumes, scheduling interviews, and managing employee onboarding processes efficiently.\n", "\n", "8. **Financial Forecasting:**\n", "   - Utilize AI to analyze financial data, predict market trends, and assist in strategic planning and investment decisions.\n", "\n", "9. **Regulatory Compliance Monitoring:**\n", "   - Implement AI agents to continuously monitor compliance requirements, ensuring adherence to regulations and reducing the risk of penalties.\n", "\n", "10. **Enhanced Cybersecurity:**\n", "    - Deploy AI to detect anomalies, respond to security threats in real-time, and adapt to emerging cyber threats, bolstering organizational security.\n"], "metadata": {"id": "OMaYYcQA38ec"}}, {"cell_type": "markdown", "source": ["## AI agents in tech\n", "\n", "1. **Code Review Automation:**\n", "   - Deploy AI agents to review code, detect bugs, enforce coding standards, and suggest improvements, reducing manual effort and ensuring high-quality code.\n", "\n", "2. **DevOps Pipeline Optimization:**\n", "   - Use AI agents to monitor CI/CD pipelines, predict bottlenecks, and automate deployment processes, enhancing efficiency in software delivery.\n", "\n", "3. **Incident Management in IT:**\n", "   - Implement AI agents to detect and diagnose IT incidents in real-time, suggest resolutions, and coordinate with teams for faster issue resolution.\n", "\n", "4. **Infrastructure Monitoring:**\n", "   - Leverage AI to monitor enterprise infrastructure health, predict failures, and recommend resource allocation for optimal performance.\n", "\n", "5. **Software Performance Testing:**\n", "   - Utilize agents to simulate user behavior, identify performance bottlenecks, and provide insights for optimization during testing phases.\n", "\n", "6. **Database Query Optimization:**\n", "   - Deploy AI to analyze database queries, suggest indexing strategies, and optimize execution plans for improved data retrieval speeds.\n", "\n", "7. **Digital Twin Management:**\n", "   - Use AI agents to manage and simulate digital twins of complex systems, enabling better analysis, optimization, and predictive maintenance.\n", "\n", "8. **API Gateway Optimization:**\n", "   - Implement agents to monitor API traffic, detect anomalies, and provide suggestions for scaling and optimizing API performance.\n", "\n", "9. **Knowledge Base Enhancement:**\n", "   - Leverage AI agents to continuously update and enrich knowledge bases by extracting insights from enterprise documents and interactions.\n", "\n", "10. **Tech Debt Prioritization:**\n", "    - Utilize AI to identify and prioritize technical debt in enterprise systems, helping teams focus on high-impact improvements.\n", "\n"], "metadata": {"id": "HgizikD14eZl"}}, {"cell_type": "markdown", "source": ["### **Module 4**: Deep document management with LLM\n", "Saturday (1 - 3pm), Aug 3, 2025\n", "\n", "Summarization with BrahmaSumm, analysis, table extraction, chatting with documents, kb creation.\n", "\n", "1. [Q&A on documents](https://colab.research.google.com/drive/1icYUUiBSAheX8Dfog0mhhQJeZdRlBlx-#scrollTo=vRFpSBs4aXDY)\n", "6. [Analyzing and Querying a Document with LLM Notebook](https://colab.research.google.com/drive/1G-3Zl8-eyxR_kP7B5gJSVCxcli0hsKoe)\n", "3. [Deep document analysis with BrahmaSumm](https://github.com/balajivis/brahmasumm)\n", "[Simple notebook](https://colab.research.google.com/drive/1Qt8-XbXODbWblnJns2qTZB5H72EJmGJ5#scrollTo=cLxzKp55yUa-)\n", "7. [Table Analysis with Unstructured IO Notebook](https://colab.research.google.com/drive/1YJ5pxuESgwcc107pNVIxh7uD2SWxRPDY?usp=sharing) -- 1 hour\n", "8. [Multimodal advanced table and image analysis](https://colab.research.google.com/drive/1n2MZj5CVlr1bXSq7JiKabbVU-RM1hO8s#scrollTo=IV018jkcRen5)\n", "9. [Deep document analysis](https://colab.research.google.com/drive/1zwJZLN63f6thjlhmZYiocIyUe_ViEYzl#scrollTo=wCzg74KkDtRT)\n"], "metadata": {"id": "7UDEAdI-QPzU"}}, {"cell_type": "markdown", "source": ["### **Module 5**: Understanding the LLM architecture in detail\n", "Saturday (3pm - 5pm), Aug 2, 2025\n", "\n", "RLHF, Model Merge, LoRA and more."], "metadata": {"id": "rp3TZy4LOAWx"}}, {"cell_type": "markdown", "source": ["### **Module 6**: Understanding theory of Finetuning and model building\n", "Sunday (10am - 12:30pm), Aug 3, 2025\n", "\n", "Learning how to fine tune models and understand the latest updates with model architectures.\n"], "metadata": {"id": "1PuvFxFlRPoV"}}, {"cell_type": "markdown", "source": ["### **Module 7**: Advanced RAG architectures\n", "Sunday (1:15pm - 3:15pm), Aug 3, 2025\n", "[Slides](https://docs.google.com/presentation/d/1Lp6OrpJzi0tVd_BM9Vw6dAa6eNGgk84kOsFQ_sHgHiM/edit#slide=id.g1e2ee25a6cc_0_0)\n", "\n", "We will look at GraphRAG, SQLRAG, explore merging elastic with embeddings, query expansion and visualization.\n", "\n", "1. [SQLRAG](https://colab.research.google.com/drive/18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi#scrollTo=XEvzlbS7Zi4E)\n", "2. [SQL RAG 2](https://colab.research.google.com/drive/1patliw_XCH-3eKJVrfVcjF8nLyPy1Mb7)\n", "2. [Advanced RAG for Finance Domain](https://colab.research.google.com/drive/15ewzaL4odo0jVqZro65AqVkWDI-_8Y3f?usp=drive_link)\n", "3. [GraphRAG](https://colab.research.google.com/drive/1AqYlciQosXZs4gzOTL9O7wbtTnc9RLCb#scrollTo=S8pL2YDQ2XzP)\n", "5. [RAG with authorization levels](https://colab.research.google.com/drive/1FarVX93keIA35dvMHr7k6FXBu1rTpv97?usp=sharing)\n", "\n"], "metadata": {"id": "F0yoz7wERuoF"}}, {"cell_type": "markdown", "source": ["### **Module 8**: Building the deployments & discussing business problems.\n", "Sunday (3:30 - 5:30pm), Aug 3, 2025\n", "\n", "We will do evaluation & observability with Langfuse, deploy LLM applications to the cloud and understand business problems.\n", "\n", "1. [Evaluating LLMs](https://colab.research.google.com/drive/1ohdsyb2athEkRawI2HF-6kBxhdFF8HTk?usp=sharing)\n", "10. Langfuse based observability (3. advanced llm)\n", "11. Final assesssment and project\n", "\n", "Notes:\n", "1. Fork this repo: https://github.com/balajivis/mai-deploy\n", "2. Next fork the front end: https://github.com/balajivis/mai-deploy-frontend\n", "2. Create an account on [Vercel](https://vercel.com/) and 2 new project with this forked repo\n", "3. Create environment with GROQ_API_KEY for the backend project\n"], "metadata": {"id": "NsS0PmSoSGWk"}}, {"cell_type": "markdown", "source": ["1. Significance of Document Intelligence\n", "2. [Generative AI for PMs](https://arxiv.org/abs/2407.17478)\n", "3. [Generative AI in Education](https://www.deakin.edu.au/students/study-support/study-resources/artificial-intelligence)\n", "4. [Lessons in Document management](https://arxiv.org/pdf/2402.07483)\n", "5. [Case study in Finance for compliance](https://go.prolego.com/llm-rag-study)\n", "6. [Case study in clinical use case](https://arxiv.org/pdf/2402.01733)\n", "7. [Fraud detection at JPM](https://medium.com/@jeyadev_needhi/how-ai-transformed-financial-fraud-detection-a-case-study-of-jp-morgan-chase-f92bbb0707bb)\n", "8. [Ideas in finance and more](https://www.assemblyai.com/blog/llm-use-cases/)\n", "\n", "**Special notebooks:**\n", "\n", "1. [Reading Excel data](https://colab.research.google.com/drive/1VWrfcB6rLIfTsg0g9uN_URwoGJWaG6ZC?usp=sharing)\n", "2. [DICOM data](https://colab.research.google.com/drive/14nOmrtFXMvqxXI7lia67rQnwGf_BJUFN)"], "metadata": {"id": "cuF29ecsZ6ec"}}, {"cell_type": "markdown", "source": ["### Issues with jailbreaking and attacks\n", "1. Dissemination of Harmful Content\n", "Generation of instructions for illegal activities (e.g., making explosives or hacking systems).\n", "Production of misinformation, propaganda, or conspiracy theories.\n", "Creation of content promoting violence, hate speech, or discrimination.\n", "2. Circumvention of Ethical Guidelines\n", "Exploitation of models to bypass ethical safeguards put in place by developers.\n", "Use of adversarial prompts to produce outputs that violate safety guidelines.\n", "Subversion of systems designed to refuse harmful or sensitive queries.\n", "3. Privacy and Security Breaches\n", "Extraction of sensitive or private information through adversarial queries.\n", "Discovery and exploitation of backdoors (Trojan attacks) in models.\n", "Facilitation of cyberattacks, such as phishing schemes or social engineering.\n", "4. Amplification of Social and Political Harm\n", "Automation of large-scale misinformation campaigns.\n", "Generation of biased or manipulative content that can skew public opinion.\n", "Support for coordinated malicious activities, such as election interference.\n", "5. Undermining of Trust in AI\n", "Erosion of public trust in AI systems due to their misuse.\n", "Difficulty in distinguishing between safe and compromised models.\n", "Potential legal and reputational risks for organizations deploying these models.\n", "6. Weaponization of AI Models\n", "Utilization of LLMs for criminal enterprises, such as fraud or identity theft.\n", "Development of malicious software or tools via AI assistance.\n", "Enhanced capabilities for adversaries in cyber and physical security contexts.\n", "7. Economic and Legal Implications\n", "Misuse leading to financial losses or liabilities for AI providers and users.\n", "Legal ramifications if models inadvertently aid in unlawful activities.\n", "Increased costs for deploying and maintaining robust defenses against such attacks.\n", "8. Ethical Dilemmas and Abuse\n", "Encouragement of unethical experimentation using compromised models.\n", "Risk of fostering harmful ideologies by providing tailored content.\n", "Exploitation of AI for unethical research or experimentation.\n", "9. <PERSON><PERSON><PERSON><PERSON> of AI Reliability\n", "Vulnerabilities in LLMs could lead to unintended outputs in benign applications.\n", "Reduced effectiveness in real-world deployments due to tampering.\n", "Degradation of model quality as safety measures are bypassed."], "metadata": {"id": "Nu3_up3m7R5Y"}}]}