{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "NumPy_Deepdive.ipynb", "provenance": [], "collapsed_sections": [], "authorship_tag": "ABX9TyNxn81BwkB5ITrjPbu2MisB"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["![image.png](data:image/png;base64,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)\n", "#Introduction to NumPy\n", "\n", "### Numerical Python and the N dimension Array object"], "metadata": {"id": "0UcBhEalgxoo"}}, {"cell_type": "code", "source": ["import numpy as np"], "metadata": {"id": "9uKqanLqg-sc"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Numpy Array vs Python list"], "metadata": {"id": "mT5g1aexhP-1"}}, {"cell_type": "code", "source": ["my_arr = np.arange(1000000)\n", "my_list = list(range(1000000))"], "metadata": {"id": "9cI4wbSXg_yP"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["%time for _ in range(10): my_arr2 = my_arr * 2"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DgIJ85h3hEV2", "executionInfo": {"status": "ok", "timestamp": 1649336310921, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "cad2ef05-92ea-404b-ecf3-5ad63cbf4f28"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["CPU times: user 14.6 ms, sys: 12.9 ms, total: 27.5 ms\n", "Wall time: 27.7 ms\n"]}]}, {"cell_type": "code", "source": ["%time for _ in range(10): my_list2 = [x * 2 for x in my_list]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "paB_9kOLhK0F", "executionInfo": {"status": "ok", "timestamp": 1649336312467, "user_tz": -330, "elapsed": 1549, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "6d92ec97-c007-4cd7-eee3-8947b0c46d9a"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["CPU times: user 824 ms, sys: 206 ms, total: 1.03 s\n", "Wall time: 1.06 s\n"]}]}, {"cell_type": "markdown", "source": ["NumPy-based algorithms are generally 10 to 100 times faster (or more) than their\n", "pure Python counterparts and use significantly less memory.\n", "\n", "### The NumPy ndarray: A Multidimensional Array Object"], "metadata": {"id": "-GhhkgcAhU9s"}}, {"cell_type": "markdown", "source": ["Use random.randn function to generate an array of any given shape"], "metadata": {"id": "3o_yUUgUCQoj"}}, {"cell_type": "code", "source": ["data = np.random.randn(2, 3)\n", "data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "duRtkV6IhMyt", "executionInfo": {"status": "ok", "timestamp": 1649336312467, "user_tz": -330, "elapsed": 37, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "51547824-2971-411d-9f0b-e9bf0888fd42"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-1.66240317,  0.46582857, -0.94128975],\n", "       [ 2.12045545,  0.89638834,  0.84809025]])"]}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "code", "source": ["data * 10"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "l_FbyinNhh3k", "executionInfo": {"status": "ok", "timestamp": 1649336312467, "user_tz": -330, "elapsed": 33, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "08eb40ff-bf46-4901-b29f-dd7f10d1eea9"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-16.62403168,   4.65828572,  -9.41289753],\n", "       [ 21.2045545 ,   8.96388342,   8.48090253]])"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "code", "source": ["data + data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M7YUkZCJhk_8", "executionInfo": {"status": "ok", "timestamp": 1649336312468, "user_tz": -330, "elapsed": 32, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "8c414a20-1a87-4041-b59d-434fbd444fbb"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-3.32480634,  0.93165714, -1.88257951],\n", "       [ 4.2409109 ,  1.79277668,  1.69618051]])"]}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "code", "source": ["data.shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jVKE98kLhmEb", "executionInfo": {"status": "ok", "timestamp": 1649336312468, "user_tz": -330, "elapsed": 30, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "38e0bf82-563c-4c64-dfa2-3ba35b0e73d7"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(2, 3)"]}, "metadata": {}, "execution_count": 8}]}, {"cell_type": "code", "source": ["data.dtype"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rKhno7-4htnT", "executionInfo": {"status": "ok", "timestamp": 1649336312468, "user_tz": -330, "elapsed": 28, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "480f4787-940f-45e1-b391-06ce15e31142"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["dtype('float64')"]}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "code", "source": ["np.random.randn(2,3,4) # creating two channeled 3x4 matrices"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9dKUiwW6Cjf_", "executionInfo": {"status": "ok", "timestamp": 1649336312468, "user_tz": -330, "elapsed": 26, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "e2a70783-436c-49dd-fe8b-21842b679270"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[[ 0.62017534,  0.07200996,  1.13130417, -2.2037881 ],\n", "        [ 0.17851321,  0.08689161, -0.54886161,  0.0955675 ],\n", "        [-0.78092458, -0.36341318,  1.53376352,  0.17055948]],\n", "\n", "       [[ 0.63771568,  1.1444865 ,  0.99659648,  0.10034857],\n", "        [-1.82697514,  0.676847  ,  1.21997186,  0.37449077],\n", "        [-0.89893606,  0.29015013, -2.3538539 ,  1.53141507]]])"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "markdown", "source": ["The easiest way to create an array is to use the array function. This accepts any sequence-like object (including other arrays) and produces a new NumPy array containing the passed data."], "metadata": {"id": "oEqWqkjZiMtZ"}}, {"cell_type": "code", "source": ["data1 = [1,2,3.5,4,5]\n", "arr1 = np.array(data1)\n", "arr1"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IAzzkpZKiD_Z", "executionInfo": {"status": "ok", "timestamp": 1649336312468, "user_tz": -330, "elapsed": 24, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "8c86dd00-4b83-48f0-90e2-b0616851fa0a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([1. , 2. , 3.5, 4. , 5. ])"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["arr1.dtype #automatic float conversion due to one floating number"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1Z7U67n-hyoa", "executionInfo": {"status": "ok", "timestamp": 1649336312469, "user_tz": -330, "elapsed": 24, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "861e0c90-390e-45bc-9d3c-1685186c8be3"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["dtype('float64')"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["data2 = [[1, 2, 3, 4], [5, 6, 7, 8]] # list of lists\n", "arr2 = np.array(data2);arr2"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "W9f16SWzi4a2", "executionInfo": {"status": "ok", "timestamp": 1649336312469, "user_tz": -330, "elapsed": 22, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "dbd3c7fc-164a-428e-ac6f-e2075c9d8cba"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[1, 2, 3, 4],\n", "       [5, 6, 7, 8]])"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["print(arr2.ndim,\"is the no. of dimensions and the shape is\",arr2.shape)\n", "arr2.dtype"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JxzK4FVoi-9-", "executionInfo": {"status": "ok", "timestamp": 1649336312469, "user_tz": -330, "elapsed": 20, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "69a72994-ce0f-4dae-efc1-edaadb289b34"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["2 is the no. of dimensions and the shape is (2, 4)\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["dtype('int64')"]}, "metadata": {}, "execution_count": 14}]}, {"cell_type": "code", "source": ["arr1 = np.array([1, 2, 3], dtype=np.float64)\n", "arr1"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "i-yV9YGLk7r4", "executionInfo": {"status": "ok", "timestamp": 1649336312469, "user_tz": -330, "elapsed": 18, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "250e37f0-0264-4773-b124-d95f024f8033"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([1., 2., 3.])"]}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "code", "source": ["#casting data types\n", "arr = np.array([3.7, -1.2, -2.6, 0.5, 12.9, 10.1])\n", "arr.astype(np.int32) # automatically truncates numbers after the decimal"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2Yk4oe6wlXTf", "executionInfo": {"status": "ok", "timestamp": 1649336312470, "user_tz": -330, "elapsed": 17, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "71a4b4be-8865-4a91-ecb4-129f66448dd7"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ 3, -1, -2,  0, 12, 10], dtype=int32)"]}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "markdown", "source": ["Shortcut functions"], "metadata": {"id": "tDpiKh_blSuf"}}, {"cell_type": "code", "source": ["np.zeros(10)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sxTjFTm9jHDW", "executionInfo": {"status": "ok", "timestamp": 1649336312470, "user_tz": -330, "elapsed": 16, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "5af1ea97-471d-44ee-b878-2d915b3f4393"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0., 0., 0., 0., 0., 0., 0., 0., 0., 0.])"]}, "metadata": {}, "execution_count": 17}]}, {"cell_type": "code", "source": ["np.ones((2,2))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dWhP--eojr3E", "executionInfo": {"status": "ok", "timestamp": 1649336312470, "user_tz": -330, "elapsed": 14, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "f2ff9c5b-89fb-48e2-d0c1-1c8cc8d7fe74"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[1., 1.],\n", "       [1., 1.]])"]}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["np.eye((5)) # eye or identity"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vAE9c4w5jtxs", "executionInfo": {"status": "ok", "timestamp": 1649336312470, "user_tz": -330, "elapsed": 12, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "d41e78a2-7541-4c8f-fe00-4d940791a597"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[1., 0., 0., 0., 0.],\n", "       [0., 1., 0., 0., 0.],\n", "       [0., 0., 1., 0., 0.],\n", "       [0., 0., 0., 1., 0.],\n", "       [0., 0., 0., 0., 1.]])"]}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "markdown", "source": ["Python range and Numpy arange"], "metadata": {"id": "Etk5Pb3Wj0yE"}}, {"cell_type": "code", "source": ["y =[x for x in range(10)]\n", "y"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SluSwOLAj3HD", "executionInfo": {"status": "ok", "timestamp": 1649336312470, "user_tz": -330, "elapsed": 10, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "2661677b-1938-4773-e98c-f79d050177ca"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]"]}, "metadata": {}, "execution_count": 20}]}, {"cell_type": "code", "source": ["np.arange(15) # direct usage - no generators"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fKJHYHA6j6cL", "executionInfo": {"status": "ok", "timestamp": 1649336313169, "user_tz": -330, "elapsed": 708, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "79f92b82-b84f-4f60-e0fd-a712c6772193"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14])"]}, "metadata": {}, "execution_count": 21}]}, {"cell_type": "markdown", "source": ["Arithmetic Operations"], "metadata": {"id": "L-sX3eMtnTgZ"}}, {"cell_type": "code", "source": ["arr = np.random.randn(3,3)\n", "arr"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AV6Pm7sqkYBC", "executionInfo": {"status": "ok", "timestamp": 1649336313169, "user_tz": -330, "elapsed": 84, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "2112b862-b36f-4a88-ee95-de9ee5465abc"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 0.35135587, -0.65583411, -1.82787927],\n", "       [-1.0666996 , -1.49500544,  1.31240276],\n", "       [-0.39605881, -1.22661861,  1.06373069]])"]}, "metadata": {}, "execution_count": 22}]}, {"cell_type": "code", "source": ["arr ** 2"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MHQXGnLLl8NW", "executionInfo": {"status": "ok", "timestamp": 1649336313170, "user_tz": -330, "elapsed": 85, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "bc77f5ef-2971-4f6e-8569-63aa6ca2810b"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[0.12345095, 0.43011838, 3.34114263],\n", "       [1.13784804, 2.23504125, 1.72240101],\n", "       [0.15686258, 1.50459321, 1.13152297]])"]}, "metadata": {}, "execution_count": 23}]}, {"cell_type": "code", "source": ["1 / arr"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XrLCE7pymESF", "executionInfo": {"status": "ok", "timestamp": 1649336313170, "user_tz": -330, "elapsed": 82, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "dabd613b-f11b-4dfc-bd66-62d9360534fd"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 2.84611721, -1.52477583, -0.54708208],\n", "       [-0.93747105, -0.66889389,  0.76196121],\n", "       [-2.52487753, -0.81524933,  0.94008757]])"]}, "metadata": {}, "execution_count": 24}]}, {"cell_type": "code", "source": ["arr * arr"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AtKUf0w-mGg9", "executionInfo": {"status": "ok", "timestamp": 1649336313170, "user_tz": -330, "elapsed": 80, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "0473de3c-813a-4675-fbcf-5ec8339bcfe8"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[0.12345095, 0.43011838, 3.34114263],\n", "       [1.13784804, 2.23504125, 1.72240101],\n", "       [0.15686258, 1.50459321, 1.13152297]])"]}, "metadata": {}, "execution_count": 25}]}, {"cell_type": "code", "source": ["arr ** 0.5 # complex numbers are turned to nan values"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kOrT0qFFmJmN", "executionInfo": {"status": "ok", "timestamp": 1649336313170, "user_tz": -330, "elapsed": 78, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "8072579d-cd94-49b8-f73d-afb55ab8adf1"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.7/dist-packages/ipykernel_launcher.py:1: RuntimeWarning: invalid value encountered in sqrt\n", "  \"\"\"Entry point for launching an IPython kernel.\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["array([[0.59275279,        nan,        nan],\n", "       [       nan,        nan, 1.14560148],\n", "       [       nan,        nan, 1.0313732 ]])"]}, "metadata": {}, "execution_count": 26}]}, {"cell_type": "code", "source": ["arr1 = np.arange(1,26)\n", "arr1 > 20"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Lnw5OUMvmsQb", "executionInfo": {"status": "ok", "timestamp": 1649336313170, "user_tz": -330, "elapsed": 76, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "6f33a2ac-3ee1-46f4-9690-6adc40bd851d"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([<PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse,\n", "       False, False, False, False, False, False, False, False, False,\n", "       False, False,  True,  True,  True,  True,  True])"]}, "metadata": {}, "execution_count": 27}]}, {"cell_type": "markdown", "source": ["Basic Indexing and Slicing"], "metadata": {"id": "i7d52OpqnPnq"}}, {"cell_type": "code", "source": ["arr1[10]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iNSpywspm2QD", "executionInfo": {"status": "ok", "timestamp": 1649336313171, "user_tz": -330, "elapsed": 75, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "60458308-c8f7-4c80-d151-7ea74655a5f2"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["11"]}, "metadata": {}, "execution_count": 28}]}, {"cell_type": "code", "source": ["arr1[5:9]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RLBBu7_rnaCp", "executionInfo": {"status": "ok", "timestamp": 1649336313171, "user_tz": -330, "elapsed": 73, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "c1a00b3f-5025-4a44-a689-0941169fb0b2"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([6, 7, 8, 9])"]}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "code", "source": ["arr1[:6], arr1[6:]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vB1HGdlGnf-p", "executionInfo": {"status": "ok", "timestamp": 1649336313171, "user_tz": -330, "elapsed": 71, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "f9f8ce42-9891-462d-89e0-7fc26005fe77"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(array([1, 2, 3, 4, 5, 6]),\n", " array([ 7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23,\n", "        24, 25]))"]}, "metadata": {}, "execution_count": 30}]}, {"cell_type": "code", "source": ["arr1[5:9] = 0\n", "arr1[:15]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DwYEifuMnjx5", "executionInfo": {"status": "ok", "timestamp": 1649336313171, "user_tz": -330, "elapsed": 69, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "6afc6f5d-e2dc-4e68-d67b-0ea97c92d518"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ 1,  2,  3,  4,  5,  0,  0,  0,  0, 10, 11, 12, 13, 14, 15])"]}, "metadata": {}, "execution_count": 31}]}, {"cell_type": "markdown", "source": ["An important first distinction from Python’s built-in lists is that array slices are views on the original array. This means that the data is not copied, and any modifications to the view will be reflected in the source array."], "metadata": {"id": "_937MmGooPm2"}}, {"cell_type": "code", "source": ["arr_slice = arr1[5:9]\n", "arr_slice"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dLp9PACrn3ug", "executionInfo": {"status": "ok", "timestamp": 1649336313172, "user_tz": -330, "elapsed": 69, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "47a3c71f-01d7-4dad-f02d-b09106c4288f"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0, 0, 0, 0])"]}, "metadata": {}, "execution_count": 32}]}, {"cell_type": "code", "source": ["arr_slice[1] = 123\n", "arr1"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zaMjtIqWooml", "executionInfo": {"status": "ok", "timestamp": 1649336313172, "user_tz": -330, "elapsed": 67, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "4154a63d-71db-437d-bdda-dd7ff5ab027c"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([  1,   2,   3,   4,   5,   0, 123,   0,   0,  10,  11,  12,  13,\n", "        14,  15,  16,  17,  18,  19,  20,  21,  22,  23,  24,  25])"]}, "metadata": {}, "execution_count": 33}]}, {"cell_type": "code", "source": ["arr_slice[:] = 456\n", "arr1 # original array is modified unless copied"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fkKCEWIPoxTN", "executionInfo": {"status": "ok", "timestamp": 1649336313172, "user_tz": -330, "elapsed": 66, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "6a05e1f7-f626-45b0-ad90-aa7d991ced9e"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([  1,   2,   3,   4,   5, 456, 456, 456, 456,  10,  11,  12,  13,\n", "        14,  15,  16,  17,  18,  19,  20,  21,  22,  23,  24,  25])"]}, "metadata": {}, "execution_count": 34}]}, {"cell_type": "markdown", "source": ["2D array slicing"], "metadata": {"id": "9LT5P1AypbGa"}}, {"cell_type": "code", "source": ["arr2d = np.random.randn(4,3)\n", "arr2d"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uV0MAW8vpJOM", "executionInfo": {"status": "ok", "timestamp": 1649336313172, "user_tz": -330, "elapsed": 64, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "3a5df837-417a-4e9f-ac33-4828ac44511e"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-2.14821612,  0.14381553,  0.74348174],\n", "       [-1.13978873,  0.42638824,  0.02417103],\n", "       [ 1.27448042,  1.03480603,  0.78745096],\n", "       [-1.88733474,  0.28047069, -0.06912137]])"]}, "metadata": {}, "execution_count": 35}]}, {"cell_type": "code", "source": ["arr2d[:2]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GWf6fc_EphHr", "executionInfo": {"status": "ok", "timestamp": 1649336313173, "user_tz": -330, "elapsed": 63, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "aece900f-1aee-46ea-f1a8-b169028ae210"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-2.14821612,  0.14381553,  0.74348174],\n", "       [-1.13978873,  0.42638824,  0.02417103]])"]}, "metadata": {}, "execution_count": 36}]}, {"cell_type": "code", "source": ["arr2d[:2,:1]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Rmnbhx0Kpimb", "executionInfo": {"status": "ok", "timestamp": 1649336313173, "user_tz": -330, "elapsed": 62, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "f7bef495-9fe5-4e04-a513-8aabb6699ce5"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-2.14821612],\n", "       [-1.13978873]])"]}, "metadata": {}, "execution_count": 37}]}, {"cell_type": "code", "source": ["arr2d[:2,1:]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OVZwYbcmp2k6", "executionInfo": {"status": "ok", "timestamp": 1649336313173, "user_tz": -330, "elapsed": 60, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "18f5690d-3562-49f2-e45e-af03174cab53"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[0.14381553, 0.74348174],\n", "       [0.42638824, 0.02417103]])"]}, "metadata": {}, "execution_count": 38}]}, {"cell_type": "markdown", "source": ["![image.png](data:image/png;base64,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*********************************************************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************************************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)"], "metadata": {"id": "nYRhtCTKqVfw"}}, {"cell_type": "markdown", "source": ["Boolean Indexing"], "metadata": {"id": "H9GEZkv9sXOw"}}, {"cell_type": "code", "source": ["names = np.array(['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'])\n", "print(names)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OxYlayKZsVzi", "executionInfo": {"status": "ok", "timestamp": 1649336313173, "user_tz": -330, "elapsed": 58, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "8be0556a-5c78-438f-f64b-c37a54d97a0f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["['<PERSON>' '<PERSON>' '<PERSON>' 'Bob' 'Will' 'Joe' '<PERSON>']\n"]}]}, {"cell_type": "code", "source": ["data = np.random.randn(7, 4)\n", "data # create a dummy array with 7 rows, correspoding to the 7 names"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "35bM04eFEMKE", "executionInfo": {"status": "ok", "timestamp": 1649336313174, "user_tz": -330, "elapsed": 58, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "6b5a1dd8-257e-49b0-fdae-6eef0f25db4c"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-0.34517719, -0.93701936, -1.2245075 , -1.15069924],\n", "       [-0.85096141, -0.64569518, -1.3850998 ,  0.46119235],\n", "       [ 0.37365685, -0.15228119, -0.15921334, -0.52108236],\n", "       [-0.66187076,  1.22060226,  0.49314325, -0.45843227],\n", "       [-0.27244354,  1.86474999, -0.40857173,  0.35759429],\n", "       [ 0.91571901, -1.0922805 , -0.87283932,  1.15663369],\n", "       [ 0.45446829, -0.85593173,  2.36654846, -1.22505954]])"]}, "metadata": {}, "execution_count": 40}]}, {"cell_type": "code", "source": ["names == '<PERSON>'"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fnzkO_TuqCOZ", "executionInfo": {"status": "ok", "timestamp": 1649336313174, "user_tz": -330, "elapsed": 56, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "16196db1-8650-4d6a-abd6-004593c52b28"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ True, False, False,  True, False, False, False])"]}, "metadata": {}, "execution_count": 41}]}, {"cell_type": "code", "source": ["data[names == 'Bob'] # return only Bob's rows"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Ix9ICaXOsjCi", "executionInfo": {"status": "ok", "timestamp": 1649336313174, "user_tz": -330, "elapsed": 54, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "6409bdb3-3bb9-4b0f-d74c-4d6477bab676"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-0.34517719, -0.93701936, -1.2245075 , -1.15069924],\n", "       [-0.66187076,  1.22060226,  0.49314325, -0.45843227]])"]}, "metadata": {}, "execution_count": 42}]}, {"cell_type": "code", "source": ["data[names == '<PERSON>', 2:] # return only Bob's rows and slice columns"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tRcpgqoGskpi", "executionInfo": {"status": "ok", "timestamp": 1649336313174, "user_tz": -330, "elapsed": 53, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "bdf2db46-d6be-42ea-90da-554d2f04e8ae"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-1.2245075 , -1.15069924],\n", "       [ 0.49314325, -0.45843227]])"]}, "metadata": {}, "execution_count": 43}]}, {"cell_type": "code", "source": ["data[names == '<PERSON>', 3] # return only 3rd column from <PERSON>'s rows"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NjT28Eazsz4Z", "executionInfo": {"status": "ok", "timestamp": 1649336313174, "user_tz": -330, "elapsed": 51, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "e4f6db21-9d1f-4036-e44c-cc9e59bf051f"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([-1.15069924, -0.45843227])"]}, "metadata": {}, "execution_count": 44}]}, {"cell_type": "code", "source": ["#Return rows where the names != '<PERSON>'\n", "data[~(names == '<PERSON>')]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Jdq31O7As42B", "executionInfo": {"status": "ok", "timestamp": 1649336313175, "user_tz": -330, "elapsed": 50, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "4226b33a-6ce6-4dfc-f800-be3546c09303"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-0.85096141, -0.64569518, -1.3850998 ,  0.46119235],\n", "       [ 0.37365685, -0.15228119, -0.15921334, -0.52108236],\n", "       [-0.27244354,  1.86474999, -0.40857173,  0.35759429],\n", "       [ 0.91571901, -1.0922805 , -0.87283932,  1.15663369],\n", "       [ 0.45446829, -0.85593173,  2.36654846, -1.22505954]])"]}, "metadata": {}, "execution_count": 45}]}, {"cell_type": "code", "source": ["mask = (names == 'Bob') | (names == 'Will')\n", "mask"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aowG5_z3s9m5", "executionInfo": {"status": "ok", "timestamp": 1649336313175, "user_tz": -330, "elapsed": 48, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "fcdd774a-fc7c-44fc-e5ba-6452ccf8bacf"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ True, False,  True,  True,  True, False, False])"]}, "metadata": {}, "execution_count": 46}]}, {"cell_type": "code", "source": ["data[mask] # Array index using a complex mask"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "U4Tq65aKtN_o", "executionInfo": {"status": "ok", "timestamp": 1649336313175, "user_tz": -330, "elapsed": 46, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "4b126150-cdab-4fb5-cafb-2ddaaa6fa59a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-0.34517719, -0.93701936, -1.2245075 , -1.15069924],\n", "       [ 0.37365685, -0.15228119, -0.15921334, -0.52108236],\n", "       [-0.66187076,  1.22060226,  0.49314325, -0.45843227],\n", "       [-0.27244354,  1.86474999, -0.40857173,  0.35759429]])"]}, "metadata": {}, "execution_count": 47}]}, {"cell_type": "code", "source": ["data[data < 0] = 0 # index into the array and set all negatives to 0\n", "data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "o11ctKzitO_g", "executionInfo": {"status": "ok", "timestamp": 1649336313175, "user_tz": -330, "elapsed": 44, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "662eaf8a-cb84-4369-9991-b275392e9a65"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[0.        , 0.        , 0.        , 0.        ],\n", "       [0.        , 0.        , 0.        , 0.46119235],\n", "       [0.37365685, 0.        , 0.        , 0.        ],\n", "       [0.        , 1.22060226, 0.49314325, 0.        ],\n", "       [0.        , 1.86474999, 0.        , 0.35759429],\n", "       [0.91571901, 0.        , 0.        , 1.15663369],\n", "       [0.45446829, 0.        , 2.36654846, 0.        ]])"]}, "metadata": {}, "execution_count": 48}]}, {"cell_type": "code", "source": ["names"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "k2R1ODP1tuQe", "executionInfo": {"status": "ok", "timestamp": 1649336313176, "user_tz": -330, "elapsed": 43, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "3afa1ad9-da6f-48d8-ec0b-cda95f554750"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array(['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'], dtype='<U4')"]}, "metadata": {}, "execution_count": 49}]}, {"cell_type": "code", "source": ["data[names != 'Joe'] = 7 # Set all non-Joe values to 7\n", "data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "urg7eQFXtalf", "executionInfo": {"status": "ok", "timestamp": 1649336313176, "user_tz": -330, "elapsed": 42, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "2ed831db-82e5-47f8-df63-c3a43f0079de"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[7.        , 7.        , 7.        , 7.        ],\n", "       [0.        , 0.        , 0.        , 0.46119235],\n", "       [7.        , 7.        , 7.        , 7.        ],\n", "       [7.        , 7.        , 7.        , 7.        ],\n", "       [7.        , 7.        , 7.        , 7.        ],\n", "       [0.91571901, 0.        , 0.        , 1.15663369],\n", "       [0.45446829, 0.        , 2.36654846, 0.        ]])"]}, "metadata": {}, "execution_count": 50}]}, {"cell_type": "markdown", "source": ["### FANCY INDEXING"], "metadata": {"id": "CoGxfnGFuIp2"}}, {"cell_type": "code", "source": ["arr = np.empty((8, 4))\n", "for i in range(8):\n", "    arr[i] = i\n", "arr"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bQiVC5astkm_", "executionInfo": {"status": "ok", "timestamp": 1649336313176, "user_tz": -330, "elapsed": 40, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "84265618-e924-4b29-f1bc-1d5f248e86c8"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[0., 0., 0., 0.],\n", "       [1., 1., 1., 1.],\n", "       [2., 2., 2., 2.],\n", "       [3., 3., 3., 3.],\n", "       [4., 4., 4., 4.],\n", "       [5., 5., 5., 5.],\n", "       [6., 6., 6., 6.],\n", "       [7., 7., 7., 7.]])"]}, "metadata": {}, "execution_count": 51}]}, {"cell_type": "code", "source": ["arr[[4, 3, 0, 6]] # get the chosen rows in any order"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "s2avyyXMuMM-", "executionInfo": {"status": "ok", "timestamp": 1649336313176, "user_tz": -330, "elapsed": 38, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "9701a36a-1011-4a73-b1a2-399b8bc8474e"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[4., 4., 4., 4.],\n", "       [3., 3., 3., 3.],\n", "       [0., 0., 0., 0.],\n", "       [6., 6., 6., 6.]])"]}, "metadata": {}, "execution_count": 52}]}, {"cell_type": "code", "source": ["arr[[-3, -5, -7]] # even in reverse order"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7lPEii5DuQWt", "executionInfo": {"status": "ok", "timestamp": 1649336313177, "user_tz": -330, "elapsed": 38, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "d7c7c56c-7aa4-4ec2-d8c1-2c2b5fb641b2"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[5., 5., 5., 5.],\n", "       [3., 3., 3., 3.],\n", "       [1., 1., 1., 1.]])"]}, "metadata": {}, "execution_count": 53}]}, {"cell_type": "code", "source": ["arr = np.arange(32).reshape((8, 4))\n", "arr #break 32 numbers into 8x4 matrix"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TrwXxjp_uYid", "executionInfo": {"status": "ok", "timestamp": 1649336313177, "user_tz": -330, "elapsed": 36, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "f16f13a1-61d7-4315-fdd7-96ea87e9d38c"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 0,  1,  2,  3],\n", "       [ 4,  5,  6,  7],\n", "       [ 8,  9, 10, 11],\n", "       [12, 13, 14, 15],\n", "       [16, 17, 18, 19],\n", "       [20, 21, 22, 23],\n", "       [24, 25, 26, 27],\n", "       [28, 29, 30, 31]])"]}, "metadata": {}, "execution_count": 54}]}, {"cell_type": "code", "source": ["arr[[1, 5, 7, 2], [0, 3, 1, 2]] # [i][j]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4fbDnqdCugRV", "executionInfo": {"status": "ok", "timestamp": 1649336313177, "user_tz": -330, "elapsed": 34, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "6e9e7c08-a454-46bd-d412-e4f8afe51a82"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ 4, 23, 29, 10])"]}, "metadata": {}, "execution_count": 55}]}, {"cell_type": "code", "source": ["arr[[1, 5, 7, 2]][:, [0, 3, 1, 2]] #reorder columns on selection"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_9l6nyE1usLs", "executionInfo": {"status": "ok", "timestamp": 1649336313177, "user_tz": -330, "elapsed": 32, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "7a1e36f7-5da2-4f81-fb63-13337de86ef4"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 4,  7,  5,  6],\n", "       [20, 23, 21, 22],\n", "       [28, 31, 29, 30],\n", "       [ 8, 11,  9, 10]])"]}, "metadata": {}, "execution_count": 56}]}, {"cell_type": "markdown", "source": ["### Transposing Arrays"], "metadata": {"id": "qAUZ61dNwJCg"}}, {"cell_type": "code", "source": ["arr = np.arange(15).reshape(3,5)\n", "arr"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "weZHt7sxvY8z", "executionInfo": {"status": "ok", "timestamp": 1649336313177, "user_tz": -330, "elapsed": 30, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "6fd48b4c-c31a-440a-99b6-a894dcf2f807"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 0,  1,  2,  3,  4],\n", "       [ 5,  6,  7,  8,  9],\n", "       [10, 11, 12, 13, 14]])"]}, "metadata": {}, "execution_count": 57}]}, {"cell_type": "code", "source": ["arr.T # convert rows to columns and vice-versa"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ingsw1e5wPbQ", "executionInfo": {"status": "ok", "timestamp": 1649336313178, "user_tz": -330, "elapsed": 30, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "50c92e4d-0495-4ea2-b27d-f75bf99cc4e5"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 0,  5, 10],\n", "       [ 1,  6, 11],\n", "       [ 2,  7, 12],\n", "       [ 3,  8, 13],\n", "       [ 4,  9, 14]])"]}, "metadata": {}, "execution_count": 58}]}, {"cell_type": "markdown", "source": ["Dot product:\n", "(M x N) . (N x K) = (M x K)"], "metadata": {"id": "FEWJhtmmwgTH"}}, {"cell_type": "code", "source": ["arr2 = np.random.randn(6,3)\n", "arr2"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "quo_VmqJwTLw", "executionInfo": {"status": "ok", "timestamp": 1649336313178, "user_tz": -330, "elapsed": 28, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "29707e75-707b-47df-a58d-0dfd74f0abf7"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-2.19231026, -0.22134498, -1.25943972],\n", "       [ 0.33185178,  1.46378314, -0.08029141],\n", "       [-0.54682538, -0.3191993 , -0.38380802],\n", "       [-0.79341612, -0.11154787,  0.65953504],\n", "       [-0.4583885 , -0.97534718, -0.68397926],\n", "       [ 0.11582993,  1.38399811,  0.03081935]])"]}, "metadata": {}, "execution_count": 59}]}, {"cell_type": "code", "source": ["arr2.shape,arr.shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UwZrWDQCxU2D", "executionInfo": {"status": "ok", "timestamp": 1649336313178, "user_tz": -330, "elapsed": 27, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "7f4e6337-2801-4e33-dad5-d3003925cdff"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["((6, 3), (3, 5))"]}, "metadata": {}, "execution_count": 60}]}, {"cell_type": "code", "source": ["np.dot(arr2,arr)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ktq5SW5cw7su", "executionInfo": {"status": "ok", "timestamp": 1649336313178, "user_tz": -330, "elapsed": 25, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "f1b0dd06-52ec-475e-f98a-2bfd1380f4bb"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-13.70112205, -17.37421701, -21.04731196, -24.72040692,\n", "        -28.39350187],\n", "       [  6.51600164,   8.23134516,   9.94668868,  11.66203219,\n", "         13.37737571],\n", "       [ -5.43407667,  -6.68390937,  -7.93374207,  -9.18357477,\n", "        -10.43340747],\n", "       [  6.03761112,   5.79218218,   5.54675324,   5.3013243 ,\n", "          5.05589536],\n", "       [-11.71652849, -13.83424343, -15.95195837, -18.06967331,\n", "        -20.18738825],\n", "       [  7.22818404,   8.75883142,  10.28947881,  11.8201262 ,\n", "         13.35077358]])"]}, "metadata": {}, "execution_count": 61}]}, {"cell_type": "code", "source": ["arr2 @ arr"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XN_1R9PY12rP", "executionInfo": {"status": "ok", "timestamp": 1649336313182, "user_tz": -330, "elapsed": 27, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "d9f11e26-e72c-48b6-be77-7493c779da5a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-13.70112205, -17.37421701, -21.04731196, -24.72040692,\n", "        -28.39350187],\n", "       [  6.51600164,   8.23134516,   9.94668868,  11.66203219,\n", "         13.37737571],\n", "       [ -5.43407667,  -6.68390937,  -7.93374207,  -9.18357477,\n", "        -10.43340747],\n", "       [  6.03761112,   5.79218218,   5.54675324,   5.3013243 ,\n", "          5.05589536],\n", "       [-11.71652849, -13.83424343, -15.95195837, -18.06967331,\n", "        -20.18738825],\n", "       [  7.22818404,   8.75883142,  10.28947881,  11.8201262 ,\n", "         13.35077358]])"]}, "metadata": {}, "execution_count": 62}]}, {"cell_type": "markdown", "source": ["### Universal Functions: Fast Element-Wise Array Functions"], "metadata": {"id": "L1d30mkBxqci"}}, {"cell_type": "code", "source": ["arr = np.arange(10)\n", "np.sqrt(arr)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "COp3X4Exxjy8", "executionInfo": {"status": "ok", "timestamp": 1649336313183, "user_tz": -330, "elapsed": 26, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "c2e2c882-50e7-4695-da06-68ff874bc108"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0.        , 1.        , 1.41421356, 1.73205081, 2.        ,\n", "       2.23606798, 2.44948974, 2.64575131, 2.82842712, 3.        ])"]}, "metadata": {}, "execution_count": 63}]}, {"cell_type": "code", "source": ["np.exp(arr).astype(int)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "akLf5WXkxJP2", "executionInfo": {"status": "ok", "timestamp": 1649336313183, "user_tz": -330, "elapsed": 25, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "5091ee8f-dbec-453f-ed3f-057f5565be21"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([   1,    2,    7,   20,   54,  148,  403, 1096, 2980, 8103])"]}, "metadata": {}, "execution_count": 64}]}, {"cell_type": "code", "source": ["x = np.random.randn(8)\n", "y = np.random.randn(8)\n", "x,y"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Dq6EDtrFxzVE", "executionInfo": {"status": "ok", "timestamp": 1649336313183, "user_tz": -330, "elapsed": 23, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "1c61ec2e-3867-42f0-a90c-3413c8298b56"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(array([ 0.16109749, -1.68769834,  1.950852  ,  0.97632896,  0.60913509,\n", "        -0.43457008, -0.8400796 , -0.1252286 ]),\n", " array([-0.63635524,  0.69057897, -1.58285433,  0.00632717, -1.21678824,\n", "        -1.36575625, -0.40551643,  0.34059987]))"]}, "metadata": {}, "execution_count": 65}]}, {"cell_type": "code", "source": ["np.maximum(x,y)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_sJQkf-_yK3r", "executionInfo": {"status": "ok", "timestamp": 1649336313985, "user_tz": -330, "elapsed": 823, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "02ef8b19-3642-48bf-dbe3-cd88042abd50"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ 0.16109749,  0.69057897,  1.950852  ,  0.97632896,  0.60913509,\n", "       -0.43457008, -0.40551643,  0.34059987])"]}, "metadata": {}, "execution_count": 66}]}, {"cell_type": "code", "source": ["np.max(x)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kEOkQN87yN4K", "executionInfo": {"status": "ok", "timestamp": 1649336313985, "user_tz": -330, "elapsed": 25, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "73e557f3-9dcf-4db9-d329-3bae81fa2109"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["1.9508519953437038"]}, "metadata": {}, "execution_count": 67}]}, {"cell_type": "code", "source": ["y = np.arange(10)\n", "np.sqrt(y)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UGNG4NqdyS_a", "executionInfo": {"status": "ok", "timestamp": 1649336313986, "user_tz": -330, "elapsed": 23, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "1c5ec6e9-0cb6-4a50-ad26-d9847e2dd832"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0.        , 1.        , 1.41421356, 1.73205081, 2.        ,\n", "       2.23606798, 2.44948974, 2.64575131, 2.82842712, 3.        ])"]}, "metadata": {}, "execution_count": 68}]}, {"cell_type": "markdown", "source": ["### Array-Oriented Programming with <PERSON><PERSON><PERSON>"], "metadata": {"id": "UkKe1yLazTCP"}}, {"cell_type": "markdown", "source": ["evaluate the function sqrt(x^2 + y^2) across a regular grid of values"], "metadata": {"id": "zuK9x_2lzVhP"}}, {"cell_type": "code", "source": ["points = np.arange(-5, 5, 0.01) # 1000 equally spaced points\n", "xs, ys = np.meshgrid(points, points)\n", "ys"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DQDovlNvyaca", "executionInfo": {"status": "ok", "timestamp": 1649336313986, "user_tz": -330, "elapsed": 21, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "de6d3ad2-0b6b-4d1c-82fa-ec37be90afa9"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[-5.  , -5.  , -5.  , ..., -5.  , -5.  , -5.  ],\n", "       [-4.99, -4.99, -4.99, ..., -4.99, -4.99, -4.99],\n", "       [-4.98, -4.98, -4.98, ..., -4.98, -4.98, -4.98],\n", "       ...,\n", "       [ 4.97,  4.97,  4.97, ...,  4.97,  4.97,  4.97],\n", "       [ 4.98,  4.98,  4.98, ...,  4.98,  4.98,  4.98],\n", "       [ 4.99,  4.99,  4.99, ...,  4.99,  4.99,  4.99]])"]}, "metadata": {}, "execution_count": 69}]}, {"cell_type": "code", "source": ["z = np.sqrt(xs ** 2 + ys ** 2)\n", "z"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1cMTnD2kztUW", "executionInfo": {"status": "ok", "timestamp": 1649336313986, "user_tz": -330, "elapsed": 19, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "b19841da-7d48-4529-d7ed-91977ba79ab6"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[7.07106781, 7.06400028, 7.05693985, ..., 7.04988652, 7.05693985,\n", "        7.06400028],\n", "       [7.06400028, 7.05692568, 7.04985815, ..., 7.04279774, 7.04985815,\n", "        7.05692568],\n", "       [7.05693985, 7.04985815, 7.04278354, ..., 7.03571603, 7.04278354,\n", "        7.04985815],\n", "       ...,\n", "       [7.04988652, 7.04279774, 7.03571603, ..., 7.0286414 , 7.03571603,\n", "        7.04279774],\n", "       [7.05693985, 7.04985815, 7.04278354, ..., 7.03571603, 7.04278354,\n", "        7.04985815],\n", "       [7.06400028, 7.05692568, 7.04985815, ..., 7.04279774, 7.04985815,\n", "        7.05692568]])"]}, "metadata": {}, "execution_count": 70}]}, {"cell_type": "code", "source": ["import matplotlib.pyplot as plt\n", "plt.imshow(z, cmap=plt.cm.gray); plt.colorbar()\n", "plt.title(\"Image plot of $\\sqrt{x^2 + y^2}$ for a grid of values\")\n", "plt.draw()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 288}, "id": "XjOh_U6EzyNe", "executionInfo": {"status": "ok", "timestamp": 1649336313986, "user_tz": -330, "elapsed": 18, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "56d4a5db-a2a3-4481-ea68-cc6924e11ed1"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 2 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "code", "source": ["plt.close('all')"], "metadata": {"id": "HSw-GSZy0JU1"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["### Expressing Conditional Logic as Array Operations"], "metadata": {"id": "cG3iNXZs00Lz"}}, {"cell_type": "code", "source": ["xarr = np.array([1.1, 1.2, 1.3, 1.4, 1.5])\n", "yarr = np.array([2.1, 2.2, 2.3, 2.4, 2.5])\n", "cond = np.array([True, False, True, True, False])"], "metadata": {"id": "nXK6XgTi0Zo8"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["result = [(x if c else y) for x, y, c in zip(xarr, yarr, cond)]\n", "result"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "j4ZBoAKW02Yy", "executionInfo": {"status": "ok", "timestamp": 1649336313987, "user_tz": -330, "elapsed": 17, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "ba60f182-44e3-42d2-b713-6e3114e40db3"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[1.1, 2.2, 1.3, 1.4, 2.5]"]}, "metadata": {}, "execution_count": 74}]}, {"cell_type": "code", "source": ["result = np.where(cond, xarr, yarr)\n", "result"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9Du_0I_604Ri", "executionInfo": {"status": "ok", "timestamp": 1649336313987, "user_tz": -330, "elapsed": 15, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "f289d22c-9aab-4bd6-bba9-523882f69637"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([1.1, 2.2, 1.3, 1.4, 2.5])"]}, "metadata": {}, "execution_count": 75}]}, {"cell_type": "code", "source": ["arr = np.random.randn(4, 4)\n", "arr"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RiJncU1v1LEx", "executionInfo": {"status": "ok", "timestamp": 1649336313988, "user_tz": -330, "elapsed": 14, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "96e0326e-35d0-4d02-fa79-ad5446fb6615"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 0.0018766 , -1.21044895, -1.64382487,  0.92953595],\n", "       [ 1.7128464 , -0.9257241 ,  1.32927106,  0.35917986],\n", "       [-0.63811002, -1.296427  , -1.14840978,  1.78337978],\n", "       [ 0.86015516,  0.38956008,  0.4458388 , -1.72525041]])"]}, "metadata": {}, "execution_count": 76}]}, {"cell_type": "code", "source": ["arr > 0"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mGfMu6k11Skq", "executionInfo": {"status": "ok", "timestamp": 1649336313988, "user_tz": -330, "elapsed": 13, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "fec9e79c-60be-4536-801b-f9d27c60a440"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ True, False, False,  True],\n", "       [ True, False,  True,  True],\n", "       [False, <PERSON>als<PERSON>, <PERSON>als<PERSON>,  <PERSON>],\n", "       [ True,  True,  True, False]])"]}, "metadata": {}, "execution_count": 77}]}, {"cell_type": "code", "source": ["np.where(arr > 0, 2, -2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0wj9a0Mp1XKJ", "executionInfo": {"status": "ok", "timestamp": 1649336313988, "user_tz": -330, "elapsed": 11, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "46413010-e727-4cfd-e757-e241cb7b1372"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 2, -2, -2,  2],\n", "       [ 2, -2,  2,  2],\n", "       [-2, -2, -2,  2],\n", "       [ 2,  2,  2, -2]])"]}, "metadata": {}, "execution_count": 78}]}, {"cell_type": "code", "source": ["np.where(arr > 0, 2, arr) # set only positive values to 2"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zilUTIBN1Z6h", "executionInfo": {"status": "ok", "timestamp": 1649336313989, "user_tz": -330, "elapsed": 10, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "19ae853f-fb64-48b4-abca-8a301c028609"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 2.        , -1.21044895, -1.64382487,  2.        ],\n", "       [ 2.        , -0.9257241 ,  2.        ,  2.        ],\n", "       [-0.63811002, -1.296427  , -1.14840978,  2.        ],\n", "       [ 2.        ,  2.        ,  2.        , -1.72525041]])"]}, "metadata": {}, "execution_count": 79}]}, {"cell_type": "markdown", "source": ["### Loading and Saving arrays"], "metadata": {"id": "mlX1gmOc2UKd"}}, {"cell_type": "code", "source": ["arr = np.arange(10)\n", "np.save('some_array', arr)"], "metadata": {"id": "2lDcDuOr1hLQ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["np.load('some_array.npy')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WUk2i1Vo2bbO", "executionInfo": {"status": "ok", "timestamp": 1649336314761, "user_tz": -330, "elapsed": 780, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "a9ef20f9-426a-4ac2-aedc-2b54d4bb4aeb"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0, 1, 2, 3, 4, 5, 6, 7, 8, 9])"]}, "metadata": {}, "execution_count": 81}]}, {"cell_type": "markdown", "source": ["### Unique and Other Set Logic"], "metadata": {"id": "5w90fwsV2mnd"}}, {"cell_type": "code", "source": ["names = np.array(['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'])\n", "np.unique(names)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JwoUMUnw2eLd", "executionInfo": {"status": "ok", "timestamp": 1649336314762, "user_tz": -330, "elapsed": 29, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "2bf1a082-c2bc-4b3c-c015-830b1b9953a8"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array(['<PERSON>', '<PERSON>', '<PERSON>'], dtype='<U4')"]}, "metadata": {}, "execution_count": 82}]}, {"cell_type": "code", "source": ["ints = np.array([3, 3, 3, 2, 2, 1, 1, 4, 4])\n", "np.unique(ints)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EirkFaFZ2qL1", "executionInfo": {"status": "ok", "timestamp": 1649336314762, "user_tz": -330, "elapsed": 26, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "ad6737f1-71e4-413d-f23b-74931a7b3bf8"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([1, 2, 3, 4])"]}, "metadata": {}, "execution_count": 83}]}, {"cell_type": "code", "source": ["sorted(set(names))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kEAJewfr2qkN", "executionInfo": {"status": "ok", "timestamp": 1649336314762, "user_tz": -330, "elapsed": 23, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "f47e3cd2-e1dd-4dc9-e5a9-1b53ae967e73"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['<PERSON>', '<PERSON>', '<PERSON>']"]}, "metadata": {}, "execution_count": 84}]}, {"cell_type": "code", "source": ["values = np.array([6, 0, 0, 3, 2, 5, 6])\n", "np.in1d(values, [2, 3, 6])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zQTWk-AU2sgG", "executionInfo": {"status": "ok", "timestamp": 1649336314762, "user_tz": -330, "elapsed": 21, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "47485b28-b5f5-46fe-a7a1-c2f921c69768"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ True, False, False,  True,  True, False,  True])"]}, "metadata": {}, "execution_count": 85}]}, {"cell_type": "markdown", "source": ["## Broadcasting"], "metadata": {"id": "RTi2EMPf4lH_"}}, {"cell_type": "code", "source": ["arr = np.arange(5)\n", "arr * 4"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qx2VCaqU29cs", "executionInfo": {"status": "ok", "timestamp": 1649336314763, "user_tz": -330, "elapsed": 20, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "9a643134-23d4-4b6c-aa21-7177820962c0"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ 0,  4,  8, 12, 16])"]}, "metadata": {}, "execution_count": 86}]}, {"cell_type": "code", "source": ["arr = np.random.randn(4, 3)\n", "arr.mean()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "y9XxiaDm9on4", "executionInfo": {"status": "ok", "timestamp": 1649336314763, "user_tz": -330, "elapsed": 18, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "01fbf2d9-cafa-4f4f-eae0-53d7988e12fc"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["-0.11413009511180834"]}, "metadata": {}, "execution_count": 87}]}, {"cell_type": "code", "source": ["arr.mean(0) # mean across rows"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fhikgpM09vyI", "executionInfo": {"status": "ok", "timestamp": 1649336314763, "user_tz": -330, "elapsed": 16, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "68c7c8af-dab0-4e32-c770-9b5d7a9175a0"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([-0.39137175, -0.11530306,  0.16428453])"]}, "metadata": {}, "execution_count": 88}]}, {"cell_type": "code", "source": ["arr.mean(1) # mean across cols"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QX8OOofX9whw", "executionInfo": {"status": "ok", "timestamp": 1649336314763, "user_tz": -330, "elapsed": 14, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "fb83c3f7-ea95-4e82-b686-94be5ead8721"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ 1.30918741, -0.35813635, -0.41023143, -0.99734001])"]}, "metadata": {}, "execution_count": 89}]}, {"cell_type": "code", "source": ["demeaned = arr - arr.mean(0)\n", "demeaned"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8XZtyySW93_3", "executionInfo": {"status": "ok", "timestamp": 1649336314764, "user_tz": -330, "elapsed": 14, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "739dcb61-6ae9-4717-b841-2ee7186781b2"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 0.84001833,  0.91135985,  2.51857433],\n", "       [-0.28753497,  0.93938547, -1.38386927],\n", "       [-0.94200229,  0.23805992, -0.18436165],\n", "       [ 0.38951893, -2.08880525, -0.95034341]])"]}, "metadata": {}, "execution_count": 90}]}, {"cell_type": "code", "source": ["arr - arr.mean(1) # Why did this not work?"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 165}, "id": "XEtHflpB-NAu", "executionInfo": {"status": "error", "timestamp": 1649336314765, "user_tz": -330, "elapsed": 13, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "cfd7a911-576b-4dbc-db58-ec01154c7a48"}, "execution_count": null, "outputs": [{"output_type": "error", "ename": "ValueError", "evalue": "ignored", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-91-afc015660f33>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0marr\u001b[0m \u001b[0;34m-\u001b[0m \u001b[0marr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmean\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;31m# Why did this not work?\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mValueError\u001b[0m: operands could not be broadcast together with shapes (4,3) (4,) "]}]}, {"cell_type": "markdown", "source": ["Broadcasting rules"], "metadata": {"id": "eXh2ZprW_wEJ"}}, {"cell_type": "markdown", "source": ["Row wise:\n", "![image.png](data:image/png;base64,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)"], "metadata": {"id": "COe3_54P_tZD"}}, {"cell_type": "code", "source": ["y = np.array([1,2,3]);y"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-07sW_6F_nfK", "executionInfo": {"status": "ok", "timestamp": 1649336327587, "user_tz": -330, "elapsed": 1, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "16e5544f-bbea-4995-c31e-90362ee7afdd"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([1, 2, 3])"]}, "metadata": {}, "execution_count": 92}]}, {"cell_type": "code", "source": ["arr + y"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "X5z4Avaa_7sh", "executionInfo": {"status": "ok", "timestamp": 1649336328169, "user_tz": -330, "elapsed": 2, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "9b59cad6-5853-4d59-f65c-050ac7993dd7"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 1.44864657,  2.7960568 ,  5.68285885],\n", "       [ 0.32109328,  2.82408241,  1.78041526],\n", "       [-0.33337404,  2.12275686,  2.97992288],\n", "       [ 0.99814718, -0.20410831,  2.21394112]])"]}, "metadata": {}, "execution_count": 93}]}, {"cell_type": "markdown", "source": ["Column wise:\n", "![image.png](data:image/png;base64,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******************************+pZpT+5i5U6a0sie2MKcqoBp3+Uff/zRH7amh2muOaN6D/PxMZHRaKBwdIwq2bInmvaTn3OvhrVa8THpq55SLRxozpQaMlRu1NKrHoeY9EXWYhPQQsBfffVVVUg10ue3gS22Br2T7v333+/SCaFRhykvLCe7on3vLagxUYslEiBQVAIMyy9qzpRILtsvVnPUdt99d3fiiSdWF8FSpfWee+7xw59SGbKu4eg6FHbaaSe/0Nf48eP9b1VE5fTPnTvXs0ghaA0B6zXV/HpVejT/WUNqFfSphp3zzjsvBXXRocME5Mzeeuut1fUd1LurRjT14CvMmTOni4RXXHGFX2H9ggsuiHLbKtmMcCs4NZypsUzz7xXkCIfBery13aZsT4xB07hsKpf0VR7PmjXLq6L8D3XWdAWt6aLeVDUspramSYz5l5LMNhfbFtJTPUVzz1PeHk1TnjRawcI+++yT9Ha2moIwe/bsqr6bb76522+//VJ6jdElMQL03CeWobGro6HqZ511lq+IWdDqpOYIxq5fXn5VBLTYXLionKYh2Ar6+etT+K25iAceeGAXVV5//fXq4oIp6IgOnSMgZ9ccPUmh3notdqVpIDrM6TUJtTe1et5iHT0ip8KG40sn6S9n1xxgObdhkDOic/n4zuVY75+sRl/rtdendDF9NRLDHC2lLGdf52RX7Z7eP5E7IFCbgOxHWF5rezQ596muJaOGs3BXAPVia+HjVIP01fa9oY3Vdn9y8AkQKCoBnPui5kyJ5dJK+WEvvRZIWrRoUbJEVDjm56ppe6dUg/I2v9CQhhSHW+qkqjt6tYcATlx7OHfqKeRvp8jz3JCA3kNttRk2GGphuZS3R1NDhkbCWJCTO3r06GRfDE3/UR6bzdEIMO1sZCPBklUcxaImwLD8qLMvTeHl2IbDKuX8as/YVIMWp8k78xoym2rQ/LVwYRrpqTl74YKKqeqOXq0nYKsY13vSW2+91eX90/BKNShqyHaMQfpq27t6PYXqqQ8XJdV/TYdNDYpRZ8mvhUlrBdkXjVyw3nsNw1dvquwLFfJaxIj7twT0jmkdGXvXtLuPppylGvTfuu+++7qs46GReJpSmWp47bXXnNYYsKAtfHfbbbdU1UWvRAjg3CeSkbGpoUJCvbXaviicA/nFF1/4RfRC515z8mOdGxrmixbtUutvOBdPQ720d2ro7KoxI4X59hoqq0Pzma1SrUrQiy++6F599dUur6wKS21/SIBAXwloZMi9995bN5lJkyZ1+b9pPv5ee+0VbeOS/luykfV6s2VntY2TBdlcrXtRrzGgLrgCnZAO9eY0a6SXpmXYAomyK9qRRfri3BcoExMQReVYuLe9Ggi1mF6qQYsGqvy2YNMKU1kPKZ9vqqdqCoIF2Q8tzlrP9uTv5zcEOkUA575T5Ev+XC3qdOGFF/relxEjRvhtjLSI3OOPP17dm9oQHXPMMUk4fvfff7/Tdir777+/r2yqAeO5555zjz32WJeW8JEjR/qtAWMPWoTmpptu8osGKo+1jsKbb77pHnzwwS5rKKgx4/jjj49dXeQvEIFGI33yC6rJ+Wt0fYHUqitKI6c1f06/Y6+My1Gvp0NeX0Grd21doJyAQDcEVH5rBfVwfYc999wz6V5slenhgqTqsZfOqQbtuDJz5syqeoMHD/ar5GuEBgECRSbAG1rk3ElYNvUm2fBYVcZUWVMhGRaUihszZoxfPT+FMGPGDL8S/rRp06o9SGoZDoOG16onUcOEYw9aCFFz1dSAYZVrLW4V9jBqqOzFF1/sttpqq9jVRX4IQAACECgJAY22e/7557toe/LJJyfr+Kkx44EHHqh2RKh+JkdXdZYUg+opqq+FuwKoMUOjvAgQKDoBnPui51Ci8mkBGh0yoPl9mKWyenM1t+n2229fYbG5WJG88847XvR8I4biVFBuuOGG7vLLL3djx46NVcWq3MpTDQXWegIK+UYMxakV/LTTTnOXXnopw2Wjz3EUgAAEIFAeAurF1mhDC3L8NOou1aCpLvldAQ4++OCkGzPCUZWqo51wwglRT2dK9d1ErxUJ4NyvyISYNhDQCqvaPkVbUGmefbj4kZx6GVE5fvUWTWqDiE1/hIaey6GVk6+tnCxovqwc+nPPPTeZIW4qCLXQznvvvedHaGikhgXNV9O8xAkTJrhDDjkk2rnOTX9BSLAtBDRfe9iwYdVnhdtutkWANj9EI6NCHVMfUip9NSJINkhBDcUECDSbwPTp093QoUOryap8j3mRykZ81AmjxgxNYTLbqcaMlFfJ18hDTR81fbV2kObbEyAQAwGc+xhyKUEZVTD85z//8asaaws07WOvSqccXQ3z0urGteZOxozisssucxMnTvT6ams/ObxaKFC66kjJyVDeaa9frS8gfbWY4IIFC/wCiuuvv76vBKVaEYr5HS2D7FdeeaWbPHlyVdXUF0eSftqey0JqdjX/zsoBGT58eDXanPz8dfyGQF8I3HLLLV1GpKnhPtWGM/2HND1y3LhxVWRauySlOkv+XdDiiGrQsKCphaqnESAQAwGc+xhyKVEZVTDokLMX6zZUvckaFQ7a4k7Hpptu2ptbo7xWFQJz4rWvfcp7/0aZQSUVWo2Hqc4TrZWlcuZTd+hDvWV38osm1uJCHAT6QkA9uWUKWnQ09oVHe5NfGvGjuikBAjES6B+j0MgMAQhAAAIQgAAEIAABCEAAAhCAwP8I4NzzNkAAAhCAAAQgAAEIQAACEIAABCIngHMfeQYiPgQgAAEIQAACEIAABCAAAQhAAOeedwACEIAABCAAAQhAAAIQgAAEIBA5AZz7yDMQ8SEAAQhAAAIQgAAEIAABCEAAAjj3vAMQgAAEIAABCEAAAhCAAAQgAIHICeDcR56BiA8BCEAAAhCAAAQgAAEIQAACEMC55x2AAAQgAAEIQAACEIAABCAAAQhETgDnPvIMRHwIQAACEIAABCAAAQhAAAIQgADOPe8ABCAAAQhAAAIQgAAEIAABCEAgcgI495FnIOJDAAK9I9CvX7/e3cDVEIAABCAAAQhAAAIQiIAAzn0EmYSIEIBAawnI4cfpby1jUocABCAAAQhAoLkEqLs0l2cKqeHcp5CL6AABCPSKgBWGOPW9wsbFEIBAAwLYlQZwOAUBCPSJgNVX8p/5RHH280TK9xvnvnx53lBjjEJDPJxMgEBYMNZTh/9BPTLEQwACjQiEtsNsTaPrOdd3AsYcu913lqRQbAL1bEq9+GJrg3StIoBz3yqypAsBCBSSgBWC/fv390Px80chhY5cKCrdkWcg4veYgOyKHXnbEv4P+E/0GCkXQgACGQHZjFq2BTgQyBPAuc8TKfFvq2xQ6SjxS1AC1cPKNw5++zLcHJ32PZEnQaC9BMLKdy3bImkoX9ubJzwNAqkQyNddzMaEtiYVXdGjbwQG9O127k6FQC3Hfvny5e6NN95wv//+eypq1tTj7bffrsbPnj3bPfPMMzWvSyVy3rx51TxdtGhR8voq3+bPn1/NvgEDBriVV17ZrbTSStWjXkU8lTwvoh4///xzKd69zz//vIr/l19+cYsXLy5idjRNpt9++81VKhWfnsqO1PU1XaWvylHZFx1mX2rZFhz8pr1u1YTyjYcq51Ivy6X833//7Rnof/b888+7wYMHNx9uQVL8888/3YIFC7w0f/31l3vppZfcnDlzCiJda8T46KOPqgnLpuRtS96xz/8PWiMVqRadAM590XOojfLljYIqZtddd503JikHNWJYePDBB92jjz6asrq+MmANNu+++6478cQTk9ZXypm+esfl2OtoVAnXPfkKeP538tCarKD4WUVESX/66aelePf++OOPKkk5+oMGDWoy2WIl988//zgdCmo8/OGHH4olYAul0fsd2hazMXkH30TApvQtM6zOkq+7KNWnn37aTZ8+vW8PiOBuObwKs2bNcmefffYK5VYEKvRYRDWkLV261F8vu3rRRRf5MiXlsGzZsqp6cu67q7uYTcG2pPxWdK9b2l5b9/pzRQ0C66yzji8gZEjVC1OmoAIjrIynrrtav9WDWpYwZMgQ71ytssoq/jBHv1YvmzGhkOzb2xFWwMV/jTXW8Amqkals756cPf3nyhJCR78MOqvMHDhwoD/MvoS9bfWc/DKwaYeO4m6dEXJ6zfFtx7M7/QzZFY0MKkvQf23JkiVlUdePBFpttdWqtsXsi/Xky7bkGzqou5Tm9VhBUZz7FZCUKyL88+u7jMMZZ5zhK6By7NWrrUO/7VPfrdImA2tHucihbQwE7P22SvXWW2/tNtxwwy6FpLWEq/AMK9/Sj8Kxebksluutt5675ppr3JNPPlm1KaFdkW0x+yK7Yj3A4dDn5knU3pS23XZb/96pN5sQN4Fa5aY0Uq+iHMxVV13Vf3bXeIh9ad57IJajRo1yV111ldP0uny9xX6rUVF2Jay3pGBfmkeSlIpCQO+0HSo7d9llF29bdOSd+1r1l6LogRztJ9AvM2r/nRyXe7ZeqDqn2i8lT2wJActfq0Sr0DMn3nqw9akhzaq06NPiaxWULRGSRCHQBwJWMFrPvCrbKhRV8dbcRBWS6smX02Wt4tb7oxbxsDWcinjvMiK0L7ItOqyx0OyIbIrZF9kYxWsYog67J6yE904CroZAawnIJshGWO+87Is59qFd0XeLl/0Je9vMrmBfep5XoW3Rd7MVZjtkR2RPrN5i3xWf76TAvvScO1e2l4DVP2x6j9kXq6+ENsYcftkXq+9Y/UdSY1/am3edeFrot9Nz34kcKOgzraIigyJjIkOiQlPOjhWAVokJe9jyreAFVQ+xSkbACrZ8BdxavMMhbvV67ykQm/PSWF6ElRUxl22RjVEwJ0n5owq49bCl1HvfHJqkUgQCoX2xclHlpjUehs683nV61pqfa1aZDe1L2NAi22H2Rdcof8KOCevYkGTWYNB8KUkRAr0jYPUOG0kYvtOyL9Z7b6OD8o2F1Ft6xzvFq3HuU8zVHupkBaMuzxeOMiaqkFiBZxVvtYzLoMi5z1e+lQ4FZA/hc1nLCVgBp896LeBWEc/3plmh2nIhE35AaF+kpuWDVVTCRkHraZDNsYZDsy9hL13CuFAtMgJhBdzKRys3za6orLTGROt9M9sSVsDD75Fh6Ii4oW3R99DGWx5Yo6AENPY2Iiic+oN96UgW8tAGBMwehO922HtvdiW0L9Z4iH1pALZEp3DuS5TZ9VStZ0iEXmmbAAAPQUlEQVSst16fVniq8m29aqp8h9cofZz7epSJbzcBKxjDAtIcS31apds+zcEMHXv7b7Rb9pSeF+aDOUGqeFvjoWyGxVujYb7yTQU8pTcifl3yZaZ+m/2wSrjZFRsVpHhrZAzvj59G5zXI23jZC9kY42x2X3liQ/jDEUFWj+m8JkgAgf91tomF3uF846HZlO567qm/lPdtwrkvb96voHm+gNQFVhlRxUQGxQrGWr1qOPYrICWiwwTsnbZPa9223h2r9FmFXL+tMLWCkQKy75loTC0lazC0eOuxD+0LDYd9504KrSNgNkVPsPfYKuFmT/QZ2prQuce+9C1vxM/siOWBWIfBeKuxUI69NRqGo4Zw7PuWD9zdGgJmX+wzrIvn6y1mZ3RN3q7Y79ZISapFJYBzX9ScaZNctQpIGQgLZhis0mJz2MLCUdfi2Lcpw3hMrwmEhaQVkPY+q5AMDzvf64dwQ0MCYYVD38NKuH7LqVec7IodYaUb+9IQLyc7RCB8r61irfe4kX2RqGaTOiR2ko81pmH9xey58kQ2Ro2IOPZJZn+SSuXtS/g+m4NvtsbOYVuSfBV6rRTOfa+RpXuDjIIMhAo/KyD1aZVxKt7p5n3KmlkBKR3tfbaCsNanFY7hfSnzaaVuYhg65mZj7Jn2O3Tswx57nPpW5g5pN4OA2QmzG7VsSljxNhvUjGeXPQ2zL5YHxtbyQnUWfVfPZr7+InbYl7K/QcXXP7QvoW1RvDUk5t/78J7ia4iErSCAc98KqpGlaQWkiS1DETr4dj7f4m3XU0BGluElFNcKO6meLwjD3/quQOHYvJfE7EetPBBv2Y+8bTGbgm1pXj6QUusIhPZC33Xk7YrFh1KE/4nWSZd2yo3si+WD7IuC2Rkjgn1J+91IQbvQRtj7LL3M0Te7En7qPLYlhdz/9zqwz/2/Z5fcnWFBp+92SNHwNxXv5LK+FArVKyTrFYoUjs19LerZl1q2xmxOcyUgNQi0joDZC/s0515PzNsYi2udNOVLOayXUHcpX/6nrnFoX/L2pNbv1Hmg34oE9B6YHcS5X5FPqWPyFXDB6C6u1MBQPioC+Qq4/TYl8uejUi4CYbuzJWEFPQJ1EBECVQKhLallR2qdB19zCdSyH93ZnOZKQGoQaA2BntqUfJ2mNdKQahEJKO9x7ouYMwWSKSwQQ7HqxRdIdESBQI8I1CsE68X3KFEu6hGBWnakVlyPEuMiCBSQQC07UiuugKJHLVI9O1IvPmplEb6UBOrZkXrxpYRUQqVx7kuY6X1RmUKxL/S4t+gEKBA7m0PYl87y5+mtJYB9aS3f7lLHvnRHiPOxEsC2xJpzrZE7dO5ZUK81jJNKtZYBocBMKotLo0ytd7k0yhdU0Vp5gn0paGYhVkMCtd7lhjdwsuUE8nmCbWk5ch7QIgL5d7lFjyHZBAjg3CeQiZ1QASPTCeo8EwLlIIB9KUc+oyUE2k0A29Ju4jwPAhBoN4H/7vvU7qfyPAhAAAIQgAAEIAABCEAAAhCAAASaRgDnvmkoSQgCEIAABCAAAQhAAAIQgAAEINAZAjj3neHOUyEAAQhAAAIQgAAEIAABCEAAAk0jgHPfNJQkBAEIQAACEIAABCAAAQhAAAIQ6AwBnPvOcOepEIAABCAAAQhAAAIQgAAEIACBphHAuW8aShKCAAQgAAEIQAACEIAABCAAAQh0hgDOfWe481QIQAACEIAABCAAAQhAAAIQgEDTCODcNw0lCUEAAhCAAAQgUHQCS5YscT///LOrVCpFFxX5IAABCEAAAr0igHPfK1xcDAEIQAACEIBAzAReeeUVN3bsWDd16lS3fPnymFVBdghAAAIQgEAXAjj3vBAQgAAEIAABCJSGwD///OPeeOMNd8opp7iTTjrJzZgxAye/NLmPohCAAATSJoBzn3b+oh0EIAABCEAAAjUI/PLLL+7RRx9148aNc9dee637/vvva1xFFAQgAAEIQCAeAjj38eQVkkIAAhCAAAQg0GQCCxYscDfccIPbdddd3V133eV+/PHHJj+B5CAAAQhAAALtIYBz3x7OPAUCEIAABCAAgQIT+Prrr90FF1zgjj76aPfCCy+433//vcDSIhoEIAABCEBgRQI49ysyIQYCEIAABCAAgRISkEP/4osvuqOOOspdfPHFbtasWSWkgMoQgAAEIBArgX7ZVjA194Lp168f28TEmqvIDQEIQKAEBDSc+rPPPvNl1dChQ92wYcP897feesstXbrUExg9erQbMGCAW7RokZszZ47TYmobbLCB23rrrf359957z2+LpjBixAg3aNAgPyx79uzZ7q+//nJDhgxxw4cPdyuvvLL74osv3Jdffumv3XLLLd3GG2/s/vjjD/fRRx+53377za266qpup512cgMHDnTffPONmzt3rr/WZNPK7B9++KHTVmySScPAdY8cSsmssNZaa/nnqQz+9NNPfToK2223nVt33XX9895+++2Gsm211VZuo402cn/++ad7//33PQvJv8MOO7jVV1/d/fTTTz5eQWkqbYUPPvjA6y7Ztt12W7f22mt3kW2NNdZwO++8s7/2k08+cd9++63r37+/Z7neeut5mV5//XV/XmGfffbxeug66SL2m2yyidtiiy38+ZkzZ3puCqNGjfLcfvjhB/fxxx+7v//+u6ZsunaXXXbx+SJuYi/9Bg8e7PVbZZVVnHrg582b59PdfPPN3aabbupZ6Npff/3Vy3jZZZf5842CdFtnnXXcpEmT3BlnnOE222yzRpdzDgIQgAAEINARAl38djn3tUImWa1o4iAAAQhAAAKFIHDHHXdU1l9//UrmgFWuv/56L1PmYFb22GMPH6cjc1Z9/GOPPVbJnDwfd/7551flP+yww6rXZg69j8+GZFcyB9THH3TQQZWsYcDH33jjjdVr77zzTh+XOZGVzIn18bvvvnsla3Dw8XfffXf12mw+t49TOtkWbD4+axyofP755z4+c2ar12ZDwiuZ01rJGgIqkydPrsY/9dRT/lrdkznvPl5pfffddz4+lC2bN+7jJMvIkSP9tZmzXskaBXz8Sy+9VE33rLPO8nEKxx9/vI+X7tOnT/dxYqI4HYceeuj/X1mpZMPXfZyYZovS+fis0aB6rc5JB4UHHnigkjU2+HNTpkypprHffvtVr//qq698/NNPP13JnGgfX0s2xWcNIf5accsaBfy1++67b2XhwoU+/rbbbqumq+8K8+fPrxxwwAE+PmukUKdGj4+ssaOSNRJVHn744apOPlECBCAAAQhAoAAEQr99QEeaF3goBCAAAQhAoI8E1Iutnl718obzo9UzrXgF9RYrqOd28eLF/jr13lrQiul2rdJRUA+7rlU66tXPym0fr3vtWj1bQffY89TrbmmYbHafPiWL0lMa+m7X6tPSlTx6ng7JafHLli2rPk+y6ehONj1DPfFKY6WVVvI96wrSz9LVKAILxkLy2P7voWw2wkHXq8ddaay22mqerYJktnSriWZfjIV0sJ56na+VT7pGukmuWrLpPtNDspl+SsvyWj35tfLJ2Iey9eS7nqdRBo888ogbM2aMH6VAgAAEIAABCBSRAM59EXMFmSAAAQhAoFsC22+/vct6d71Tl/Wa++s1NE0LomW9uP63hnoraBj96aef7uQ87r333j5OIeu5rw7Rl3OuoKHjp556qnfmNaxbQ+cVNGz/7LPP9t9tKLuGqmt+djZawDt9Gh5u5+1a3acgR/iII47ww+41/F9DyxX0XLtWOmlYvIaES05zZDXlQEH3aH92Ocka3t5INj3j2GOPdVnvvr9PQ/AVNJ3AnmfcFJ+NUvDnJKcYKISyabi/BQ25V9AweLG175aufksHhW222cadeeaZXpc999zTxykceeSR1XzTdAEFDX0/7bTTfINAKFs2SsHLpmDOtWSTfsprDb2X3AqaGmFy7Ljjjj5O+aIt7zQVIhsl4KZNm+bjuwvKC+XthRde6CSD8psAAQhAAAIQKCoB5twXNWeQCwIQgAAEIACBphPIhv67ww8/vNt0syH87rzzzvNz7umt7xYXF0AAAhCAQIcIhHPu6bnvUCbwWAhAAAIQgAAEikdACwmecMIJ7uqrr/YLExIgAAEIQAACsRDAuY8lp5ATAhCAAAQgAIGWEdCw/mzRPXfJJZf4lf5tSkfLHkjCEIAABCAAgSYTwLlvMlCSgwAEIAABCEAgLgLaRk9D8LU2gNYRIEAAAhCAAARiJIBzH2OuITMEIAABCEAAAn0ioDmKQ4cOdePHj3fnnHOOn1dviwD2KWFuhgAEIAABCHSIAM59h8DzWAhAAAIQgAAEOkNAQ/CPO+44N2HCBDdy5Ei/VSABAhCAAAQgEDsBVsuPPQeRHwIQgAAEIACBHhOYO3eumzdvnjvwwANx6ntMjQshAAEIQKCoBMLV8nHui5pLyAUBCEAAAhCAAAQgAAEIQAACEGhAIHTu+ze4jlMQgAAEIAABCEAAAhCAAAQgAAEIREAA5z6CTEJECEAAAhCAAAQgAAEIQAACEIBAIwI4943ocA4CEIAABCAAAQhAAAIQgAAEIBABAZz7CDIJESEAAQhAAAIQgAAEIAABCEAAAo0I4Nw3osM5CEAAAhCAAAQgAAEIQAACEIBABARw7iPIJESEAAQgAAEIQAACEIAABCAAAQg0IoBz34gO5yAAAQhAAAIQgAAEIAABCEAAAhEQwLmPIJMQEQIQgAAEIAABCEAAAhCAAAQg0IgAzn0jOpyDAAQgAAEIQAACEIAABCAAAQhEQADnPoJMQkQIQAACEIAABCAAAQhAAAIQgEAjAjj3jehwDgIQgAAEIAABCEAAAhCAAAQgEAEBnPsIMgkRIQABCEAAAhCAAAQgAAEIQAACjQjg3DeiwzkIQAACEIAABCAAAQhAAAIQgEAEBHDuI8gkRIQABCAAAQhAAAIQgAAEIAABCDQiMKDeyYEDB7p+/frVO008BCAAAQhAAAIQgAAEIAABCEAAAh0iMGTIkC5PruvcL1y40K255podEpPHQgACEIAABCAAAQhAAAIQgAAEINCIwNSpU6un+1Wy0OhizkEAAhCAAAQgAAEIQAACEIAABCBQbALMuS92/iAdBCAAAQhAAAIQgAAEIAABCECgWwI4990i4gIIQAACEIAABCAAAQhAAAIQgECxCeDcFzt/kA4CEIAABCAAAQhAAAIQgAAEINAtgf8DbdHZ9rBdbyYAAAAASUVORK5CYII=)"], "metadata": {"id": "jRdeYjqOBFF2"}}, {"cell_type": "code", "source": ["y1 = np.arange(1,5).reshape(4,1)\n", "y1"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_-cQ20Ir_9Zh", "executionInfo": {"status": "ok", "timestamp": 1649336329352, "user_tz": -330, "elapsed": 2, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "ceee14c6-d8c0-484a-cefe-3847cedc1390"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[1],\n", "       [2],\n", "       [3],\n", "       [4]])"]}, "metadata": {}, "execution_count": 94}]}, {"cell_type": "code", "source": ["arr + y1"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YJFcQ2qdAS_A", "executionInfo": {"status": "ok", "timestamp": 1649336329963, "user_tz": -330, "elapsed": 1, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "3dc0616c-c257-4bab-93e1-14b0e9c18e2a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[1.44864657, 1.7960568 , 3.68285885],\n", "       [1.32109328, 2.82408241, 0.78041526],\n", "       [1.66662596, 3.12275686, 2.97992288],\n", "       [3.99814718, 1.79589169, 3.21394112]])"]}, "metadata": {}, "execution_count": 95}]}, {"cell_type": "code", "source": ["arr + y1.T # incompatible shapes cannot be broadcast"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 165}, "id": "jNNxnD6OAh7v", "executionInfo": {"status": "error", "timestamp": 1649336335857, "user_tz": -330, "elapsed": 502, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "05764608292492166571"}}, "outputId": "ed387714-f024-4736-8b68-1f1ae99c93c2"}, "execution_count": null, "outputs": [{"output_type": "error", "ename": "ValueError", "evalue": "ignored", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-96-aa51c290f184>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0marr\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0my1\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mT\u001b[0m \u001b[0;31m# incompatible shapes cannot be broadcast\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mValueError\u001b[0m: operands could not be broadcast together with shapes (4,3) (1,4) "]}]}, {"cell_type": "code", "source": [""], "metadata": {"id": "Jz7GZqUzBqQc"}, "execution_count": null, "outputs": []}]}