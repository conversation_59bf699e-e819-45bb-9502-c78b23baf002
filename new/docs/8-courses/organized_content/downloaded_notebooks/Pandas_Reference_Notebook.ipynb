{"cells": [{"cell_type": "markdown", "metadata": {"id": "nlvEhZD72QhP"}, "source": ["![image.png](data:image/png;base64,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)"]}, {"cell_type": "markdown", "metadata": {"id": "fTNUwT3gtNdl"}, "source": ["# Comprehensive Guide to Pandas  \n", "\n", "Let's explore how to use one of the most important and widely used tool among the data scientists - **Pandas**.\n", "\n", "The `Pandas` library makes creating, viewing and manipulating dataframes a lot easier. \n", "\n", "In this notebook, let's see how to do the following in detail, \n", "\n", "* Getting Data: How to get/read different kinds of datafiles. \n", "* Creating Data: How to create pandas dataframes using pandas objects.\n", "* Knowing your Data: How to check some basic info about the data.\n", "* Manipulating Data: How to do basic operations on your dataframe and extract information. \n", "* Cleaning the Data: How to handle raw data and perform required preprocessing for further use \n", "\n", "Now let's get started...\n"]}, {"cell_type": "markdown", "metadata": {"id": "BUkHDM1c6JSW"}, "source": ["## Installation"]}, {"cell_type": "markdown", "metadata": {"id": "-q4bhtIc4-nf"}, "source": ["Before we continue, we need to import the pandas library. This can be done by following:\n", "\n", "`import pandas as pd`\n", "\n", "By default, the library will be installed in Google Colab.\n", "If you want to install the pandas library locally, install it using the following: \n", "\n", "`pip install pandas` (installing in terminal/command prompt) or \n", "\n", "`!pip install pandas` (installing in jupyter notebook)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "2eMiRHLy4ALj", "executionInfo": {"status": "ok", "timestamp": 1650368501206, "user_tz": -330, "elapsed": 592, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [], "source": ["# Import pandas \n", "import pandas as pd"]}, {"cell_type": "markdown", "metadata": {"id": "S1bog53b3SGf"}, "source": ["## Getting the Data\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "cftAwaGhB8Y2"}, "source": ["In this section, let's see different ways to read the data. We will use the `read_csv` method that has the following properties, \n", "* Read different formats of data i.e. CSV, TSV, Text files etc easily using a single line of code.\n", "* Can load files both locally and from an URL \n", "* Convert the data into pandas dataframes that can be easily manipulated\n", "\n", "Now let's see it in action,"]}, {"cell_type": "markdown", "metadata": {"id": "rvz8K0gi7Gyl"}, "source": ["### Reading files from local computer\n"]}, {"cell_type": "markdown", "metadata": {"id": "uVrhAVYVBzPR"}, "source": ["\n", "For the illustration purpose, download the Housing Prices dataset from kaggle into local computer. \n", "\n", "Link: https://www.kaggle.com/competitions/house-prices-advanced-regression-techniques/data?select=test.csv\n", "\n", "Then upload the CSV files to colab."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "GqHLNOiy7mTq"}, "outputs": [], "source": ["from google.colab import files\n", "uploaded = files.upload()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yPL-FxXh8XDS", "outputId": "20ce307b-598f-4abe-b204-7f7142704c75"}, "outputs": [{"data": {"text/plain": ["['.DS_Store', 'Pandas_Reference_Notebook.ipynb', '.ipynb_checkpoints']"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check the contents by using the os module\n", "import os\n", "os.listdir()"]}, {"cell_type": "markdown", "metadata": {"id": "z2prTKiV8fSU"}, "source": ["Great we have the data. Now let's read it using pandas. `CSV` or `Comma separated values` are files that have the values that are separated by commas. You can just open the csv file and inspect it. \n", "\n", "The `read_csv()` method  takes in the path of the file as argument and loads them and converts it into the `DataFrame` format."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 299}, "id": "Y3g5Ws4I8eUB", "outputId": "757edb8b-3f7b-4c2d-ff55-73bafa039ca5"}, "outputs": [{"data": {"text/html": ["\n", "  <div id=\"df-f227a09f-3871-485b-8811-f05ca98e7be8\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Id</th>\n", "      <th>MSSubClass</th>\n", "      <th>MSZoning</th>\n", "      <th>LotFrontage</th>\n", "      <th>LotArea</th>\n", "      <th>Street</th>\n", "      <th>Alley</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>LandContour</th>\n", "      <th>Utilities</th>\n", "      <th>...</th>\n", "      <th>PoolArea</th>\n", "      <th>PoolQC</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>MiscFeature</th>\n", "      <th>MiscVal</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>YrSold</th>\n", "      <th>SaleType</th>\n", "      <th>SaleCondition</th>\n", "      <th>SalePrice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>65.0</td>\n", "      <td>8450</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>208500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>20</td>\n", "      <td>RL</td>\n", "      <td>80.0</td>\n", "      <td>9600</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>2007</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>181500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>68.0</td>\n", "      <td>11250</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>223500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>70</td>\n", "      <td>RL</td>\n", "      <td>60.0</td>\n", "      <td>9550</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2006</td>\n", "      <td>WD</td>\n", "      <td>Abnorml</td>\n", "      <td>140000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>84.0</td>\n", "      <td>14260</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>250000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 81 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-f227a09f-3871-485b-8811-f05ca98e7be8')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-f227a09f-3871-485b-8811-f05ca98e7be8 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-f227a09f-3871-485b-8811-f05ca98e7be8');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "], "text/plain": ["   Id  MSSubClass MSZoning  LotFrontage  LotArea Street Alley LotShape  \\\n", "0   1          60       RL         65.0     8450   Pave   NaN      Reg   \n", "1   2          20       RL         80.0     9600   Pave   NaN      Reg   \n", "2   3          60       RL         68.0    11250   Pave   NaN      IR1   \n", "3   4          70       RL         60.0     9550   Pave   NaN      IR1   \n", "4   5          60       RL         84.0    14260   Pave   NaN      IR1   \n", "\n", "  LandContour Utilities  ... PoolArea PoolQC Fence MiscFeature MiscVal MoSold  \\\n", "0         Lvl    AllPub  ...        0    NaN   NaN         NaN       0      2   \n", "1         Lvl    AllPub  ...        0    NaN   NaN         NaN       0      5   \n", "2         Lvl    AllPub  ...        0    NaN   NaN         NaN       0      9   \n", "3         Lvl    AllPub  ...        0    NaN   NaN         NaN       0      2   \n", "4         Lvl    AllPub  ...        0    NaN   NaN         NaN       0     12   \n", "\n", "  YrSold  SaleType  SaleCondition  SalePrice  \n", "0   2008        WD         Normal     208500  \n", "1   2007        WD         Normal     181500  \n", "2   2008        WD         Normal     223500  \n", "3   2006        WD        Abnorml     140000  \n", "4   2008        WD         Normal     250000  \n", "\n", "[5 rows x 81 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["train_df = pd.read_csv(\"train.csv\")\n", "train_df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "RIxP-i1l8u3m"}, "source": ["### Reading files from URL \n"]}, {"cell_type": "markdown", "metadata": {"id": "0PxFDVaGB206"}, "source": ["\n", "Now let's load the dataframe directly from an URL that contains the CSV/TSV file. Also observe how we can load TSV or any kind of file by using the `read_csv()` method. By defining the `sep` or separator we can load the data from any formats. \n", "\n", "In the below example, we are download a `TSV` or `Tab separated values` file by specifying `sep=\"\\t\"`."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "YxA9CnsD8qV4", "outputId": "b50c01e5-af55-4692-fe2a-820c85ee1518", "executionInfo": {"status": "ok", "timestamp": 1650368516541, "user_tz": -330, "elapsed": 602, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   order_id  quantity                              item_name  \\\n", "0         1         1           Chips and Fresh Tomato Salsa   \n", "1         1         1                                   Izze   \n", "2         1         1                       Nantucket Nectar   \n", "3         1         1  Chips and Tomatillo-Green Chili Salsa   \n", "4         2         2                           Chicken Bowl   \n", "\n", "                                  choice_description item_price  \n", "0                                                NaN     $2.39   \n", "1                                       [<PERSON><PERSON>]     $3.39   \n", "2                                            [Apple]     $3.39   \n", "3                                                NaN     $2.39   \n", "4  [Tomatillo-<PERSON> (Hot), [Black Beans...    $16.98   "], "text/html": ["\n", "  <div id=\"df-413430a6-1d4e-487c-9225-70905144785c\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>quantity</th>\n", "      <th>item_name</th>\n", "      <th>choice_description</th>\n", "      <th>item_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON>s and Fresh <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Izze</td>\n", "      <td>[<PERSON><PERSON>]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Nantucket Nectar</td>\n", "      <td>[Apple]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Chips and Tomatillo-Green <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>Chicken Bowl</td>\n", "      <td>[Tomatillo<PERSON><PERSON> (Hot), [Black Beans...</td>\n", "      <td>$16.98</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-413430a6-1d4e-487c-9225-70905144785c')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-413430a6-1d4e-487c-9225-70905144785c button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-413430a6-1d4e-487c-9225-70905144785c');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 3}], "source": ["url = \"https://raw.githubusercontent.com/justmarkham/DAT8/master/data/chipotle.tsv\"\n", "chipotle_df = pd.read_csv(url, sep=\"\\t\")\n", "\n", "chipotle_df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "EDxNmjWH_vcl"}, "source": ["Great! Now since we have loaded the data, let's get some information about the data."]}, {"cell_type": "markdown", "metadata": {"id": "nCOsyKIk8tew"}, "source": ["## Introduction to Pandas Objects "]}, {"cell_type": "markdown", "metadata": {"id": "LylXoeYT8tew"}, "source": ["We have seen how to read already existing data from CSV files. But you might wonder what if we want to create our dataframes from numpy arrays or python list? Don't need to worry. We have some of the fundamental data structures of pandas library for our rescue. Those are `Series`, `DataFrame` and `Index`. Let's look each of these in detail. "]}, {"cell_type": "markdown", "metadata": {"id": "YLn29T6i8tex"}, "source": ["### The Pandas Series Object"]}, {"cell_type": "markdown", "metadata": {"id": "-Eh9VKHw8tey"}, "source": ["A Pandas `Series` is a one-dimensional array of indexed data. It can be created from a list or array as follows:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UvmPZV978tez", "outputId": "69c97884-9ad6-4731-b4af-7abbbd94388e", "executionInfo": {"status": "ok", "timestamp": 1650368521362, "user_tz": -330, "elapsed": 601, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    0.25\n", "1    0.50\n", "2    0.75\n", "3    1.00\n", "dtype: float64"]}, "metadata": {}, "execution_count": 4}], "source": ["data = pd.Series([0.25, 0.5, 0.75, 1.0])\n", "data"]}, {"cell_type": "markdown", "metadata": {"id": "YMv092q38tez"}, "source": ["As we can see, the series object wraps the sequence of values and a sequence of indeces. We can access these by using the `values` and `index` attributes respectively. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ObscWsBE8tez", "outputId": "e55c2971-4f31-49c9-f3b3-a3881ca48731", "executionInfo": {"status": "ok", "timestamp": 1650368522947, "user_tz": -330, "elapsed": 3, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0.25, 0.5 , 0.75, 1.  ])"]}, "metadata": {}, "execution_count": 5}], "source": ["# Access the values from data\n", "data.values"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "umagS6h_8te0", "outputId": "61966266-8b40-4085-cee8-4358ddec636e", "executionInfo": {"status": "ok", "timestamp": 1650368524763, "user_tz": -330, "elapsed": 3, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["RangeIndex(start=0, stop=4, step=1)"]}, "metadata": {}, "execution_count": 6}], "source": ["# Access the index\n", "data.index"]}, {"cell_type": "markdown", "metadata": {"id": "cuAswJ-A8te0"}, "source": ["From the above, we can conclude that the values are just numpy arrays whereas the index column consists of array-like object of type `pd.index`. Pandas will add the index as range of numbers starting from 0 to length of series object by default. \n", "\n", "There are some other ways through which we can create pandas series objects. Some of them are as follows: "]}, {"cell_type": "markdown", "metadata": {"id": "Q0_rvQHf8te0"}, "source": ["#### I: Series as generalised NumPy array "]}, {"cell_type": "markdown", "metadata": {"id": "h9Exfbpa8te0"}, "source": ["We can create `Series` object by specifying the values and index as numpy arrays. "]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kRRAmJa38te0", "outputId": "b4a34fde-9bd2-4a35-bc87-32b77a543307", "executionInfo": {"status": "ok", "timestamp": 1650368527041, "user_tz": -330, "elapsed": 605, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["a    0.25\n", "b    0.50\n", "c    0.75\n", "d    1.00\n", "dtype: float64"]}, "metadata": {}, "execution_count": 7}], "source": ["data1 = pd.Series([0.25, 0.5, 0.75, 1.0],\n", "                 index=['a', 'b', 'c', 'd'])\n", "data1"]}, {"cell_type": "markdown", "metadata": {"id": "x4YoKXbW8te1"}, "source": ["We can even use non-contiguous or non-sequential indices (Index sequence length should be equal to values array):"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fC4pl91i8te1", "outputId": "ee8f197f-91c7-4f2e-95e9-578cf4527ea4", "executionInfo": {"status": "ok", "timestamp": 1650368528570, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2    0.25\n", "5    0.50\n", "3    0.75\n", "7    1.00\n", "dtype: float64"]}, "metadata": {}, "execution_count": 8}], "source": ["data2 = pd.Series([0.25, 0.5, 0.75, 1.0],\n", "                 index=[2, 5, 3, 7])\n", "data2"]}, {"cell_type": "markdown", "metadata": {"id": "n2wzshJC9-cL"}, "source": ["The Index column is important because it is primarily used for accessing items. We will discuss more techniques of indexing in the next section."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "p_379SAm-OiW", "outputId": "666cf457-b12c-489d-96e9-a6378f34e007", "executionInfo": {"status": "ok", "timestamp": 1650368530656, "user_tz": -330, "elapsed": 381, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["1.0"]}, "metadata": {}, "execution_count": 9}], "source": ["# Access using string key from data1\n", "data1['d']"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zss-b-jX-ZGx", "outputId": "b564bb37-e04f-4ca5-9613-701742026996", "executionInfo": {"status": "ok", "timestamp": 1650368531236, "user_tz": -330, "elapsed": 2, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0.75"]}, "metadata": {}, "execution_count": 10}], "source": ["# Access using the int key from data2\n", "data2[3]"]}, {"cell_type": "markdown", "metadata": {"id": "xJCdsIQC-f7h"}, "source": ["Great! Now let's check another form of creating and accessing `Series` data. "]}, {"cell_type": "markdown", "metadata": {"id": "peNHwKkD8te1"}, "source": ["#### II: Series as specialized dictionary"]}, {"cell_type": "markdown", "metadata": {"id": "UpqeeNFR8te1"}, "source": ["The Series-as-dictionary analogy can be made even more clear by constructing a Series object directly from a Python dictionary:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FsdZUFOH8te1", "outputId": "66a513ef-f1e9-4bab-ae1c-91880b303f9e", "executionInfo": {"status": "ok", "timestamp": 1650368534425, "user_tz": -330, "elapsed": 432, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["California    38332521\n", "Texas         26448193\n", "New York      19651127\n", "Florida       19552860\n", "Illinois      12882135\n", "dtype: int64"]}, "metadata": {}, "execution_count": 11}], "source": ["population_dict = {'California': 38332521,\n", "                   'Texas': 26448193,\n", "                   'New York': 19651127,\n", "                   'Florida': 19552860,\n", "                   'Illinois': 12882135}\n", "population = pd.Series(population_dict)\n", "population"]}, {"cell_type": "markdown", "metadata": {"id": "nc4gdEdF--CT"}, "source": ["When we create pandas `series` object directly from python dictionary, the keys and values of the dictionary will be converted to the `index` and `values` of the Series object. Let's just check it out...\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "r86mnnVb8te2", "outputId": "d89541e1-6c55-447a-bea9-db3a099bfcea", "executionInfo": {"status": "ok", "timestamp": 1650368536127, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([38332521, 26448193, 19651127, 19552860, 12882135])"]}, "metadata": {}, "execution_count": 12}], "source": ["# Check the values \n", "population.values"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8uJnBxAi-7in", "outputId": "e5ac721a-c4e5-4ed4-ed75-dbbace9e5275", "executionInfo": {"status": "ok", "timestamp": 1650368537440, "user_tz": -330, "elapsed": 2, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Index(['California', 'Texas', 'New York', 'Florida', 'Illinois'], dtype='object')"]}, "metadata": {}, "execution_count": 13}], "source": ["# Check the index \n", "population.index"]}, {"cell_type": "markdown", "metadata": {"id": "m93htVU8BCKC"}, "source": ["Now let's dive into the Pandas DataFrame object."]}, {"cell_type": "markdown", "metadata": {"id": "S6hzYgGCBQ5Y"}, "source": ["### The Pandas DataFrame Object"]}, {"cell_type": "markdown", "metadata": {"id": "xw7_P42DBeon"}, "source": ["Like the `Series` object, the Pandas `DataFrame` object is a 2-dimensional array with flexibile row indeces and columns names. The `DataFrame` object can also be thought as multiple `Series` objects sequentially aligned by sharing the same indeces. "]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "Z-vLhTNR-9Bo", "colab": {"base_uri": "https://localhost:8080/", "height": 206}, "executionInfo": {"status": "ok", "timestamp": 1650368539026, "user_tz": -330, "elapsed": 7, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "520fce43-aa25-482c-b3b3-e409b4cfaad2"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["            population\n", "California    38332521\n", "Texas         26448193\n", "New York      19651127\n", "Florida       19552860\n", "Illinois      12882135"], "text/html": ["\n", "  <div id=\"df-4df0ce04-927b-443e-9d86-208083a126ea\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>population</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>California</th>\n", "      <td>38332521</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Texas</th>\n", "      <td>26448193</td>\n", "    </tr>\n", "    <tr>\n", "      <th>New York</th>\n", "      <td>19651127</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Florida</th>\n", "      <td>19552860</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Illinois</th>\n", "      <td>12882135</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-4df0ce04-927b-443e-9d86-208083a126ea')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-4df0ce04-927b-443e-9d86-208083a126ea button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-4df0ce04-927b-443e-9d86-208083a126ea');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 14}], "source": ["# Create the population_df dataframe with population key\n", "population_df = pd.DataFrame({'population': population})\n", "population_df"]}, {"cell_type": "markdown", "metadata": {"id": "wb0XmIvNZg0r"}, "source": ["We can create a pandas `DataFrame` using the `pd.DataFrame()` method. This method takes in a dictionary of series objects as argument. Let's add another column to the population dataframe."]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "vY2rS4GXZg0s", "executionInfo": {"status": "ok", "timestamp": 1650368703616, "user_tz": -330, "elapsed": 401, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "4cba6f56-1463-47e3-9b1a-7dec695ed2d6"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["            population   area\n", "California    38332521  52312\n", "Texas         26448193  32123\n", "New York      19651127  83432\n", "Florida       19552860   8291\n", "Illinois      12882135   2313"], "text/html": ["\n", "  <div id=\"df-22a73c28-179c-4adf-864e-dab8c2b102b2\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>population</th>\n", "      <th>area</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>California</th>\n", "      <td>38332521</td>\n", "      <td>52312</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Texas</th>\n", "      <td>26448193</td>\n", "      <td>32123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>New York</th>\n", "      <td>19651127</td>\n", "      <td>83432</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Florida</th>\n", "      <td>19552860</td>\n", "      <td>8291</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Illinois</th>\n", "      <td>12882135</td>\n", "      <td>2313</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-22a73c28-179c-4adf-864e-dab8c2b102b2')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-22a73c28-179c-4adf-864e-dab8c2b102b2 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-22a73c28-179c-4adf-864e-dab8c2b102b2');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 24}], "source": ["# Add area column to the population dataframe\n", "area = [52312, 32123, 83432, 8291, 2313]\n", "population_df = pd.DataFrame({'population': population, 'area': area})\n", "population_df"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "V2hzIvnxZg0s", "executionInfo": {"status": "ok", "timestamp": 1650368706346, "user_tz": -330, "elapsed": 408, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "ce3e01c4-84a9-4720-f3ef-c24f93acdf88"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["            population   area\n", "California    38332521  52312\n", "Texas         26448193  32123\n", "New York      19651127  83432\n", "Florida       19552860   8291\n", "Illinois      12882135   2313"], "text/html": ["\n", "  <div id=\"df-24cddd40-1518-47fa-a3fb-11e91ef8f31b\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>population</th>\n", "      <th>area</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>California</th>\n", "      <td>38332521</td>\n", "      <td>52312</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Texas</th>\n", "      <td>26448193</td>\n", "      <td>32123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>New York</th>\n", "      <td>19651127</td>\n", "      <td>83432</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Florida</th>\n", "      <td>19552860</td>\n", "      <td>8291</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Illinois</th>\n", "      <td>12882135</td>\n", "      <td>2313</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-24cddd40-1518-47fa-a3fb-11e91ef8f31b')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-24cddd40-1518-47fa-a3fb-11e91ef8f31b button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-24cddd40-1518-47fa-a3fb-11e91ef8f31b');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 25}], "source": ["# We can do the above using the indeces \n", "area = {'California': 52312, 'Texas': 32123, 'New York': 83432, 'Florida': 8291, 'Illinois': 2313}\n", "population_df = pd.DataFrame({'population': population, 'area': area})\n", "population_df"]}, {"cell_type": "markdown", "metadata": {"id": "4iHwLTkjZg0s"}, "source": ["Great! We got the same results. Now let's check the values and indece of the dataframe. "]}, {"cell_type": "code", "execution_count": 26, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mgm1CXB4Zg0s", "executionInfo": {"status": "ok", "timestamp": 1650368711291, "user_tz": -330, "elapsed": 610, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "ed36d535-2f87-4235-c21f-42ca487d59cb"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[38332521,    52312],\n", "       [26448193,    32123],\n", "       [19651127,    83432],\n", "       [19552860,     8291],\n", "       [12882135,     2313]])"]}, "metadata": {}, "execution_count": 26}], "source": ["# Check the values of population_df\n", "population_df.values"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "X7mzPIxkZg0s", "executionInfo": {"status": "ok", "timestamp": 1650368713557, "user_tz": -330, "elapsed": 3, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "c8a4de5d-8227-4b57-b008-bae03b62710a"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Index(['California', 'Texas', 'New York', 'Florida', 'Illinois'], dtype='object')"]}, "metadata": {}, "execution_count": 27}], "source": ["# Check the indeces of population_df\n", "population_df.index"]}, {"cell_type": "markdown", "metadata": {"id": "vaGgQeQWZg0u"}, "source": ["It is same as the `series` object. In DataFrame, the index column is the row index and the values are the column values. Hence let's check the columns that the dataframe contains."]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fhlNx1PTZg0u", "executionInfo": {"status": "ok", "timestamp": 1650368715007, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "1b04b3d6-7046-4217-a8cc-423604aae003"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Index(['population', 'area'], dtype='object')"]}, "metadata": {}, "execution_count": 28}], "source": ["# Check the columns of population_df\n", "population_df.columns"]}, {"cell_type": "markdown", "source": ["We can access separate columns from dataframe in the same way:"], "metadata": {"id": "Mvh7i80OodtP"}}, {"cell_type": "code", "source": ["# Access the population column\n", "population_df.population"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CCBjKGw_ojWY", "executionInfo": {"status": "ok", "timestamp": 1650368717676, "user_tz": -330, "elapsed": 594, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "d8aaab7b-fb84-451d-a63f-bb69a18a2343"}, "execution_count": 29, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["California    38332521\n", "Texas         26448193\n", "New York      19651127\n", "Florida       19552860\n", "Illinois      12882135\n", "Name: population, dtype: int64"]}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "code", "source": ["# Access the area column\n", "population_df.area"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "57g15iR7op0u", "executionInfo": {"status": "ok", "timestamp": 1650368719498, "user_tz": -330, "elapsed": 3, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "9febdb91-7399-40db-d0db-17e1f6aedf63"}, "execution_count": 30, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["California    52312\n", "Texas         32123\n", "New York      83432\n", "Florida        8291\n", "Illinois       2313\n", "Name: area, dtype: int64"]}, "metadata": {}, "execution_count": 30}]}, {"cell_type": "markdown", "source": ["Note: We can flip the values and index of the `DataFrame` object by using the transpose method."], "metadata": {"id": "ZIpVafQaaSDy"}}, {"cell_type": "code", "source": ["population_df.T"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 112}, "id": "mDUwbZpnaSaR", "executionInfo": {"status": "ok", "timestamp": 1650368721129, "user_tz": -330, "elapsed": 7, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "f62fd0a6-4093-4c8c-d17b-d5c6cbcf5ed4"}, "execution_count": 31, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["            California     Texas  New York   Florida  Illinois\n", "population    38332521  26448193  19651127  19552860  12882135\n", "area             52312     32123     83432      8291      2313"], "text/html": ["\n", "  <div id=\"df-33239f89-fb29-4b72-8fb1-a149717ecd4e\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>California</th>\n", "      <th>Texas</th>\n", "      <th>New York</th>\n", "      <th>Florida</th>\n", "      <th>Illinois</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>population</th>\n", "      <td>38332521</td>\n", "      <td>26448193</td>\n", "      <td>19651127</td>\n", "      <td>19552860</td>\n", "      <td>12882135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>area</th>\n", "      <td>52312</td>\n", "      <td>32123</td>\n", "      <td>83432</td>\n", "      <td>8291</td>\n", "      <td>2313</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-33239f89-fb29-4b72-8fb1-a149717ecd4e')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-33239f89-fb29-4b72-8fb1-a149717ecd4e button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-33239f89-fb29-4b72-8fb1-a149717ecd4e');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 31}]}, {"cell_type": "markdown", "metadata": {"id": "SP5HmD-jZg0u"}, "source": ["Let's now create another dataframe from scratch and see how it looks like. "]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "PlU_2-ZbZg0u", "executionInfo": {"status": "ok", "timestamp": 1650368723810, "user_tz": -330, "elapsed": 597, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "d66f5bd9-2f3f-4183-9b8e-093fe5faf004"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      a   b   c\n", "0     0  24  94\n", "1    29   3  75\n", "2    82  72  43\n", "3    71  91  63\n", "4    78   9  25\n", "..   ..  ..  ..\n", "995  21  22   0\n", "996  48  99  67\n", "997  46  11  29\n", "998  78  84  82\n", "999  32  14  78\n", "\n", "[1000 rows x 3 columns]"], "text/html": ["\n", "  <div id=\"df-d7060dbe-0516-4f04-98e4-efc2277a0b18\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>24</td>\n", "      <td>94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>29</td>\n", "      <td>3</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>82</td>\n", "      <td>72</td>\n", "      <td>43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>71</td>\n", "      <td>91</td>\n", "      <td>63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>78</td>\n", "      <td>9</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>995</th>\n", "      <td>21</td>\n", "      <td>22</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>996</th>\n", "      <td>48</td>\n", "      <td>99</td>\n", "      <td>67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>997</th>\n", "      <td>46</td>\n", "      <td>11</td>\n", "      <td>29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>998</th>\n", "      <td>78</td>\n", "      <td>84</td>\n", "      <td>82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>999</th>\n", "      <td>32</td>\n", "      <td>14</td>\n", "      <td>78</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1000 rows × 3 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-d7060dbe-0516-4f04-98e4-efc2277a0b18')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-d7060dbe-0516-4f04-98e4-efc2277a0b18 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-d7060dbe-0516-4f04-98e4-efc2277a0b18');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 32}], "source": ["# Create a new dataframe with columns 'a', 'b', 'c'.\n", "# The values will be random integers between 0 and 100\n", "import numpy as np\n", "df = pd.DataFrame({'a': pd.Series(np.random.randint(0, 100, 1000)),\n", "                   'b': pd.Series(np.random.randint(0, 100, 1000)),\n", "                   'c': pd.Series(np.random.randint(0, 100, 1000))})\n", "df"]}, {"cell_type": "markdown", "metadata": {"id": "Vn8gUTIEZg0v"}, "source": ["Cool ! We have created the dataframe with 3 columns and 1000 rows. Now let's see how we can explore the dataframe."]}, {"cell_type": "markdown", "metadata": {"id": "wSNSiz7NBsYi"}, "source": ["## Exploring Data"]}, {"cell_type": "markdown", "metadata": {"id": "usjp3wnrDyhM"}, "source": ["Let's now explore the dataframe using various methods available in pandas.\n"]}, {"cell_type": "markdown", "metadata": {"id": "WZp7tyvTHJJ0"}, "source": ["### 1. Load the occupation data from URL\n", "\n", "Load the data from the URL and create a dataframe."]}, {"cell_type": "code", "execution_count": 33, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "eCPu3_0e53rQ", "outputId": "1c53b523-e31c-4bd2-e777-2efd123eba1b", "executionInfo": {"status": "ok", "timestamp": 1650368729013, "user_tz": -330, "elapsed": 492, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender     occupation zip_code\n", "0          1   24      M     technician    85711\n", "1          2   53      F          other    94043\n", "2          3   23      M         writer    32067\n", "3          4   24      M     technician    43537\n", "4          5   33      F          other    15213\n", "..       ...  ...    ...            ...      ...\n", "938      939   26      F        student    33319\n", "939      940   32      M  administrator    02215\n", "940      941   20      M        student    97229\n", "941      942   48      F      librarian    78209\n", "942      943   22      M        student    77841\n", "\n", "[943 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-eca07368-bb2a-4429-80fb-cf9be5499c0b\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>M</td>\n", "      <td>technician</td>\n", "      <td>85711</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>53</td>\n", "      <td>F</td>\n", "      <td>other</td>\n", "      <td>94043</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>23</td>\n", "      <td>M</td>\n", "      <td>writer</td>\n", "      <td>32067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>24</td>\n", "      <td>M</td>\n", "      <td>technician</td>\n", "      <td>43537</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>33</td>\n", "      <td>F</td>\n", "      <td>other</td>\n", "      <td>15213</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>938</th>\n", "      <td>939</td>\n", "      <td>26</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>33319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>939</th>\n", "      <td>940</td>\n", "      <td>32</td>\n", "      <td>M</td>\n", "      <td>administrator</td>\n", "      <td>02215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>940</th>\n", "      <td>941</td>\n", "      <td>20</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>97229</td>\n", "    </tr>\n", "    <tr>\n", "      <th>941</th>\n", "      <td>942</td>\n", "      <td>48</td>\n", "      <td>F</td>\n", "      <td>librarian</td>\n", "      <td>78209</td>\n", "    </tr>\n", "    <tr>\n", "      <th>942</th>\n", "      <td>943</td>\n", "      <td>22</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>77841</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>943 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-eca07368-bb2a-4429-80fb-cf9be5499c0b')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-eca07368-bb2a-4429-80fb-cf9be5499c0b button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-eca07368-bb2a-4429-80fb-cf9be5499c0b');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 33}], "source": ["# Load the Occupation dataset from the URL. Paste the link in browser to view it.\n", "occupation_df = pd.read_csv('https://raw.githubusercontent.com/justmarkham/DAT8/master/data/u.user', sep=\"|\")\n", "occupation_df"]}, {"cell_type": "markdown", "metadata": {"id": "V6zjO_tGG6so"}, "source": ["### 2. View first 10 rows in the dataframe using `head()`"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 362}, "id": "pIcyRdpnFGfZ", "outputId": "61ab2265-4b9b-43b8-8eca-624dbf9c51f5", "executionInfo": {"status": "ok", "timestamp": 1650368732561, "user_tz": -330, "elapsed": 624, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   user_id  age gender     occupation zip_code\n", "0        1   24      M     technician    85711\n", "1        2   53      F          other    94043\n", "2        3   23      M         writer    32067\n", "3        4   24      M     technician    43537\n", "4        5   33      F          other    15213\n", "5        6   42      M      executive    98101\n", "6        7   57      M  administrator    91344\n", "7        8   36      M  administrator    05201\n", "8        9   29      M        student    01002\n", "9       10   53      M         lawyer    90703"], "text/html": ["\n", "  <div id=\"df-69a00ed8-4564-4fb3-826c-c040ed06fde7\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>M</td>\n", "      <td>technician</td>\n", "      <td>85711</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>53</td>\n", "      <td>F</td>\n", "      <td>other</td>\n", "      <td>94043</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>23</td>\n", "      <td>M</td>\n", "      <td>writer</td>\n", "      <td>32067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>24</td>\n", "      <td>M</td>\n", "      <td>technician</td>\n", "      <td>43537</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>33</td>\n", "      <td>F</td>\n", "      <td>other</td>\n", "      <td>15213</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>42</td>\n", "      <td>M</td>\n", "      <td>executive</td>\n", "      <td>98101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td>57</td>\n", "      <td>M</td>\n", "      <td>administrator</td>\n", "      <td>91344</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8</td>\n", "      <td>36</td>\n", "      <td>M</td>\n", "      <td>administrator</td>\n", "      <td>05201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>9</td>\n", "      <td>29</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>01002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10</td>\n", "      <td>53</td>\n", "      <td>M</td>\n", "      <td>lawyer</td>\n", "      <td>90703</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-69a00ed8-4564-4fb3-826c-c040ed06fde7')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-69a00ed8-4564-4fb3-826c-c040ed06fde7 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-69a00ed8-4564-4fb3-826c-c040ed06fde7');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 34}], "source": ["# Display first 10 rows\n", "occupation_df.head(10)"]}, {"cell_type": "markdown", "metadata": {"id": "f0lPScoIHb-m"}, "source": ["### 3. View last 5 rows from the dataframe using `tail()`"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "ovWgg_koHaZb", "outputId": "d934081e-41d2-4185-8c9f-b2d061fe1e6b", "executionInfo": {"status": "ok", "timestamp": 1650368735610, "user_tz": -330, "elapsed": 355, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender     occupation zip_code\n", "938      939   26      F        student    33319\n", "939      940   32      M  administrator    02215\n", "940      941   20      M        student    97229\n", "941      942   48      F      librarian    78209\n", "942      943   22      M        student    77841"], "text/html": ["\n", "  <div id=\"df-4c890c33-f47c-48ad-b790-f6c89f7b4e8d\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>938</th>\n", "      <td>939</td>\n", "      <td>26</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>33319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>939</th>\n", "      <td>940</td>\n", "      <td>32</td>\n", "      <td>M</td>\n", "      <td>administrator</td>\n", "      <td>02215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>940</th>\n", "      <td>941</td>\n", "      <td>20</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>97229</td>\n", "    </tr>\n", "    <tr>\n", "      <th>941</th>\n", "      <td>942</td>\n", "      <td>48</td>\n", "      <td>F</td>\n", "      <td>librarian</td>\n", "      <td>78209</td>\n", "    </tr>\n", "    <tr>\n", "      <th>942</th>\n", "      <td>943</td>\n", "      <td>22</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>77841</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-4c890c33-f47c-48ad-b790-f6c89f7b4e8d')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-4c890c33-f47c-48ad-b790-f6c89f7b4e8d button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-4c890c33-f47c-48ad-b790-f6c89f7b4e8d');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 35}], "source": ["# Display last 5 rows\n", "occupation_df.tail(5)"]}, {"cell_type": "markdown", "metadata": {"id": "Bn5f3GycIwbq"}, "source": ["### 4. Check the shape and length of dataframe\n", "\n", "Now let's check some basic information like shape, length,  number of columns and rows etc. "]}, {"cell_type": "code", "execution_count": 36, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7JoOnhRYHj5w", "outputId": "25c95335-4df9-4b0a-d29c-577a2748999f", "executionInfo": {"status": "ok", "timestamp": 1650368740510, "user_tz": -330, "elapsed": 354, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Shape of dataframe is (943, 5)\n", "Number of rows in df: 943\n", "Number of columns in df: 5\n", "Length of df: 943\n"]}], "source": ["# Print the shape and length of the dataset\n", "print(f\"Shape of dataframe is {occupation_df.shape}\")\n", "print(f\"Number of rows in df: {occupation_df.shape[0]}\")\n", "print(f\"Number of columns in df: {occupation_df.shape[1]}\")\n", "print(f\"Length of df: {len(occupation_df)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "qE9XS_SbJwrp"}, "source": ["### 5. Check the columns and its datatypes\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M-K_iHR7JJ06", "outputId": "024fb71d-9738-4eab-feee-087c3f3eb0e9", "executionInfo": {"status": "ok", "timestamp": 1650368742006, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Columns : Index(['user_id', 'age', 'gender', 'occupation', 'zip_code'], dtype='object')\n", "Datatypes of each column: \n", "user_id        int64\n", "age            int64\n", "gender        object\n", "occupation    object\n", "zip_code      object\n", "dtype: object\n"]}], "source": ["# Print the columns and the data types of the columns\n", "print(f\"Columns : {occupation_df.columns}\")\n", "print(f\"Datatypes of each column: \\n{occupation_df.dtypes}\")"]}, {"cell_type": "markdown", "metadata": {"id": "hUyQxqAzKTRT"}, "source": ["### 6. Access individual columns or group of columns at a time"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "W3y-Bj5WKNeT", "outputId": "b1203b01-8ca9-4313-af6f-4b42f6accdc5", "executionInfo": {"status": "ok", "timestamp": 1650368743637, "user_tz": -330, "elapsed": 3, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0         technician\n", "1              other\n", "2             writer\n", "3         technician\n", "4              other\n", "           ...      \n", "938          student\n", "939    administrator\n", "940          student\n", "941        librarian\n", "942          student\n", "Name: occupation, Length: 943, dtype: object"]}, "metadata": {}, "execution_count": 38}], "source": ["# Select the 'occupation' column\n", "occupation_df[\"occupation\"]"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"id": "VkmPMGL-Kp3A", "outputId": "1584b994-a88b-4dc8-b308-60fa8c948abb", "colab": {"base_uri": "https://localhost:8080/", "height": 423}, "executionInfo": {"status": "ok", "timestamp": 1650368746136, "user_tz": -330, "elapsed": 611, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     age gender     occupation\n", "0     24      M     technician\n", "1     53      F          other\n", "2     23      M         writer\n", "3     24      M     technician\n", "4     33      F          other\n", "..   ...    ...            ...\n", "938   26      F        student\n", "939   32      M  administrator\n", "940   20      M        student\n", "941   48      F      librarian\n", "942   22      M        student\n", "\n", "[943 rows x 3 columns]"], "text/html": ["\n", "  <div id=\"df-eaf07780-70fe-404d-b015-a0d7971fe87b\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>24</td>\n", "      <td>M</td>\n", "      <td>technician</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>53</td>\n", "      <td>F</td>\n", "      <td>other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>23</td>\n", "      <td>M</td>\n", "      <td>writer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>24</td>\n", "      <td>M</td>\n", "      <td>technician</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>33</td>\n", "      <td>F</td>\n", "      <td>other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>938</th>\n", "      <td>26</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "    </tr>\n", "    <tr>\n", "      <th>939</th>\n", "      <td>32</td>\n", "      <td>M</td>\n", "      <td>administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>940</th>\n", "      <td>20</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "    </tr>\n", "    <tr>\n", "      <th>941</th>\n", "      <td>48</td>\n", "      <td>F</td>\n", "      <td>librarian</td>\n", "    </tr>\n", "    <tr>\n", "      <th>942</th>\n", "      <td>22</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>943 rows × 3 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-eaf07780-70fe-404d-b015-a0d7971fe87b')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-eaf07780-70fe-404d-b015-a0d7971fe87b button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-eaf07780-70fe-404d-b015-a0d7971fe87b');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 39}], "source": ["# Select the 'age', 'gender' and 'occupation' columns\n", "occupation_df[[\"age\", \"gender\", \"occupation\"]]"]}, {"cell_type": "markdown", "metadata": {"id": "6RLJouwPZg0x"}, "source": ["### 7. Use `describe()` to get the summary statistics of the dataframe"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"id": "sVjmVxIv8te6", "colab": {"base_uri": "https://localhost:8080/", "height": 300}, "executionInfo": {"status": "ok", "timestamp": 1650368749753, "user_tz": -330, "elapsed": 614, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "8dfce3c3-7e89-4891-ba61-ab018a1cee9c"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["          user_id         age\n", "count  943.000000  943.000000\n", "mean   472.000000   34.051962\n", "std    272.364951   12.192740\n", "min      1.000000    7.000000\n", "25%    236.500000   25.000000\n", "50%    472.000000   31.000000\n", "75%    707.500000   43.000000\n", "max    943.000000   73.000000"], "text/html": ["\n", "  <div id=\"df-89bf9e17-63ba-47d3-8ea2-e5e27ed84a2a\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>943.000000</td>\n", "      <td>943.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>472.000000</td>\n", "      <td>34.051962</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>272.364951</td>\n", "      <td>12.192740</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>7.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>236.500000</td>\n", "      <td>25.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>472.000000</td>\n", "      <td>31.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>707.500000</td>\n", "      <td>43.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>943.000000</td>\n", "      <td>73.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-89bf9e17-63ba-47d3-8ea2-e5e27ed84a2a')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-89bf9e17-63ba-47d3-8ea2-e5e27ed84a2a button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-89bf9e17-63ba-47d3-8ea2-e5e27ed84a2a');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 40}], "source": ["# Explore the summary statistics using `describe()`\n", "occupation_df.describe()"]}, {"cell_type": "markdown", "metadata": {"id": "iwQ_jol8Zg0x"}, "source": ["By default, the `describe()` method is applied to only numerical values."]}, {"cell_type": "markdown", "metadata": {"id": "AcDgZC2bZg0x"}, "source": ["### 8. Access data using indexing - `iloc` and `loc`"]}, {"cell_type": "markdown", "metadata": {"id": "-YSwXrcrZg0x"}, "source": ["Pandas provides two methods for accessing data using indexing. The `iloc` method is used for integer-location based indexing and the `loc` method is used for label-location based indexing. Let's see in detail how each of the indexing technqiues works."]}, {"cell_type": "markdown", "metadata": {"id": "6LYgCAvOZg0x"}, "source": ["#### 1. `iloc` - Implicit Location-based Indexing\n", "\n", "This method is used to index and slice dataframe using the implicit-Python style indexing.\n", "Let's create a sample dataframe to illustrate the difference between `iloc` and `loc` indexing. "]}, {"cell_type": "code", "execution_count": 41, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "1YOeP1PQZg0y", "executionInfo": {"status": "ok", "timestamp": 1650368752510, "user_tz": -330, "elapsed": 567, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "ce1720c6-fb23-4557-b401-41274705d0bb"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3\n", "a  778  40  95\n", "b  283  91  97\n", "c  641  99  88\n", "d  798   8  69\n", "e  865  11  50"], "text/html": ["\n", "  <div id=\"df-cdc0572c-0e7d-41f5-a008-07d42fbb0cd5\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>a</th>\n", "      <td>778</td>\n", "      <td>40</td>\n", "      <td>95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b</th>\n", "      <td>283</td>\n", "      <td>91</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c</th>\n", "      <td>641</td>\n", "      <td>99</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d</th>\n", "      <td>798</td>\n", "      <td>8</td>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e</th>\n", "      <td>865</td>\n", "      <td>11</td>\n", "      <td>50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-cdc0572c-0e7d-41f5-a008-07d42fbb0cd5')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-cdc0572c-0e7d-41f5-a008-07d42fbb0cd5 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-cdc0572c-0e7d-41f5-a008-07d42fbb0cd5');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 41}], "source": ["# Create a sample dataframe with indeces ['a', 'b', 'c', 'd', 'e'] and two columns filled with random integers in the range of 0 to 100\n", "sample_df = pd.DataFrame({'c1': [np.random.randint(0, 1000) for i in range(5)],\n", "                            'c2': [np.random.randint(0, 100) for i in range(5)],\n", "                            'c3': [np.random.randint(0, 100) for i in range(5)]}, \n", "                        index=['a', 'b', 'c', 'd', 'e'])\n", "sample_df"]}, {"cell_type": "markdown", "metadata": {"id": "1-BvgeosZg0y"}, "source": ["In the above cell, we have created a dataframe with 3 columns and 5 rows. Let's see how we can access the data using `iloc` indexing. Before that let's just print the index and columns of the dataframe."]}, {"cell_type": "code", "execution_count": 42, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AiEu0LH6Zg0y", "executionInfo": {"status": "ok", "timestamp": 1650368755744, "user_tz": -330, "elapsed": 590, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "1847949b-e7b8-4c55-9011-70d34e90e5fe"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Index(['a', 'b', 'c', 'd', 'e'], dtype='object')"]}, "metadata": {}, "execution_count": 42}], "source": ["# Print the index of the sample dataframe\n", "sample_df.index"]}, {"cell_type": "code", "execution_count": 43, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NflHpGv8Zg0y", "executionInfo": {"status": "ok", "timestamp": 1650368757013, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "caae3e48-0be2-4d2e-dffd-770782cdb432"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["c1    778\n", "c2     40\n", "c3     95\n", "Name: a, dtype: int64"]}, "metadata": {}, "execution_count": 43}], "source": ["# Access the samle dataframe using the iloc method - select first row\n", "sample_df.iloc[0]"]}, {"cell_type": "code", "execution_count": 44, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 143}, "id": "QE7A0-sRZg0y", "executionInfo": {"status": "ok", "timestamp": 1650368758904, "user_tz": -330, "elapsed": 8, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "f1e8bb22-b056-4232-95a8-2651a79c5170"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3\n", "a  778  40  95\n", "b  283  91  97\n", "c  641  99  88"], "text/html": ["\n", "  <div id=\"df-c270218f-253f-4817-886f-51af721db7db\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>a</th>\n", "      <td>778</td>\n", "      <td>40</td>\n", "      <td>95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b</th>\n", "      <td>283</td>\n", "      <td>91</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c</th>\n", "      <td>641</td>\n", "      <td>99</td>\n", "      <td>88</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-c270218f-253f-4817-886f-51af721db7db')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-c270218f-253f-4817-886f-51af721db7db button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-c270218f-253f-4817-886f-51af721db7db');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 44}], "source": ["# Select the first 3 rows using iloc\n", "sample_df.iloc[:3]"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 143}, "id": "IGq-S-fnZg0y", "executionInfo": {"status": "ok", "timestamp": 1650368760643, "user_tz": -330, "elapsed": 7, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "cccb83db-6299-46fe-fdde-a45a27e14aa2"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3\n", "b  283  91  97\n", "c  641  99  88\n", "d  798   8  69"], "text/html": ["\n", "  <div id=\"df-d30a62c8-9d62-48ac-b5d2-69cbf1303114\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>b</th>\n", "      <td>283</td>\n", "      <td>91</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c</th>\n", "      <td>641</td>\n", "      <td>99</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d</th>\n", "      <td>798</td>\n", "      <td>8</td>\n", "      <td>69</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-d30a62c8-9d62-48ac-b5d2-69cbf1303114')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-d30a62c8-9d62-48ac-b5d2-69cbf1303114 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-d30a62c8-9d62-48ac-b5d2-69cbf1303114');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 45}], "source": ["# Select the middle 3 rows using iloc\n", "sample_df.iloc[1:4]"]}, {"cell_type": "code", "execution_count": 46, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 112}, "id": "Qn0GyM1YZg0y", "executionInfo": {"status": "ok", "timestamp": 1650368762224, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "ba6ce628-aeaf-41e1-a784-f2cc944eba48"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3\n", "d  798   8  69\n", "e  865  11  50"], "text/html": ["\n", "  <div id=\"df-ba24f9df-8ac6-41f8-b827-438957837976\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>d</th>\n", "      <td>798</td>\n", "      <td>8</td>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e</th>\n", "      <td>865</td>\n", "      <td>11</td>\n", "      <td>50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-ba24f9df-8ac6-41f8-b827-438957837976')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-ba24f9df-8ac6-41f8-b827-438957837976 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-ba24f9df-8ac6-41f8-b827-438957837976');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 46}], "source": ["# Select the last 2 rows using iloc\n", "sample_df.iloc[-2:]"]}, {"cell_type": "markdown", "metadata": {"id": "ap1nyM7dZg0z"}, "source": ["In the above four different scenarios, we can observe that while indexing or selecting the dataframe using the `iloc` method, the indexing is done using the row index number i.e. an integer and not the index name. It is important to note: \n", "\n", "**`iloc` indexing is 0-based. It follows the Python convention.**"]}, {"cell_type": "markdown", "metadata": {"id": "sugrCUdhZg0z"}, "source": ["Now let's explore various methods of `loc` indexing."]}, {"cell_type": "markdown", "metadata": {"id": "RhkpchjoZg0z"}, "source": ["#### 2. `loc` - Explicit Location-based Indexing\n", "\n", "This method is used to index and slice dataframe using the explicit indeces of the dataframe.\n", "Let's use the dataset created above for the same purpose.\n"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "ZPZ_3AjeZg0z", "executionInfo": {"status": "ok", "timestamp": 1650368765056, "user_tz": -330, "elapsed": 434, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "a5eaad80-65ac-46da-b665-0b1ed5e11142"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3\n", "a  778  40  95\n", "b  283  91  97\n", "c  641  99  88\n", "d  798   8  69\n", "e  865  11  50"], "text/html": ["\n", "  <div id=\"df-1b146049-4020-4a5b-b11e-d97978e01339\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>a</th>\n", "      <td>778</td>\n", "      <td>40</td>\n", "      <td>95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b</th>\n", "      <td>283</td>\n", "      <td>91</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c</th>\n", "      <td>641</td>\n", "      <td>99</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d</th>\n", "      <td>798</td>\n", "      <td>8</td>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e</th>\n", "      <td>865</td>\n", "      <td>11</td>\n", "      <td>50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-1b146049-4020-4a5b-b11e-d97978e01339')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-1b146049-4020-4a5b-b11e-d97978e01339 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-1b146049-4020-4a5b-b11e-d97978e01339');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 47}], "source": ["# Display the sample dataframe\n", "sample_df"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 513}, "id": "nW1sEEF1Zg0z", "executionInfo": {"status": "error", "timestamp": 1650368767298, "user_tz": -330, "elapsed": 499, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "08ba0261-a1ec-4c7d-92b0-e74115f17ecc"}, "outputs": [{"output_type": "error", "ename": "KeyError", "evalue": "ignored", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/usr/local/lib/python3.7/dist-packages/pandas/core/indexes/base.py\u001b[0m in \u001b[0;36mget_loc\u001b[0;34m(self, key, method, tolerance)\u001b[0m\n\u001b[1;32m   3360\u001b[0m             \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 3361\u001b[0;31m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_loc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcasted_key\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   3362\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mKeyError\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0merr\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.7/dist-packages/pandas/_libs/index.pyx\u001b[0m in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.7/dist-packages/pandas/_libs/index.pyx\u001b[0m in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/hashtable_class_helper.pxi\u001b[0m in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/hashtable_class_helper.pxi\u001b[0m in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;31m<PERSON><PERSON>E<PERSON>r\u001b[0m: 0", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-48-3329197dfb99>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;31m# Select the first row using loc\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0msample_df\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mloc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m/usr/local/lib/python3.7/dist-packages/pandas/core/indexing.py\u001b[0m in \u001b[0;36m__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m    929\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    930\u001b[0m             \u001b[0mmaybe_callable\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcom\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply_if_callable\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mobj\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 931\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_getitem_axis\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmaybe_callable\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0maxis\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0maxis\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    932\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    933\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_is_scalar_access\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkey\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mtuple\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.7/dist-packages/pandas/core/indexing.py\u001b[0m in \u001b[0;36m_getitem_axis\u001b[0;34m(self, key, axis)\u001b[0m\n\u001b[1;32m   1162\u001b[0m         \u001b[0;31m# fall thru to straight lookup\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1163\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_validate_key\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0maxis\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1164\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_get_label\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0maxis\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0maxis\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1165\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1166\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_get_slice_axis\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mslice_obj\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mslice\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0maxis\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mint\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.7/dist-packages/pandas/core/indexing.py\u001b[0m in \u001b[0;36m_get_label\u001b[0;34m(self, label, axis)\u001b[0m\n\u001b[1;32m   1111\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_get_label\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlabel\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0maxis\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mint\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1112\u001b[0m         \u001b[0;31m# GH#5667 this will fail if the label is not present in the axis.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1113\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mobj\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mxs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlabel\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0maxis\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0maxis\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1114\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1115\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_handle_lowerdim_multi_index_axis0\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtup\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mtuple\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.7/dist-packages/pandas/core/generic.py\u001b[0m in \u001b[0;36mxs\u001b[0;34m(self, key, axis, level, drop_level)\u001b[0m\n\u001b[1;32m   3774\u001b[0m                 \u001b[0;32mraise\u001b[0m \u001b[0mTypeError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf\"Expected label or tuple of labels, got {key}\"\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   3775\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 3776\u001b[0;31m             \u001b[0mloc\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mindex\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_loc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   3777\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   3778\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0misinstance\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mloc\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mndarray\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.7/dist-packages/pandas/core/indexes/base.py\u001b[0m in \u001b[0;36mget_loc\u001b[0;34m(self, key, method, tolerance)\u001b[0m\n\u001b[1;32m   3361\u001b[0m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_loc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcasted_key\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   3362\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mKeyError\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0merr\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 3363\u001b[0;31m                 \u001b[0;32mraise\u001b[0m \u001b[0mKeyError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0merr\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   3364\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   3365\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mis_scalar\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0misna\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mhasnans\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31m<PERSON><PERSON>E<PERSON>r\u001b[0m: 0"]}], "source": ["# Select the first row using loc\n", "sample_df.loc[0]"]}, {"cell_type": "markdown", "metadata": {"id": "3zV6FTvWZg0z"}, "source": ["Oh ! We ran into `KeyError` error. It means that the key through which we are trying to access the data is not present in indeces i.e. ***0 is not present in the index row.*** Now let's check the correct way for using `loc` to access the data. "]}, {"cell_type": "code", "execution_count": 49, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vCKrpzT2Zg0z", "executionInfo": {"status": "ok", "timestamp": 1650368771064, "user_tz": -330, "elapsed": 416, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "e0c30482-7946-4a54-fd4e-a944ef1a10a8"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Index(['a', 'b', 'c', 'd', 'e'], dtype='object')\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["c1    778\n", "c2     40\n", "c3     95\n", "Name: a, dtype: int64"]}, "metadata": {}, "execution_count": 49}], "source": ["# Select the first row using loc method\n", "print(sample_df.index)\n", "sample_df.loc['a']"]}, {"cell_type": "code", "execution_count": 50, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 143}, "id": "l_lw4jyGZg00", "executionInfo": {"status": "ok", "timestamp": 1650368774264, "user_tz": -330, "elapsed": 591, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "a4a316d5-5150-4a4e-d08c-af9421d093c2"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3\n", "a  778  40  95\n", "b  283  91  97\n", "c  641  99  88"], "text/html": ["\n", "  <div id=\"df-3961ac87-d6d2-40c2-979d-3ff68468a3cf\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>a</th>\n", "      <td>778</td>\n", "      <td>40</td>\n", "      <td>95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b</th>\n", "      <td>283</td>\n", "      <td>91</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c</th>\n", "      <td>641</td>\n", "      <td>99</td>\n", "      <td>88</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-3961ac87-d6d2-40c2-979d-3ff68468a3cf')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-3961ac87-d6d2-40c2-979d-3ff68468a3cf button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-3961ac87-d6d2-40c2-979d-3ff68468a3cf');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 50}], "source": ["# Select the first 3 rows using loc\n", "sample_df.loc[:'c']"]}, {"cell_type": "code", "execution_count": 51, "metadata": {"id": "kcblwlwyJ7-D", "colab": {"base_uri": "https://localhost:8080/", "height": 143}, "executionInfo": {"status": "ok", "timestamp": 1650368776018, "user_tz": -330, "elapsed": 7, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "126ff1ab-b704-4d33-83a9-e53ffe473e57"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3\n", "b  283  91  97\n", "c  641  99  88\n", "d  798   8  69"], "text/html": ["\n", "  <div id=\"df-4a39eda8-9eab-45ba-9f00-ab8024dd0083\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>b</th>\n", "      <td>283</td>\n", "      <td>91</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c</th>\n", "      <td>641</td>\n", "      <td>99</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d</th>\n", "      <td>798</td>\n", "      <td>8</td>\n", "      <td>69</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-4a39eda8-9eab-45ba-9f00-ab8024dd0083')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-4a39eda8-9eab-45ba-9f00-ab8024dd0083 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-4a39eda8-9eab-45ba-9f00-ab8024dd0083');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 51}], "source": ["# Select the middle 3 rows using loc\n", "sample_df.loc['b':'d']"]}, {"cell_type": "code", "execution_count": 52, "metadata": {"id": "jctfSrs7s06d", "colab": {"base_uri": "https://localhost:8080/", "height": 81}, "executionInfo": {"status": "ok", "timestamp": 1650368777784, "user_tz": -330, "elapsed": 8, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "ef479a56-e0b0-4138-a57b-20ef807f0f5e"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3\n", "e  865  11  50"], "text/html": ["\n", "  <div id=\"df-3a577a39-a811-4e1b-9cf8-dc72f1bfc59f\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>e</th>\n", "      <td>865</td>\n", "      <td>11</td>\n", "      <td>50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-3a577a39-a811-4e1b-9cf8-dc72f1bfc59f')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-3a577a39-a811-4e1b-9cf8-dc72f1bfc59f button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-3a577a39-a811-4e1b-9cf8-dc72f1bfc59f');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 52}], "source": ["# Select the last 2 rows using loc\n", "sample_df.loc['e':]"]}, {"cell_type": "markdown", "metadata": {"id": "7M2DDbTMZg00"}, "source": ["From the above examples, we got some basic idea about the differences between `iloc` and `loc` indexing. In the `loc` indexing, the name of the index is used to index the dataframe rather than the position of the index. It is recomendded to use iloc indexing for accessing the dataframe due to its consistent and flexible nature."]}, {"cell_type": "markdown", "metadata": {"id": "fLL0TwEIZg00"}, "source": ["## Manipulating Data\n", "\n", "Lets now look at some basic manipulations that we can perform on the dataframe. This includes arithmetic operations, sorting, filtering, and more. We can also perform the same operations on the dataframe using the `apply()` method. Let's discuss some methods in detail."]}, {"cell_type": "code", "execution_count": 53, "metadata": {"id": "F1mijjcSZg00", "executionInfo": {"status": "ok", "timestamp": 1650368780342, "user_tz": -330, "elapsed": 622, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [], "source": ["# Create two dataframe and fill it with random integers in the range of 0 to 100\n", "sample_df1 = pd.DataFrame({'c1': [np.random.randint(0, 1000) for i in range(5)],\n", "                            'c2': [np.random.randint(0, 100) for i in range(5)],\n", "                            'c3': [np.random.randint(0, 100) for i in range(5)]})\n", "\n", "sample_df2 = pd.DataFrame({'c1': [np.random.randint(0, 1000) for i in range(5)],\n", "                            'c2': [np.random.randint(0, 100) for i in range(5)],\n", "                            'c3': [np.random.randint(0, 100) for i in range(5)]})"]}, {"cell_type": "code", "execution_count": 54, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "Uj-CYs28Zg00", "executionInfo": {"status": "ok", "timestamp": 1650368782352, "user_tz": -330, "elapsed": 386, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "7f74c730-2603-4f13-e42b-b86608e3c602"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3\n", "0  174  90  61\n", "1  131  70  36\n", "2  204  24  30\n", "3  261  79  31\n", "4  301  57  63"], "text/html": ["\n", "  <div id=\"df-d9e655ea-eb3f-48ec-b6d5-4d4870c0af14\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>174</td>\n", "      <td>90</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>131</td>\n", "      <td>70</td>\n", "      <td>36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>204</td>\n", "      <td>24</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>261</td>\n", "      <td>79</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>301</td>\n", "      <td>57</td>\n", "      <td>63</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-d9e655ea-eb3f-48ec-b6d5-4d4870c0af14')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-d9e655ea-eb3f-48ec-b6d5-4d4870c0af14 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-d9e655ea-eb3f-48ec-b6d5-4d4870c0af14');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 54}], "source": ["sample_df1"]}, {"cell_type": "code", "execution_count": 55, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "nuK84hfbZg01", "executionInfo": {"status": "ok", "timestamp": 1650368785173, "user_tz": -330, "elapsed": 633, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "385acbaa-dbd3-410f-9802-7617ac75e388"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3\n", "0  655  59  80\n", "1  222  15  73\n", "2  725  52  49\n", "3  492  28  62\n", "4  888  68   3"], "text/html": ["\n", "  <div id=\"df-41a66ac4-ba93-44a8-a6b9-fe8441f7c5a0\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>655</td>\n", "      <td>59</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>222</td>\n", "      <td>15</td>\n", "      <td>73</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>725</td>\n", "      <td>52</td>\n", "      <td>49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>492</td>\n", "      <td>28</td>\n", "      <td>62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>888</td>\n", "      <td>68</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-41a66ac4-ba93-44a8-a6b9-fe8441f7c5a0')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-41a66ac4-ba93-44a8-a6b9-fe8441f7c5a0 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-41a66ac4-ba93-44a8-a6b9-fe8441f7c5a0');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 55}], "source": ["sample_df2"]}, {"cell_type": "markdown", "metadata": {"id": "1Q-Pya7CZg01"}, "source": ["### 1. Arithmetic Operations\n", "\n", "Basic arithmetic operations can be performed on the dataframe. Let's see how we can perform addition, subtraction, multiplication and division on the dataframe."]}, {"cell_type": "code", "execution_count": 56, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "USERmGT3Zg01", "executionInfo": {"status": "ok", "timestamp": 1650368786894, "user_tz": -330, "elapsed": 5, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "131c41b8-62a3-4e4e-8c7f-54334a20d25c"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     c1   c2   c3\n", "0   829  149  141\n", "1   353   85  109\n", "2   929   76   79\n", "3   753  107   93\n", "4  1189  125   66"], "text/html": ["\n", "  <div id=\"df-99cee1d5-5de8-4285-ab5c-6b0a3605a498\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>829</td>\n", "      <td>149</td>\n", "      <td>141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>353</td>\n", "      <td>85</td>\n", "      <td>109</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>929</td>\n", "      <td>76</td>\n", "      <td>79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>753</td>\n", "      <td>107</td>\n", "      <td>93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1189</td>\n", "      <td>125</td>\n", "      <td>66</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-99cee1d5-5de8-4285-ab5c-6b0a3605a498')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-99cee1d5-5de8-4285-ab5c-6b0a3605a498 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-99cee1d5-5de8-4285-ab5c-6b0a3605a498');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 56}], "source": ["# Perform addition of two dataframes\n", "sample_df1 + sample_df2"]}, {"cell_type": "markdown", "metadata": {"id": "KnMxwptPZg01"}, "source": ["Similary we can perform Subtraction, Multiplication and Division operations on the dataframe. The result of these operations will be a new dataframe. We can save it as a new dataframe or assign it to the original dataframe. "]}, {"cell_type": "code", "execution_count": 57, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "lYZdT_XPZg01", "executionInfo": {"status": "ok", "timestamp": 1650368790114, "user_tz": -330, "elapsed": 579, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "b66ff45f-08cf-426f-a521-6246281b2db9"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3  result\n", "0  174  90  61   15599\n", "1  131  70  36    9134\n", "2  204  24  30    4866\n", "3  261  79  31   20588\n", "4  301  57  63   17094"], "text/html": ["\n", "  <div id=\"df-9e859c15-2dbc-4815-a640-871388e007ee\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "      <th>result</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>174</td>\n", "      <td>90</td>\n", "      <td>61</td>\n", "      <td>15599</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>131</td>\n", "      <td>70</td>\n", "      <td>36</td>\n", "      <td>9134</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>204</td>\n", "      <td>24</td>\n", "      <td>30</td>\n", "      <td>4866</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>261</td>\n", "      <td>79</td>\n", "      <td>31</td>\n", "      <td>20588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>301</td>\n", "      <td>57</td>\n", "      <td>63</td>\n", "      <td>17094</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-9e859c15-2dbc-4815-a640-871388e007ee')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-9e859c15-2dbc-4815-a640-871388e007ee button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-9e859c15-2dbc-4815-a640-871388e007ee');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 57}], "source": ["# Perform arithmetic operation of of all columns and create new column\n", "sample_df1[\"result\"] = sample_df1[\"c1\"] * sample_df1[\"c2\"] - sample_df1[\"c3\"]\n", "sample_df1"]}, {"cell_type": "code", "execution_count": 58, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RENVhqYyZg01", "executionInfo": {"status": "ok", "timestamp": 1650368792402, "user_tz": -330, "elapsed": 618, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "86fbd78a-97df-4e30-b9b9-90c5164b3e91"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    173.322222\n", "1    130.485714\n", "2    202.750000\n", "3    260.607595\n", "4    299.894737\n", "dtype: float64"]}, "metadata": {}, "execution_count": 58}], "source": ["# Divide the \"result\" column by the \"c2\" column\n", "sample_df1[\"result\"] / sample_df1[\"c2\"]"]}, {"cell_type": "markdown", "metadata": {"id": "-Cg8JWxmZg02"}, "source": ["The above code is applicable to all the arithmetic operations. Let's see how we can perform the same operations on the dataframe using the `apply()` method."]}, {"cell_type": "markdown", "metadata": {"id": "SjkbIU6AZg02"}, "source": ["### 2. Using `apply()` method"]}, {"cell_type": "markdown", "metadata": {"id": "iQ2emmioZg02"}, "source": ["Let's apply some arithmetic operations on the dataframe using the `apply()` method. We will use the lambda function to perform operation on the dataframe."]}, {"cell_type": "code", "execution_count": 59, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "MBIxADy_Zg02", "executionInfo": {"status": "ok", "timestamp": 1650368795125, "user_tz": -330, "elapsed": 361, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "f97eef5b-daa2-437c-8c55-cbbb403fe1b1"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3  result\n", "0  174  90  61   15599\n", "1  131  70  36    9134\n", "2  204  24  30    4866\n", "3  261  79  31   20588\n", "4  301  57  63   17094"], "text/html": ["\n", "  <div id=\"df-0acfbb7c-78ea-45b8-ae44-42be44f06435\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "      <th>result</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>174</td>\n", "      <td>90</td>\n", "      <td>61</td>\n", "      <td>15599</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>131</td>\n", "      <td>70</td>\n", "      <td>36</td>\n", "      <td>9134</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>204</td>\n", "      <td>24</td>\n", "      <td>30</td>\n", "      <td>4866</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>261</td>\n", "      <td>79</td>\n", "      <td>31</td>\n", "      <td>20588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>301</td>\n", "      <td>57</td>\n", "      <td>63</td>\n", "      <td>17094</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-0acfbb7c-78ea-45b8-ae44-42be44f06435')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-0acfbb7c-78ea-45b8-ae44-42be44f06435 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-0acfbb7c-78ea-45b8-ae44-42be44f06435');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 59}], "source": ["# Perform floor division on the \"result\" column by 2\n", "sample_df1[\"result\"].apply(lambda x: x // 2)\n", "sample_df1"]}, {"cell_type": "markdown", "metadata": {"id": "0RAnEYoTZg02"}, "source": ["We can see observe that there is no change in the `result` column even though we have performed floor division operation. This is because by default pandas will return the result of the operation and we need to explicitly update the column. This can be done as follows:"]}, {"cell_type": "code", "execution_count": 60, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "86MFja6BZg02", "executionInfo": {"status": "ok", "timestamp": 1650368796642, "user_tz": -330, "elapsed": 5, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "4d727d2b-4f49-450a-cb21-5998a0436f37"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    c1  c2  c3  result\n", "0  174  90  61    7799\n", "1  131  70  36    4567\n", "2  204  24  30    2433\n", "3  261  79  31   10294\n", "4  301  57  63    8547"], "text/html": ["\n", "  <div id=\"df-3788febc-d16b-4771-8920-7d273b4c3681\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "      <th>c3</th>\n", "      <th>result</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>174</td>\n", "      <td>90</td>\n", "      <td>61</td>\n", "      <td>7799</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>131</td>\n", "      <td>70</td>\n", "      <td>36</td>\n", "      <td>4567</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>204</td>\n", "      <td>24</td>\n", "      <td>30</td>\n", "      <td>2433</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>261</td>\n", "      <td>79</td>\n", "      <td>31</td>\n", "      <td>10294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>301</td>\n", "      <td>57</td>\n", "      <td>63</td>\n", "      <td>8547</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-3788febc-d16b-4771-8920-7d273b4c3681')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-3788febc-d16b-4771-8920-7d273b4c3681 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-3788febc-d16b-4771-8920-7d273b4c3681');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 60}], "source": ["# Assigning the result back to the \"result\" column\n", "sample_df1[\"result\"] = sample_df1[\"result\"].apply(lambda x: x // 2)\n", "sample_df1"]}, {"cell_type": "markdown", "metadata": {"id": "Ccr1jg3sZg02"}, "source": ["### 3. Sorting & Filtering based on conditions"]}, {"cell_type": "markdown", "metadata": {"id": "cea-Fvl_Zg03"}, "source": ["We can perform basic sorting and filtering operations on the dataframe using some boolean expressions. To illustrate, let's use the `occupation_df` dataframe. "]}, {"cell_type": "code", "execution_count": 61, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "KQtTV8IfZg03", "executionInfo": {"status": "ok", "timestamp": 1650368799769, "user_tz": -330, "elapsed": 499, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "a9cc7afc-cb72-4893-8ab5-f7954b3c02ea"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender     occupation zip_code\n", "0          1   24      M     technician    85711\n", "1          2   53      F          other    94043\n", "2          3   23      M         writer    32067\n", "3          4   24      M     technician    43537\n", "4          5   33      F          other    15213\n", "..       ...  ...    ...            ...      ...\n", "938      939   26      F        student    33319\n", "939      940   32      M  administrator    02215\n", "940      941   20      M        student    97229\n", "941      942   48      F      librarian    78209\n", "942      943   22      M        student    77841\n", "\n", "[943 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-840f2987-c93a-4c80-9d41-f954de4529b3\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>M</td>\n", "      <td>technician</td>\n", "      <td>85711</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>53</td>\n", "      <td>F</td>\n", "      <td>other</td>\n", "      <td>94043</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>23</td>\n", "      <td>M</td>\n", "      <td>writer</td>\n", "      <td>32067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>24</td>\n", "      <td>M</td>\n", "      <td>technician</td>\n", "      <td>43537</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>33</td>\n", "      <td>F</td>\n", "      <td>other</td>\n", "      <td>15213</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>938</th>\n", "      <td>939</td>\n", "      <td>26</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>33319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>939</th>\n", "      <td>940</td>\n", "      <td>32</td>\n", "      <td>M</td>\n", "      <td>administrator</td>\n", "      <td>02215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>940</th>\n", "      <td>941</td>\n", "      <td>20</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>97229</td>\n", "    </tr>\n", "    <tr>\n", "      <th>941</th>\n", "      <td>942</td>\n", "      <td>48</td>\n", "      <td>F</td>\n", "      <td>librarian</td>\n", "      <td>78209</td>\n", "    </tr>\n", "    <tr>\n", "      <th>942</th>\n", "      <td>943</td>\n", "      <td>22</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>77841</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>943 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-840f2987-c93a-4c80-9d41-f954de4529b3')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-840f2987-c93a-4c80-9d41-f954de4529b3 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-840f2987-c93a-4c80-9d41-f954de4529b3');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 61}], "source": ["# Load the Occupation dataset from the URL. Paste the link in browser to view it.\n", "occupation_df = pd.read_csv('https://raw.githubusercontent.com/justmarkham/DAT8/master/data/u.user', sep=\"|\")\n", "occupation_df"]}, {"cell_type": "markdown", "metadata": {"id": "vAJJsjx3Zg03"}, "source": ["Now let's apply some `sort` operations on some rows of the dataframe."]}, {"cell_type": "markdown", "metadata": {"id": "f6R9ceamZg03"}, "source": ["Note: By default, the pandas won't apply the changes to the original dataframe. We need to explicitly update the dataframe. We can use the `inplace` argument to do so."]}, {"cell_type": "markdown", "metadata": {"id": "zWHXS_uXZg03"}, "source": ["#### 1. Sort the dataframe - I "]}, {"cell_type": "markdown", "metadata": {"id": "donwHh8bZg04"}, "source": ["Sort the dataframe based on the `age` column. We can sort the dataframe based on the `age` column using the `sort_values()` method. By specifying the `ascending` parameter, we can sort the dataframe in ascending order."]}, {"cell_type": "code", "execution_count": 62, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "vRIyMEhUZg04", "executionInfo": {"status": "ok", "timestamp": 1650368804177, "user_tz": -330, "elapsed": 623, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "fedbe1db-640a-416b-ae09-857edae3ee89"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender     occupation zip_code\n", "29        30    7      M        student    55436\n", "470      471   10      M        student    77459\n", "288      289   11      M           none    94619\n", "879      880   13      M        student    83702\n", "608      609   13      F        student    55106\n", "..       ...  ...    ...            ...      ...\n", "584      585   69      M      librarian    98501\n", "766      767   70      M       engineer    00000\n", "802      803   70      M  administrator    78212\n", "859      860   70      F        retired    48322\n", "480      481   73      M        retired    37771\n", "\n", "[943 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-65883f92-862c-4e30-b308-9584f5c4f9a3\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>30</td>\n", "      <td>7</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>55436</td>\n", "    </tr>\n", "    <tr>\n", "      <th>470</th>\n", "      <td>471</td>\n", "      <td>10</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>77459</td>\n", "    </tr>\n", "    <tr>\n", "      <th>288</th>\n", "      <td>289</td>\n", "      <td>11</td>\n", "      <td>M</td>\n", "      <td>none</td>\n", "      <td>94619</td>\n", "    </tr>\n", "    <tr>\n", "      <th>879</th>\n", "      <td>880</td>\n", "      <td>13</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>83702</td>\n", "    </tr>\n", "    <tr>\n", "      <th>608</th>\n", "      <td>609</td>\n", "      <td>13</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>55106</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>584</th>\n", "      <td>585</td>\n", "      <td>69</td>\n", "      <td>M</td>\n", "      <td>librarian</td>\n", "      <td>98501</td>\n", "    </tr>\n", "    <tr>\n", "      <th>766</th>\n", "      <td>767</td>\n", "      <td>70</td>\n", "      <td>M</td>\n", "      <td>engineer</td>\n", "      <td>00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>802</th>\n", "      <td>803</td>\n", "      <td>70</td>\n", "      <td>M</td>\n", "      <td>administrator</td>\n", "      <td>78212</td>\n", "    </tr>\n", "    <tr>\n", "      <th>859</th>\n", "      <td>860</td>\n", "      <td>70</td>\n", "      <td>F</td>\n", "      <td>retired</td>\n", "      <td>48322</td>\n", "    </tr>\n", "    <tr>\n", "      <th>480</th>\n", "      <td>481</td>\n", "      <td>73</td>\n", "      <td>M</td>\n", "      <td>retired</td>\n", "      <td>37771</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>943 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-65883f92-862c-4e30-b308-9584f5c4f9a3')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-65883f92-862c-4e30-b308-9584f5c4f9a3 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-65883f92-862c-4e30-b308-9584f5c4f9a3');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 62}], "source": ["# Sort the occupation_df by age in ascending order\n", "occupation_df.sort_values(by=\"age\", ascending=True)"]}, {"cell_type": "code", "execution_count": 63, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "oPJ3RkE2Zg04", "executionInfo": {"status": "ok", "timestamp": 1650368807481, "user_tz": -330, "elapsed": 375, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "9e40850f-f247-4931-c2fe-5701eb611394"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender     occupation zip_code\n", "480      481   73      M        retired    37771\n", "802      803   70      M  administrator    78212\n", "766      767   70      M       engineer    00000\n", "859      860   70      F        retired    48322\n", "584      585   69      M      librarian    98501\n", "..       ...  ...    ...            ...      ...\n", "879      880   13      M        student    83702\n", "608      609   13      F        student    55106\n", "288      289   11      M           none    94619\n", "470      471   10      M        student    77459\n", "29        30    7      M        student    55436\n", "\n", "[943 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-47d02163-dbed-4e3d-b2c7-4d388959074b\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>480</th>\n", "      <td>481</td>\n", "      <td>73</td>\n", "      <td>M</td>\n", "      <td>retired</td>\n", "      <td>37771</td>\n", "    </tr>\n", "    <tr>\n", "      <th>802</th>\n", "      <td>803</td>\n", "      <td>70</td>\n", "      <td>M</td>\n", "      <td>administrator</td>\n", "      <td>78212</td>\n", "    </tr>\n", "    <tr>\n", "      <th>766</th>\n", "      <td>767</td>\n", "      <td>70</td>\n", "      <td>M</td>\n", "      <td>engineer</td>\n", "      <td>00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>859</th>\n", "      <td>860</td>\n", "      <td>70</td>\n", "      <td>F</td>\n", "      <td>retired</td>\n", "      <td>48322</td>\n", "    </tr>\n", "    <tr>\n", "      <th>584</th>\n", "      <td>585</td>\n", "      <td>69</td>\n", "      <td>M</td>\n", "      <td>librarian</td>\n", "      <td>98501</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>879</th>\n", "      <td>880</td>\n", "      <td>13</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>83702</td>\n", "    </tr>\n", "    <tr>\n", "      <th>608</th>\n", "      <td>609</td>\n", "      <td>13</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>55106</td>\n", "    </tr>\n", "    <tr>\n", "      <th>288</th>\n", "      <td>289</td>\n", "      <td>11</td>\n", "      <td>M</td>\n", "      <td>none</td>\n", "      <td>94619</td>\n", "    </tr>\n", "    <tr>\n", "      <th>470</th>\n", "      <td>471</td>\n", "      <td>10</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>77459</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>30</td>\n", "      <td>7</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>55436</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>943 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-47d02163-dbed-4e3d-b2c7-4d388959074b')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-47d02163-dbed-4e3d-b2c7-4d388959074b button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-47d02163-dbed-4e3d-b2c7-4d388959074b');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 63}], "source": ["# Sort the occupation_df by age in descending order\n", "occupation_df.sort_values(by=\"age\", ascending=False)"]}, {"cell_type": "markdown", "metadata": {"id": "jiZqUE6_Zg04"}, "source": ["#### 2. Sort the dataframe - II"]}, {"cell_type": "markdown", "metadata": {"id": "PN7xgTTfZg04"}, "source": ["In the below result, we can see that the `occupation` column is sorted in ascending order. To the change permanent, let's use the `inplace` parameter. "]}, {"cell_type": "code", "execution_count": 64, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "n0ZnPbF4Zg04", "executionInfo": {"status": "ok", "timestamp": 1650368809180, "user_tz": -330, "elapsed": 7, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "029998ef-f961-439b-cc3c-c52b0314f562"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender     occupation zip_code\n", "71        72   48      F  administrator    73034\n", "767      768   29      M  administrator    12866\n", "325      326   41      M  administrator    15235\n", "856      857   35      F  administrator    V1G4L\n", "88        89   43      F  administrator    68106\n", "..       ...  ...    ...            ...      ...\n", "263      264   36      F         writer    90064\n", "490      491   43      F         writer    53711\n", "497      498   26      M         writer    55408\n", "389      390   42      F         writer    85016\n", "832      833   34      M         writer    90019\n", "\n", "[943 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-4c25447f-1f8f-4643-8344-dd37d60790dc\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>72</td>\n", "      <td>48</td>\n", "      <td>F</td>\n", "      <td>administrator</td>\n", "      <td>73034</td>\n", "    </tr>\n", "    <tr>\n", "      <th>767</th>\n", "      <td>768</td>\n", "      <td>29</td>\n", "      <td>M</td>\n", "      <td>administrator</td>\n", "      <td>12866</td>\n", "    </tr>\n", "    <tr>\n", "      <th>325</th>\n", "      <td>326</td>\n", "      <td>41</td>\n", "      <td>M</td>\n", "      <td>administrator</td>\n", "      <td>15235</td>\n", "    </tr>\n", "    <tr>\n", "      <th>856</th>\n", "      <td>857</td>\n", "      <td>35</td>\n", "      <td>F</td>\n", "      <td>administrator</td>\n", "      <td>V1G4L</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>89</td>\n", "      <td>43</td>\n", "      <td>F</td>\n", "      <td>administrator</td>\n", "      <td>68106</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>263</th>\n", "      <td>264</td>\n", "      <td>36</td>\n", "      <td>F</td>\n", "      <td>writer</td>\n", "      <td>90064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>490</th>\n", "      <td>491</td>\n", "      <td>43</td>\n", "      <td>F</td>\n", "      <td>writer</td>\n", "      <td>53711</td>\n", "    </tr>\n", "    <tr>\n", "      <th>497</th>\n", "      <td>498</td>\n", "      <td>26</td>\n", "      <td>M</td>\n", "      <td>writer</td>\n", "      <td>55408</td>\n", "    </tr>\n", "    <tr>\n", "      <th>389</th>\n", "      <td>390</td>\n", "      <td>42</td>\n", "      <td>F</td>\n", "      <td>writer</td>\n", "      <td>85016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>832</th>\n", "      <td>833</td>\n", "      <td>34</td>\n", "      <td>M</td>\n", "      <td>writer</td>\n", "      <td>90019</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>943 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-4c25447f-1f8f-4643-8344-dd37d60790dc')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-4c25447f-1f8f-4643-8344-dd37d60790dc button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-4c25447f-1f8f-4643-8344-dd37d60790dc');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 64}], "source": ["# Sort the dataframe based on the `age` column with inplace=True\n", "occupation_df.sort_values(by=\"occupation\", inplace=True)\n", "occupation_df"]}, {"cell_type": "markdown", "metadata": {"id": "NExo3xv7Zg05"}, "source": ["#### 3. Sort the dataframe - III"]}, {"cell_type": "markdown", "metadata": {"id": "uBuuQJDLZg05"}, "source": ["Sort the dataframe based on both `age` and `gender` columns with `inplace=True`. Let's sort the dataframe based on both `age` and `gender` columns."]}, {"cell_type": "code", "execution_count": 65, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "NliegmxuZg05", "executionInfo": {"status": "ok", "timestamp": 1650368812178, "user_tz": -330, "elapsed": 595, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "ba955229-84cb-427d-da99-f3e567153411"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender     occupation zip_code\n", "29        30    7      M        student    55436\n", "470      471   10      M        student    77459\n", "288      289   11      M           none    94619\n", "608      609   13      F        student    55106\n", "673      674   13      F        student    55337\n", "..       ...  ...    ...            ...      ...\n", "584      585   69      M      librarian    98501\n", "859      860   70      F        retired    48322\n", "802      803   70      M  administrator    78212\n", "766      767   70      M       engineer    00000\n", "480      481   73      M        retired    37771\n", "\n", "[943 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-0b1c5806-50e8-43d2-bbd2-ad52921ef1e0\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>30</td>\n", "      <td>7</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>55436</td>\n", "    </tr>\n", "    <tr>\n", "      <th>470</th>\n", "      <td>471</td>\n", "      <td>10</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>77459</td>\n", "    </tr>\n", "    <tr>\n", "      <th>288</th>\n", "      <td>289</td>\n", "      <td>11</td>\n", "      <td>M</td>\n", "      <td>none</td>\n", "      <td>94619</td>\n", "    </tr>\n", "    <tr>\n", "      <th>608</th>\n", "      <td>609</td>\n", "      <td>13</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>55106</td>\n", "    </tr>\n", "    <tr>\n", "      <th>673</th>\n", "      <td>674</td>\n", "      <td>13</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>55337</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>584</th>\n", "      <td>585</td>\n", "      <td>69</td>\n", "      <td>M</td>\n", "      <td>librarian</td>\n", "      <td>98501</td>\n", "    </tr>\n", "    <tr>\n", "      <th>859</th>\n", "      <td>860</td>\n", "      <td>70</td>\n", "      <td>F</td>\n", "      <td>retired</td>\n", "      <td>48322</td>\n", "    </tr>\n", "    <tr>\n", "      <th>802</th>\n", "      <td>803</td>\n", "      <td>70</td>\n", "      <td>M</td>\n", "      <td>administrator</td>\n", "      <td>78212</td>\n", "    </tr>\n", "    <tr>\n", "      <th>766</th>\n", "      <td>767</td>\n", "      <td>70</td>\n", "      <td>M</td>\n", "      <td>engineer</td>\n", "      <td>00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>480</th>\n", "      <td>481</td>\n", "      <td>73</td>\n", "      <td>M</td>\n", "      <td>retired</td>\n", "      <td>37771</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>943 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-0b1c5806-50e8-43d2-bbd2-ad52921ef1e0')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-0b1c5806-50e8-43d2-bbd2-ad52921ef1e0 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-0b1c5806-50e8-43d2-bbd2-ad52921ef1e0');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 65}], "source": ["# Sort the dataframe based on the `age` and  'gender' column with inplace=False\n", "occupation_df.sort_values(by=[\"age\", \"gender\"], ascending=True, inplace=False)"]}, {"cell_type": "markdown", "metadata": {"id": "LVT2TfKKZg05"}, "source": ["If we observe the result, the entire dataframe has been sorted according to ascending order depending on the `age` and `gender` columns."]}, {"cell_type": "markdown", "metadata": {"id": "Bi2jj4oAZg05"}, "source": ["#### 4. <PERSON><PERSON> the Dataframe - I "]}, {"cell_type": "markdown", "metadata": {"id": "GdMCtbq-Zg05"}, "source": ["Select separate male and female students list. We can filter the dataframes by using the subscript method and matching the required conditions. "]}, {"cell_type": "code", "execution_count": 66, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "cv1KNJXeZg05", "executionInfo": {"status": "ok", "timestamp": 1650368814746, "user_tz": -330, "elapsed": 613, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "66c8ba15-b8db-44ec-87c8-c82d40368ae2"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender occupation zip_code\n", "938      939   26      F    student    33319\n", "786      787   18      F    student    98620\n", "886      887   14      F    student    27249\n", "812      813   14      F    student    02136\n", "848      849   15      F    student    25652"], "text/html": ["\n", "  <div id=\"df-ee0a5ee3-6588-42e7-b85a-cb48174f5b7d\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>938</th>\n", "      <td>939</td>\n", "      <td>26</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>33319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>786</th>\n", "      <td>787</td>\n", "      <td>18</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>98620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>887</td>\n", "      <td>14</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>27249</td>\n", "    </tr>\n", "    <tr>\n", "      <th>812</th>\n", "      <td>813</td>\n", "      <td>14</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>02136</td>\n", "    </tr>\n", "    <tr>\n", "      <th>848</th>\n", "      <td>849</td>\n", "      <td>15</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>25652</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-ee0a5ee3-6588-42e7-b85a-cb48174f5b7d')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-ee0a5ee3-6588-42e7-b85a-cb48174f5b7d button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-ee0a5ee3-6588-42e7-b85a-cb48174f5b7d');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 66}], "source": ["# Select rows that has only gender=F and occupation=student\n", "female_students = occupation_df[(occupation_df[\"gender\"] == 'F') & (occupation_df[\"occupation\"] == 'student')]\n", "female_students.head()"]}, {"cell_type": "code", "execution_count": 67, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "HYbbc_LAZg06", "executionInfo": {"status": "ok", "timestamp": 1650368816343, "user_tz": -330, "elapsed": 4, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "18d0f2a8-959f-44da-8bb0-02cd8d901356"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender occupation zip_code\n", "724      725   21      M    student    91711\n", "940      941   20      M    student    97229\n", "728      729   19      M    student    56567\n", "726      727   25      M    student    78741\n", "868      869   30      M    student    10025"], "text/html": ["\n", "  <div id=\"df-7c3a2a4c-321f-491f-8e29-c68bb286115e\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>724</th>\n", "      <td>725</td>\n", "      <td>21</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>91711</td>\n", "    </tr>\n", "    <tr>\n", "      <th>940</th>\n", "      <td>941</td>\n", "      <td>20</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>97229</td>\n", "    </tr>\n", "    <tr>\n", "      <th>728</th>\n", "      <td>729</td>\n", "      <td>19</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>56567</td>\n", "    </tr>\n", "    <tr>\n", "      <th>726</th>\n", "      <td>727</td>\n", "      <td>25</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>78741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>868</th>\n", "      <td>869</td>\n", "      <td>30</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>10025</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-7c3a2a4c-321f-491f-8e29-c68bb286115e')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-7c3a2a4c-321f-491f-8e29-c68bb286115e button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-7c3a2a4c-321f-491f-8e29-c68bb286115e');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 67}], "source": ["# Select rows that has only gender=< and occupation=student\n", "male_students = occupation_df[(occupation_df[\"gender\"] == 'M') & (occupation_df[\"occupation\"] == 'student')]\n", "male_students.head()"]}, {"cell_type": "markdown", "metadata": {"id": "9fhMiZRNZg06"}, "source": ["In the above example, we have used `&` to select rows that satisfy both the conditions. Now let's see how we can filter the dataframe if only one condition is met."]}, {"cell_type": "markdown", "metadata": {"id": "JF2gWloyZg06"}, "source": ["#### 5. <PERSON><PERSON> the Dataframe - II"]}, {"cell_type": "markdown", "metadata": {"id": "pLQXYv7HZg06"}, "source": ["Select students with age between 18 and 22 or engineer with age between 25 to 30."]}, {"cell_type": "code", "execution_count": 68, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "gqnjWziDZg06", "executionInfo": {"status": "ok", "timestamp": 1650368819204, "user_tz": -330, "elapsed": 468, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "025ed83a-02ea-4f4a-f751-6e0dce345e3c"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender occupation zip_code\n", "246      247   28      M   engineer    20770\n", "539      540   28      M   engineer    91201\n", "915      916   27      M   engineer    N2L5N\n", "69        70   27      M   engineer    60067\n", "745      746   25      M   engineer    19047\n", "..       ...  ...    ...        ...      ...\n", "533      534   20      M    student    05464\n", "67        68   19      M    student    22904\n", "75        76   20      M    student    02215\n", "541      542   21      M    student    60515\n", "496      497   20      M    student    50112\n", "\n", "[70 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-cf130915-cf5d-40ae-899d-3c97d8017a0c\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>246</th>\n", "      <td>247</td>\n", "      <td>28</td>\n", "      <td>M</td>\n", "      <td>engineer</td>\n", "      <td>20770</td>\n", "    </tr>\n", "    <tr>\n", "      <th>539</th>\n", "      <td>540</td>\n", "      <td>28</td>\n", "      <td>M</td>\n", "      <td>engineer</td>\n", "      <td>91201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>915</th>\n", "      <td>916</td>\n", "      <td>27</td>\n", "      <td>M</td>\n", "      <td>engineer</td>\n", "      <td>N2L5N</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>70</td>\n", "      <td>27</td>\n", "      <td>M</td>\n", "      <td>engineer</td>\n", "      <td>60067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>745</th>\n", "      <td>746</td>\n", "      <td>25</td>\n", "      <td>M</td>\n", "      <td>engineer</td>\n", "      <td>19047</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>533</th>\n", "      <td>534</td>\n", "      <td>20</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>05464</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>68</td>\n", "      <td>19</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>22904</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>76</td>\n", "      <td>20</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>02215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541</th>\n", "      <td>542</td>\n", "      <td>21</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>60515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>496</th>\n", "      <td>497</td>\n", "      <td>20</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>50112</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>70 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-cf130915-cf5d-40ae-899d-3c97d8017a0c')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-cf130915-cf5d-40ae-899d-3c97d8017a0c button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-cf130915-cf5d-40ae-899d-3c97d8017a0c');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 68}], "source": ["# Filter the dataframes using the `or` condition\n", "occupation_df[ (((occupation_df[\"age\"] > 18) & (occupation_df[\"age\"] < 22)) &  (occupation_df[\"occupation\"] == 'student')) | \n", "               (((occupation_df[\"age\"] >= 25) & (occupation_df[\"age\"] < 30)) & (occupation_df[\"occupation\"] == 'engineer')) ]"]}, {"cell_type": "markdown", "metadata": {"id": "W3cSvfw1Zg06"}, "source": ["#### 6. Shuffle/Sample the Dataframe "]}, {"cell_type": "markdown", "metadata": {"id": "HjAJhHquZg06"}, "source": ["Let's sample the dataframe i.e. randomly select some samples from the dataset. We can use the `sample()` method to do this. This can also be used to shuffle the dataframe."]}, {"cell_type": "code", "execution_count": 69, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "jh42imSAZg07", "executionInfo": {"status": "ok", "timestamp": 1650368822440, "user_tz": -330, "elapsed": 617, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "5871e19d-6734-4b24-c7b2-283dd258a9fa"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender occupation zip_code\n", "485      486   39      M   educator    93101\n", "915      916   27      M   engineer    N2L5N\n", "803      804   39      M   educator    61820\n", "170      171   48      F   educator    78750\n", "917      918   40      M  scientist    70116\n", "..       ...  ...    ...        ...      ...\n", "902      903   28      M   educator    20850\n", "149      150   20      F     artist    02139\n", "334      335   45      M  executive    33775\n", "823      824   31      M      other    15017\n", "701      702   37      M      other    89104\n", "\n", "[100 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-2c5160c4-f906-4042-b90d-819896df4cb0\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>485</th>\n", "      <td>486</td>\n", "      <td>39</td>\n", "      <td>M</td>\n", "      <td>educator</td>\n", "      <td>93101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>915</th>\n", "      <td>916</td>\n", "      <td>27</td>\n", "      <td>M</td>\n", "      <td>engineer</td>\n", "      <td>N2L5N</td>\n", "    </tr>\n", "    <tr>\n", "      <th>803</th>\n", "      <td>804</td>\n", "      <td>39</td>\n", "      <td>M</td>\n", "      <td>educator</td>\n", "      <td>61820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>171</td>\n", "      <td>48</td>\n", "      <td>F</td>\n", "      <td>educator</td>\n", "      <td>78750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>917</th>\n", "      <td>918</td>\n", "      <td>40</td>\n", "      <td>M</td>\n", "      <td>scientist</td>\n", "      <td>70116</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>902</th>\n", "      <td>903</td>\n", "      <td>28</td>\n", "      <td>M</td>\n", "      <td>educator</td>\n", "      <td>20850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>150</td>\n", "      <td>20</td>\n", "      <td>F</td>\n", "      <td>artist</td>\n", "      <td>02139</td>\n", "    </tr>\n", "    <tr>\n", "      <th>334</th>\n", "      <td>335</td>\n", "      <td>45</td>\n", "      <td>M</td>\n", "      <td>executive</td>\n", "      <td>33775</td>\n", "    </tr>\n", "    <tr>\n", "      <th>823</th>\n", "      <td>824</td>\n", "      <td>31</td>\n", "      <td>M</td>\n", "      <td>other</td>\n", "      <td>15017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>701</th>\n", "      <td>702</td>\n", "      <td>37</td>\n", "      <td>M</td>\n", "      <td>other</td>\n", "      <td>89104</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>100 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-2c5160c4-f906-4042-b90d-819896df4cb0')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-2c5160c4-f906-4042-b90d-819896df4cb0 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-2c5160c4-f906-4042-b90d-819896df4cb0');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 69}], "source": ["# Select 100 samples randomly from the dataframe\n", "occupation_df.sample(n=100, random_state=100)"]}, {"cell_type": "code", "execution_count": 70, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "xIoR54YWZg07", "executionInfo": {"status": "ok", "timestamp": 1650368825529, "user_tz": -330, "elapsed": 7, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "3829385b-54b4-4319-fdcc-85c7efdaa64b"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender  occupation zip_code\n", "485      486   39      M    educator    93101\n", "915      916   27      M    engineer    N2L5N\n", "803      804   39      M    educator    61820\n", "170      171   48      F    educator    78750\n", "917      918   40      M   scientist    70116\n", "..       ...  ...    ...         ...      ...\n", "540      541   19      F     student    84302\n", "3          4   24      M  technician    43537\n", "585      586   20      M     student    79508\n", "229      230   28      F     student    14476\n", "311      312   48      M       other    02110\n", "\n", "[943 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-290b41e8-a006-4208-a15c-9d5ae5d24791\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>485</th>\n", "      <td>486</td>\n", "      <td>39</td>\n", "      <td>M</td>\n", "      <td>educator</td>\n", "      <td>93101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>915</th>\n", "      <td>916</td>\n", "      <td>27</td>\n", "      <td>M</td>\n", "      <td>engineer</td>\n", "      <td>N2L5N</td>\n", "    </tr>\n", "    <tr>\n", "      <th>803</th>\n", "      <td>804</td>\n", "      <td>39</td>\n", "      <td>M</td>\n", "      <td>educator</td>\n", "      <td>61820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>171</td>\n", "      <td>48</td>\n", "      <td>F</td>\n", "      <td>educator</td>\n", "      <td>78750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>917</th>\n", "      <td>918</td>\n", "      <td>40</td>\n", "      <td>M</td>\n", "      <td>scientist</td>\n", "      <td>70116</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>540</th>\n", "      <td>541</td>\n", "      <td>19</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>84302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>24</td>\n", "      <td>M</td>\n", "      <td>technician</td>\n", "      <td>43537</td>\n", "    </tr>\n", "    <tr>\n", "      <th>585</th>\n", "      <td>586</td>\n", "      <td>20</td>\n", "      <td>M</td>\n", "      <td>student</td>\n", "      <td>79508</td>\n", "    </tr>\n", "    <tr>\n", "      <th>229</th>\n", "      <td>230</td>\n", "      <td>28</td>\n", "      <td>F</td>\n", "      <td>student</td>\n", "      <td>14476</td>\n", "    </tr>\n", "    <tr>\n", "      <th>311</th>\n", "      <td>312</td>\n", "      <td>48</td>\n", "      <td>M</td>\n", "      <td>other</td>\n", "      <td>02110</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>943 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-290b41e8-a006-4208-a15c-9d5ae5d24791')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-290b41e8-a006-4208-a15c-9d5ae5d24791 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-290b41e8-a006-4208-a15c-9d5ae5d24791');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 70}], "source": ["# Shuffle the entire dataframe using frac=1\n", "occupation_df.sample(frac=1, random_state=100)"]}, {"cell_type": "code", "execution_count": 71, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "af2WERWbZg07", "executionInfo": {"status": "ok", "timestamp": 1650368828528, "user_tz": -330, "elapsed": 454, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "77fb3938-4104-40cc-fdc6-138ce66548ed"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     user_id  age gender occupation zip_code\n", "0        486   39      M   educator    93101\n", "1        916   27      M   engineer    N2L5N\n", "2        804   39      M   educator    61820\n", "3        171   48      F   educator    78750\n", "4        918   40      M  scientist    70116\n", "..       ...  ...    ...        ...      ...\n", "467       54   22      M  executive    66315\n", "468       27   40      F  librarian    30030\n", "469      764   27      F   educator    62903\n", "470      840   39      M     artist    55406\n", "471      932   58      M   educator    06437\n", "\n", "[472 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-c3a18d6e-0a16-400e-9207-40619246f787\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>occupation</th>\n", "      <th>zip_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>486</td>\n", "      <td>39</td>\n", "      <td>M</td>\n", "      <td>educator</td>\n", "      <td>93101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>916</td>\n", "      <td>27</td>\n", "      <td>M</td>\n", "      <td>engineer</td>\n", "      <td>N2L5N</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>804</td>\n", "      <td>39</td>\n", "      <td>M</td>\n", "      <td>educator</td>\n", "      <td>61820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>171</td>\n", "      <td>48</td>\n", "      <td>F</td>\n", "      <td>educator</td>\n", "      <td>78750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>918</td>\n", "      <td>40</td>\n", "      <td>M</td>\n", "      <td>scientist</td>\n", "      <td>70116</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>467</th>\n", "      <td>54</td>\n", "      <td>22</td>\n", "      <td>M</td>\n", "      <td>executive</td>\n", "      <td>66315</td>\n", "    </tr>\n", "    <tr>\n", "      <th>468</th>\n", "      <td>27</td>\n", "      <td>40</td>\n", "      <td>F</td>\n", "      <td>librarian</td>\n", "      <td>30030</td>\n", "    </tr>\n", "    <tr>\n", "      <th>469</th>\n", "      <td>764</td>\n", "      <td>27</td>\n", "      <td>F</td>\n", "      <td>educator</td>\n", "      <td>62903</td>\n", "    </tr>\n", "    <tr>\n", "      <th>470</th>\n", "      <td>840</td>\n", "      <td>39</td>\n", "      <td>M</td>\n", "      <td>artist</td>\n", "      <td>55406</td>\n", "    </tr>\n", "    <tr>\n", "      <th>471</th>\n", "      <td>932</td>\n", "      <td>58</td>\n", "      <td>M</td>\n", "      <td>educator</td>\n", "      <td>06437</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>472 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-c3a18d6e-0a16-400e-9207-40619246f787')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-c3a18d6e-0a16-400e-9207-40619246f787 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-c3a18d6e-0a16-400e-9207-40619246f787');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 71}], "source": ["# Select 50% of the samples randomly from the dataframe and reset the index to start from 0\n", "occupation_df.sample(frac=0.5, random_state=100).reset_index(drop=True)"]}, {"cell_type": "markdown", "metadata": {"id": "0DNgy-I8Zg07"}, "source": ["Now we have some basic idea about how to manipulate and select data in the dataframe. Now let's check how to clean the dataframe."]}, {"cell_type": "markdown", "metadata": {"id": "b5TX99oUZg07"}, "source": ["## Cleaning Data"]}, {"cell_type": "markdown", "metadata": {"id": "W3QXFBelZg07"}, "source": ["Cleaning data includes handling null values and missing values. Let's see how we can handle null values in the dataframe. For this section, we will us the chipotle_df dataframe."]}, {"cell_type": "code", "execution_count": 72, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "r8XF4abkZg08", "executionInfo": {"status": "ok", "timestamp": 1650368831351, "user_tz": -330, "elapsed": 382, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "6f670475-8541-4589-e564-26b3679f437c"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      order_id  quantity                              item_name  \\\n", "0            1         1           Chips and Fresh Tomato Salsa   \n", "1            1         1                                   Izze   \n", "2            1         1                       Nantucket Nectar   \n", "3            1         1  Chips and Tomatillo-Green Chili Salsa   \n", "4            2         2                           Chicken Bowl   \n", "...        ...       ...                                    ...   \n", "4617      1833         1                          Steak Burrito   \n", "4618      1833         1                          Steak Burrito   \n", "4619      1834         1                     Chicken Salad Bowl   \n", "4620      1834         1                     Chicken Salad Bowl   \n", "4621      1834         1                     Chicken Salad Bowl   \n", "\n", "                                     choice_description item_price  \n", "0                                                   NaN     $2.39   \n", "1                                          [<PERSON><PERSON>]     $3.39   \n", "2                                               [Apple]     $3.39   \n", "3                                                   NaN     $2.39   \n", "4     [Tomatillo-<PERSON> (Hot), [Black Beans...    $16.98   \n", "...                                                 ...        ...  \n", "4617  [<PERSON>, [<PERSON>, <PERSON>s, Sour ...    $11.75   \n", "4618  [Fresh Tomato Sal<PERSON>, [Rice, Sour Cream, Cheese...    $11.75   \n", "4619  [<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...    $11.25   \n", "4620  [<PERSON>, [<PERSON><PERSON><PERSON>, Lettu...     $8.75   \n", "4621  [<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...     $8.75   \n", "\n", "[4622 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-a2fc2da2-2a5c-4733-961e-52b18ec07a93\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>quantity</th>\n", "      <th>item_name</th>\n", "      <th>choice_description</th>\n", "      <th>item_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON>s and Fresh <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Izze</td>\n", "      <td>[<PERSON><PERSON>]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Nantucket Nectar</td>\n", "      <td>[Apple]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Chips and Tomatillo-Green <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>Chicken Bowl</td>\n", "      <td>[Tomatillo<PERSON><PERSON> (Hot), [Black Beans...</td>\n", "      <td>$16.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4617</th>\n", "      <td>1833</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[<PERSON>, [<PERSON>, Black Beans, Sour ...</td>\n", "      <td>$11.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4618</th>\n", "      <td>1833</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[Fresh Tomato Salsa, [Rice, Sour Cream, Cheese...</td>\n", "      <td>$11.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4619</th>\n", "      <td>1834</td>\n", "      <td>1</td>\n", "      <td>Chicken Salad Bowl</td>\n", "      <td>[<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...</td>\n", "      <td>$11.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4620</th>\n", "      <td>1834</td>\n", "      <td>1</td>\n", "      <td>Chicken Salad Bowl</td>\n", "      <td>[<PERSON>, [<PERSON><PERSON><PERSON>, <PERSON><PERSON>...</td>\n", "      <td>$8.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4621</th>\n", "      <td>1834</td>\n", "      <td>1</td>\n", "      <td>Chicken Salad Bowl</td>\n", "      <td>[<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...</td>\n", "      <td>$8.75</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4622 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-a2fc2da2-2a5c-4733-961e-52b18ec07a93')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-a2fc2da2-2a5c-4733-961e-52b18ec07a93 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-a2fc2da2-2a5c-4733-961e-52b18ec07a93');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 72}], "source": ["# Get the chiptotle_df dataframe\n", "chiptotle_df = pd.read_csv('https://raw.githubusercontent.com/justmarkham/DAT8/master/data/chipotle.tsv', sep=\"\\t\")\n", "chiptotle_df"]}, {"cell_type": "markdown", "metadata": {"id": "J3Kww3RCZg08"}, "source": ["We can clean the dataframe in the following steps:"]}, {"cell_type": "markdown", "metadata": {"id": "9QLVH8TcZg08"}, "source": ["### 1. Check for null values "]}, {"cell_type": "markdown", "metadata": {"id": "p30V4vjkZg08"}, "source": ["We should check for null values in the dataframe before doing any manipulations. This is becasue we can't perform any manipulations on the dataframe if there are null values and it causes errors."]}, {"cell_type": "code", "execution_count": 73, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Gpyq8gPQZg08", "executionInfo": {"status": "ok", "timestamp": 1650368834429, "user_tz": -330, "elapsed": 437, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "37ce5b49-b7d5-42d0-eba5-f8b94e73e392"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["order_id                 0\n", "quantity                 0\n", "item_name                0\n", "choice_description    1246\n", "item_price               0\n", "dtype: int64"]}, "metadata": {}, "execution_count": 73}], "source": ["# Find the total number of null values\n", "chiptotle_df.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {"id": "aR9AYa-HZg08"}, "source": ["From the above example, we can see that there are 1246 null values in the `choice_description` column. Let's check the dataframe using the `isnull()` method."]}, {"cell_type": "code", "execution_count": 74, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "NsjBRQpCZg08", "executionInfo": {"status": "ok", "timestamp": 1650368837139, "user_tz": -330, "elapsed": 6, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "03e4ac2f-5fc1-4a73-9e8c-6daf888038c0"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      order_id  quantity                              item_name  \\\n", "0            1         1           Chips and Fresh Tomato Salsa   \n", "3            1         1  Chips and Tomatillo-Green Chili Salsa   \n", "6            3         1                          Side of Chips   \n", "10           5         1                    Chips and Guacamole   \n", "14           7         1                    Chips and Guacamole   \n", "...        ...       ...                                    ...   \n", "4600      1827         1                    Chips and Guacamole   \n", "4605      1828         1                    Chips and Guacamole   \n", "4613      1831         1                                  Chips   \n", "4614      1831         1                          Bottled Water   \n", "4616      1832         1                    Chips and Guacamole   \n", "\n", "     choice_description item_price  \n", "0                   NaN     $2.39   \n", "3                   NaN     $2.39   \n", "6                   NaN     $1.69   \n", "10                  NaN     $4.45   \n", "14                  NaN     $4.45   \n", "...                 ...        ...  \n", "4600                NaN     $4.45   \n", "4605                NaN     $4.45   \n", "4613                NaN     $2.15   \n", "4614                NaN     $1.50   \n", "4616                NaN     $4.45   \n", "\n", "[1246 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-c8345848-7f53-4037-a896-fc7f385abd07\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>quantity</th>\n", "      <th>item_name</th>\n", "      <th>choice_description</th>\n", "      <th>item_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON>s and Fresh <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Chips and Tomatillo-Green <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Side of Chips</td>\n", "      <td>NaN</td>\n", "      <td>$1.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>Chips and Guacamole</td>\n", "      <td>NaN</td>\n", "      <td>$4.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>Chips and Guacamole</td>\n", "      <td>NaN</td>\n", "      <td>$4.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4600</th>\n", "      <td>1827</td>\n", "      <td>1</td>\n", "      <td>Chips and Guacamole</td>\n", "      <td>NaN</td>\n", "      <td>$4.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4605</th>\n", "      <td>1828</td>\n", "      <td>1</td>\n", "      <td>Chips and Guacamole</td>\n", "      <td>NaN</td>\n", "      <td>$4.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4613</th>\n", "      <td>1831</td>\n", "      <td>1</td>\n", "      <td>Chips</td>\n", "      <td>NaN</td>\n", "      <td>$2.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4614</th>\n", "      <td>1831</td>\n", "      <td>1</td>\n", "      <td>Bottled Water</td>\n", "      <td>NaN</td>\n", "      <td>$1.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4616</th>\n", "      <td>1832</td>\n", "      <td>1</td>\n", "      <td>Chips and Guacamole</td>\n", "      <td>NaN</td>\n", "      <td>$4.45</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1246 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-c8345848-7f53-4037-a896-fc7f385abd07')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-c8345848-7f53-4037-a896-fc7f385abd07 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-c8345848-7f53-4037-a896-fc7f385abd07');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 74}], "source": ["# Display the rows with null values \n", "chiptotle_df[chiptotle_df.isnull().any(axis=1)]"]}, {"cell_type": "markdown", "metadata": {"id": "68M-HnGgZg09"}, "source": ["### 2. Drop the rows with null values"]}, {"cell_type": "markdown", "metadata": {"id": "NDGK5n4UZg09"}, "source": ["Since we have `NaN` values in the `choice_description` column, we can't perform any manipulations on the dataframe. Let's see how we can handle the null values. We can do the following: \n", "* Drop the rows with null values.\n", "* Replace the null values with a string.\n", "\n", "Since the column `choice_description` is a string, we can drop all the rows with null values."]}, {"cell_type": "code", "execution_count": 75, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "qOIgqQQVZg09", "executionInfo": {"status": "ok", "timestamp": 1650368840840, "user_tz": -330, "elapsed": 517, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "92f6e60c-4bef-45bb-eb27-66347869b86e"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      order_id  quantity           item_name  \\\n", "1            1         1                Izze   \n", "2            1         1    Nantucket Nectar   \n", "4            2         2        Chicken Bowl   \n", "5            3         1        Chicken Bowl   \n", "7            4         1       Steak Burrito   \n", "...        ...       ...                 ...   \n", "4617      1833         1       Steak Burrito   \n", "4618      1833         1       Steak Burrito   \n", "4619      1834         1  Chicken Salad Bowl   \n", "4620      1834         1  Chicken Salad Bowl   \n", "4621      1834         1  Chicken Salad Bowl   \n", "\n", "                                     choice_description item_price  \n", "1                                          [<PERSON><PERSON>]     $3.39   \n", "2                                               [Apple]     $3.39   \n", "4     [Tomatillo-<PERSON> (Hot), [Black Beans...    $16.98   \n", "5     [<PERSON> (Mild), [Rice, Cheese, Sou...    $10.98   \n", "7     [Tomatillo Red Chili Salsa, [Fajita Vegetables...    $11.75   \n", "...                                                 ...        ...  \n", "4617  [<PERSON>, [<PERSON>, <PERSON>s, Sour ...    $11.75   \n", "4618  [Fresh Tomato Sal<PERSON>, [Rice, Sour Cream, Cheese...    $11.75   \n", "4619  [<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...    $11.25   \n", "4620  [<PERSON>, [<PERSON><PERSON><PERSON>, Lettu...     $8.75   \n", "4621  [<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...     $8.75   \n", "\n", "[3376 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-04f5d7ee-1c4a-4421-bf2e-f005702ef082\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>quantity</th>\n", "      <th>item_name</th>\n", "      <th>choice_description</th>\n", "      <th>item_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Izze</td>\n", "      <td>[<PERSON><PERSON>]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Nantucket Nectar</td>\n", "      <td>[Apple]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>Chicken Bowl</td>\n", "      <td>[Tomatillo<PERSON><PERSON> (Hot), [Black Beans...</td>\n", "      <td>$16.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Chicken Bowl</td>\n", "      <td>[<PERSON> (Mild), [Rice, Cheese, Sou...</td>\n", "      <td>$10.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[Tomatillo Red Chili Salsa, [Fajita Vegetables...</td>\n", "      <td>$11.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4617</th>\n", "      <td>1833</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[<PERSON>, [<PERSON>, Black Beans, Sour ...</td>\n", "      <td>$11.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4618</th>\n", "      <td>1833</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[Fresh Tomato Salsa, [Rice, Sour Cream, Cheese...</td>\n", "      <td>$11.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4619</th>\n", "      <td>1834</td>\n", "      <td>1</td>\n", "      <td>Chicken Salad Bowl</td>\n", "      <td>[<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...</td>\n", "      <td>$11.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4620</th>\n", "      <td>1834</td>\n", "      <td>1</td>\n", "      <td>Chicken Salad Bowl</td>\n", "      <td>[<PERSON>, [<PERSON><PERSON><PERSON>, <PERSON><PERSON>...</td>\n", "      <td>$8.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4621</th>\n", "      <td>1834</td>\n", "      <td>1</td>\n", "      <td>Chicken Salad Bowl</td>\n", "      <td>[<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...</td>\n", "      <td>$8.75</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3376 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-04f5d7ee-1c4a-4421-bf2e-f005702ef082')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-04f5d7ee-1c4a-4421-bf2e-f005702ef082 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-04f5d7ee-1c4a-4421-bf2e-f005702ef082');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 75}], "source": ["# Drop the null values in the chipotle_df\n", "chiptotle_df.dropna()"]}, {"cell_type": "markdown", "metadata": {"id": "Vzwoi7iyZg09"}, "source": ["From the above code, we can drop the rows with null values using the `dropna()` method. But if we want to explicitly apply the `dropna()` to specific column, we can specify it in the subset."]}, {"cell_type": "code", "execution_count": 76, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "CfL30iThZg09", "executionInfo": {"status": "ok", "timestamp": 1650368846464, "user_tz": -330, "elapsed": 444, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "c540e247-694a-477a-d880-78c8568e89ce"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      order_id  quantity           item_name  \\\n", "1            1         1                Izze   \n", "2            1         1    Nantucket Nectar   \n", "4            2         2        Chicken Bowl   \n", "5            3         1        Chicken Bowl   \n", "7            4         1       Steak Burrito   \n", "...        ...       ...                 ...   \n", "4617      1833         1       Steak Burrito   \n", "4618      1833         1       Steak Burrito   \n", "4619      1834         1  Chicken Salad Bowl   \n", "4620      1834         1  Chicken Salad Bowl   \n", "4621      1834         1  Chicken Salad Bowl   \n", "\n", "                                     choice_description item_price  \n", "1                                          [<PERSON><PERSON>]     $3.39   \n", "2                                               [Apple]     $3.39   \n", "4     [Tomatillo-<PERSON> (Hot), [Black Beans...    $16.98   \n", "5     [<PERSON> (Mild), [Rice, Cheese, Sou...    $10.98   \n", "7     [Tomatillo Red Chili Salsa, [Fajita Vegetables...    $11.75   \n", "...                                                 ...        ...  \n", "4617  [<PERSON>, [<PERSON>, <PERSON>s, Sour ...    $11.75   \n", "4618  [Fresh Tomato Sal<PERSON>, [Rice, Sour Cream, Cheese...    $11.75   \n", "4619  [<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...    $11.25   \n", "4620  [<PERSON>, [<PERSON><PERSON><PERSON>, Lettu...     $8.75   \n", "4621  [<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...     $8.75   \n", "\n", "[3376 rows x 5 columns]"], "text/html": ["\n", "  <div id=\"df-8bc3fd96-bb90-4297-9d70-2b3e76050603\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>quantity</th>\n", "      <th>item_name</th>\n", "      <th>choice_description</th>\n", "      <th>item_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Izze</td>\n", "      <td>[<PERSON><PERSON>]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Nantucket Nectar</td>\n", "      <td>[Apple]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>Chicken Bowl</td>\n", "      <td>[Tomatillo<PERSON><PERSON> (Hot), [Black Beans...</td>\n", "      <td>$16.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Chicken Bowl</td>\n", "      <td>[<PERSON> (Mild), [Rice, Cheese, Sou...</td>\n", "      <td>$10.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[Tomatillo Red Chili Salsa, [Fajita Vegetables...</td>\n", "      <td>$11.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4617</th>\n", "      <td>1833</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[<PERSON>, [<PERSON>, Black Beans, Sour ...</td>\n", "      <td>$11.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4618</th>\n", "      <td>1833</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[Fresh Tomato Salsa, [Rice, Sour Cream, Cheese...</td>\n", "      <td>$11.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4619</th>\n", "      <td>1834</td>\n", "      <td>1</td>\n", "      <td>Chicken Salad Bowl</td>\n", "      <td>[<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...</td>\n", "      <td>$11.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4620</th>\n", "      <td>1834</td>\n", "      <td>1</td>\n", "      <td>Chicken Salad Bowl</td>\n", "      <td>[<PERSON>, [<PERSON><PERSON><PERSON>, <PERSON><PERSON>...</td>\n", "      <td>$8.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4621</th>\n", "      <td>1834</td>\n", "      <td>1</td>\n", "      <td>Chicken Salad Bowl</td>\n", "      <td>[<PERSON>, [<PERSON><PERSON><PERSON>, Pinto...</td>\n", "      <td>$8.75</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3376 rows × 5 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-8bc3fd96-bb90-4297-9d70-2b3e76050603')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-8bc3fd96-bb90-4297-9d70-2b3e76050603 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-8bc3fd96-bb90-4297-9d70-2b3e76050603');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 76}], "source": ["# Drop the null values present in the choice_description column \n", "chiptotle_df.dropna(subset=[\"choice_description\"], inplace=True)\n", "chiptotle_df"]}, {"cell_type": "markdown", "metadata": {"id": "LiCwUnCHZg0-"}, "source": ["We can see that the length of the dataframe has reduced i.e. from 4622 to 3376. Because we have dropped the entire row even if there is only one null value."]}, {"cell_type": "markdown", "metadata": {"id": "8GXVwrj1Zg0-"}, "source": ["### 3. Replace the null values with a string"]}, {"cell_type": "markdown", "metadata": {"id": "5PFXaBmPZg0-"}, "source": ["But what if we don't want to drop the dataframe with null values? We can use the `fillna()` method. When we use the `fillna()` method, we can specify the value to be filled in null values."]}, {"cell_type": "code", "execution_count": 77, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "j9sM8dRJZg0-", "executionInfo": {"status": "ok", "timestamp": 1650368851145, "user_tz": -330, "elapsed": 674, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "2e69dac5-4218-4c52-cb96-15175b7569f6"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   order_id  quantity                              item_name  \\\n", "0         1         1           Chips and Fresh Tomato Salsa   \n", "1         1         1                                   Izze   \n", "2         1         1                       Nantucket Nectar   \n", "3         1         1  Chips and Tomatillo-Green Chili Salsa   \n", "4         2         2                           Chicken Bowl   \n", "\n", "                                  choice_description item_price  \n", "0                                                NaN     $2.39   \n", "1                                       [<PERSON><PERSON>]     $3.39   \n", "2                                            [Apple]     $3.39   \n", "3                                                NaN     $2.39   \n", "4  [Tomatillo-<PERSON> (Hot), [Black Beans...    $16.98   "], "text/html": ["\n", "  <div id=\"df-a5948b75-3a33-4d0a-938b-dc6f341bb430\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>quantity</th>\n", "      <th>item_name</th>\n", "      <th>choice_description</th>\n", "      <th>item_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON>s and Fresh <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Izze</td>\n", "      <td>[<PERSON><PERSON>]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Nantucket Nectar</td>\n", "      <td>[Apple]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Chips and Tomatillo-Green <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>Chicken Bowl</td>\n", "      <td>[Tomatillo<PERSON><PERSON> (Hot), [Black Beans...</td>\n", "      <td>$16.98</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-a5948b75-3a33-4d0a-938b-dc6f341bb430')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.********** ********** 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-a5948b75-3a33-4d0a-938b-dc6f341bb430 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-a5948b75-3a33-4d0a-938b-dc6f341bb430');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 77}], "source": ["# Get the chipotle_df dataframe\n", "chiptotle_df = pd.read_csv('https://raw.githubusercontent.com/justmarkham/DAT8/master/data/chipotle.tsv', sep=\"\\t\")\n", "chiptotle_df.head()"]}, {"cell_type": "code", "execution_count": 78, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "J-kLbqYVZg0-", "executionInfo": {"status": "ok", "timestamp": 1650368854951, "user_tz": -330, "elapsed": 591, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "3a6f917e-bef8-454c-cd16-48a20f9aeda9"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Total number of rows: 4622\n", "Number of null values in dataframe: 1246\n"]}], "source": ["# Print the number of null values and total number of rows\n", "print(f\"Total number of rows: {chiptotle_df.shape[0]}\")\n", "print(f\"Number of null values in dataframe: {chiptotle_df.isnull().sum().sum()}\")"]}, {"cell_type": "markdown", "metadata": {"id": "mqRO5XP0Zg0-"}, "source": ["To illustrate for this example, let's replace the null values with the string `'No description'`."]}, {"cell_type": "code", "execution_count": 79, "metadata": {"id": "pVap4_FhZg0-", "executionInfo": {"status": "ok", "timestamp": 1650368857885, "user_tz": -330, "elapsed": 358, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}}, "outputs": [], "source": ["# Replace the null values with a string\n", "chiptotle_df.choice_description.fillna(\"No description\", inplace=True)"]}, {"cell_type": "code", "execution_count": 80, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "N_3zteUGZg0_", "executionInfo": {"status": "ok", "timestamp": 1650368858314, "user_tz": -330, "elapsed": 2, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "17669163023994243788"}}, "outputId": "ce2a379d-2f6f-4f9b-d03f-8b3c6eea77d2"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Total number of rows: 4622\n", "Number of null values in dataframe: 0\n"]}], "source": ["# Print the number of null values and total number of rows\n", "print(f\"Total number of rows: {chiptotle_df.shape[0]}\")\n", "print(f\"Number of null values in dataframe: {chiptotle_df.isnull().sum().sum()}\")"]}, {"cell_type": "markdown", "metadata": {"id": "EH6vnoWpZg0_"}, "source": ["Great ! Now we have 0 `NaN` values in the dataframe and the total size is still 4622."]}, {"cell_type": "markdown", "source": ["## Advanced Data Manipulation"], "metadata": {"id": "_KOm-hTWpy-r"}}, {"cell_type": "markdown", "source": ["In this final section, let's explore some advanced manipulation techniques like `merging` and `grouping` dataframes. We will also look at chaining techniques to apply multiple operations/methods at once to dataframes. "], "metadata": {"id": "NInxQ4TIqldP"}}, {"cell_type": "markdown", "source": ["### Merging Dataframes"], "metadata": {"id": "wlhyXyqiqXgz"}}, {"cell_type": "code", "source": [""], "metadata": {"id": "JEPO_N4rqEJl"}, "execution_count": null, "outputs": []}], "metadata": {"colab": {"collapsed_sections": ["BUkHDM1c6JSW", "S1bog53b3SGf", "rvz8K0gi7Gyl", "nCOsyKIk8tew", "S6hzYgGCBQ5Y", "wSNSiz7NBsYi", "fLL0TwEIZg00", "b5TX99oUZg07"], "name": "Pandas_Reference_Notebook.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}}, "nbformat": 4, "nbformat_minor": 0}