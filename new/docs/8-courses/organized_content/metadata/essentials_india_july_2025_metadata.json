{"course_metadata": {"title": "Modern AI Pro Essentials (India) July 2025", "source_file": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1sIGCkjMuvS4v9Ws3_dwOPdQqYxXmwz_w_Essentials_India_July_2025.ipynb", "total_sessions": 8, "total_notebooks": 31, "target_audience": "Executives, Managers, Business Leaders", "duration": "3 days (14+ hours)", "level": "Beginner to Intermediate", "processed_date": "2025-08-07"}, "sessions": [{"session_id": 1, "title": "Foundations of Machine Learning", "duration": "2.5 hours", "focus": "ML fundamentals and AI evolution", "day": "Day 1 (Friday)", "notebooks": [{"title": "Intro Course - ML Foundations", "colab_id": "1mL6nG14-bViWUA-hRBj2CLSBVV7yrRT5", "colab_url": "https://colab.research.google.com/drive/1mL6nG14-bViWUA-hRBj2CLSBVV7yrRT5", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1mL6nG14-bViWUA-hRBj2CLSBVV7yrRT5_Intro_Course.ipynb", "concepts": ["machine_learning", "supervised_learning", "ai_evolution"], "type": "foundational", "estimated_time": "60 minutes", "difficulty": "beginner"}, {"title": "Basic Linear Regression", "colab_id": "1LpynadI_FV7x-ge537WvZm1nios6tCdc", "colab_url": "https://colab.research.google.com/drive/1LpynadI_FV7x-ge537WvZm1nios6tCdc", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1LpynadI_FV7x-ge537WvZm1nios6tCdc_Basic_Linear_Regression.ipynb", "concepts": ["linear_regression", "sklearn", "prediction"], "type": "practical", "estimated_time": "45 minutes", "difficulty": "beginner"}]}, {"session_id": 2, "title": "Train and Deploy Machine Learning Applications", "duration": "2.5 hours", "focus": "Classification algorithms and ML-to-LLM evolution", "day": "Day 1 (Friday)", "notebooks": [{"title": "Mini Project 0: Home Price Prediction", "colab_id": "1CEgw080U0FarmFIwQLBtbusgmyBdYB5f", "colab_url": "https://colab.research.google.com/drive/1CEgw080U0FarmFIwQLBtbusgmyBdYB5f", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1CEgw080U0FarmFIwQLBtbusgmyBdYB5f_Home_Price_Prediction.ipynb", "concepts": ["regression", "real_estate", "prediction"], "type": "mini_project", "estimated_time": "60 minutes", "difficulty": "intermediate"}, {"title": "Mini Project 1: Income Classification with Responsible AI", "colab_id": "1KdOjCip0w5DR3Kiw5IEB3gkJZlzpV4We", "colab_url": "https://colab.research.google.com/drive/1KdOjCip0w5DR3Kiw5IEB3gkJZlzpV4We", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1KdOjCip0w5DR3Kiw5IEB3gkJZlzpV4We_Income_Class_Prediction.ipynb", "concepts": ["classification", "income_prediction", "responsible_ai"], "type": "mini_project", "estimated_time": "75 minutes", "difficulty": "intermediate"}, {"title": "Advanced Home Price Prediction (100% accuracy)", "colab_id": "1ZXhsahmkbItEJGeOCON9mLUh_aSwW47P", "colab_url": "https://colab.research.google.com/drive/1ZXhsahmkbItEJGeOCON9mLUh_aSwW47P", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1ZXhsahmkbItEJGeOCON9mLUh_aSwW47P_Advanced_Home_Prices.ipynb", "concepts": ["advanced_regression", "model_optimization", "accuracy"], "type": "advanced", "estimated_time": "60 minutes", "difficulty": "intermediate"}]}, {"session_id": 3, "title": "0 to <PERSON> in LLM", "duration": "2.25 hours", "focus": "Introduction to Large Language Models using Ollama", "day": "Day 2 (Saturday)", "notebooks": [{"title": "Mini Project 2: First LLM App", "colab_id": "14I39CnLNSZYekRVzArEuE2Q1Sryhov2i", "colab_url": "https://colab.research.google.com/drive/14I39CnLNSZYekRVzArEuE2Q1Sryhov2i", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/14I39CnLNSZYekRVzArEuE2Q1Sryhov2i_First_LLM_Call_on_Colab.ipynb", "concepts": ["llm", "groq", "api_calls"], "type": "mini_project", "estimated_time": "30 minutes", "difficulty": "beginner"}, {"title": "Mini Project 3: First Chatbot", "colab_id": "1q14coEFNdJ6rgk8Nd0sZ4ymnufbDlqaq", "colab_url": "https://colab.research.google.com/drive/1q14coEFNdJ6rgk8Nd0sZ4ymnufbDlqaq", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1q14coEFNdJ6rgk8Nd0sZ4ymnufbDlqaq_First_chatbot.ipynb", "concepts": ["chatbot", "gradio", "user_interface"], "type": "mini_project", "estimated_time": "45 minutes", "difficulty": "beginner"}]}, {"session_id": 4, "title": "Building Bigger LLM Applications", "duration": "2.25 hours", "focus": "Advanced LLM capabilities with real-time data and memory", "day": "Day 2 (Saturday)", "notebooks": [{"title": "Mini Project 4: LLM with Real-time Data", "colab_id": "1mK9R-aJFWwREhuH99if9okpjxBhw2rt3", "colab_url": "https://colab.research.google.com/drive/1mK9R-aJFWwREhuH99if9okpjxBhw2rt3", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1mK9R-aJFWwREhuH99if9okpjxBhw2rt3_LLM_with_search.ipynb", "concepts": ["llm", "real_time_data", "search_integration"], "type": "mini_project", "estimated_time": "60 minutes", "difficulty": "intermediate"}, {"title": "Mini Project 5: LLM with Memory", "colab_id": "1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1", "colab_url": "https://colab.research.google.com/drive/1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1_LLM_with_memory.ipynb", "concepts": ["llm", "memory", "conversation_history"], "type": "mini_project", "estimated_time": "45 minutes", "difficulty": "intermediate"}, {"title": "Vector Similarity Search", "colab_id": "1cQr0ssoXcYxhJ5MvhtoYwmG67g3l0P5W", "colab_url": "https://colab.research.google.com/drive/1cQr0ssoXcYxhJ5MvhtoYwmG67g3l0P5W", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1cQr0ssoXcYxhJ5MvhtoYwmG67g3l0P5W_Vector_Similarity_Search.ipynb", "concepts": ["vector_search", "embeddings", "similarity"], "type": "advanced", "estimated_time": "45 minutes", "difficulty": "intermediate"}, {"title": "Multimodal Chat", "colab_id": "1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6", "colab_url": "https://colab.research.google.com/drive/1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6_Multimodal_chat.ipynb", "concepts": ["multimodal", "vision", "chat"], "type": "advanced", "estimated_time": "45 minutes", "difficulty": "intermediate"}]}, {"session_id": 5, "title": "Theory of Deep Learning and Model Metrics", "duration": "2.5 hours", "focus": "Neural networks, video AI, and RAG systems", "day": "Day 2 (Saturday)", "notebooks": [{"title": "Income Classification with Neural Networks", "colab_id": "1siq7IZTLHj9X9iGc0g0MELE_-XYAtne6", "colab_url": "https://colab.research.google.com/drive/1siq7IZTLHj9X9iGc0g0MELE_-XYAtne6", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/Income class prediction with Neural Nets.ipynb", "concepts": ["neural_networks", "deep_learning", "classification"], "type": "advanced", "estimated_time": "90 minutes", "difficulty": "intermediate"}, {"title": "Video Summarization with Whisper API", "colab_id": "17qFwmGKsRxhav7gB2JZy9NolK_9furAT", "colab_url": "https://colab.research.google.com/drive/17qFwmGKsRxhav7gB2JZy9NolK_9furAT", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/Summarize YouTube:  Modern AI Pro", "concepts": ["whisper", "video_ai", "summarization"], "type": "advanced", "estimated_time": "15 minutes", "difficulty": "intermediate"}, {"title": "RAG <PERSON>", "colab_id": "1npEnRSYXB4GC5nbRPowhkPCG7S6fhBRb", "colab_url": "https://colab.research.google.com/drive/1npEnRSYXB4GC5nbRPowhkPCG7S6fhBRb", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1npEnRSYXB4GC5nbRPowhkPCG7S6fhBRb_Agentic_RAG_1.ipynb", "concepts": ["rag", "retrieval", "chatbot"], "type": "advanced", "estimated_time": "75 minutes", "difficulty": "advanced"}]}, {"session_id": "3b", "title": "Foundations of Neural Networks", "duration": "45 minutes", "focus": "Neural network fundamentals and MNIST classification", "day": "Day 2 (Saturday)", "slides": "https://drive.google.com/file/d/1yYNqFv8wk_KXQ-7qkEOzMIhdTv_iSdE4/view", "notebooks": []}, {"session_id": "3c", "title": "Computer Vision", "duration": "1 hour", "focus": "Introduction to computer vision applications", "day": "Day 2 (Saturday)", "slides": "https://drive.google.com/file/d/1VV2aNCFJR-5J0FBPqp35kumcWgKL88mb/view", "notebooks": []}, {"session_id": 6, "title": "Moving from Chatbots to Agents", "duration": "2 hours", "focus": "Advanced RAG systems, SQL agents, and LLM security", "day": "Day 3 (NLP to RAG)", "time_slot": "9:30am - 11:30am", "slides": "https://docs.google.com/presentation/d/1BETtFncM602LN-xGia3h7yp6xXM7jAow-xKhDTH0sg8/edit#slide=id.g1e2ee25a6cc_0_0", "slides_2": "https://docs.google.com/presentation/d/1Lp6OrpJzi0tVd_BM9Vw6dAa6eNGgk84kOsFQ_sHgHiM/edit#slide=id.g1e2ee25a6cc_0_0", "notebooks": [{"title": "Similarity Search", "colab_id": "1cQr0ssoXcYxhJ5MvhtoYwmG67g3l0P5W", "colab_url": "https://colab.research.google.com/drive/1cQr0ssoXcYxhJ5MvhtoYwmG67g3l0P5W?usp=sharing", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1cQr0ssoXcYxhJ5MvhtoYwmG67g3l0P5W_Vector_Similarity_Search.ipynb", "concepts": ["vector_search", "similarity", "semantic_search"], "type": "advanced", "estimated_time": "30 minutes", "difficulty": "intermediate"}, {"title": "SQL Agent 1", "colab_id": "18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi", "colab_url": "https://colab.research.google.com/drive/18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi#scrollTo=XEvzlbS7Zi4E", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi_SQLRAG.ipynb", "concepts": ["sql_agents", "database_automation", "natural_language_to_sql"], "type": "advanced", "estimated_time": "45 minutes", "difficulty": "advanced"}, {"title": "Security in LLM", "colab_id": "1FarVX93keIA35dvMHr7k6FXBu1rTpv97", "colab_url": "https://colab.research.google.com/drive/1FarVX93keIA35dvMHr7k6FXBu1rTpv97", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1FarVX93keIA35dvMHr7k6FXBu1rTpv97_RAG_with_authorization.ipynb", "concepts": ["llm_security", "prompt_injection", "authorization"], "type": "security", "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "SQL Agent 2", "colab_id": "1patliw_XCH-3eKJVrfVcjF8nLyPy1Mb7", "colab_url": "https://colab.research.google.com/drive/1patliw_XCH-3eKJVrfVcjF8nLyPy1Mb7", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1patliw_XCH-3eKJVrfVcjF8nLyPy1Mb7_SQL_RAG_2.ipynb", "concepts": ["advanced_sql_agents", "complex_queries", "business_intelligence"], "type": "advanced", "estimated_time": "45 minutes", "difficulty": "advanced"}]}, {"session_id": 8, "title": "Advanced Applications and Data Science Evening Session", "duration": "2.5 hours", "focus": "Data visualization and advanced analytical capabilities", "day": "Day 3 (NLP to RAG)", "time_slot": "3:00pm - 5:30pm", "notebooks": [{"title": "Visualization with Matplotlib and Seaborn", "colab_id": "1HaCuMkMRrPySFdTaDyzOhqFPokEb_pjq", "colab_url": "https://colab.research.google.com/drive/1HaCuMkMRrPySFdTaDyzOhqFPokEb_pjq", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/Visualization with Matplotlib and Seaborn.ipynb", "concepts": ["data_visualization", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn"], "type": "foundational", "estimated_time": "45 minutes", "difficulty": "beginner"}, {"title": "Pandas Data Manipulation", "colab_id": "1IEi22MR7q7mzU6kNsccxEUw5RR59j4IV", "colab_url": "https://colab.research.google.com/drive/1IEi22MR7q7mzU6kNsccxEUw5RR59j4IV", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/Pandas_Reference_Notebook.ipynb", "concepts": ["pandas", "data_manipulation", "dataframes"], "type": "reference", "estimated_time": "60 minutes", "difficulty": "beginner"}, {"title": "NumPy Numerical Computing", "colab_id": "1lqYTQYqsQHoIQ97GTglXuQls7mODXVmQ", "colab_url": "https://colab.research.google.com/drive/1lqYTQYqsQHoIQ97GTglXuQls7mODXVmQ", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/NumPy_Deepdive.ipynb", "concepts": ["numpy", "arrays", "numerical_computing"], "type": "reference", "estimated_time": "45 minutes", "difficulty": "beginner"}]}], "supplementary_notebooks": [{"title": "Hands-on Linear Regression (2 variables)", "colab_id": "12f1_gQSElk3hJvGy8wbwLFsVzZgsXQZU", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/Fibonacci_Sequence_LR.ipynb", "category": "ML_Foundations"}, {"title": "Linear Regression from Scratch", "colab_id": "1HaZyo9E7Zu_aJDrHlRLPQu7sN0n-lK1i", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/Linear Regression_from scratch.ipynb", "category": "ML_Foundations"}, {"title": "Decision Trees and Random Forests", "colab_id": "1fyV33mDQ-kth_tf-JY2PN85jw9GChktg", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/FINGIT_Decision_trees.ipynb", "category": "ML_Advanced"}, {"title": "ML Metrics and Performance", "colab_id": "1hoFqN_gHkOYwVXZ2MBJVVslEuarKsd70", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/FINGIT_ML_METRICS.ipynb", "category": "ML_Evaluation"}, {"title": "Logistic Regression", "colab_id": "1IfDhJf4-Up1Qa04gjnGrnpAeTqjAW0VI", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/Logistic Regression Example.ipynb", "category": "ML_Classification"}, {"title": "Neural Networks Introduction", "colab_id": "1qNLKrel6dKKgLMTPj_hQdUt2E8WXl6oy", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/ANN MNIST Classifier.ipynb", "category": "Deep_Learning"}, {"title": "Regularization: Overfitting vs Underfitting", "colab_id": "1WC6_vxyHAYUDAvU8RBTZjK6OQIoB757t", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/FINGIT_Regularization.ipynb", "category": "ML_Advanced"}, {"title": "Video Summarization with Whisper API", "colab_id": "17qFwmGKsRxhav7gB2JZy9NolK_9furAT", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/Summarize YouTube:  Modern AI Pro", "category": "Multimodal_AI"}, {"title": "Categories of Language Models", "colab_id": "1bRZYvajrSV_k1AgH_Je1O4TWfpNvRU0P", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/Advanced operations with HuggingFace Models: Modern AI Pro", "category": "LLM_Theory"}], "course_statistics": {"total_sessions": 8, "total_core_notebooks": 21, "total_supplementary_notebooks": 12, "estimated_total_time": "14+ hours", "difficulty_distribution": {"beginner": 6, "intermediate": 15, "advanced": 10}, "concept_coverage": ["Machine Learning Fundamentals", "Data Science Tools (Pandas, NumPy, Visualization)", "Classification and Regression", "Neural Networks and Deep Learning", "Large Language Models", "Retrieval Augmented Generation (RAG)", "Multimodal AI", "Vector Search and Embeddings"]}}