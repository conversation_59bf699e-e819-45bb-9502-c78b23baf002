{"course_metadata": {"title": "Modern AI Pro Practitioner (India) Aug 2025", "source_file": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/Modern_AI_Pro_Practitioner_(India)_Aug_2025.ipynb", "total_modules": 6, "total_notebooks": 27, "processed_date": "2025-08-07"}, "modules": [{"module_id": 1, "title": "Strengthening the application foundation", "schedule": "Friday (4-6pm)", "notebooks": [{"title": "First LLM Call on Colab", "colab_id": "14I39CnLNSZYekRVzArEuE2Q1Sryhov2i", "colab_url": "https://colab.research.google.com/drive/14I39CnLNSZYekRVzArEuE2Q1Sryhov2i", "context": "0. Practising LLMs on VSCode (0, 2, 3 folders)\n1. [First LLM Call on Colab](https://colab.research.google.com/drive/14I39CnLNSZYekRVzArEuE2Q1Sryhov2i#scrollTo=BF0-EoHaRS4W)", "concepts": ["llm"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/14I39CnLNSZYekRVzArEuE2Q1Sryhov2i_First_LLM_Call_on_Colab.ipynb", "cell_count": 6, "code_cells": 6, "markdown_cells": 0, "imports": ["langchain_groq", "google.colab", "userdata", "ChatGroq"], "functions_defined": [], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "intermediate"}]}, {"module_id": 3, "title": "Moving from chabots to agents", "schedule": "Saturday (9am - 12:10pm)", "notebooks": [{"title": "First chatbot", "colab_id": "1q14coEFNdJ6rgk8Nd0sZ4ymnufbDlqaq", "colab_url": "https://colab.research.google.com/drive/1q14coEFNdJ6rgk8Nd0sZ4ymnufbDlqaq", "context": "1. [First chatbot](https://colab.research.google.com/drive/1q14coEFNdJ6rgk8Nd0sZ4ymnufbDlqaq?usp=sharing)\n3. [Local LLM with HuggingFace](https://colab.research.google.com/drive/1bz8eDPHFVX9nNLGGFkMMp_-PZDn8BBQr?usp=sharing)\n0. [Multimodal chat](https://colab.research.google.com/drive/1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6#scrollTo=s3kha54YQN3g)", "concepts": ["ai", "deployment", "llm", "memory", "tools", "agents", "langchain", "huggingface"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1q14coEFNdJ6rgk8Nd0sZ4ymnufbDlqaq_First_chatbot.ipynb", "cell_count": 3, "code_cells": 3, "markdown_cells": 0, "imports": ["langchain_groq", "userdata", "gradio", "ChatGroq", "google.colab"], "functions_defined": ["chat"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "beginner"}, {"title": "Local LLM with HuggingFace", "colab_id": "1bz8eDPHFVX9nNLGGFkMMp_-PZDn8BBQr", "colab_url": "https://colab.research.google.com/drive/1bz8eDPHFVX9nNLGGFkMMp_-PZDn8BBQr", "context": "1. [First chatbot](https://colab.research.google.com/drive/1q14coEFNdJ6rgk8Nd0sZ4ymnufbDlqaq?usp=sharing)\n3. [Local LLM with HuggingFace](https://colab.research.google.com/drive/1bz8eDPHFVX9nNLGGFkMMp_-PZDn8BBQr?usp=sharing)\n0. [Multimodal chat](https://colab.research.google.com/drive/1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6#scrollTo=s3kha54YQN3g)\n1. [LLM with memory](https://colab.research.google.com/drive/1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1?usp=sharing)", "concepts": ["ai", "deployment", "llm", "memory", "tools", "agents", "langchain", "huggingface"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1bz8eDPHFVX9nNLGGFkMMp_-PZDn8BBQr_Local_LLM_with_HuggingFace.ipynb", "cell_count": 11, "code_cells": 11, "markdown_cells": 0, "imports": ["transformers", "langchain_huggingface", "torch", "AutoTokenizer", "HuggingFacePipeline"], "functions_defined": ["clear_memory", "generate_text", "check_memory"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "Multimodal chat", "colab_id": "1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6", "colab_url": "https://colab.research.google.com/drive/1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6", "context": "1. [First chatbot](https://colab.research.google.com/drive/1q14coEFNdJ6rgk8Nd0sZ4ymnufbDlqaq?usp=sharing)\n3. [Local LLM with HuggingFace](https://colab.research.google.com/drive/1bz8eDPHFVX9nNLGGFkMMp_-PZDn8BBQr?usp=sharing)\n0. [Multimodal chat](https://colab.research.google.com/drive/1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6#scrollTo=s3kha54YQN3g)\n1. [LLM with memory](https://colab.research.google.com/drive/1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1?usp=sharing)\n   Additional work: [Types of Memory in LLMs with Langchain](https://colab.research.google.com/drive/1AcyWGauS0KooIGpk5KBR6yKE62pgJUA1#scrollTo=-WM4x4obAbOw)", "concepts": ["ai", "deployment", "llm", "memory", "tools", "agents", "langchain", "huggingface"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6_Multimodal_chat.ipynb", "cell_count": 9, "code_cells": 7, "markdown_cells": 2, "imports": ["userdata", "gradio", "os", "Groq", "PIL", "io", "Image", "google.colab", "groq", "base64"], "functions_defined": ["process_file", "encode_image_to_base64"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "intermediate"}, {"title": "LLM with memory", "colab_id": "1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1", "colab_url": "https://colab.research.google.com/drive/1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1", "context": "3. [Local LLM with HuggingFace](https://colab.research.google.com/drive/1bz8eDPHFVX9nNLGGFkMMp_-PZDn8BBQr?usp=sharing)\n0. [Multimodal chat](https://colab.research.google.com/drive/1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6#scrollTo=s3kha54YQN3g)\n1. [LLM with memory](https://colab.research.google.com/drive/1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1?usp=sharing)\n   Additional work: [Types of Memory in LLMs with Langchain](https://colab.research.google.com/drive/1AcyWGauS0KooIGpk5KBR6yKE62pgJUA1#scrollTo=-WM4x4obAbOw)\n2. [LLM with search](https://colab.research.google.com/drive/1mK9R-aJFWwREhuH99if9okpjxBhw2rt3?usp=sharing)", "concepts": ["ai", "deployment", "llm", "memory", "tools", "agents", "langchain", "huggingface"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1_LLM_with_memory.ipynb", "cell_count": 5, "code_cells": 5, "markdown_cells": 0, "imports": ["langchain_groq", "userdata", "gradio", "ChatGroq", "google.colab"], "functions_defined": ["__call__", "chat", "__init__"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "intermediate"}, {"title": "Types of Memory in LLMs with Langchain", "colab_id": "1AcyWGauS0KooIGpk5KBR6yKE62pgJUA1", "colab_url": "https://colab.research.google.com/drive/1AcyWGauS0KooIGpk5KBR6yKE62pgJUA1", "context": "0. [Multimodal chat](https://colab.research.google.com/drive/1EUu7lS6r6DUEqoxnlobiOh5FHQuCm2r6#scrollTo=s3kha54YQN3g)\n1. [LLM with memory](https://colab.research.google.com/drive/1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1?usp=sharing)\n   Additional work: [Types of Memory in LLMs with Langchain](https://colab.research.google.com/drive/1AcyWGauS0KooIGpk5KBR6yKE62pgJUA1#scrollTo=-WM4x4obAbOw)\n2. [LLM with search](https://colab.research.google.com/drive/1mK9R-aJFWwREhuH99if9okpjxBhw2rt3?usp=sharing)\n1. [LLM with tools](https://colab.research.google.com/drive/16fm4MQuT6Z6kG6fHYn3HJAXqdg_zdr7g?usp=sharing)", "concepts": ["ai", "deployment", "llm", "memory", "tools", "agents", "langchain", "huggingface"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1AcyWGauS0KooIGpk5KBR6yKE62pgJUA1_Types_of_Memory_in_LLMs_with_Langchain.ipynb", "cell_count": 16, "code_cells": 13, "markdown_cells": 3, "imports": ["userdata", "ConversationBufferMemory", "gradio", "os", "Conversation<PERSON>hain", "langchain.memory", "ChatAnthropic", "langchain_core.prompts.prompt", "PromptTemplate", "environ", "google.colab", "langchain.chains", "langchain_anthropic"], "functions_defined": ["chat", "summary_chat"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "LLM with search", "colab_id": "1mK9R-aJFWwREhuH99if9okpjxBhw2rt3", "colab_url": "https://colab.research.google.com/drive/1mK9R-aJFWwREhuH99if9okpjxBhw2rt3", "context": "1. [LLM with memory](https://colab.research.google.com/drive/1nIQbid1SBPCkoQhjysgbXGClLZ4I8sy1?usp=sharing)\n   Additional work: [Types of Memory in LLMs with Langchain](https://colab.research.google.com/drive/1AcyWGauS0KooIGpk5KBR6yKE62pgJUA1#scrollTo=-WM4x4obAbOw)\n2. [LLM with search](https://colab.research.google.com/drive/1mK9R-aJFWwREhuH99if9okpjxBhw2rt3?usp=sharing)\n1. [LLM with tools](https://colab.research.google.com/drive/16fm4MQuT6Z6kG6fHYn3HJAXqdg_zdr7g?usp=sharing)\n4. [Agents talking to one another](https://colab.research.google.com/drive/1GKnxasdBqh81YooV8oCqekTwB3YYTXuY#scrollTo=NQ9hO4O85SeW)", "concepts": ["ai", "deployment", "llm", "memory", "tools", "agents", "langchain", "huggingface"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1mK9R-aJFWwREhuH99if9okpjxBhw2rt3_LLM_with_search.ipynb", "cell_count": 17, "code_cells": 17, "markdown_cells": 0, "imports": ["langchain_groq", "userdata", "ChatGroq", "IPython.display", "DDGS", "HTML", "google.colab", "ddgs", "the"], "functions_defined": ["news_analyzer", "set_css"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "LLM with tools", "colab_id": "16fm4MQuT6Z6kG6fHYn3HJAXqdg_zdr7g", "colab_url": "https://colab.research.google.com/drive/16fm4MQuT6Z6kG6fHYn3HJAXqdg_zdr7g", "context": "Additional work: [Types of Memory in LLMs with Langchain](https://colab.research.google.com/drive/1AcyWGauS0KooIGpk5KBR6yKE62pgJUA1#scrollTo=-WM4x4obAbOw)\n2. [LLM with search](https://colab.research.google.com/drive/1mK9R-aJFWwREhuH99if9okpjxBhw2rt3?usp=sharing)\n1. [LLM with tools](https://colab.research.google.com/drive/16fm4MQuT6Z6kG6fHYn3HJAXqdg_zdr7g?usp=sharing)\n4. [Agents talking to one another](https://colab.research.google.com/drive/1GKnxasdBqh81YooV8oCqekTwB3YYTXuY#scrollTo=NQ9hO4O85SeW)\n4. [First agent with the ReAct Framework](https://colab.research.google.com/drive/1IK5JvjczY5QsTbqC7SWy206ZGFC4k5nw?usp=sharing)", "concepts": ["ai", "deployment", "llm", "memory", "tools", "agents", "langchain", "huggingface"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/16fm4MQuT6Z6kG6fHYn3HJAXqdg_zdr7g_LLM_with_tools.ipynb", "cell_count": 9, "code_cells": 9, "markdown_cells": 0, "imports": ["langchain_groq", "userdata", "gradio", "ChatGroq", "re", "json", "datetime", "google.colab", "math"], "functions_defined": ["calculate", "__call__", "__init__", "handle_action", "chat", "submit_complaint"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "intermediate"}, {"title": "Agent with CrewAI", "colab_id": "1ACt6TPg8SdhtXD1xOQNpIWS4RjrIQKfW", "colab_url": "https://colab.research.google.com/drive/1ACt6TPg8SdhtXD1xOQNpIWS4RjrIQKfW", "context": "Agent framework for collaborative AI systems using CrewAI", "concepts": ["crewai", "agents", "collaboration", "frameworks"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1ACt6TPg8SdhtXD1xOQNpIWS4RjrIQKfW_Agent_with_CrewAI.ipynb", "cell_count": 12, "code_cells": 8, "markdown_cells": 4, "imports": ["crewai", "langchain", "agents"], "functions_defined": [], "key_concepts": ["Multi-agent systems", "CrewAI framework", "Agent coordination"], "estimated_time": "75 minutes", "difficulty": "advanced"}, {"title": "Agents talking to one another", "colab_id": "1GKnxasdBqh81YooV8oCqekTwB3YYTXuY", "colab_url": "https://colab.research.google.com/drive/1GKnxasdBqh81YooV8oCqekTwB3YYTXuY", "context": "2. [LLM with search](https://colab.research.google.com/drive/1mK9R-aJFWwREhuH99if9okpjxBhw2rt3?usp=sharing)\n1. [LLM with tools](https://colab.research.google.com/drive/16fm4MQuT6Z6kG6fHYn3HJAXqdg_zdr7g?usp=sharing)\n4. [Agents talking to one another](https://colab.research.google.com/drive/1GKnxasdBqh81YooV8oCqekTwB3YYTXuY#scrollTo=NQ9hO4O85SeW)\n4. [First agent with the ReAct Framework](https://colab.research.google.com/drive/1IK5JvjczY5QsTbqC7SWy206ZGFC4k5nw?usp=sharing)\n6. [Agents with Langgraph](https://colab.research.google.com/drive/15UuvEmi955OsIMTknxwh3pn3nE-XJptr?usp=sharing)", "concepts": ["ai", "deployment", "llm", "memory", "tools", "agents", "langchain", "huggingface"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1GKnxasdBqh81YooV8oCqekTwB3YYTXuY_Agents_talking_to_one_another.ipynb", "cell_count": 23, "code_cells": 20, "markdown_cells": 3, "imports": ["userdata", "os", "Python", "the", "ConversableAgent", "IPython.display", "HTML", "google.colab", "autogen"], "functions_defined": ["set_css"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "First agent with the ReAct Framework", "colab_id": "1IK5JvjczY5QsTbqC7SWy206ZGFC4k5nw", "colab_url": "https://colab.research.google.com/drive/1IK5JvjczY5QsTbqC7SWy206ZGFC4k5nw", "context": "1. [LLM with tools](https://colab.research.google.com/drive/16fm4MQuT6Z6kG6fHYn3HJAXqdg_zdr7g?usp=sharing)\n4. [Agents talking to one another](https://colab.research.google.com/drive/1GKnxasdBqh81YooV8oCqekTwB3YYTXuY#scrollTo=NQ9hO4O85SeW)\n4. [First agent with the ReAct Framework](https://colab.research.google.com/drive/1IK5JvjczY5QsTbqC7SWy206ZGFC4k5nw?usp=sharing)\n6. [Agents with Langgraph](https://colab.research.google.com/drive/15UuvEmi955OsIMTknxwh3pn3nE-XJptr?usp=sharing)\n7. [Advanced agents use case](https://colab.research.google.com/drive/1I2XOUw0S_vj58jOUOt8081dneiF_3wCm?usp=sharing)", "concepts": ["ai", "deployment", "llm", "memory", "tools", "agents", "langchain", "huggingface"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1IK5JvjczY5QsTbqC7SWy206ZGFC4k5nw_First_agent_with_the_ReAct_Framework.ipynb", "cell_count": 19, "code_cells": 17, "markdown_cells": 2, "imports": ["langchain_groq", "userdata", "os", "ChatGroq", "polygon", "re", "DDGS", "google.colab", "ddgs", "math"], "functions_defined": ["get_stock_price", "simple_query", "multi_step_reasoning", "calculate", "__call__", "parse_actions", "__init__", "search_news"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "Agents with Langgraph", "colab_id": "15UuvEmi955OsIMTknxwh3pn3nE-XJptr", "colab_url": "https://colab.research.google.com/drive/15UuvEmi955OsIMTknxwh3pn3nE-XJptr", "context": "4. [Agents talking to one another](https://colab.research.google.com/drive/1GKnxasdBqh81YooV8oCqekTwB3YYTXuY#scrollTo=NQ9hO4O85SeW)\n4. [First agent with the ReAct Framework](https://colab.research.google.com/drive/1IK5JvjczY5QsTbqC7SWy206ZGFC4k5nw?usp=sharing)\n6. [Agents with Langgraph](https://colab.research.google.com/drive/15UuvEmi955OsIMTknxwh3pn3nE-XJptr?usp=sharing)\n7. [Advanced agents use case](https://colab.research.google.com/drive/1I2XOUw0S_vj58jOUOt8081dneiF_3wCm?usp=sharing)", "concepts": ["ai", "deployment", "llm", "memory", "tools", "agents", "langchain", "huggingface"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/15UuvEmi955OsIMTknxwh3pn3nE-XJptr_Agents_with_Langgraph.ipynb", "cell_count": 5, "code_cells": 4, "markdown_cells": 1, "imports": ["langchain_groq", "nasapy", "create_react_agent", "TavilySearchResults", "langgraph.prebuilt", "gradio", "os", "langgraph.checkpoint.memory", "langchain_core.tools", "userdata", "ChatGroq", "<PERSON><PERSON>", "HumanMessage", "langchain_core.messages", "datetime", "langchain_community.tools.tavily_search", "tool", "MemorySaver", "google.colab"], "functions_defined": ["chat", "get_hotel"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "beginner"}, {"title": "Advanced agents use case", "colab_id": "1I2XOUw0S_vj58jOUOt8081dneiF_3wCm", "colab_url": "https://colab.research.google.com/drive/1I2XOUw0S_vj58jOUOt8081dneiF_3wCm", "context": "4. [First agent with the ReAct Framework](https://colab.research.google.com/drive/1IK5JvjczY5QsTbqC7SWy206ZGFC4k5nw?usp=sharing)\n6. [Agents with Langgraph](https://colab.research.google.com/drive/15UuvEmi955OsIMTknxwh3pn3nE-XJptr?usp=sharing)\n7. [Advanced agents use case](https://colab.research.google.com/drive/1I2XOUw0S_vj58jOUOt8081dneiF_3wCm?usp=sharing)\n\nAI deployment: https://github.com/balajivis/mai-deploy-frontend", "concepts": ["ai", "deployment", "llm", "memory", "tools", "agents", "langchain", "huggingface"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1I2XOUw0S_vj58jOUOt8081dneiF_3wCm_Advanced_agents_use_case.ipynb", "cell_count": 8, "code_cells": 8, "markdown_cells": 0, "imports": ["langchain_groq", "nasapy", "create_react_agent", "ArxivQuery<PERSON>un", "json", "TavilySearchResults", "langgraph.prebuilt", "PolygonFinancials", "langchain_community.tools.arxiv", "langchain_community.utilities.polygon", "gradio", "os", "langgraph.checkpoint.memory", "YouTubeSearchTool", "langchain_core.tools", "the", "userdata", "ChatGroq", "<PERSON><PERSON>", "HumanMessage", "langchain_community.tools.polygon", "langchain_core.messages", "datetime", "PolygonAPIWrapper", "polygon", "langchain_community.tools.tavily_search", "tool", "MemorySaver", "langchain_community.tools.youtube.search", "google.colab"], "functions_defined": ["get_stock_price", "chat", "get_nasa_pic_of_day", "get_hotel"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "intermediate"}]}, {"module_id": 4, "title": "Deep document management with LLM", "schedule": "Saturday (1 - 3pm)", "notebooks": [{"title": "Q&A on documents", "colab_id": "1icYUUiBSAheX8Dfog0mhhQJeZdRlBlx-", "colab_url": "https://colab.research.google.com/drive/1icYUUiBSAheX8Dfog0mhhQJeZdRlBlx-", "context": "Summarization with BrahmaSumm, analysis, table extraction, chatting with documents, kb creation.\n\n1. [Q&A on documents](https://colab.research.google.com/drive/1icYUUiBSAheX8Dfog0mhhQJeZdRlBlx-#scrollTo=vRFpSBs4aXDY)\n6. [Analyzing and Querying a Document with LLM Notebook](https://colab.research.google.com/drive/1G-3Zl8-eyxR_kP7B5gJSVCxcli0hsKoe)\n3. [Deep document analysis with BrahmaSumm](https://github.com/balajivis/brahmasumm)", "concepts": ["llm"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1icYUUiBSAheX8Dfog0mhhQJeZdRlBlx-_QA_on_documents.ipynb", "cell_count": 19, "code_cells": 15, "markdown_cells": 4, "imports": ["userdata", "langchain_groq", "gradio", "os", "ChatGroq", "langchain.document_loaders", "RecursiveCharacterTextSplitter", "IPython.display", "HTML", "chromadb", "google.colab", "langchain_text_splitters", "the", "PyPD<PERSON><PERSON>der"], "functions_defined": ["load_and_chunk_pdf", "rag", "add_chunks_to_vector_db", "chat", "set_css"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "Analyzing and Querying a Document with LLM Notebook", "colab_id": "1G-3Zl8-eyxR_kP7B5gJSVCxcli0hsKoe", "colab_url": "https://colab.research.google.com/drive/1G-3Zl8-eyxR_kP7B5gJSVCxcli0hsKoe", "context": "1. [Q&A on documents](https://colab.research.google.com/drive/1icYUUiBSAheX8Dfog0mhhQJeZdRlBlx-#scrollTo=vRFpSBs4aXDY)\n6. [Analyzing and Querying a Document with LLM Notebook](https://colab.research.google.com/drive/1G-3Zl8-eyxR_kP7B5gJSVCxcli0hsKoe)\n3. [Deep document analysis with BrahmaSumm](https://github.com/balajivis/brahmasumm)\n[Simple notebook](https://colab.research.google.com/drive/1Qt8-XbXODbWblnJns2qTZB5H72EJmGJ5#scrollTo=cLxzKp55yUa-)", "concepts": ["llm"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1G-3Zl8-eyxR_kP7B5gJSVCxcli0hsKoe_Analyzing_and_Querying_a_Document_with_LLM_Notebook.ipynb", "cell_count": 26, "code_cells": 18, "markdown_cells": 8, "imports": ["langchain_groq", "WordNetLemmatizer", "wordcloud", "nltk", "IPython.display", "word_tokenize", "nltk.corpus", "os", "nltk.tokenize", "attacks", "PdfReader", "WordCloud", "HTML", "langchain.schema.document", "userdata", "ChatGroq", "re", "RetrievalQA", "langchain.vectorstores", "collections", "langchain.text_splitter", "Document", "PyPDF2", "Counter", "Chroma", "stopwords", "matplotlib.pyplot", "langchain.embeddings", "Hugging<PERSON><PERSON><PERSON>mbe<PERSON><PERSON>", "your", "google.colab", "langchain.chains", "RecursiveCharacterTextSplitter", "nltk.stem"], "functions_defined": ["rag_manager", "display_word_cloud", "set_css"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "Simple notebook", "colab_id": "1Qt8-XbXODbWblnJns2qTZB5H72EJmGJ5", "colab_url": "https://colab.research.google.com/drive/1Qt8-XbXODbWblnJns2qTZB5H72EJmGJ5", "context": "6. [Analyzing and Querying a Document with LLM Notebook](https://colab.research.google.com/drive/1G-3Zl8-eyxR_kP7B5gJSVCxcli0hsKoe)\n3. [Deep document analysis with BrahmaSumm](https://github.com/balajivis/brahmasumm)\n[Simple notebook](https://colab.research.google.com/drive/1Qt8-XbXODbWblnJns2qTZB5H72EJmGJ5#scrollTo=cLxzKp55yUa-)\n7. [Table Analysis with Unstructured IO Notebook](https://colab.research.google.com/drive/1YJ5pxuESgwcc107pNVIxh7uD2SWxRPDY?usp=sharing) -- 1 hour\n8. [Multimodal advanced table and image analysis](https://colab.research.google.com/drive/1n2MZj5CVlr1bXSq7JiKabbVU-RM1hO8s#scrollTo=IV018jkcRen5)", "concepts": ["llm"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1Qt8-XbXODbWblnJns2qTZB5H72EJmGJ5_Simple_notebook.ipynb", "cell_count": 33, "code_cells": 27, "markdown_cells": 6, "imports": ["langchain_groq", "IPython.display", "web", "PyPD<PERSON><PERSON>der", "this", "os", "sklearn.cluster", "umap", "tqdm", "HTML", "<PERSON><PERSON><PERSON><PERSON>", "userdata", "ChatGroq", "re", "WebBaseLoader", "a", "numpy", "langchain_huggingface", "matplotlib.pyplot", "Hugging<PERSON><PERSON><PERSON>mbe<PERSON><PERSON>", "langchain_community.document_loaders", "google.colab"], "functions_defined": ["find_themes_for_clusters", "process_paragraph", "preprocess_text", "find_suitable_theme", "embed_documents_with_progress", "finalize_chunk", "print_labels_in_grid", "get_word_count_per_chunk", "chunk_document_with_flexibility", "cluster_document", "find_n_closest_representatives", "detailed_summarizer", "document_loader", "set_css", "plot_clusters_with_umap"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "Table Analysis with Unstructured IO Notebook", "colab_id": "1YJ5pxuESgwcc107pNVIxh7uD2SWxRPDY", "colab_url": "https://colab.research.google.com/drive/1YJ5pxuESgwcc107pNVIxh7uD2SWxRPDY", "context": "3. [Deep document analysis with BrahmaSumm](https://github.com/balajivis/brahmasumm)\n[Simple notebook](https://colab.research.google.com/drive/1Qt8-XbXODbWblnJns2qTZB5H72EJmGJ5#scrollTo=cLxzKp55yUa-)\n7. [Table Analysis with Unstructured IO Notebook](https://colab.research.google.com/drive/1YJ5pxuESgwcc107pNVIxh7uD2SWxRPDY?usp=sharing) -- 1 hour\n8. [Multimodal advanced table and image analysis](https://colab.research.google.com/drive/1n2MZj5CVlr1bXSq7JiKabbVU-RM1hO8s#scrollTo=IV018jkcRen5)\n9. [Deep document analysis](https://colab.research.google.com/drive/1zwJZLN63f6thjlhmZYiocIyUe_ViEYzl#scrollTo=wCzg74KkDtRT)", "concepts": ["llm"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1YJ5pxuESgwcc107pNVIxh7uD2SWxRPDY_Table_Analysis_with_Unstructured_IO_Notebook.ipynb", "cell_count": 27, "code_cells": 20, "markdown_cells": 7, "imports": ["langchain_groq", "pdf2image", "io", "pipeline", "IPython.display", "os", "convert_from_path", "Image", "unstructured.partition.pdf", "HTML", "display", "requests", "the", "userdata", "BytesIO", "transformers", "ChatGroq", "PIL", "partition_pdf", "matplotlib.pyplot", "google.colab"], "functions_defined": [], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "Multimodal advanced table and image analysis", "colab_id": "1n2MZj5CVlr1bXSq7JiKabbVU-RM1hO8s", "colab_url": "https://colab.research.google.com/drive/1n2MZj5CVlr1bXSq7JiKabbVU-RM1hO8s", "context": "[Simple notebook](https://colab.research.google.com/drive/1Qt8-XbXODbWblnJns2qTZB5H72EJmGJ5#scrollTo=cLxzKp55yUa-)\n7. [Table Analysis with Unstructured IO Notebook](https://colab.research.google.com/drive/1YJ5pxuESgwcc107pNVIxh7uD2SWxRPDY?usp=sharing) -- 1 hour\n8. [Multimodal advanced table and image analysis](https://colab.research.google.com/drive/1n2MZj5CVlr1bXSq7JiKabbVU-RM1hO8s#scrollTo=IV018jkcRen5)\n9. [Deep document analysis](https://colab.research.google.com/drive/1zwJZLN63f6thjlhmZYiocIyUe_ViEYzl#scrollTo=wCzg74KkDtRT)", "concepts": ["llm"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1n2MZj5CVlr1bXSq7JiKabbVU-RM1hO8s_Multimodal_advanced_table_and_image_analysis.ipynb", "cell_count": 30, "code_cells": 19, "markdown_cells": 11, "imports": ["userdata", "this", "llama_index.multi_modal_llms.anthropic", "matplotlib.image", "llama_index.core.schema", "os", "pdf2image", "ImageDocument", "convert_from_path", "AnthropicMultiModal", "matplotlib.pyplot", "unstructured.partition.pdf", "IPython.display", "HTML", "https", "google.colab", "partition_pdf", "the"], "functions_defined": ["set_css"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "Deep document analysis", "colab_id": "1zwJZLN63f6thjlhmZYiocIyUe_ViEYzl", "colab_url": "https://colab.research.google.com/drive/1zwJZLN63f6thjlhmZYiocIyUe_ViEYzl", "context": "1. [Q&A on documents](https://colab.research.google.com/drive/1icYUUiBSAheX8Dfog0mhhQJeZdRlBlx-#scrollTo=vRFpSBs4aXDY)\n6. [Analyzing and Querying a Document with LLM Notebook](https://colab.research.google.com/drive/1G-3Zl8-eyxR_kP7B5gJSVCxcli0hsKoe)\n3. [Deep document analysis with BrahmaSumm](https://github.com/balajivis/brahmasumm)\n[Simple notebook](https://colab.research.google.com/drive/1Qt8-XbXODbWblnJns2qTZB5H72EJmGJ5#scrollTo=cLxzKp55yUa-)\n7. [Table Analysis with Unstructured IO Notebook](https://colab.research.google.com/drive/1YJ5pxuESgwcc107pNVIxh7uD2SWxRPDY?usp=sharing) -- 1 hour", "concepts": ["llm"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1zwJZLN63f6thjlhmZYiocIyUe_ViEYzl_Deep_document_analysis.ipynb", "cell_count": 50, "code_cells": 39, "markdown_cells": 11, "imports": ["langchain_groq", "algorithms", "WordNetLemmatizer", "wordcloud", "io", "nltk", "IPython.display", "load_summarize_chain", "word_tokenize", "nltk.corpus", "os", "networkx", "nltk.tokenize", "sklearn.cluster", "langchain.schema", "PdfReader", "cdlib", "WordCloud", "HTML", "<PERSON><PERSON><PERSON><PERSON>", "time", "requests", "Arxiv<PERSON><PERSON>der", "the", "userdata", "BytesIO", "ChatGroq", "re", "WebBaseLoader", "these", "PromptTemplate", "langchain", "numpy", "collections", "langchain.text_splitter", "Document", "langchain.chains.summarize", "PyPDF2", "Counter", "stopwords", "matplotlib.pyplot", "langchain.embeddings", "langchain_community.document_loaders", "Hugging<PERSON><PERSON><PERSON>mbe<PERSON><PERSON>", "your", "google.colab", "RecursiveCharacterTextSplitter", "nltk.stem", "that"], "functions_defined": ["display_word_cloud", "build_graph_from_summaries", "set_css", "detect_communities"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}]}, {"module_id": 7, "title": "Advanced RAG architectures", "schedule": "Sunday (1:15pm - 3:15pm)", "notebooks": [{"title": "SQLRAG", "colab_id": "18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi", "colab_url": "https://colab.research.google.com/drive/18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi", "context": "[Slides](https://docs.google.com/presentation/d/1Lp6OrpJzi0tVd_BM9Vw6dAa6eNGgk84kOsFQ_sHgHiM/edit#slide=id.g1e2ee25a6cc_0_0)\n\nWe will look at GraphRAG, SQLRAG, explore merging elastic with embeddings, query expansion and visualization.\n\n1. [SQLRAG](https://colab.research.google.com/drive/18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi#scrollTo=XEvzlbS7Zi4E)", "concepts": ["rag"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi_SQLRAG.ipynb", "cell_count": 22, "code_cells": 20, "markdown_cells": 2, "imports": ["langchain_groq", "ChatPromptTemplate", "SQLDatabase", "IPython.display", "https", "pandas", "our", "os", "langchain_community.vectorstores", "HTML", "langchain_community.utilities", "the", "StrOutputParser", "langchain_core.prompts", "userdata", "ChatGroq", "langchain_core.runnables", "these", "RunnablePassthrough", "langchain_community.embeddings", "langchain.docstore.document", "Document", "sqlite3", "Chroma", "langchain_core.output_parsers", "Hugging<PERSON><PERSON><PERSON>mbe<PERSON><PERSON>", "google.colab"], "functions_defined": ["run_query", "set_css", "get_schema"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "SQL RAG 2", "colab_id": "1patliw_XCH-3eKJVrfVcjF8nLyPy1Mb7", "colab_url": "https://colab.research.google.com/drive/1patliw_XCH-3eKJVrfVcjF8nLyPy1Mb7", "context": "1. [SQLRAG](https://colab.research.google.com/drive/18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi#scrollTo=XEvzlbS7Zi4E)\n2. [SQL RAG 2](https://colab.research.google.com/drive/1patliw_XCH-3eKJVrfVcjF8nLyPy1Mb7)\n2. [Advanced RAG for Finance Domain](https://colab.research.google.com/drive/15ewzaL4odo0jVqZro65AqVkWDI-_8Y3f?usp=drive_link)\n3. [GraphRAG](https://colab.research.google.com/drive/1AqYlciQosXZs4gzOTL9O7wbtTnc9RLCb#scrollTo=S8pL2YDQ2XzP)", "concepts": ["rag"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1patliw_XCH-3eKJVrfVcjF8nLyPy1Mb7_SQL_RAG_2.ipynb", "cell_count": 47, "code_cells": 34, "markdown_cells": 13, "imports": ["langchain_groq", "ChatPromptTemplate", "SQLDatabase", "google.colab", "wordcloud", "IPython.display", "pandas", "our", "HuggingFacePipeline", "os", "citations.", "langchain_community.vectorstores", "torch", "WordCloud", "HTML", "langchain_community.utilities", "the", "StrOutputParser", "langchain_core.prompts", "userdata", "transformers", "BitsAndBytesConfig", "ChatGroq", "langchain_core.runnables", "these", "langchain_community.llms.huggingface_pipeline", "RunnablePassthrough", "langchain_community.embeddings", "langchain.docstore.document", "Document", "sqlite3", "Chroma", "langchain_core.output_parsers", "matplotlib.pyplot", "Hugging<PERSON><PERSON><PERSON>mbe<PERSON><PERSON>", "environ", "world_events"], "functions_defined": ["run_query", "set_css", "get_schema"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "Advanced RAG for Finance Domain", "colab_id": "15ewzaL4odo0jVqZro65AqVkWDI-_8Y3f", "colab_url": "https://colab.research.google.com/drive/15ewzaL4odo0jVqZro65AqVkWDI-_8Y3f", "context": "1. [SQLRAG](https://colab.research.google.com/drive/18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi#scrollTo=XEvzlbS7Zi4E)\n2. [SQL RAG 2](https://colab.research.google.com/drive/1patliw_XCH-3eKJVrfVcjF8nLyPy1Mb7)\n2. [Advanced RAG for Finance Domain](https://colab.research.google.com/drive/15ewzaL4odo0jVqZro65AqVkWDI-_8Y3f?usp=drive_link)\n3. [GraphRAG](https://colab.research.google.com/drive/1AqYlciQosXZs4gzOTL9O7wbtTnc9RLCb#scrollTo=S8pL2YDQ2XzP)\n5. [RAG with authorization levels](https://colab.research.google.com/drive/1FarVX93keIA35dvMHr7k6FXBu1rTpv97?usp=sharing)", "concepts": ["rag"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/15ewzaL4odo0jVqZro65AqVkWDI-_8Y3f_Advanced_RAG_for_Finance_Domain.ipynb", "cell_count": 30, "code_cells": 24, "markdown_cells": 6, "imports": ["langchain_groq", "google.colab", "io", "sentence_transformers", "CrossEncoder", "umap", "tqdm", "the", "userdata", "ChatGroq", "urllib.request", "numpy", "chromadb", "langchain.text_splitter", "PyPDF2", "SentenceTransformerEmbeddingFunction", "matplotlib.pyplot", "chromadb.utils.embedding_functions", "RecursiveCharacterTextSplitter"], "functions_defined": ["augment_query_generated", "rag_query", "analyze_query", "compute_ranking", "visualize_results", "augmented_rag", "project_embeddings", "word_wrap"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "GraphRAG", "colab_id": "1AqYlciQosXZs4gzOTL9O7wbtTnc9RLCb", "colab_url": "https://colab.research.google.com/drive/1AqYlciQosXZs4gzOTL9O7wbtTnc9RLCb", "context": "[Slides](https://docs.google.com/presentation/d/1Lp6OrpJzi0tVd_BM9Vw6dAa6eNGgk84kOsFQ_sHgHiM/edit#slide=id.g1e2ee25a6cc_0_0)\n\nWe will look at GraphRAG, SQLRAG, explore merging elastic with embeddings, query expansion and visualization.\n\n1. [SQLRAG](https://colab.research.google.com/drive/18ZyyCfpPDnGOQNw0aevpfx-ZjSstwFsi#scrollTo=XEvzlbS7Zi4E)", "concepts": ["rag"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1AqYlciQosXZs4gzOTL9O7wbtTnc9RLCb_GraphRAG.ipynb", "cell_count": 26, "code_cells": 22, "markdown_cells": 4, "imports": ["langchain_groq", "ChatPromptTemplate", "Neo4jGraph", "typing", "IPython.display", "os", "langchain_core.prompts.prompt", "langchain_community.vectorstores", "HTML", "langchain_core.pydantic_v1", "the", "langchain_core.prompts", "userdata", "remove_lucene_chars", "ChatGroq", "langchain_community.graphs", "WebBaseLoader", "Neo4jVector", "PromptTemplate", "LLMGraphTransformer", "langchain_experimental.graph_transformers", "langchain.text_splitter", "langchain.docstore.document", "Document", "langchain_huggingface", "<PERSON><PERSON>", "BaseModel", "Hugging<PERSON><PERSON><PERSON>mbe<PERSON><PERSON>", "langchain_community.document_loaders", "langchain_community.vectorstores.neo4j_vector", "google.colab", "RecursiveCharacterTextSplitter"], "functions_defined": ["structured_retriever", "graph_retriever", "graph_rag", "generate_full_text_query", "vector_rag", "set_css"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}, {"title": "RAG with authorization levels", "colab_id": "1FarVX93keIA35dvMHr7k6FXBu1rTpv97", "colab_url": "https://colab.research.google.com/drive/1FarVX93keIA35dvMHr7k6FXBu1rTpv97", "context": "2. [Advanced RAG for Finance Domain](https://colab.research.google.com/drive/15ewzaL4odo0jVqZro65AqVkWDI-_8Y3f?usp=drive_link)\n3. [GraphRAG](https://colab.research.google.com/drive/1AqYlciQosXZs4gzOTL9O7wbtTnc9RLCb#scrollTo=S8pL2YDQ2XzP)\n5. [RAG with authorization levels](https://colab.research.google.com/drive/1FarVX93keIA35dvMHr7k6FXBu1rTpv97?usp=sharing)", "concepts": ["rag"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1FarVX93keIA35dvMHr7k6FXBu1rTpv97_RAG_with_authorization_levels.ipynb", "cell_count": 11, "code_cells": 7, "markdown_cells": 4, "imports": ["chromadb"], "functions_defined": ["vector_search_with_rbac"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "intermediate"}]}, {"module_id": 8, "title": "Building the deployments & discussing business problems.", "schedule": "Sunday (3:30 - 5:30pm)", "notebooks": [{"title": "Evaluating LLMs", "colab_id": "1ohdsyb2athEkRawI2HF-6kBxhdFF8HTk", "colab_url": "https://colab.research.google.com/drive/1ohdsyb2athEkRawI2HF-6kBxhdFF8HTk", "context": "We will do evaluation & observability with Langfuse, deploy LLM applications to the cloud and understand business problems.\n\n1. [Evaluating LLMs](https://colab.research.google.com/drive/1ohdsyb2athEkRawI2HF-6kBxhdFF8HTk?usp=sharing)\n10. Langfuse based observability (3. advanced llm)\n11. Final assesssment and project", "concepts": ["llm", "evaluation"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1ohdsyb2athEkRawI2HF-6kBxhdFF8HTk_Evaluating_LLMs.ipynb", "cell_count": 25, "code_cells": 19, "markdown_cells": 6, "imports": ["langchain_groq", "non", "IPv4", "IPython.display", "Criteria", "EvaluatorType", "os", "torch", "HTML", "the", "userdata", "transformers", "ChatGroq", "South", "British", "AutoModelForCausalLM", "langchain.evaluation", "brutal", "google.colab", "load_evaluator"], "functions_defined": ["generate", "set_css"], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "advanced"}]}, {"module_id": 0, "title": null, "schedule": null, "notebooks": [{"title": "Reading Excel data", "colab_id": "1VWrfcB6rLIfTsg0g9uN_URwoGJWaG6ZC", "colab_url": "https://colab.research.google.com/drive/1VWrfcB6rLIfTsg0g9uN_URwoGJWaG6ZC", "context": "**Special notebooks:**\n\n1. [Reading Excel data](https://colab.research.google.com/drive/1VWrfcB6rLIfTsg0g9uN_URwoGJWaG6ZC?usp=sharing)\n2. [DICOM data](https://colab.research.google.com/drive/14nOmrtFXMvqxXI7lia67rQnwGf_BJUFN)", "concepts": ["llm", "ai", "rag"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/1VWrfcB6rLIfTsg0g9uN_URwoGJWaG6ZC_Reading_Excel_data.ipynb", "cell_count": 5, "code_cells": 4, "markdown_cells": 1, "imports": ["openpyxl", "load_workbook", "win32com.client"], "functions_defined": [], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "beginner"}, {"title": "DICOM data", "colab_id": "14nOmrtFXMvqxXI7lia67rQnwGf_BJUFN", "colab_url": "https://colab.research.google.com/drive/14nOmrtFXMvqxXI7lia67rQnwGf_BJUFN", "context": "1. [Reading Excel data](https://colab.research.google.com/drive/1VWrfcB6rLIfTsg0g9uN_URwoGJWaG6ZC?usp=sharing)\n2. [DICOM data](https://colab.research.google.com/drive/14nOmrtFXMvqxXI7lia67rQnwGf_BJUFN)", "concepts": ["llm", "ai", "rag"], "type": "practical", "downloaded_path": "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/courses/organized_content/downloaded_notebooks/14nOmrtFXMvqxXI7lia67rQnwGf_BJUFN_DICOM_data.ipynb", "cell_count": 7, "code_cells": 7, "markdown_cells": 0, "imports": ["userdata", "nibabel", "os", "<PERSON><PERSON><PERSON><PERSON>", "pydicom", "matplotlib.pyplot", "environ", "google.colab"], "functions_defined": [], "key_concepts": [], "estimated_time": "30 minutes", "difficulty": "intermediate"}]}], "setup_requirements": {"required": ["Ollama", "VSCode", "Git"], "optional": ["Miniconda"], "api_keys": ["Groq", "HuggingFace", "<PERSON><PERSON>", "Polygon", "<PERSON><PERSON>", "<PERSON><PERSON>"]}}