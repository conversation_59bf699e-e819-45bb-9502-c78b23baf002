# Project Structure Reorganization - August 12, 2025

## Overview

Reorganize project structure to eliminate scattered files and create maintainable architecture. **Database consolidation already complete** - authentication uses `leads.db`.

## Target Structure

```
new/
├── data/                    # All dynamic data
│   ├── database/           # databases (leads.db)
│   ├── logs/              # application logs
│   ├── imports/           # CSV files, data imports
│   ├── exports/           # generated reports
│   └── backups/           # database backups
├── shared/                 # Shared code
│   ├── models/            # data models
│   ├── config/            # configuration
│   └── utils/             # utilities
├── dashboard/             # Next.js application
│   ├── src/               # source code
│   └── public/            # web static assets
├── scripts/               # Python automation
│   ├── core/              # business logic scripts
│   └── data-operations/   # data processing
├── docs/                  # All documentation
│   ├── design/            # mockups, designs
│   ├── courses/           # course materials
│   └── technical/         # API docs, guides
└── temp/                  # temporary files
```

## Current Issues

1. **Orphaned Files**: `dashboard.db` unused (auth migrated to `leads.db`)
2. **Scattered Files**: Database, logs, assets in multiple locations  
3. **Duplicate Models**: `/dashboard/lib/models/` vs `/dashboard/models/`
4. **Mixed Concerns**: Setup scripts in dashboard folder

## Implementation Plan

### Phase 1: Clean Legacy Files
```bash
# Remove orphaned database
rm -rf dashboard/data/
rm dashboard/scripts/setup-database.js
```

### Phase 2: Create Structure & Move Files
```bash
# Create directories
mkdir -p data/{database,logs,imports,exports,backups}
mkdir -p shared/{models,config,utils}
mkdir -p dashboard/src/{components,pages,hooks,lib}
mkdir -p scripts/{core,data-operations,monitoring}
mkdir -p docs/{design,courses,technical}

# Move files
mv dashboard/database/leads.db* data/database/
mv logs/* data/logs/
mv assets/leads_data/* data/imports/
mv mockups/* docs/design/
mv assets/courses/* docs/courses/
```

### Phase 3: Code Organization
```bash
# Consolidate models
cp dashboard/models/*.js shared/models/
rm -rf dashboard/lib/models/

# Reorganize dashboard
mv dashboard/components/* dashboard/src/components/
mv dashboard/pages/* dashboard/src/pages/

# Reorganize scripts by function
mv scripts/lead_flow scripts/core/lead-management
mv scripts/email scripts/core/email-automation
mv scripts/stripe scripts/core/payment-processing
mv scripts/facebook scripts/core/facebook-integration
mv scripts/data_migration scripts/data-operations/migration
mv scripts/analysis scripts/data-operations/analysis
```

### Phase 4: Update References
```bash
# Update database paths (45+ files)
find scripts/ -name "*.py" -exec sed -i 's|../../dashboard/database/leads.db|../../data/database/leads.db|g' {} \;
find dashboard/ -name "*.js" -exec sed -i 's|database/leads.db|../data/database/leads.db|g' {} \;

# Update model imports
# Change: import { User } from '../lib/models/User.js';
# To: import { User } from '../../shared/models/User.js';
```

## Reorganization Script

```bash
#!/bin/bash
echo "🚀 Starting project reorganization..."

# Phase 1: Clean legacy
rm -rf dashboard/data/ 2>/dev/null || true
rm dashboard/scripts/setup-database.js 2>/dev/null || true

# Phase 2: Create structure & move files
mkdir -p data/{database,logs,imports,exports,backups}
mkdir -p shared/{models,config,utils}
mkdir -p dashboard/src/{components,pages,hooks,lib}
mkdir -p scripts/{core,data-operations,monitoring}
mkdir -p docs/{design,courses,technical}

mv dashboard/database/leads.db* data/database/ 2>/dev/null || true
mv logs/* data/logs/ 2>/dev/null || true
mv assets/leads_data/* data/imports/ 2>/dev/null || true
mv mockups/* docs/design/ 2>/dev/null || true

# Phase 3: Code organization
cp dashboard/models/*.js shared/models/ 2>/dev/null || true
rm -rf dashboard/lib/models/ 2>/dev/null || true

mv scripts/lead_flow scripts/core/lead-management 2>/dev/null || true
mv scripts/email scripts/core/email-automation 2>/dev/null || true
mv scripts/data_migration scripts/data-operations/migration 2>/dev/null || true

echo "✅ Structure created! Run Phase 4 to update file references."
```

## Timeline

- **Week 1**: Execute Phases 1-3 (structure & files)
- **Week 2**: Phase 4 (update references), test workflows  
- **Week 3**: Final testing, documentation updates

## Success Metrics

- [ ] Unified `data/` directory eliminates scattered files
- [ ] Shared models eliminate duplication  
- [ ] Clean `dashboard/src/` structure
- [ ] Scripts organized by business function
- [ ] All workflows continue functioning

---

*This reorganization creates a scalable foundation for the Modern AI Pro system.*