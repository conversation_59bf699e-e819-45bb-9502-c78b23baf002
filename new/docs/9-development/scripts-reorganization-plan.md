# Scripts Reorganization Plan

## Current Issues
- Inconsistent naming conventions (hyphen-case vs underscore_case)
- Incomplete requirements.txt missing major dependencies
- Hardcoded absolute paths throughout codebase
- Scattered configuration management
- Mixed concerns in directory structure

## Priority 1: Critical Fixes

### 1. Standardize Naming Conventions
```bash
# Rename to underscore_case
git mv core/email-automation → core/email_automation
git mv core/facebook-integration → core/facebook_integration  
git mv core/lead-management → core/lead_management
git mv core/payment-processing → integrations/stripe
git mv data-operations → data_operations
```

### 2. Fix Requirements Management
Create comprehensive requirements.txt with:
- facebook-business>=19.0.0
- stripe>=7.0.0
- openai>=1.0.0
- python-dotenv>=1.0.0
- flask>=3.0.0
- sqlalchemy>=2.0.0

### 3. Centralize Configuration
Create `config/` module:
- settings.py (central config)
- database.py (DB config)
- external_apis.py (API keys)
- logging_config.py (unified logging)

### 4. Remove Hardcoded Paths
Replace absolute paths with relative/environment-based paths using python-dotenv.

## Priority 2: Structure Improvements

### Reorganize Core Logic
```
core/
├── lead_processing/          # Unified lead management
├── integrations/             # External services (Facebook, Stripe, OpenAI)
└── monitoring/               # Health checks
```

### Enhance Data Operations
```
data_operations/
├── migration/               # Keep existing structure
├── validation/              # Move validation scripts
└── analytics/               # Move analysis scripts
```

## Implementation Notes
- Backup before changes: `cp -r scripts scripts_backup_$(date +%Y%m%d)`
- Use `git mv` to preserve file history
- Update import statements after directory renames
- Test all scripts after reorganization
- Update documentation references

## Success Criteria
- All directories use consistent underscore_case naming
- requirements.txt includes all actual dependencies
- No hardcoded absolute paths in codebase
- Centralized configuration system in place
- All scripts functional after reorganization