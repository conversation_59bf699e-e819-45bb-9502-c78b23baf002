## Installation Requirements

### 1. Cursor IDE

- Visit [cursor.sh](https://cursor.sh/) and install
- Sign in with GitHub account
### 2. Python Environment

- Install Python 3.9+ from [python.org](https://python.org/)
- Create environment:

```bash
python -m venv modern-ai-env
# Activate: Windows: modern-ai-env\Scripts\activate | Mac/Linux: source modern-ai-env/bin/activate
pip install jupyter notebook pandas numpy requests openai anthropic langchain streamlit fastapi
```

### 3. AWS Account

- Create account at [aws.amazon.com](https://aws.amazon.com/) (credit card required)
- Navigate to EC2 and Bedrock services in console
- Install AWS CLI v2

### 4. Communication Tools

- Join Slack: https://join.slack.com/t/modernaipractitioner/shared_invite/zt-3d2gv6a44-C~TMJChtthtqR9uXQaZYvw
- Access Google Colab: [colab.research.google.com](https://colab.research.google.com/)
- Test GPU runtime: Runtime → Change runtime type → GPU

## Setup Checklist

- [ ] Cursor IDE installed
- [ ] Ollama + 3 models downloaded
- [ ] Python environment with libraries
- [ ] AWS account with EC2/Bedrock access
- [ ] Slack workspace joined

**Hardware:** 8GB RAM minimum (16GB+ recommended), 20GB disk space

**Troubleshooting:** Post in #technical-support Slack channel with error details