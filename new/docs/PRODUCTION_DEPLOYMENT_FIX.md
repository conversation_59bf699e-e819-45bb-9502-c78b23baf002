# Production Authentication Fix

## Issue Identified 🔍
The authentication failures on `https://modernaipro.com/leads` (showing "Unassigned" and "failed to load users") are caused by **JWT_SECRET mismatch** between local development and production environments.

### Root Cause:
- **Local JWT_SECRET**: `your-super-secret-jwt-key-change-in-production` (placeholder)
- **Production JWT_SECRET**: Different value (as mentioned by user)
- **Result**: Tokens generated with one secret cannot be validated with another secret

## Immediate Fix Required ⚡

### 1. Update Production Environment Variables

Replace the production JWT_SECRET with this secure, randomly generated key:

```bash
JWT_SECRET=fe4eed07443f4058926dc4e71a291268a2d5b2fe57085311607115c27be73ebb71a75ebca6c7f6e0ed8bfaae0b7f2d6ad232770d4836577b4a1e16b564ab30f9
```

### 2. Production Deployment Steps

#### Option A: Update Server Environment Variables
```bash
# SSH into production server
ssh <EMAIL>

# Update .env file in production
cd /path/to/production/dashboard
nano .env

# Update JWT_SECRET line:
JWT_SECRET=fe4eed07443f4058926dc4e71a291268a2d5b2fe57085311607115c27be73ebb71a75ebca6c7f6e0ed8bfaae0b7f2d6ad232770d4836577b4a1e16b564ab30f9

# Restart the application
pm2 restart dashboard
# OR
npm run build && npm start
```

#### Option B: Environment Variables via Hosting Platform
If using a hosting platform (Vercel, Netlify, etc.):

1. Go to project settings
2. Add environment variable:
   - **Key**: `JWT_SECRET`
   - **Value**: `fe4eed07443f4058926dc4e71a291268a2d5b2fe57085311607115c27be73ebb71a75ebca6c7f6e0ed8bfaae0b7f2d6ad232770d4836577b4a1e16b564ab30f9`
3. Redeploy the application

### 3. Force User Re-authentication

After updating JWT_SECRET, **all existing user sessions will be invalid**. Users need to:

1. **Clear browser localStorage** (or force logout)
2. **Log in again** with new JWT_SECRET

#### Auto-logout Script (Optional)
Add this to login page to clear old tokens:
```javascript
// Clear old tokens on login page load
if (typeof window !== 'undefined') {
  localStorage.removeItem('access_token'); // Old token key
  localStorage.removeItem('mai_auth_token'); // New token key  
  localStorage.removeItem('mai_user_data'); // User data
}
```

## Verification Steps ✅

### 1. Check JWT_SECRET Loading
The middleware already has debug logging. Check server logs for:
```
🛡️ [MIDDLEWARE] Loading JWT_SECRET from environment...
🔍 [MIDDLEWARE] process.env.JWT_SECRET exists: true
🔑 [MIDDLEWARE] JWT_SECRET length: 128
🔑 [MIDDLEWARE] JWT_SECRET preview: fe4eed07443f4058...
```

### 2. Test Authentication Flow
1. Visit `https://modernaipro.com/login`
2. Log in with valid credentials
3. Should redirect to appropriate role-based page
4. Visit `https://modernaipro.com/leads`
5. Should show assignee names (not "Unassigned")
6. Assignment dropdown should show users (not "failed to load users")

### 3. API Endpoint Testing
```bash
# Test with curl (replace TOKEN with actual JWT)
curl -H "Authorization: Bearer TOKEN" https://modernaipro.com/api/users/assignable
# Should return: {"success": true, "users": [...]}

curl -H "Authorization: Bearer TOKEN" https://modernaipro.com/api/leads
# Should return leads with proper assignee data
```

## Security Best Practices 🔒

### 1. JWT_SECRET Requirements
✅ **Length**: 128+ characters (current: 128)  
✅ **Randomness**: Cryptographically secure (used Node.js crypto)  
✅ **Uniqueness**: Different from development  

### 2. Environment Variable Security
- Store in `.env` file (not in code)
- Add `.env` to `.gitignore` 
- Use different secrets for dev/staging/production
- Rotate secrets periodically

### 3. Token Management
- Set reasonable expiration times
- Implement token refresh if needed
- Clear tokens on logout
- Handle token expiry gracefully

## Troubleshooting 🔧

### If Still Getting "Unassigned" After Fix:
1. **Clear browser cache completely**
2. **Incognito/private browsing mode test**
3. **Check server logs** for JWT validation errors
4. **Verify .env file** is loaded correctly

### If Getting "Failed to load users":
1. **Check API endpoint** `/api/users/assignable` directly
2. **Verify database** has users with `role='sales'` and `is_active=1`
3. **Check rate limiting** (current limit: 30 requests per 15 minutes)

### Debug Mode
Temporarily add to `clientAuth.js` for production debugging:
```javascript
console.log('🔍 Auth Debug:', {
  hasToken: !!this.getToken(),
  tokenLength: this.getToken()?.length,
  isAuthenticated: this.isAuthenticated(),
  tokenKey: this.tokenKey
});
```

## Expected Outcome 🎯

After implementing this fix:
- ✅ `https://modernaipro.com/leads` shows proper assignee names
- ✅ Assignment dropdowns load users successfully  
- ✅ All API endpoints work consistently
- ✅ Authentication works the same as localhost

## Environment Sync (Optional)

To keep environments in sync, consider:
1. **Staging environment** with same JWT_SECRET as production
2. **Environment-specific config files**
3. **Automated deployment** with environment variable injection
4. **JWT secret rotation** process

---

**Priority**: 🚨 **CRITICAL** - This blocks core functionality  
**Estimated Fix Time**: 5-10 minutes  
**Requires**: Server access or hosting platform access  
**Impact**: All authenticated users will need to re-login after fix