# Modern AI Pro Documentation

## 📚 Documentation Structure

### 🎯 Strategic & Business
- **[1-overview/](1-overview/)** - Vision, strategy, and business context
  - [vision-and-strategy.md](1-overview/vision-and-strategy.md) - Strategic direction
  - [product-differentiation.md](1-overview/product-differentiation.md) - Market positioning
  - [scaled-education-business-plan.md](1-overview/scaled_education_business_plan.md) - Growth strategy
- **[2-product-specs/](2-product-specs/)** - Business requirements and product specifications
  - [user_stories.md](2-product-specs/user_stories.md) - Comprehensive user stories and acceptance criteria
  - [product_overview.md](2-product-specs/product_overview.md) - Product vision, components, and business requirements

### ⚙️ Technical Implementation  
- **[3-technical-implementation/](3-technical-implementation/)** - Technical architecture and implementation details
  - [system_architecture.md](3-technical-implementation/system_architecture.md) - System architecture and implementation timeline
  - [technical-specs.md](3-technical-implementation/technical-specs.md) - Technical specifications overview
  - [database-schema-complete.md](3-technical-implementation/database-schema-complete.md) - Complete database design
  - [mvp_roadmap.md](3-technical-implementation/mvp_roadmap.md) - Implementation roadmap and timelines
  - [facebook-lead-collection.md](3-technical-implementation/facebook-lead-collection.md) - Facebook integration
  - [payment_system_integration.md](3-technical-implementation/payment_system_integration.md) - Stripe payment processing
  - [email_tracking_architecture.md](3-technical-implementation/email_tracking_architecture.md) - Email tracking system
  - [dashboard_implementation.md](3-technical-implementation/dashboard_implementation.md) - Dashboard technical specs
  - [rate-limiting-strategy.md](3-technical-implementation/rate-limiting-strategy.md) - API rate limiting
- **[4-api-reference/](4-api-reference/)** - API documentation
  - [api-overview.md](4-api-reference/api-overview.md) - API introduction and flows
  - [endpoints-reference.md](4-api-reference/endpoints-reference.md) - Complete endpoint reference
  - [authentication.md](4-api-reference/authentication.md) - Authentication flows
  - [data-models.md](4-api-reference/data-models.md) - Data schemas and models
- **[5-scripts-reference/](5-scripts-reference/)** - Automation scripts documentation
  - [scripts-overview.md](5-scripts-reference/scripts-overview.md) - Scripts introduction
  - [facebook-automation.md](5-scripts-reference/facebook-automation.md) - Facebook Ads automation
  - [payment-processing.md](5-scripts-reference/payment-processing.md) - Stripe integration
  - [email-campaigns.md](5-scripts-reference/email-campaigns.md) - Email automation
  - [utilities-reference.md](5-scripts-reference/utilities-reference.md) - Utility scripts and tools

### 📖 Operations & User Guides
- **[6-usage-guide/](6-usage-guide/)** - Deployment, operations, and user workflows
  - [deployment-guide.md](6-usage-guide/deployment-guide.md) - Complete deployment guide (local & Azure)
  - [troubleshooting.md](6-usage-guide/troubleshooting.md) - Problem diagnosis and solutions
  - [monitoring-guide.md](6-usage-guide/monitoring-guide.md) - System monitoring and alerts

### 📊 Marketing & Content
- **[7-marketing/](7-marketing/)** - Marketing materials and campaigns
  - [AI_Essentials_Marketing_Brief.md](7-marketing/AI_Essentials_Marketing_Brief.md) - Marketing strategy
  - [user-workflows.md](7-marketing/user-workflows.md) - Dashboard workflows
  - [twilio_lead_reachout_plan.md](7-marketing/twilio_lead_reachout_plan.md) - Lead outreach strategy
- **[8-courses/](8-courses/)** - Educational content and materials  
  - [AI_Essentials_North_America.md](8-courses/AI_Essentials_North_America.md) - Course curriculum
  - [Modern_AI_Pro_Course_Navigator.md](8-courses/Modern_AI_Pro_Course_Navigator.md) - Learning paths
  - [Modern_AI_Pro_Master_Study_Guide.md](8-courses/Modern_AI_Pro_Master_Study_Guide.md) - Study resources
  - [organized_content/](8-courses/organized_content/) - Course notebooks and materials

### 🛠️ Development & Design
- **[9-development/](9-development/)** - Development documentation and processes
  - [db-migration-Aug-12.md](9-development/db-migration-Aug-12.md) - Database migration documentation
- **[10-design/](10-design/)** - UI/UX design specifications and mockups
  - [00-mockup-overview.md](10-design/00-mockup-overview.md) - Design overview
  - [01-main-dashboard.md](10-design/01-main-dashboard.md) - Main dashboard design
  - [02-leads-management.md](10-design/02-leads-management.md) - Leads management interface
  - [03-campaign-analytics.md](10-design/03-campaign-analytics.md) - Analytics dashboard design
  - [04-certificate-management.md](10-design/04-certificate-management.md) - Certificate system design
  - [05-mobile-responsive.md](10-design/05-mobile-responsive.md) - Mobile responsive design
  - [06-revenue-customer-success.md](10-design/06-revenue-customer-success.md) - Revenue and customer success views

### 🗂️ Archive
- **[archive/](archive/)** - Historical documents and migration records
  - Migration documentation
  - Deprecated specifications

## 🚀 Quick Start Guides

### For Developers
1. **New Developer Setup**: [deployment-guide.md](6-usage-guide/deployment-guide.md)
2. **API Integration**: [api-overview.md](4-api-reference/api-overview.md)
3. **Script Operations**: [scripts-overview.md](5-scripts-reference/scripts-overview.md)

### For Business Users
1. **System Overview**: [vision-and-strategy.md](1-overview/vision-and-strategy.md)
2. **User Workflows**: [user-workflows.md](7-marketing/user-workflows.md)
3. **Product Roadmap**: [dashboard-prd.md](2-product-specs/dashboard_prd.md)

### For System Administrators
1. **Deployment Guide**: [deployment-guide.md](6-usage-guide/deployment-guide.md)
2. **Facebook Automation**: [facebook-automation.md](5-scripts-reference/facebook-automation.md)
3. **Database Management**: [data-models.md](4-api-reference/data-models.md)

## 🔧 System Components

### Core Applications
- **Dashboard**: Next.js web application for lead and customer management
- **Scripts**: Python automation for Facebook, Stripe, and email operations
- **Database**: SQLite database with leads, payments, and campaign data

### External Integrations
- **Facebook Ads API**: Lead collection and campaign management
- **Stripe API**: Payment processing and customer management
- **Gmail API**: Automated email campaigns
- **Google Sheets API**: Data import/export

## 📋 Documentation Conventions

### File Naming
- Use **kebab-case** for all markdown files: `api-reference.md`
- Descriptive names that clearly indicate content purpose
- Avoid abbreviations where possible

### Directory Structure
- Numbered prefixes for logical ordering: `1-overview/`, `2-product-specs/`
- Clear separation between business, technical, and operational content
- Archive folder for historical/deprecated documentation

### Content Organization
- Each major topic gets its own directory
- Split large documents into focused, smaller files
- Cross-reference related documentation
- Maintain consistent formatting and style

## 🔄 Maintenance

This documentation is actively maintained and updated as the system evolves. 

- **Last Updated**: 2025-08-12
- **Review Schedule**: Weekly for technical docs, monthly for business docs
- **Contribution Guidelines**: Follow existing naming conventions and structure

For questions or suggestions about documentation, please open an issue or contact the development team.