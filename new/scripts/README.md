# Modern AI Pro Scripts Directory

This directory contains all automation scripts, utilities, and tools for the Modern AI Pro lead management system.

## Directory Structure

### Core Business Logic
- **`core/lead_processing/`** - Unified lead management and processing
  - `email_automation/` - Email campaigns and automated communications  
  - `workflow/` - Lead workflow management and orchestration
  - `orchestrator/` - Central coordination logic
- **`config/`** - Centralized configuration management
  - `settings.py` - Application settings and paths
  - `database.py` - Database connection management
  - `external_apis.py` - API keys and external service configuration
  - `logging_config.py` - Unified logging setup

### External Integrations
- **`integrations/`** - External service integrations
  - `facebook/` - Facebook Lead Ads API integration
  - `stripe/` - Stripe payment processing and synchronization
  - `openai/` - OpenAI/LLM integration scripts

### Data Operations
- **`data_operations/`** - Data processing and management
  - `migration/` - Lead migration from various sources
    - `migrators/` - Core migration scripts
    - `analyzers/` - Data analysis and validation
    - `reports/` - Generated analysis reports
  - `validation/` - Data validation and integrity checks
  - `analytics/` - Business intelligence and reporting

### Support Infrastructure
- **`utilities/`** - General utility scripts and helper functions
- **`tests/`** - Testing modules and test scripts
- **`monitoring/`** - System monitoring and health checks
- **`gdocs_colab/`** - Google Docs and Colab integration tools
- **`venv/`** - Python virtual environment

## Recent Migration Summary

The scripts in this directory were recently reorganized following a major lead migration effort that imported **9,726 new leads** from 7 different sources:
- HubSpot leads migration
- Legacy CSV data migration
- India-specific leads processing
- US Lead CRM integration
- Excel data analysis and imports
- Workshop connection establishment
- Duplicate analysis and cleanup

## Quick Start

### 1. Environment Setup
```bash
# Activate virtual environment
source venv/bin/activate

# Install all dependencies
pip install -r requirements.txt
```

### 2. Configuration
Scripts use centralized configuration from the `config/` module:
```python
from config import settings, api_config, setup_logging

# Access database path
db_path = settings.database_path

# Access API keys
stripe_key = api_config.stripe_secret_key

# Setup logging
logger = setup_logging('script_name')
```

### 3. Common Usage Patterns
```bash
# Facebook health check
python integrations/facebook/fb_account_manager.py health-check

# Stripe payment sync
python integrations/stripe/sync_stripe_payments.py --days 7

# Database validation
python data_operations/validation/validate_enrichment.py
```

## Configuration Management

### Environment Variables
Set up your `.env` file in `../config/.env`:
```
FB_EXTENDED_TOKEN=your_token
STRIPE_SECRET_KEY=your_key
OPENAI_API_KEY=your_key
```

### Path Resolution
All scripts now use centralized path management:
- Database: `settings.database_path`
- Logs: `settings.logs_dir` 
- Configuration: `settings.config_root`

## Architecture Improvements

**Recent Reorganization (August 12, 2025):**
- ✅ Standardized naming conventions (underscore_case)
- ✅ Centralized configuration management
- ✅ Removed hardcoded absolute paths
- ✅ Logical separation of concerns
- ✅ Comprehensive dependency management

This reorganization eliminates technical debt and establishes a maintainable foundation for future development.