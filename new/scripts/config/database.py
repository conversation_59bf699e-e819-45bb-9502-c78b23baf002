"""Database configuration."""

import sqlite3
import os
from .settings import settings

class DatabaseConfig:
    """Database configuration and connection management."""
    
    @staticmethod
    def get_connection() -> sqlite3.Connection:
        """Get a database connection."""
        os.makedirs(os.path.dirname(settings.database_path), exist_ok=True)
        conn = sqlite3.connect(settings.database_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    @staticmethod
    def execute_query(query: str, params: tuple = None) -> list:
        """Execute a query and return results."""
        with DatabaseConfig.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.fetchall()
    
    @staticmethod
    def execute_insert(query: str, params: tuple) -> int:
        """Execute an insert query and return the row ID."""
        with DatabaseConfig.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.lastrowid