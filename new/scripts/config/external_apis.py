"""External API configuration."""

import os

class ExternalAPIConfig:
    """Configuration for external APIs."""
    
    # Facebook API
    @property
    def facebook_extended_token(self) -> str:
        return os.getenv('FB_EXTENDED_TOKEN', '')
    
    @property
    def facebook_app_secret(self) -> str:
        return os.getenv('FB_APP_SECRET', '')
    
    @property
    def facebook_app_id(self) -> str:
        return os.getenv('FB_APP_ID', '')
    
    # Stripe API
    @property
    def stripe_secret_key(self) -> str:
        return os.getenv('STRIPE_SECRET_KEY', '')
    
    @property
    def stripe_webhook_secret(self) -> str:
        return os.getenv('STRIPE_WEBHOOK_SECRET', '')
    
    # OpenAI API
    @property
    def openai_api_key(self) -> str:
        return os.getenv('OPENAI_API_KEY', '')
    
    # Google Services
    @property
    def google_service_account_path(self) -> str:
        return os.getenv('GOOGLE_SERVICE_ACCOUNT_PATH', '')

# Global API config instance
api_config = ExternalAPIConfig()