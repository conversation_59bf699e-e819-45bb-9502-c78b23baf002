"""Central configuration settings."""

import os
from pathlib import Path
from dotenv import load_dotenv

class Settings:
    """Central configuration settings."""
    
    def __init__(self):
        # Get the project root directory
        self.PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
        self.SCRIPTS_ROOT = self.PROJECT_ROOT / "new" / "scripts"
        self.CONFIG_ROOT = self.PROJECT_ROOT / "new" / "config"
        self.DASHBOARD_ROOT = self.PROJECT_ROOT / "new" / "dashboard"
        
        # Load environment variables
        env_path = self.CONFIG_ROOT / ".env"
        if env_path.exists():
            load_dotenv(env_path)
        
        # Database paths (use main data directory)
        self.DATABASE_DIR = self.PROJECT_ROOT / "new" / "data" / "database"
        self.LEADS_DB_PATH = self.DATABASE_DIR / "leads.db"
        
        # Logs directory
        self.LOGS_DIR = self.PROJECT_ROOT / "new" / "data" / "logs"
        self.LOGS_DIR.mkdir(exist_ok=True)
        
        # External data directory
        self.DATA_DIR = self.PROJECT_ROOT / "new" / "data"
        self.DATA_DIR.mkdir(exist_ok=True)
    
    @property
    def database_path(self) -> str:
        """Get the absolute path to the database."""
        return str(self.LEADS_DB_PATH)
    
    @property
    def logs_dir(self) -> str:
        """Get the absolute path to logs directory."""
        return str(self.LOGS_DIR)

# Global settings instance
settings = Settings()