#!/usr/bin/env python3
"""
Email Campaign Manager
High-level email campaign orchestration using Lead Selector and Email Library
"""

import os
import sys
import sqlite3
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Import our core services
from lead_selector import LeadSelector
from email_library import <PERSON><PERSON><PERSON><PERSON><PERSON>, EmailConfig, EmailMessage
from enhanced_email_composer import EnhancedEmailComposer

# Import SDR configuration utility
sys.path.append(str(Path(__file__).parent.parent / "utilities"))
from sdr_config import get_assignment_email_map, get_sdr_configs

class EmailCampaignManager:
    """High-level email campaign orchestration"""
    
    def __init__(self, db_path: str, test_mode: bool = True):
        self.db_path = db_path
        self.test_mode = test_mode
        self.lead_selector = LeadSelector(db_path)
        
        # Load assignment to email mapping from database
        self.ASSIGNMENT_EMAIL_MAP = get_assignment_email_map(db_path)
        
        # Load SDR configurations from database
        self.SDR_CONFIGS = get_sdr_configs(db_path)
        
        # Log file
        self.log_file = Path(__file__).parent.parent.parent / "data" / "logs" / "email_campaigns.log"
    
    def get_sender_email_from_assignment(self, assigned_to: int) -> str:
        """Get sender email based on lead assignment"""
        return self.ASSIGNMENT_EMAIL_MAP.get(assigned_to, self.ASSIGNMENT_EMAIL_MAP.get(None, "<EMAIL>"))
    
    def get_sdr_config(self, email: str) -> Optional[Dict]:
        """Get SDR configuration by email"""
        for sdr_key, config in self.SDR_CONFIGS.items():
            if config['email'] == email:
                return config
        return None
    
    def get_leads_for_campaign(self, 
                              status: str = "New", 
                              hours_back: int = 24, 
                              limit: int = 10) -> List[Dict]:
        """Get leads for email campaign using Lead Selector"""
        try:
            if status == "New":
                # Get recent unprocessed leads
                leads = self.lead_selector.get_recent_unprocessed_leads(
                    hours=hours_back, 
                    limit=limit
                )
            else:
                # Get leads by specific status
                leads = self.lead_selector.get_leads_by_status(
                    status=status, 
                    limit=limit, 
                    include_interactions=True
                )
            
            return leads if isinstance(leads, list) else []
            
        except Exception as e:
            print(f"❌ Error getting leads for campaign: {e}")
            return []
    
    def create_composer_for_lead(self, lead: Dict) -> EnhancedEmailComposer:
        """Create email composer with correct sender for lead"""
        sender_email = self.get_sender_email_from_assignment(lead.get('assigned_to'))
        return EnhancedEmailComposer(
            self.db_path, 
            test_mode=self.test_mode, 
            sender_email=sender_email
        )
    
    def send_email_to_lead(self, lead: Dict, composer: EnhancedEmailComposer) -> Dict:
        """Send email to a single lead and log interaction"""
        try:
            # Compose email
            subject, body, generation_method = composer.compose_email(lead)
            
            if self.test_mode:
                # Log composition in test mode
                success = composer._log_email_composition(lead, subject, body, generation_method)
                result = {
                    'success': success,
                    'mode': 'test',
                    'lead_id': lead['id'],
                    'generation_method': generation_method,
                    'message': f"Email composition logged (using {generation_method})"
                }
            else:
                # Actually send email in production mode
                success = composer.send_email(lead, subject, body)
                result = {
                    'success': success,
                    'mode': 'production',
                    'lead_id': lead['id'],
                    'generation_method': generation_method,
                    'message': f"Email {'sent successfully' if success else 'sending failed'} (using {generation_method})"
                }
            
            # Log to campaign file
            self.log_campaign_activity(lead, subject, success, generation_method, composer.sender_email)
            
            return result
            
        except Exception as e:
            error_result = {
                'success': False,
                'mode': 'test' if self.test_mode else 'production',
                'lead_id': lead['id'],
                'generation_method': 'error',
                'message': f"Error processing lead: {e}"
            }
            self.log_campaign_activity(lead, "ERROR", False, "error", "unknown")
            return error_result
    
    def log_campaign_activity(self, lead: Dict, subject: str, success: bool, method: str, sender_email: str):
        """Log campaign activity to file"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Ensure log directory exists
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Check if file is empty to add header
        add_header = not self.log_file.exists() or self.log_file.stat().st_size == 0
        
        if add_header:
            header = f"{'TIMESTAMP':<20} | {'SENDER':<25} | {'LEAD_ID':<8} | {'LEAD_NAME':<20} | {'LEAD_EMAIL':<25} | {'STATUS':<8} | {'METHOD':<15} | {'MODE':<10}\n"
            separator = f"{'-'*20}-+-{'-'*25}-+-{'-'*8}-+-{'-'*20}-+-{'-'*25}-+-{'-'*8}-+-{'-'*15}-+-{'-'*10}\n"
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write(header)
                f.write(separator)
        
        # Format data for table
        lead_name = lead.get('full_name', 'Unknown')[:19]
        lead_email = lead.get('email', 'Unknown')[:24]
        sender = sender_email[:24]
        status = 'SUCCESS' if success else 'FAILED'
        method = method[:14]
        mode = 'TEST' if self.test_mode else 'PROD'
        
        log_entry = f"{timestamp:<20} | {sender:<25} | {lead['id']:<8} | {lead_name:<20} | {lead_email:<25} | {status:<8} | {method:<15} | {mode:<10}\n"
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
    
    def run_campaign(self, 
                    status: str = "New", 
                    max_emails: int = 5, 
                    hours_back: int = 24) -> Dict:
        """Run email campaign for leads"""
        print(f"🚀 Starting email campaign (status: {status}, max: {max_emails}, mode: {'TEST' if self.test_mode else 'PRODUCTION'})")
        
        # Get leads for campaign
        leads = self.get_leads_for_campaign(status, hours_back, max_emails)
        print(f"📋 Found {len(leads)} leads for campaign")
        
        if not leads:
            return {
                "status": "no_leads",
                "message": "No leads found for campaign",
                "total": 0,
                "successful": 0,
                "failed": 0
            }
        
        results = []
        success_count = 0
        failed_count = 0
        
        for i, lead in enumerate(leads, 1):
            print(f"\n📤 Processing lead {i}/{len(leads)}: {lead.get('full_name', 'Unknown')} ({lead.get('email', 'Unknown')})")
            print(f"   Assignment: {lead.get('assigned_to', 'Unassigned')} | Priority: {lead.get('priority_score', 'N/A')} | Location: {lead.get('detected_location', 'Unknown')}")
            
            try:
                # Create composer with correct sender
                composer = self.create_composer_for_lead(lead)
                print(f"   Sender: {composer.sender_email}")
                
                # Send email
                result = self.send_email_to_lead(lead, composer)
                results.append(result)
                
                if result['success']:
                    success_count += 1
                    print(f"   ✅ {result['message']}")
                else:
                    failed_count += 1
                    print(f"   ❌ {result['message']}")
                
                # Rate limiting for production mode
                if not self.test_mode and i < len(leads):
                    print("   ⏱️ Rate limiting: waiting 2 seconds...")
                    time.sleep(2)
                    
            except Exception as e:
                failed_count += 1
                print(f"   ❌ Error processing lead: {e}")
                results.append({
                    'success': False,
                    'lead_id': lead['id'],
                    'message': f"Exception: {e}"
                })
        
        campaign_result = {
            "status": "completed",
            "total": len(leads),
            "successful": success_count,
            "failed": failed_count,
            "mode": "test" if self.test_mode else "production",
            "results": results
        }
        
        print(f"\n📊 Campaign completed: {success_count} successful, {failed_count} failed")
        return campaign_result
    
    def run_assignment_campaign(self, 
                               assigned_to: int, 
                               max_emails: int = 10, 
                               hours_back: int = 168) -> Dict:
        """Run campaign for leads assigned to specific user"""
        print(f"🎯 Running assignment-based campaign for user {assigned_to}")
        
        # Get leads by criteria
        leads = self.lead_selector.get_leads_by_criteria(
            status="New",
            hours_back=hours_back,
            assigned_to=str(assigned_to),
            limit=max_emails,
            include_interactions=True
        )
        
        if not leads or (len(leads) == 1 and 'error' in leads[0]):
            return {
                "status": "no_leads",
                "message": f"No leads found for assignment {assigned_to}",
                "total": 0,
                "successful": 0,
                "failed": 0
            }
        
        print(f"📋 Found {len(leads)} leads assigned to user {assigned_to}")
        
        # Process leads (reuse campaign logic)
        results = []
        success_count = 0
        failed_count = 0
        
        sender_email = self.get_sender_email_from_assignment(assigned_to)
        print(f"📧 Using sender: {sender_email}")
        
        for i, lead in enumerate(leads, 1):
            print(f"\n📤 Processing lead {i}/{len(leads)}: {lead.get('full_name', 'Unknown')}")
            
            try:
                composer = EnhancedEmailComposer(self.db_path, self.test_mode, sender_email)
                result = self.send_email_to_lead(lead, composer)
                results.append(result)
                
                if result['success']:
                    success_count += 1
                    print(f"   ✅ {result['message']}")
                else:
                    failed_count += 1
                    print(f"   ❌ {result['message']}")
                
                # Rate limiting
                if not self.test_mode and i < len(leads):
                    time.sleep(2)
                    
            except Exception as e:
                failed_count += 1
                print(f"   ❌ Error: {e}")
        
        return {
            "status": "completed",
            "total": len(leads),
            "successful": success_count,
            "failed": failed_count,
            "assigned_to": assigned_to,
            "sender_email": sender_email,
            "mode": "test" if self.test_mode else "production",
            "results": results
        }
    
    def get_campaign_summary(self) -> Dict:
        """Get campaign statistics from Lead Selector"""
        return self.lead_selector.get_lead_statistics()

def main():
    """CLI entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Email Campaign Manager')
    parser.add_argument('--db-path', 
                       default='/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db',
                       help='Path to database file')
    parser.add_argument('--production', action='store_true', 
                       help='Run in production mode (actually send emails)')
    parser.add_argument('--status', default='New',
                       help='Lead status to target (New, Contacted, Customer, etc.)')
    parser.add_argument('--max-emails', type=int, default=5,
                       help='Maximum number of emails to send')
    parser.add_argument('--hours-back', type=int, default=24,
                       help='Hours to look back for leads')
    parser.add_argument('--assigned-to', type=int,
                       help='Run campaign for specific assignment ID')
    parser.add_argument('--stats', action='store_true',
                       help='Show campaign statistics only')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.db_path):
        print(f"❌ Database not found: {args.db_path}")
        return 1
    
    manager = EmailCampaignManager(args.db_path, test_mode=not args.production)
    
    if args.stats:
        stats = manager.get_campaign_summary()
        print(f"📊 Campaign Statistics:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
        return 0
    
    if args.assigned_to:
        result = manager.run_assignment_campaign(
            assigned_to=args.assigned_to,
            max_emails=args.max_emails,
            hours_back=args.hours_back
        )
    else:
        result = manager.run_campaign(
            status=args.status,
            max_emails=args.max_emails,
            hours_back=args.hours_back
        )
    
    print(f"\n📊 Final result: {result}")
    return 0 if result['status'] in ['completed', 'no_leads'] else 1

if __name__ == "__main__":
    exit(main())