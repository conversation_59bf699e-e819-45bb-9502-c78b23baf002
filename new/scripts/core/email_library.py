#!/usr/bin/env python3
"""
Modern AI Pro Email Library
A comprehensive email automation library supporting:
- Plain text and HTML emails
- File attachments (PDF, images, documents)
- Service account authentication with domain delegation
- Rate limiting and error handling
- Template system for email content

Author: Modern AI Pro Team
"""

import os
import sys
import base64
import time
import mimetypes
from typing import List, Dict, Optional, Union, Tuple
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email.mime.image import MIMEImage
from email.mime.audio import MIMEAudio
from email.mime.application import MIMEApplication
from email import encoders
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

class EmailConfig:
    """Email configuration class"""
    
    def __init__(self, service_account_file: str = None, sender_email: str = None):
        # Calculate relative path to config directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        # From core/ go up to new/ then into config/
        config_dir = os.path.join(script_dir, '..', '..', 'config')
        
        self.service_account_file = service_account_file or os.path.join(
            config_dir, 'modernaipro-b601d7749092.json'
        )
        self.sender_email = sender_email or '<EMAIL>'
        self.scopes = ['https://www.googleapis.com/auth/gmail.send']

class EmailAttachment:
    """Class to represent email attachments"""
    
    def __init__(self, file_path: str, filename: str = None, content_type: str = None):
        self.file_path = file_path
        self.filename = filename or os.path.basename(file_path)
        self.content_type = content_type or self._detect_content_type()
        
    def _detect_content_type(self) -> str:
        """Auto-detect content type based on file extension"""
        content_type, _ = mimetypes.guess_type(self.file_path)
        return content_type or 'application/octet-stream'
    
    def exists(self) -> bool:
        """Check if the attachment file exists"""
        return os.path.exists(self.file_path)
    
    def size(self) -> int:
        """Get file size in bytes"""
        return os.path.getsize(self.file_path) if self.exists() else 0
    
    def size_mb(self) -> float:
        """Get file size in MB"""
        return self.size() / (1024 * 1024)

class EmailMessage:
    """Class to represent an email message"""
    
    def __init__(self, 
                 to: Union[str, List[str]], 
                 subject: str,
                 body: str,
                 sender: str = None,
                 reply_to: str = None,
                 cc: Union[str, List[str]] = None,
                 bcc: Union[str, List[str]] = None,
                 html: bool = True,
                 attachments: List[EmailAttachment] = None):
        
        self.to = to if isinstance(to, list) else [to]
        self.subject = subject
        self.body = body
        self.sender = sender
        self.reply_to = reply_to
        self.cc = cc if isinstance(cc, list) else ([cc] if cc else [])
        self.bcc = bcc if isinstance(bcc, list) else ([bcc] if bcc else [])
        self.html = html
        self.attachments = attachments or []
    
    def add_attachment(self, attachment: EmailAttachment):
        """Add an attachment to the email"""
        self.attachments.append(attachment)
    
    def add_attachment_from_path(self, file_path: str, filename: str = None):
        """Add an attachment from file path"""
        attachment = EmailAttachment(file_path, filename)
        self.add_attachment(attachment)
    
    def total_attachment_size_mb(self) -> float:
        """Get total size of all attachments in MB"""
        return sum(att.size_mb() for att in self.attachments)

class ModernAIEmailer:
    """Main email automation class"""
    
    def __init__(self, config: EmailConfig = None):
        self.config = config or EmailConfig()
        self._service = None
        self._last_send_time = 0
        self.rate_limit_seconds = 1  # Default rate limit
    
    def _get_service(self):
        """Get authenticated Gmail service"""
        if self._service is None:
            try:
                credentials = service_account.Credentials.from_service_account_file(
                    self.config.service_account_file, scopes=self.config.scopes)
                
                # Use domain-wide delegation
                delegated_credentials = credentials.with_subject(self.config.sender_email)
                self._service = build('gmail', 'v1', credentials=delegated_credentials)
                
            except Exception as error:
                raise Exception(f"Failed to authenticate Gmail service: {error}")
        
        return self._service
    
    def _create_mime_message(self, email_message: EmailMessage) -> Dict:
        """Create MIME message from EmailMessage object"""
        message = MIMEMultipart()
        
        # Set headers
        message['to'] = ', '.join(email_message.to)
        message['from'] = email_message.sender or self.config.sender_email
        message['subject'] = email_message.subject
        
        if email_message.reply_to:
            message['reply-to'] = email_message.reply_to
        
        if email_message.cc:
            message['cc'] = ', '.join(email_message.cc)
        
        if email_message.bcc:
            message['bcc'] = ', '.join(email_message.bcc)
        
        # Add body
        body_type = 'html' if email_message.html else 'plain'
        msg_body = MIMEText(email_message.body, body_type)
        message.attach(msg_body)
        
        # Add attachments
        for attachment in email_message.attachments:
            if not attachment.exists():
                print(f"⚠️ Warning: Attachment file not found: {attachment.file_path}")
                continue
            
            try:
                self._attach_file(message, attachment)
                print(f"✅ Attachment added: {attachment.filename} ({attachment.size_mb():.2f} MB)")
            except Exception as e:
                print(f"❌ Failed to attach {attachment.filename}: {e}")
        
        # Encode message
        raw = base64.urlsafe_b64encode(message.as_bytes()).decode()
        return {'raw': raw}
    
    def _attach_file(self, message: MIMEMultipart, attachment: EmailAttachment):
        """Attach a file to the message"""
        with open(attachment.file_path, 'rb') as file:
            file_data = file.read()
        
        # Determine MIME type and create appropriate attachment
        main_type, sub_type = attachment.content_type.split('/', 1)
        
        if main_type == 'text':
            part = MIMEText(file_data.decode(), sub_type)
        elif main_type == 'image':
            part = MIMEImage(file_data, sub_type)
        elif main_type == 'audio':
            part = MIMEAudio(file_data, sub_type)
        elif main_type == 'application':
            part = MIMEApplication(file_data, sub_type)
        else:
            part = MIMEBase(main_type, sub_type)
            part.set_payload(file_data)
            encoders.encode_base64(part)
        
        # Add header for attachment
        part.add_header(
            'Content-Disposition',
            f'attachment; filename="{attachment.filename}"'
        )
        
        message.attach(part)
    
    def _rate_limit(self):
        """Apply rate limiting between emails"""
        current_time = time.time()
        time_since_last = current_time - self._last_send_time
        
        if time_since_last < self.rate_limit_seconds:
            sleep_time = self.rate_limit_seconds - time_since_last
            print(f"⏱️ Rate limiting: waiting {sleep_time:.1f} seconds...")
            time.sleep(sleep_time)
        
        self._last_send_time = time.time()
    
    def send_email(self, email_message: EmailMessage, apply_rate_limit: bool = True) -> bool:
        """Send a single email"""
        try:
            if apply_rate_limit:
                self._rate_limit()
            
            service = self._get_service()
            mime_message = self._create_mime_message(email_message)
            
            result = service.users().messages().send(
                userId='me', body=mime_message).execute()
            
            print(f"✅ Email sent successfully - ID: {result['id']}")
            return True
            
        except HttpError as error:
            print(f"❌ Gmail API error: {error}")
            return False
        except Exception as error:
            print(f"❌ Error sending email: {error}")
            return False
    
    def send_bulk_emails(self, 
                        recipients: List[Tuple[str, str]], 
                        subject: str,
                        body_template: str,
                        attachments: List[EmailAttachment] = None,
                        html: bool = True,
                        rate_limit_seconds: float = 2.0) -> Dict[str, int]:
        """
        Send bulk emails to multiple recipients
        
        Args:
            recipients: List of (email, name) tuples
            subject: Email subject
            body_template: Email body template (use {name} for personalization)
            attachments: List of attachments to include
            html: Whether body is HTML
            rate_limit_seconds: Seconds to wait between emails
        
        Returns:
            Dictionary with success/failure counts
        """
        self.rate_limit_seconds = rate_limit_seconds
        
        results = {"total": len(recipients), "success": 0, "failed": 0}
        
        for i, (email, name) in enumerate(recipients, 1):
            print(f"\n📤 Sending email {i}/{len(recipients)} to: {name} <{email}>")
            
            # Personalize body
            personalized_body = body_template.format(name=name, email=email)
            
            # Create email message
            email_msg = EmailMessage(
                to=email,
                subject=subject,
                body=personalized_body,
                html=html,
                attachments=attachments or []
            )
            
            # Send email
            if self.send_email(email_msg, apply_rate_limit=(i > 1)):
                results["success"] += 1
            else:
                results["failed"] += 1
        
        return results
    
    def validate_config(self) -> bool:
        """Validate email configuration"""
        if not os.path.exists(self.config.service_account_file):
            print(f"❌ Service account file not found: {self.config.service_account_file}")
            return False
        
        try:
            self._get_service()
            print("✅ Email configuration validated successfully")
            return True
        except Exception as e:
            print(f"❌ Email configuration validation failed: {e}")
            return False

class EmailTemplates:
    """Pre-built email templates"""
    
    @staticmethod
    def test_email(recipient_name: str = "there") -> str:
        """Simple test email template"""
        first_name = recipient_name.split()[0] if recipient_name else "there"
        
        return f"""<html><body>
<p>Hi {first_name},</p>

<p>This is a test email from the Modern AI Pro email automation system.</p>

<p><strong>Test Details:</strong></p>
<ul>
    <li>Email system: Working ✅</li>
    <li>Authentication: Service account</li>
    <li>Test time: {time.strftime('%Y-%m-%d %H:%M:%S')}</li>
</ul>

<p>If you received this email, the email automation is functioning correctly.</p>

<p>Best regards,<br>
Modern AI Pro Email System</p>
</body></html>"""
    
    @staticmethod
    def sales_email(recipient_name: str = "there") -> str:
        """Sales email template"""
        first_name = recipient_name.split()[0] if recipient_name else "there"
        
        return f"""<html><body>
<p>Hi {first_name},</p>

<p><strong>4 days left, 4 spots remaining</strong></p>

<p>Our AI Bootcamp with Dr. Balaji starts <strong>August 15th</strong> - this is your final chance to join.</p>

<p><strong>What you'll build:</strong> 5 interactive mini-projects covering real AI applications (LLMs, chatbots, RAG systems). Build a strong grasp of both AI foundations and enterprise implementation - combining AI strategy with hands-on development.</p>

<p><strong>When:</strong> Aug 15-17 (Fri evening + weekend)<br>
<strong>Spots left:</strong> Only 4<br>
<strong>Registration closes:</strong> Tomorrow</p>

<p>Ready to secure your spot?</p>

<p>Best,<br>
Mahalakshmi<br>
Modern AI</p>
</body></html>"""
    
    @staticmethod
    def followup_with_gift(recipient_name: str = "there") -> str:
        """Follow-up email template with gift"""
        greeting = f"Hey {recipient_name}," if recipient_name else "Hey,"
        
        return f"""<html><body>
<p>{greeting}</p>

<p>Following up on my previous email about our AI Bootcamp with <strong>Dr. Balaji Viswanathan</strong>...</p>

<p><strong>Here's the reality:</strong> While everyone talks about AI replacing jobs, the executives who understand Python are the ones <em>creating</em> the AI-powered roles.</p>

<div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;">
<h3 style="color: #007bff; margin-top: 0;">The 10-20-100 Formula</h3>
<ul style="margin: 0;">
<li><strong>10 hours:</strong> Master Python fundamentals for business</li>
<li><strong>20 hours:</strong> Complete our Modern AI Pro program</li>
<li><strong>100x:</strong> Your career potential return on investment</li>
</ul>
</div>

<p>That's why I'm gifting you <strong>"Python for the Executive Mind"</strong> - a 73-page guide I wrote specifically for professionals like you.</p>

<p>The book is attached to this email - no strings attached, no opt-ins required.</p>

<p>Best,<br><br>
Mahalakshmi<br><br>
Head of Sales, Modern AI</p>
</body></html>"""

def create_quick_email(to: str, subject: str, body: str, attachments: List[str] = None) -> EmailMessage:
    """Quick helper function to create an email"""
    email = EmailMessage(to=to, subject=subject, body=body)
    
    if attachments:
        for attachment_path in attachments:
            email.add_attachment_from_path(attachment_path)
    
    return email