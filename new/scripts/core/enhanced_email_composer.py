#!/usr/bin/env python3
"""
Enhanced Email Composer - Refactored to use new architecture:
1. Uses Lead Selector Service for intelligent lead selection
2. Uses Email Library for modern email sending with attachments
3. Maintains existing LLM composition capabilities
4. Enhanced logging and interaction tracking
"""

import os
import re
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Op<PERSON>, Tuple, List

# Import our new services
from lead_selector import LeadSelector
from email_library import (
    ModernAIEmailer, 
    EmailConfig, 
    EmailMessage, 
    EmailTemplates,
    EmailAttachment
)

# Import SDR configuration utility
sys.path.append(str(Path(__file__).parent.parent / "utilities"))
from sdr_config import get_default_sdr_email

class EnhancedEmailComposer:
    """Enhanced email composer using Lead Selector and Email Library"""
    
    def __init__(self, db_path: str, test_mode: bool = True, sender_email: str = None):
        self.db_path = db_path
        self.test_mode = test_mode
        
        # Get sender email from database if not provided
        if sender_email is None:
            self.sender_email = get_default_sdr_email(db_path)
        else:
            self.sender_email = sender_email
        
        # Initialize new services
        self.lead_selector = LeadSelector(db_path)
        self.email_config = EmailConfig(sender_email=self.sender_email)
        self.emailer = ModernAIEmailer(self.email_config) if not test_mode else None
        
        # File paths
        self.templates_dir = Path(__file__).parent.parent.parent / "config" / "email_templates"
        self.log_file = Path(__file__).parent.parent.parent / "data" / "logs" / "email_compositions.log"
        
        # Ensure directories exist
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        
    def get_latest_unprocessed_lead(self) -> Optional[Dict]:
        """Get the most recent lead that hasn't been contacted using Lead Selector"""
        try:
            lead = self.lead_selector.get_latest_unprocessed_lead()
            if lead and 'error' not in lead:
                return lead
            return None
        except Exception as e:
            print(f"❌ Error getting latest lead: {e}")
            return None
    
    def get_lead_by_id(self, lead_id: int) -> Optional[Dict]:
        """Get a specific lead by ID with enriched data"""
        try:
            lead = self.lead_selector.get_lead_by_id(lead_id, include_interactions=True)
            if lead and 'error' not in lead:
                return lead
            return None
        except Exception as e:
            print(f"❌ Error getting lead {lead_id}: {e}")
            return None
    
    def get_leads_for_campaign(self, status: str = "New", hours_back: int = 24, limit: int = 10) -> List[Dict]:
        """Get leads for bulk campaign using Lead Selector"""
        try:
            leads = self.lead_selector.get_leads_by_status(
                status=status, 
                limit=limit, 
                include_interactions=True
            )
            # Filter by hours if needed
            if hours_back < 168:  # Less than a week
                from datetime import datetime, timedelta
                cutoff_time = datetime.now() - timedelta(hours=hours_back)
                leads = [lead for lead in leads if 
                        lead.get('hours_since_created', 0) <= hours_back]
            
            return leads
        except Exception as e:
            print(f"❌ Error getting leads for campaign: {e}")
            return []
    
    def send_email(self, lead: Dict, subject: str, body: str, attachments: List[str] = None) -> bool:
        """Send email using the Email Library"""
        if self.test_mode:
            return self._log_email_composition(lead, subject, body, "Test Mode")
        
        try:
            # Create email message
            email = EmailMessage(
                to=lead['email'],
                subject=subject,
                body=body,
                sender=self.sender_email,
                reply_to=self.sender_email
            )
            
            # Add attachments if provided
            if attachments:
                for attachment_path in attachments:
                    if os.path.exists(attachment_path):
                        email.add_attachment_from_path(attachment_path)
                    else:
                        print(f"⚠️ Attachment not found: {attachment_path}")
            
            # Send the email
            success = self.emailer.send_email(email)
            
            if success:
                self._log_interaction_to_database(lead, subject, body, True)
                print(f"✅ Email sent to {lead['full_name']} <{lead['email']}>")
            else:
                self._log_interaction_to_database(lead, subject, body, False)
                print(f"❌ Failed to send email to {lead['full_name']} <{lead['email']}>")
            
            return success
            
        except Exception as e:
            print(f"❌ Error sending email: {e}")
            self._log_interaction_to_database(lead, subject, body, False)
            return False
    
    def _log_interaction_to_database(self, lead: Dict, subject: str, body: str, success: bool):
        """Log email interaction to database"""
        try:
            import sqlite3
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO lead_interactions (
                        lead_id, user_email, interaction_type, interaction_date,
                        subject, message, outcome
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    lead['id'],
                    self.sender_email,
                    'email_sent',
                    datetime.now().isoformat(),
                    subject,
                    body,
                    'sent' if success else 'failed'
                ))
                
                # Update lead status and last contact date
                if success:
                    cursor.execute("""
                        UPDATE leads 
                        SET email_sent = TRUE, last_contact_date = ?, status = 'Contacted'
                        WHERE id = ?
                    """, (datetime.now().isoformat(), lead['id']))
                
                conn.commit()
                
        except Exception as e:
            print(f"❌ Error logging interaction: {e}")
    
    def format_lead_data_for_llm(self, lead: Dict) -> str:
        """Format enriched lead data for LLM using Lead Selector data"""
        output = []
        
        # Basic lead information
        output.append("=== ENRICHED LEAD INFORMATION ===")
        output.append(f"Name: {lead.get('full_name', 'Not provided')}")
        output.append(f"Email: {lead.get('email', 'Not provided')}")
        output.append(f"Phone: {lead.get('phone', 'Not provided')}")
        output.append(f"First Name: {lead.get('first_name', 'Not provided')}")
        
        # Enriched location and priority data
        output.append(f"Detected Location: {lead.get('detected_location', 'Unknown')}")
        output.append(f"Priority Score: {lead.get('priority_score', 0)}")
        output.append(f"Hours Since Created: {lead.get('hours_since_created', 'Unknown')}")
        
        # Professional information if available
        if lead.get('current_title'):
            output.append(f"Job Title: {lead.get('current_title')}")
        if lead.get('company'):
            output.append(f"Company: {lead.get('company')}")
        if lead.get('location'):
            output.append(f"Location: {lead.get('location')}")
            
        # Experience levels from database
        if lead.get('ai_experience_level'):
            output.append(f"AI Experience: {lead.get('ai_experience_level')}")
        if lead.get('programming_experience'):
            output.append(f"Programming Experience: {lead.get('programming_experience')}")
        if lead.get('learning_goals'):
            output.append(f"Learning Goals: {lead.get('learning_goals')}")
        
        # Interaction history from Lead Selector
        output.append(f"\n=== INTERACTION HISTORY ===")
        output.append(f"Email Count: {lead.get('email_count', 0)}")
        output.append(f"Enrolled: {lead.get('enrolled', False)}")
        output.append(f"Response Received: {lead.get('response_received', False)}")
        output.append(f"Follow-up Required: {lead.get('follow_up_required', False)}")
        if lead.get('last_email_date'):
            output.append(f"Last Email Date: {lead.get('last_email_date')}")
        
        # Facebook form data (parsed by Lead Selector)
        facebook_data = lead.get('facebook_form_data', {})
        if facebook_data:
            output.append("\n=== FACEBOOK FORM RESPONSES ===")
            for field_name, value in facebook_data.items():
                if value and value != 'No response':
                    output.append(f"{field_name}: {value}")
        
        # Campaign information
        if lead.get('campaign_name'):
            output.append(f"\n=== CAMPAIGN INFO ===")
            output.append(f"Campaign: {lead.get('campaign_name')}")
            output.append(f"Lead Source: {lead.get('lead_source', 'Facebook')}")
            output.append(f"Status: {lead.get('status', 'Unknown')}")
            output.append(f"Assigned To: {lead.get('assigned_to', 'Unassigned')}")
            
        return '\n'.join(output)
    
    def compose_email_with_template(self, lead: Dict) -> Tuple[str, str, str]:
        """Compose email using built-in EmailTemplates"""
        try:
            # Use the EmailTemplates from the library
            first_name = lead.get('first_name', lead.get('full_name', '').split()[0] if lead.get('full_name') else 'there')
            
            # Choose template based on lead status and interaction history
            if lead.get('email_count', 0) > 0:
                # Follow-up email
                body = EmailTemplates.followup_with_gift(first_name)
                subject = f"🎁 Your Free Python Executive Guide - {first_name}"
            elif lead.get('status') == 'New':
                # Initial sales email
                body = EmailTemplates.sales_email(first_name)
                subject = f"🔥 Last 4 spots - AI Bootcamp starts soon, {first_name}"
            else:
                # Default test email
                body = EmailTemplates.test_email(first_name)
                subject = f"AI Training Opportunity for {first_name}"
            
            return subject, body, "EmailTemplates"
            
        except Exception as e:
            print(f"❌ Template composition failed: {e}")
            # Fallback to simple template
            name = lead.get('first_name', 'there')
            subject = f"AI Training Opportunity for {name}"
            body = f"""<html><body>
<p>Hi {name},</p>

<p>I noticed you're interested in our AI training program - exciting choice!</p>

<p>Great to connect with you!</p>

<p>As a professional, you're perfectly positioned to leverage AI in your career.</p>

<p>What makes our program special:</p>
<ul>
    <li>Weekend format that fits your schedule</li>
    <li>Hands-on projects you can implement immediately</li>
    <li>Learn from Dr. Balaji Viswanathan</li>
    <li>Join a community of professionals</li>
</ul>

<p>Would you like to hop on a quick call this week?</p>

<p>Best regards,<br>
{self.sender_email.split('@')[0].title()}<br>
Modern AI Pro Team</p>
</body></html>"""
            
            return subject, body, "Fallback Template"
    
    def compose_email(self, lead: Dict) -> Tuple[str, str, str]:
        """Compose personalized email using LLM or templates with enriched lead data"""
        
        # Try LLM composition first
        try:
            import sys
            import os
            # Add the integrations path to sys.path
            integrations_path = os.path.join(os.path.dirname(__file__), '..', 'integrations', 'openai')
            if integrations_path not in sys.path:
                sys.path.append(integrations_path)
            from email_generator import EmailGenerator
            
            print(f"🤖 Using LLM for email composition...")
            email_generator = EmailGenerator()
            
            # Load prompt template if available
            prompt_path = self.templates_dir / "intro_outreach_prompt.md"
            if prompt_path.exists():
                with open(prompt_path, 'r') as f:
                    prompt_content = f.read()
            else:
                prompt_content = "Create a personalized outreach email for this lead."
            
            # Create system prompt with sender context
            sender_name = self.sender_email.split('@')[0].title()
            system_prompt = f"""You are {sender_name}, Business Development Manager at Modern AI Pro. You're composing an outreach email for workshop leads. Create a personalized, compelling email that drives immediate response using the enriched lead data provided."""
            
            # Format enriched lead data for the LLM
            formatted_lead_data = self.format_lead_data_for_llm(lead)
            
            # Create user prompt with enriched data
            user_prompt = f"""
{prompt_content}

{formatted_lead_data}

Create a complete email following the guidelines above. Use ALL the available enriched lead information including priority score, interaction history, and location data to personalize the email. Ensure:
1. No template variables remain in output
2. Include proper subject line
3. Reference their detected location and interaction history appropriately
4. Keep under 150 words
5. Make it friendly and conversational
6. Use their Facebook form responses and priority data to personalize
7. Consider their current status and previous interactions
"""
            
            # Generate email using LLM
            email_content, success, model_used = email_generator.generate_email(system_prompt, user_prompt)
            
            if success and email_content:
                # Parse LLM response to extract subject and body
                subject, body = self._parse_llm_email(email_content, lead.get('first_name', 'there'))
                return subject, body, f"LLM ({model_used})"
            else:
                print(f"⚠️ LLM generation failed: {email_content}")
                
        except Exception as e:
            print(f"⚠️ LLM error: {e}")
        
        # Fall back to template composition
        print("📝 Falling back to template composition...")
        return self.compose_email_with_template(lead)
    
    def _parse_llm_email(self, email_content: str, name: str) -> Tuple[str, str]:
        """Parse LLM-generated email to extract subject and body"""
        lines = email_content.strip().split('\n')
        
        # Look for subject line
        subject = f"AI Training Opportunity for {name}"  # Default
        body_start = 0
        
        for i, line in enumerate(lines):
            line_lower = line.lower().strip()
            if line_lower.startswith('subject:'):
                subject = line.split(':', 1)[1].strip()
                body_start = i + 1
                break
            elif '**subject:**' in line_lower:
                subject = line.split('**subject:**', 1)[1].strip()
                body_start = i + 1
                break
        
        # Extract body (skip empty lines after subject)
        body_lines = lines[body_start:]
        while body_lines and not body_lines[0].strip():
            body_lines = body_lines[1:]
        
        body = '\n'.join(body_lines).strip()
        
        # If no body found, use the entire content
        if not body:
            body = email_content.strip()
        
        return subject, body
    
    def _log_email_composition(self, lead: Dict, subject: str, body: str, generation_method: str = "Unknown") -> bool:
        """Log composed email to file for testing"""
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        location = lead.get('detected_location', 'Unknown')
        
        log_entry = f"""
{'='*80}
TIMESTAMP: {timestamp}
LEAD ID: {lead['id']}
NAME: {lead.get('full_name', 'Unknown')}
EMAIL: {lead.get('email', 'Unknown')}
PHONE: {lead.get('phone', 'None')}
LOCATION: {location}
PRIORITY SCORE: {lead.get('priority_score', 'N/A')}
EMAIL COUNT: {lead.get('email_count', 0)}
STATUS: {lead.get('status', 'Unknown')}
GENERATION METHOD: {generation_method}

SUBJECT: {subject}

BODY:
{body}

{'='*80}

"""
        
        # Ensure log directory exists
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
            
            print(f"📧 Email composition logged for {lead.get('full_name', 'Unknown')} ({lead.get('email', 'Unknown')})")
            return True
            
        except Exception as e:
            print(f"❌ Error logging email composition: {e}")
            return False
    
    
    def process_latest_lead(self) -> Dict:
        """Main function: process the latest unprocessed lead"""
        
        print("🔍 Looking for latest unprocessed lead...")
        
        # Get latest lead
        lead = self.get_latest_unprocessed_lead()
        if not lead:
            return {"status": "no_leads", "message": "No unprocessed leads found"}
        
        print(f"📋 Found lead: {lead.get('full_name', 'Unknown')} ({lead.get('email', 'Unknown')})")
        
        # Compose email
        try:
            subject, body, generation_method = self.compose_email(lead)
            
            if self.test_mode:
                # Log to file instead of sending
                self._log_email_composition(lead, subject, body, generation_method)
                return {
                    "status": "success", 
                    "mode": "test",
                    "lead_id": lead['id'],
                    "lead_name": lead.get('full_name'),
                    "lead_email": lead.get('email'),
                    "generation_method": generation_method,
                    "message": f"Email composition logged to file (using {generation_method})"
                }
            else:
                # Send email using Email Library
                success = self.send_email(lead, subject, body)
                return {
                    "status": "success" if success else "error",
                    "mode": "production",
                    "lead_id": lead['id'],
                    "lead_name": lead.get('full_name'),
                    "lead_email": lead.get('email'),
                    "generation_method": generation_method,
                    "message": f"Email {'sent successfully' if success else 'sending failed'} (using {generation_method})"
                }
                
        except Exception as e:
            return {"status": "error", "message": f"Email composition failed: {e}"}


def main():
    """CLI entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Enhanced Email Composer')
    parser.add_argument('--db-path', 
                       default='/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db',
                       help='Path to database file')
    parser.add_argument('--test-mode', action='store_true', default=True,
                       help='Test mode - log emails instead of sending')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.db_path):
        print(f"❌ Database not found: {args.db_path}")
        return 1
    
    composer = EnhancedEmailComposer(args.db_path, test_mode=args.test_mode)
    result = composer.process_latest_lead()
    
    print(f"📊 Result: {result}")
    return 0 if result['status'] == 'success' else 1


if __name__ == "__main__":
    exit(main())