#!/usr/bin/env python3
"""
Lead Selector Service
Simple service to select leads for email campaigns and return them as JSON
Extracted from complex email composer for better orchestration
"""

import sqlite3
import json
import re
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

class LeadSelector:
    """Service to select and format leads for email campaigns"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        
    def _detect_location_from_phone(self, phone: str) -> str:
        """Detect location based on phone number format"""
        if not phone:
            return "Unknown"
            
        # Clean phone number
        clean_phone = re.sub(r'[^\d+]', '', phone)
        
        # US numbers
        if clean_phone.startswith('+1') or (len(clean_phone) == 10 and clean_phone[0] in '2-9'):
            return "US"
        
        # India numbers
        if clean_phone.startswith('+91') or clean_phone.startswith('91'):
            return "India"
        
        # UK numbers
        if clean_phone.startswith('+44'):
            return "UK"
        
        # Canada (also +1 but different patterns)
        if clean_phone.startswith('+1'):
            return "North America"
            
        # Default for international
        if clean_phone.startswith('+'):
            return "International"
            
        return "Unknown"
    
    def _parse_facebook_form_data(self, notes: str) -> Dict[str, Any]:
        """Parse Facebook form data from lead notes"""
        form_data = {}
        
        if not notes or 'Raw fields:' not in notes:
            return form_data
            
        try:
            # Extract JSON from notes
            json_start = notes.find('Raw fields:') + len('Raw fields:')
            json_end = notes.find(' |', json_start)
            if json_end == -1:
                json_end = len(notes)
            
            json_str = notes[json_start:json_end].strip()
            raw_fields = json.loads(json_str)
            
            # Convert to simple key-value pairs
            for field in raw_fields:
                field_name = field.get('name', 'Unknown Field')
                values = field.get('values', [])
                value = values[0] if values else 'No response'
                form_data[field_name] = value
                
        except (json.JSONDecodeError, ValueError) as e:
            form_data['parsing_error'] = str(e)
        
        return form_data
    
    def _enrich_lead_data(self, lead: Dict) -> Dict:
        """Enrich lead data with derived fields and parsing"""
        enriched = dict(lead)
        
        # Add location detection
        enriched['detected_location'] = self._detect_location_from_phone(lead.get('phone', ''))
        
        # Add first name
        full_name = lead.get('full_name', '')
        enriched['first_name'] = full_name.split()[0] if full_name else 'there'
        
        # Parse Facebook form data
        notes = lead.get('notes', '') or ''
        enriched['facebook_form_data'] = self._parse_facebook_form_data(notes)
        
        # Add timestamps in readable format
        if lead.get('created_time'):
            try:
                # Assuming SQLite datetime format
                dt = datetime.fromisoformat(lead['created_time'].replace('Z', '+00:00'))
                enriched['created_time_readable'] = dt.strftime('%Y-%m-%d %H:%M:%S')
                enriched['created_date'] = dt.strftime('%Y-%m-%d')
                enriched['hours_since_created'] = (datetime.now() - dt.replace(tzinfo=None)).total_seconds() / 3600
            except:
                enriched['created_time_readable'] = lead.get('created_time', 'Unknown')
        
        # Add priority score (simple scoring based on recency and completeness)
        priority_score = 0
        if enriched.get('hours_since_created', 999):
            if enriched['hours_since_created'] < 1:
                priority_score += 100  # Very recent
            elif enriched['hours_since_created'] < 6:
                priority_score += 50   # Recent
            elif enriched['hours_since_created'] < 24:
                priority_score += 25   # Today
        
        if lead.get('phone'):
            priority_score += 10
        if lead.get('ai_experience_level'):
            priority_score += 5
        if lead.get('learning_goals'):
            priority_score += 5
        if enriched['facebook_form_data']:
            priority_score += 10
            
        enriched['priority_score'] = priority_score
        
        return enriched
    
    def _get_lead_interactions(self, lead_id: int) -> List[Dict]:
        """Get all interactions for a specific lead"""
        query = """
        SELECT 
            interaction_type, user_email, subject, outcome, 
            interaction_date, response_received, follow_up_required,
            follow_up_date, follow_up_notes
        FROM lead_interactions 
        WHERE lead_id = ?
        ORDER BY interaction_date DESC
        """
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query, (lead_id,))
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception:
            return []
    
    def _enrich_with_interactions(self, lead: Dict) -> Dict:
        """Enrich lead with interaction history"""
        enriched = dict(lead)
        
        # Get interaction history
        interactions = self._get_lead_interactions(lead['id'])
        enriched['interactions'] = interactions
        
        # Calculate interaction stats
        email_interactions = [i for i in interactions if i['interaction_type'] == 'email_sent']
        enrollments = [i for i in interactions if i['interaction_type'] == 'enrollment']
        
        enriched['email_count'] = len(email_interactions)
        enriched['enrolled'] = len(enrollments) > 0
        enriched['last_email_date'] = email_interactions[0]['interaction_date'] if email_interactions else None
        enriched['last_interaction_date'] = interactions[0]['interaction_date'] if interactions else None
        
        # Check if follow-up is required
        follow_up_needed = any(i.get('follow_up_required') for i in interactions)
        enriched['follow_up_required'] = follow_up_needed
        
        # Check for responses
        responses_received = any(i.get('response_received') for i in interactions)
        enriched['response_received'] = responses_received
        
        # Adjust priority based on interaction history
        if enriched['enrolled']:
            enriched['priority_score'] = 0  # Don't contact enrolled leads
        elif enriched['email_count'] >= 3:
            enriched['priority_score'] *= 0.1  # Lower priority for heavily contacted leads
        elif enriched['response_received']:
            enriched['priority_score'] *= 2  # Higher priority for responsive leads
        elif follow_up_needed:
            enriched['priority_score'] *= 1.5  # Higher priority for follow-ups
        
        return enriched
    
    def get_latest_unprocessed_lead(self) -> Optional[Dict]:
        """Get the most recent lead that hasn't been contacted"""
        
        query = """
        SELECT 
            l.id, l.full_name, l.email, l.phone, l.campaign_name,
            l.ai_experience_level, l.programming_experience, l.learning_goals,
            l.created_time, l.assigned_to, l.lead_source, l.notes, l.status,
            l.current_title, l.company, l.location
        FROM leads l
        WHERE 
            l.created_time >= datetime('now', '-24 hours')
            AND l.status = 'New'
            AND l.id NOT IN (
                SELECT DISTINCT lead_id 
                FROM lead_interactions 
                WHERE interaction_type = 'email_sent'
                AND outcome = 'sent'
                AND lead_id IS NOT NULL
            )
            AND l.email IS NOT NULL 
            AND l.email != ''
            AND l.email LIKE '%@%'
        ORDER BY l.created_time DESC
        LIMIT 1
        """
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query)
                row = cursor.fetchone()
                
                if row:
                    lead = dict(row)
                    return self._enrich_lead_data(lead)
                return None
                
        except Exception as e:
            return {"error": f"Database error: {e}"}
    
    def get_recent_unprocessed_leads(self, hours: int = 24, limit: int = 10) -> List[Dict]:
        """Get recent leads that haven't been contacted"""
        
        query = """
        SELECT 
            l.id, l.full_name, l.email, l.phone, l.campaign_name,
            l.ai_experience_level, l.programming_experience, l.learning_goals,
            l.created_time, l.assigned_to, l.lead_source, l.notes, l.status,
            l.current_title, l.company, l.location
        FROM leads l
        WHERE 
            l.created_time >= datetime('now', '-{hours} hours')
            AND l.status = 'New'
            AND l.id NOT IN (
                SELECT DISTINCT lead_id 
                FROM lead_interactions 
                WHERE interaction_type = 'email_sent'
                AND outcome = 'sent'
                AND lead_id IS NOT NULL
            )
            AND l.email IS NOT NULL 
            AND l.email != ''
            AND l.email LIKE '%@%'
        ORDER BY l.created_time DESC
        LIMIT {limit}
        """.format(hours=hours, limit=limit)
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query)
                rows = cursor.fetchall()
                
                leads = []
                for row in rows:
                    lead = dict(row)
                    enriched_lead = self._enrich_lead_data(lead)
                    leads.append(enriched_lead)
                
                # Sort by priority score descending
                leads.sort(key=lambda x: x.get('priority_score', 0), reverse=True)
                
                return leads
                
        except Exception as e:
            return [{"error": f"Database error: {e}"}]
    
    def get_leads_by_criteria(self, 
                             status: str = "New",
                             hours_back: int = 168,  # 1 week
                             location: str = None,
                             campaign: str = None,
                             assigned_to: str = None,
                             limit: int = 50,
                             include_interactions: bool = False) -> List[Dict]:
        """Get leads by specific criteria"""
        
        # Build dynamic query
        conditions = []
        params = {}
        
        conditions.append("l.created_time >= datetime('now', '-{hours_back} hours')".format(hours_back=hours_back))
        conditions.append("l.status = :status")
        params['status'] = status
        
        conditions.append("""l.id NOT IN (
            SELECT DISTINCT lead_id 
            FROM lead_interactions 
            WHERE interaction_type = 'email_sent'
            AND outcome = 'sent'
            AND lead_id IS NOT NULL
        )""")
        
        conditions.append("l.email IS NOT NULL AND l.email != '' AND l.email LIKE '%@%'")
        
        if location:
            conditions.append("l.location LIKE :location")
            params['location'] = f"%{location}%"
        
        if campaign:
            conditions.append("l.campaign_name LIKE :campaign")
            params['campaign'] = f"%{campaign}%"
        
        if assigned_to:
            conditions.append("l.assigned_to = :assigned_to")
            params['assigned_to'] = assigned_to
        
        query = f"""
        SELECT 
            l.id, l.full_name, l.email, l.phone, l.campaign_name,
            l.ai_experience_level, l.programming_experience, l.learning_goals,
            l.created_time, l.assigned_to, l.lead_source, l.notes, l.status,
            l.current_title, l.company, l.location
        FROM leads l
        WHERE {' AND '.join(conditions)}
        ORDER BY l.created_time DESC
        LIMIT {limit}
        """
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query, params)
                rows = cursor.fetchall()
                
                leads = []
                for row in rows:
                    lead = dict(row)
                    enriched_lead = self._enrich_lead_data(lead)
                    leads.append(enriched_lead)
                
                # Sort by priority score descending
                leads.sort(key=lambda x: x.get('priority_score', 0), reverse=True)
                
                return leads
                
        except Exception as e:
            return [{"error": f"Database error: {e}"}]
    
    def get_lead_statistics(self) -> Dict:
        """Get statistics about leads for dashboard/monitoring"""
        
        stats_query = """
        SELECT 
            COUNT(*) as total_leads,
            COUNT(CASE WHEN created_time >= datetime('now', '-24 hours') THEN 1 END) as leads_24h,
            COUNT(CASE WHEN created_time >= datetime('now', '-7 days') THEN 1 END) as leads_7d,
            COUNT(CASE WHEN status = 'New' THEN 1 END) as new_leads,
            COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as leads_with_email,
            COUNT(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 END) as leads_with_phone
        FROM leads
        """
        
        contacted_query = """
        SELECT COUNT(DISTINCT lead_id) as contacted_leads
        FROM lead_interactions 
        WHERE interaction_type = 'email_sent'
        AND lead_id IS NOT NULL
        """
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                # Get basic stats
                cursor = conn.execute(stats_query)
                stats = dict(cursor.fetchone())
                
                # Get contacted leads count
                cursor = conn.execute(contacted_query)
                contacted = dict(cursor.fetchone())
                stats.update(contacted)
                
                # Calculate derived stats
                stats['uncontacted_leads'] = stats['leads_with_email'] - stats['contacted_leads']
                stats['contact_rate'] = (stats['contacted_leads'] / max(stats['leads_with_email'], 1)) * 100
                
                # Add timestamp
                stats['generated_at'] = datetime.now().isoformat()
                
                return stats
                
        except Exception as e:
            return {"error": f"Database error: {e}"}
    
    def get_leads_by_status(self, status: str, limit: int = 50, include_interactions: bool = False) -> List[Dict]:
        """Get leads by specific status value
        
        Available statuses: New, new, Customer, Contacted, Lead, Enrolled, Qualified, qualified
        """
        
        query = """
        SELECT 
            l.id, l.full_name, l.email, l.phone, l.campaign_name,
            l.ai_experience_level, l.programming_experience, l.learning_goals,
            l.created_time, l.assigned_to, l.lead_source, l.notes, l.status,
            l.current_title, l.company, l.location, l.priority, l.lead_score,
            l.payment_status, l.enrolled_date, l.workshop_completed_date
        FROM leads l
        WHERE l.status = ?
        AND l.email IS NOT NULL 
        AND l.email != ''
        AND l.email LIKE '%@%'
        ORDER BY l.created_time DESC
        LIMIT ?
        """
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query, (status, limit))
                rows = cursor.fetchall()
                
                leads = []
                for row in rows:
                    lead = dict(row)
                    enriched_lead = self._enrich_lead_data(lead)
                    if include_interactions:
                        enriched_lead = self._enrich_with_interactions(enriched_lead)
                    leads.append(enriched_lead)
                
                # Sort by priority score descending
                leads.sort(key=lambda x: x.get('priority_score', 0), reverse=True)
                
                return leads
                
        except Exception as e:
            return [{"error": f"Database error: {e}"}]
    
    def get_status_summary(self) -> Dict:
        """Get summary of lead statuses"""
        
        query = """
        SELECT 
            status, 
            COUNT(*) as count,
            COUNT(CASE WHEN created_time >= datetime('now', '-24 hours') THEN 1 END) as count_24h,
            COUNT(CASE WHEN created_time >= datetime('now', '-7 days') THEN 1 END) as count_7d
        FROM leads 
        WHERE status IS NOT NULL
        GROUP BY status 
        ORDER BY count DESC
        """
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query)
                rows = cursor.fetchall()
                
                summary = {
                    "timestamp": datetime.now().isoformat(),
                    "statuses": {}
                }
                
                for row in rows:
                    status_data = dict(row)
                    summary["statuses"][status_data["status"]] = {
                        "total": status_data["count"],
                        "last_24h": status_data["count_24h"],
                        "last_7d": status_data["count_7d"]
                    }
                
                return summary
                
        except Exception as e:
            return {"error": f"Database error: {e}"}
    
    def get_lead_by_id(self, lead_id: int, include_interactions: bool = False) -> Optional[Dict]:
        """Get a specific lead by ID with optional interaction history"""
        
        query = """
        SELECT 
            l.id, l.full_name, l.email, l.phone, l.campaign_name,
            l.ai_experience_level, l.programming_experience, l.learning_goals,
            l.created_time, l.assigned_to, l.lead_source, l.notes, l.status,
            l.current_title, l.company, l.location, l.priority, l.lead_score,
            l.payment_status, l.enrolled_date, l.workshop_completed_date,
            l.last_contact_date
        FROM leads l
        WHERE l.id = ?
        """
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query, (lead_id,))
                row = cursor.fetchone()
                
                if row:
                    lead = dict(row)
                    enriched_lead = self._enrich_lead_data(lead)
                    if include_interactions:
                        enriched_lead = self._enrich_with_interactions(enriched_lead)
                    return enriched_lead
                return None
                
        except Exception as e:
            return {"error": f"Database error: {e}"}

def main():
    """CLI entry point for lead selection"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Lead Selector Service')
    parser.add_argument('--db-path', 
                       default='/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db',
                       help='Path to database file')
    parser.add_argument('--action', 
                       choices=['latest', 'recent', 'criteria', 'stats', 'status', 'status-summary', 'by-id'],
                       default='latest',
                       help='Action to perform')
    parser.add_argument('--hours', type=int, default=24,
                       help='Hours to look back for recent leads')
    parser.add_argument('--limit', type=int, default=10,
                       help='Maximum number of leads to return')
    parser.add_argument('--location', help='Filter by location')
    parser.add_argument('--campaign', help='Filter by campaign')
    parser.add_argument('--assigned-to', help='Filter by assigned user')
    parser.add_argument('--status', help='Lead status (New, Contacted, Customer, etc.)')
    parser.add_argument('--include-interactions', action='store_true',
                       help='Include interaction history in results')
    parser.add_argument('--lead-id', type=int, help='Lead ID for by-id action')
    parser.add_argument('--output', choices=['json', 'summary'], default='json',
                       help='Output format')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.db_path):
        print(json.dumps({"error": f"Database not found: {args.db_path}"}))
        return 1
    
    selector = LeadSelector(args.db_path)
    
    try:
        if args.action == 'latest':
            result = selector.get_latest_unprocessed_lead()
            if result:
                leads = [result]
            else:
                leads = []
                
        elif args.action == 'recent':
            leads = selector.get_recent_unprocessed_leads(hours=args.hours, limit=args.limit)
            
        elif args.action == 'criteria':
            leads = selector.get_leads_by_criteria(
                hours_back=args.hours,
                location=args.location,
                campaign=args.campaign,
                assigned_to=args.assigned_to,
                limit=args.limit
            )
            
        elif args.action == 'stats':
            stats = selector.get_lead_statistics()
            print(json.dumps(stats, indent=2))
            return 0
        
        elif args.action == 'status':
            if not args.status:
                print(json.dumps({"error": "Status value required for --action status"}))
                return 1
            leads = selector.get_leads_by_status(
                status=args.status,
                limit=args.limit,
                include_interactions=args.include_interactions
            )
            
        elif args.action == 'status-summary':
            summary = selector.get_status_summary()
            print(json.dumps(summary, indent=2))
            return 0
        
        elif args.action == 'by-id':
            if not args.lead_id:
                print(json.dumps({"error": "Lead ID required for --action by-id"}))
                return 1
            lead = selector.get_lead_by_id(
                lead_id=args.lead_id,
                include_interactions=args.include_interactions
            )
            if lead:
                leads = [lead]
            else:
                leads = []
        
        # Output results
        if args.output == 'json':
            output = {
                "timestamp": datetime.now().isoformat(),
                "action": args.action,
                "parameters": {
                    "hours": args.hours,
                    "limit": args.limit,
                    "location": args.location,
                    "campaign": args.campaign,
                    "assigned_to": args.assigned_to
                },
                "count": len(leads),
                "leads": leads
            }
            print(json.dumps(output, indent=2))
        else:
            # Summary output
            print(f"Action: {args.action}")
            print(f"Found: {len(leads)} leads")
            for i, lead in enumerate(leads, 1):
                if 'error' in lead:
                    print(f"  {i}. ERROR: {lead['error']}")
                else:
                    print(f"  {i}. {lead.get('full_name', 'Unknown')} <{lead.get('email', 'No email')}> "
                          f"(Priority: {lead.get('priority_score', 0)}, Location: {lead.get('detected_location', 'Unknown')})")
        
        return 0
        
    except Exception as e:
        print(json.dumps({"error": f"Lead selection failed: {e}"}))
        return 1

if __name__ == "__main__":
    exit(main())