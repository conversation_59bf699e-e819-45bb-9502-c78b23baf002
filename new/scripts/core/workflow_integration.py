#!/usr/bin/env python3
"""
Workflow Integration Layer
Bridges the newer Email Campaign Manager with enhanced logging and status tracking
from the older workflow system.
"""

import os
import sys
import sqlite3
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from email_campaign_manager import EmailCampaignManager
from lead_selector import LeadSelector

class WorkflowLogger:
    """Enhanced workflow logging with detailed tracking"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.log_file = Path(__file__).parent.parent.parent / "data" / "logs" / "workflow_runs.log"
        self.stats_file = Path(__file__).parent.parent.parent / "data" / "logs" / "email_generation_stats.log"
        
        # Ensure log directories exist
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        self.stats_file.parent.mkdir(parents=True, exist_ok=True)
    
    def log_workflow_run(self, summary: Dict) -> None:
        """Log complete workflow run summary"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Check if file is empty to add header
        add_header = not self.log_file.exists() or self.log_file.stat().st_size == 0
        
        if add_header:
            header = f"{'TIMESTAMP':<20} | {'STATUS':<15} | {'PROCESSED':<10} | {'SUCCESS':<8} | {'FAILED':<8} | {'LLM_SUCCESS':<12} | {'TEMPLATE':<10} | {'ERRORS':<50}\n"
            separator = f"{'-'*20}-+-{'-'*15}-+-{'-'*10}-+-{'-'*8}-+-{'-'*8}-+-{'-'*12}-+-{'-'*10}-+-{'-'*50}\n"
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write(header)
                f.write(separator)
        
        # Format data for table
        status = summary.get('status', 'unknown')[:14]
        processed = str(summary.get('leads_processed', 0))
        successful = str(summary.get('successful', 0))
        failed = str(summary.get('failed', 0))
        llm_success = str(summary.get('llm_generation_count', 0))
        template_count = str(summary.get('template_generation_count', 0))
        error_count = len(summary.get('errors', []))
        errors_preview = f"{error_count} errors" if error_count > 0 else "None"
        
        log_entry = f"{timestamp:<20} | {status:<15} | {processed:<10} | {successful:<8} | {failed:<8} | {llm_success:<12} | {template_count:<10} | {errors_preview:<50}\n"
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
    
    def log_lead_processing(self, 
                           lead: Dict, 
                           assigned_to: str,
                           sequence_step: int,
                           generation_method: str,
                           email_sent: bool,
                           sender_email: str = None) -> Dict:
        """Log detailed lead processing information"""
        
        lead_detail = {
            'lead_id': lead['id'],
            'lead_name': lead.get('full_name', 'Unknown'),
            'lead_email': lead.get('email', 'Unknown'),
            'assigned_to': assigned_to,
            'sequence_step': sequence_step,
            'generation_method': generation_method,
            'email_sent': email_sent,
            'sender_email': sender_email or 'Unknown',
            'priority_score': lead.get('priority_score', 0),
            'detected_location': lead.get('detected_location', 'Unknown'),
            'timestamp': datetime.now().isoformat()
        }
        
        return lead_detail
    
    def log_email_generation_stats(self, llm_success: int, template_fallback: int) -> None:
        """Log email generation method statistics"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        total = llm_success + template_fallback
        llm_percentage = (llm_success / total * 100) if total > 0 else 0
        
        # Check if file is empty to add header
        add_header = not self.stats_file.exists() or self.stats_file.stat().st_size == 0
        
        if add_header:
            header = f"{'TIMESTAMP':<20} | {'LLM_COUNT':<10} | {'TEMPLATE':<10} | {'TOTAL':<8} | {'LLM_RATE':<10}\n"
            separator = f"{'-'*20}-+-{'-'*10}-+-{'-'*10}-+-{'-'*8}-+-{'-'*10}\n"
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                f.write(header)
                f.write(separator)
        
        log_entry = f"{timestamp:<20} | {llm_success:<10} | {template_fallback:<10} | {total:<8} | {llm_percentage:<10.1f}%\n"
        
        with open(self.stats_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)

class EnhancedInteractionLogger:
    """Enhanced interaction logging with better status tracking"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def log_email_sent(self,
                      lead_id: int,
                      subject: str,
                      message: str,
                      outcome: str = 'sent',
                      user_email: str = '<EMAIL>',
                      generation_method: str = 'Unknown',
                      follow_up_required: bool = True,
                      follow_up_days: int = 1) -> int:
        """Enhanced email logging with generation method tracking"""
        
        follow_up_date = None
        if follow_up_required:
            follow_up_date = datetime.now() + timedelta(days=follow_up_days)
        
        query = """
        INSERT INTO lead_interactions (
            lead_id,
            interaction_type,
            user_email,
            subject,
            message,
            outcome,
            interaction_date,
            response_received,
            follow_up_required,
            follow_up_date,
            follow_up_notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(query, (
                    lead_id,
                    'email_sent',
                    user_email,
                    subject,
                    message,
                    outcome,
                    datetime.now().isoformat(),
                    False,  # response_received
                    follow_up_required,
                    follow_up_date.isoformat() if follow_up_date else None,
                    f"Generated by: {generation_method}"
                ))
                
                interaction_id = cursor.lastrowid
                
                # Update lead status if this is first contact
                self._update_lead_status_after_contact(conn, lead_id)
                
                return interaction_id
                
        except Exception as e:
            print(f"❌ Error logging interaction: {e}")
            return 0
    
    def log_status_change(self,
                         lead_id: int,
                         old_status: str,
                         new_status: str,
                         user_email: str,
                         reason: str = '') -> int:
        """Log lead status changes with detailed tracking"""
        
        query = """
        INSERT INTO lead_interactions (
            lead_id,
            interaction_type,
            user_email,
            subject,
            message,
            outcome,
            interaction_date
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(query, (
                    lead_id,
                    'status_change',
                    user_email,
                    f'Status: {old_status} → {new_status}',
                    reason,
                    'updated',
                    datetime.now().isoformat()
                ))
                
                return cursor.lastrowid
                
        except Exception as e:
            print(f"❌ Error logging status change: {e}")
            return 0
    
    def _update_lead_status_after_contact(self, conn: sqlite3.Connection, lead_id: int):
        """Update lead status to 'Contacted' after first interaction"""
        # Check if this is the first contact
        check_query = """
        SELECT COUNT(*) as count FROM lead_interactions 
        WHERE lead_id = ? AND interaction_type IN ('email_sent', 'call_attempted', 'call_completed')
        """
        
        cursor = conn.execute(check_query, (lead_id,))
        result = cursor.fetchone()
        if result and result[0] == 1:  # This is the first contact
            update_query = """
            UPDATE leads 
            SET status = 'Contacted', last_contact_date = ?
            WHERE id = ? AND status = 'New'
            """
            conn.execute(update_query, (datetime.now().isoformat(), lead_id))
    
    def get_interaction_stats(self, days: int = 30) -> Dict:
        """Get comprehensive interaction statistics"""
        query = """
        SELECT 
            interaction_type,
            outcome,
            COUNT(*) as count,
            AVG(CASE WHEN response_received THEN 1.0 ELSE 0.0 END) as response_rate
        FROM lead_interactions 
        WHERE interaction_date >= datetime('now', '-{} days')
        GROUP BY interaction_type, outcome
        ORDER BY interaction_type, count DESC
        """.format(days)
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query)
                
                stats = {}
                for row in cursor.fetchall():
                    interaction_type = row['interaction_type']
                    if interaction_type not in stats:
                        stats[interaction_type] = {}
                    
                    stats[interaction_type][row['outcome']] = {
                        'count': row['count'],
                        'response_rate': round(row['response_rate'] * 100, 2)
                    }
                
                return stats
                
        except Exception as e:
            print(f"❌ Error getting interaction stats: {e}")
            return {}

class EnhancedEmailCampaignManager(EmailCampaignManager):
    """Extended Email Campaign Manager with enhanced workflow logging"""
    
    def __init__(self, db_path: str, test_mode: bool = True):
        super().__init__(db_path, test_mode)
        self.workflow_logger = WorkflowLogger(db_path)
        self.interaction_logger = EnhancedInteractionLogger(db_path)
        
        # Track generation method stats
        self.llm_generation_count = 0
        self.template_generation_count = 0
    
    def send_email_to_lead(self, lead: Dict, composer) -> Dict:
        """Enhanced email sending with detailed logging"""
        try:
            # Compose email
            subject, body, generation_method = composer.compose_email(lead)
            
            # Track generation methods
            if generation_method.startswith('LLM'):
                self.llm_generation_count += 1
            else:
                self.template_generation_count += 1
            
            if self.test_mode:
                # Log composition in test mode
                success = composer._log_email_composition(lead, subject, body, generation_method)
                result = {
                    'success': success,
                    'mode': 'test',
                    'lead_id': lead['id'],
                    'generation_method': generation_method,
                    'message': f"Email composition logged (using {generation_method})"
                }
            else:
                # Actually send email in production mode
                success = composer.send_email(lead, subject, body)
                
                # Enhanced interaction logging
                if success:
                    self.interaction_logger.log_email_sent(
                        lead_id=lead['id'],
                        subject=subject,
                        message=body,
                        outcome='sent',
                        user_email=composer.sender_email,
                        generation_method=generation_method,
                        follow_up_required=True,
                        follow_up_days=2
                    )
                
                result = {
                    'success': success,
                    'mode': 'production',
                    'lead_id': lead['id'],
                    'generation_method': generation_method,
                    'message': f"Email {'sent successfully' if success else 'sending failed'} (using {generation_method})"
                }
            
            # Log to campaign file with enhanced details
            self.log_campaign_activity_enhanced(lead, subject, success, generation_method, composer.sender_email)
            
            return result
            
        except Exception as e:
            error_result = {
                'success': False,
                'mode': 'test' if self.test_mode else 'production',
                'lead_id': lead['id'],
                'generation_method': 'error',
                'message': f"Error processing lead: {e}"
            }
            self.log_campaign_activity_enhanced(lead, "ERROR", False, "error", "unknown")
            return error_result
    
    def log_campaign_activity_enhanced(self, lead: Dict, subject: str, success: bool, method: str, sender_email: str):
        """Enhanced campaign activity logging with more details"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Ensure log directory exists
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Check if file is empty to add header
        add_header = not self.log_file.exists() or self.log_file.stat().st_size == 0
        
        if add_header:
            header = f"{'TIMESTAMP':<20} | {'SENDER':<25} | {'LEAD_ID':<8} | {'LEAD_NAME':<20} | {'LEAD_EMAIL':<25} | {'STATUS':<8} | {'METHOD':<15} | {'MODE':<10} | {'PRIORITY':<8} | {'LOCATION':<10}\n"
            separator = f"{'-'*20}-+-{'-'*25}-+-{'-'*8}-+-{'-'*20}-+-{'-'*25}-+-{'-'*8}-+-{'-'*15}-+-{'-'*10}-+-{'-'*8}-+-{'-'*10}\n"
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write(header)
                f.write(separator)
        
        # Format data for table with enhanced fields
        lead_name = lead.get('full_name', 'Unknown')[:19]
        lead_email = lead.get('email', 'Unknown')[:24]
        sender = sender_email[:24]
        status = 'SUCCESS' if success else 'FAILED'
        method = method[:14]
        mode = 'TEST' if self.test_mode else 'PROD'
        priority = str(lead.get('priority_score', 0))[:7]
        location = lead.get('detected_location', 'Unknown')[:9]
        
        log_entry = f"{timestamp:<20} | {sender:<25} | {lead['id']:<8} | {lead_name:<20} | {lead_email:<25} | {status:<8} | {method:<15} | {mode:<10} | {priority:<8} | {location:<10}\n"
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
    
    def run_campaign(self, 
                    status: str = "New", 
                    max_emails: int = 5, 
                    hours_back: int = 24) -> Dict:
        """Enhanced campaign execution with comprehensive logging"""
        
        print(f"🚀 Starting enhanced email campaign (status: {status}, max: {max_emails}, mode: {'TEST' if self.test_mode else 'PRODUCTION'})")
        
        # Reset generation counters
        self.llm_generation_count = 0
        self.template_generation_count = 0
        
        # Get leads for campaign
        leads = self.get_leads_for_campaign(status, hours_back, max_emails)
        print(f"📋 Found {len(leads)} leads for campaign")
        
        if not leads:
            summary = {
                "status": "no_leads",
                "message": "No leads found for campaign",
                "total": 0,
                "successful": 0,
                "failed": 0,
                "llm_generation_count": 0,
                "template_generation_count": 0,
                "errors": []
            }
            self.workflow_logger.log_workflow_run(summary)
            return summary
        
        results = []
        success_count = 0
        failed_count = 0
        lead_details = []
        errors = []
        
        for i, lead in enumerate(leads, 1):
            print(f"\n📤 Processing lead {i}/{len(leads)}: {lead.get('full_name', 'Unknown')} ({lead.get('email', 'Unknown')})")
            print(f"   Assignment: {lead.get('assigned_to', 'Unassigned')} | Priority: {lead.get('priority_score', 'N/A')} | Location: {lead.get('detected_location', 'Unknown')}")
            
            try:
                # Get sequence step (check interaction history)
                sequence_step = self._get_sequence_step(lead['id'])
                
                # Get assigned SDR info
                assigned_sdr = self._get_assigned_sdr_name(lead.get('assigned_to'))
                
                # Create composer with correct sender
                composer = self.create_composer_for_lead(lead)
                print(f"   Sender: {composer.sender_email} | Sequence Step: {sequence_step}")
                
                # Send email with enhanced logging
                result = self.send_email_to_lead(lead, composer)
                results.append(result)
                
                # Log detailed lead processing
                lead_detail = self.workflow_logger.log_lead_processing(
                    lead=lead,
                    assigned_to=assigned_sdr,
                    sequence_step=sequence_step,
                    generation_method=result.get('generation_method', 'Unknown'),
                    email_sent=result['success'],
                    sender_email=composer.sender_email
                )
                lead_details.append(lead_detail)
                
                if result['success']:
                    success_count += 1
                    print(f"   ✅ {result['message']}")
                else:
                    failed_count += 1
                    errors.append(f"Lead {lead['id']}: {result['message']}")
                    print(f"   ❌ {result['message']}")
                
                # Rate limiting for production mode
                if not self.test_mode and i < len(leads):
                    print("   ⏱️ Rate limiting: waiting 2 seconds...")
                    time.sleep(2)
                    
            except Exception as e:
                failed_count += 1
                error_msg = f"Lead {lead['id']}: {str(e)}"
                errors.append(error_msg)
                print(f"   ❌ Error processing lead: {e}")
                results.append({
                    'success': False,
                    'lead_id': lead['id'],
                    'message': f"Exception: {e}"
                })
        
        # Create comprehensive campaign summary
        campaign_result = {
            "status": "completed",
            "total": len(leads),
            "successful": success_count,
            "failed": failed_count,
            "leads_processed": len(leads),
            "llm_generation_count": self.llm_generation_count,
            "template_generation_count": self.template_generation_count,
            "mode": "test" if self.test_mode else "production",
            "errors": errors,
            "results": results,
            "lead_details": lead_details,
            "timestamp": datetime.now().isoformat()
        }
        
        # Log workflow run and generation stats
        self.workflow_logger.log_workflow_run(campaign_result)
        self.workflow_logger.log_email_generation_stats(
            self.llm_generation_count, 
            self.template_generation_count
        )
        
        print(f"\n📊 Campaign completed: {success_count} successful, {failed_count} failed")
        print(f"🤖 Generation methods: {self.llm_generation_count} LLM, {self.template_generation_count} template")
        
        return campaign_result
    
    def _get_sequence_step(self, lead_id: int) -> int:
        """Determine email sequence step based on interaction history"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT COUNT(*) 
                    FROM lead_interactions 
                    WHERE lead_id = ? 
                    AND interaction_type = 'email_sent'
                    AND outcome = 'sent'
                """, (lead_id,))
                
                count = cursor.fetchone()[0]
                return count + 1  # Next sequence step
        except:
            return 1  # Default to first email
    
    def _get_assigned_sdr_name(self, assigned_to: int) -> str:
        """Get SDR name from assignment ID"""
        if not assigned_to:
            return "Unassigned"
        
        # Map assignment IDs to names (from your ASSIGNMENT_EMAIL_MAP)
        assignment_names = {
            3: "Manish",
            9: "Mahalakshmi"
        }
        
        return assignment_names.get(assigned_to, f"User#{assigned_to}")

def main():
    """CLI entry point for enhanced campaign manager"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Enhanced Email Campaign Manager with Workflow Logging')
    parser.add_argument('--db-path', 
                       default='/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db',
                       help='Path to database file')
    parser.add_argument('--production', action='store_true', 
                       help='Run in production mode (actually send emails)')
    parser.add_argument('--status', default='New',
                       help='Lead status to target')
    parser.add_argument('--max-emails', type=int, default=5,
                       help='Maximum number of emails to send')
    parser.add_argument('--hours-back', type=int, default=24,
                       help='Hours to look back for leads')
    parser.add_argument('--stats', action='store_true',
                       help='Show interaction statistics')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.db_path):
        print(f"❌ Database not found: {args.db_path}")
        return 1
    
    manager = EnhancedEmailCampaignManager(args.db_path, test_mode=not args.production)
    
    if args.stats:
        interaction_logger = EnhancedInteractionLogger(args.db_path)
        stats = interaction_logger.get_interaction_stats()
        print(f"📊 Interaction Statistics (last 30 days):")
        for interaction_type, outcomes in stats.items():
            print(f"   {interaction_type}:")
            for outcome, data in outcomes.items():
                print(f"     {outcome}: {data['count']} ({data['response_rate']}% response rate)")
        return 0
    
    result = manager.run_campaign(
        status=args.status,
        max_emails=args.max_emails,
        hours_back=args.hours_back
    )
    
    print(f"\n📊 Final result: {result}")
    return 0 if result['status'] in ['completed', 'no_leads'] else 1

if __name__ == "__main__":
    exit(main())
