#!/usr/bin/env python3
"""
Duplicate Email Record Merger

This script identifies and merges duplicate email records in the leads database.
Each email should have only ONE consolidated record with all payment history 
and enriched customer data combined.

Business Logic:
- Email should be unique (primary customer identifier)
- Multiple payments per email = repeat customer (valuable!)
- Merge all records per email into single comprehensive customer profile
- Preserve payment history as JSON array
- Keep the most complete/recent customer information

Author: Claude Code
Date: 2025-08-08
"""

import sqlite3
import json
import pandas as pd
from datetime import datetime
import os

# Database connection
DB_PATH = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
REPORTS_DIR = "/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/analysis/reports"

def ensure_reports_dir():
    """Ensure the reports directory exists"""
    if not os.path.exists(REPORTS_DIR):
        os.makedirs(REPORTS_DIR)

def analyze_duplicate_emails():
    """Analyze the scope of duplicate email records"""
    print("🔍 DUPLICATE EMAIL ANALYSIS")
    print("=" * 40)
    
    conn = sqlite3.connect(DB_PATH)
    
    # Find all duplicate emails
    duplicate_query = """
    SELECT email, COUNT(*) as record_count
    FROM leads 
    WHERE email IS NOT NULL AND email != ''
    GROUP BY email 
    HAVING COUNT(*) > 1 
    ORDER BY COUNT(*) DESC
    """
    
    duplicates_df = pd.read_sql_query(duplicate_query, conn)
    total_duplicates = len(duplicates_df)
    total_duplicate_records = duplicates_df['record_count'].sum()
    
    print(f"📊 Emails with duplicates: {total_duplicates}")
    print(f"📊 Total duplicate records: {total_duplicate_records}")
    print(f"📊 Average records per duplicate email: {total_duplicate_records/total_duplicates:.1f}")
    
    # Show top duplicates
    print(f"\n🔝 TOP 10 DUPLICATE EMAILS:")
    print("-" * 30)
    for _, row in duplicates_df.head(10).iterrows():
        print(f"{row['email']}: {row['record_count']} records")
    
    # Analyze duplicate patterns
    stripe_duplicates = pd.read_sql_query("""
        SELECT email, COUNT(*) as stripe_count
        FROM leads 
        WHERE email IS NOT NULL AND stripe_payment_id IS NOT NULL
        GROUP BY email 
        HAVING COUNT(*) > 1 
        ORDER BY COUNT(*) DESC
    """, conn)
    
    print(f"\n💳 Stripe customers with multiple payments: {len(stripe_duplicates)}")
    if len(stripe_duplicates) > 0:
        total_stripe_payments = stripe_duplicates['stripe_count'].sum()
        print(f"💳 Total Stripe payment records: {total_stripe_payments}")
        print(f"💳 Average payments per repeat customer: {total_stripe_payments/len(stripe_duplicates):.1f}")
    
    conn.close()
    return duplicates_df

def create_merger_strategy():
    """Create strategy for merging duplicate records"""
    print(f"\n🎯 MERGER STRATEGY")
    print("-" * 20)
    
    conn = sqlite3.connect(DB_PATH)
    
    # Sample detailed analysis of a duplicate email
    sample_query = """
    SELECT id, email, full_name, data_source, stripe_payment_id, payment_amount, 
           payment_status, phone, ai_experience_level, programming_experience,
           created_time, notes
    FROM leads 
    WHERE email = '<EMAIL>'
    ORDER BY created_time
    """
    
    sample_df = pd.read_sql_query(sample_query, conn)
    
    print("📋 SAMPLE: <EMAIL> records:")
    for _, row in sample_df.iterrows():
        print(f"  ID {row['id']}: {row['data_source']} | ${row['payment_amount'] or 0} | {row['created_time']}")
        print(f"    Name: {row['full_name']}")
        print(f"    Phone: {row['phone']}")
        print(f"    Stripe ID: {row['stripe_payment_id']}")
        print()
    
    conn.close()
    
    print("🔧 MERGE STRATEGY:")
    print("1. Keep ONE record per email (master record)")
    print("2. Combine payment history into JSON array")
    print("3. Use most complete customer information")
    print("4. Preserve all transaction details")
    print("5. Mark as repeat customer if multiple payments")

def execute_merge_simulation():
    """Simulate the merge process for validation"""
    print(f"\n🧪 MERGE SIMULATION")
    print("-" * 20)
    
    conn = sqlite3.connect(DB_PATH)
    
    # Get <EMAIL> records for simulation
    rajesh_records = pd.read_sql_query("""
        SELECT * FROM leads WHERE email = '<EMAIL>' ORDER BY created_time
    """, conn)
    
    # Simulate merge logic
    master_record = rajesh_records.iloc[0].copy()  # Start with first record
    payment_history = []
    
    total_payments = 0
    total_amount = 0
    
    for _, record in rajesh_records.iterrows():
        if record['stripe_payment_id']:
            payment_entry = {
                'payment_id': record['stripe_payment_id'],
                'amount': float(record['payment_amount'] or 0),
                'status': record['payment_status'],
                'date': record['created_time'],
                'currency': 'USD'  # Assuming USD
            }
            payment_history.append(payment_entry)
            total_payments += 1
            total_amount += payment_entry['amount']
        
        # Use most complete data (non-null values take precedence)
        for field in ['full_name', 'phone', 'ai_experience_level', 'programming_experience']:
            if pd.notna(record[field]) and record[field]:
                master_record[field] = record[field]
    
    # Create merged customer profile
    merged_profile = {
        'customer_email': master_record['email'],
        'customer_name': master_record['full_name'],
        'phone': master_record['phone'],
        'customer_type': 'repeat_customer' if total_payments > 1 else 'single_customer',
        'total_payments': total_payments,
        'total_spent': total_amount,
        'average_payment': total_amount / total_payments if total_payments > 0 else 0,
        'payment_history': payment_history,
        'first_purchase': min([p['date'] for p in payment_history]) if payment_history else None,
        'last_purchase': max([p['date'] for p in payment_history]) if payment_history else None,
        'customer_lifetime_value': total_amount
    }
    
    print("🎭 MERGE SIMULATION RESULT:")
    print(f"Email: {merged_profile['customer_email']}")
    print(f"Name: {merged_profile['customer_name']}")
    print(f"Phone: {merged_profile['phone']}")
    print(f"Type: {merged_profile['customer_type']}")
    print(f"Payments: {merged_profile['total_payments']}")
    print(f"Total Spent: ${merged_profile['total_spent']:.2f}")
    print(f"CLV: ${merged_profile['customer_lifetime_value']:.2f}")
    
    print(f"\n💳 PAYMENT HISTORY:")
    for payment in payment_history:
        print(f"  {payment['date']}: ${payment['amount']} ({payment['status']})")
    
    conn.close()
    
    # Save simulation results
    ensure_reports_dir()
    simulation_file = os.path.join(REPORTS_DIR, f"merge_simulation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(simulation_file, 'w') as f:
        json.dump(merged_profile, f, indent=2, default=str)
    
    print(f"\n💾 Simulation results saved to: {simulation_file}")
    
    return merged_profile

def main():
    """Main analysis function"""
    print("🔧 DUPLICATE EMAIL MERGER ANALYSIS")
    print("=" * 50)
    
    # Step 1: Analyze scope
    duplicates_df = analyze_duplicate_emails()
    
    # Step 2: Create strategy
    create_merger_strategy()
    
    # Step 3: Test simulation
    simulation_result = execute_merge_simulation()
    
    # Step 4: Recommendations
    print(f"\n💡 RECOMMENDATIONS")
    print("-" * 20)
    print("✅ IMMEDIATE: Implement email-based record merging")
    print("✅ PRIORITY: Focus on Stripe customer duplicates first")
    print("✅ BUSINESS: Recognize repeat customers as high-value segments")
    print("✅ TECHNICAL: Create unique email constraint after merge")
    print("✅ ANALYTICS: Use payment history for customer lifetime value")
    
    total_records_before = 11174  # From previous query
    unique_emails = total_records_before - duplicates_df['record_count'].sum() + len(duplicates_df)
    
    print(f"\n📊 IMPACT PROJECTION:")
    print(f"Records before merge: {total_records_before}")
    print(f"Records after merge: ~{unique_emails}")
    print(f"Reduction: ~{total_records_before - unique_emails} duplicate records")
    print(f"Data quality improvement: Unique email enforcement")

if __name__ == "__main__":
    main()