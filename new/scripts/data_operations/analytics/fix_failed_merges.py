#!/usr/bin/env python3
"""
Fix Failed Duplicate Merges

This script identifies and fixes cases where the duplicate merger failed to properly
consolidate records, <NAME_EMAIL> having both Stripe and Facebook records.

Author: Claude Code
Date: 2025-08-08
"""

import sqlite3
import json
import pandas as pd
from datetime import datetime
import os

# Database connection
DB_PATH = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"

def find_remaining_duplicates():
    """Find emails that still have duplicate records"""
    print("🔍 FINDING REMAINING DUPLICATE EMAILS")
    print("=" * 40)
    
    conn = sqlite3.connect(DB_PATH)
    
    # Find remaining duplicates
    duplicate_query = """
    SELECT email, COUNT(*) as record_count,
           GROUP_CONCAT(id) as record_ids,
           GROUP_CONCAT(data_source) as data_sources,
           GROUP_CONCAT(full_name) as names
    FROM leads 
    WHERE email IS NOT NULL AND email != ''
    GROUP BY email 
    HAVING COUNT(*) > 1 
    ORDER BY COUNT(*) DESC
    """
    
    duplicates_df = pd.read_sql_query(duplicate_query, conn)
    total_remaining = len(duplicates_df)
    
    print(f"📊 Remaining duplicate emails: {total_remaining}")
    
    if total_remaining > 0:
        print(f"\n🔝 REMAINING DUPLICATES:")
        for _, row in duplicates_df.head(10).iterrows():
            print(f"  {row['email']}: {row['record_count']} records")
            print(f"    IDs: {row['record_ids']}")
            print(f"    Sources: {row['data_sources']}")
            print(f"    Names: {row['names']}")
            print()
    
    conn.close()
    return duplicates_df

def analyze_specific_case(email):
    """Analyze a specific duplicate case in detail"""
    print(f"\n🔍 ANALYZING: {email}")
    print("-" * 30)
    
    conn = sqlite3.connect(DB_PATH)
    
    query = """
    SELECT id, full_name, data_source, stripe_payment_id, payment_amount, 
           payment_history, customer_type, phone, ai_experience_level,
           created_time, notes
    FROM leads 
    WHERE email = ?
    ORDER BY id
    """
    
    records = pd.read_sql_query(query, conn, params=[email])
    
    print(f"Found {len(records)} records:")
    for _, record in records.iterrows():
        print(f"  ID {record['id']}: {record['data_source']}")
        print(f"    Name: {record['full_name']}")
        print(f"    Payment: ${record['payment_amount'] or 0}")
        print(f"    Phone: {record['phone']}")
        print(f"    Experience: {record['ai_experience_level']}")
        print(f"    Created: {record['created_time']}")
        print()
    
    conn.close()
    return records

def fix_specific_merge(email, keep_id, merge_ids):
    """Fix a specific failed merge"""
    print(f"\n🔧 FIXING MERGE: {email}")
    print(f"Keep record ID: {keep_id}")
    print(f"Merge from IDs: {merge_ids}")
    
    conn = sqlite3.connect(DB_PATH)
    
    try:
        # Start transaction
        conn.execute("BEGIN TRANSACTION")
        
        # Get all records for this email
        all_records = pd.read_sql_query("""
            SELECT * FROM leads WHERE email = ? ORDER BY id
        """, conn, params=[email])
        
        if len(all_records) <= 1:
            print("❌ No duplicates found")
            return False
        
        # Find the master record (the one to keep)
        master_record = all_records[all_records['id'] == keep_id].iloc[0]
        other_records = all_records[all_records['id'] != keep_id]
        
        print(f"Master record: ID {master_record['id']}")
        print(f"Records to merge: {list(other_records['id'])}")
        
        # Collect the best data from all records
        merged_data = {
            'full_name': master_record['full_name'],
            'phone': master_record['phone'],
            'ai_experience_level': master_record['ai_experience_level'],
            'programming_experience': master_record['programming_experience'],
            'learning_goals': master_record['learning_goals'],
            'notes': master_record['notes'],
            'stripe_payment_id': master_record['stripe_payment_id'],
            'payment_amount': master_record['payment_amount'],
            'payment_status': master_record['payment_status'],
            'payment_history': master_record['payment_history'],
            'customer_type': master_record['customer_type'],
            'customer_lifetime_value': master_record['customer_lifetime_value'],
            'total_payments': master_record['total_payments']
        }
        
        # Merge data from other records (use non-null values)
        for _, other_record in other_records.iterrows():
            for field in ['full_name', 'phone', 'ai_experience_level', 'programming_experience', 
                         'learning_goals', 'notes', 'stripe_payment_id', 'payment_amount', 
                         'payment_status', 'payment_history', 'customer_type', 
                         'customer_lifetime_value', 'total_payments']:
                
                if pd.notna(other_record[field]) and other_record[field] and not merged_data[field]:
                    merged_data[field] = other_record[field]
                    print(f"  Using {field} from ID {other_record['id']}: {other_record[field]}")
                
                # Special case: prefer real names over "unknown_pi_" names
                if field == 'full_name' and other_record[field]:
                    if (not str(other_record[field]).startswith('unknown_pi_') and 
                        str(merged_data[field]).startswith('unknown_pi_')):
                        merged_data[field] = other_record[field]
                        print(f"  Using real name from ID {other_record['id']}: {other_record[field]}")
        
        # Update the master record with merged data
        update_query = """
        UPDATE leads SET 
            full_name = ?,
            phone = ?,
            ai_experience_level = ?,
            programming_experience = ?,
            learning_goals = ?,
            notes = ?,
            stripe_payment_id = ?,
            payment_amount = ?,
            payment_status = ?,
            payment_history = ?,
            customer_type = ?,
            customer_lifetime_value = ?,
            total_payments = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        
        conn.execute(update_query, [
            merged_data['full_name'],
            merged_data['phone'],
            merged_data['ai_experience_level'],
            merged_data['programming_experience'],
            merged_data['learning_goals'],
            merged_data['notes'],
            merged_data['stripe_payment_id'],
            merged_data['payment_amount'],
            merged_data['payment_status'],
            merged_data['payment_history'],
            merged_data['customer_type'],
            merged_data['customer_lifetime_value'],
            merged_data['total_payments'],
            keep_id
        ])
        
        # Delete the duplicate records
        for _, other_record in other_records.iterrows():
            conn.execute("DELETE FROM leads WHERE id = ?", [other_record['id']])
            print(f"  Deleted duplicate record ID {other_record['id']}")
        
        # Commit transaction
        conn.commit()
        print(f"✅ Successfully merged {len(other_records)} records into ID {keep_id}")
        
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Error during merge: {e}")
        return False
    
    finally:
        conn.close()

def main():
    """Main analysis and fix function"""
    print("🔧 FIXING FAILED DUPLICATE MERGES")
    print("=" * 50)
    
    # Step 1: Find remaining duplicates
    remaining_duplicates = find_remaining_duplicates()
    
    if len(remaining_duplicates) == 0:
        print("✅ No remaining duplicates found!")
        return
    
    # Step 2: <NAME_EMAIL> case
    dinesh_records = analyze_specific_case("<EMAIL>")
    
    # Step 3: <NAME_EMAIL> case
    # Keep ID 13186 (has real name) and merge from 1994 (has payment data)
    if len(dinesh_records) == 2:
        # Keep the Facebook record (has real name), merge Stripe payment data
        facebook_id = dinesh_records[dinesh_records['data_source'] == 'facebook']['id'].iloc[0] if 'facebook' in dinesh_records['data_source'].values else None
        stripe_id = dinesh_records[dinesh_records['data_source'] == 'stripe']['id'].iloc[0] if 'stripe' in dinesh_records['data_source'].values else None
        
        if facebook_id and stripe_id:
            success = fix_specific_merge("<EMAIL>", facebook_id, [stripe_id])
            if success:
                print(f"\n✅ Fixed <EMAIL> merge")
        
    # Step 4: Check for other similar cases (stripe + facebook pairs)
    print(f"\n🔍 CHECKING FOR SIMILAR CASES")
    print("-" * 30)
    
    similar_cases = []
    for _, row in remaining_duplicates.head(20).iterrows():  # Check top 20
        if 'stripe' in row['data_sources'] and 'facebook' in row['data_sources']:
            similar_cases.append(row['email'])
            print(f"Found similar case: {row['email']}")
    
    # Offer to fix similar cases
    if similar_cases:
        print(f"\nFound {len(similar_cases)} similar cases with stripe+facebook duplicates")
        for email in similar_cases[:5]:  # Fix first 5 similar cases
            records = analyze_specific_case(email)
            if len(records) == 2:
                facebook_id = records[records['data_source'] == 'facebook']['id'].iloc[0] if 'facebook' in records['data_source'].values else None
                stripe_id = records[records['data_source'] == 'stripe']['id'].iloc[0] if 'stripe' in records['data_source'].values else None
                
                if facebook_id and stripe_id:
                    fix_specific_merge(email, facebook_id, [stripe_id])
    
    print(f"\n✅ Merge fixes completed!")

if __name__ == "__main__":
    main()