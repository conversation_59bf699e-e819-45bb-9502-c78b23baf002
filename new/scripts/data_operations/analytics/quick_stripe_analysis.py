#!/usr/bin/env python3
"""
Quick Stripe Analysis - Fast version
"""

import sqlite3
import pandas as pd

DB_PATH = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"

def quick_stripe_analysis():
    conn = sqlite3.connect(DB_PATH)
    
    # Quick check: Do any Stripe records have enriched data?
    print("🔍 QUICK STRIPE DATA CHECK")
    print("=" * 30)
    
    # Check if ANY Stripe record has phone/experience data
    enriched_stripe = pd.read_sql_query("""
        SELECT COUNT(*) as count
        FROM leads 
        WHERE stripe_payment_id IS NOT NULL 
        AND (phone IS NOT NULL OR ai_experience_level IS NOT NULL)
    """, conn)
    
    print(f"Stripe records with ANY enriched data: {enriched_stripe['count'].iloc[0]}")
    
    # Get a few sample Stripe records
    samples = pd.read_sql_query("""
        SELECT id, email, full_name, phone, ai_experience_level, programming_experience, data_source
        FROM leads 
        WHERE stripe_payment_id IS NOT NULL 
        LIMIT 10
    """, conn)
    
    print("\n📋 SAMPLE STRIPE RECORDS:")
    for _, row in samples.iterrows():
        print(f"ID {row['id']}: {row['email']}")
        print(f"  Name: {row['full_name']}")
        print(f"  Phone: {row['phone']}")
        print(f"  AI Exp: {row['ai_experience_level']}")
        print(f"  Source: {row['data_source']}")
        print()
    
    # Check exact email matches
    email_matches = pd.read_sql_query("""
        SELECT 
            s.email,
            s.id as stripe_id,
            f.id as facebook_id,
            f.ai_experience_level,
            f.programming_experience,
            f.phone as facebook_phone
        FROM leads s
        INNER JOIN leads f ON LOWER(s.email) = LOWER(f.email)
        WHERE s.stripe_payment_id IS NOT NULL 
        AND f.data_source != 'stripe'
        AND (f.ai_experience_level IS NOT NULL OR f.programming_experience IS NOT NULL OR f.phone IS NOT NULL)
        LIMIT 10
    """, conn)
    
    print(f"📧 EXACT EMAIL MATCHES WITH ENRICHED DATA: {len(email_matches)}")
    if len(email_matches) > 0:
        for _, match in email_matches.iterrows():
            print(f"  {match['email']}: Stripe ID {match['stripe_id']} → Facebook ID {match['facebook_id']}")
            if match['ai_experience_level']:
                print(f"    AI Experience: {match['ai_experience_level']}")
            if match['programming_experience']:
                print(f"    Programming: {match['programming_experience']}")
            if match['facebook_phone']:
                print(f"    Phone: {match['facebook_phone']}")
            print()
    
    conn.close()

if __name__ == "__main__":
    quick_stripe_analysis()