{"analysis_date": "2025-08-08T06:55:55.600085", "total_stripe_payments": 668, "data_completeness": {"missing_phone": 668, "missing_ai_experience": 668, "missing_programming_exp": 668, "missing_learning_goals": 668, "missing_campaign_info": 0, "has_facebook_notes": 0}, "incomplete_records": {"count": 668, "percentage": 100.0, "sample_emails": ["<EMAIL>", "din<PERSON><PERSON><EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<PERSON><EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "deep<PERSON><PERSON><PERSON><PERSON>@gmail.com", "<EMAIL>", "<EMAIL>"]}, "email_matching": {"matched_count": 11, "unmatched_count": 507, "unmatched_emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "r<PERSON><PERSON><PERSON><PERSON>@yahoo.co.in", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "hit<PERSON><PERSON><PERSON><PERSON>@gmail.com", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "p<PERSON><PERSON>vas<PERSON><EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "ankit<PERSON><PERSON><PERSON><PERSON>@gmail.com", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<PERSON><EMAIL>", "<EMAIL>", "santhos<PERSON>.<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "j.boop<PERSON>@gmail.com", "<EMAIL>", "bala<PERSON><PERSON><PERSON><PERSON>@gmail.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "potential_name_matches": []}