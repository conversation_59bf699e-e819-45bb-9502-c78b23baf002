#!/usr/bin/env python3
"""
Stripe Data Completeness Analysis

This script analyzes Stripe payment records to identify:
1. Stripe payments that lack rich lead data (Facebook form data)
2. Email mismatches between Stripe payments and Facebook leads
3. Data completeness gaps that may indicate missing lead enrichment
4. Potential alternate email scenarios (30% of Stripe payments)

Author: Claude Code
Date: 2025-08-07
"""

import sqlite3
import json
import pandas as pd
from datetime import datetime
import os

# Database connection
DB_PATH = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
REPORTS_DIR = "/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/analysis/reports"

def ensure_reports_dir():
    """Ensure the reports directory exists"""
    if not os.path.exists(REPORTS_DIR):
        os.makedirs(REPORTS_DIR)

def analyze_stripe_data_completeness():
    """Main analysis function"""
    print("🔍 STRIPE DATA COMPLETENESS ANALYSIS")
    print("=" * 50)
    
    conn = sqlite3.connect(DB_PATH)
    
    try:
        # 1. Get all Stripe payment records
        stripe_query = """
        SELECT 
            id,
            email,
            full_name,
            phone,
            notes,
            ai_experience_level,
            programming_experience,
            learning_goals,
            campaign_name,
            account_name,
            lead_source,
            data_source,
            payment_amount,
            payment_status,
            stripe_payment_id,
            created_time
        FROM leads 
        WHERE stripe_payment_id IS NOT NULL 
        ORDER BY created_time DESC
        """
        
        stripe_df = pd.read_sql_query(stripe_query, conn)
        total_stripe_payments = len(stripe_df)
        
        print(f"📊 Total Stripe payments: {total_stripe_payments}")
        
        # 2. Analyze data completeness
        print("\n🔍 DATA COMPLETENESS ANALYSIS")
        print("-" * 30)
        
        # Check for missing enriched data fields
        missing_phone = stripe_df['phone'].isna().sum()
        missing_ai_experience = stripe_df['ai_experience_level'].isna().sum()
        missing_programming_exp = stripe_df['programming_experience'].isna().sum()
        missing_learning_goals = stripe_df['learning_goals'].isna().sum()
        missing_campaign_info = stripe_df['campaign_name'].isna().sum()
        has_facebook_notes = stripe_df['notes'].str.contains('Raw fields:', na=False).sum()
        
        print(f"Missing phone numbers: {missing_phone} ({missing_phone/total_stripe_payments*100:.1f}%)")
        print(f"Missing AI experience: {missing_ai_experience} ({missing_ai_experience/total_stripe_payments*100:.1f}%)")
        print(f"Missing programming exp: {missing_programming_exp} ({missing_programming_exp/total_stripe_payments*100:.1f}%)")
        print(f"Missing learning goals: {missing_learning_goals} ({missing_learning_goals/total_stripe_payments*100:.1f}%)")
        print(f"Missing campaign info: {missing_campaign_info} ({missing_campaign_info/total_stripe_payments*100:.1f}%)")
        print(f"Has Facebook form data: {has_facebook_notes} ({has_facebook_notes/total_stripe_payments*100:.1f}%)")
        
        # 3. Identify incomplete records (likely alternate email scenarios)
        print("\n🚨 INCOMPLETE STRIPE RECORDS")
        print("-" * 30)
        
        # Records with minimal data (likely just email + payment info)
        incomplete_mask = (
            stripe_df['phone'].isna() & 
            stripe_df['ai_experience_level'].isna() & 
            stripe_df['programming_experience'].isna() & 
            stripe_df['learning_goals'].isna() &
            ~stripe_df['notes'].str.contains('Raw fields:', na=False)
        )
        
        incomplete_records = stripe_df[incomplete_mask]
        incomplete_count = len(incomplete_records)
        
        print(f"Incomplete records: {incomplete_count} ({incomplete_count/total_stripe_payments*100:.1f}%)")
        
        # 4. Check for potential email mismatches
        print("\n📧 EMAIL MISMATCH ANALYSIS")
        print("-" * 30)
        
        # Get all non-Stripe leads with rich data
        facebook_leads_query = """
        SELECT DISTINCT email, full_name, phone, ai_experience_level, programming_experience
        FROM leads 
        WHERE data_source != 'stripe' 
        AND (
            phone IS NOT NULL OR 
            ai_experience_level IS NOT NULL OR 
            programming_experience IS NOT NULL OR
            notes LIKE '%Raw fields:%'
        )
        """
        
        facebook_df = pd.read_sql_query(facebook_leads_query, conn)
        facebook_emails = set(facebook_df['email'].str.lower())
        
        # Check Stripe emails against Facebook emails
        stripe_emails = set(stripe_df['email'].str.lower())
        matched_emails = stripe_emails.intersection(facebook_emails)
        unmatched_emails = stripe_emails - facebook_emails
        
        print(f"Stripe emails with Facebook data: {len(matched_emails)} ({len(matched_emails)/total_stripe_payments*100:.1f}%)")
        print(f"Stripe emails WITHOUT Facebook data: {len(unmatched_emails)} ({len(unmatched_emails)/total_stripe_payments*100:.1f}%)")
        
        # 5. Detailed analysis of incomplete records
        print(f"\n📋 SAMPLE INCOMPLETE RECORDS")
        print("-" * 30)
        
        if incomplete_count > 0:
            sample_incomplete = incomplete_records[['email', 'full_name', 'payment_amount', 'created_time']].head(10)
            print(sample_incomplete.to_string(index=False))
        
        # 6. Look for potential name-based matches
        print(f"\n🔍 POTENTIAL NAME-BASED MATCHES")
        print("-" * 30)
        
        # Extract names from incomplete Stripe records that don't start with 'unknown_pi_'
        incomplete_with_names = incomplete_records[
            ~incomplete_records['full_name'].str.startswith('unknown_pi_', na=False)
        ]
        
        name_matches = 0
        potential_matches = []
        
        for _, stripe_record in incomplete_with_names.iterrows():
            stripe_name = str(stripe_record['full_name']).lower()
            if stripe_name and stripe_name != 'nan':
                # Look for similar names in Facebook data
                similar_names = facebook_df[
                    facebook_df['full_name'].str.lower().str.contains(stripe_name[:10], na=False)
                ]
                if len(similar_names) > 0:
                    name_matches += 1
                    potential_matches.append({
                        'stripe_email': stripe_record['email'],
                        'stripe_name': stripe_record['full_name'],
                        'facebook_matches': similar_names[['email', 'full_name']].to_dict('records')
                    })
        
        print(f"Potential name-based matches found: {name_matches}")
        
        # 7. Generate comprehensive report
        report_data = {
            'analysis_date': datetime.now().isoformat(),
            'total_stripe_payments': total_stripe_payments,
            'data_completeness': {
                'missing_phone': int(missing_phone),
                'missing_ai_experience': int(missing_ai_experience),
                'missing_programming_exp': int(missing_programming_exp),
                'missing_learning_goals': int(missing_learning_goals),
                'missing_campaign_info': int(missing_campaign_info),
                'has_facebook_notes': int(has_facebook_notes)
            },
            'incomplete_records': {
                'count': incomplete_count,
                'percentage': round(incomplete_count/total_stripe_payments*100, 1),
                'sample_emails': incomplete_records['email'].head(20).tolist()
            },
            'email_matching': {
                'matched_count': len(matched_emails),
                'unmatched_count': len(unmatched_emails),
                'unmatched_emails': list(unmatched_emails)[:50]  # Sample
            },
            'potential_name_matches': potential_matches[:10]  # Sample
        }
        
        # Save detailed report
        ensure_reports_dir()
        report_file = os.path.join(REPORTS_DIR, f"stripe_completeness_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        # Save CSV of incomplete records for further analysis
        if incomplete_count > 0:
            csv_file = os.path.join(REPORTS_DIR, f"incomplete_stripe_records_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            incomplete_records.to_csv(csv_file, index=False)
            print(f"\n💾 Incomplete records saved to: {csv_file}")
        
        print(f"\n📄 Detailed analysis saved to: {report_file}")
        
        # 8. Recommendations
        print(f"\n💡 RECOMMENDATIONS")
        print("-" * 20)
        
        if incomplete_count > total_stripe_payments * 0.2:  # More than 20% incomplete
            print("⚠️  HIGH PRIORITY: Over 20% of Stripe payments lack enriched lead data")
            print("   → Implement email matching algorithms to connect payments to Facebook leads")
            print("   → Consider name-based fuzzy matching for alternate email scenarios")
        
        if len(unmatched_emails) > total_stripe_payments * 0.3:  # More than 30% unmatched
            print("⚠️  EMAIL MISMATCH: Over 30% of Stripe emails don't match Facebook lead emails")
            print("   → This confirms the alternate email hypothesis")
            print("   → Implement customer matching beyond just email addresses")
        
        if has_facebook_notes < total_stripe_payments * 0.1:  # Less than 10% have form data
            print("⚠️  MISSING ENRICHMENT: Very few Stripe records have Facebook form data")
            print("   → Critical data quality issue - need to connect payment to lead data")
        
        print(f"\n✅ Analysis complete. Check reports directory for detailed files.")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()

if __name__ == "__main__":
    analyze_stripe_data_completeness()