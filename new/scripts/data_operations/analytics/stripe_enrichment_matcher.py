#!/usr/bin/env python3
"""
Stripe Payment Enrichment Matcher

This script attempts to match Stripe payment records with enriched Facebook lead data
to recover missing customer information using various matching strategies:

1. Direct email matching
2. Fuzzy email matching (handling typos, variations)
3. Name-based matching with email domain correlation
4. Phone number cross-referencing (when available)

Author: Claude Code
Date: 2025-08-07
"""

import sqlite3
import json
import pandas as pd
from datetime import datetime
import os
from difflib import SequenceMatcher
import re

# Database connection
DB_PATH = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
REPORTS_DIR = "/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/analysis/reports"

def similarity(a, b):
    """Calculate similarity between two strings"""
    return SequenceMatcher(None, a.lower(), b.lower()).ratio()

def extract_name_from_email(email):
    """Extract potential name from email address"""
    if not email or '@' not in email:
        return None
    
    local_part = email.split('@')[0]
    # Remove numbers and common separators
    name_part = re.sub(r'[0-9._-]', ' ', local_part)
    return name_part.strip()

def analyze_stripe_enrichment_opportunities():
    """Find opportunities to enrich Stripe records with Facebook lead data"""
    print("🔍 STRIPE ENRICHMENT MATCHING ANALYSIS")
    print("=" * 50)
    
    conn = sqlite3.connect(DB_PATH)
    
    try:
        # Get all Stripe payments (incomplete records)
        stripe_query = """
        SELECT 
            id as stripe_id,
            email as stripe_email,
            full_name as stripe_name,
            phone as stripe_phone,
            payment_amount,
            payment_status,
            stripe_payment_id,
            created_time as stripe_created_time
        FROM leads 
        WHERE stripe_payment_id IS NOT NULL
        """
        
        stripe_df = pd.read_sql_query(stripe_query, conn)
        
        # Get all non-Stripe leads with rich data
        facebook_query = """
        SELECT 
            id as facebook_id,
            email as facebook_email,
            full_name as facebook_name,
            phone as facebook_phone,
            ai_experience_level,
            programming_experience,
            learning_goals,
            notes,
            campaign_name,
            account_name,
            data_source as facebook_source,
            created_time as facebook_created_time
        FROM leads 
        WHERE data_source != 'stripe' 
        AND (
            phone IS NOT NULL OR 
            ai_experience_level IS NOT NULL OR 
            programming_experience IS NOT NULL OR
            learning_goals IS NOT NULL OR
            notes LIKE '%Raw fields:%' OR
            notes IS NOT NULL
        )
        """
        
        facebook_df = pd.read_sql_query(facebook_query, conn)
        
        print(f"📊 Stripe payments to enrich: {len(stripe_df)}")
        print(f"📊 Facebook leads with data: {len(facebook_df)}")
        
        # Matching strategies
        matches = {
            'exact_email': [],
            'fuzzy_email': [],
            'name_based': [],
            'email_name_combo': []
        }
        
        print("\n🎯 MATCHING STRATEGIES")
        print("-" * 30)
        
        # Strategy 1: Direct email matching
        print("1️⃣ Direct email matching...")
        stripe_emails = set(stripe_df['stripe_email'].str.lower())
        facebook_emails = set(facebook_df['facebook_email'].str.lower())
        
        exact_matches = stripe_emails.intersection(facebook_emails)
        print(f"   Exact email matches: {len(exact_matches)}")
        
        for match_email in exact_matches:
            stripe_record = stripe_df[stripe_df['stripe_email'].str.lower() == match_email].iloc[0]
            facebook_record = facebook_df[facebook_df['facebook_email'].str.lower() == match_email].iloc[0]
            
            matches['exact_email'].append({
                'stripe_id': stripe_record['stripe_id'],
                'facebook_id': facebook_record['facebook_id'],
                'email': match_email,
                'confidence': 1.0,
                'match_reason': 'exact_email'
            })
        
        # Strategy 2: Fuzzy email matching (typos, variations)
        print("2️⃣ Fuzzy email matching...")
        unmatched_stripe = stripe_df[~stripe_df['stripe_email'].str.lower().isin(exact_matches)]
        unmatched_facebook = facebook_df[~facebook_df['facebook_email'].str.lower().isin(exact_matches)]
        
        fuzzy_matches = 0
        for _, stripe_record in unmatched_stripe.iterrows():
            stripe_email = stripe_record['stripe_email'].lower()
            
            for _, facebook_record in unmatched_facebook.iterrows():
                facebook_email = facebook_record['facebook_email'].lower()
                
                # Check similarity
                sim_score = similarity(stripe_email, facebook_email)
                if sim_score > 0.85:  # High similarity threshold
                    matches['fuzzy_email'].append({
                        'stripe_id': stripe_record['stripe_id'],
                        'facebook_id': facebook_record['facebook_id'],
                        'stripe_email': stripe_email,
                        'facebook_email': facebook_email,
                        'confidence': sim_score,
                        'match_reason': 'fuzzy_email'
                    })
                    fuzzy_matches += 1
                    break  # One match per Stripe record
        
        print(f"   Fuzzy email matches: {fuzzy_matches}")
        
        # Strategy 3: Name-based matching
        print("3️⃣ Name-based matching...")
        name_matches = 0
        
        # Get Stripe records that don't start with 'unknown_pi_'
        named_stripe = stripe_df[~stripe_df['stripe_name'].str.startswith('unknown_pi_', na=False)]
        
        for _, stripe_record in named_stripe.iterrows():
            stripe_name = str(stripe_record['stripe_name']).lower()
            if not stripe_name or stripe_name == 'nan':
                continue
                
            for _, facebook_record in unmatched_facebook.iterrows():
                facebook_name = str(facebook_record['facebook_name']).lower()
                if not facebook_name or facebook_name == 'nan':
                    continue
                
                # Check name similarity
                name_sim = similarity(stripe_name, facebook_name)
                if name_sim > 0.7:  # Moderate similarity for names
                    matches['name_based'].append({
                        'stripe_id': stripe_record['stripe_id'],
                        'facebook_id': facebook_record['facebook_id'],
                        'stripe_name': stripe_name,
                        'facebook_name': facebook_name,
                        'confidence': name_sim,
                        'match_reason': 'name_similarity'
                    })
                    name_matches += 1
                    break
        
        print(f"   Name-based matches: {name_matches}")
        
        # Strategy 4: Email name extraction + name matching
        print("4️⃣ Email name extraction matching...")
        email_name_matches = 0
        
        for _, stripe_record in unmatched_stripe.iterrows():
            stripe_email = stripe_record['stripe_email']
            extracted_name = extract_name_from_email(stripe_email)
            
            if not extracted_name or len(extracted_name) < 3:
                continue
                
            for _, facebook_record in unmatched_facebook.iterrows():
                facebook_name = str(facebook_record['facebook_name']).lower()
                if not facebook_name or facebook_name == 'nan':
                    continue
                
                # Check if extracted name appears in Facebook name
                if extracted_name.lower() in facebook_name or any(part in facebook_name for part in extracted_name.split()):
                    matches['email_name_combo'].append({
                        'stripe_id': stripe_record['stripe_id'],
                        'facebook_id': facebook_record['facebook_id'],
                        'stripe_email': stripe_email,
                        'extracted_name': extracted_name,
                        'facebook_name': facebook_name,
                        'confidence': 0.6,  # Lower confidence
                        'match_reason': 'email_name_extraction'
                    })
                    email_name_matches += 1
                    break
        
        print(f"   Email name extraction matches: {email_name_matches}")
        
        # Summarize results
        total_matches = sum(len(matches[strategy]) for strategy in matches)
        match_rate = (total_matches / len(stripe_df)) * 100
        
        print(f"\n📊 MATCHING RESULTS")
        print("-" * 20)
        print(f"Total potential matches: {total_matches}/{len(stripe_df)} ({match_rate:.1f}%)")
        
        for strategy, match_list in matches.items():
            if match_list:
                print(f"  {strategy}: {len(match_list)} matches")
        
        # Generate detailed enrichment opportunities report
        enrichment_opportunities = []
        
        for strategy, match_list in matches.items():
            for match in match_list:
                # Get full Facebook record for enrichment
                facebook_record = facebook_df[facebook_df['facebook_id'] == match['facebook_id']].iloc[0]
                
                enrichment_opportunity = {
                    'stripe_id': match['stripe_id'],
                    'facebook_id': match['facebook_id'],
                    'match_strategy': strategy,
                    'confidence': match['confidence'],
                    'enrichment_data': {
                        'ai_experience_level': facebook_record['ai_experience_level'],
                        'programming_experience': facebook_record['programming_experience'],
                        'learning_goals': facebook_record['learning_goals'],
                        'phone': facebook_record['facebook_phone'],
                        'campaign_info': facebook_record['campaign_name'],
                        'account_info': facebook_record['account_name'],
                        'notes': facebook_record['notes'][:200] if facebook_record['notes'] else None,  # Sample
                        'data_source': facebook_record['facebook_source']
                    }
                }
                enrichment_opportunities.append(enrichment_opportunity)
        
        # Save enrichment opportunities
        if not os.path.exists(REPORTS_DIR):
            os.makedirs(REPORTS_DIR)
        
        report_file = os.path.join(REPORTS_DIR, f"stripe_enrichment_opportunities_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(report_file, 'w') as f:
            json.dump({
                'analysis_date': datetime.now().isoformat(),
                'total_stripe_payments': len(stripe_df),
                'total_facebook_leads': len(facebook_df),
                'total_matches_found': total_matches,
                'match_rate_percent': round(match_rate, 1),
                'matches_by_strategy': {k: len(v) for k, v in matches.items()},
                'enrichment_opportunities': enrichment_opportunities
            }, f, indent=2)
        
        print(f"\n💾 Enrichment opportunities saved to: {report_file}")
        
        # Show sample high-confidence matches
        if enrichment_opportunities:
            print(f"\n🎯 SAMPLE HIGH-CONFIDENCE MATCHES")
            print("-" * 35)
            
            high_confidence = [opp for opp in enrichment_opportunities if opp['confidence'] > 0.8]
            for opp in high_confidence[:5]:
                print(f"Stripe ID {opp['stripe_id']} → Facebook ID {opp['facebook_id']}")
                print(f"  Strategy: {opp['match_strategy']} (confidence: {opp['confidence']:.2f})")
                if opp['enrichment_data']['ai_experience_level']:
                    print(f"  AI Experience: {opp['enrichment_data']['ai_experience_level']}")
                if opp['enrichment_data']['programming_experience']:
                    print(f"  Programming: {opp['enrichment_data']['programming_experience']}")
                print()
        
        # Recommendations
        print(f"\n💡 ENRICHMENT RECOMMENDATIONS")
        print("-" * 30)
        
        if total_matches > 0:
            print(f"✅ Found {total_matches} enrichment opportunities!")
            print("   → Review high-confidence matches (confidence > 0.8)")
            print("   → Implement automated enrichment for exact email matches")
            print("   → Manual review for fuzzy/name-based matches")
        else:
            print("❌ No enrichment opportunities found")
            print("   → Alternate email hypothesis confirmed")
            print("   → Need advanced customer matching strategies")
        
        if len(exact_matches) > 10:
            print(f"🚀 IMMEDIATE ACTION: {len(exact_matches)} exact email matches ready for automation")
        
    except Exception as e:
        print(f"❌ Error during enrichment analysis: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()

if __name__ == "__main__":
    analyze_stripe_enrichment_opportunities()