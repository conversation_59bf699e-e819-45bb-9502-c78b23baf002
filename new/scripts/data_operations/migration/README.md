# Data Migration Scripts

This directory contains all scripts related to lead migration and data processing for Modern AI Pro.

## Migration Overview

Successfully migrated **9,726 leads** from 7 different sources during the recent migration project.

## Directory Structure

### `migrators/`
Core migration scripts that import leads from various sources:
- `hubspot_leads_migrator.py` - HubSpot CRM lead imports
- `india_leads_migrator.py` - India-specific lead processing
- `legacy_csv_migrator.py` - Legacy CSV data migration
- `new_leads_migration.py` - General new leads processing
- `us_leads_migration.py` - US Lead CRM integration

### `analyzers/`
Data analysis and validation scripts:
- `analyze_cleaned_leads.py` - Cleaned leads analysis and reporting
- `duplicate_analysis_detailed.py` - Comprehensive duplicate detection
- `hubspot_analysis.py` - HubSpot data analysis
- `investigate_discrepancy.py` - Data discrepancy investigation
- `detailed_legacy_analysis.py` - Legacy data analysis
- `legacy_csv_analysis.py` - Legacy CSV file analysis
- `legacy_csv_analyzer.py` - Legacy CSV data analyzer

### `reports/`
Generated reports and analysis outputs:
- Migration summaries (dated files)
- Duplicate analysis reports
- Excel analysis outputs
- Audit reports
- Progress tracking files

## Key Migration Sources

1. **HubSpot CRM** - Existing customer and lead data
2. **US Lead CRM** - US-specific lead database
3. **Legacy CSV Files** - Historical lead data in CSV format
4. **India Regional Data** - India-specific lead processing
5. **Excel Imports** - Various Excel-based lead lists
6. **Workshop Connections** - Workshop attendee data
7. **Cleaned Lead Sheets** - Processed and validated leads

## Usage

1. **Before Migration**: Always backup existing data
2. **Run Analysis First**: Use analyzer scripts to understand data quality
3. **Check for Duplicates**: Run duplicate analysis before importing
4. **Review Reports**: Check generated reports for issues
5. **Test Imports**: Run with small batches first

## Migration Status

All major migrations have been completed as of August 2025. These scripts are now primarily for:
- Ongoing data maintenance
- Additional source integrations
- Data quality monitoring
- Historical analysis

## Dependencies

See the main `requirements.txt` file in the scripts root directory.