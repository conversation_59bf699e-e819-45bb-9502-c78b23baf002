#!/usr/bin/env python3
"""
Database Schema Analyzer
========================
Analyzes the leads database schema and duplicate patterns
"""

import sqlite3
from pathlib import Path

def analyze_database_schema(database_path: str):
    """Analyze the database schema and sample data"""
    
    if not Path(database_path).exists():
        print(f"❌ Database not found: {database_path}")
        return
    
    conn = sqlite3.connect(database_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("🔍 ANALYZING DATABASE SCHEMA")
        print("=" * 50)
        
        # Get table info
        cursor.execute("PRAGMA table_info(leads)")
        columns = cursor.fetchall()
        
        print("📊 TABLE COLUMNS:")
        for col in columns:
            print(f"   • {col['name']} ({col['type']}) - {'NOT NULL' if col['notnull'] else 'NULL OK'}")
        
        print("\n📈 DATABASE STATISTICS:")
        
        # Total records
        cursor.execute("SELECT COUNT(*) as total FROM leads")
        total = cursor.fetchone()['total']
        print(f"   • Total records: {total:,}")
        
        # Records with email
        cursor.execute("SELECT COUNT(*) as count FROM leads WHERE email IS NOT NULL AND email != ''")
        with_email = cursor.fetchone()['count']
        print(f"   • Records with email: {with_email:,}")
        
        # Duplicate emails
        cursor.execute("""
            SELECT COUNT(*) as duplicate_emails
            FROM (
                SELECT email
                FROM leads 
                WHERE email IS NOT NULL AND email != ''
                GROUP BY LOWER(email)
                HAVING COUNT(*) > 1
            )
        """)
        duplicate_emails = cursor.fetchone()['duplicate_emails']
        print(f"   • Emails with duplicates: {duplicate_emails:,}")
        
        # Total duplicate records
        cursor.execute("""
            SELECT SUM(count) as total_duplicates
            FROM (
                SELECT COUNT(*) as count
                FROM leads 
                WHERE email IS NOT NULL AND email != ''
                GROUP BY LOWER(email)
                HAVING COUNT(*) > 1
            )
        """)
        total_duplicates = cursor.fetchone()['total_duplicates']
        print(f"   • Total duplicate records: {total_duplicates:,}")
        
        # Sample duplicate analysis
        print(f"\n🎯 TOP 10 DUPLICATE EMAILS:")
        cursor.execute("""
            SELECT email, COUNT(*) as count
            FROM leads 
            WHERE email IS NOT NULL AND email != ''
            GROUP BY LOWER(email)
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC
            LIMIT 10
        """)
        
        top_duplicates = cursor.fetchall()
        for row in top_duplicates:
            print(f"   • {row['email']}: {row['count']} records")
        
        # Check for payment-related columns
        print(f"\n💳 PAYMENT-RELATED COLUMNS:")
        payment_columns = [col['name'] for col in columns if any(keyword in col['name'].lower() 
                          for keyword in ['payment', 'amount', 'stripe', 'paid', 'price', 'fee'])]
        
        if payment_columns:
            for col in payment_columns:
                cursor.execute(f"SELECT COUNT(*) as count FROM leads WHERE {col} IS NOT NULL AND {col} != ''")
                count = cursor.fetchone()['count']
                print(f"   • {col}: {count:,} non-null records")
        else:
            print("   • No obvious payment columns found")
        
        # Sample records
        print(f"\n📝 SAMPLE RECORDS (first 3):")
        cursor.execute("SELECT * FROM leads LIMIT 3")
        samples = cursor.fetchall()
        
        for i, record in enumerate(samples, 1):
            print(f"\n   Record {i}:")
            for key in record.keys():
                value = record[key]
                if value is not None and str(value).strip():
                    print(f"     {key}: {value}")
        
        # Check for duplicate sample
        if duplicate_emails > 0:
            print(f"\n🔍 SAMPLE DUPLICATE EMAIL RECORDS:")
            cursor.execute("""
                SELECT * FROM leads 
                WHERE LOWER(email) = (
                    SELECT LOWER(email) 
                    FROM leads 
                    WHERE email IS NOT NULL AND email != ''
                    GROUP BY LOWER(email)
                    HAVING COUNT(*) > 1
                    LIMIT 1
                )
                ORDER BY id
            """)
            
            duplicate_samples = cursor.fetchall()
            print(f"   Email: {duplicate_samples[0]['email']} ({len(duplicate_samples)} records)")
            
            for i, record in enumerate(duplicate_samples, 1):
                print(f"\n     Record {i} (ID: {record['id']}):")
                for key in record.keys():
                    value = record[key]
                    if value is not None and str(value).strip():
                        print(f"       {key}: {value}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    database_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    analyze_database_schema(database_path)