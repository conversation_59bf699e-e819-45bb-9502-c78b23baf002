#!/usr/bin/env python3
"""
Custom analysis script for Cleaned_Leads_Sheet.xlsx
Performs comprehensive analysis and duplication checking against existing database
"""

import pandas as pd
import sqlite3
import json
import os
from datetime import datetime
from pathlib import Path

def analyze_cleaned_leads_file():
    """Analyze the Cleaned_Leads_Sheet.xlsx file comprehensively"""
    
    file_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/leads_data/Cleaned_Leads_Sheet.xlsx"
    db_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    
    print("=" * 80)
    print("📊 CLEANED LEADS SHEET ANALYSIS")
    print("=" * 80)
    
    try:
        # Read Excel file
        xl_file = pd.ExcelFile(file_path)
        print(f"📁 File: {Path(file_path).name}")
        print(f"📋 Sheets found: {xl_file.sheet_names}")
        print(f"📊 Total sheets: {len(xl_file.sheet_names)}")
        
        analysis_results = {
            'file_info': {
                'name': Path(file_path).name,
                'sheets': xl_file.sheet_names,
                'total_sheets': len(xl_file.sheet_names)
            },
            'sheet_analysis': {},
            'database_comparison': {},
            'migration_recommendation': {}
        }
        
        # Analyze each sheet
        for sheet_name in xl_file.sheet_names:
            print(f"\n" + "="*60)
            print(f"📊 ANALYZING SHEET: '{sheet_name}'")
            print("="*60)
            
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                # Basic statistics
                total_rows = len(df)
                total_cols = len(df.columns)
                
                print(f"📏 Dimensions: {total_rows} rows × {total_cols} columns")
                print(f"📋 Columns: {list(df.columns)}")
                
                # Data types analysis
                print(f"\n📊 Data Types:")
                for col, dtype in df.dtypes.items():
                    non_null_count = df[col].count()
                    null_count = total_rows - non_null_count
                    print(f"   {col}: {dtype} ({non_null_count} non-null, {null_count} null)")
                
                # Check for potential lead data columns
                columns_lower = [col.lower() for col in df.columns]
                lead_indicators = {
                    'name_cols': [col for col in df.columns if any(term in col.lower() for term in ['name', 'first', 'last'])],
                    'email_cols': [col for col in df.columns if 'email' in col.lower()],
                    'phone_cols': [col for col in df.columns if any(term in col.lower() for term in ['phone', 'mobile', 'contact'])],
                    'date_cols': [col for col in df.columns if any(term in col.lower() for term in ['date', 'time', 'created', 'updated'])],
                    'payment_cols': [col for col in df.columns if any(term in col.lower() for term in ['payment', 'paid', 'amount', 'price', 'fee'])],
                    'source_cols': [col for col in df.columns if any(term in col.lower() for term in ['source', 'campaign', 'utm', 'referrer'])],
                    'workshop_cols': [col for col in df.columns if any(term in col.lower() for term in ['workshop', 'course', 'program', 'cohort'])]
                }
                
                print(f"\n🔍 Potential Lead Data Columns:")
                for category, cols in lead_indicators.items():
                    if cols:
                        print(f"   {category}: {cols}")
                
                # Sample data preview
                print(f"\n📝 Sample Data (first 5 rows):")
                if total_rows > 0:
                    sample_df = df.head(5)
                    for idx, row in sample_df.iterrows():
                        print(f"   Row {idx + 1}:")
                        for col, val in row.items():
                            val_str = str(val)[:50] + "..." if len(str(val)) > 50 else str(val)
                            print(f"      {col}: {val_str}")
                        print()
                
                # Email validation analysis
                email_cols = lead_indicators['email_cols']
                valid_emails = 0
                total_emails = 0
                unique_emails = 0
                
                if email_cols:
                    email_col = email_cols[0]  # Use first email column
                    email_series = df[email_col].dropna()
                    total_emails = len(email_series)
                    
                    # Count valid emails (simple check)
                    valid_emails = sum(1 for email in email_series if isinstance(email, str) and '@' in email and '.' in email)
                    unique_emails = len(email_series.unique())
                    
                    print(f"\n📧 Email Analysis (column: {email_col}):")
                    print(f"   Total emails: {total_emails}")
                    print(f"   Valid emails: {valid_emails}")
                    print(f"   Unique emails: {unique_emails}")
                    print(f"   Duplicates: {total_emails - unique_emails}")
                
                # Store sheet analysis
                analysis_results['sheet_analysis'][sheet_name] = {
                    'rows': total_rows,
                    'columns': total_cols,
                    'column_names': list(df.columns),
                    'data_types': df.dtypes.to_dict(),
                    'lead_indicators': lead_indicators,
                    'email_stats': {
                        'total': total_emails,
                        'valid': valid_emails,
                        'unique': unique_emails,
                        'duplicates': total_emails - unique_emails
                    },
                    'sample_data': df.head(3).to_dict('records') if total_rows > 0 else []
                }
                
                # Compare against existing database
                print(f"\n🔍 DATABASE OVERLAP ANALYSIS")
                print("-" * 40)
                
                if email_cols and total_emails > 0:
                    email_col = email_cols[0]
                    overlap_analysis = analyze_database_overlap(df, email_col, db_path)
                    analysis_results['database_comparison'][sheet_name] = overlap_analysis
                    
                    print(f"📊 Overlap Results:")
                    print(f"   Existing in DB: {overlap_analysis['existing_count']}")
                    print(f"   New records: {overlap_analysis['new_count']}")
                    print(f"   Total valid emails: {overlap_analysis['total_valid_emails']}")
                    print(f"   Overlap percentage: {overlap_analysis['overlap_percentage']:.1f}%")
                else:
                    print("   ⚠️  No email column found - cannot check database overlap")
                
            except Exception as e:
                print(f"❌ Error analyzing sheet '{sheet_name}': {e}")
                analysis_results['sheet_analysis'][sheet_name] = {'error': str(e)}
        
        # Generate migration recommendation
        recommendation = generate_migration_recommendation(analysis_results)
        analysis_results['migration_recommendation'] = recommendation
        
        print(f"\n" + "="*80)
        print("🎯 MIGRATION RECOMMENDATION")
        print("="*80)
        print(f"Recommendation: {recommendation['action']}")
        print(f"Reasoning: {recommendation['reasoning']}")
        print(f"Estimated new records: {recommendation['estimated_new_records']}")
        print(f"Value assessment: {recommendation['value_assessment']}")
        
        if recommendation['action'] == 'MIGRATE':
            print(f"\n📋 Migration Strategy:")
            for step in recommendation['migration_steps']:
                print(f"   • {step}")
        
        # Save analysis report
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f"/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/data_migration/cleaned_leads_analysis_{timestamp}.json"
        
        with open(report_path, 'w') as f:
            json.dump(analysis_results, f, indent=2, default=str)
        
        print(f"\n📄 Full analysis report saved: {report_path}")
        
        return analysis_results
        
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")
        return None

def analyze_database_overlap(df, email_col, db_path):
    """Analyze overlap between Excel data and existing database"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all existing emails from database
        cursor.execute("SELECT LOWER(email) as email FROM leads WHERE email IS NOT NULL AND email != ''")
        existing_emails = set(row[0] for row in cursor.fetchall())
        
        # Get current database stats
        cursor.execute("SELECT COUNT(*) FROM leads")
        total_db_leads = cursor.fetchone()[0]
        
        conn.close()
        
        # Clean and analyze Excel emails
        excel_emails = df[email_col].dropna()
        excel_emails_clean = set()
        
        for email in excel_emails:
            if isinstance(email, str) and '@' in email:
                clean_email = email.lower().strip()
                if clean_email:
                    excel_emails_clean.add(clean_email)
        
        # Calculate overlap
        overlapping_emails = existing_emails.intersection(excel_emails_clean)
        new_emails = excel_emails_clean - existing_emails
        
        overlap_percentage = (len(overlapping_emails) / len(excel_emails_clean) * 100) if excel_emails_clean else 0
        
        return {
            'total_db_leads': total_db_leads,
            'total_valid_emails': len(excel_emails_clean),
            'existing_count': len(overlapping_emails),
            'new_count': len(new_emails),
            'overlap_percentage': overlap_percentage,
            'overlapping_emails': list(overlapping_emails)[:10],  # Sample of overlapping emails
            'new_emails_sample': list(new_emails)[:10]  # Sample of new emails
        }
        
    except Exception as e:
        print(f"❌ Error analyzing database overlap: {e}")
        return {
            'error': str(e),
            'total_db_leads': 0,
            'total_valid_emails': 0,
            'existing_count': 0,
            'new_count': 0,
            'overlap_percentage': 0
        }

def generate_migration_recommendation(analysis_results):
    """Generate migration recommendation based on analysis"""
    
    # Calculate total potential new records
    total_new_records = 0
    total_existing_records = 0
    
    for sheet_name, sheet_data in analysis_results.get('sheet_analysis', {}).items():
        if 'error' not in sheet_data:
            comparison = analysis_results.get('database_comparison', {}).get(sheet_name, {})
            total_new_records += comparison.get('new_count', 0)
            total_existing_records += comparison.get('existing_count', 0)
    
    # Decision logic
    if total_new_records == 0:
        action = "SKIP"
        reasoning = "No new records found - all data already exists in database"
        value_assessment = "No value - would be duplicate data"
    elif total_new_records < 10:
        action = "MANUAL_REVIEW"
        reasoning = f"Only {total_new_records} new records found - recommend manual review for quality"
        value_assessment = "Low value - small number of new records"
    elif total_new_records < 100:
        action = "MIGRATE"
        reasoning = f"{total_new_records} new records found - moderate value addition"
        value_assessment = "Medium value - decent number of quality leads"
    else:
        action = "MIGRATE"
        reasoning = f"{total_new_records} new records found - significant value addition"
        value_assessment = "High value - substantial number of new leads"
    
    migration_steps = []
    if action == "MIGRATE":
        migration_steps = [
            "Use workshop_parser_template.py for data processing",
            "Apply data cleaning (email normalization, phone formatting)",
            "Set data_source = 'Cleaned_Leads_Sheet.xlsx'",
            "Import only new records (skip existing emails)",
            "Validate data quality after import",
            "Update lead statistics and dashboard"
        ]
    
    return {
        'action': action,
        'reasoning': reasoning,
        'estimated_new_records': total_new_records,
        'estimated_existing_records': total_existing_records,
        'value_assessment': value_assessment,
        'migration_steps': migration_steps
    }

if __name__ == "__main__":
    analyze_cleaned_leads_file()