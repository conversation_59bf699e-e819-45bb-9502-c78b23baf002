#!/usr/bin/env python3
"""
Detailed Legacy CSV Analysis
Deep dive into data quality and migration feasibility
"""

import pandas as pd
import sqlite3
import re
from collections import Counter

def analyze_email_quality(emails):
    """Analyze quality of email addresses"""
    invalid_patterns = []
    suspicious_domains = []
    
    for email in emails:
        if not email:
            continue
            
        # Check for common typos in email
        if any(typo in email for typo in ['.comm', '.con', '.gmavil', '.gmai.', '@mmm.', '@heh.']):
            invalid_patterns.append(email)
            
        # Check for suspicious domains
        domain = email.split('@')[-1] if '@' in email else ''
        if len(domain.split('.')) < 2 or len(domain) < 4:
            suspicious_domains.append(email)
            
    return invalid_patterns, suspicious_domains

def detailed_analysis():
    """Perform detailed analysis of both legacy files"""
    
    # Load files
    master_df = pd.read_csv('/Users/<USER>/Code/modernaipro/mai-administrative/legacy/old_crm/master_list.csv')
    leads_df = pd.read_csv('/Users/<USER>/Code/modernaipro/mai-administrative/legacy/old_crm/leads_export.csv')
    
    print("DETAILED LEGACY CSV ANALYSIS")
    print("="*60)
    
    # Master List Analysis
    print(f"\n1. MASTER_LIST.CSV DEEP DIVE:")
    print(f"   Records: {len(master_df)}")
    print(f"   Structure: {list(master_df.columns)}")
    
    # Check for email quality issues
    master_emails = master_df['email'].dropna().str.strip().str.lower()
    invalid_emails, suspicious_emails = analyze_email_quality(master_emails)
    
    print(f"   Email quality issues:")
    print(f"     - Invalid format emails: {len(invalid_emails)}")
    print(f"     - Suspicious domains: {len(suspicious_emails)}")
    
    if invalid_emails:
        print(f"   Sample invalid emails: {invalid_emails[:5]}")
    
    # Phone number analysis
    phone_lengths = master_df['phone'].astype(str).str.len()
    print(f"   Phone number analysis:")
    print(f"     - Average length: {phone_lengths.mean():.1f}")
    print(f"     - Length distribution: {dict(Counter(phone_lengths.values))}")
    
    # Name completeness
    fn_complete = master_df['fn'].notna().sum()
    ln_complete = master_df['ln'].notna().sum()
    print(f"   Name completeness:")
    print(f"     - First name: {fn_complete}/{len(master_df)} ({fn_complete/len(master_df)*100:.1f}%)")
    print(f"     - Last name: {ln_complete}/{len(master_df)} ({ln_complete/len(master_df)*100:.1f}%)")
    
    # Leads Export Analysis  
    print(f"\n2. LEADS_EXPORT.CSV DEEP DIVE:")
    print(f"   Records: {len(leads_df)}")
    print(f"   Structure: {list(leads_df.columns)}")
    
    # Lead source analysis
    lead_sources = leads_df['lead_source'].value_counts()
    print(f"   Lead sources: {dict(lead_sources)}")
    
    # Name format analysis
    name_with_spaces = leads_df['name'].str.contains(' ', na=False).sum()
    print(f"   Name format:")
    print(f"     - Names with spaces: {name_with_spaces}/{len(leads_df)} ({name_with_spaces/len(leads_df)*100:.1f}%)")
    
    # WhatsApp vs Phone analysis
    whatsapp_complete = leads_df['whatsapp'].notna().sum()
    print(f"   Contact info:")
    print(f"     - WhatsApp numbers: {whatsapp_complete}/{len(leads_df)} ({whatsapp_complete/len(leads_df)*100:.1f}%)")
    
    # Check for international numbers
    intl_numbers = leads_df['whatsapp'].str.contains(r'^\+1[0-9]', na=False).sum()
    print(f"     - International (+1) numbers: {intl_numbers}")
    
    # Data vintage analysis
    print(f"\n3. DATA VINTAGE ASSESSMENT:")
    
    # Check ID patterns in leads_export
    max_id = leads_df['id'].max()
    min_id = leads_df['id'].min()
    print(f"   leads_export.csv ID range: {min_id} to {max_id}")
    print(f"   Suggests sequential data collection over time")
    
    # Domain analysis for age assessment
    gmail_count_master = master_df['email'].str.contains('@gmail.com', na=False).sum()
    gmail_count_leads = leads_df['email'].str.contains('@gmail.com', na=False).sum()
    
    print(f"   Gmail usage (indicator of era):")
    print(f"     - master_list.csv: {gmail_count_master}/{len(master_df)} ({gmail_count_master/len(master_df)*100:.1f}%)")
    print(f"     - leads_export.csv: {gmail_count_leads}/{len(leads_df)} ({gmail_count_leads/len(leads_df)*100:.1f}%)")
    
    # Migration feasibility
    print(f"\n4. MIGRATION FEASIBILITY:")
    
    # Load existing database emails for comparison
    conn = sqlite3.connect('/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db')
    existing_emails = pd.read_sql("SELECT LOWER(TRIM(email)) as email FROM leads WHERE email IS NOT NULL", conn)
    existing_set = set(existing_emails['email'])
    conn.close()
    
    # Calculate actual new leads
    master_clean = set(master_df['email'].str.strip().str.lower().dropna())
    leads_clean = set(leads_df['email'].str.strip().str.lower().dropna())
    
    master_new = master_clean - existing_set
    leads_new = leads_clean - existing_set
    combined_new = master_new.union(leads_new)
    
    print(f"   New lead potential:")
    print(f"     - master_list.csv only: {len(master_new)} new leads")
    print(f"     - leads_export.csv only: {len(leads_new)} new leads")
    print(f"     - Combined unique new: {len(combined_new)} leads")
    print(f"     - Database growth: {len(combined_new)/len(existing_set)*100:.1f}%")
    
    # Cost-benefit analysis
    estimated_hours = 2  # Time to set up migration
    leads_per_hour_value = 50  # Arbitrary value per lead processed
    
    benefit_score = len(combined_new) * 1  # 1 point per new lead
    effort_score = estimated_hours * 10     # 10 points per hour effort
    
    roi = benefit_score / effort_score if effort_score > 0 else 0
    
    print(f"\n5. ROI ANALYSIS:")
    print(f"   New leads available: {len(combined_new)}")
    print(f"   Estimated migration hours: {estimated_hours}")
    print(f"   ROI ratio: {roi:.2f} (>1.0 = worthwhile)")
    
    # Final recommendation
    if len(combined_new) > 1000 and roi > 0.5:
        recommendation = "STRONGLY RECOMMENDED"
    elif len(combined_new) > 500 and roi > 0.3:
        recommendation = "RECOMMENDED"  
    elif len(combined_new) > 200:
        recommendation = "CONDITIONAL - if resources permit"
    else:
        recommendation = "NOT RECOMMENDED - low value"
        
    print(f"\n6. FINAL RECOMMENDATION: {recommendation}")
    
    if "RECOMMENDED" in recommendation:
        print(f"\n   MIGRATION PRIORITY:")
        print(f"   1. Migrate leads_export.csv first (higher new lead ratio)")
        print(f"   2. Skip master_list.csv (very high overlap, likely quality issues)")
        print(f"   3. Tag leads with: data_vintage='legacy', email_quality='medium', priority='Low'")
        
        print(f"\n   EXPECTED OUTCOMES:")
        print(f"   - Database growth: +{len(leads_new)} quality leads")
        print(f"   - Lead source diversity: All from Facebook")
        print(f"   - Contact completeness: ~100% names, ~99% phone numbers")

if __name__ == "__main__":
    detailed_analysis()