#!/usr/bin/env python3
"""
Detailed Duplicate Analysis for Cleaned_Leads_Sheet.xlsx
Provides comprehensive breakdown of why only 97 out of 3,047 leads are new
"""

import pandas as pd
import sqlite3
import json
import re
from datetime import datetime
from pathlib import Path
from collections import Counter

class DetailedDuplicateAnalyzer:
    def __init__(self):
        self.excel_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/leads_data/Cleaned_Leads_Sheet.xlsx"
        self.db_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
        
        print("🔍 Detailed Duplicate Analysis for Cleaned_Leads_Sheet.xlsx")
        print("=" * 70)
        
    def validate_email(self, email):
        """Validate email format"""
        if pd.isna(email) or not email or str(email).strip() == '':
            return False
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(email_pattern, str(email).strip().lower()))
    
    def clean_email(self, email):
        """Clean and normalize email"""
        if pd.isna(email) or not email:
            return None
        return str(email).strip().lower()
    
    def analyze_excel_internal_structure(self):
        """Step 1: Analyze internal structure of Excel file"""
        print("\n📊 STEP 1: Excel File Internal Structure Analysis")
        print("-" * 50)
        
        try:
            df = pd.read_excel(self.excel_path)
            
            total_rows = len(df)
            print(f"📋 Total rows in Excel file: {total_rows:,}")
            
            # Check for email column
            email_cols = [col for col in df.columns if 'email' in col.lower()]
            if not email_cols:
                print("❌ No email column found!")
                return None
            
            email_col = email_cols[0]
            print(f"📧 Using email column: '{email_col}'")
            
            # Raw email analysis
            raw_emails = df[email_col].tolist()
            non_empty_emails = [email for email in raw_emails if pd.notna(email) and str(email).strip() != '']
            
            print(f"📈 Raw email statistics:")
            print(f"   • Non-empty emails: {len(non_empty_emails):,}")
            print(f"   • Empty/NaN emails: {total_rows - len(non_empty_emails):,}")
            
            # Valid email analysis
            valid_emails = []
            invalid_emails = []
            
            for email in non_empty_emails:
                if self.validate_email(email):
                    valid_emails.append(self.clean_email(email))
                else:
                    invalid_emails.append(str(email))
            
            print(f"   • Valid email format: {len(valid_emails):,}")
            print(f"   • Invalid email format: {len(invalid_emails):,}")
            
            # Show examples of invalid emails
            if invalid_emails:
                print(f"\n📝 Examples of invalid emails:")
                for i, email in enumerate(invalid_emails[:5]):
                    print(f"   {i+1}. '{email}'")
                if len(invalid_emails) > 5:
                    print(f"   ... and {len(invalid_emails) - 5} more")
            
            # Internal duplicate analysis
            email_counts = Counter(valid_emails)
            unique_emails = list(email_counts.keys())
            duplicates = {email: count for email, count in email_counts.items() if count > 1}
            
            print(f"\n🔄 Internal duplicate analysis:")
            print(f"   • Unique valid emails: {len(unique_emails):,}")
            print(f"   • Duplicate email addresses: {len(duplicates):,}")
            print(f"   • Total duplicate instances: {sum(duplicates.values()) - len(duplicates):,}")
            
            # Show examples of internal duplicates
            if duplicates:
                print(f"\n📝 Examples of internal duplicates:")
                for i, (email, count) in enumerate(list(duplicates.items())[:5]):
                    print(f"   {i+1}. '{email}' appears {count} times")
                if len(duplicates) > 5:
                    print(f"   ... and {len(duplicates) - 5} more duplicate emails")
            
            return {
                'total_rows': total_rows,
                'non_empty_emails': len(non_empty_emails),
                'valid_emails': len(valid_emails),
                'invalid_emails': len(invalid_emails),
                'unique_valid_emails': len(unique_emails),
                'duplicate_emails': len(duplicates),
                'unique_emails_list': unique_emails,
                'duplicates_detail': duplicates,
                'invalid_examples': invalid_emails[:10]
            }
            
        except Exception as e:
            print(f"❌ Error analyzing Excel file: {e}")
            return None
    
    def analyze_database_records(self):
        """Step 2: Analyze existing database records"""
        print("\n🗄️ STEP 2: Database Records Analysis")
        print("-" * 50)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Total records
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM leads")
            total_records = cursor.fetchone()[0]
            print(f"📊 Total database records: {total_records:,}")
            
            # Records with emails
            cursor.execute("SELECT COUNT(*) FROM leads WHERE email IS NOT NULL AND email != ''")
            records_with_emails = cursor.fetchone()[0]
            print(f"📧 Records with email addresses: {records_with_emails:,}")
            
            # Get all emails from database
            cursor.execute("SELECT DISTINCT LOWER(TRIM(email)) as email FROM leads WHERE email IS NOT NULL AND email != ''")
            db_emails = [row[0] for row in cursor.fetchall()]
            print(f"🔍 Unique email addresses in database: {len(db_emails):,}")
            
            # Data source breakdown
            cursor.execute("""
                SELECT data_source, COUNT(*) as count 
                FROM leads 
                WHERE email IS NOT NULL AND email != ''
                GROUP BY data_source 
                ORDER BY count DESC
            """)
            
            data_sources = cursor.fetchall()
            print(f"\n📈 Data source breakdown:")
            for source, count in data_sources:
                print(f"   • {source}: {count:,} records")
            
            conn.close()
            
            return {
                'total_records': total_records,
                'records_with_emails': records_with_emails,
                'unique_db_emails': len(db_emails),
                'db_emails_set': set(db_emails),
                'data_sources': data_sources
            }
            
        except Exception as e:
            print(f"❌ Error analyzing database: {e}")
            return None
    
    def analyze_overlap(self, excel_analysis, db_analysis):
        """Step 3: Analyze overlap between Excel and Database"""
        print("\n🔗 STEP 3: Excel vs Database Overlap Analysis")
        print("-" * 50)
        
        excel_emails = set(excel_analysis['unique_emails_list'])
        db_emails = db_analysis['db_emails_set']
        
        # Find overlaps
        overlapping_emails = excel_emails.intersection(db_emails)
        new_emails = excel_emails - db_emails
        
        print(f"📊 Overlap analysis:")
        print(f"   • Excel unique emails: {len(excel_emails):,}")
        print(f"   • Database unique emails: {len(db_emails):,}")
        print(f"   • Overlapping emails: {len(overlapping_emails):,}")
        print(f"   • New emails from Excel: {len(new_emails):,}")
        
        # Verify the calculation
        overlap_percentage = (len(overlapping_emails) / len(excel_emails)) * 100 if excel_emails else 0
        print(f"   • Overlap percentage: {overlap_percentage:.1f}%")
        
        # Show examples of overlapping emails
        if overlapping_emails:
            print(f"\n📝 Examples of overlapping emails (already in database):")
            for i, email in enumerate(list(overlapping_emails)[:10]):
                print(f"   {i+1}. {email}")
            if len(overlapping_emails) > 10:
                print(f"   ... and {len(overlapping_emails) - 10} more")
        
        # Show examples of new emails
        if new_emails:
            print(f"\n📝 Examples of new emails (not in database):")
            for i, email in enumerate(list(new_emails)[:10]):
                print(f"   {i+1}. {email}")
            if len(new_emails) > 10:
                print(f"   ... and {len(new_emails) - 10} more")
        
        return {
            'excel_emails_count': len(excel_emails),
            'db_emails_count': len(db_emails),
            'overlapping_count': len(overlapping_emails),
            'new_emails_count': len(new_emails),
            'overlap_percentage': overlap_percentage,
            'overlapping_emails': list(overlapping_emails)[:50],  # Store first 50 for reporting
            'new_emails': list(new_emails)
        }
    
    def verify_database_query(self):
        """Step 4: Verify database query accuracy"""
        print("\n🔍 STEP 4: Database Query Verification")
        print("-" * 50)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check for case sensitivity issues
            cursor.execute("""
                SELECT COUNT(*) 
                FROM leads 
                WHERE email IS NOT NULL AND email != ''
            """)
            total_with_emails = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT COUNT(DISTINCT LOWER(TRIM(email))) 
                FROM leads 
                WHERE email IS NOT NULL AND email != ''
            """)
            unique_normalized = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT COUNT(DISTINCT email) 
                FROM leads 
                WHERE email IS NOT NULL AND email != ''
            """)
            unique_raw = cursor.fetchone()[0]
            
            print(f"📊 Database email verification:")
            print(f"   • Total records with emails: {total_with_emails:,}")
            print(f"   • Unique raw emails: {unique_raw:,}")
            print(f"   • Unique normalized emails: {unique_normalized:,}")
            
            # Check for whitespace/case variations
            case_variations = unique_raw - unique_normalized
            print(f"   • Case/whitespace variations: {case_variations:,}")
            
            if case_variations > 0:
                print(f"\n⚠️ Found {case_variations} emails with case/whitespace variations")
                cursor.execute("""
                    SELECT email, COUNT(*) as cnt
                    FROM leads 
                    WHERE email IS NOT NULL AND email != ''
                    GROUP BY LOWER(TRIM(email))
                    HAVING COUNT(*) > 1
                    LIMIT 5
                """)
                
                variations = cursor.fetchall()
                for email, count in variations:
                    print(f"   Example: '{email}' has {count} variations")
            
            conn.close()
            
            return {
                'total_with_emails': total_with_emails,
                'unique_raw': unique_raw,
                'unique_normalized': unique_normalized,
                'case_variations': case_variations
            }
            
        except Exception as e:
            print(f"❌ Error verifying database: {e}")
            return None
    
    def generate_step_by_step_breakdown(self, excel_analysis, db_analysis, overlap_analysis):
        """Generate final step-by-step breakdown"""
        print("\n📈 STEP-BY-STEP BREAKDOWN: From 3,047 rows to 97 new leads")
        print("=" * 70)
        
        print(f"1️⃣ Starting with Excel file:")
        print(f"   📋 Total rows in Excel: {excel_analysis['total_rows']:,}")
        
        print(f"\n2️⃣ After removing empty/invalid emails:")
        print(f"   ❌ Empty/NaN emails: {excel_analysis['total_rows'] - excel_analysis['non_empty_emails']:,}")
        print(f"   ❌ Invalid email format: {excel_analysis['invalid_emails']:,}")
        print(f"   ✅ Valid emails remaining: {excel_analysis['valid_emails']:,}")
        
        print(f"\n3️⃣ After removing internal duplicates:")
        print(f"   🔄 Duplicate email instances: {excel_analysis['valid_emails'] - excel_analysis['unique_valid_emails']:,}")
        print(f"   ✅ Unique valid emails: {excel_analysis['unique_valid_emails']:,}")
        
        print(f"\n4️⃣ After checking against database:")
        print(f"   🗄️ Database contains: {db_analysis['unique_db_emails']:,} unique emails")
        print(f"   🔗 Overlapping emails: {overlap_analysis['overlapping_count']:,}")
        print(f"   ✅ NEW EMAILS: {overlap_analysis['new_emails_count']:,}")
        
        print(f"\n🎯 FINAL RESULT: {overlap_analysis['new_emails_count']:,} new leads to be added")
        
        # Verification calculation
        expected_new = excel_analysis['unique_valid_emails'] - overlap_analysis['overlapping_count']
        if expected_new == overlap_analysis['new_emails_count']:
            print(f"✅ Calculation verified: {excel_analysis['unique_valid_emails']:,} - {overlap_analysis['overlapping_count']:,} = {overlap_analysis['new_emails_count']:,}")
        else:
            print(f"⚠️ Calculation mismatch detected!")
    
    def export_detailed_report(self, excel_analysis, db_analysis, overlap_analysis, verification):
        """Export comprehensive report"""
        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'file_analyzed': 'Cleaned_Leads_Sheet.xlsx',
            'excel_analysis': excel_analysis,
            'database_analysis': db_analysis,
            'overlap_analysis': overlap_analysis,
            'verification': verification,
            'summary': {
                'total_excel_rows': excel_analysis['total_rows'],
                'valid_unique_emails': excel_analysis['unique_valid_emails'],
                'database_emails': db_analysis['unique_db_emails'],
                'overlapping_emails': overlap_analysis['overlapping_count'],
                'new_emails': overlap_analysis['new_emails_count'],
                'overlap_percentage': overlap_analysis['overlap_percentage']
            }
        }
        
        output_file = f"detailed_duplicate_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            print(f"\n📄 Detailed report exported: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"❌ Error exporting report: {e}")
            return None
    
    def run_full_analysis(self):
        """Run complete duplicate analysis"""
        # Step 1: Analyze Excel file
        excel_analysis = self.analyze_excel_internal_structure()
        if not excel_analysis:
            return
        
        # Step 2: Analyze database
        db_analysis = self.analyze_database_records()
        if not db_analysis:
            return
        
        # Step 3: Analyze overlap
        overlap_analysis = self.analyze_overlap(excel_analysis, db_analysis)
        
        # Step 4: Verify database queries
        verification = self.verify_database_query()
        
        # Step 5: Generate breakdown
        self.generate_step_by_step_breakdown(excel_analysis, db_analysis, overlap_analysis)
        
        # Step 6: Export report
        report_file = self.export_detailed_report(excel_analysis, db_analysis, overlap_analysis, verification)
        
        print(f"\n🎉 ANALYSIS COMPLETE!")
        print(f"📊 Summary: From {excel_analysis['total_rows']:,} rows → {overlap_analysis['new_emails_count']:,} new leads")
        if report_file:
            print(f"📄 Detailed report: {report_file}")

def main():
    analyzer = DetailedDuplicateAnalyzer()
    analyzer.run_full_analysis()

if __name__ == "__main__":
    main()