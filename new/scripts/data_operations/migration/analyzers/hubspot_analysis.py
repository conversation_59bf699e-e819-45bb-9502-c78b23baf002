#!/usr/bin/env python3
"""
HubSpot Leads CSV Analysis Script
Comprehensive analysis of HubSpot leads data for migration assessment
"""

import pandas as pd
import numpy as np
import sqlite3
import re
from pathlib import Path
from datetime import datetime

def analyze_hubspot_csv():
    """Analyze HubSpot CSV file structure and data quality"""
    
    # Load HubSpot CSV
    hubspot_file = '/Users/<USER>/Code/modernaipro/mai-administrative/legacy/old_crm/hubspot-leads.csv'
    print('Loading HubSpot CSV file...')
    df = pd.read_csv(hubspot_file)
    
    print('=' * 60)
    print('HUBSPOT LEADS CSV ANALYSIS')
    print('=' * 60)
    print(f'Total Records: {len(df):,}')
    print(f'Total Columns: {len(df.columns)}')
    print()
    
    # 1. DATA QUALITY ASSESSMENT
    print('1. DATA QUALITY ASSESSMENT')
    print('-' * 40)
    
    # Core fields analysis
    print('Core Field Completeness:')
    core_fields = ['First Name', 'Last Name', 'Email', 'Phone Number']
    for field in core_fields:
        total = len(df)
        non_null = df[field].notna().sum()
        field_data = df[field].astype(str).str.strip()
        non_empty = (df[field].notna() & (field_data != '') & (field_data != 'nan')).sum()
        print(f'  {field:15}: {non_null:4d}/{total} ({non_null/total*100:5.1f}%) non-null, {non_empty:4d} non-empty')
    print()
    
    # Email quality analysis
    print('Email Quality Analysis:')
    emails = df['Email'].dropna()
    total_emails = len(emails)
    
    # Email format validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    valid_emails = emails[emails.str.match(email_pattern, na=False)]
    print(f'  Total emails: {total_emails:,}')
    print(f'  Valid format: {len(valid_emails):,} ({len(valid_emails)/total_emails*100:.1f}%)')
    print(f'  Duplicates: {emails.duplicated().sum():,}')
    print(f'  Unique emails: {emails.nunique():,}')
    
    # Show sample of invalid emails if any
    invalid_emails = emails[~emails.str.match(email_pattern, na=False)]
    if len(invalid_emails) > 0:
        print(f'  Sample invalid emails: {list(invalid_emails.head(5))}')
    print()
    
    # Phone number analysis
    print('Phone Number Analysis:')
    phones = df['Phone Number'].dropna()
    total_phones = len(phones)
    print(f'  Total phone numbers: {total_phones:,}')
    
    # Phone patterns
    phone_strings = phones.astype(str)
    numeric_phones = phone_strings.str.replace(r'[^0-9]', '', regex=True)
    valid_length_phones = numeric_phones[numeric_phones.str.len().between(10, 15)]
    print(f'  Valid length (10-15 digits): {len(valid_length_phones):,} ({len(valid_length_phones)/total_phones*100:.1f}%)')
    print()
    
    # 2. HUBSPOT-SPECIFIC FIELDS ANALYSIS
    print('2. HUBSPOT-SPECIFIC FIELDS ANALYSIS')
    print('-' * 40)
    
    hubspot_fields = ['Lead Status', 'Marketing contact status', 'Last Activity Date', 
                      'Contact owner', 'Create Date', 'Associated Company']
    
    for field in hubspot_fields:
        if field in df.columns:
            non_null = df[field].notna().sum()
            unique_vals = df[field].nunique()
            print(f'  {field:25}: {non_null:4d}/{len(df)} ({non_null/len(df)*100:5.1f}%) | {unique_vals} unique values')
            
            # Show unique values for categorical fields
            if field in ['Lead Status', 'Marketing contact status'] and non_null > 0:
                unique_values = df[field].value_counts().head(10)
                print(f'    Top values: {dict(unique_values)}')
    print()
    
    # 3. DATE ANALYSIS
    print('3. DATE ANALYSIS')
    print('-' * 40)
    
    date_fields = ['Create Date', 'Last Activity Date', 'Added To List On', 'Create Date.1']
    for field in date_fields:
        if field in df.columns:
            non_null = df[field].notna().sum()
            if non_null > 0:
                try:
                    dates = pd.to_datetime(df[field], errors='coerce')
                    valid_dates = dates.notna().sum()
                    if valid_dates > 0:
                        date_range = f"{dates.min().strftime('%Y-%m-%d')} to {dates.max().strftime('%Y-%m-%d')}"
                        print(f'  {field:20}: {valid_dates:4d} valid dates, range: {date_range}')
                except:
                    print(f'  {field:20}: {non_null:4d} non-null (date parsing failed)')
            else:
                print(f'  {field:20}: No dates found')
    print()
    
    # 4. COMPANY DATA ANALYSIS
    print('4. COMPANY DATA ANALYSIS')
    print('-' * 40)
    
    company_fields = ['Associated Company', 'Company name', 'City', 'Country/Region']
    for field in company_fields:
        if field in df.columns:
            non_null = df[field].notna().sum()
            unique_vals = df[field].nunique() if non_null > 0 else 0
            print(f'  {field:20}: {non_null:4d}/{len(df)} ({non_null/len(df)*100:5.1f}%) | {unique_vals} unique')
    
    # Show top countries/cities
    if 'Country/Region' in df.columns and df['Country/Region'].notna().sum() > 0:
        print('  Top Countries:')
        top_countries = df['Country/Region'].value_counts().head(5)
        for country, count in top_countries.items():
            print(f'    {country}: {count}')
    print()
    
    return df

def analyze_database_overlap(df):
    """Analyze overlap with existing database"""
    
    print('5. DATABASE OVERLAP ANALYSIS')
    print('-' * 40)
    
    # Load existing database
    db_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db'
    
    try:
        conn = sqlite3.connect(db_path)
        
        # Get existing emails
        existing_emails = pd.read_sql_query("SELECT LOWER(email) as email FROM leads WHERE email IS NOT NULL", conn)
        print(f'  Existing database emails: {len(existing_emails):,}')
        
        # Prepare HubSpot emails
        hubspot_emails = df['Email'].dropna().str.lower().str.strip()
        print(f'  HubSpot emails: {len(hubspot_emails):,}')
        print(f'  HubSpot unique emails: {hubspot_emails.nunique():,}')
        
        # Find overlaps
        existing_email_set = set(existing_emails['email'].tolist())
        hubspot_email_set = set(hubspot_emails.tolist())
        
        overlap = existing_email_set.intersection(hubspot_email_set)
        new_emails = hubspot_email_set - existing_email_set
        
        print(f'  Overlap: {len(overlap):,} emails ({len(overlap)/len(hubspot_email_set)*100:.1f}%)')
        print(f'  New emails: {len(new_emails):,} ({len(new_emails)/len(hubspot_email_set)*100:.1f}%)')
        
        # Analyze new leads potential
        new_leads_df = df[df['Email'].str.lower().isin(new_emails)]
        print(f'  New leads with complete data: {len(new_leads_df):,}')
        
        # Quality of new leads
        new_complete = new_leads_df[
            new_leads_df['First Name'].notna() & 
            new_leads_df['Email'].notna() & 
            (new_leads_df['Phone Number'].notna() | new_leads_df['Last Name'].notna())
        ]
        print(f'  New leads with name+email+phone: {len(new_complete):,}')
        
        conn.close()
        
        return {
            'total_hubspot': len(hubspot_emails),
            'unique_hubspot': hubspot_emails.nunique(),
            'overlap_count': len(overlap),
            'new_count': len(new_emails),
            'overlap_percentage': len(overlap)/len(hubspot_email_set)*100,
            'new_percentage': len(new_emails)/len(hubspot_email_set)*100,
            'new_leads_df': new_leads_df,
            'new_complete': len(new_complete)
        }
        
    except Exception as e:
        print(f'  Error connecting to database: {e}')
        return None

def migration_assessment(df, overlap_data):
    """Provide migration value assessment"""
    
    print()
    print('6. MIGRATION VALUE ASSESSMENT')
    print('-' * 40)
    
    # HubSpot metadata value
    hubspot_fields = ['Lead Status', 'Marketing contact status', 'Contact owner', 
                      'Last Activity Date', 'Associated Company']
    
    valuable_fields = []
    for field in hubspot_fields:
        if field in df.columns and df[field].notna().sum() > 100:  # Threshold for usefulness
            valuable_fields.append(field)
    
    print(f'  Valuable HubSpot fields: {len(valuable_fields)}')
    for field in valuable_fields:
        non_null = df[field].notna().sum()
        print(f'    {field}: {non_null:,} records ({non_null/len(df)*100:.1f}%)')
    
    if overlap_data:
        print()
        print('  Migration Recommendation:')
        if overlap_data['new_count'] > 500 and overlap_data['new_complete'] > 200:
            print('    RECOMMENDED - High value migration')
            print(f'    • {overlap_data["new_count"]:,} new unique emails')
            print(f'    • {overlap_data["new_complete"]:,} new leads with complete data')
            print(f'    • {len(valuable_fields)} valuable HubSpot metadata fields')
        elif overlap_data['new_count'] > 200:
            print('    CONDITIONALLY RECOMMENDED - Moderate value')
            print(f'    • {overlap_data["new_count"]:,} new unique emails')
            print(f'    • Consider if HubSpot metadata adds business value')
        else:
            print('    NOT RECOMMENDED - Low new lead count')
            print(f'    • Only {overlap_data["new_count"]:,} new unique emails')
    
    print()
    print('  HubSpot Data Enrichment Opportunities:')
    
    # Analyze HubSpot-specific value
    lead_statuses = df['Lead Status'].value_counts().head(5) if 'Lead Status' in df.columns else {}
    marketing_status = df['Marketing contact status'].value_counts().head(5) if 'Marketing contact status' in df.columns else {}
    
    if len(lead_statuses) > 0:
        print('    Lead Status distribution:', dict(lead_statuses))
    if len(marketing_status) > 0:
        print('    Marketing Status distribution:', dict(marketing_status))
    
    # Company association value
    company_data = df['Associated Company'].notna().sum() if 'Associated Company' in df.columns else 0
    if company_data > 0:
        print(f'    Company associations: {company_data:,} records')

if __name__ == "__main__":
    # Run analysis
    df = analyze_hubspot_csv()
    overlap_data = analyze_database_overlap(df)
    migration_assessment(df, overlap_data)
    
    print()
    print('=' * 60)
    print('ANALYSIS COMPLETE')
    print('=' * 60)