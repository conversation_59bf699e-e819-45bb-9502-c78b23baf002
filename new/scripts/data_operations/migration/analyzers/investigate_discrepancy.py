#!/usr/bin/env python3
"""
Investigate the discrepancy in lead count between expected (~1492) and found (1401).
"""
import pandas as pd
import openpyxl
import sys
import sqlite3

def investigate_excel_discrepancy():
    print('=== INVESTIGATING LEAD COUNT DISCREPANCY ===')
    print()
    
    excel_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/leads_data/US Lead CRM.xlsx'
    
    # Load with openpyxl for raw analysis
    wb = openpyxl.load_workbook(excel_path, data_only=True)
    ws = wb['New Leads']
    
    print('1. RAW EXCEL SHEET ANALYSIS:')
    print(f'Sheet dimensions: {ws.max_row} rows x {ws.max_column} columns')
    
    # Count rows with actual data more carefully
    data_rows = 0
    header_row = None
    
    # Find header row first
    for row in range(1, min(10, ws.max_row + 1)):  # Check first 10 rows for header
        first_cell = ws.cell(row=row, column=1).value
        if first_cell and 'contact' in str(first_cell).lower():
            header_row = row
            print(f'Header found at row: {header_row}')
            break
    
    if not header_row:
        header_row = 1
        print(f'Assuming header at row: {header_row}')
    
    # Count data rows after header
    for row in range(header_row + 1, ws.max_row + 1):
        # Check if row has meaningful data
        has_data = False
        for col in range(1, min(6, ws.max_column + 1)):  # Check first 5 columns
            cell_value = ws.cell(row=row, column=col).value
            if cell_value is not None:
                val_str = str(cell_value).strip()
                if val_str and val_str.lower() != 'nan':
                    has_data = True
                    break
        
        if has_data:
            data_rows += 1
    
    print(f'Manual count of data rows: {data_rows}')
    
    # Pandas analysis
    print()
    print('2. PANDAS ANALYSIS:')
    df = pd.read_excel(excel_path, sheet_name='New Leads', engine='openpyxl')
    print(f'Pandas reads: {len(df)} rows')
    print(f'DataFrame columns: {list(df.columns)}')
    
    # Check if pandas is stopping early due to empty rows
    print()
    print('3. CHECKING FOR EARLY TERMINATION:')
    
    # Sample rows to see where data might be
    sample_points = [1, 100, 500, 1000, 1400, 1450, 1500, 1900, 2000]
    for row_num in sample_points:
        if row_num <= ws.max_row:
            sample_data = []
            for col in range(1, min(4, ws.max_column + 1)):
                val = ws.cell(row=row_num, column=col).value
                sample_data.append(str(val)[:20] if val else 'None')
            print(f'Row {row_num}: {sample_data}')
    
    # Check for gaps in data
    print()
    print('4. CHECKING FOR DATA GAPS:')
    gap_rows = []
    continuous_data_end = None
    
    for row in range(header_row + 1, min(ws.max_row + 1, header_row + 2000)):
        has_data = False
        for col in range(1, min(6, ws.max_column + 1)):
            cell_value = ws.cell(row=row, column=col).value
            if cell_value is not None and str(cell_value).strip():
                has_data = True
                break
        
        if has_data and continuous_data_end is None:
            # Still in continuous data
            continue
        elif not has_data and continuous_data_end is None:
            # Found first gap
            continuous_data_end = row - 1
            gap_rows.append(row)
        elif has_data and continuous_data_end is not None:
            # Found data after gap
            print(f'Data gap found: rows {gap_rows[0]} to {row-1}')
            print(f'Data resumes at row: {row}')
            break
    
    if continuous_data_end:
        print(f'Continuous data ends at row: {continuous_data_end}')
        print(f'That would be {continuous_data_end - header_row} data records')
    
    return data_rows, len(df)

def check_database_state():
    print()
    print('5. DATABASE STATE CHECK:')
    
    db_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check total leads
        cursor.execute("SELECT COUNT(*) FROM leads")
        total_leads = cursor.fetchone()[0]
        print(f'Total leads in database: {total_leads}')
        
        # Check leads with data_source
        cursor.execute("SELECT data_source, COUNT(*) FROM leads WHERE data_source IS NOT NULL GROUP BY data_source")
        sources = cursor.fetchall()
        print('Leads by data source:')
        for source, count in sources:
            print(f'  {source}: {count}')
        
        # Check recent additions (if there's a created_at or similar field)
        cursor.execute("PRAGMA table_info(leads)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        print(f'Available columns: {column_names}')
        
        conn.close()
        
    except Exception as e:
        print(f'Database check error: {str(e)}')

if __name__ == '__main__':
    manual_count, pandas_count = investigate_excel_discrepancy()
    check_database_state()
    
    print()
    print('=== SUMMARY ===')
    print(f'Expected leads: ~1492')
    print(f'Manual Excel count: {manual_count}')
    print(f'Pandas count: {pandas_count}')
    print(f'Discrepancy: {1492 - pandas_count} records')
    
    if manual_count > pandas_count:
        print(f'WARNING: Manual count ({manual_count}) > Pandas count ({pandas_count})')
        print('This suggests pandas might be stopping early or missing data')
    elif manual_count == pandas_count:
        print('Manual and pandas counts match - discrepancy may be in user expectation')
    else:
        print('Manual count < pandas count - unusual, needs investigation')