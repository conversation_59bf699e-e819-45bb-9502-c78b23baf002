#!/usr/bin/env python3
"""
Legacy CSV Analysis Tool
========================
Analyzes legacy CRM CSV file for database overlap and migration value assessment.

Usage: python legacy_csv_analysis.py
"""

import pandas as pd
import sqlite3
import re
import os
from datetime import datetime
from typing import Dict, List, Tuple, Set

class LegacyCSVAnalyzer:
    def __init__(self, csv_path: str, db_path: str):
        self.csv_path = csv_path
        self.db_path = db_path
        self.csv_data = None
        self.existing_emails = set()
        
    def load_csv_data(self) -> pd.DataFrame:
        """Load and analyze CSV structure"""
        print("📊 Loading legacy CSV data...")
        
        try:
            # Read CSV with proper handling
            self.csv_data = pd.read_csv(self.csv_path)
            
            print(f"✅ CSV loaded successfully")
            print(f"   Columns: {list(self.csv_data.columns)}")
            print(f"   Total rows: {len(self.csv_data)}")
            print(f"   Memory usage: {self.csv_data.memory_usage(deep=True).sum() / 1024:.2f} KB")
            
            return self.csv_data
            
        except Exception as e:
            print(f"❌ Error loading CSV: {e}")
            return None
    
    def analyze_data_quality(self) -> Dict:
        """Comprehensive data quality analysis"""
        print("\n🔍 Analyzing data quality...")
        
        if self.csv_data is None:
            return {}
        
        analysis = {
            'total_records': len(self.csv_data),
            'column_analysis': {},
            'email_quality': {},
            'phone_quality': {},
            'name_quality': {},
            'data_completeness': {}
        }
        
        # Column-by-column analysis
        for col in self.csv_data.columns:
            if col.strip():  # Skip unnamed columns
                col_data = self.csv_data[col]
                analysis['column_analysis'][col] = {
                    'non_null_count': col_data.notna().sum(),
                    'null_count': col_data.isna().sum(),
                    'unique_values': col_data.nunique(),
                    'completion_rate': (col_data.notna().sum() / len(col_data)) * 100
                }
        
        # Email quality analysis
        if 'email' in self.csv_data.columns:
            email_col = self.csv_data['email']
            valid_emails = email_col.dropna()
            
            # Email format validation
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            valid_format = valid_emails.apply(lambda x: bool(re.match(email_pattern, str(x))))
            
            # Domain analysis
            domains = valid_emails.apply(lambda x: str(x).split('@')[1] if '@' in str(x) else 'invalid')
            
            analysis['email_quality'] = {
                'total_emails': len(valid_emails),
                'valid_format_count': valid_format.sum(),
                'invalid_format_count': len(valid_emails) - valid_format.sum(),
                'unique_emails': valid_emails.nunique(),
                'duplicate_emails': len(valid_emails) - valid_emails.nunique(),
                'top_domains': domains.value_counts().head(10).to_dict(),
                'completion_rate': (len(valid_emails) / len(self.csv_data)) * 100
            }
        
        # Phone quality analysis
        if 'phone_number' in self.csv_data.columns:
            phone_col = self.csv_data['phone_number'].dropna()
            
            # Clean phone numbers for analysis
            cleaned_phones = phone_col.apply(lambda x: re.sub(r'[^\d+]', '', str(x)))
            
            analysis['phone_quality'] = {
                'total_phones': len(phone_col),
                'unique_phones': phone_col.nunique(),
                'avg_length': cleaned_phones.str.len().mean(),
                'length_distribution': cleaned_phones.str.len().value_counts().to_dict(),
                'with_country_code': cleaned_phones.str.startswith('+').sum(),
                'completion_rate': (len(phone_col) / len(self.csv_data)) * 100
            }
        
        # Name quality analysis
        if 'name' in self.csv_data.columns:
            name_col = self.csv_data['name'].dropna()
            
            analysis['name_quality'] = {
                'total_names': len(name_col),
                'unique_names': name_col.nunique(),
                'avg_length': name_col.str.len().mean(),
                'single_word_names': name_col.apply(lambda x: len(str(x).split()) == 1).sum(),
                'multi_word_names': name_col.apply(lambda x: len(str(x).split()) > 1).sum(),
                'completion_rate': (len(name_col) / len(self.csv_data)) * 100
            }
        
        return analysis
    
    def load_existing_database(self) -> Set[str]:
        """Load existing emails from database"""
        print("\n🗄️  Loading existing database records...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get database schema info
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"   Database tables: {[t[0] for t in tables]}")
            
            # Load existing emails
            cursor.execute("SELECT email FROM leads WHERE email IS NOT NULL AND email != ''")
            existing_emails = cursor.fetchall()
            
            self.existing_emails = {email[0].lower().strip() for email in existing_emails}
            
            # Get database stats
            cursor.execute("SELECT COUNT(*) FROM leads")
            total_leads = cursor.fetchone()[0]
            
            print(f"✅ Database loaded successfully")
            print(f"   Total leads in database: {total_leads:,}")
            print(f"   Unique emails in database: {len(self.existing_emails):,}")
            
            conn.close()
            return self.existing_emails
            
        except Exception as e:
            print(f"❌ Error loading database: {e}")
            return set()
    
    def analyze_overlap(self) -> Dict:
        """Analyze overlap between CSV and existing database"""
        print("\n🔄 Analyzing overlap with existing database...")
        
        if self.csv_data is None or not self.existing_emails:
            return {}
        
        # Clean CSV emails for comparison
        csv_emails = self.csv_data['email'].dropna()
        cleaned_csv_emails = {email.lower().strip() for email in csv_emails}
        
        # Find overlaps and new emails
        overlapping_emails = cleaned_csv_emails.intersection(self.existing_emails)
        new_emails = cleaned_csv_emails.difference(self.existing_emails)
        
        # Analyze overlap by email quality
        overlap_analysis = {
            'total_csv_emails': len(cleaned_csv_emails),
            'existing_db_emails': len(self.existing_emails),
            'overlapping_emails': len(overlapping_emails),
            'new_emails': len(new_emails),
            'overlap_percentage': (len(overlapping_emails) / len(cleaned_csv_emails)) * 100 if cleaned_csv_emails else 0,
            'new_percentage': (len(new_emails) / len(cleaned_csv_emails)) * 100 if cleaned_csv_emails else 0
        }
        
        # Get sample of new emails for quality assessment
        if new_emails:
            sample_new = list(new_emails)[:10]
            overlap_analysis['sample_new_emails'] = sample_new
        
        return overlap_analysis
    
    def assess_new_leads_quality(self) -> Dict:
        """Assess quality of potential new leads"""
        print("\n⭐ Assessing quality of potential new leads...")
        
        if self.csv_data is None:
            return {}
        
        # Filter to only new leads (not in existing database)
        # Create a proper mask based on the DataFrame index
        email_col = self.csv_data['email'].dropna()
        cleaned_csv_emails = email_col.str.lower().str.strip()
        
        # Create mask for rows with valid emails
        valid_email_indices = email_col.index
        new_email_indices = []
        
        for idx in valid_email_indices:
            email = cleaned_csv_emails.loc[idx]
            if email not in self.existing_emails:
                new_email_indices.append(idx)
        
        new_leads = self.csv_data.loc[new_email_indices].copy() if new_email_indices else self.csv_data.iloc[0:0].copy()
        
        if len(new_leads) == 0:
            return {'message': 'No new leads found'}
        
        quality_assessment = {
            'total_new_leads': len(new_leads),
            'quality_metrics': {},
            'recommended_quality_tags': {
                'email_quality': 'low',
                'response_expectation': 'low', 
                'data_vintage': 'legacy',
                'lead_origin': 'old_crm'
            }
        }
        
        # Email quality for new leads
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        valid_emails = new_leads['email'].apply(lambda x: bool(re.match(email_pattern, str(x))))
        
        quality_assessment['quality_metrics']['email_validity'] = {
            'valid_emails': valid_emails.sum(),
            'invalid_emails': len(new_leads) - valid_emails.sum(),
            'validity_rate': (valid_emails.sum() / len(new_leads)) * 100
        }
        
        # Name completeness for new leads
        if 'name' in new_leads.columns:
            name_complete = new_leads['name'].notna() & (new_leads['name'].str.strip() != '')
            quality_assessment['quality_metrics']['name_completeness'] = {
                'complete_names': name_complete.sum(),
                'missing_names': len(new_leads) - name_complete.sum(),
                'completion_rate': (name_complete.sum() / len(new_leads)) * 100
            }
        
        # Phone completeness for new leads
        if 'phone_number' in new_leads.columns:
            phone_complete = new_leads['phone_number'].notna() & (new_leads['phone_number'].str.strip() != '')
            quality_assessment['quality_metrics']['phone_completeness'] = {
                'complete_phones': phone_complete.sum(),
                'missing_phones': len(new_leads) - phone_complete.sum(),
                'completion_rate': (phone_complete.sum() / len(new_leads)) * 100
            }
        
        return quality_assessment
    
    def generate_migration_recommendation(self, overlap_analysis: Dict, quality_assessment: Dict) -> Dict:
        """Generate migration recommendation based on analysis"""
        print("\n💡 Generating migration recommendation...")
        
        recommendation = {
            'should_migrate': False,
            'reasoning': [],
            'migration_strategy': {},
            'expected_outcome': {}
        }
        
        new_emails_count = overlap_analysis.get('new_emails', 0)
        overlap_percentage = overlap_analysis.get('overlap_percentage', 0)
        
        # Decision logic
        if new_emails_count < 50:
            recommendation['reasoning'].append(f"Only {new_emails_count} new leads - minimal value add")
            recommendation['should_migrate'] = False
        elif overlap_percentage > 90:
            recommendation['reasoning'].append(f"High overlap ({overlap_percentage:.1f}%) confirms most leads already imported")
            if new_emails_count > 100:
                recommendation['should_migrate'] = True
                recommendation['reasoning'].append(f"But {new_emails_count} new leads justify migration")
            else:
                recommendation['should_migrate'] = False
        else:
            recommendation['should_migrate'] = True
            recommendation['reasoning'].append(f"Moderate overlap with {new_emails_count} new leads")
        
        # Migration strategy if recommended
        if recommendation['should_migrate']:
            recommendation['migration_strategy'] = {
                'approach': 'Selective migration with quality tagging',
                'quality_tags': quality_assessment.get('recommended_quality_tags', {}),
                'validation_steps': [
                    'Email format validation',
                    'Duplicate detection within CSV',
                    'Final overlap check before insertion',
                    'Quality scoring for email segmentation'
                ],
                'post_migration': [
                    'Update email campaign segments',
                    'Mark for low-priority outreach',
                    'Monitor engagement rates'
                ]
            }
            
            recommendation['expected_outcome'] = {
                'new_leads_added': new_emails_count,
                'database_growth': f"{((new_emails_count / len(self.existing_emails)) * 100):.1f}%",
                'quality_level': 'Low (legacy data)',
                'email_segment': 'Low priority/experimental'
            }
        
        return recommendation
    
    def generate_report(self) -> str:
        """Generate comprehensive analysis report"""
        print("\n📋 Generating comprehensive analysis report...")
        
        # Run all analyses
        self.load_csv_data()
        quality_analysis = self.analyze_data_quality()
        self.load_existing_database()
        overlap_analysis = self.analyze_overlap()
        new_leads_quality = self.assess_new_leads_quality()
        recommendation = self.generate_migration_recommendation(overlap_analysis, new_leads_quality)
        
        # Generate report
        report_lines = [
            "=" * 80,
            "LEGACY CSV ANALYSIS REPORT",
            "=" * 80,
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Source CSV: {self.csv_path}",
            f"Target DB: {self.db_path}",
            "",
            "1. FILE STRUCTURE ANALYSIS",
            "-" * 30,
            f"Total records in CSV: {quality_analysis.get('total_records', 'N/A'):,}",
            f"Columns: {', '.join(self.csv_data.columns) if self.csv_data is not None else 'N/A'}",
            ""
        ]
        
        # Data Quality Section
        if quality_analysis.get('email_quality'):
            email_qual = quality_analysis['email_quality']
            report_lines.extend([
                "2. DATA QUALITY ASSESSMENT",
                "-" * 30,
                "Email Quality:",
                f"  • Total emails: {email_qual.get('total_emails', 0):,}",
                f"  • Valid format: {email_qual.get('valid_format_count', 0):,}",
                f"  • Invalid format: {email_qual.get('invalid_format_count', 0):,}",
                f"  • Unique emails: {email_qual.get('unique_emails', 0):,}",
                f"  • Duplicates: {email_qual.get('duplicate_emails', 0):,}",
                f"  • Completion rate: {email_qual.get('completion_rate', 0):.1f}%",
                ""
            ])
        
        if quality_analysis.get('phone_quality'):
            phone_qual = quality_analysis['phone_quality']
            report_lines.extend([
                "Phone Quality:",
                f"  • Total phones: {phone_qual.get('total_phones', 0):,}",
                f"  • Completion rate: {phone_qual.get('completion_rate', 0):.1f}%",
                f"  • With country code: {phone_qual.get('with_country_code', 0):,}",
                ""
            ])
        
        if quality_analysis.get('name_quality'):
            name_qual = quality_analysis['name_quality']
            report_lines.extend([
                "Name Quality:",
                f"  • Total names: {name_qual.get('total_names', 0):,}",
                f"  • Completion rate: {name_qual.get('completion_rate', 0):.1f}%",
                f"  • Multi-word names: {name_qual.get('multi_word_names', 0):,}",
                ""
            ])
        
        # Overlap Analysis Section
        if overlap_analysis:
            report_lines.extend([
                "3. DATABASE OVERLAP ANALYSIS",
                "-" * 30,
                f"CSV emails: {overlap_analysis.get('total_csv_emails', 0):,}",
                f"Existing DB emails: {overlap_analysis.get('existing_db_emails', 0):,}",
                f"Overlapping emails: {overlap_analysis.get('overlapping_emails', 0):,}",
                f"New emails: {overlap_analysis.get('new_emails', 0):,}",
                f"Overlap percentage: {overlap_analysis.get('overlap_percentage', 0):.1f}%",
                f"New percentage: {overlap_analysis.get('new_percentage', 0):.1f}%",
                ""
            ])
            
            if overlap_analysis.get('sample_new_emails'):
                report_lines.extend([
                    "Sample new emails:",
                    *[f"  • {email}" for email in overlap_analysis['sample_new_emails'][:5]],
                    ""
                ])
        
        # New Leads Quality Section
        if new_leads_quality and 'quality_metrics' in new_leads_quality:
            metrics = new_leads_quality['quality_metrics']
            report_lines.extend([
                "4. NEW LEADS QUALITY ASSESSMENT",
                "-" * 30,
                f"Potential new leads: {new_leads_quality.get('total_new_leads', 0):,}",
                ""
            ])
            
            if 'email_validity' in metrics:
                ev = metrics['email_validity']
                report_lines.extend([
                    "Email validity:",
                    f"  • Valid emails: {ev.get('valid_emails', 0):,}",
                    f"  • Validity rate: {ev.get('validity_rate', 0):.1f}%",
                    ""
                ])
            
            if 'name_completeness' in metrics:
                nc = metrics['name_completeness']
                report_lines.extend([
                    "Name completeness:",
                    f"  • Complete names: {nc.get('complete_names', 0):,}",
                    f"  • Completion rate: {nc.get('completion_rate', 0):.1f}%",
                    ""
                ])
        
        # Recommendation Section
        if recommendation:
            report_lines.extend([
                "5. MIGRATION RECOMMENDATION",
                "-" * 30,
                f"Should migrate: {'YES' if recommendation.get('should_migrate') else 'NO'}",
                "",
                "Reasoning:"
            ])
            
            for reason in recommendation.get('reasoning', []):
                report_lines.append(f"  • {reason}")
            
            report_lines.append("")
            
            if recommendation.get('migration_strategy'):
                strategy = recommendation['migration_strategy']
                report_lines.extend([
                    "Migration Strategy:",
                    f"  • Approach: {strategy.get('approach', 'N/A')}",
                    "  • Quality tags to apply:"
                ])
                
                for key, value in strategy.get('quality_tags', {}).items():
                    report_lines.append(f"    - {key}: {value}")
                
                report_lines.append("")
            
            if recommendation.get('expected_outcome'):
                outcome = recommendation['expected_outcome']
                report_lines.extend([
                    "Expected Outcome:",
                    f"  • New leads added: {outcome.get('new_leads_added', 0):,}",
                    f"  • Database growth: {outcome.get('database_growth', 'N/A')}",
                    f"  • Quality level: {outcome.get('quality_level', 'N/A')}",
                    ""
                ])
        
        report_lines.extend([
            "=" * 80,
            "END OF REPORT",
            "=" * 80
        ])
        
        return "\n".join(report_lines)

def main():
    csv_path = "/Users/<USER>/Code/modernaipro/mai-administrative/legacy/old_crm/all_leads.csv"
    db_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    
    analyzer = LegacyCSVAnalyzer(csv_path, db_path)
    report = analyzer.generate_report()
    
    print(report)
    
    # Save report to file
    report_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/data_migration/legacy_csv_analysis_report.txt"
    with open(report_path, 'w') as f:
        f.write(report)
    
    print(f"\n📄 Full report saved to: {report_path}")

if __name__ == "__main__":
    main()