#!/usr/bin/env python3
"""
Legacy CSV Analysis Tool
Analyzes overlap between legacy CSV files and existing database
"""

import pandas as pd
import sqlite3
import re
from pathlib import Path
from collections import defaultdict

class LegacyCSVAnalyzer:
    def __init__(self, db_path):
        self.db_path = db_path
        self.existing_emails = set()
        self.load_existing_emails()
        
    def load_existing_emails(self):
        """Load all existing emails from database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT LOWER(TRIM(email)) FROM leads WHERE email IS NOT NULL AND email != ''")
        self.existing_emails = {email[0] for email in cursor.fetchall()}
        conn.close()
        print(f"Loaded {len(self.existing_emails)} existing emails from database")
        
    def clean_email(self, email):
        """Clean and standardize email format"""
        if pd.isna(email) or not email:
            return None
        email = str(email).strip().lower()
        # Basic email validation
        if '@' not in email or '.' not in email:
            return None
        return email
        
    def clean_phone(self, phone):
        """Clean and standardize phone number"""
        if pd.isna(phone) or not phone:
            return None
        phone = str(phone).strip()
        # Remove common prefixes and formatting
        phone = re.sub(r'[^\d+]', '', phone)
        return phone if phone else None
        
    def analyze_file(self, file_path, file_name):
        """Analyze a single CSV file"""
        print(f"\n{'='*50}")
        print(f"ANALYZING: {file_name}")
        print(f"{'='*50}")
        
        try:
            df = pd.read_csv(file_path)
        except Exception as e:
            print(f"ERROR loading {file_name}: {e}")
            return None
            
        total_records = len(df)
        print(f"Total records: {total_records}")
        print(f"Columns: {list(df.columns)}")
        
        # Identify email column
        email_col = None
        for col in df.columns:
            if 'email' in col.lower():
                email_col = col
                break
                
        if not email_col:
            print("WARNING: No email column found!")
            return None
            
        # Clean emails
        df['clean_email'] = df[email_col].apply(self.clean_email)
        valid_emails = df[df['clean_email'].notna()]
        
        print(f"Valid emails: {len(valid_emails)} ({len(valid_emails)/total_records*100:.1f}%)")
        
        # Check for duplicates within file
        duplicate_emails = valid_emails[valid_emails.duplicated('clean_email', keep=False)]
        unique_emails = valid_emails['clean_email'].nunique()
        
        print(f"Unique emails: {unique_emails}")
        print(f"Duplicate emails within file: {len(duplicate_emails)}")
        
        # Check overlap with existing database
        file_emails = set(valid_emails['clean_email'])
        overlap_emails = file_emails.intersection(self.existing_emails)
        new_emails = file_emails - self.existing_emails
        
        overlap_percentage = len(overlap_emails) / len(file_emails) * 100 if file_emails else 0
        
        print(f"Database overlap: {len(overlap_emails)} ({overlap_percentage:.1f}%)")
        print(f"Truly new emails: {len(new_emails)} ({len(new_emails)/len(file_emails)*100:.1f}%)")
        
        # Data quality assessment
        self.assess_data_quality(df, file_name)
        
        return {
            'file_name': file_name,
            'total_records': total_records,
            'valid_emails': len(valid_emails),
            'unique_emails': unique_emails,
            'duplicate_emails': len(duplicate_emails),
            'overlap_with_db': len(overlap_emails),
            'new_emails': len(new_emails),
            'overlap_percentage': overlap_percentage,
            'file_emails': file_emails,
            'new_email_list': new_emails,
            'df': df
        }
        
    def assess_data_quality(self, df, file_name):
        """Assess data quality of the file"""
        print(f"\nDATA QUALITY ASSESSMENT - {file_name}")
        print("-" * 40)
        
        # Check name completeness
        name_cols = []
        for col in df.columns:
            if any(name_part in col.lower() for name_part in ['name', 'fn', 'ln', 'first', 'last']):
                name_cols.append(col)
                
        if name_cols:
            name_completeness = 0
            for col in name_cols:
                non_empty = df[col].notna() & (df[col].astype(str).str.strip() != '')
                completeness = non_empty.sum() / len(df) * 100
                print(f"{col} completeness: {completeness:.1f}%")
                name_completeness = max(name_completeness, completeness)
        else:
            print("No name columns identified")
            
        # Check phone completeness
        phone_cols = []
        for col in df.columns:
            if any(phone_part in col.lower() for phone_part in ['phone', 'whatsapp', 'mobile']):
                phone_cols.append(col)
                
        if phone_cols:
            for col in phone_cols:
                non_empty = df[col].notna() & (df[col].astype(str).str.strip() != '')
                completeness = non_empty.sum() / len(df) * 100
                print(f"{col} completeness: {completeness:.1f}%")
        else:
            print("No phone columns identified")
            
        # Check for additional columns
        other_cols = [col for col in df.columns if not any(
            key in col.lower() for key in ['email', 'name', 'fn', 'ln', 'first', 'last', 'phone', 'whatsapp', 'mobile', 'id']
        )]
        
        if other_cols:
            print(f"Additional columns: {other_cols}")
            
    def compare_files(self, analysis1, analysis2):
        """Compare overlap between the two files themselves"""
        print(f"\n{'='*50}")
        print("CROSS-FILE COMPARISON")
        print(f"{'='*50}")
        
        emails1 = analysis1['file_emails']
        emails2 = analysis2['file_emails']
        
        overlap_between_files = emails1.intersection(emails2)
        only_in_file1 = emails1 - emails2
        only_in_file2 = emails2 - emails1
        
        print(f"Emails in both files: {len(overlap_between_files)}")
        print(f"Only in {analysis1['file_name']}: {len(only_in_file1)}")
        print(f"Only in {analysis2['file_name']}: {len(only_in_file2)}")
        
        # Combined analysis
        all_emails = emails1.union(emails2)
        combined_new = all_emails - self.existing_emails
        
        print(f"\nCOMBINED ANALYSIS:")
        print(f"Total unique emails across both files: {len(all_emails)}")
        print(f"Combined new emails (not in database): {len(combined_new)}")
        
        return {
            'cross_file_overlap': len(overlap_between_files),
            'only_in_file1': len(only_in_file1),
            'only_in_file2': len(only_in_file2),
            'combined_unique': len(all_emails),
            'combined_new': len(combined_new)
        }
        
    def migration_recommendation(self, analysis1, analysis2, comparison):
        """Provide migration recommendation"""
        print(f"\n{'='*50}")
        print("MIGRATION RECOMMENDATION")
        print(f"{'='*50}")
        
        # Calculate migration value
        total_new_leads = comparison['combined_new']
        effort_threshold = 100  # Minimum new leads to justify migration effort
        
        print(f"Combined new leads available: {total_new_leads}")
        print(f"Current database size: {len(self.existing_emails)}")
        print(f"Potential database growth: {total_new_leads/len(self.existing_emails)*100:.1f}%")
        
        # Quality assessment
        avg_overlap = (analysis1['overlap_percentage'] + analysis2['overlap_percentage']) / 2
        
        print(f"\nQUALITY INDICATORS:")
        print(f"Average database overlap: {avg_overlap:.1f}% (higher = older/stale data)")
        print(f"Cross-file overlap: {comparison['cross_file_overlap']} emails")
        
        # Recommendation logic
        if total_new_leads < effort_threshold:
            recommendation = "NOT RECOMMENDED"
            reason = f"Only {total_new_leads} new leads available - below threshold of {effort_threshold}"
        elif avg_overlap > 80:
            recommendation = "NOT RECOMMENDED" 
            reason = f"High database overlap ({avg_overlap:.1f}%) indicates very stale data"
        elif total_new_leads < 500:
            recommendation = "CONDITIONAL"
            reason = f"Moderate value ({total_new_leads} new leads) - migrate only if time permits"
        else:
            recommendation = "RECOMMENDED"
            reason = f"Good value with {total_new_leads} new leads"
            
        print(f"\nRECOMMENDATION: {recommendation}")
        print(f"REASON: {reason}")
        
        if recommendation in ["RECOMMENDED", "CONDITIONAL"]:
            print(f"\nMIGRATION STRATEGY:")
            print(f"- Tag with quality markers: email_quality='low', response_expectation='very_low'")
            print(f"- Tag with vintage: data_vintage='old_legacy'")
            print(f"- Set priority: priority='Very Low'")
            
            # Which file to migrate first
            file1_value = analysis1['new_emails'] / analysis1['total_records']
            file2_value = analysis2['new_emails'] / analysis2['total_records']
            
            if file1_value > file2_value:
                print(f"- Migrate {analysis1['file_name']} first (better new lead ratio)")
            else:
                print(f"- Migrate {analysis2['file_name']} first (better new lead ratio)")
                
        return recommendation

def main():
    # File paths
    db_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    master_list_path = "/Users/<USER>/Code/modernaipro/mai-administrative/legacy/old_crm/master_list.csv"
    leads_export_path = "/Users/<USER>/Code/modernaipro/mai-administrative/legacy/old_crm/leads_export.csv"
    
    analyzer = LegacyCSVAnalyzer(db_path)
    
    # Analyze both files
    analysis1 = analyzer.analyze_file(master_list_path, "master_list.csv")
    analysis2 = analyzer.analyze_file(leads_export_path, "leads_export.csv")
    
    if analysis1 and analysis2:
        comparison = analyzer.compare_files(analysis1, analysis2)
        recommendation = analyzer.migration_recommendation(analysis1, analysis2, comparison)
        
        # Sample of new emails for verification
        print(f"\nSAMPLE NEW EMAILS FROM master_list.csv:")
        for email in list(analysis1['new_email_list'])[:10]:
            print(f"  - {email}")
            
        print(f"\nSAMPLE NEW EMAILS FROM leads_export.csv:")
        for email in list(analysis2['new_email_list'])[:10]:
            print(f"  - {email}")

if __name__ == "__main__":
    main()