#!/usr/bin/env python3
"""
Comprehensive Duplicate Email Record Merger
============================================

CRITICAL MISSION: Clean up major data quality issue where 637 emails have duplicate records,
including 106 high-value repeat Stripe customers.

This script consolidates duplicate email records into single customer profiles with:
- Complete payment history preservation
- Customer lifetime value calculation
- VIP customer identification
- Comprehensive data validation

Author: Claude <PERSON> (Excel Data Migration Specialist)
Date: 2025-08-08
"""

import sqlite3
import json
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import sys

# Set up comprehensive logging
log_filename = f"duplicate_merger_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DuplicateEmailMerger:
    """Comprehensive duplicate email record merger with payment history preservation"""
    
    def __init__(self, database_path: str):
        self.database_path = database_path
        self.backup_path = f"{database_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.merged_count = 0
        self.deleted_count = 0
        self.total_clv = 0.0
        self.repeat_customers = 0
        self.vip_customers = 0
        
    def create_backup(self) -> bool:
        """Create database backup before any changes"""
        try:
            shutil.copy2(self.database_path, self.backup_path)
            logger.info(f"✅ Database backup created: {self.backup_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to create backup: {e}")
            return False
    
    def analyze_duplicates(self) -> Dict[str, Any]:
        """Analyze duplicate email patterns and payment data"""
        logger.info("🔍 Analyzing duplicate email patterns...")
        
        conn = sqlite3.connect(self.database_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            # Find duplicate emails with counts
            cursor.execute("""
                SELECT email, COUNT(*) as count 
                FROM leads 
                WHERE email IS NOT NULL AND email != '' 
                GROUP BY LOWER(email) 
                HAVING COUNT(*) > 1
                ORDER BY COUNT(*) DESC
            """)
            
            duplicates = cursor.fetchall()
            duplicate_emails = [row['email'] for row in duplicates]
            total_duplicate_records = sum(row['count'] for row in duplicates)
            
            # Analyze Stripe customers in duplicates
            cursor.execute("""
                SELECT email, COUNT(*) as count, 
                       SUM(CASE WHEN stripe_payment_id IS NOT NULL THEN 1 ELSE 0 END) as stripe_payments,
                       SUM(CASE WHEN payment_amount IS NOT NULL THEN payment_amount ELSE 0 END) as total_amount
                FROM leads 
                WHERE LOWER(email) IN ({})
                GROUP BY LOWER(email)
                ORDER BY total_amount DESC
            """.format(','.join(['?' for _ in duplicate_emails])), 
            [email.lower() for email in duplicate_emails])
            
            stripe_analysis = cursor.fetchall()
            stripe_customers = [row for row in stripe_analysis if row['stripe_payments'] > 0]
            high_value_customers = [row for row in stripe_analysis if row['total_amount'] > 500]
            
            analysis = {
                'total_duplicate_emails': len(duplicate_emails),
                'total_duplicate_records': total_duplicate_records,
                'stripe_customers_with_duplicates': len(stripe_customers),
                'high_value_customers': len(high_value_customers),
                'top_duplicates': duplicates[:10],
                'stripe_duplicate_analysis': stripe_customers[:20]
            }
            
            logger.info(f"📊 Analysis Complete:")
            logger.info(f"   • {analysis['total_duplicate_emails']} emails have duplicates")
            logger.info(f"   • {analysis['total_duplicate_records']} total duplicate records")
            logger.info(f"   • {analysis['stripe_customers_with_duplicates']} Stripe customers with duplicates")
            logger.info(f"   • {analysis['high_value_customers']} high-value customers (>$500)")
            
            return analysis
            
        finally:
            conn.close()
    
    def get_duplicate_records(self, email: str) -> List[sqlite3.Row]:
        """Get all duplicate records for a given email"""
        conn = sqlite3.connect(self.database_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                SELECT * FROM leads 
                WHERE LOWER(email) = LOWER(?)
                ORDER BY 
                    CASE WHEN stripe_payment_id IS NOT NULL THEN 1 ELSE 2 END,
                    CASE WHEN payment_amount IS NOT NULL THEN payment_amount ELSE 0 END DESC,
                    CASE WHEN created_time IS NOT NULL THEN created_time ELSE '1900-01-01' END DESC
            """, (email,))
            
            return cursor.fetchall()
            
        finally:
            conn.close()
    
    def choose_master_record(self, records: List[sqlite3.Row]) -> sqlite3.Row:
        """Choose the master record based on data completeness and business value"""
        def record_score(record):
            score = 0
            
            # Stripe payment gets highest priority
            if record['stripe_payment_id']:
                score += 1000
            
            # Payment amount priority
            if record['payment_amount']:
                score += float(record['payment_amount'])
            
            # Data completeness scoring
            fields = ['full_name', 'phone', 'ai_experience_level', 'workshop_type', 'created_time']
            for field in fields:
                if record[field]:
                    score += 10
            
            # Recent records preferred
            if record['created_time']:
                try:
                    date_obj = datetime.fromisoformat(record['created_time'].replace('Z', '+00:00'))
                    days_old = (datetime.now() - date_obj.replace(tzinfo=None)).days
                    score += max(0, 365 - days_old) / 10  # Newer is better
                except:
                    pass
            
            return score
        
        # Sort by score and return the best record
        sorted_records = sorted(records, key=record_score, reverse=True)
        return sorted_records[0]
    
    def create_payment_history(self, records: List[sqlite3.Row]) -> Dict[str, Any]:
        """Create comprehensive payment history from all records"""
        payment_history = []
        total_spent = 0.0
        payment_dates = []
        
        for record in records:
            if record['payment_amount'] and float(record['payment_amount']) > 0:
                payment_entry = {
                    'payment_id': record['stripe_payment_id'] or f"internal_{record['id']}",
                    'amount': float(record['payment_amount']),
                    'status': record['payment_status'] or 'Paid',
                    'date': record['payment_date'] or record['created_time'],
                    'currency': record['workshop_currency'] or 'USD',
                    'course': record['workshop_type'] or 'Unknown',
                    'source': record['data_source'] or 'Unknown'
                }
                
                payment_history.append(payment_entry)
                total_spent += payment_entry['amount']
                
                if payment_entry['date']:
                    try:
                        date_obj = datetime.fromisoformat(payment_entry['date'].replace('Z', '+00:00'))
                        payment_dates.append(date_obj)
                    except:
                        pass
        
        # Sort payment history by date
        payment_history.sort(key=lambda x: x['date'] or '1900-01-01')
        payment_dates.sort()
        
        # Calculate customer metrics
        customer_metrics = {
            'total_payments': len(payment_history),
            'total_spent': round(total_spent, 2),
            'first_purchase': payment_dates[0].isoformat() if payment_dates else None,
            'last_purchase': payment_dates[-1].isoformat() if payment_dates else None,
            'customer_type': 'repeat_customer' if len(payment_history) > 1 else 'single_purchase',
            'customer_lifetime_value': round(total_spent, 2),
            'is_vip': total_spent >= 1000,
            'payment_frequency_days': None
        }
        
        # Calculate payment frequency for repeat customers
        if len(payment_dates) > 1:
            total_days = (payment_dates[-1] - payment_dates[0]).days
            customer_metrics['payment_frequency_days'] = round(total_days / (len(payment_dates) - 1), 1)
        
        return {
            'payment_history': payment_history,
            'customer_metrics': customer_metrics
        }
    
    def consolidate_customer_data(self, master_record: sqlite3.Row, all_records: List[sqlite3.Row]) -> Dict[str, Any]:
        """Consolidate customer data from all duplicate records"""
        consolidated = dict(master_record)
        
        # Use the most complete data from all records
        for record in all_records:
            # Fill in missing data from other records
            for field in ['full_name', 'phone', 'ai_experience_level', 'workshop_type']:
                if not consolidated[field] and record[field]:
                    consolidated[field] = record[field]
                elif consolidated[field] and record[field] and len(str(record[field])) > len(str(consolidated[field])):
                    # Use longer/more complete version
                    consolidated[field] = record[field]
            
            # Collect notes from all records
            notes_list = []
            for r in all_records:
                if r['notes']:
                    notes_list.append(str(r['notes']))
            if notes_list:
                consolidated['notes'] = ' | '.join(set(notes_list))  # Remove duplicates
        
        return consolidated
    
    def merge_duplicate_email(self, email: str) -> bool:
        """Merge all duplicate records for a single email"""
        try:
            records = self.get_duplicate_records(email)
            if len(records) <= 1:
                return True
            
            logger.info(f"🔄 Merging {len(records)} records for {email}")
            
            # Choose master record
            master_record = self.choose_master_record(records)
            
            # Create payment history
            payment_data = self.create_payment_history(records)
            
            # Consolidate customer data
            consolidated_data = self.consolidate_customer_data(master_record, records)
            
            # Update master record with consolidated data and payment history
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            try:
                # Update the master record
                update_fields = []
                update_values = []
                
                # Standard fields
                for field, value in consolidated_data.items():
                    if field not in ['id', 'created_time']:  # Don't update ID or creation date
                        update_fields.append(f"{field} = ?")
                        update_values.append(value)
                
                # Add payment history as JSON
                update_fields.append("payment_history = ?")
                update_values.append(json.dumps(payment_data, indent=2))
                
                # Add customer metrics
                metrics = payment_data['customer_metrics']
                update_fields.extend([
                    "customer_type = ?",
                    "customer_lifetime_value = ?",
                    "total_payments = ?",
                    "is_vip_customer = ?"
                ])
                update_values.extend([
                    metrics['customer_type'],
                    metrics['customer_lifetime_value'],
                    metrics['total_payments'],
                    1 if metrics['is_vip'] else 0
                ])
                
                update_values.append(master_record['id'])
                
                cursor.execute(f"""
                    UPDATE leads 
                    SET {', '.join(update_fields)}, 
                        updated_at = datetime('now')
                    WHERE id = ?
                """, update_values)
                
                # Delete duplicate records (keep master)
                duplicate_ids = [str(record['id']) for record in records if record['id'] != master_record['id']]
                if duplicate_ids:
                    cursor.execute(f"""
                        DELETE FROM leads 
                        WHERE id IN ({','.join(['?' for _ in duplicate_ids])})
                    """, duplicate_ids)
                    
                    self.deleted_count += len(duplicate_ids)
                
                conn.commit()
                self.merged_count += 1
                
                # Update statistics
                self.total_clv += metrics['customer_lifetime_value']
                if metrics['customer_type'] == 'repeat_customer':
                    self.repeat_customers += 1
                if metrics['is_vip']:
                    self.vip_customers += 1
                
                logger.info(f"✅ Merged {email}: {len(records)} → 1 record, CLV: ${metrics['customer_lifetime_value']}")
                return True
                
            except Exception as e:
                conn.rollback()
                logger.error(f"❌ Failed to merge {email}: {e}")
                return False
                
            finally:
                conn.close()
                
        except Exception as e:
            logger.error(f"❌ Error processing {email}: {e}")
            return False
    
    def add_missing_columns(self):
        """Add missing columns for enhanced customer tracking"""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        try:
            # Get existing columns
            cursor.execute("PRAGMA table_info(leads)")
            existing_columns = [row[1] for row in cursor.fetchall()]
            
            # Add missing columns
            new_columns = [
                ('payment_history', 'TEXT'),
                ('customer_type', 'TEXT'),
                ('customer_lifetime_value', 'REAL DEFAULT 0'),
                ('total_payments', 'INTEGER DEFAULT 0'),
                ('is_vip_customer', 'INTEGER DEFAULT 0')
            ]
            
            for column_name, column_type in new_columns:
                if column_name not in existing_columns:
                    cursor.execute(f"ALTER TABLE leads ADD COLUMN {column_name} {column_type}")
                    logger.info(f"➕ Added column: {column_name}")
            
            conn.commit()
            
        except Exception as e:
            logger.error(f"❌ Failed to add columns: {e}")
            conn.rollback()
            
        finally:
            conn.close()
    
    def validate_results(self) -> Dict[str, Any]:
        """Validate merger results and data integrity"""
        logger.info("🔍 Validating merger results...")
        
        conn = sqlite3.connect(self.database_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            # Check for remaining duplicates
            cursor.execute("""
                SELECT email, COUNT(*) as count 
                FROM leads 
                WHERE email IS NOT NULL AND email != '' 
                GROUP BY LOWER(email) 
                HAVING COUNT(*) > 1
            """)
            remaining_duplicates = cursor.fetchall()
            
            # Get total records
            cursor.execute("SELECT COUNT(*) as total FROM leads")
            total_records = cursor.fetchone()['total']
            
            # Check payment history preservation
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM leads 
                WHERE payment_history IS NOT NULL AND payment_history != ''
            """)
            records_with_payment_history = cursor.fetchone()['count']
            
            # Check customer segmentation
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN customer_type = 'repeat_customer' THEN 1 ELSE 0 END) as repeat_customers,
                    SUM(CASE WHEN is_vip_customer = 1 THEN 1 ELSE 0 END) as vip_customers,
                    SUM(CASE WHEN customer_lifetime_value > 0 THEN customer_lifetime_value ELSE 0 END) as total_clv
                FROM leads
            """)
            stats = cursor.fetchone()
            
            validation = {
                'remaining_duplicates': len(remaining_duplicates),
                'total_records': total_records,
                'records_with_payment_history': records_with_payment_history,
                'repeat_customers': stats['repeat_customers'],
                'vip_customers': stats['vip_customers'],
                'total_customer_lifetime_value': round(stats['total_clv'] or 0, 2)
            }
            
            logger.info("📊 Validation Results:")
            logger.info(f"   • Remaining duplicates: {validation['remaining_duplicates']}")
            logger.info(f"   • Total records: {validation['total_records']}")
            logger.info(f"   • Records with payment history: {validation['records_with_payment_history']}")
            logger.info(f"   • Repeat customers: {validation['repeat_customers']}")
            logger.info(f"   • VIP customers: {validation['vip_customers']}")
            logger.info(f"   • Total CLV: ${validation['total_customer_lifetime_value']:,.2f}")
            
            return validation
            
        finally:
            conn.close()
    
    def execute_merger(self) -> bool:
        """Execute the complete duplicate email merger process"""
        logger.info("🚀 Starting Comprehensive Duplicate Email Merger")
        logger.info("=" * 60)
        
        try:
            # Phase 1: Backup & Safety
            logger.info("📦 Phase 1: Creating Database Backup")
            if not self.create_backup():
                logger.error("❌ Backup failed - aborting merger")
                return False
            
            # Add missing columns
            logger.info("🔧 Adding missing database columns")
            self.add_missing_columns()
            
            # Phase 2: Analysis
            logger.info("🔍 Phase 2: Analyzing Duplicate Patterns")
            analysis = self.analyze_duplicates()
            
            if analysis['total_duplicate_emails'] == 0:
                logger.info("✅ No duplicate emails found!")
                return True
            
            # Phase 3: Consolidation
            logger.info(f"🔄 Phase 3: Consolidating {analysis['total_duplicate_emails']} Duplicate Emails")
            
            # Get all duplicate emails
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT email 
                FROM leads 
                WHERE email IS NOT NULL AND email != '' 
                GROUP BY LOWER(email) 
                HAVING COUNT(*) > 1
            """)
            duplicate_emails = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            # Process each duplicate email
            success_count = 0
            for i, email in enumerate(duplicate_emails, 1):
                logger.info(f"Processing {i}/{len(duplicate_emails)}: {email}")
                if self.merge_duplicate_email(email):
                    success_count += 1
                
                # Progress update every 50 emails
                if i % 50 == 0:
                    logger.info(f"Progress: {i}/{len(duplicate_emails)} emails processed")
            
            # Phase 4: Validation
            logger.info("✅ Phase 4: Validating Results")
            validation = self.validate_results()
            
            # Final Report
            logger.info("🎉 DUPLICATE EMAIL MERGER COMPLETE!")
            logger.info("=" * 60)
            logger.info(f"📊 MERGER STATISTICS:")
            logger.info(f"   • Emails processed: {len(duplicate_emails)}")
            logger.info(f"   • Successful mergers: {success_count}")
            logger.info(f"   • Records deleted: {self.deleted_count}")
            logger.info(f"   • Repeat customers identified: {self.repeat_customers}")
            logger.info(f"   • VIP customers identified: {self.vip_customers}")
            logger.info(f"   • Total CLV preserved: ${self.total_clv:,.2f}")
            logger.info(f"   • Database size reduction: {analysis['total_duplicate_records'] - len(duplicate_emails)} records")
            logger.info(f"📁 Backup saved: {self.backup_path}")
            logger.info(f"📋 Log saved: {log_filename}")
            
            return success_count == len(duplicate_emails)
            
        except Exception as e:
            logger.error(f"❌ Critical error during merger: {e}")
            return False

def main():
    """Main execution function"""
    database_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    
    if not Path(database_path).exists():
        logger.error(f"❌ Database not found: {database_path}")
        return False
    
    merger = DuplicateEmailMerger(database_path)
    success = merger.execute_merger()
    
    if success:
        logger.info("🎉 Duplicate email merger completed successfully!")
    else:
        logger.error("❌ Duplicate email merger failed!")
        logger.info(f"🔄 Database backup available: {merger.backup_path}")
    
    return success

if __name__ == "__main__":
    main()