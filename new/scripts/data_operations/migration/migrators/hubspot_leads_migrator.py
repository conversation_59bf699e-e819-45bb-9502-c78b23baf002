#!/usr/bin/env python3
"""
HubSpot Leads CSV Migration Script
Migrates 2,763 HubSpot leads from CSV to the leads database.
"""

import pandas as pd
import sqlite3
import json
import re
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class HubSpotLeadsMigrator:
    def __init__(self):
        self.csv_path = "/Users/<USER>/Code/modernaipro/mai-administrative/legacy/old_crm/hubspot-leads.csv"
        self.db_path = project_root / "dashboard" / "database" / "leads.db"
        
        print("🚀 HubSpot Leads Migration Started")
        print(f"📁 Source CSV: {self.csv_path}")
        print(f"🗄️  Database: {self.db_path}")
    
    def analyze_csv_structure(self):
        """Analyze the HubSpot CSV structure and data patterns"""
        print("\n🔍 Analyzing HubSpot CSV structure...")
        
        df = pd.read_csv(self.csv_path)
        
        print(f"📊 Total rows: {len(df)}")
        print(f"📊 Columns: {list(df.columns)}")
        print(f"\n🔍 First few rows (raw data):")
        print(df.head(5).to_string())
        
        # Data quality analysis
        print(f"\n📋 Data quality analysis:")
        print(f"   • Total records: {len(df)}")
        print(f"   • Records with email: {df['Email'].notna().sum()}")
        print(f"   • Records with phone: {df['Phone Number'].notna().sum()}")
        print(f"   • Unique emails: {df['Email'].nunique()}")
        
        # Phone number analysis - convert to string first
        df_phone_str = df['Phone Number'].astype(str)
        phone_patterns = {
            'indian_mobile': df_phone_str.str.match(r'^91\d{10}$', na=False).sum(),
            'indian_with_prefix': df_phone_str.str.match(r'^919\d{9}$', na=False).sum(),
            'international': df_phone_str.str.match(r'^1\d{10}$', na=False).sum(),
            'other_format': df['Phone Number'].notna().sum() - df_phone_str.str.match(r'^(91\d{10}|919\d{9}|1\d{10})$', na=False).sum()
        }
        
        print(f"\n📞 Phone number patterns:")
        for pattern, count in phone_patterns.items():
            print(f"   • {pattern}: {count}")
        
        return df
    
    def check_existing_emails(self, emails):
        """Check which emails already exist in the database"""
        print(f"\n🔍 Checking for existing emails in database...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create placeholders for the IN clause
        placeholders = ','.join(['?' for _ in emails])
        query = f"SELECT email FROM leads WHERE email IN ({placeholders})"
        
        cursor.execute(query, emails)
        existing_emails = set(row[0] for row in cursor.fetchall())
        
        conn.close()
        
        print(f"   • Existing emails found: {len(existing_emails)}")
        print(f"   • New emails to import: {len(emails) - len(existing_emails)}")
        
        return existing_emails
    
    def clean_phone_number(self, phone):
        """Clean and standardize phone numbers"""
        if pd.isna(phone):
            return None
        
        # Convert to string and remove any non-digits
        phone_str = str(phone).strip()
        digits_only = re.sub(r'\D', '', phone_str)
        
        if not digits_only:
            return None
        
        # Return the phone number as-is (we'll preserve original format)
        return phone_str
    
    def determine_phone_format(self, phone):
        """Determine phone number format for metadata"""
        if not phone:
            return None
        
        digits_only = re.sub(r'\D', '', str(phone))
        
        if digits_only.startswith('91') and len(digits_only) == 12:
            return "indian"
        elif digits_only.startswith('1') and len(digits_only) == 11:
            return "international"
        else:
            return "other"
    
    def create_hubspot_metadata(self, row):
        """Create HubSpot metadata JSON"""
        phone_format = self.determine_phone_format(row['Phone Number'])
        
        metadata = {
            "hubspot_record_id": str(row['Record ID - Contact']),
            "marketing_contact_status": row['Marketing contact status'],
            "create_date": "2024-05-14",
            "data_source": "hubspot_leads_2024",
            "data_quality": "high",
            "phone_format": phone_format
        }
        
        return json.dumps(metadata)
    
    def prepare_lead_data(self, row):
        """Prepare lead data for database insertion"""
        # Combine first and last name
        first_name = str(row['First Name']).strip() if pd.notna(row['First Name']) else ""
        last_name = str(row['Last Name']).strip() if pd.notna(row['Last Name']) else ""
        full_name = f"{first_name} {last_name}".strip()
        
        # Clean email
        email = str(row['Email']).strip().lower() if pd.notna(row['Email']) else ""
        
        # Clean phone
        phone = self.clean_phone_number(row['Phone Number'])
        
        # Determine location based on phone pattern
        location = "India"
        if phone:
            phone_format = self.determine_phone_format(phone)
            if phone_format == "international":
                location = "International"
        
        # Create metadata
        hubspot_metadata = self.create_hubspot_metadata(row)
        
        lead_data = {
            'full_name': full_name,
            'email': email,
            'phone': phone,
            'location': location,
            'data_source': 'hubspot_leads',
            'lead_source': 'hubspot_crm',
            'created_time': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'notes': hubspot_metadata,
            'status': 'new',
            'priority': 'standard'
        }
        
        return lead_data
    
    def insert_leads_batch(self, leads_data):
        """Insert leads in batch to database"""
        print(f"\n💾 Inserting {len(leads_data)} leads to database...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Prepare insert query
        insert_query = """
        INSERT INTO leads (
            full_name, email, phone, location, data_source, lead_source,
            created_time, updated_at, notes, status, priority
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        # Prepare batch data
        batch_data = []
        for lead in leads_data:
            batch_data.append((
                lead['full_name'],
                lead['email'],
                lead['phone'],
                lead['location'],
                lead['data_source'],
                lead['lead_source'],
                lead['created_time'],
                lead['updated_at'],
                lead['notes'],
                lead['status'],
                lead['priority']
            ))
        
        # Execute batch insert
        cursor.executemany(insert_query, batch_data)
        
        inserted_count = cursor.rowcount
        conn.commit()
        conn.close()
        
        print(f"✅ Successfully inserted {inserted_count} leads")
        return inserted_count
    
    def verify_migration(self):
        """Verify the migration results"""
        print(f"\n🔍 Verifying migration results...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get total count
        cursor.execute("SELECT COUNT(*) FROM leads")
        total_leads = cursor.fetchone()[0]
        
        # Get HubSpot leads count
        cursor.execute("SELECT COUNT(*) FROM leads WHERE data_source = 'hubspot_leads'")
        hubspot_leads = cursor.fetchone()[0]
        
        # Get unique emails
        cursor.execute("SELECT COUNT(DISTINCT email) FROM leads")
        unique_emails = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ Migration verification:")
        print(f"   • Total leads in database: {total_leads}")
        print(f"   • HubSpot leads imported: {hubspot_leads}")
        print(f"   • Unique emails: {unique_emails}")
        print(f"   • Expected database growth: 5,131 → {total_leads}")
        
        return {
            'total_leads': total_leads,
            'hubspot_leads': hubspot_leads,
            'unique_emails': unique_emails
        }
    
    def run_migration(self):
        """Execute the complete migration process"""
        print("🚀 Starting HubSpot leads migration...\n")
        
        # Step 1: Analyze CSV
        df = self.analyze_csv_structure()
        
        # Step 2: Check for existing emails
        all_emails = df['Email'].dropna().str.strip().str.lower().tolist()
        existing_emails = self.check_existing_emails(all_emails)
        
        # Step 3: Filter out existing emails
        df['email_clean'] = df['Email'].str.strip().str.lower()
        df_new = df[~df['email_clean'].isin(existing_emails)]
        
        print(f"\n📊 Migration summary:")
        print(f"   • Total records in CSV: {len(df)}")
        print(f"   • Records already in database: {len(existing_emails)}")
        print(f"   • New records to import: {len(df_new)}")
        
        # Step 4: Prepare lead data
        print(f"\n🔧 Preparing lead data...")
        leads_data = []
        
        for idx, row in df_new.iterrows():
            try:
                lead_data = self.prepare_lead_data(row)
                leads_data.append(lead_data)
            except Exception as e:
                print(f"⚠️  Error preparing lead at row {idx}: {e}")
                continue
        
        print(f"   • Successfully prepared: {len(leads_data)} leads")
        
        # Step 5: Insert leads
        if leads_data:
            inserted_count = self.insert_leads_batch(leads_data)
            
            # Step 6: Verify migration
            verification = self.verify_migration()
            
            print(f"\n🎉 Migration completed successfully!")
            print(f"   • Records processed: {len(df)}")
            print(f"   • Records skipped (duplicates): {len(existing_emails)}")
            print(f"   • Records inserted: {inserted_count}")
            print(f"   • Database total: {verification['total_leads']}")
            
            return verification
        else:
            print(f"\n⚠️  No new leads to import")
            return None

def main():
    """Main execution function"""
    migrator = HubSpotLeadsMigrator()
    results = migrator.run_migration()
    
    if results:
        print(f"\n📈 Final Statistics:")
        print(f"   • Database grew from 5,131 to {results['total_leads']} leads")
        print(f"   • Growth: {results['total_leads'] - 5131} leads ({((results['total_leads'] - 5131) / 5131 * 100):.1f}%)")
        print(f"   • HubSpot leads imported: {results['hubspot_leads']}")
        print(f"   • Total unique emails: {results['unique_emails']}")

if __name__ == "__main__":
    main()