#!/usr/bin/env python3
"""
India Leads Migration Script for All Enrollees.xlsx
Specialized parser for India workshop enrollees with quality marking for email segmentation
"""

import pandas as pd
import sqlite3
import sys
import json
import re
from datetime import datetime
from pathlib import Path
from collections import defaultdict

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class IndiaLeadsMigrator:
    def __init__(self):
        self.assets_dir = project_root / "assets" / "leads_data"
        self.db_path = project_root / "dashboard" / "database" / "leads.db"
        self.file_path = self.assets_dir / "All Enrollees.xlsx"
        
        print("🇮🇳 India Leads Migrator initialized")
        print(f"📁 File: {self.file_path}")
        print(f"🗄️  Database: {self.db_path}")
        print(f"🎯 Target: India workshop enrollees with email segmentation")
    
    def load_workshop_dates(self):
        """Load workshop dates from the Dates sheet"""
        print("\n📅 Loading workshop date mappings...")
        
        dates_df = pd.read_excel(self.file_path, sheet_name='Dates')
        workshop_dates = {}
        
        for _, row in dates_df.iterrows():
            workshop_code = row.iloc[0]
            workshop_date = row.iloc[1]
            workshop_dates[workshop_code] = workshop_date
            print(f"   {workshop_code}: {workshop_date.strftime('%Y-%m-%d')}")
        
        return workshop_dates
    
    def assess_email_quality(self, email):
        """Assess email quality for segmentation purposes"""
        if not email or pd.isna(email):
            return "invalid"
        
        email = str(email).lower().strip()
        
        # Basic format check
        if '@' not in email or '.' not in email:
            return "invalid"
        
        # Domain quality assessment
        domain = email.split('@')[1] if '@' in email else ''
        
        # High quality domains
        high_quality_domains = {
            'gmail.com', 'outlook.com', 'hotmail.com', 'yahoo.com', 
            'icloud.com', 'protonmail.com', 'live.com'
        }
        
        # Professional/company domains
        professional_indicators = [
            '.edu', '.org', '.gov', 'company.com', 'corp.com',
            'ibm.com', 'microsoft.com', 'google.com', 'amazon.com'
        ]
        
        # Low quality indicators
        low_quality_indicators = [
            'tempmail', 'disposable', '10minute', 'guerrillamail',
            'mailinator', 'yopmail'
        ]
        
        # Check for low quality
        for indicator in low_quality_indicators:
            if indicator in email:
                return "low"
        
        # Check for professional domains
        for indicator in professional_indicators:
            if indicator in domain:
                return "high"
        
        # Check for high quality personal domains
        if domain in high_quality_domains:
            return "high"
        
        # Default to medium for other domains
        return "medium"
    
    def assess_name_quality(self, name):
        """Assess name completeness for quality scoring"""
        if not name or pd.isna(name):
            return "low"
        
        name = str(name).strip()
        
        # Empty or very short
        if len(name) <= 2:
            return "low"
        
        # Has multiple parts (likely first + last name)
        if ' ' in name and len(name.split()) >= 2:
            # Check if parts are meaningful (not just initials)
            parts = name.split()
            if all(len(part) > 1 for part in parts):
                return "high"
            else:
                return "medium"
        
        # Single word but reasonable length
        if len(name) > 3:
            return "medium"
        
        return "low"
    
    def determine_overall_quality(self, email_quality, name_quality, has_phone=False):
        """Determine overall lead quality for segmentation"""
        quality_scores = {
            "high": 3,
            "medium": 2,
            "low": 1,
            "invalid": 0
        }
        
        email_score = quality_scores.get(email_quality, 0)
        name_score = quality_scores.get(name_quality, 0)
        phone_bonus = 0.5 if has_phone else 0
        
        total_score = email_score + name_score + phone_bonus
        
        if total_score >= 5:
            return "high"
        elif total_score >= 3:
            return "medium"
        else:
            return "low"
    
    def determine_vintage_category(self, workshop_date):
        """Categorize data by vintage for campaign expectations"""
        if not workshop_date:
            return "unknown"
        
        current_date = datetime.now()
        date_diff = current_date - workshop_date
        
        if date_diff.days > 365:  # More than 1 year old
            return "old"
        elif date_diff.days > 180:  # 6 months to 1 year
            return "medium"
        else:
            return "recent"
    
    def process_workshop_sheet(self, sheet_name, workshop_dates):
        """Process a single workshop sheet"""
        print(f"\n📄 Processing sheet: {sheet_name}")
        
        try:
            df = pd.read_excel(self.file_path, sheet_name=sheet_name)
            print(f"   📊 Raw records: {len(df)}")
            
            # Remove empty rows
            df = df.dropna(how='all')
            
            # Get column names (always 2 columns: name, email)
            if len(df.columns) < 2:
                print(f"   ⚠️  Warning: Sheet has less than 2 columns, skipping")
                return []
            
            name_col = df.columns[0]
            email_col = df.columns[1]
            
            print(f"   📋 Columns: {name_col} | {email_col}")
            
            # Get workshop date
            workshop_date = workshop_dates.get(sheet_name)
            if not workshop_date and sheet_name.startswith('BC'):
                # Try to match BC## pattern
                workshop_date = workshop_dates.get(sheet_name)
            
            vintage_category = self.determine_vintage_category(workshop_date)
            
            records = []
            
            for idx, row in df.iterrows():
                try:
                    name = str(row[name_col]).strip() if pd.notna(row[name_col]) else None
                    email = str(row[email_col]).strip().lower() if pd.notna(row[email_col]) else None
                    
                    # Skip if missing critical data
                    if not name or not email or email == 'nan':
                        continue
                    
                    # Quality assessments
                    email_quality = self.assess_email_quality(email)
                    name_quality = self.assess_name_quality(name)
                    overall_quality = self.determine_overall_quality(email_quality, name_quality)
                    
                    # Skip invalid emails
                    if email_quality == "invalid":
                        continue
                    
                    # Create JSON metadata for segmentation
                    json_metadata = {
                        "email_quality": email_quality,
                        "name_quality": name_quality,
                        "overall_quality": overall_quality,
                        "response_expectation": "low" if overall_quality == "low" else "standard",
                        "data_vintage": vintage_category,
                        "lead_origin": "india_enrollees",
                        "workshop_sheet": sheet_name,
                        "campaign_segment": f"india_{overall_quality}_{vintage_category}"
                    }
                    
                    # Determine payment status (assume enrollees have paid)
                    payment_status = "Paid" if sheet_name not in ["Assorted without dates"] else "Unknown"
                    
                    # Create record
                    record = {
                        'full_name': name,
                        'email': email,
                        'phone': None,  # No phone data in this file
                        'location': 'India',
                        'workshop_type': f'BC-{sheet_name}' if sheet_name.startswith('BC') else 'India Workshop',
                        'enrolled_date': workshop_date.isoformat() if workshop_date else None,
                        'payment_status': payment_status,
                        'lead_source': 'excel_import',
                        'data_source': 'All Enrollees.xlsx',
                        'lead_source_detail': sheet_name,
                        'json_metadata': json.dumps(json_metadata),
                        'notes': f'India workshop enrollee from {sheet_name}. Quality: {overall_quality}',
                        'created_time': datetime.now().isoformat(),
                        'status': 'Customer' if payment_status == 'Paid' else 'Lead'
                    }
                    
                    records.append(record)
                    
                except Exception as e:
                    print(f"   ❌ Error processing row {idx}: {e}")
                    continue
            
            print(f"   ✅ Processed: {len(records)} valid records")
            return records
            
        except Exception as e:
            print(f"   ❌ Error processing sheet {sheet_name}: {e}")
            return []
    
    def check_duplicates_with_existing(self, records):
        """Check for duplicates against existing database"""
        print(f"\n🔍 Checking for duplicates against existing {self.get_existing_count()} leads...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        new_records = []
        duplicate_count = 0
        
        for record in records:
            cursor.execute("SELECT id, full_name, data_source FROM leads WHERE email = ?", (record['email'],))
            existing = cursor.fetchone()
            
            if existing:
                duplicate_count += 1
                print(f"   ⏭️  Duplicate: {record['full_name']} ({record['email']}) - exists as ID {existing[0]}")
            else:
                new_records.append(record)
        
        conn.close()
        
        print(f"   📊 New records to insert: {len(new_records)}")
        print(f"   📊 Duplicates found: {duplicate_count}")
        
        return new_records
    
    def get_existing_count(self):
        """Get current lead count"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM leads")
        count = cursor.fetchone()[0]
        conn.close()
        return count
    
    def insert_records(self, records):
        """Insert records into database"""
        if not records:
            print("❌ No records to insert")
            return
        
        print(f"\n💾 Inserting {len(records)} India leads to database...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Use the notes field temporarily to store json_metadata since there's no json_data column
        insert_sql = """
            INSERT INTO leads (
                full_name, email, phone, location, workshop_type, enrolled_date,
                payment_status, lead_source, data_source, lead_source_detail,
                internal_notes, notes, created_time, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        inserted_count = 0
        
        for record in records:
            try:
                cursor.execute(insert_sql, (
                    record['full_name'],
                    record['email'], 
                    record['phone'],
                    record['location'],
                    record['workshop_type'],
                    record['enrolled_date'],
                    record['payment_status'],
                    record['lead_source'],
                    record['data_source'],
                    record['lead_source_detail'],
                    record['json_metadata'],  # Store in internal_notes for now
                    record['notes'],
                    record['created_time'],
                    record['status']
                ))
                inserted_count += 1
                print(f"   ✅ Inserted: {record['full_name']} ({record['email']})")
                
            except Exception as e:
                print(f"   ❌ Error inserting {record['full_name']}: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 Successfully inserted {inserted_count} India leads!")
        return inserted_count
    
    def generate_quality_report(self, all_records):
        """Generate quality and segmentation report"""
        print(f"\n📊 INDIA LEADS QUALITY & SEGMENTATION REPORT")
        print("=" * 60)
        
        # Overall stats
        total_records = len(all_records)
        print(f"Total India leads processed: {total_records}")
        
        # Quality distribution
        quality_counts = defaultdict(int)
        vintage_counts = defaultdict(int)
        domain_counts = defaultdict(int)
        
        for record in all_records:
            metadata = json.loads(record['json_metadata'])
            quality_counts[metadata['overall_quality']] += 1
            vintage_counts[metadata['data_vintage']] += 1
            
            email_domain = record['email'].split('@')[1]
            domain_counts[email_domain] += 1
        
        print(f"\n📈 Quality Distribution:")
        for quality, count in sorted(quality_counts.items()):
            percentage = (count / total_records) * 100
            print(f"   {quality.upper()}: {count} leads ({percentage:.1f}%)")
        
        print(f"\n⏰ Vintage Distribution:")
        for vintage, count in sorted(vintage_counts.items()):
            percentage = (count / total_records) * 100
            print(f"   {vintage.upper()}: {count} leads ({percentage:.1f}%)")
        
        print(f"\n📧 Top Email Domains:")
        for domain, count in sorted(domain_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"   {domain}: {count} leads")
        
        print(f"\n🎯 Email Campaign Segmentation Recommendations:")
        print(f"   • High Quality + Recent: Premium engagement campaigns")
        print(f"   • High Quality + Old: Re-engagement campaigns") 
        print(f"   • Medium Quality: Standard nurture sequences")
        print(f"   • Low Quality: Light touch, unsubscribe-friendly campaigns")
        
        return {
            'total_records': total_records,
            'quality_distribution': dict(quality_counts),
            'vintage_distribution': dict(vintage_counts),
            'top_domains': dict(sorted(domain_counts.items(), key=lambda x: x[1], reverse=True)[:10])
        }
    
    def migrate_all_india_leads(self):
        """Main migration function"""
        print("🚀 Starting India Leads Migration...")
        print("=" * 60)
        
        # Step 1: Load workshop dates
        workshop_dates = self.load_workshop_dates()
        
        # Step 2: Process all data sheets
        data_sheets = [
            'BC01', 'BC02', 'BC03', 'BC04', 'BC05', 'BC06', 
            'BC07', 'BC08', 'BC09', 'BC10', 'BC11', 'BC12',
            'Manish', 'Assorted without dates'
        ]
        
        all_records = []
        
        for sheet_name in data_sheets:
            sheet_records = self.process_workshop_sheet(sheet_name, workshop_dates)
            all_records.extend(sheet_records)
        
        print(f"\n📋 Total records from all sheets: {len(all_records)}")
        
        # Step 3: Check for duplicates
        unique_records = self.check_duplicates_with_existing(all_records)
        
        # Step 4: Generate quality report
        quality_report = self.generate_quality_report(all_records)
        
        # Step 5: Insert new records
        inserted_count = self.insert_records(unique_records)
        
        # Step 6: Final summary
        final_count = self.get_existing_count()
        
        print(f"\n🎉 MIGRATION COMPLETE!")
        print("=" * 60)
        print(f"📊 Total leads before: 3,960")
        print(f"📊 Total leads after: {final_count}")
        print(f"📊 India leads added: {inserted_count}")
        print(f"📊 Unique India emails processed: {len(all_records)}")
        print(f"📊 Duplicates skipped: {len(all_records) - len(unique_records)}")
        print(f"\n🇮🇳 India leads ready for email segmentation with quality markers!")
        
        return {
            'total_processed': len(all_records),
            'inserted': inserted_count,
            'duplicates': len(all_records) - len(unique_records),
            'final_count': final_count,
            'quality_report': quality_report
        }

def main():
    """Main function"""
    migrator = IndiaLeadsMigrator()
    result = migrator.migrate_all_india_leads()
    return result

if __name__ == "__main__":
    main()