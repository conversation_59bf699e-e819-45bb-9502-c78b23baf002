#!/usr/bin/env python3
"""
Legacy CSV Migration Tool
========================
Migrates legacy CRM CSV data to leads database with proper quality tagging.

Usage: python legacy_csv_migrator.py [--dry-run]
"""

import pandas as pd
import sqlite3
import re
import argparse
from datetime import datetime
from typing import Dict, List, Set

class LegacyCSVMigrator:
    def __init__(self, csv_path: str, db_path: str, dry_run: bool = False):
        self.csv_path = csv_path
        self.db_path = db_path
        self.dry_run = dry_run
        self.existing_emails = set()
        
        # Quality tags for legacy data - Updated for leads_export.csv
        self.quality_tags = {
            'email_quality': 'medium',
            'response_expectation': 'low', 
            'data_vintage': 'legacy_facebook',
            'lead_origin': 'facebook_export_legacy'
        }
    
    def load_existing_emails(self) -> Set[str]:
        """Load existing emails from database"""
        print("🗄️  Loading existing database emails...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT email FROM leads WHERE email IS NOT NULL AND email != ''")
            existing_emails = cursor.fetchall()
            
            self.existing_emails = {email[0].lower().strip() for email in existing_emails}
            
            print(f"✅ Loaded {len(self.existing_emails):,} existing emails")
            conn.close()
            
            return self.existing_emails
            
        except Exception as e:
            print(f"❌ Error loading existing emails: {e}")
            return set()
    
    def clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and validate CSV data"""
        print("🧹 Cleaning and validating data...")
        
        cleaned = df.copy()
        
        # Clean email addresses
        if 'email' in cleaned.columns:
            cleaned['email'] = cleaned['email'].str.lower().str.strip()
            # Remove rows with invalid emails
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            valid_emails = cleaned['email'].apply(lambda x: bool(re.match(email_pattern, str(x))) if pd.notna(x) else False)
            cleaned = cleaned[valid_emails]
        
        # Clean names
        if 'name' in cleaned.columns:
            cleaned['name'] = cleaned['name'].str.strip()
            # Replace empty strings with None
            cleaned['name'] = cleaned['name'].replace('', None)
        
        # Clean phone numbers - Handle 'whatsapp' column from leads_export.csv
        phone_col = 'whatsapp' if 'whatsapp' in cleaned.columns else 'phone_number'
        if phone_col in cleaned.columns:
            cleaned[phone_col] = cleaned[phone_col].astype(str).str.strip()
            # Replace empty strings with None
            cleaned[phone_col] = cleaned[phone_col].replace('', None)
            cleaned[phone_col] = cleaned[phone_col].replace('nan', None)
        
        # Filter out existing emails
        if 'email' in cleaned.columns:
            new_leads_mask = ~cleaned['email'].isin(self.existing_emails)
            cleaned = cleaned[new_leads_mask]
        
        # Remove duplicates within the CSV
        if 'email' in cleaned.columns:
            initial_count = len(cleaned)
            cleaned = cleaned.drop_duplicates(subset=['email'])
            duplicates_removed = initial_count - len(cleaned)
            if duplicates_removed > 0:
                print(f"   Removed {duplicates_removed} duplicate emails within CSV")
        
        print(f"✅ Data cleaned: {len(cleaned)} records ready for migration")
        return cleaned
    
    def prepare_lead_records(self, df: pd.DataFrame) -> List[Dict]:
        """Prepare lead records for database insertion"""
        print("📝 Preparing lead records...")
        
        lead_records = []
        
        for _, row in df.iterrows():
            # Generate unique lead_id
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            lead_id = f"legacy_{timestamp}_{len(lead_records):04d}"
            
            # Handle whatsapp column from leads_export.csv
            phone_value = row.get('whatsapp') if 'whatsapp' in df.columns else row.get('phone_number')
            
            # Create additional metadata JSON
            additional_metadata = {
                "data_source": "leads_export_legacy", 
                "lead_origin": "facebook_export_legacy",
                "quality_assessment": "medium_legacy",
                "migration_date": "2025-08-07",
                "expected_response": "low",
                "segmentation_tier": "legacy"
            }
            
            record = {
                'lead_id': lead_id,
                'full_name': row.get('name'),
                'email': row.get('email'),
                'phone': phone_value,
                'lead_source': 'facebook_export',  # Set to facebook_export as specified
                'status': 'New',  # Default status
                'priority': 'Low',  # Legacy leads get low priority
                'data_source': 'leads_export_legacy',
                'notes': f"Legacy Facebook export - {self.quality_tags['email_quality']} quality, {self.quality_tags['data_vintage']} vintage. Additional metadata: {str(additional_metadata)}",
                'created_time': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            lead_records.append(record)
        
        print(f"✅ Prepared {len(lead_records)} lead records")
        return lead_records
    
    def insert_leads(self, lead_records: List[Dict]) -> int:
        """Insert lead records into database"""
        if self.dry_run:
            print("🔍 DRY RUN MODE - Would insert the following records:")
            for i, record in enumerate(lead_records[:5]):  # Show first 5 as sample
                print(f"   Record {i+1}: {record['full_name']} - {record['email']}")
            if len(lead_records) > 5:
                print(f"   ... and {len(lead_records) - 5} more records")
            return len(lead_records)
        
        print("💾 Inserting leads into database...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get the leads table schema to ensure we match expected columns
            cursor.execute("PRAGMA table_info(leads)")
            columns_info = cursor.fetchall()
            db_columns = {col[1] for col in columns_info}
            
            # Prepare insertion SQL
            insert_sql = """
                INSERT INTO leads (
                    lead_id, full_name, email, phone, lead_source, status, 
                    priority, data_source, notes, created_time, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            # Prepare data for insertion
            insert_data = []
            for record in lead_records:
                insert_data.append((
                    record['lead_id'],
                    record['full_name'],
                    record['email'],
                    record['phone'],
                    record['lead_source'],
                    record['status'],
                    record['priority'],
                    record['data_source'],
                    record['notes'],
                    record['created_time'],
                    record['updated_at']
                ))
            
            # Execute batch insert
            cursor.executemany(insert_sql, insert_data)
            
            # Commit changes
            conn.commit()
            inserted_count = cursor.rowcount
            
            print(f"✅ Successfully inserted {inserted_count} new leads")
            
            conn.close()
            return inserted_count
            
        except Exception as e:
            print(f"❌ Error inserting leads: {e}")
            return 0
    
    def generate_migration_summary(self, original_count: int, cleaned_count: int, inserted_count: int):
        """Generate migration summary report"""
        summary_lines = [
            "\n" + "=" * 60,
            "LEGACY CSV MIGRATION SUMMARY",
            "=" * 60,
            f"Migration Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Source File: {self.csv_path}",
            f"Target Database: {self.db_path}",
            f"Mode: {'DRY RUN' if self.dry_run else 'LIVE MIGRATION'}",
            "",
            "RESULTS:",
            f"  • Original CSV records: {original_count:,}",
            f"  • Records after cleaning: {cleaned_count:,}",
            f"  • Records inserted: {inserted_count:,}",
            f"  • Success rate: {(inserted_count / original_count * 100):.1f}%",
            "",
            "QUALITY TAGS APPLIED:",
            f"  • Email Quality: {self.quality_tags['email_quality']}",
            f"  • Response Expectation: {self.quality_tags['response_expectation']}",
            f"  • Data Vintage: {self.quality_tags['data_vintage']}",
            f"  • Lead Origin: {self.quality_tags['lead_origin']}",
            "",
            "NEXT STEPS:",
            "  • Update email campaign segments to include quality tags",
            "  • Set up low-priority outreach campaigns",
            "  • Monitor engagement rates for legacy leads",
            "  • Consider additional data enrichment",
            "",
            "=" * 60
        ]
        
        summary = "\n".join(summary_lines)
        print(summary)
        
        # Save summary to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = f"/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/data_migration/legacy_migration_summary_{timestamp}.txt"
        
        with open(summary_file, 'w') as f:
            f.write(summary)
        
        print(f"📄 Migration summary saved to: {summary_file}")
    
    def migrate(self):
        """Execute the complete migration process"""
        print("🚀 Starting legacy CSV migration...")
        print(f"   Source: {self.csv_path}")
        print(f"   Target: {self.db_path}")
        print(f"   Mode: {'DRY RUN' if self.dry_run else 'LIVE MIGRATION'}")
        print()
        
        # Load existing emails
        self.load_existing_emails()
        
        # Load and clean CSV data
        print("📊 Loading CSV data...")
        csv_data = pd.read_csv(self.csv_path)
        original_count = len(csv_data)
        print(f"   Loaded {original_count:,} records from CSV")
        
        # Clean and validate data
        cleaned_data = self.clean_and_validate_data(csv_data)
        cleaned_count = len(cleaned_data)
        
        if cleaned_count == 0:
            print("⚠️  No new leads to migrate after cleaning")
            return
        
        # Prepare lead records
        lead_records = self.prepare_lead_records(cleaned_data)
        
        # Insert leads (or simulate in dry run mode)
        inserted_count = self.insert_leads(lead_records)
        
        # Generate summary
        self.generate_migration_summary(original_count, cleaned_count, inserted_count)
        
        print("\n✨ Migration process completed!")

def main():
    parser = argparse.ArgumentParser(description='Migrate legacy CRM CSV to leads database')
    parser.add_argument('--dry-run', action='store_true', help='Run in dry-run mode (no actual database changes)')
    
    args = parser.parse_args()
    
    csv_path = "/Users/<USER>/Code/modernaipro/mai-administrative/legacy/old_crm/leads_export.csv"
    db_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    
    migrator = LegacyCSVMigrator(csv_path, db_path, dry_run=args.dry_run)
    migrator.migrate()

if __name__ == "__main__":
    main()