#!/usr/bin/env python3
"""
New Leads Migration Script for US Lead CRM.xlsx - "New Leads" Sheet
==================================================================

This script migrates leads from the "New Leads" sheet with rich data mapping
to the existing database schema fields. Additional data is stored in notes field.

Modern AI Pro Lead Management System
Author: <PERSON> (Anthropic)
Date: 2025-08-07
"""

import pandas as pd
import sqlite3
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import sys
import os

class NewLeadsMigrator:
    def __init__(self):
        self.db_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db'
        self.excel_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/leads_data/US Lead CRM.xlsx'
        self.sheet_name = 'New Leads'
        
        # Migration statistics
        self.stats = {
            'total_rows': 0,
            'processed': 0,
            'new_leads': 0,
            'duplicates': 0,
            'errors': 0,
            'customers_identified': 0,
            'rich_data_entries': 0
        }
        
        # Data source identifier
        self.data_source = 'us_lead_crm_new_leads'
        
    def clean_email(self, email: str) -> Optional[str]:
        """Clean and validate email addresses"""
        if pd.isna(email) or not email:
            return None
        
        email = str(email).strip().lower()
        
        # Basic email validation
        if '@' not in email or '.' not in email.split('@')[1]:
            return None
            
        return email
    
    def clean_phone(self, phone: str) -> Optional[str]:
        """Clean and standardize phone numbers"""
        if pd.isna(phone) or not phone:
            return None
        
        phone = str(phone).strip()
        
        # Remove all non-digit characters except +
        phone = re.sub(r'[^\d+]', '', phone)
        
        # Handle different formats
        if phone.startswith('+1') and len(phone) == 12:
            return phone
        elif phone.startswith('1') and len(phone) == 11:
            return '+' + phone
        elif len(phone) == 10:
            return '+1' + phone
        elif phone.startswith('+') and len(phone) > 10:
            return phone
        
        return phone if phone else None
    
    def parse_date(self, date_val) -> Optional[datetime]:
        """Parse date values with multiple format support"""
        if pd.isna(date_val):
            return None
            
        if isinstance(date_val, datetime):
            return date_val
            
        if isinstance(date_val, str):
            # Try different date formats
            formats = ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y', '%Y-%m-%d %H:%M:%S']
            for fmt in formats:
                try:
                    return datetime.strptime(date_val, fmt)
                except ValueError:
                    continue
        
        return None
    
    def map_experience_level(self, python_exp: str) -> Optional[str]:
        """Map Python experience to programming_experience field"""
        if pd.isna(python_exp):
            return None
            
        exp = str(python_exp).lower().strip()
        
        if 'no experience' in exp or 'none' in exp:
            return 'Beginner'
        elif 'beginner' in exp:
            return 'Beginner'
        elif 'intermediate' in exp:
            return 'Intermediate'
        elif 'advanced' in exp or 'expert' in exp:
            return 'Advanced'
        else:
            return 'Beginner'  # Default
    
    def extract_rich_data(self, row: pd.Series) -> Dict:
        """Extract additional data for rich storage"""
        rich_data = {}
        
        # Add non-standard fields to rich data
        if not pd.isna(row.get('Contact Code')):
            rich_data['contact_code'] = str(row['Contact Code'])
            
        if not pd.isna(row.get('Channel')):
            rich_data['channel'] = str(row['Channel'])
            
        if not pd.isna(row.get('Status')):
            rich_data['crm_status'] = str(row['Status'])
            
        if not pd.isna(row.get('Purchase')):
            rich_data['purchase_info'] = str(row['Purchase'])
            
        if not pd.isna(row.get('How to contact?')):
            rich_data['preferred_contact'] = str(row['How to contact?'])
            
        if not pd.isna(row.get('Reach Out Count')):
            rich_data['reach_out_count'] = float(row['Reach Out Count'])
            
        return rich_data
    
    def determine_status_and_payment(self, row: pd.Series) -> Tuple[str, str, Optional[datetime], Optional[float]]:
        """Determine lead status and payment information"""
        status = 'New'
        payment_status = 'Unpaid'
        payment_date = None
        payment_amount = None
        
        # Check Purchase column for payment indicators
        purchase = row.get('Purchase')
        if not pd.isna(purchase):
            purchase_str = str(purchase).lower()
            
            # Look for date patterns (indicates purchase)
            if re.search(r'\d{1,2}/\d{1,2}/\d{4}', str(purchase)):
                payment_status = 'Paid'
                payment_date = self.parse_date(purchase)
                status = 'Customer'
                # Default amount for identified customers
                payment_amount = 297.0  # Typical workshop price
            elif 'intro call' in purchase_str:
                status = 'Qualified'
            elif 'bought' in purchase_str or 'paid' in purchase_str:
                payment_status = 'Paid'
                status = 'Customer'
                payment_amount = 297.0
        
        # Check Status column
        crm_status = row.get('Status')
        if not pd.isna(crm_status):
            status_str = str(crm_status).lower()
            if 'responded' in status_str:
                status = 'Contacted'
            elif 'not responded' in status_str:
                status = 'New'
                
        return status, payment_status, payment_date, payment_amount
    
    def check_duplicate(self, conn: sqlite3.Connection, email: str) -> bool:
        """Check if lead already exists based on email"""
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM leads WHERE email = ?', (email,))
        return cursor.fetchone() is not None
    
    def process_leads_data(self) -> pd.DataFrame:
        """Load and process the Excel data"""
        print(f"📊 Loading data from {self.sheet_name}...")
        
        try:
            df = pd.read_excel(self.excel_path, sheet_name=self.sheet_name)
            self.stats['total_rows'] = len(df)
            
            print(f"✅ Loaded {len(df)} rows from {self.sheet_name}")
            print(f"📋 Columns: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            print(f"❌ Error loading Excel file: {e}")
            sys.exit(1)
    
    def migrate_leads(self):
        """Main migration function"""
        print("🚀 Starting New Leads Migration")
        print("=" * 60)
        
        # Load data
        df = self.process_leads_data()
        
        # Connect to database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        print(f"📊 Processing {len(df)} leads...")
        
        for index, row in df.iterrows():
            try:
                # Clean and validate email (required field)
                email = self.clean_email(row.get('Email'))
                if not email:
                    print(f"⚠️  Row {index + 1}: Skipping - no valid email")
                    continue
                
                # Check for duplicate
                if self.check_duplicate(conn, email):
                    self.stats['duplicates'] += 1
                    print(f"🔄 Row {index + 1}: Duplicate email {email}")
                    continue
                
                # Extract basic fields
                full_name = str(row.get('Name', '')).strip() if not pd.isna(row.get('Name')) else ''
                phone = self.clean_phone(row.get('Phone'))
                
                # Map additional fields
                current_title = str(row.get('Profession', '')).strip() if not pd.isna(row.get('Profession')) else None
                programming_experience = self.map_experience_level(row.get('Python Exp'))
                
                # Parse dates
                created_date = self.parse_date(row.get('Date'))
                last_contact = self.parse_date(row.get('Last Contact Date'))
                
                # Determine status and payment info
                status, payment_status, payment_date, payment_amount = self.determine_status_and_payment(row)
                
                # Extract rich data
                rich_data = self.extract_rich_data(row)
                
                # Create notes field with rich data
                notes_data = []
                if rich_data:
                    for key, value in rich_data.items():
                        notes_data.append(f"{key}: {value}")
                    self.stats['rich_data_entries'] += 1
                
                notes = "; ".join(notes_data) if notes_data else None
                
                # Determine lead source
                lead_source = 'facebook' if row.get('Channel') == 'Facebook' else 'other'
                
                # Insert lead record
                insert_sql = """
                INSERT INTO leads (
                    full_name, email, phone, current_title, programming_experience,
                    status, payment_status, payment_date, payment_amount,
                    last_contact_date, lead_source, data_source, notes,
                    created_time, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                values = (
                    full_name, email, phone, current_title, programming_experience,
                    status, payment_status, payment_date, payment_amount,
                    last_contact or created_date, lead_source, self.data_source, notes,
                    created_date or datetime.now(), datetime.now()
                )
                
                cursor.execute(insert_sql, values)
                
                self.stats['processed'] += 1
                self.stats['new_leads'] += 1
                
                if status == 'Customer':
                    self.stats['customers_identified'] += 1
                
                if (index + 1) % 100 == 0:
                    print(f"📊 Processed {index + 1} rows...")
                
            except Exception as e:
                self.stats['errors'] += 1
                print(f"❌ Error processing row {index + 1}: {e}")
                continue
        
        # Commit changes
        conn.commit()
        conn.close()
        
        self.print_migration_report()
    
    def print_migration_report(self):
        """Print comprehensive migration report"""
        print("\n🎯 MIGRATION REPORT")
        print("=" * 60)
        print(f"📊 Total rows in Excel:          {self.stats['total_rows']:,}")
        print(f"✅ Successfully processed:       {self.stats['processed']:,}")
        print(f"🆕 New leads created:            {self.stats['new_leads']:,}")
        print(f"🔄 Duplicates skipped:           {self.stats['duplicates']:,}")
        print(f"❌ Errors encountered:           {self.stats['errors']:,}")
        print(f"💰 Customers identified:         {self.stats['customers_identified']:,}")
        print(f"📝 Rich data entries:            {self.stats['rich_data_entries']:,}")
        
        # Calculate success rate
        success_rate = (self.stats['processed'] / self.stats['total_rows']) * 100 if self.stats['total_rows'] > 0 else 0
        print(f"📈 Success rate:                 {success_rate:.1f}%")
        
        print(f"\n📁 Data source:                  {self.data_source}")
        print(f"🗄️  Database:                    leads.db")
        print(f"📋 Sheet processed:              {self.sheet_name}")
        
        # Database verification
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM leads')
        total_leads = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM leads WHERE data_source = ?', (self.data_source,))
        new_source_leads = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"\n🗄️  DATABASE STATUS:")
        print(f"📊 Total leads in database:      {total_leads:,}")
        print(f"🆕 From this migration:          {new_source_leads:,}")
        
        print("\n✅ Migration completed successfully!")

def main():
    """Main execution function"""
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        print("New Leads Migration Script")
        print("Usage: python new_leads_migration.py")
        print("Migrates leads from 'New Leads' sheet in US Lead CRM.xlsx")
        return
    
    migrator = NewLeadsMigrator()
    migrator.migrate_leads()

if __name__ == "__main__":
    main()