#!/usr/bin/env python3
"""
US Leads Migration Script - Lead Sheet to Database V2
Migrates leads from 'Lead Sheet' in US Leads.xlsx to the V2 customer-centric database schema
Filters for creation dates from September 2024 to March 2025
"""

import pandas as pd
import sqlite3
import sys
import re
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class USLeadsMigrator:
    def __init__(self):
        self.assets_dir = project_root / "assets" / "leads_data"
        self.db_path = project_root / "dashboard" / "database" / "leads.db"
        self.file_path = self.assets_dir / "US Leads.xlsx"
        self.sheet_name = "Lead Sheet"
        
        # Date filtering parameters
        self.start_date = "2024-09-01"
        self.end_date = "2025-03-31"
        
        print("=" * 60)
        print("🇺🇸 US LEADS MIGRATION TOOL - V2 SCHEMA")
        print("=" * 60)
        print(f"📁 Source File: {self.file_path}")
        print(f"📋 Sheet: {self.sheet_name}")
        print(f"🗄️  Database: {self.db_path}")
        print(f"📅 Date Range: {self.start_date} to {self.end_date}")
        
    def validate_environment(self):
        """Validate that required files and database exist"""
        print(f"\n🔍 Validating environment...")
        
        if not self.file_path.exists():
            print(f"❌ Excel file not found: {self.file_path}")
            return False
            
        if not self.db_path.exists():
            print(f"❌ Database not found: {self.db_path}")
            return False
            
        print(f"✅ Excel file found")
        print(f"✅ Database found")
        return True
    
    def analyze_lead_sheet(self):
        """Analyze the Lead Sheet structure and data quality"""
        print(f"\n🔍 Analyzing {self.sheet_name}...")
        
        try:
            df = pd.read_excel(self.file_path, sheet_name=self.sheet_name)
            
            # Convert date column and filter
            df['ar'] = pd.to_datetime(df['ar'], errors='coerce')
            
            # Filter by date range and valid data
            filtered_df = df[
                (df['ar'] >= self.start_date) & 
                (df['ar'] <= self.end_date) & 
                (df['Name'].notna()) &
                (df['Email'].notna())
            ]
            
            print(f"📊 Total records in sheet: {len(df)}")
            print(f"📅 Records with valid dates: {df['ar'].notna().sum()}")
            print(f"🎯 Records in target date range: {len(filtered_df)}")
            print(f"📧 Records with name and email: {(df['Name'].notna() & df['Email'].notna()).sum()}")
            
            if len(filtered_df) > 0:
                print(f"\n📅 Target date range data:")
                print(f"   Earliest: {filtered_df['ar'].min()}")
                print(f"   Latest: {filtered_df['ar'].max()}")
                
                # Check data quality
                print(f"\n📋 Data Quality Analysis:")
                print(f"   • Complete records (name, email, phone): {(filtered_df['Name'].notna() & filtered_df['Email'].notna() & filtered_df['Phone'].notna()).sum()}")
                print(f"   • Python experience levels: {filtered_df['Python Exp'].value_counts().to_dict()}")
                print(f"   • Valid phone numbers: {filtered_df['Phone'].notna().sum()}")
                
                return filtered_df
            else:
                print("❌ No records found in the specified date range")
                return None
                
        except Exception as e:
            print(f"❌ Error analyzing sheet: {e}")
            return None
    
    def clean_data(self, df):
        """Clean and standardize the lead data"""
        print(f"\n🧹 Cleaning data...")
        
        cleaned_records = []
        
        for idx, row in df.iterrows():
            try:
                # Clean name
                name = str(row['Name']).strip() if pd.notna(row['Name']) else None
                if not name or name.lower() in ['nan', 'none', '']:
                    continue
                
                # Clean email
                email = str(row['Email']).strip().lower() if pd.notna(row['Email']) else None
                if not email or email.lower() in ['nan', 'none', '']:
                    continue
                
                # Validate email format
                if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                    print(f"   ⚠️  Invalid email format: {email} for {name}")
                    continue
                
                # Clean phone
                phone = str(row['Phone']).strip() if pd.notna(row['Phone']) else None
                if phone and phone.lower() in ['nan', 'none']:
                    phone = None
                
                # Clean phone number (remove non-digits but keep +)
                if phone:
                    # Remove everything except digits and +
                    phone_cleaned = re.sub(r'[^\d+]', '', phone)
                    # If it starts with +1, keep it, otherwise just digits
                    if phone_cleaned.startswith('+1'):
                        phone = phone_cleaned[:12]  # +1 and 10 digits
                    else:
                        phone = re.sub(r'[^\d]', '', phone_cleaned)
                        # Ensure 10 digits for US numbers
                        if len(phone) == 10:
                            phone = phone
                        elif len(phone) == 11 and phone.startswith('1'):
                            phone = phone[1:]  # Remove leading 1
                        else:
                            phone = None  # Invalid length
                
                # Parse Python experience
                python_exp = str(row['Python Exp']).strip().lower() if pd.notna(row['Python Exp']) else 'unknown'
                if python_exp in ['nan', 'none', '']:
                    python_exp = 'unknown'
                
                # Map Python experience to AI experience level
                ai_experience_mapping = {
                    'no_experience': 'Beginner',
                    'no experience': 'Beginner',
                    'beginner': 'Beginner',
                    'intermediate': 'Intermediate',
                    'intermediary': 'Intermediate',
                    'advanced': 'Advanced',
                    'expert': 'Advanced'
                }
                ai_experience = ai_experience_mapping.get(python_exp, 'Beginner')
                
                # Get creation date
                created_time = row['ar'].isoformat() if pd.notna(row['ar']) else datetime.now().isoformat()
                
                # Extract designation and notes
                designation = str(row['Designation']).strip() if pd.notna(row['Designation']) and str(row['Designation']).lower() not in ['nan', 'none'] else None
                contact_pref = str(row['How to contact?']).strip() if pd.notna(row['How to contact?']) and str(row['How to contact?']).lower() not in ['nan', 'none'] else None
                notes = str(row['Notes']).strip() if pd.notna(row['Notes']) and str(row['Notes']).lower() not in ['nan', 'none'] else None
                
                # Build notes field
                notes_parts = []
                if designation:
                    notes_parts.append(f"Designation: {designation}")
                if contact_pref:
                    notes_parts.append(f"Contact preference: {contact_pref}")
                if notes:
                    notes_parts.append(f"Notes: {notes}")
                
                final_notes = " | ".join(notes_parts) if notes_parts else "Migrated from US Leads.xlsx - Lead Sheet"
                
                record = {
                    'full_name': name,
                    'email': email,
                    'phone': phone,
                    'current_title': designation,
                    'location': 'US',
                    'workshop_type': 'TBD',  # To be determined based on enrollment
                    'lead_source': 'excel_import',
                    'data_source': 'US Leads.xlsx - Lead Sheet',
                    'status': 'New',
                    'priority': 'Medium',
                    'ai_experience_level': ai_experience,
                    'programming_experience': python_exp,
                    'payment_status': 'Unknown',
                    'created_time': created_time,
                    'notes': final_notes,
                    'whatsapp_available': contact_pref and 'whatsapp' in contact_pref.lower()
                }
                
                cleaned_records.append(record)
                
            except Exception as e:
                print(f"   ❌ Error processing row {idx}: {e}")
                continue
        
        print(f"📋 Successfully cleaned {len(cleaned_records)} records")
        return cleaned_records
    
    def check_existing_leads(self, records):
        """Check which leads already exist in the database"""
        print(f"\n🔍 Checking for existing leads...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        new_records = []
        duplicate_count = 0
        
        for record in records:
            cursor.execute("SELECT id, full_name, created_time FROM leads WHERE email = ?", (record['email'],))
            existing = cursor.fetchone()
            
            if existing:
                duplicate_count += 1
                print(f"   ⏭️  Duplicate found: {record['full_name']} ({record['email']}) - existing ID: {existing[0]}")
            else:
                new_records.append(record)
        
        conn.close()
        
        print(f"📊 Duplicate analysis:")
        print(f"   • New records to insert: {len(new_records)}")
        print(f"   • Duplicates skipped: {duplicate_count}")
        
        return new_records
    
    def insert_leads(self, records):
        """Insert new leads into the database"""
        print(f"\n💾 Inserting {len(records)} new leads...")
        
        if not records:
            print("❌ No records to insert")
            return {'inserted': 0, 'errors': 0}
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        error_count = 0
        
        # Prepare insert statement for leads table
        insert_sql = """
            INSERT INTO leads (
                full_name, email, phone, current_title, location, workshop_type,
                lead_source, data_source, status, priority, ai_experience_level,
                programming_experience, payment_status, created_time, notes, whatsapp_available,
                updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        for record in records:
            try:
                cursor.execute(insert_sql, (
                    record['full_name'],
                    record['email'], 
                    record['phone'],
                    record['current_title'],
                    record['location'],
                    record['workshop_type'],
                    record['lead_source'],
                    record['data_source'],
                    record['status'],
                    record['priority'],
                    record['ai_experience_level'],
                    record['programming_experience'],
                    record['payment_status'],
                    record['created_time'],
                    record['notes'],
                    record['whatsapp_available'],
                    datetime.now().isoformat()
                ))
                
                inserted_count += 1
                
                if inserted_count <= 5:  # Show first 5 insertions
                    print(f"   ✅ Inserted: {record['full_name']} ({record['email']})")
                elif inserted_count % 100 == 0:  # Progress updates every 100
                    print(f"   📊 Progress: {inserted_count} records inserted...")
                
            except Exception as e:
                error_count += 1
                print(f"   ❌ Error inserting {record['full_name']}: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 Migration complete!")
        print(f"   ✅ Successfully inserted: {inserted_count} leads")
        print(f"   ❌ Errors: {error_count} records")
        
        return {'inserted': inserted_count, 'errors': error_count}
    
    def verify_migration(self, expected_count):
        """Verify the migration was successful"""
        print(f"\n🔍 Verifying migration...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Count total leads from US Leads.xlsx source
        cursor.execute("""
            SELECT COUNT(*) FROM leads 
            WHERE data_source = 'US Leads.xlsx - Lead Sheet'
        """)
        total_migrated = cursor.fetchone()[0]
        
        # Count leads created today (migration date)
        today = datetime.now().date().isoformat()
        cursor.execute("""
            SELECT COUNT(*) FROM leads 
            WHERE data_source = 'US Leads.xlsx - Lead Sheet' 
            AND date(updated_at) = ?
        """, (today,))
        today_migrated = cursor.fetchone()[0]
        
        # Get sample of migrated records
        cursor.execute("""
            SELECT full_name, email, location, ai_experience_level, created_time
            FROM leads 
            WHERE data_source = 'US Leads.xlsx - Lead Sheet'
            ORDER BY updated_at DESC
            LIMIT 5
        """)
        sample_records = cursor.fetchall()
        
        conn.close()
        
        print(f"📊 Migration Verification:")
        print(f"   • Total US Leads records in DB: {total_migrated}")
        print(f"   • Records migrated today: {today_migrated}")
        print(f"   • Expected from this run: {expected_count}")
        
        if sample_records:
            print(f"\n📝 Sample migrated records:")
            for record in sample_records:
                print(f"   • {record[0]} ({record[1]}) - {record[2]} - {record[3]} - {record[4][:10]}")
        
        return total_migrated
    
    def run_migration(self):
        """Run the complete migration process"""
        print(f"\n🚀 Starting US Leads migration process...")
        
        # Step 1: Validate environment
        if not self.validate_environment():
            return False
        
        # Step 2: Analyze data
        df = self.analyze_lead_sheet()
        if df is None or len(df) == 0:
            print("❌ No valid data found to migrate")
            return False
        
        # Step 3: Clean data
        cleaned_records = self.clean_data(df)
        if not cleaned_records:
            print("❌ No valid records after cleaning")
            return False
        
        # Step 4: Check for existing leads
        new_records = self.check_existing_leads(cleaned_records)
        
        # Step 5: Insert new leads
        if new_records:
            result = self.insert_leads(new_records)
            
            # Step 6: Verify migration
            self.verify_migration(result['inserted'])
            
            print(f"\n🎯 MIGRATION SUMMARY:")
            print(f"   📊 Total records in date range: {len(df)}")
            print(f"   🧹 Records after cleaning: {len(cleaned_records)}")
            print(f"   📧 New leads inserted: {result['inserted']}")
            print(f"   ⏭️  Duplicates skipped: {len(cleaned_records) - len(new_records)}")
            print(f"   ❌ Errors: {result['errors']}")
            
            return True
        else:
            print("ℹ️  All leads already exist in database - no new records to insert")
            return True

def main():
    """Main function"""
    migrator = USLeadsMigrator()
    success = migrator.run_migration()
    
    if success:
        print(f"\n✅ Migration completed successfully!")
        print(f"💡 Next steps:")
        print(f"   1. Review the migrated data in the dashboard")
        print(f"   2. Consider running workshop enrollment connections if needed")
        print(f"   3. Update lead statuses based on business rules")
    else:
        print(f"\n❌ Migration failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()