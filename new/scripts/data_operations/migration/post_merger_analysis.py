#!/usr/bin/env python3
"""
Post-Merger Analysis & Validation Report
========================================

Comprehensive analysis and validation of the duplicate email merger results.
Generates detailed reports on data consolidation, customer metrics, and database integrity.
"""

import sqlite3
import json
from datetime import datetime
from pathlib import Path
import pandas as pd

def analyze_merger_results(database_path: str):
    """Generate comprehensive post-merger analysis and validation report"""
    
    if not Path(database_path).exists():
        print(f"❌ Database not found: {database_path}")
        return
    
    print("🔍 POST-MERGER ANALYSIS & VALIDATION REPORT")
    print("=" * 60)
    print(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    conn = sqlite3.connect(database_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # ==============================================
        # 1. DATABASE OVERVIEW
        # ==============================================
        print("📊 DATABASE OVERVIEW")
        print("-" * 30)
        
        # Total records
        cursor.execute("SELECT COUNT(*) as total FROM leads")
        total_records = cursor.fetchone()['total']
        print(f"Total Records: {total_records:,}")
        
        # Records with email
        cursor.execute("SELECT COUNT(*) as count FROM leads WHERE email IS NOT NULL AND email != ''")
        records_with_email = cursor.fetchone()['count']
        print(f"Records with Email: {records_with_email:,}")
        
        # Remaining duplicates (should be 0)
        cursor.execute("""
            SELECT COUNT(*) as duplicate_emails
            FROM (
                SELECT email
                FROM leads 
                WHERE email IS NOT NULL AND email != ''
                GROUP BY LOWER(email)
                HAVING COUNT(*) > 1
            )
        """)
        remaining_duplicates = cursor.fetchone()['duplicate_emails']
        print(f"Remaining Duplicate Emails: {remaining_duplicates:,}")
        
        if remaining_duplicates == 0:
            print("✅ SUCCESS: All duplicate emails have been merged!")
        else:
            print("⚠️  WARNING: Some duplicate emails still exist!")
        
        print()
        
        # ==============================================
        # 2. CUSTOMER SEGMENTATION ANALYSIS
        # ==============================================
        print("👥 CUSTOMER SEGMENTATION ANALYSIS")
        print("-" * 40)
        
        # Customer types
        cursor.execute("""
            SELECT 
                customer_type,
                COUNT(*) as count,
                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM leads), 2) as percentage
            FROM leads 
            WHERE customer_type IS NOT NULL
            GROUP BY customer_type
            ORDER BY count DESC
        """)
        
        customer_types = cursor.fetchall()
        for row in customer_types:
            print(f"{row['customer_type'] or 'Unknown'}: {row['count']:,} ({row['percentage']}%)")
        
        print()
        
        # ==============================================
        # 3. PAYMENT HISTORY ANALYSIS
        # ==============================================
        print("💳 PAYMENT HISTORY ANALYSIS")
        print("-" * 35)
        
        # Records with payment history
        cursor.execute("""
            SELECT COUNT(*) as count 
            FROM leads 
            WHERE payment_history IS NOT NULL AND payment_history != '' AND payment_history != 'null'
        """)
        records_with_payment_history = cursor.fetchone()['count']
        print(f"Records with Payment History: {records_with_payment_history:,}")
        
        # Customer Lifetime Value analysis
        cursor.execute("""
            SELECT 
                COUNT(*) as customers_with_clv,
                SUM(customer_lifetime_value) as total_clv,
                AVG(customer_lifetime_value) as avg_clv,
                MAX(customer_lifetime_value) as max_clv,
                MIN(CASE WHEN customer_lifetime_value > 0 THEN customer_lifetime_value END) as min_clv
            FROM leads 
            WHERE customer_lifetime_value > 0
        """)
        
        clv_stats = cursor.fetchone()
        print(f"Customers with CLV > 0: {clv_stats['customers_with_clv']:,}")
        print(f"Total Customer Lifetime Value: ${clv_stats['total_clv']:,.2f}")
        print(f"Average CLV: ${clv_stats['avg_clv']:,.2f}")
        print(f"Highest CLV: ${clv_stats['max_clv']:,.2f}")
        print(f"Lowest CLV (>0): ${clv_stats['min_clv']:,.2f}")
        
        print()
        
        # ==============================================
        # 4. VIP & REPEAT CUSTOMER ANALYSIS
        # ==============================================
        print("⭐ VIP & REPEAT CUSTOMER ANALYSIS")
        print("-" * 40)
        
        # VIP customers
        cursor.execute("SELECT COUNT(*) as count FROM leads WHERE is_vip_customer = 1")
        vip_count = cursor.fetchone()['count']
        print(f"VIP Customers (CLV ≥ $1,000): {vip_count:,}")
        
        # Repeat customers
        cursor.execute("SELECT COUNT(*) as count FROM leads WHERE customer_type = 'repeat_customer'")
        repeat_count = cursor.fetchone()['count']
        print(f"Repeat Customers: {repeat_count:,}")
        
        # Multiple payments
        cursor.execute("SELECT COUNT(*) as count FROM leads WHERE total_payments > 1")
        multi_payment_count = cursor.fetchone()['count']
        print(f"Customers with Multiple Payments: {multi_payment_count:,}")
        
        print()
        
        # ==============================================
        # 5. HIGH-VALUE CUSTOMER SPOTLIGHT
        # ==============================================
        print("💎 HIGH-VALUE CUSTOMER SPOTLIGHT")
        print("-" * 40)
        
        # Top 10 customers by CLV
        cursor.execute("""
            SELECT 
                full_name,
                email,
                customer_lifetime_value as clv,
                total_payments,
                customer_type,
                is_vip_customer
            FROM leads 
            WHERE customer_lifetime_value > 0
            ORDER BY customer_lifetime_value DESC
            LIMIT 10
        """)
        
        top_customers = cursor.fetchall()
        print("Top 10 Customers by Lifetime Value:")
        for i, customer in enumerate(top_customers, 1):
            vip_badge = "⭐" if customer['is_vip_customer'] else ""
            print(f"  {i:2}. {customer['full_name']} ({customer['email']}) - ${customer['clv']:,.2f} ({customer['total_payments']} payments) {vip_badge}")
        
        print()
        
        # ==============================================
        # 6. PAYMENT HISTORY SAMPLE ANALYSIS
        # ==============================================
        print("🧾 PAYMENT HISTORY SAMPLE ANALYSIS")
        print("-" * 45)
        
        # Get a sample of payment histories
        cursor.execute("""
            SELECT 
                full_name,
                email,
                payment_history,
                customer_lifetime_value,
                total_payments
            FROM leads 
            WHERE payment_history IS NOT NULL 
            AND payment_history != '' 
            AND payment_history != 'null'
            AND customer_lifetime_value > 500
            ORDER BY customer_lifetime_value DESC
            LIMIT 5
        """)
        
        payment_samples = cursor.fetchall()
        print("Sample Payment Histories (High-Value Customers):")
        
        for i, customer in enumerate(payment_samples, 1):
            print(f"\n{i}. {customer['full_name']} ({customer['email']})")
            print(f"   CLV: ${customer['customer_lifetime_value']:,.2f} | Payments: {customer['total_payments']}")
            
            try:
                payment_history = json.loads(customer['payment_history'])
                if 'payment_history' in payment_history:
                    payments = payment_history['payment_history']
                    print(f"   Payment Details:")
                    for j, payment in enumerate(payments[:3], 1):  # Show first 3 payments
                        date = payment.get('date', 'Unknown')[:10] if payment.get('date') else 'Unknown'
                        amount = payment.get('amount', 0)
                        course = payment.get('course', 'Unknown')
                        print(f"     {j}. ${amount:,.2f} - {course} ({date})")
                    if len(payments) > 3:
                        print(f"     ... and {len(payments) - 3} more payments")
            except (json.JSONDecodeError, KeyError):
                print("   Payment history format error")
        
        print()
        
        # ==============================================
        # 7. DATA QUALITY METRICS
        # ==============================================
        print("🔍 DATA QUALITY METRICS")
        print("-" * 30)
        
        # Completeness metrics
        fields_to_check = [
            ('full_name', 'Full Name'),
            ('email', 'Email'),
            ('phone', 'Phone'),
            ('workshop_type', 'Workshop Type'),
            ('payment_amount', 'Payment Amount'),
            ('stripe_payment_id', 'Stripe Payment ID'),
            ('data_source', 'Data Source')
        ]
        
        for field, label in fields_to_check:
            cursor.execute(f"""
                SELECT 
                    COUNT(*) as total,
                    COUNT({field}) as non_null,
                    ROUND(COUNT({field}) * 100.0 / COUNT(*), 1) as completeness
                FROM leads
            """)
            stats = cursor.fetchone()
            print(f"{label}: {stats['non_null']:,}/{stats['total']:,} ({stats['completeness']}% complete)")
        
        print()
        
        # ==============================================
        # 8. STRIPE INTEGRATION VALIDATION
        # ==============================================
        print("💰 STRIPE INTEGRATION VALIDATION")
        print("-" * 40)
        
        # Stripe payment records
        cursor.execute("""
            SELECT COUNT(*) as count 
            FROM leads 
            WHERE stripe_payment_id IS NOT NULL AND stripe_payment_id != ''
        """)
        stripe_records = cursor.fetchone()['count']
        print(f"Records with Stripe Payment ID: {stripe_records:,}")
        
        # Payment amount distribution
        cursor.execute("""
            SELECT 
                COUNT(*) as count,
                SUM(payment_amount) as total_amount,
                AVG(payment_amount) as avg_amount,
                MIN(payment_amount) as min_amount,
                MAX(payment_amount) as max_amount
            FROM leads 
            WHERE payment_amount > 0
        """)
        
        payment_stats = cursor.fetchone()
        print(f"Total Payment Records: {payment_stats['count']:,}")
        print(f"Total Payment Amount: ${payment_stats['total_amount']:,.2f}")
        print(f"Average Payment: ${payment_stats['avg_amount']:,.2f}")
        print(f"Payment Range: ${payment_stats['min_amount']:,.2f} - ${payment_stats['max_amount']:,.2f}")
        
        print()
        
        # ==============================================
        # 9. MERGER SUCCESS SUMMARY
        # ==============================================
        print("🎉 MERGER SUCCESS SUMMARY")
        print("-" * 35)
        
        print(f"✅ Total Records: {total_records:,}")
        print(f"✅ Duplicate Emails Eliminated: {remaining_duplicates == 0}")
        print(f"✅ Payment Histories Preserved: {records_with_payment_history:,}")
        print(f"✅ Customer Lifetime Value Calculated: ${clv_stats['total_clv']:,.2f}")
        print(f"✅ VIP Customers Identified: {vip_count:,}")
        print(f"✅ Repeat Customers Segmented: {repeat_count:,}")
        
        # Calculate estimated reduction
        original_estimate = 11174  # From previous analysis
        reduction = original_estimate - total_records
        print(f"✅ Database Size Reduction: ~{reduction:,} records")
        
        print()
        print("🚀 DUPLICATE EMAIL MERGER COMPLETED SUCCESSFULLY!")
        print("📊 All business-critical data preserved and consolidated")
        print("💎 Customer segmentation and analytics enhanced")
        
        print()
        print("=" * 60)
        
        # ==============================================
        # 10. EXPORT TOP CUSTOMERS FOR REVIEW
        # ==============================================
        print("📤 EXPORTING TOP CUSTOMERS FOR REVIEW")
        print("-" * 45)
        
        # Export top customers to CSV for business review
        cursor.execute("""
            SELECT 
                full_name as "Full Name",
                email as "Email",
                phone as "Phone",
                customer_lifetime_value as "Lifetime Value",
                total_payments as "Total Payments",
                customer_type as "Customer Type",
                CASE WHEN is_vip_customer = 1 THEN 'YES' ELSE 'NO' END as "VIP Customer",
                workshop_type as "Workshop Type",
                data_source as "Data Source",
                created_time as "First Seen"
            FROM leads 
            WHERE customer_lifetime_value > 0
            ORDER BY customer_lifetime_value DESC
            LIMIT 100
        """)
        
        top_customers_data = cursor.fetchall()
        
        # Convert to DataFrame and save
        df = pd.DataFrame([dict(row) for row in top_customers_data])
        export_filename = f"top_customers_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(export_filename, index=False)
        
        print(f"✅ Exported top 100 customers to: {export_filename}")
        print(f"📊 Total high-value customers exported: {len(df)}")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    database_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    analyze_merger_results(database_path)