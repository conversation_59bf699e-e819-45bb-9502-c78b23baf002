# Detailed Duplicate Analysis: Cleaned_Leads_Sheet.xlsx

## Executive Summary
**Analysis Result: 96 new leads out of 3,047 total rows (not 97 as initially reported)**

## Complete Breakdown: 3,047 → 96 New Leads

### Step 1: Excel File Analysis
- **Total rows in Excel file:** 3,047
- **Empty/NaN emails:** 34 (removed)
- **Invalid email format:** 2 (removed)
  - Examples: '<PERSON><PERSON>araj', 'raj@buildwisea.i'
- **Valid emails remaining:** 3,011

### Step 2: Internal Duplicate Removal
- **Internal duplicates found:** 380 unique email addresses with duplicates
- **Total duplicate instances:** 510 (emails appearing multiple times)
- **Unique valid emails after deduplication:** 2,501

**Examples of internal duplicates:**
- <EMAIL> (appears 2 times)
- <EMAIL> (appears 2 times)
- <EMAIL> (appears 2 times)
- <EMAIL> (appears 2 times)
- <EMAIL> (appears 2 times)

### Step 3: Database Overlap Analysis
- **Database total records:** 3,862
- **Database unique emails (normalized):** 3,415
- **Excel unique emails:** 2,501
- **Overlapping emails (already in database):** 2,405
- **Overlap percentage:** 96.2%
- **NEW EMAILS (not in database):** 96

## Database Source Breakdown
The existing 3,862 database records come from:
- US Leads.xlsx - Lead Sheet: 1,630 records
- Sheet20 - US Leads.xlsx: 735 records
- Facebook: 722 records
- Stripe: 668 records
- us_lead_crm_new_leads: 56 records
- Various workshop sheets: 51 records
- Manual entries: 1 record

## Data Quality Issues Identified

### 1. Case/Whitespace Variations in Database
- **Found 78 emails with case/whitespace variations**
- Database contains 3,493 unique raw emails vs 3,415 normalized emails
- Examples of variations:
  - <EMAIL> (2 variations)
  - <EMAIL> (3 variations)
  - <EMAIL> (2 variations)

### 2. High Internal Duplication in Excel
- 380 email addresses appear multiple times within the Excel file
- This represents 15.2% of unique emails having internal duplicates
- Total of 510 duplicate instances removed

## Sample New Emails (96 total)
These emails are NOT in the database and would be new additions:
1. <EMAIL>
2. <EMAIL>
3. <EMAIL>
4. <EMAIL>
5. <EMAIL>
6. <EMAIL>
7. <EMAIL>
8. <EMAIL>
9. <EMAIL>
10. <EMAIL>
... and 86 more

## Sample Overlapping Emails (2,405 total)
These emails already exist in the database:
1. <EMAIL>
2. <EMAIL>
3. <EMAIL>
4. <EMAIL>
5. <EMAIL>
6. <EMAIL>
7. <EMAIL>
8. <EMAIL>
9. <EMAIL>
10. <EMAIL>
... and 2,395 more

## Verification & Quality Checks

✅ **Database Query Verification:**
- Total database records: 3,862 ✓
- Unique normalized emails: 3,415 ✓
- Case-insensitive email matching implemented ✓
- Whitespace trimming applied ✓

✅ **Calculation Verification:**
- 2,501 (Excel unique) - 2,405 (overlapping) = 96 (new emails) ✓

✅ **Email Validation:**
- Regex pattern validation applied ✓
- Email normalization (lowercase, trimmed) ✓
- Empty/null email filtering ✓

## Why Only 96 New Leads?

The Cleaned_Leads_Sheet.xlsx file appears to be a consolidated file containing leads that have already been imported into the database from various sources. The analysis shows:

1. **96.2% overlap** with existing database records
2. **High internal duplication** (510 duplicate instances)
3. **Multiple data sources** already represented in database
4. **Comprehensive existing coverage** of the lead universe

This suggests the Excel file is more of a "master export" rather than a new source of fresh leads.

## Recommendation

The 96 new unique leads represent legitimate additions to the database. The high overlap percentage (96.2%) is expected and indicates good data integrity - the Excel file largely contains leads that have already been properly imported from their original sources.

---
**Analysis Date:** August 7, 2025  
**Tools Used:** Python pandas, sqlite3, regex validation  
**Source File:** `/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/leads_data/Cleaned_Leads_Sheet.xlsx`  
**Database:** `/Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/database/leads.db`