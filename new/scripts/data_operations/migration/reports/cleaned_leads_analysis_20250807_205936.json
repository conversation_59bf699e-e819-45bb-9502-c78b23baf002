{"file_info": {"name": "Cleaned_Leads_Sheet.xlsx", "sheets": ["Sheet1"], "total_sheets": 1}, "sheet_analysis": {"Sheet1": {"rows": 3047, "columns": 3, "column_names": ["First Name", "Last Name", "Email"], "data_types": {"First Name": "object", "Last Name": "object", "Email": "object"}, "lead_indicators": {"name_cols": ["First Name", "Last Name"], "email_cols": ["Email"], "phone_cols": [], "date_cols": [], "payment_cols": [], "source_cols": [], "workshop_cols": []}, "email_stats": {"total": 3013, "valid": 3012, "unique": 2524, "duplicates": 489}, "sample_data": [{"First Name": "<PERSON><PERSON><PERSON>", "Last Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"First Name": "<PERSON><PERSON><PERSON>", "Last Name": "<PERSON><PERSON>", "Email": "<EMAIL>"}, {"First Name": "<PERSON>", "Last Name": "<PERSON>", "Email": "<EMAIL>"}]}}, "database_comparison": {"Sheet1": {"total_db_leads": 3862, "total_valid_emails": 2502, "existing_count": 2405, "new_count": 97, "overlap_percentage": 96.12310151878496, "overlapping_emails": ["<EMAIL>", "venka<PERSON><PERSON><PERSON><PERSON><PERSON><EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "new_emails_sample": ["<EMAIL>", "mahmud<PERSON><EMAIL>", "ka<PERSON><PERSON>@gmail.com", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]}}, "migration_recommendation": {"action": "MIGRATE", "reasoning": "97 new records found - moderate value addition", "estimated_new_records": 97, "estimated_existing_records": 2405, "value_assessment": "Medium value - decent number of quality leads", "migration_steps": ["Use workshop_parser_template.py for data processing", "Apply data cleaning (email normalization, phone formatting)", "Set data_source = 'Cleaned_Leads_Sheet.xlsx'", "Import only new records (skip existing emails)", "Validate data quality after import", "Update lead statistics and dashboard"]}}