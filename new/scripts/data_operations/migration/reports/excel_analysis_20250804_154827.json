{"US Leads.xlsx": {"file_name": "US Leads.xlsx", "sheets": {"Lead Sheet": {"rows": 1959, "columns": 8, "column_names": ["ar", "Name", "Phone", "Email", "Python Exp", "How to contact?", "Designation", "Notes"], "data_types": {"ar": "object", "Name": "object", "Phone": "object", "Email": "object", "Python Exp": "object", "How to contact?": "object", "Designation": "object", "Notes": "object"}, "preview": [{"ar": NaN, "Name": NaN, "Phone": NaN, "Email": NaN, "Python Exp": NaN, "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"ar": "2024-08-26 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 2408932297, "Email": "<EMAIL>", "Python Exp": "beginner", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"ar": "2024-08-26 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 5623745835, "Email": "<EMAIL>", "Python Exp": "no_experience", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"ar": "2024-08-26 00:00:00", "Name": "<PERSON>", "Phone": 2104600511, "Email": "<EMAIL>", "Python Exp": "expert", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"ar": "2024-08-26 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 6472953180, "Email": "<EMAIL>", "Python Exp": "no_experience", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"ar": "2024-08-26 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 7654046393, "Email": "<EMAIL>", "Python Exp": "No experience", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"ar": "2024-08-26 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 5126563562, "Email": "An<PERSON><EMAIL>", "Python Exp": "No experience", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"ar": "2024-08-26 00:00:00", "Name": "Bindu G", "Phone": 4083002343, "Email": "<EMAIL>", "Python Exp": "<PERSON><PERSON><PERSON>", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"ar": "2024-08-26 00:00:00", "Name": "<PERSON>", "Phone": 6366753353, "Email": "<EMAIL>", "Python Exp": "No experience", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"ar": "2024-08-26 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Phone": 6693073203, "Email": "<EMAIL>", "Python Exp": "No experience", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}]}, "Sheet20": {"rows": 1091, "columns": 2, "column_names": ["<PERSON><PERSON><PERSON>", "<EMAIL>"], "data_types": {"Rajasekhar Chappidi": "object", "<EMAIL>": "object"}, "preview": [{"Rajasekhar Chappidi": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rajasekhar Chappidi": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rajasekhar Chappidi": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rajasekhar Chappidi": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rajasekhar Chappidi": "<PERSON><PERSON><PERSON>", "<EMAIL>": "vivek<PERSON><EMAIL>"}, {"Rajasekhar Chappidi": "<PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rajasekhar Chappidi": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rajasekhar Chappidi": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rajasekhar Chappidi": "Kranti", "<EMAIL>": "<EMAIL>"}, {"Rajasekhar Chappidi": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "L1 April 5&6": {"rows": 13, "columns": 10, "column_names": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9"], "data_types": {"Lead Date": "datetime64[ns]", "Payment Date": "datetime64[ns]", "Name": "object", "Phone": "object", "Email": "object", "Payment Method": "object", "Amount": "float64", "Unnamed: 7": "object", "Unnamed: 8": "object", "Unnamed: 9": "object"}, "preview": [{"Lead Date": "NaT", "Payment Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN}, {"Lead Date": "NaT", "Payment Date": "2024-12-04 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": 6047678023, "Email": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "Payment Method": "Stripe", "Amount": 320.0, "Unnamed: 7": "AA315", "Unnamed: 8": NaN, "Unnamed: 9": "AA451 Niece"}, {"Lead Date": "NaT", "Payment Date": "2025-01-27 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": "************", "Email": "pat<PERSON><PERSON><PERSON><PERSON>@gmail.com", "Payment Method": "Stripe", "Amount": 320.0, "Unnamed: 7": "AA447", "Unnamed: 8": NaN, "Unnamed: 9": "AA438 <PERSON><PERSON>yaselvan referred"}, {"Lead Date": "NaT", "Payment Date": "2024-10-22 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 7047067791, "Email": "chandra<PERSON><PERSON><EMAIL>", "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": NaN, "Unnamed: 8": "Repeat", "Unnamed: 9": "Gurveen"}, {"Lead Date": "NaT", "Payment Date": "NaT", "Name": "<PERSON><PERSON>", "Phone": 7788550602, "Email": "<EMAIL>", "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": "AA453", "Unnamed: 8": "Repeat", "Unnamed: 9": "Jagadish"}, {"Lead Date": "2024-08-29 00:00:00", "Payment Date": "2025-02-28 00:00:00", "Name": "Biplab <PERSON>", "Phone": 4372168870, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 150.0, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON><PERSON>"}, {"Lead Date": "NaT", "Payment Date": "2025-03-02 00:00:00", "Name": "<PERSON>", "Phone": 8133786199, "Email": "gadi<PERSON><PERSON>@ieee.org", "Payment Method": "Stripe", "Amount": 240.0, "Unnamed: 7": "AA487", "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON><PERSON>"}, {"Lead Date": "2025-02-28 00:00:00", "Payment Date": "2025-03-03 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 6504489748, "Email": "vivek<PERSON><EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": "Na<PERSON><PERSON>"}, {"Lead Date": "2025-02-28 00:00:00", "Payment Date": "2025-03-04 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 2143074075, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON>"}, {"Lead Date": "NaT", "Payment Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": "Nipun"}]}, "A1 May 3&4": {"rows": 3, "columns": 8, "column_names": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7"], "data_types": {"Lead Date": "datetime64[ns]", "Payment Date": "datetime64[ns]", "Name": "object", "Phone": "float64", "Email": "object", "Payment Method": "object", "Amount": "float64", "Unnamed: 7": "object"}, "preview": [{"Lead Date": "NaT", "Payment Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": NaN}, {"Lead Date": "2025-12-19 00:00:00", "Payment Date": "2024-12-20 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Phone": 7326663947.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 272.0, "Unnamed: 7": "AA368"}, {"Lead Date": "NaT", "Payment Date": "2025-02-24 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 2486352174.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 256.0, "Unnamed: 7": "AA55"}]}, "Week Report": {"rows": 40, "columns": 12, "column_names": ["Unnamed: 0", "Marketing spent (in INR)", "Total Leads", "Cost per lead", "Conversions", "# of Payments", "$ amount", "Pipeline", "COCA", "Monthly COCA", "Monthly rev", "Direct:Referrals:Alumnis"], "data_types": {"Unnamed: 0": "object", "Marketing spent (in INR)": "float64", "Total Leads": "float64", "Cost per lead": "float64", "Conversions": "object", "# of Payments": "float64", "$ amount": "float64", "Pipeline": "float64", "COCA": "float64", "Monthly COCA": "object", "Monthly rev": "object", "Direct:Referrals:Alumnis": "object"}, "preview": [{"Unnamed: 0": "Aug Week 5", "Marketing spent (in INR)": 100000.0, "Total Leads": 75.0, "Cost per lead": 1333.333333, "Conversions": 17, "# of Payments": 3.0, "$ amount": 690.0, "Pipeline": NaN, "COCA": 5882.352941, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Sep Week 1", "Marketing spent (in INR)": 70000.0, "Total Leads": 108.0, "Cost per lead": 648.1481481, "Conversions": 9, "# of Payments": 5.0, "$ amount": 1070.0, "Pipeline": 2.0, "COCA": 7777.777778, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Sep Week 2", "Marketing spent (in INR)": 77000.0, "Total Leads": 86.0, "Cost per lead": 895.3488372, "Conversions": 6, "# of Payments": 7.0, "$ amount": 502.0, "Pipeline": 3.0, "COCA": 12833.33333, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Sep Week 3", "Marketing spent (in INR)": 60000.0, "Total Leads": 90.0, "Cost per lead": 666.6666667, "Conversions": 6, "# of Payments": 9.0, "$ amount": 2035.0, "Pipeline": 3.0, "COCA": 10000.0, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Sep Week 4", "Marketing spent (in INR)": 20000.0, "Total Leads": 32.0, "Cost per lead": 625.0, "Conversions": 2, "# of Payments": 9.0, "$ amount": 2425.0, "Pipeline": 5.0, "COCA": 10000.0, "Monthly COCA": 7566.666667, "Monthly rev": 6032, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Oct Week 1", "Marketing spent (in INR)": 30000.0, "Total Leads": 21.0, "Cost per lead": 1428.571429, "Conversions": 0, "# of Payments": 0.0, "$ amount": 0.0, "Pipeline": 5.0, "COCA": NaN, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Oct Week 2", "Marketing spent (in INR)": 25000.0, "Total Leads": 46.0, "Cost per lead": 543.4782609, "Conversions": 3, "# of Payments": 3.0, "$ amount": 790.0, "Pipeline": 15.0, "COCA": 8333.333333, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Oct Week 3", "Marketing spent (in INR)": 65000.0, "Total Leads": 77.0, "Cost per lead": 844.1558442, "Conversions": 4, "# of Payments": 10.0, "$ amount": 1936.0, "Pipeline": 7.0, "COCA": 16250.0, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Oct Week 4", "Marketing spent (in INR)": 30000.0, "Total Leads": 49.0, "Cost per lead": 612.244898, "Conversions": 2, "# of Payments": 4.0, "$ amount": 920.0, "Pipeline": 8.0, "COCA": 15000.0, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Oct Week 5 ", "Marketing spent (in INR)": 36000.0, "Total Leads": 58.0, "Cost per lead": 620.6896552, "Conversions": 3, "# of Payments": 4.0, "$ amount": 880.0, "Pipeline": 10.0, "COCA": 12000.0, "Monthly COCA": 8857.142857, "Monthly rev": 4526, "Direct:Referrals:Alumnis": NaN}]}, "Marketing Spent": {"rows": 160, "columns": 12, "column_names": ["India Campaign", "Unnamed: 1", "Unnamed: 2", "Unnamed: 3", "US Campaign", "Unnamed: 5", "Total Leads", "Lead Closure", "Unnamed: 8", "Unnamed: 9", "Google Ads", "Unnamed: 11"], "data_types": {"India Campaign": "object", "Unnamed: 1": "object", "Unnamed: 2": "float64", "Unnamed: 3": "object", "US Campaign": "object", "Unnamed: 5": "float64", "Total Leads": "float64", "Lead Closure": "float64", "Unnamed: 8": "float64", "Unnamed: 9": "object", "Google Ads": "object", "Unnamed: 11": "object"}, "preview": [{"India Campaign": NaN, "Unnamed: 1": NaN, "Unnamed: 2": NaN, "Unnamed: 3": NaN, "US Campaign": NaN, "Unnamed: 5": NaN, "Total Leads": NaN, "Lead Closure": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Google Ads": NaN, "Unnamed: 11": NaN}, {"India Campaign": "Date", "Unnamed: 1": "Amount", "Unnamed: 2": NaN, "Unnamed: 3": "Date", "US Campaign": "Amount", "Unnamed: 5": NaN, "Total Leads": NaN, "Lead Closure": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Google Ads": "Date", "Unnamed: 11": "Amount"}, {"India Campaign": "Week1", "Unnamed: 1": NaN, "Unnamed: 2": NaN, "Unnamed: 3": NaN, "US Campaign": NaN, "Unnamed: 5": NaN, "Total Leads": NaN, "Lead Closure": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Google Ads": NaN, "Unnamed: 11": NaN}, {"India Campaign": "2024-02-09 00:00:00", "Unnamed: 1": 7500, "Unnamed: 2": NaN, "Unnamed: 3": "2024-01-09 00:00:00", "US Campaign": 15000, "Unnamed: 5": NaN, "Total Leads": NaN, "Lead Closure": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Google Ads": "2024-10-09 00:00:00", "Unnamed: 11": 10000}, {"India Campaign": "2024-03-09 00:00:00", "Unnamed: 1": 15000, "Unnamed: 2": NaN, "Unnamed: 3": "2024-02-09 00:00:00", "US Campaign": 20000, "Unnamed: 5": NaN, "Total Leads": NaN, "Lead Closure": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Google Ads": NaN, "Unnamed: 11": NaN}, {"India Campaign": "2024-04-09 00:00:00", "Unnamed: 1": 10000, "Unnamed: 2": NaN, "Unnamed: 3": "2024-03-09 00:00:00", "US Campaign": 20000, "Unnamed: 5": NaN, "Total Leads": NaN, "Lead Closure": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Google Ads": NaN, "Unnamed: 11": NaN}, {"India Campaign": "2024-05-09 00:00:00", "Unnamed: 1": 2500, "Unnamed: 2": NaN, "Unnamed: 3": "2024-05-09 00:00:00", "US Campaign": 15000, "Unnamed: 5": NaN, "Total Leads": NaN, "Lead Closure": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Google Ads": NaN, "Unnamed: 11": NaN}, {"India Campaign": "2024-05-09 00:00:00", "Unnamed: 1": 10000, "Unnamed: 2": NaN, "Unnamed: 3": NaN, "US Campaign": NaN, "Unnamed: 5": NaN, "Total Leads": NaN, "Lead Closure": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Google Ads": NaN, "Unnamed: 11": NaN}, {"India Campaign": "Total", "Unnamed: 1": 45000, "Unnamed: 2": NaN, "Unnamed: 3": NaN, "US Campaign": 70000, "Unnamed: 5": NaN, "Total Leads": 81.0, "Lead Closure": 6.0, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Google Ads": NaN, "Unnamed: 11": NaN}, {"India Campaign": "Week2", "Unnamed: 1": NaN, "Unnamed: 2": NaN, "Unnamed: 3": NaN, "US Campaign": NaN, "Unnamed: 5": NaN, "Total Leads": NaN, "Lead Closure": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Google Ads": NaN, "Unnamed: 11": NaN}]}, "A1 March 1&2": {"rows": 41, "columns": 11, "column_names": ["Lead Date", "Payment Date", "Name", "Email", "Phone", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "<PERSON><PERSON><PERSON>", "Unnamed: 10"], "data_types": {"Lead Date": "object", "Payment Date": "datetime64[ns]", "Name": "object", "Email": "object", "Phone": "object", "Payment Method": "object", "Amount": "float64", "Unnamed: 7": "object", "Unnamed: 8": "object", "Kannan M": "object", "Unnamed: 10": "object"}, "preview": [{"Lead Date": NaN, "Payment Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Kannan M": "<PERSON><PERSON><PERSON> next batch", "Unnamed: 10": NaN}, {"Lead Date": NaN, "Payment Date": "2024-12-16 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 3194711338, "Payment Method": "<PERSON><PERSON>", "Amount": 240.0, "Unnamed: 7": "L1-11", "Unnamed: 8": NaN, "Kannan M": "<PERSON><PERSON><PERSON><PERSON> next batch", "Unnamed: 10": "L1-4"}, {"Lead Date": NaN, "Payment Date": "2025-01-15 00:00:00", "Name": "<PERSON>", "Email": "<EMAIL>", "Phone": 5418703257, "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": "L1-62", "Unnamed: 8": NaN, "Kannan M": NaN, "Unnamed: 10": NaN}, {"Lead Date": "2025-01-07 00:00:00", "Payment Date": "2025-02-01 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 6099022410, "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": "AA400", "Unnamed: 8": NaN, "Kannan M": "<PERSON><PERSON><PERSON> - <PERSON><PERSON> son", "Unnamed: 10": NaN}, {"Lead Date": "2025-02-05 00:00:00", "Payment Date": "2025-02-07 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "avis<PERSON><PERSON>@gmail.com", "Phone": 6083352106, "Payment Method": "Stripe", "Amount": 256.0, "Unnamed: 7": "AA490", "Unnamed: 8": NaN, "Kannan M": "AA451", "Unnamed: 10": NaN}, {"Lead Date": NaN, "Payment Date": "NaT", "Name": "<PERSON><PERSON><PERSON> Kodavati", "Email": "<EMAIL>", "Phone": 9045999866, "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": "L1-3", "Unnamed: 8": "Repeat", "Kannan M": "AA450", "Unnamed: 10": NaN}, {"Lead Date": NaN, "Payment Date": "2025-02-08 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 2899240744, "Payment Method": "Stripe", "Amount": 272.0, "Unnamed: 7": "L1-32", "Unnamed: 8": NaN, "Kannan M": "L1-47", "Unnamed: 10": NaN}, {"Lead Date": NaN, "Payment Date": "2025-02-09 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 3103570473, "Payment Method": "Stripe", "Amount": 272.0, "Unnamed: 7": "L1-18", "Unnamed: 8": NaN, "Kannan M": "AA400", "Unnamed: 10": NaN}, {"Lead Date": "2025-01-07 00:00:00", "Payment Date": "2025-02-12 00:00:00", "Name": "<PERSON>", "Email": "<EMAIL>", "Phone": 2132005505, "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": "AA401", "Unnamed: 8": NaN, "Kannan M": "AA469 - will check with friend and let me know. As of now, given 15% discount and if friend enrolls then 25%", "Unnamed: 10": NaN}, {"Lead Date": "2025-02-10 00:00:00", "Payment Date": "2025-02-13 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 4807380861, "Payment Method": "Stripe", "Amount": 240.0, "Unnamed: 7": "AA470", "Unnamed: 8": NaN, "Kannan M": "<PERSON><PERSON><PERSON>", "Unnamed: 10": NaN}]}, "L1 Feb1&2": {"rows": 44, "columns": 10, "column_names": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9"], "data_types": {"Lead Date": "datetime64[ns]", "Payment Date": "object", "Name": "object", "Phone": "object", "Email": "object", "Payment Method": "object", "Amount": "object", "Unnamed: 7": "object", "Unnamed: 8": "object", "Unnamed: 9": "object"}, "preview": [{"Lead Date": "NaT", "Payment Date": NaN, "Name": NaN, "Phone": NaN, "Email": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN}, {"Lead Date": "NaT", "Payment Date": "2024-11-17 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 4252899083, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230, "Unnamed: 7": "L1-101", "Unnamed: 8": "<PERSON><PERSON><PERSON>", "Unnamed: 9": "L1-17"}, {"Lead Date": "NaT", "Payment Date": "2024-11-17 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 4252896818, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 320, "Unnamed: 7": "L1-102", "Unnamed: 8": "<PERSON><PERSON><PERSON>", "Unnamed: 9": NaN}, {"Lead Date": "2024-10-16 00:00:00", "Payment Date": "2024-10-22 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 7047067791, "Email": "chandra<PERSON><PERSON><EMAIL>", "Payment Method": "Stripe", "Amount": 230, "Unnamed: 7": "L1-103", "Unnamed: 8": NaN, "Unnamed: 9": NaN}, {"Lead Date": "NaT", "Payment Date": NaN, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": 4092910950, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": NaN, "Unnamed: 7": NaN, "Unnamed: 8": "Repeat", "Unnamed: 9": "Reynold - Jan batch"}, {"Lead Date": "2024-10-22 00:00:00", "Payment Date": "2024-10-23 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 5149668920, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230, "Unnamed: 7": "L1-104", "Unnamed: 8": NaN, "Unnamed: 9": "AA295"}, {"Lead Date": "2024-12-19 00:00:00", "Payment Date": "2024-12-26 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 2065659928, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 256, "Unnamed: 7": "AA375", "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON> - next batch"}, {"Lead Date": "2024-12-10 00:00:00", "Payment Date": "2025-01-10 00:00:00", "Name": "<PERSON>", "Phone": 5714716116, "Email": "v<PERSON><PERSON><PERSON><PERSON>@yahoo.com", "Payment Method": "Stripe", "Amount": 272, "Unnamed: 7": "AA392", "Unnamed: 8": NaN, "Unnamed: 9": NaN}, {"Lead Date": "NaT", "Payment Date": NaN, "Name": "<PERSON>", "Phone": 4089214386, "Email": "<EMAIL>", "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": "L1-20", "Unnamed: 8": "Repeat", "Unnamed: 9": "<PERSON><PERSON>"}, {"Lead Date": "NaT", "Payment Date": "2025-01-17 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 7033093293, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230, "Unnamed: 7": "AA406", "Unnamed: 8": NaN, "Unnamed: 9": NaN}]}, "A1 Jan18&19": {"rows": 43, "columns": 11, "column_names": ["Unnamed: 0", "Payment Date", "Name", "Email", "Phone", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "data_types": {"Unnamed: 0": "object", "Payment Date": "datetime64[ns]", "Name": "object", "Email": "object", "Phone": "object", "Payment Method": "object", "Amount": "float64", "Unnamed: 7": "object", "Unnamed: 8": "object", "Unnamed: 9": "object", "Unnamed: 10": "object"}, "preview": [{"Unnamed: 0": NaN, "Payment Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Unnamed: 0": NaN, "Payment Date": "2024-11-05 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "ka<PERSON><PERSON><PERSON><PERSON>@gmail.com", "Phone": 6692256600, "Payment Method": "Stripe", "Amount": 320.0, "Unnamed: 7": "<PERSON><PERSON><PERSON>", "Unnamed: 8": "P", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Unnamed: 0": "2024-11-20 00:00:00", "Payment Date": "2024-12-05 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 5712097359, "Payment Method": "Stripe", "Amount": 185.0, "Unnamed: 7": "L1-77", "Unnamed: 8": "P", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Unnamed: 0": NaN, "Payment Date": "2024-12-16 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 4053341160, "Payment Method": "Stripe", "Amount": 272.0, "Unnamed: 7": "L1-27", "Unnamed: 8": "P", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Unnamed: 0": NaN, "Payment Date": "2024-12-17 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 4084725631, "Payment Method": "Stripe", "Amount": 272.0, "Unnamed: 7": "L1-21", "Unnamed: 8": "P", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Unnamed: 0": NaN, "Payment Date": "2024-12-18 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "Phone": 6786629052, "Payment Method": "Stripe", "Amount": 272.0, "Unnamed: 7": "L1-70", "Unnamed: 8": "P", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Unnamed: 0": NaN, "Payment Date": "2024-12-18 00:00:00", "Name": "<PERSON><PERSON><PERSON> Kodavati", "Email": "<EMAIL>", "Phone": 9045999866, "Payment Method": "Stripe", "Amount": 272.0, "Unnamed: 7": "L1-3", "Unnamed: 8": "P", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Unnamed: 0": NaN, "Payment Date": "2024-12-19 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 9802548213, "Payment Method": "Stripe", "Amount": 272.0, "Unnamed: 7": "L1-42", "Unnamed: 8": "P", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Unnamed: 0": NaN, "Payment Date": "2024-12-19 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 2016738310, "Payment Method": "Stripe", "Amount": 320.0, "Unnamed: 7": "L1-12", "Unnamed: 8": "P", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Unnamed: 0": NaN, "Payment Date": "2024-12-23 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 2019126720, "Payment Method": "Stripe", "Amount": 272.0, "Unnamed: 7": "L1-61", "Unnamed: 8": "P", "Unnamed: 9": NaN, "Unnamed: 10": NaN}]}, "L1 Dec 14&15": {"rows": 34, "columns": 11, "column_names": ["n", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "data_types": {"n": "datetime64[ns]", "Payment Date": "datetime64[ns]", "Name": "object", "Phone": "float64", "Email": "object", "Payment Method": "object", "Amount": "float64", "Unnamed: 7": "object", "Unnamed: 8": "object", "Unnamed: 9": "object", "Unnamed: 10": "object"}, "preview": [{"n": "NaT", "Payment Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"n": "2024-10-10 00:00:00", "Payment Date": "2024-10-11 00:00:00", "Name": "<PERSON>", "Phone": 3095338299.0, "Email": "<EMAIL>", "Payment Method": "<PERSON><PERSON>", "Amount": 150.0, "Unnamed: 7": "L1-60", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"n": "2024-11-05 00:00:00", "Payment Date": "2024-11-06 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 2019126720.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 185.0, "Unnamed: 7": "L1-61", "Unnamed: 8": NaN, "Unnamed: 9": "Pipeline", "Unnamed: 10": "Email"}, {"n": "NaT", "Payment Date": "2024-10-22 00:00:00", "Name": "<PERSON>", "Phone": 5418703257.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 185.0, "Unnamed: 7": "L1-62", "Unnamed: 8": "Manish closed", "Unnamed: 9": "AA288", "Unnamed: 10": "<PERSON><PERSON>"}, {"n": "2024-08-26 00:00:00", "Payment Date": "2024-10-23 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 5109530237.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": "L1-63", "Unnamed: 8": NaN, "Unnamed: 9": "AA269", "Unnamed: 10": "<PERSON><PERSON>"}, {"n": "2024-11-12 00:00:00", "Payment Date": "2024-11-17 00:00:00", "Name": "chand", "Phone": 4162742525.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": "L1-64", "Unnamed: 8": NaN, "Unnamed: 9": "L1-47", "Unnamed: 10": NaN}, {"n": "2024-10-16 00:00:00", "Payment Date": "2024-11-18 00:00:00", "Name": "<PERSON>", "Phone": 9172268327.0, "Email": "jaymu<PERSON><PERSON>@gmail.com", "Payment Method": "Stripe", "Amount": 185.0, "Unnamed: 7": "L1-65", "Unnamed: 8": NaN, "Unnamed: 9": "Bilal", "Unnamed: 10": "<PERSON><PERSON><PERSON>"}, {"n": "2024-09-01 00:00:00", "Payment Date": "2024-11-21 00:00:00", "Name": "Ram<PERSON>bu <PERSON>", "Phone": 4695501961.0, "Email": "<EMAIL>", "Payment Method": "<PERSON><PERSON>", "Amount": 150.0, "Unnamed: 7": "L1-66", "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON>", "Unnamed: 10": NaN}, {"n": "2024-11-19 00:00:00", "Payment Date": "2024-11-22 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 6474102313.0, "Email": "<EMAIL>", "Payment Method": "<PERSON><PERSON>", "Amount": 185.0, "Unnamed: 7": "L1-67", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": "Ballara - Later"}, {"n": "2024-08-26 00:00:00", "Payment Date": "2024-11-23 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 5126563562.0, "Email": "An<PERSON><EMAIL>", "Payment Method": "<PERSON><PERSON>", "Amount": 230.0, "Unnamed: 7": "L1-68", "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON>- reach out in Jan", "Unnamed: 10": NaN}]}, "L2 Nov16&17": {"rows": 31, "columns": 9, "column_names": ["Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 6", "Unnamed: 7", "Unnamed: 8"], "data_types": {"Date": "datetime64[ns]", "Name": "object", "Phone": "object", "Email": "object", "Payment Method": "object", "Amount": "float64", "Unnamed: 6": "object", "Unnamed: 7": "object", "Unnamed: 8": "object"}, "preview": [{"Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN}, {"Date": "2024-09-11 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": 7139075073, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 240.0, "Unnamed: 6": "L1-4", "Unnamed: 7": NaN, "Unnamed: 8": "yes"}, {"Date": "2024-09-13 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 3194711338, "Email": "<EMAIL>", "Payment Method": "<PERSON><PERSON>", "Amount": 320.0, "Unnamed: 6": "L1-11", "Unnamed: 7": NaN, "Unnamed: 8": "yes"}, {"Date": "2024-09-21 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 7853808970, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 320.0, "Unnamed: 6": "L1-57", "Unnamed: 7": NaN, "Unnamed: 8": "yes"}, {"Date": "2024-09-28 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 2016738310, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 320.0, "Unnamed: 6": "L1-12", "Unnamed: 7": NaN, "Unnamed: 8": NaN}, {"Date": "2024-10-07 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 4084725631, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 320.0, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": "yes"}, {"Date": "2024-10-13 00:00:00", "Name": "<PERSON>", "Phone": "************", "Email": "<EMAIL>", "Payment Method": "<PERSON><PERSON>", "Amount": 200.0, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN}, {"Date": "2024-10-14 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 4085077813, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 320.0, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN}, {"Date": "2024-10-17 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 6092164769, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 272.0, "Unnamed: 6": "L1-13", "Unnamed: 7": NaN, "Unnamed: 8": "yes"}, {"Date": "2024-10-17 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 7347251146, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 240.0, "Unnamed: 6": "L1-38", "Unnamed: 7": NaN, "Unnamed: 8": "yes"}]}, "L1 Nov9&10": {"rows": 21, "columns": 10, "column_names": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9"], "data_types": {"Lead Date": "datetime64[ns]", "Payment Date": "datetime64[ns]", "Name": "object", "Phone": "object", "Email": "object", "Payment Method": "object", "Amount": "float64", "Unnamed: 7": "object", "Unnamed: 8": "object", "Unnamed: 9": "object"}, "preview": [{"Lead Date": "NaT", "Payment Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN}, {"Lead Date": "NaT", "Payment Date": "2024-09-25 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Phone": 9802548213, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 185.0, "Unnamed: 7": "L1-42", "Unnamed: 8": "Manish Closed", "Unnamed: 9": "aa260"}, {"Lead Date": "NaT", "Payment Date": "NaT", "Name": "<PERSON>", "Phone": 9259151181, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 370.0, "Unnamed: 7": "L1-43", "Unnamed: 8": "email", "Unnamed: 9": "aa265"}, {"Lead Date": "NaT", "Payment Date": "NaT", "Name": "<PERSON><PERSON><PERSON> (Sita)", "Phone": "************", "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": "L1-44", "Unnamed: 8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unnamed: 9": "aa191"}, {"Lead Date": "2024-10-10 00:00:00", "Payment Date": "2024-10-13 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 6266647684, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": "L1-45", "Unnamed: 8": NaN, "Unnamed: 9": "aa201"}, {"Lead Date": "2024-09-05 00:00:00", "Payment Date": "2024-10-23 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>nan", "Phone": 6122244995, "Email": "Narendran.<PERSON><PERSON>@gmail.com", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": "L1-46", "Unnamed: 8": NaN, "Unnamed: 9": "aa92"}, {"Lead Date": "2024-10-16 00:00:00", "Payment Date": "2024-10-29 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": 9253891785, "Email": "santhos<PERSON>.<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": "L1-47", "Unnamed: 8": NaN, "Unnamed: 9": "aa224"}, {"Lead Date": "2024-10-17 00:00:00", "Payment Date": "2024-10-30 00:00:00", "Name": "<PERSON><PERSON>", "Phone": "************", "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 7": "L1-48", "Unnamed: 8": NaN, "Unnamed: 9": "aa255"}, {"Lead Date": "NaT", "Payment Date": "2024-10-31 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 4259411661, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 210.0, "Unnamed: 7": "L1-49", "Unnamed: 8": "<PERSON><PERSON>", "Unnamed: 9": "aa256"}, {"Lead Date": "2024-10-28 00:00:00", "Payment Date": "2024-10-31 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 7203002156, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 210.0, "Unnamed: 7": "L1-50", "Unnamed: 8": NaN, "Unnamed: 9": "aa257"}]}, "L1 Sep28&29": {"rows": 32, "columns": 8, "column_names": ["Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 6", "Unnamed: 7"], "data_types": {"Date": "datetime64[ns]", "Name": "object", "Phone": "object", "Email": "object", "Payment Method": "object", "Amount": "float64", "Unnamed: 6": "object", "Unnamed: 7": "object"}, "preview": [{"Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 6": NaN, "Unnamed: 7": NaN}, {"Date": "2024-09-12 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Phone": "************", "Email": "mural<PERSON><PERSON><PERSON>av<PERSON>@yahoo.com", "Payment Method": "Stripe", "Amount": 185.0, "Unnamed: 6": "L1-16", "Unnamed: 7": "<PERSON>"}, {"Date": "2024-09-13 00:00:00", "Name": "<PERSON>", "Phone": "************", "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 185.0, "Unnamed: 6": "L1-17", "Unnamed: 7": "<PERSON>"}, {"Date": "2024-09-13 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 3103570473, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 6": "L1-18", "Unnamed: 7": "Interested in Dec Level 2 class"}, {"Date": "2024-09-13 00:00:00", "Name": "<PERSON>", "Phone": 4083165994, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 200.0, "Unnamed: 6": "L1-19", "Unnamed: 7": NaN}, {"Date": "2024-09-14 00:00:00", "Name": "<PERSON>", "Phone": 4089214386, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 6": "L1-20", "Unnamed: 7": NaN}, {"Date": "2024-09-15 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 4084725631, "Email": "<EMAIL>", "Payment Method": "<PERSON><PERSON>", "Amount": 230.0, "Unnamed: 6": "L1-21", "Unnamed: 7": NaN}, {"Date": "2024-09-15 00:00:00", "Name": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "Phone": 3236201992, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 6": "L1-22", "Unnamed: 7": NaN}, {"Date": "2024-09-15 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 6692121906, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 6": "L1-23", "Unnamed: 7": "<PERSON>"}, {"Date": "2024-09-15 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 7328291302, "Email": "dodejas<PERSON><EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 6": "L1-24", "Unnamed: 7": "Attend March class"}]}, "L1 Sep7&8": {"rows": 20, "columns": 8, "column_names": ["Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 6", "Unnamed: 7"], "data_types": {"Date": "datetime64[ns]", "Name": "object", "Phone": "float64", "Email": "object", "Payment Method": "object", "Amount": "float64", "Unnamed: 6": "object", "Unnamed: 7": "object"}, "preview": [{"Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 6": NaN, "Unnamed: 7": NaN}, {"Date": "2024-08-26 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 5623745835.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 185.0, "Unnamed: 6": NaN, "Unnamed: 7": NaN}, {"Date": "2024-08-27 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 6729990260.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 6": NaN, "Unnamed: 7": NaN}, {"Date": "2024-08-27 00:00:00", "Name": "<PERSON><PERSON><PERSON> Kodavati", "Phone": 9045999866.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 6": NaN, "Unnamed: 7": NaN}, {"Date": "2024-08-27 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": 7139075073.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 6": NaN, "Unnamed: 7": NaN}, {"Date": "2024-08-28 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 9493101939.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 6": NaN, "Unnamed: 7": NaN}, {"Date": "2024-08-28 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 6478352719.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 6": NaN, "Unnamed: 7": NaN}, {"Date": "2024-08-28 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 2142284897.0, "Email": "<PERSON><PERSON><PERSON><PERSON>@gmail.com", "Payment Method": "Stripe", "Amount": 230.0, "Unnamed: 6": NaN, "Unnamed: 7": NaN}, {"Date": "2024-08-29 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 6099373885.0, "Email": "<EMAIL>", "Payment Method": "<PERSON><PERSON>", "Amount": 200.0, "Unnamed: 6": "email", "Unnamed: 7": NaN}, {"Date": "2024-08-30 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 7328224533.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 185.0, "Unnamed: 6": NaN, "Unnamed: 7": NaN}]}, "Young AI": {"rows": 13, "columns": 11, "column_names": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "data_types": {"Lead Date": "datetime64[ns]", "Payment Date": "datetime64[ns]", "Name": "object", "Phone": "float64", "Email": "object", "Payment Method": "object", "Amount": "float64", "Unnamed: 7": "object", "Unnamed: 8": "object", "Unnamed: 9": "float64", "Unnamed: 10": "object"}, "preview": [{"Lead Date": "NaT", "Payment Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Payment Method": NaN, "Amount": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Lead Date": "NaT", "Payment Date": "2024-11-12 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 4084725631.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 250.0, "Unnamed: 7": NaN, "Unnamed: 8": "Send both us and india meeting links", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Lead Date": "NaT", "Payment Date": "2024-11-12 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 4084725631.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 250.0, "Unnamed: 7": NaN, "Unnamed: 8": "Send both us and india meeting links", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Lead Date": "NaT", "Payment Date": "2024-11-14 00:00:00", "Name": "<PERSON>", "Phone": 8056571999.0, "Email": "shawn<PERSON><PERSON>@gmail.com", "Payment Method": "Stripe", "Amount": 329.0, "Unnamed: 7": "AY01", "Unnamed: 8": "<PERSON><PERSON>", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Lead Date": "2024-11-20 00:00:00", "Payment Date": "2024-11-25 00:00:00", "Name": "Narsing R Bidar", "Phone": 3052983310.0, "Email": "<EMAIL>", "Payment Method": "<PERSON><PERSON>", "Amount": 200.0, "Unnamed: 7": "AA297", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Lead Date": "2025-08-28 00:00:00", "Payment Date": "2025-12-20 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 4077619397.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 275.0, "Unnamed: 7": "AA18", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Lead Date": "NaT", "Payment Date": "2024-12-04 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 7325433392.0, "Email": "<EMAIL>", "Payment Method": "<PERSON><PERSON>", "Amount": 220.0, "Unnamed: 7": "L1-53", "Unnamed: 8": "<PERSON><PERSON>", "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Lead Date": "2024-11-20 00:00:00", "Payment Date": "2024-12-05 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 7038556937.0, "Email": "<EMAIL>", "Payment Method": "Stripe", "Amount": 185.0, "Unnamed: 7": "AA316", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Lead Date": "2024-11-25 00:00:00", "Payment Date": "2024-12-09 00:00:00", "Name": "D<PERSON><PERSON>van G Reddy", "Phone": 7326478716.0, "Email": "<EMAIL>", "Payment Method": "<PERSON><PERSON>", "Amount": 200.0, "Unnamed: 7": "AA341", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Lead Date": "NaT", "Payment Date": "2024-12-10 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 9176853551.0, "Email": "<EMAIL>", "Payment Method": "Phonepe to Sandeep", "Amount": 186.0, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": "L1-25"}]}, "Young AI Marketing": {"rows": 2, "columns": 11, "column_names": ["Unnamed: 0", "Marketing spent (in INR)", "Total Leads", "Cost per lead", "Conversations", "# of Payments", "$ amount", "Pipeline", "COCA", "Unnamed: 9", "Notes"], "data_types": {"Unnamed: 0": "object", "Marketing spent (in INR)": "float64", "Total Leads": "float64", "Cost per lead": "float64", "Conversations": "float64", "# of Payments": "float64", "$ amount": "float64", "Pipeline": "float64", "COCA": "float64", "Unnamed: 9": "float64", "Notes": "float64"}, "preview": [{"Unnamed: 0": NaN, "Marketing spent (in INR)": NaN, "Total Leads": NaN, "Cost per lead": NaN, "Conversations": NaN, "# of Payments": NaN, "$ amount": NaN, "Pipeline": NaN, "COCA": NaN, "Unnamed: 9": NaN, "Notes": NaN}, {"Unnamed: 0": "Nov Week 2", "Marketing spent (in INR)": 7500.0, "Total Leads": 7.0, "Cost per lead": NaN, "Conversations": 0.0, "# of Payments": 3.0, "$ amount": 775.0, "Pipeline": 0.0, "COCA": NaN, "Unnamed: 9": NaN, "Notes": NaN}]}, "Potential Enterprise": {"rows": 6, "columns": 2, "column_names": ["Organisation Name", "Unnamed: 1"], "data_types": {"Organisation Name": "object", "Unnamed: 1": "object"}, "preview": [{"Organisation Name": NaN, "Unnamed: 1": NaN}, {"Organisation Name": "Shell", "Unnamed: 1": "<PERSON><PERSON>"}, {"Organisation Name": "<PERSON> - Germany", "Unnamed: 1": "<PERSON><PERSON><PERSON>"}, {"Organisation Name": "<PERSON><PERSON> ", "Unnamed: 1": "Manish"}, {"Organisation Name": "Fleetgaurd - Pathak", "Unnamed: 1": "<PERSON><PERSON>"}, {"Organisation Name": "<PERSON> <PERSON> <PERSON>", "Unnamed: 1": "Balaji/Maha"}]}}, "total_sheets": 17, "mapping_suggestions": {"potential_leads": [{"sheet": "Lead Sheet", "columns": ["ar", "Name", "Phone", "Email", "Python Exp", "How to contact?", "Designation", "Notes"], "confidence": "high"}, {"sheet": "L1 April 5&6", "columns": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9"], "confidence": "high"}, {"sheet": "A1 May 3&4", "columns": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7"], "confidence": "high"}, {"sheet": "Week Report", "columns": ["Unnamed: 0", "Marketing spent (in INR)", "Total Leads", "Cost per lead", "Conversions", "# of Payments", "$ amount", "Pipeline", "COCA", "Monthly COCA", "Monthly rev", "Direct:Referrals:Alumnis"], "confidence": "medium"}, {"sheet": "Marketing Spent", "columns": ["India Campaign", "Unnamed: 1", "Unnamed: 2", "Unnamed: 3", "US Campaign", "Unnamed: 5", "Total Leads", "Lead Closure", "Unnamed: 8", "Unnamed: 9", "Google Ads", "Unnamed: 11"], "confidence": "medium"}, {"sheet": "A1 March 1&2", "columns": ["Lead Date", "Payment Date", "Name", "Email", "Phone", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "<PERSON><PERSON><PERSON>", "Unnamed: 10"], "confidence": "high"}, {"sheet": "L1 Feb1&2", "columns": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9"], "confidence": "high"}, {"sheet": "A1 Jan18&19", "columns": ["Unnamed: 0", "Payment Date", "Name", "Email", "Phone", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "confidence": "high"}, {"sheet": "L1 Dec 14&15", "columns": ["n", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "confidence": "high"}, {"sheet": "L2 Nov16&17", "columns": ["Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 6", "Unnamed: 7", "Unnamed: 8"], "confidence": "high"}, {"sheet": "L1 Nov9&10", "columns": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9"], "confidence": "high"}, {"sheet": "L1 Sep28&29", "columns": ["Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 6", "Unnamed: 7"], "confidence": "high"}, {"sheet": "L1 Sep7&8", "columns": ["Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 6", "Unnamed: 7"], "confidence": "high"}, {"sheet": "Young AI", "columns": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "confidence": "high"}, {"sheet": "Young AI Marketing", "columns": ["Unnamed: 0", "Marketing spent (in INR)", "Total Leads", "Cost per lead", "Conversations", "# of Payments", "$ amount", "Pipeline", "COCA", "Unnamed: 9", "Notes"], "confidence": "medium"}, {"sheet": "Potential Enterprise", "columns": ["Organisation Name", "Unnamed: 1"], "confidence": "medium"}], "potential_workshops": [], "potential_payments": [{"sheet": "L1 April 5&6", "columns": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9"], "confidence": "high"}, {"sheet": "A1 May 3&4", "columns": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7"], "confidence": "high"}, {"sheet": "Week Report", "columns": ["Unnamed: 0", "Marketing spent (in INR)", "Total Leads", "Cost per lead", "Conversions", "# of Payments", "$ amount", "Pipeline", "COCA", "Monthly COCA", "Monthly rev", "Direct:Referrals:Alumnis"], "confidence": "high"}, {"sheet": "A1 March 1&2", "columns": ["Lead Date", "Payment Date", "Name", "Email", "Phone", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "<PERSON><PERSON><PERSON>", "Unnamed: 10"], "confidence": "high"}, {"sheet": "L1 Feb1&2", "columns": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9"], "confidence": "high"}, {"sheet": "A1 Jan18&19", "columns": ["Unnamed: 0", "Payment Date", "Name", "Email", "Phone", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "confidence": "high"}, {"sheet": "L1 Dec 14&15", "columns": ["n", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "confidence": "high"}, {"sheet": "L2 Nov16&17", "columns": ["Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 6", "Unnamed: 7", "Unnamed: 8"], "confidence": "high"}, {"sheet": "L1 Nov9&10", "columns": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9"], "confidence": "high"}, {"sheet": "L1 Sep28&29", "columns": ["Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 6", "Unnamed: 7"], "confidence": "high"}, {"sheet": "L1 Sep7&8", "columns": ["Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 6", "Unnamed: 7"], "confidence": "high"}, {"sheet": "Young AI", "columns": ["Lead Date", "Payment Date", "Name", "Phone", "Email", "Payment Method", "Amount", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "confidence": "high"}, {"sheet": "Young AI Marketing", "columns": ["Unnamed: 0", "Marketing spent (in INR)", "Total Leads", "Cost per lead", "Conversations", "# of Payments", "$ amount", "Pipeline", "COCA", "Unnamed: 9", "Notes"], "confidence": "high"}], "workshop_enrollments": []}}, "US Lead CRM.xlsx": {"file_name": "US Lead CRM.xlsx", "sheets": {"Dashboard": {"rows": 4, "columns": 8, "column_names": ["Unnamed: 0", "Leads", "Bought", "Revenues", "MKT Spent", "CPL", "Conv", "COCA"], "data_types": {"Unnamed: 0": "object", "Leads": "int64", "Bought": "int64", "Revenues": "object", "MKT Spent": "object", "CPL": "object", "Conv": "object", "COCA": "object"}, "preview": [{"Unnamed: 0": "Total", "Leads": 1309, "Bought": 83, "Revenues": "#REF!", "MKT Spent": 3660.91954, "CPL": 2.796729977, "Conv": 15.77108434, "COCA": 44.10746434}, {"Unnamed: 0": "Last 7 days", "Leads": 117, "Bought": 0, "Revenues": NaN, "MKT Spent": 0, "CPL": 0, "Conv": "#DIV/0!", "COCA": "#DIV/0!"}, {"Unnamed: 0": "Last 30 days", "Leads": 307, "Bought": 0, "Revenues": NaN, "MKT Spent": 0, "CPL": 0, "Conv": "#DIV/0!", "COCA": "#DIV/0!"}, {"Unnamed: 0": "Today", "Leads": 0, "Bought": 0, "Revenues": NaN, "MKT Spent": "#VALUE!", "CPL": "#VALUE!", "Conv": "#DIV/0!", "COCA": "#VALUE!"}]}, "Marketing": {"rows": 27, "columns": 13, "column_names": ["Date", "Channel", "Spent", "Spent (Dollar)", "Payment Method", "Unnamed: 5", "Unnamed: 6", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "data_types": {"Date": "datetime64[ns]", "Channel": "object", "Spent": "float64", "Spent (Dollar)": "float64", "Payment Method": "object", "Unnamed: 5": "float64", "Unnamed: 6": "object", "Unnamed: 7": "object", "Unnamed: 8": "object", "Unnamed: 9": "object", "Unnamed: 10": "object", "Unnamed: 11": "object", "Unnamed: 12": "object"}, "preview": [{"Date": "2025-02-04 00:00:00", "Channel": "Facebook", "Spent": 2500.0, "Spent (Dollar)": 28.73563218, "Payment Method": "ICICI", "Unnamed: 5": NaN, "Unnamed: 6": "USD to INR", "Unnamed: 7": 87, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Date": "2025-02-05 00:00:00", "Channel": "Facebook", "Spent": 12500.0, "Spent (Dollar)": 143.6781609, "Payment Method": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Date": "2025-02-06 00:00:00", "Channel": "Facebook", "Spent": 15000.0, "Spent (Dollar)": 172.4137931, "Payment Method": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": "May US Results", "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Date": "2025-02-10 00:00:00", "Channel": "Facebook", "Spent": 10000.0, "Spent (Dollar)": 114.9425287, "Payment Method": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": "Budget", "Unnamed: 9": "Leads", "Unnamed: 10": "Closures from the leads", "Unnamed: 11": "Total closures", "Unnamed: 12": "Revenue"}, {"Date": "2025-02-11 00:00:00", "Channel": "Facebook", "Spent": 15000.0, "Spent (Dollar)": 172.4137931, "Payment Method": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN, "Unnamed: 7": "May ", "Unnamed: 8": "1,75,000", "Unnamed: 9": 282, "Unnamed: 10": 23, "Unnamed: 11": 40, "Unnamed: 12": 12124}, {"Date": "2025-02-13 00:00:00", "Channel": "Facebook", "Spent": 15000.0, "Spent (Dollar)": 172.4137931, "Payment Method": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Date": "2025-02-17 00:00:00", "Channel": "Facebook", "Spent": 12000.0, "Spent (Dollar)": 137.9310345, "Payment Method": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Date": "2025-02-19 00:00:00", "Channel": "Facebook", "Spent": 13000.0, "Spent (Dollar)": 149.4252874, "Payment Method": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Date": "2025-02-20 00:00:00", "Channel": "Facebook", "Spent": 15000.0, "Spent (Dollar)": 172.4137931, "Payment Method": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Date": "2025-02-25 00:00:00", "Channel": "Facebook", "Spent": 15000.0, "Spent (Dollar)": 172.4137931, "Payment Method": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN, "Unnamed: 7": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}]}, "Bootcamp codes": {"rows": 4, "columns": 3, "column_names": ["Unnamed: 0", "US", "India"], "data_types": {"Unnamed: 0": "object", "US": "object", "India": "object"}, "preview": [{"Unnamed: 0": "Essentials", "US": "EU0725", "India": "EI0725"}, {"Unnamed: 0": "Agents", "US": "AU0725", "India": "AI0725"}, {"Unnamed: 0": "Practioners", "US": "PU0725", "India": "PI0725"}, {"Unnamed: 0": "Vibe", "US": "VU0725", "India": "VI0725"}]}, "India Leads": {"rows": 67, "columns": 16, "column_names": ["Contact Code", "Date", "Name", "Email", "Phone", "Status", "Purchase", "Python Exp", "Profession", "Company", "Designation", "LinkedIn Profile", "Notes", "Reach Out Count", "Last Contact Date", "Next Action"], "data_types": {"Contact Code": "object", "Date": "datetime64[ns]", "Name": "object", "Email": "object", "Phone": "object", "Status": "object", "Purchase": "float64", "Python Exp": "object", "Profession": "object", "Company": "float64", "Designation": "float64", "LinkedIn Profile": "float64", "Notes": "float64", "Reach Out Count": "float64", "Last Contact Date": "float64", "Next Action": "float64"}, "preview": [{"Contact Code": NaN, "Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Status": NaN, "Purchase": NaN, "Python Exp": NaN, "Profession": NaN, "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": NaN, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "EI07301", "Date": "2025-07-30 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 919717508748, "Status": NaN, "Purchase": NaN, "Python Exp": "Expert", "Profession": "Software Engineer", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": NaN, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "EI07302", "Date": "2025-07-30 00:00:00", "Name": "Satish MK", "Email": "<EMAIL>", "Phone": 918143700849, "Status": NaN, "Purchase": NaN, "Python Exp": "<PERSON><PERSON><PERSON>", "Profession": "Project manager", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": NaN, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "EI07303", "Date": "2025-07-30 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON> ", "Email": "j<PERSON><PERSON><PERSON><PERSON>@gmail.com", "Phone": 919949537586, "Status": NaN, "Purchase": NaN, "Python Exp": "Intermediary", "Profession": "Senior Engineering", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": NaN, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "EI07304", "Date": "2025-07-30 00:00:00", "Name": "<PERSON><PERSON>", "Email": "dilli<PERSON><PERSON><PERSON><EMAIL>", "Phone": 917894685566, "Status": NaN, "Purchase": NaN, "Python Exp": "<PERSON><PERSON><PERSON>", "Profession": "full stake developer", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": NaN, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "EI07305", "Date": "2025-07-30 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 919959913961, "Status": NaN, "Purchase": NaN, "Python Exp": "No Experience", "Profession": "Team leader", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": NaN, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "EI07306", "Date": "2025-07-30 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 919951859747, "Status": NaN, "Purchase": NaN, "Python Exp": "Intermediary", "Profession": "Sr tech lead", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": NaN, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "EI07307", "Date": "2025-07-30 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "p<PERSON><PERSON><PERSON><PERSON>@gmail.com", "Phone": 918142346319, "Status": NaN, "Purchase": NaN, "Python Exp": "<PERSON><PERSON><PERSON>", "Profession": "Software Developer", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": NaN, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "EI07308", "Date": "2025-07-30 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 919791387901, "Status": NaN, "Purchase": NaN, "Python Exp": "Intermediary", "Profession": "Lead Analyst", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": NaN, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "EI07309", "Date": "2025-07-30 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 917696964845, "Status": NaN, "Purchase": NaN, "Python Exp": "Expert", "Profession": NaN, "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": NaN, "Last Contact Date": NaN, "Next Action": NaN}]}, "Updates": {"rows": 0, "columns": 0, "column_names": [], "data_types": {}, "preview": []}, "Email": {"rows": 301, "columns": 2, "column_names": ["Name", "Email"], "data_types": {"Name": "object", "Email": "object"}, "preview": [{"Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON>", "Email": "<EMAIL>"}]}, "New Leads": {"rows": 1401, "columns": 18, "column_names": ["Contact Code", "Date", "Channel", "Name", "Email", "Phone", "Status", "Purchase", "Python Exp", "How to contact?", "Profession", "Company", "Designation", "LinkedIn Profile", "Notes", "Reach Out Count", "Last Contact Date", "Next Action"], "data_types": {"Contact Code": "object", "Date": "object", "Channel": "object", "Name": "object", "Email": "object", "Phone": "object", "Status": "object", "Purchase": "object", "Python Exp": "object", "How to contact?": "object", "Profession": "object", "Company": "float64", "Designation": "float64", "LinkedIn Profile": "float64", "Notes": "float64", "Reach Out Count": "float64", "Last Contact Date": "float64", "Next Action": "float64"}, "preview": [{"Contact Code": "CUS-B-01", "Date": "2025-02-28 00:00:00", "Channel": "Facebook", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": "+***********", "Status": "Not responded", "Purchase": NaN, "Python Exp": "No experience", "How to contact?": "email", "Profession": "Business Development", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": 1.0, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "CUS-B-02", "Date": "2025-02-28 00:00:00", "Channel": "Facebook", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": "+***********", "Status": "Not responded", "Purchase": NaN, "Python Exp": "No experience", "How to contact?": "<EMAIL>", "Profession": "<PERSON><PERSON><PERSON><PERSON>", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": 0.0, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "CUS-B-03", "Date": "2025-02-28 00:00:00", "Channel": "Facebook", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": "+***********", "Status": "Not responded", "Purchase": NaN, "Python Exp": "<PERSON><PERSON><PERSON>", "How to contact?": "email", "Profession": "Qa", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": 3.0, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "CUS-B-04", "Date": "2025-02-28 00:00:00", "Channel": "Facebook", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": "+15857542226", "Status": "Not responded", "Purchase": NaN, "Python Exp": "Intermediary", "How to contact?": "Afternoon", "Profession": "Front end developer", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": 0.0, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "CUS-B-05", "Date": "2025-02-28 00:00:00", "Channel": "Facebook", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": "+14252690534", "Status": "Considering", "Purchase": "Intro call", "Python Exp": "No experience", "How to contact?": "call", "Profession": "Senior IT manager", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": 0.0, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "CUS-B-06", "Date": "2025-02-28 00:00:00", "Channel": "Facebook", "Name": "<PERSON><PERSON><PERSON>", "Email": "vivek<PERSON><EMAIL>", "Phone": "+16504489748", "Status": "Bought", "Purchase": "3/3/2025", "Python Exp": "<PERSON><PERSON><PERSON>", "How to contact?": "Weekends or after 5 pm", "Profession": "Senior Engineer", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": 0.0, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "CUS-B-07", "Date": "2025-02-28 00:00:00", "Channel": "Facebook", "Name": "<PERSON>", "Email": "<EMAIL>", "Phone": "+12012085681", "Status": "Not responded", "Purchase": NaN, "Python Exp": "Intermediary", "How to contact?": "<EMAIL>", "Profession": "SDE", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": 0.0, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "CUS-B-08", "Date": "2025-02-28 00:00:00", "Channel": "Facebook", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": "+19522614195", "Status": "Not responded", "Purchase": NaN, "Python Exp": "No experience", "How to contact?": "email", "Profession": "Quality Engineering", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": 0.0, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "CUS-B-09", "Date": "2025-02-28 00:00:00", "Channel": "Facebook", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": "+12143074075", "Status": "Bought", "Purchase": "3/4/2025", "Python Exp": "Expert", "How to contact?": "email", "Profession": "Tech lead", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": 0.0, "Last Contact Date": NaN, "Next Action": NaN}, {"Contact Code": "CUS-B-10", "Date": "2025-03-02 00:00:00", "Channel": "Facebook", "Name": "Kranti", "Email": "<EMAIL>", "Phone": "+12036405399", "Status": "Not responded", "Purchase": NaN, "Python Exp": "<PERSON><PERSON><PERSON>", "How to contact?": "email", "Profession": "Own", "Company": NaN, "Designation": NaN, "LinkedIn Profile": NaN, "Notes": NaN, "Reach Out Count": 0.0, "Last Contact Date": NaN, "Next Action": NaN}]}, "L1 Aug 15-17": {"rows": 28, "columns": 12, "column_names": ["Lead Date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11"], "data_types": {"Lead Date": "object", "Purchase Date": "datetime64[ns]", "Name": "object", "Email": "object", "Phone": "object", "Source": "object", "Amount": "float64", "Contact Code": "object", "Unnamed: 8": "object", "Unnamed: 9": "object", "Unnamed: 10": "object", "Unnamed: 11": "object"}, "preview": [{"Lead Date": NaN, "Purchase Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": "Payment Link", "Unnamed: 11": "Next batch"}, {"Lead Date": "2025-05-12 00:00:00", "Purchase Date": "2025-05-13 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 14086019906, "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA782", "Unnamed: 8": NaN, "Unnamed: 9": "AA997", "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Lead Date": "2025-03-23 00:00:00", "Purchase Date": "2025-03-28 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 12019854233, "Source": "Stripe", "Amount": 150.0, "Contact Code": "AA575", "Unnamed: 8": NaN, "Unnamed: 9": "AA1043", "Unnamed: 10": "<PERSON><PERSON><PERSON>", "Unnamed: 11": "AA1108"}, {"Lead Date": "2025-05-30 00:00:00", "Purchase Date": "2025-06-04 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 17348345085, "Source": "Stripe", "Amount": 210.0, "Contact Code": "AA926", "Unnamed: 8": NaN, "Unnamed: 9": "AA1071", "Unnamed: 10": "AA1019", "Unnamed: 11": "AA787"}, {"Lead Date": NaN, "Purchase Date": "NaT", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 16179013971, "Source": NaN, "Amount": NaN, "Contact Code": "AA653", "Unnamed: 8": "Repeat", "Unnamed: 9": "AA718", "Unnamed: 10": "<PERSON><PERSON><PERSON>", "Unnamed: 11": NaN}, {"Lead Date": NaN, "Purchase Date": "2025-07-24 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 17742622441, "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA884", "Unnamed: 8": NaN, "Unnamed: 9": "AA1012 - Monday", "Unnamed: 10": "AA1160", "Unnamed: 11": NaN}, {"Lead Date": "<PERSON><PERSON> referral", "Purchase Date": "2025-07-15 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": NaN, "Source": "Stripe", "Amount": 240.0, "Contact Code": "AA978", "Unnamed: 8": NaN, "Unnamed: 9": "AA570", "Unnamed: 10": "AA1053", "Unnamed: 11": NaN}, {"Lead Date": "<PERSON>", "Purchase Date": "2025-07-21 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<PERSON><EMAIL>", "Phone": "************", "Source": "Stripe", "Amount": 240.0, "Contact Code": "AA979", "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON><PERSON> - Google voice", "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Lead Date": "<PERSON><PERSON><PERSON>", "Purchase Date": "2025-07-22 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": "************", "Source": "Stripe", "Amount": 240.0, "Contact Code": "AA1207", "Unnamed: 8": NaN, "Unnamed: 9": "AA1049 - Sunday", "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Lead Date": NaN, "Purchase Date": "2025-07-22 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 4084725631, "Source": "Stripe", "Amount": 160.0, "Contact Code": "L1-21", "Unnamed: 8": NaN, "Unnamed: 9": "AA877", "Unnamed: 10": NaN, "Unnamed: 11": NaN}]}, "L2 Sep 5-7": {"rows": 6, "columns": 9, "column_names": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8"], "data_types": {"Lead date": "float64", "Purchase Date": "datetime64[ns]", "Name": "object", "Email": "object", "Phone": "float64", "Source": "object", "Amount": "float64", "Contact Code": "object", "Unnamed: 8": "object"}, "preview": [{"Lead date": NaN, "Purchase Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": NaN, "Unnamed: 8": NaN}, {"Lead date": NaN, "Purchase Date": "2025-05-05 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 9145467861.0, "Source": "Stripe", "Amount": 240.0, "Contact Code": "AA631", "Unnamed: 8": NaN}, {"Lead date": NaN, "Purchase Date": "2025-06-25 00:00:00", "Name": "<PERSON><PERSON>", "Email": NaN, "Phone": NaN, "Source": "Stripe", "Amount": 185.0, "Contact Code": "L1-70", "Unnamed: 8": NaN}, {"Lead date": NaN, "Purchase Date": "2025-07-22 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 4084725631.0, "Source": "Stripe", "Amount": 160.0, "Contact Code": "L1-21", "Unnamed: 8": NaN}, {"Lead date": NaN, "Purchase Date": "2025-07-31 00:00:00", "Name": "Varada Pyda", "Email": "<EMAIL>", "Phone": NaN, "Source": "Stripe", "Amount": 272.0, "Contact Code": NaN, "Unnamed: 8": NaN}, {"Lead date": NaN, "Purchase Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": "L1-90", "Unnamed: 8": "Repeat"}]}, "A1 Oct": {"rows": 4, "columns": 11, "column_names": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "data_types": {"Lead date": "float64", "Purchase Date": "float64", "Name": "object", "Email": "object", "Phone": "float64", "Source": "float64", "Amount": "float64", "Contact Code": "object", "Unnamed: 8": "object", "Unnamed: 9": "float64", "Unnamed: 10": "object"}, "preview": [{"Lead date": NaN, "Purchase Date": NaN, "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"Lead date": NaN, "Purchase Date": NaN, "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 7162077578.0, "Source": NaN, "Amount": NaN, "Contact Code": "AA449", "Unnamed: 8": "Repeat Class", "Unnamed: 9": NaN, "Unnamed: 10": "AA941"}, {"Lead date": NaN, "Purchase Date": NaN, "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": "AA1108"}, {"Lead date": NaN, "Purchase Date": NaN, "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": "AA115"}]}, "V1 Nov": {"rows": 8, "columns": 10, "column_names": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9"], "data_types": {"Lead date": "float64", "Purchase Date": "datetime64[ns]", "Name": "object", "Email": "object", "Phone": "float64", "Source": "object", "Amount": "float64", "Contact Code": "object", "Unnamed: 8": "object", "Unnamed: 9": "object"}, "preview": [{"Lead date": NaN, "Purchase Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN}, {"Lead date": NaN, "Purchase Date": "2025-05-21 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 6474102313.0, "Source": "Stripe", "Amount": 246.75, "Contact Code": "L1-67", "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON><PERSON>- email"}, {"Lead date": NaN, "Purchase Date": "NaT", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 447405979988.0, "Source": NaN, "Amount": NaN, "Contact Code": "AA955", "Unnamed: 8": "Repeat", "Unnamed: 9": "AA626"}, {"Lead date": NaN, "Purchase Date": "2025-05-28 00:00:00", "Name": "<PERSON>", "Email": "<EMAIL>", "Phone": 6178521792.0, "Source": "Stripe", "Amount": 246.75, "Contact Code": "L1-94", "Unnamed: 8": NaN, "Unnamed: 9": NaN}, {"Lead date": NaN, "Purchase Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": "AA623", "Unnamed: 8": NaN, "Unnamed: 9": NaN}, {"Lead date": NaN, "Purchase Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": "AA55", "Unnamed: 8": NaN, "Unnamed: 9": NaN}, {"Lead date": NaN, "Purchase Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": "AA950", "Unnamed: 8": NaN, "Unnamed: 9": NaN}, {"Lead date": NaN, "Purchase Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": "AA796", "Unnamed: 8": NaN, "Unnamed: 9": NaN}]}, "V1 June13-15": {"rows": 36, "columns": 11, "column_names": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Next batch"], "data_types": {"Lead date": "object", "Purchase Date": "datetime64[ns]", "Name": "object", "Email": "object", "Phone": "object", "Source": "object", "Amount": "float64", "Contact Code": "object", "Unnamed: 8": "object", "Unnamed: 9": "object", "Next batch": "object"}, "preview": [{"Lead date": NaN, "Purchase Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Next batch": NaN}, {"Lead date": NaN, "Purchase Date": "2025-03-30 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 9802548213, "Source": "Stripe", "Amount": 246.75, "Contact Code": "L1-42", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Next batch": "L1-79"}, {"Lead date": NaN, "Purchase Date": "2025-04-08 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 14802082093, "Source": "Stripe", "Amount": 246.75, "Contact Code": "AA488", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Next batch": "<PERSON><PERSON>"}, {"Lead date": NaN, "Purchase Date": "2025-04-08 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Email": "r<PERSON><PERSON><PERSON><PERSON>@yahoo.co.in", "Phone": 14259997981, "Source": "Stripe", "Amount": 246.75, "Contact Code": "AA621", "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON><PERSON>", "Next batch": NaN}, {"Lead date": NaN, "Purchase Date": "NaT", "Name": "<PERSON>", "Email": "<EMAIL>", "Phone": 2052188963, "Source": NaN, "Amount": NaN, "Contact Code": "AA293", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Next batch": NaN}, {"Lead date": NaN, "Purchase Date": "NaT", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 3144941267, "Source": NaN, "Amount": NaN, "Contact Code": "L1-54", "Unnamed: 8": NaN, "Unnamed: 9": "AA626", "Next batch": NaN}, {"Lead date": NaN, "Purchase Date": "NaT", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 7802311509, "Source": NaN, "Amount": NaN, "Contact Code": "L1-96", "Unnamed: 8": "Repeat", "Unnamed: 9": NaN, "Next batch": NaN}, {"Lead date": NaN, "Purchase Date": "2025-02-24 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 2486352174, "Source": "Stripe", "Amount": 256.0, "Contact Code": "AA55", "Unnamed: 8": NaN, "Unnamed: 9": "L1-15", "Next batch": NaN}, {"Lead date": NaN, "Purchase Date": "2025-05-19 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 5512291094, "Source": "Stripe", "Amount": 246.75, "Contact Code": "L1-59", "Unnamed: 8": NaN, "Unnamed: 9": "L1-33", "Next batch": NaN}, {"Lead date": NaN, "Purchase Date": "NaT", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 6124049894, "Source": NaN, "Amount": NaN, "Contact Code": "AA474", "Unnamed: 8": "Repeat", "Unnamed: 9": "AA474 Friend", "Next batch": NaN}]}, "A1 May 10&11": {"rows": 50, "columns": 12, "column_names": ["Customer ID", "Lead Date", "Purchase Date", "Name", "Phone", "Email", "Source", "Amount", "Product", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11"], "data_types": {"Customer ID": "float64", "Lead Date": "object", "Purchase Date": "datetime64[ns]", "Name": "object", "Phone": "object", "Email": "object", "Source": "object", "Amount": "float64", "Product": "object", "Unnamed: 9": "object", "Unnamed: 10": "object", "Unnamed: 11": "object"}, "preview": [{"Customer ID": NaN, "Lead Date": NaN, "Purchase Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Source": NaN, "Amount": NaN, "Product": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Customer ID": NaN, "Lead Date": "2025-12-19 00:00:00", "Purchase Date": "2024-12-20 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Phone": "<EMAIL>", "Email": 7326663947, "Source": "Stripe", "Amount": 272.0, "Product": "AA368", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": "Next batch"}, {"Customer ID": NaN, "Lead Date": "G<PERSON>ham Referral", "Purchase Date": "2025-03-08 00:00:00", "Name": "Kalpita ", "Phone": "<EMAIL>", "Email": 9198690529, "Source": "Stripe", "Amount": 230.0, "Product": "AA393", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": "<PERSON><PERSON><PERSON>"}, {"Customer ID": NaN, "Lead Date": "2025-01-22 00:00:00", "Purchase Date": "2025-03-22 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><EMAIL>", "Email": 2245078345, "Source": "Stripe", "Amount": 225.0, "Product": "AA619", "Unnamed: 9": NaN, "Unnamed: 10": "<PERSON><PERSON><PERSON>", "Unnamed: 11": "<PERSON><PERSON><PERSON>"}, {"Customer ID": NaN, "Lead Date": "Sandeep referral", "Purchase Date": "2025-03-26 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": "<EMAIL>", "Email": 8185315314, "Source": "Stripe", "Amount": 230.0, "Product": "AA613", "Unnamed: 9": NaN, "Unnamed: 10": "<PERSON><PERSON><PERSON><PERSON>", "Unnamed: 11": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Customer ID": NaN, "Lead Date": NaN, "Purchase Date": "NaT", "Name": "<PERSON><PERSON><PERSON>", "Phone": "<EMAIL>", "Email": 4084725631, "Source": NaN, "Amount": NaN, "Product": "L1-21", "Unnamed: 9": "Repeat", "Unnamed: 10": NaN, "Unnamed: 11": "AA734"}, {"Customer ID": NaN, "Lead Date": NaN, "Purchase Date": "2025-04-06 00:00:00", "Name": "<PERSON><PERSON>", "Phone": "<EMAIL>", "Email": 16047047966, "Source": "Stripe", "Amount": 240.0, "Product": "AA625", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": "<PERSON><PERSON>"}, {"Customer ID": NaN, "Lead Date": "2025-04-08 00:00:00", "Purchase Date": "2025-04-09 00:00:00", "Name": "<PERSON><PERSON>", "Phone": "<EMAIL>", "Email": 17132567112, "Source": "Stripe", "Amount": 240.0, "Product": "AA646", "Unnamed: 9": NaN, "Unnamed: 10": "Stuti", "Unnamed: 11": NaN}, {"Customer ID": NaN, "Lead Date": "Inbound email", "Purchase Date": "2025-04-11 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": "<EMAIL>", "Email": "************", "Source": "Stripe", "Amount": 240.0, "Product": "AA763", "Unnamed: 9": NaN, "Unnamed: 10": "AA665", "Unnamed: 11": NaN}, {"Customer ID": NaN, "Lead Date": NaN, "Purchase Date": "2025-04-17 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": "<EMAIL>", "Email": 12148720584, "Source": "<PERSON><PERSON>", "Amount": 150.0, "Product": "AA617", "Unnamed: 9": NaN, "Unnamed: 10": "L1-85", "Unnamed: 11": NaN}]}, "L1 June7&8": {"rows": 38, "columns": 12, "column_names": ["Lead Date", "Purchase Date", "Name", "Phone", "Email", "Source", "Amount", "Contact Code", "Unnamed: 8", "AA438", "Payment link", "Next Batch"], "data_types": {"Lead Date": "object", "Purchase Date": "datetime64[ns]", "Name": "object", "Phone": "object", "Email": "object", "Source": "object", "Amount": "float64", "Contact Code": "object", "Unnamed: 8": "object", "AA438": "object", "Payment link": "object", "Next Batch": "object"}, "preview": [{"Lead Date": NaN, "Purchase Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Source": NaN, "Amount": NaN, "Contact Code": NaN, "Unnamed: 8": NaN, "AA438": "AA506", "Payment link": NaN, "Next Batch": NaN}, {"Lead Date": "2025-03-10 00:00:00", "Purchase Date": "2025-03-10 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": 14802082093, "Email": "<EMAIL>", "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA488", "Unnamed: 8": NaN, "AA438": "AA636 Friend", "Payment link": "<PERSON><PERSON>", "Next Batch": "AA787"}, {"Lead Date": "Site", "Purchase Date": "2025-03-27 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 14087074073, "Email": "<EMAIL>", "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA632", "Unnamed: 8": NaN, "AA438": "<PERSON><PERSON><PERSON>", "Payment link": "<PERSON><PERSON>", "Next Batch": "<PERSON><PERSON><PERSON>"}, {"Lead Date": NaN, "Purchase Date": "2025-01-27 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": "************", "Email": "pat<PERSON><PERSON><PERSON><PERSON>@gmail.com", "Source": "Stripe", "Amount": 320.0, "Contact Code": "AA447", "Unnamed: 8": NaN, "AA438": "<PERSON><PERSON><PERSON><PERSON>", "Payment link": NaN, "Next Batch": "AA801"}, {"Lead Date": NaN, "Purchase Date": "2024-10-22 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 7047067791, "Email": "chandra<PERSON><PERSON><EMAIL>", "Source": NaN, "Amount": NaN, "Contact Code": "AA212", "Unnamed: 8": "Repeat", "AA438": "AA572", "Payment link": NaN, "Next Batch": "AA767- he will chk with friends - 2 for 210$, 3 for 185$"}, {"Lead Date": NaN, "Purchase Date": "NaT", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": 17144748156, "Email": "<EMAIL>", "Source": NaN, "Amount": NaN, "Contact Code": "AA601", "Unnamed: 8": "Repeat", "AA438": "AA476", "Payment link": NaN, "Next Batch": "<PERSON>-<EMAIL>"}, {"Lead Date": "2025-09-12 00:00:00", "Purchase Date": "2025-05-02 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 3056132852, "Email": "<EMAIL>", "Source": "Stripe", "Amount": 240.0, "Contact Code": "AA125", "Unnamed: 8": NaN, "AA438": "AA532", "Payment link": NaN, "Next Batch": "AA960"}, {"Lead Date": "2025-04-10 00:00:00", "Purchase Date": "2025-05-08 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": 16179013971, "Email": "<EMAIL>", "Source": "Stripe", "Amount": 240.0, "Contact Code": "AA653", "Unnamed: 8": "Will attend next batch as well", "AA438": "AA29", "Payment link": NaN, "Next Batch": NaN}, {"Lead Date": "2025-05-09 00:00:00", "Purchase Date": "2025-05-14 00:00:00", "Name": "<PERSON>", "Phone": 17328223817, "Email": "<EMAIL>", "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA774", "Unnamed: 8": NaN, "AA438": "Teja - text", "Payment link": NaN, "Next Batch": NaN}, {"Lead Date": "2025-05-11 00:00:00", "Purchase Date": "2025-05-15 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 14807389587, "Email": "<EMAIL>", "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA771", "Unnamed: 8": NaN, "AA438": "<PERSON><PERSON><PERSON>", "Payment link": NaN, "Next Batch": NaN}]}, "V1 April 19&20": {"rows": 38, "columns": 13, "column_names": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "data_types": {"Lead date": "object", "Purchase Date": "datetime64[ns]", "Name": "object", "Email": "object", "Phone": "object", "Source": "object", "Amount": "float64", "Contact Code": "object", "Unnamed: 8": "object", "Unnamed: 9": "float64", "Unnamed: 10": "object", "Unnamed: 11": "object", "Unnamed: 12": "object"}, "preview": [{"Lead date": NaN, "Purchase Date": "NaT", "Name": NaN, "Email": NaN, "Phone": NaN, "Source": NaN, "Amount": NaN, "Contact Code": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Lead date": NaN, "Purchase Date": "2025-03-30 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 4084725631, "Source": "Stripe", "Amount": 329.0, "Contact Code": "L1-21", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": "Payment Link", "Unnamed: 11": "Considering", "Unnamed: 12": "Next Batch"}, {"Lead date": NaN, "Purchase Date": "2025-03-30 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 8189170137, "Source": "Stripe", "Amount": 246.75, "Contact Code": "L1-90", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Lead date": NaN, "Purchase Date": "2025-04-01 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": "************", "Source": "<PERSON><PERSON>", "Amount": 200.0, "Contact Code": "L1-58", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": "<PERSON><PERSON><PERSON>", "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Lead date": NaN, "Purchase Date": "2025-04-02 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 3194711338, "Source": "<PERSON><PERSON>", "Amount": 240.0, "Contact Code": "L1-11", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": "AA348", "Unnamed: 11": NaN, "Unnamed: 12": "<PERSON>"}, {"Lead date": NaN, "Purchase Date": "2025-04-03 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 7203002156, "Source": "Stripe", "Amount": 246.75, "Contact Code": "L1-50", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": "<PERSON>", "Unnamed: 11": "<PERSON><PERSON>", "Unnamed: 12": "<PERSON><PERSON>"}, {"Lead date": NaN, "Purchase Date": "2025-04-07 00:00:00", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 3103570473, "Source": "Stripe", "Amount": 246.75, "Contact Code": "L1-18", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": "<PERSON><PERSON><PERSON>", "Unnamed: 12": "<PERSON><PERSON>"}, {"Lead date": NaN, "Purchase Date": "2025-04-07 00:00:00", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>", "Phone": 7139075073, "Source": "Stripe", "Amount": 246.75, "Contact Code": "L1-4", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": "L1-33", "Unnamed: 11": NaN, "Unnamed: 12": "L1-103"}, {"Lead date": NaN, "Purchase Date": "2025-04-07 00:00:00", "Name": "<PERSON><PERSON>", "Email": "dodejas<PERSON><EMAIL>", "Phone": 7328291302, "Source": "Stripe", "Amount": 279.65, "Contact Code": "L1-24", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<PERSON><PERSON>"}, {"Lead date": NaN, "Purchase Date": "2025-04-07 00:00:00", "Name": "<PERSON><PERSON>", "Email": "ash<PERSON><PERSON><PERSON><EMAIL>", "Phone": 3174318565, "Source": "Stripe", "Amount": 246.75, "Contact Code": "AA495", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<PERSON><PERSON><PERSON>"}]}, "L1 April5&6": {"rows": 49, "columns": 10, "column_names": ["Lead Date", "Purchase Date", "Name", "Phone", "Email", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9"], "data_types": {"Lead Date": "object", "Purchase Date": "datetime64[ns]", "Name": "object", "Phone": "object", "Email": "object", "Source": "object", "Amount": "float64", "Contact Code": "object", "Unnamed: 8": "object", "Unnamed: 9": "object"}, "preview": [{"Lead Date": NaN, "Purchase Date": "NaT", "Name": NaN, "Phone": NaN, "Email": NaN, "Source": NaN, "Amount": NaN, "Contact Code": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN}, {"Lead Date": NaN, "Purchase Date": "NaT", "Name": "<PERSON><PERSON>", "Phone": 7788550602, "Email": "<EMAIL>", "Source": NaN, "Amount": NaN, "Contact Code": "AA453", "Unnamed: 8": "Repeat", "Unnamed: 9": "AA451 Niece"}, {"Lead Date": "2024-08-29 00:00:00", "Purchase Date": "2025-02-28 00:00:00", "Name": "Biplab <PERSON>", "Phone": 4372168870, "Email": "<EMAIL>", "Source": "Stripe", "Amount": 150.0, "Contact Code": "AA47", "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON><PERSON>"}, {"Lead Date": NaN, "Purchase Date": "2025-03-02 00:00:00", "Name": "<PERSON>", "Phone": 8133786199, "Email": "gadi<PERSON><PERSON>@ieee.org", "Source": "Stripe", "Amount": 240.0, "Contact Code": "AA487", "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON><PERSON>"}, {"Lead Date": "2025-02-28 00:00:00", "Purchase Date": "2025-03-03 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": 6504489748, "Email": "vivek<PERSON><EMAIL>", "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA630", "Unnamed: 8": NaN, "Unnamed: 9": "AA438 <PERSON><PERSON>yaselvan referred"}, {"Lead Date": "2025-02-28 00:00:00", "Purchase Date": "2025-03-04 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 5125356105, "Email": "<EMAIL>", "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA693", "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON>"}, {"Lead Date": "2025-03-03 00:00:00", "Purchase Date": "2025-03-07 00:00:00", "Name": "<PERSON><PERSON><PERSON>", "Phone": "+14254447867", "Email": "gehari<PERSON><EMAIL>", "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA628", "Unnamed: 8": NaN, "Unnamed: 9": "Nipun"}, {"Lead Date": "2025-03-03 00:00:00", "Purchase Date": "2025-03-09 00:00:00", "Name": "Jagadish Subramonyan", "Phone": "+13025213233", "Email": "<EMAIL>", "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA627", "Unnamed: 8": NaN, "Unnamed: 9": "Krishna-email"}, {"Lead Date": "2025-03-03 00:00:00", "Purchase Date": "2025-03-10 00:00:00", "Name": "<PERSON><PERSON>", "Phone": "+15147013703", "Email": "<EMAIL>", "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA626", "Unnamed: 8": NaN, "Unnamed: 9": "<PERSON><PERSON>"}, {"Lead Date": "2025-03-03 00:00:00", "Purchase Date": "2025-03-11 00:00:00", "Name": "<PERSON><PERSON>", "Phone": 16047047966, "Email": "<EMAIL>", "Source": "Stripe", "Amount": 230.0, "Contact Code": "AA625", "Unnamed: 8": NaN, "Unnamed: 9": "AA580"}]}, "Not Bought": {"error": "sequence item 0: expected str instance, datetime.datetime found"}, "Old Lead Sheet": {"rows": 1957, "columns": 9, "column_names": ["Date", "Bought or not?", "Name", "Phone", "Email", "Python Exp", "How to contact?", "Designation", "Notes"], "data_types": {"Date": "object", "Bought or not?": "object", "Name": "object", "Phone": "object", "Email": "object", "Python Exp": "object", "How to contact?": "object", "Designation": "object", "Notes": "object"}, "preview": [{"Date": "2024-08-26 00:00:00", "Bought or not?": "Bought", "Name": "<PERSON><PERSON><PERSON>", "Phone": 5623745835, "Email": "<EMAIL>", "Python Exp": "no_experience", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"Date": "2024-08-26 00:00:00", "Bought or not?": "Bought", "Name": "<PERSON><PERSON><PERSON>", "Phone": 5126563562, "Email": "An<PERSON><EMAIL>", "Python Exp": "No experience", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"Date": "2024-08-26 00:00:00", "Bought or not?": "Bought", "Name": "<PERSON><PERSON>", "Phone": 5109530237, "Email": "<EMAIL>", "Python Exp": "No experience", "How to contact?": NaN, "Designation": NaN, "Notes": NaN}, {"Date": "2024-08-27 00:00:00", "Bought or not?": "Bought", "Name": "<PERSON><PERSON><PERSON>", "Phone": 7786800701, "Email": "<EMAIL>", "Python Exp": "<PERSON><PERSON><PERSON>", "How to contact?": "email", "Designation": NaN, "Notes": NaN}, {"Date": "2024-08-27 00:00:00", "Bought or not?": "Bought", "Name": "<PERSON><PERSON><PERSON> Kodavati", "Phone": 9045999866, "Email": "<EMAIL>", "Python Exp": "No experience", "How to contact?": "email", "Designation": NaN, "Notes": NaN}, {"Date": "2024-08-27 00:00:00", "Bought or not?": "Bought", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": 7139075073, "Email": "<EMAIL>", "Python Exp": "<PERSON><PERSON><PERSON>", "How to contact?": "Afternoon", "Designation": NaN, "Notes": NaN}, {"Date": "2024-08-28 00:00:00", "Bought or not?": "Bought", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone": 4077619397, "Email": "<EMAIL>", "Python Exp": "Intermediary", "How to contact?": "Text anytime", "Designation": "Interested in Young AI Pro", "Notes": NaN}, {"Date": "2024-08-28 00:00:00", "Bought or not?": "Bought", "Name": "<PERSON><PERSON>", "Phone": 3479525248, "Email": "<EMAIL>", "Python Exp": "No experience", "How to contact?": "Evenings 7 PM -8 PM EST", "Designation": NaN, "Notes": NaN}, {"Date": "2024-08-28 00:00:00", "Bought or not?": "Bought", "Name": "<PERSON><PERSON><PERSON>", "Phone": 9493101939, "Email": "<EMAIL>", "Python Exp": "Intermediary", "How to contact?": "email", "Designation": NaN, "Notes": NaN}, {"Date": "2024-08-28 00:00:00", "Bought or not?": "Bought", "Name": "<PERSON><PERSON><PERSON>", "Phone": 6478352719, "Email": "<EMAIL>", "Python Exp": "No experience", "How to contact?": "email", "Designation": NaN, "Notes": NaN}]}, "Week Report": {"rows": 55, "columns": 12, "column_names": ["Unnamed: 0", "Marketing spent (in INR)", "Total Leads", "Cost per lead", "Conversions", "# of Payments", "$ amount", "Pipeline", "COCA", "Monthly COCA", "Monthly rev", "Direct:Referrals:Alumnis"], "data_types": {"Unnamed: 0": "object", "Marketing spent (in INR)": "float64", "Total Leads": "float64", "Cost per lead": "float64", "Conversions": "object", "# of Payments": "float64", "$ amount": "float64", "Pipeline": "float64", "COCA": "float64", "Monthly COCA": "object", "Monthly rev": "object", "Direct:Referrals:Alumnis": "object"}, "preview": [{"Unnamed: 0": "Aug Week 5", "Marketing spent (in INR)": 100000.0, "Total Leads": 75.0, "Cost per lead": 1333.333333, "Conversions": 17, "# of Payments": 3.0, "$ amount": 690.0, "Pipeline": NaN, "COCA": 5882.352941, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": NaN, "Marketing spent (in INR)": NaN, "Total Leads": NaN, "Cost per lead": NaN, "Conversions": NaN, "# of Payments": NaN, "$ amount": NaN, "Pipeline": NaN, "COCA": NaN, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Sep Week 1", "Marketing spent (in INR)": 70000.0, "Total Leads": 108.0, "Cost per lead": 648.1481481, "Conversions": 9, "# of Payments": 5.0, "$ amount": 1070.0, "Pipeline": 2.0, "COCA": 7777.777778, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Sep Week 2", "Marketing spent (in INR)": 70000.0, "Total Leads": 86.0, "Cost per lead": 813.9534884, "Conversions": 6, "# of Payments": 7.0, "$ amount": 502.0, "Pipeline": 3.0, "COCA": 11666.66667, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Sep Week 3", "Marketing spent (in INR)": 60000.0, "Total Leads": 90.0, "Cost per lead": 666.6666667, "Conversions": 6, "# of Payments": 9.0, "$ amount": 2035.0, "Pipeline": 3.0, "COCA": 10000.0, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Sep Week 4", "Marketing spent (in INR)": 20000.0, "Total Leads": 32.0, "Cost per lead": 625.0, "Conversions": 2, "# of Payments": 9.0, "$ amount": 2425.0, "Pipeline": 5.0, "COCA": 10000.0, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Sep Total", "Marketing spent (in INR)": 220000.0, "Total Leads": 316.0, "Cost per lead": 696.2025316, "Conversions": 23, "# of Payments": 30.0, "$ amount": 6032.0, "Pipeline": NaN, "COCA": NaN, "Monthly COCA": 7333.333333, "Monthly rev": 6032, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": NaN, "Marketing spent (in INR)": NaN, "Total Leads": NaN, "Cost per lead": NaN, "Conversions": NaN, "# of Payments": NaN, "$ amount": NaN, "Pipeline": NaN, "COCA": NaN, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Oct Week 1", "Marketing spent (in INR)": 30000.0, "Total Leads": 21.0, "Cost per lead": 1428.571429, "Conversions": 0, "# of Payments": 0.0, "$ amount": 0.0, "Pipeline": 5.0, "COCA": NaN, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}, {"Unnamed: 0": "Oct Week 2", "Marketing spent (in INR)": 25000.0, "Total Leads": 46.0, "Cost per lead": 543.4782609, "Conversions": 3, "# of Payments": 3.0, "$ amount": 790.0, "Pipeline": 15.0, "COCA": 8333.333333, "Monthly COCA": NaN, "Monthly rev": NaN, "Direct:Referrals:Alumnis": NaN}]}, "Linkedin": {"rows": 2, "columns": 4, "column_names": ["Month", "Total Leads", "No.Of. Conversions", "Amount"], "data_types": {"Month": "object", "Total Leads": "float64", "No.Of. Conversions": "float64", "Amount": "float64"}, "preview": [{"Month": NaN, "Total Leads": NaN, "No.Of. Conversions": NaN, "Amount": NaN}, {"Month": "March", "Total Leads": 12.0, "No.Of. Conversions": 2.0, "Amount": 460.0}]}}, "total_sheets": 20, "mapping_suggestions": {"potential_leads": [{"sheet": "Dashboard", "columns": ["Unnamed: 0", "Leads", "Bought", "Revenues", "MKT Spent", "CPL", "Conv", "COCA"], "confidence": "medium"}, {"sheet": "Marketing", "columns": ["Date", "Channel", "Spent", "Spent (Dollar)", "Payment Method", "Unnamed: 5", "Unnamed: 6", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "confidence": "medium"}, {"sheet": "Bootcamp codes", "columns": ["Unnamed: 0", "US", "India"], "confidence": "medium"}, {"sheet": "India Leads", "columns": ["Contact Code", "Date", "Name", "Email", "Phone", "Status", "Purchase", "Python Exp", "Profession", "Company", "Designation", "LinkedIn Profile", "Notes", "Reach Out Count", "Last Contact Date", "Next Action"], "confidence": "high"}, {"sheet": "Email", "columns": ["Name", "Email"], "confidence": "high"}, {"sheet": "New Leads", "columns": ["Contact Code", "Date", "Channel", "Name", "Email", "Phone", "Status", "Purchase", "Python Exp", "How to contact?", "Profession", "Company", "Designation", "LinkedIn Profile", "Notes", "Reach Out Count", "Last Contact Date", "Next Action"], "confidence": "high"}, {"sheet": "L1 Aug 15-17", "columns": ["Lead Date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11"], "confidence": "high"}, {"sheet": "L2 Sep 5-7", "columns": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8"], "confidence": "high"}, {"sheet": "A1 Oct", "columns": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "confidence": "high"}, {"sheet": "V1 Nov", "columns": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9"], "confidence": "high"}, {"sheet": "V1 June13-15", "columns": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Next batch"], "confidence": "high"}, {"sheet": "A1 May 10&11", "columns": ["Customer ID", "Lead Date", "Purchase Date", "Name", "Phone", "Email", "Source", "Amount", "Product", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11"], "confidence": "high"}, {"sheet": "L1 June7&8", "columns": ["Lead Date", "Purchase Date", "Name", "Phone", "Email", "Source", "Amount", "Contact Code", "Unnamed: 8", "AA438", "Payment link", "Next Batch"], "confidence": "high"}, {"sheet": "V1 April 19&20", "columns": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "confidence": "high"}, {"sheet": "L1 April5&6", "columns": ["Lead Date", "Purchase Date", "Name", "Phone", "Email", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9"], "confidence": "high"}, {"sheet": "Old Lead Sheet", "columns": ["Date", "Bought or not?", "Name", "Phone", "Email", "Python Exp", "How to contact?", "Designation", "Notes"], "confidence": "high"}, {"sheet": "Week Report", "columns": ["Unnamed: 0", "Marketing spent (in INR)", "Total Leads", "Cost per lead", "Conversions", "# of Payments", "$ amount", "Pipeline", "COCA", "Monthly COCA", "Monthly rev", "Direct:Referrals:Alumnis"], "confidence": "medium"}, {"sheet": "Linkedin", "columns": ["Month", "Total Leads", "No.Of. Conversions", "Amount"], "confidence": "medium"}], "potential_workshops": [{"sheet": "V1 June13-15", "columns": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Next batch"], "confidence": "high"}, {"sheet": "L1 June7&8", "columns": ["Lead Date", "Purchase Date", "Name", "Phone", "Email", "Source", "Amount", "Contact Code", "Unnamed: 8", "AA438", "Payment link", "Next Batch"], "confidence": "high"}], "potential_payments": [{"sheet": "Dashboard", "columns": ["Unnamed: 0", "Leads", "Bought", "Revenues", "MKT Spent", "CPL", "Conv", "COCA"], "confidence": "medium"}, {"sheet": "Marketing", "columns": ["Date", "Channel", "Spent", "Spent (Dollar)", "Payment Method", "Unnamed: 5", "Unnamed: 6", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "confidence": "high"}, {"sheet": "L1 Aug 15-17", "columns": ["Lead Date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11"], "confidence": "high"}, {"sheet": "L2 Sep 5-7", "columns": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8"], "confidence": "high"}, {"sheet": "A1 Oct", "columns": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "confidence": "high"}, {"sheet": "V1 Nov", "columns": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9"], "confidence": "high"}, {"sheet": "V1 June13-15", "columns": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Next batch"], "confidence": "high"}, {"sheet": "A1 May 10&11", "columns": ["Customer ID", "Lead Date", "Purchase Date", "Name", "Phone", "Email", "Source", "Amount", "Product", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11"], "confidence": "high"}, {"sheet": "L1 June7&8", "columns": ["Lead Date", "Purchase Date", "Name", "Phone", "Email", "Source", "Amount", "Contact Code", "Unnamed: 8", "AA438", "Payment link", "Next Batch"], "confidence": "high"}, {"sheet": "V1 April 19&20", "columns": ["Lead date", "Purchase Date", "Name", "Email", "Phone", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "confidence": "high"}, {"sheet": "L1 April5&6", "columns": ["Lead Date", "Purchase Date", "Name", "Phone", "Email", "Source", "Amount", "Contact Code", "Unnamed: 8", "Unnamed: 9"], "confidence": "high"}, {"sheet": "Week Report", "columns": ["Unnamed: 0", "Marketing spent (in INR)", "Total Leads", "Cost per lead", "Conversions", "# of Payments", "$ amount", "Pipeline", "COCA", "Monthly COCA", "Monthly rev", "Direct:Referrals:Alumnis"], "confidence": "high"}, {"sheet": "Linkedin", "columns": ["Month", "Total Leads", "No.Of. Conversions", "Amount"], "confidence": "high"}], "workshop_enrollments": []}}, "All Enrollees.xlsx": {"file_name": "All Enrollees.xlsx", "sheets": {"Dates": {"error": "sequence item 1: expected str instance, datetime.datetime found"}, "BC01": {"rows": 122, "columns": 2, "column_names": ["A<PERSON>thyan US", "<EMAIL>"], "data_types": {"Aadithyan US": "object", "<EMAIL>": "object"}, "preview": [{"Aadithyan US": "Aarsha JS", "<EMAIL>": "<EMAIL>"}, {"Aadithyan US": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Aadithyan US": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Aadithyan US": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Aadithyan US": "<PERSON><PERSON><PERSON>", "<EMAIL>": "balaji.venka<PERSON><EMAIL>"}, {"Aadithyan US": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Aadithyan US": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "bm<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"Aadithyan US": "<PERSON><PERSON><PERSON><PERSON> J", "<EMAIL>": "guru<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"Aadithyan US": "<PERSON>", "<EMAIL>": "<EMAIL>"}, {"Aadithyan US": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL> "}]}, "BC02": {"rows": 43, "columns": 2, "column_names": ["Name", "Email"], "data_types": {"Name": "object", "Email": "object"}, "preview": [{"Name": "A<PERSON>thyan US", "Email": "<EMAIL>"}, {"Name": "Aarsha JS", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email": "v<PERSON><PERSON><PERSON><PERSON>@hotmail.com"}, {"Name": "k<PERSON>hna raghav", "Email": "k<PERSON><PERSON><PERSON><PERSON><EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "Sathwik A", "Email": "<EMAIL>"}, {"Name": "Rajavale ", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON> ", "Email": "shri.seen<PERSON><PERSON>@gmail.com"}, {"Name": "<PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON>ry copps", "Email": "<EMAIL>"}]}, "BC03": {"rows": 31, "columns": 2, "column_names": ["<PERSON><PERSON><PERSON>", "<EMAIL>"], "data_types": {"Muthuswamy": "object", "<EMAIL>": "object"}, "preview": [{"Muthuswamy": "Varun", "<EMAIL>": "<EMAIL>"}, {"Muthuswamy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"Muthuswamy": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Muthuswamy": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Muthuswamy": "<PERSON><PERSON><PERSON>", "<EMAIL>": "tekulam<PERSON><EMAIL>"}, {"Muthuswamy": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Muthuswamy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "saheb<PERSON><PERSON>@gmail.com"}, {"Muthuswamy": "Rajavale", "<EMAIL>": "<EMAIL>"}, {"Muthuswamy": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Muthuswamy": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "BC04": {"rows": 35, "columns": 2, "column_names": ["<PERSON><PERSON>", "<EMAIL>"], "data_types": {"Nagaraj Dhandapani": "object", "<EMAIL>": "object"}, "preview": [{"Nagaraj Dhandapani": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON>", "<EMAIL>": "<PERSON><PERSON>.u<PERSON><PERSON>@outlook.com"}, {"Nagaraj Dhandapani": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "Venkat", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "PREETI SINHA", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "BC05": {"rows": 36, "columns": 2, "column_names": ["<PERSON><PERSON>", "<EMAIL>"], "data_types": {"Adi": "object", "<EMAIL>": "object"}, "preview": [{"Adi": "<PERSON>", "<EMAIL>": "<EMAIL>"}, {"Adi": "Anant", "<EMAIL>": "<EMAIL>"}, {"Adi": "Anil", "<EMAIL>": "<EMAIL>"}, {"Adi": "Ankit", "<EMAIL>": "<EMAIL>"}, {"Adi": "<PERSON>", "<EMAIL>": "<EMAIL>"}, {"Adi": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Adi": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>\n"}, {"Adi": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "bharga<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"Adi": "Chaitanya", "<EMAIL>": "<EMAIL>"}, {"Adi": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "BC06": {"rows": 34, "columns": 2, "column_names": ["Ra<PERSON><PERSON>", "<EMAIL>"], "data_types": {"Rakesh": "object", "<EMAIL>": "object"}, "preview": [{"Rakesh": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rakesh": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rakesh": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rakesh": "Vinay_<PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rakesh": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rakesh": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rakesh": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"Rakesh": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Rakesh": "Bodapati Trinadh Baba", "<EMAIL>": "<EMAIL>"}, {"Rakesh": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "BC07": {"rows": 39, "columns": 2, "column_names": ["Vinoth", "<EMAIL>"], "data_types": {"Vinoth": "object", "<EMAIL>": "object"}, "preview": [{"Vinoth": "a<PERSON><PERSON><PERSON><PERSON> thakur", "<EMAIL>": "<EMAIL>"}, {"Vinoth": "Gokul Nk", "<EMAIL>": "<EMAIL>"}, {"Vinoth": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Vinoth": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Vinoth": "Ravindhar", "<EMAIL>": "<EMAIL>"}, {"Vinoth": "<PERSON><PERSON><PERSON>", "<EMAIL>": "shravan<PERSON><EMAIL>"}, {"Vinoth": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Vinoth": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Vinoth": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Vinoth": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "BC08": {"rows": 30, "columns": 2, "column_names": ["<PERSON><PERSON><PERSON>", "<EMAIL>"], "data_types": {"Phaneendra S": "object", "<EMAIL>": "object"}, "preview": [{"Phaneendra S": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Phaneendra S": "Venkata Rama<PERSON><PERSON><PERSON>", "<EMAIL>": "ramak<PERSON><EMAIL>"}, {"Phaneendra S": "<PERSON><PERSON> ka<PERSON>", "<EMAIL>": "<EMAIL>"}, {"Phaneendra S": "<PERSON>", "<EMAIL>": "<EMAIL>"}, {"Phaneendra S": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Phaneendra S": "Sumit", "<EMAIL>": "<EMAIL>"}, {"Phaneendra S": "<PERSON><PERSON><PERSON>", "<EMAIL>": "gayat<PERSON>balaba<PERSON><EMAIL>"}, {"Phaneendra S": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Phaneendra S": "DIVYANSH SHIVHARE", "<EMAIL>": "<EMAIL>"}, {"Phaneendra S": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "BC09": {"rows": 52, "columns": 2, "column_names": ["<PERSON><PERSON>", "<EMAIL>"], "data_types": {"Salah": "object", "<EMAIL>": "object"}, "preview": [{"Salah": "<PERSON><PERSON>", "<EMAIL>": "b.<PERSON><PERSON><PERSON><PERSON>@icloud.com"}, {"Salah": "Paras", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "Si<PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "Xavier", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "BC10": {"rows": 16, "columns": 2, "column_names": ["<PERSON><PERSON>sh G.", "<EMAIL>"], "data_types": {"Avinash G.": "object", "<EMAIL>": "object"}, "preview": [{"Avinash G.": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Avinash G.": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Avinash G.": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Avinash G.": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Avinash G.": "<PERSON><PERSON><PERSON> G<PERSON>", "<EMAIL>": "sukha<PERSON>gh<PERSON><EMAIL>"}, {"Avinash G.": "<PERSON>", "<EMAIL>": "<EMAIL>"}, {"Avinash G.": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Avinash G.": "Amol G. K.", "<EMAIL>": "<EMAIL>"}, {"Avinash G.": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Avinash G.": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "BC11": {"rows": 52, "columns": 2, "column_names": ["<PERSON><PERSON>", "<EMAIL>"], "data_types": {"Salah": "object", "<EMAIL>": "object"}, "preview": [{"Salah": "<PERSON><PERSON>", "<EMAIL>": "b.<PERSON><PERSON><PERSON><PERSON>@icloud.com"}, {"Salah": "Paras", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "Si<PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Salah": "Xavier", "<EMAIL>": "<EMAIL>"}, {"Salah": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "BC12": {"rows": 129, "columns": 2, "column_names": ["<PERSON><PERSON><PERSON>", "<EMAIL>"], "data_types": {"Senthil Kumar": "object", "<EMAIL>": "object"}, "preview": [{"Senthil Kumar": "Sadat", "<EMAIL>": "<EMAIL>"}, {"Senthil Kumar": "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Senthil Kumar": "<PERSON>", "<EMAIL>": "<EMAIL>"}, {"Senthil Kumar": "<PERSON>", "<EMAIL>": "<PERSON>@okahu.ai"}, {"Senthil Kumar": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Senthil Kumar": "<PERSON><PERSON><PERSON>", "<EMAIL>": "raj<PERSON><EMAIL>"}, {"Senthil Kumar": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Senthil Kumar": "Gururaja U N", "<EMAIL>": "guru<PERSON><PERSON><PERSON>@gmail.com"}, {"Senthil Kumar": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Senthil Kumar": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "Manish": {"rows": 629, "columns": 2, "column_names": ["<PERSON><PERSON>", "<EMAIL>"], "data_types": {"Nagaraj Dhandapani": "object", "<EMAIL>": "object"}, "preview": [{"Nagaraj Dhandapani": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON>", "<EMAIL>": "<PERSON><PERSON>.u<PERSON><PERSON>@outlook.com"}, {"Nagaraj Dhandapani": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "Venkat", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "PREETI SINHA", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}, {"Nagaraj Dhandapani": "<PERSON><PERSON><PERSON>", "<EMAIL>": "<EMAIL>"}]}, "Assorted without dates": {"rows": 429, "columns": 2, "column_names": ["Name", "Email"], "data_types": {"Name": "object", "Email": "object"}, "preview": [{"Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON>", "Email": "<PERSON><PERSON>@theproductartisan.com"}, {"Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "Venkat", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON>", "Email": "b.<PERSON><PERSON><PERSON><PERSON>@icloud.com"}, {"Name": "<PERSON><PERSON>", "Email": "<EMAIL>"}, {"Name": "<PERSON><PERSON>", "Email": "<EMAIL>"}]}}, "total_sheets": 15, "mapping_suggestions": {"potential_leads": [{"sheet": "BC02", "columns": ["Name", "Email"], "confidence": "high"}, {"sheet": "Assorted without dates", "columns": ["Name", "Email"], "confidence": "high"}], "potential_workshops": [], "potential_payments": [], "workshop_enrollments": []}}, "Mitra B2C Edtech Sales.xlsx": {"file_name": "Mitra B2C Edtech Sales.xlsx", "sheets": {"MAP -18": {"rows": 45, "columns": 20, "column_names": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "Source", "DATE OF PAY", "More details", "Unnamed: 11", "Unnamed: 12", "PAID IN JULY MONTH", "Unnamed: 14", "Unnamed: 15", "Unnamed: 16", "Unnamed: 17", "Unnamed: 18", "Unnamed: 19"], "data_types": {"Name": "object", "Email ": "object", "Unnamed: 2": "float64", "Amount": "object", "Status": "object", "Closer": "object", "Level": "object", "Phone": "object", "Source": "object", "DATE OF PAY": "object", "More details": "object", "Unnamed: 11": "float64", "Unnamed: 12": "object", "PAID IN JULY MONTH": "object", "Unnamed: 14": "float64", "Unnamed: 15": "float64", "Unnamed: 16": "object", "Unnamed: 17": "object", "Unnamed: 18": "object", "Unnamed: 19": "float64"}, "preview": [{"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 15000, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 9847825202, "Source": NaN, "DATE OF PAY": NaN, "More details": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN, "PAID IN JULY MONTH": NaN, "Unnamed: 14": NaN, "Unnamed: 15": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN}, {"Name": "<PERSON><PERSON><PERSON> ", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10500, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 9866690870, "Source": NaN, "DATE OF PAY": NaN, "More details": NaN, "Unnamed: 11": NaN, "Unnamed: 12": 1, "PAID IN JULY MONTH": "Gokul ", "Unnamed: 14": NaN, "Unnamed: 15": NaN, "Unnamed: 16": "Paid", "Unnamed: 17": "M", "Unnamed: 18": "Essentials", "Unnamed: 19": 9500422211.0}, {"Name": "<PERSON><PERSON> ", "Email ": NaN, "Unnamed: 2": NaN, "Amount": 10000, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 9844093744, "Source": NaN, "DATE OF PAY": NaN, "More details": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN, "PAID IN JULY MONTH": NaN, "Unnamed: 14": NaN, "Unnamed: 15": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 9687620503, "Source": NaN, "DATE OF PAY": NaN, "More details": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN, "PAID IN JULY MONTH": NaN, "Unnamed: 14": NaN, "Unnamed: 15": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "ma<PERSON><PERSON><PERSON><PERSON><EMAIL>", "Unnamed: 2": NaN, "Amount": 12000, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 9972268878, "Source": NaN, "DATE OF PAY": NaN, "More details": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<PERSON> <PERSON> <PERSON><PERSON>", "PAID IN JULY MONTH": NaN, "Unnamed: 14": NaN, "Unnamed: 15": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 9008523478, "Source": NaN, "DATE OF PAY": NaN, "More details": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "TS- <PERSON><PERSON><PERSON>", "PAID IN JULY MONTH": NaN, "Unnamed: 14": NaN, "Unnamed: 15": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN}, {"Name": "Selvam V", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 12000, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 8925655504, "Source": NaN, "DATE OF PAY": NaN, "More details": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "S- Sanyu", "PAID IN JULY MONTH": NaN, "Unnamed: 14": NaN, "Unnamed: 15": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN}, {"Name": "<PERSON><PERSON> ", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10800, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 9920908233, "Source": NaN, "DATE OF PAY": NaN, "More details": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN, "PAID IN JULY MONTH": NaN, "Unnamed: 14": NaN, "Unnamed: 15": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "ATTEND SEPT BATCH", "Unnamed: 2": NaN, "Amount": 4500, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 93544 09662‬", "Source": "Paid", "DATE OF PAY": "1st Aug", "More details": "Advance", "Unnamed: 11": NaN, "Unnamed: 12": NaN, "PAID IN JULY MONTH": NaN, "Unnamed: 14": NaN, "Unnamed: 15": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN}, {"Name": "nitin", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 12750, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": NaN, "Source": "ATTEND SEPT BATCH", "DATE OF PAY": NaN, "More details": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN, "PAID IN JULY MONTH": NaN, "Unnamed: 14": NaN, "Unnamed: 15": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN}]}, "MAP - Prac 2": {"rows": 26, "columns": 21, "column_names": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "Source", "Unnamed: 9", "More details", "Unnamed: 11", "PAID IN JULY AND AUG ", "Unnamed: 13", "Unnamed: 14", "Unnamed: 15", "Unnamed: 16", "Unnamed: 17", "Unnamed: 18", "Unnamed: 19", "Unnamed: 20"], "data_types": {"Name": "object", "Email ": "object", "Unnamed: 2": "float64", "Amount": "float64", "Status": "object", "Closer": "object", "Level": "object", "Phone": "object", "Source": "object", "Unnamed: 9": "object", "More details": "float64", "Unnamed: 11": "float64", "PAID IN JULY AND AUG ": "object", "Unnamed: 13": "float64", "Unnamed: 14": "float64", "Unnamed: 15": "object", "Unnamed: 16": "object", "Unnamed: 17": "object", "Unnamed: 18": "object", "Unnamed: 19": "object", "Unnamed: 20": "object"}, "preview": [{"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 15000.0, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+1 (314) 603‑4591‬", "Source": NaN, "Unnamed: 9": NaN, "More details": NaN, "Unnamed: 11": NaN, "PAID IN JULY AND AUG ": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Unnamed: 15": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Unnamed: 20": NaN}, {"Name": "Ka<PERSON><PERSON>ya ", "Email ": "ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "Unnamed: 2": NaN, "Amount": 11250.0, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": NaN, "Source": "6th Aug", "Unnamed: 9": NaN, "More details": NaN, "Unnamed: 11": 1.0, "PAID IN JULY AND AUG ": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": 10300.0, "Unnamed: 15": "Paid", "Unnamed: 16": "M", "Unnamed: 17": "<PERSON><PERSON><PERSON><PERSON>", "Unnamed: 18": "‪+91 99866 15267‬", "Unnamed: 19": "Balaji GPAY", "Unnamed: 20": NaN}, {"Name": "sunil", "Email ": NaN, "Unnamed: 2": NaN, "Amount": 11250.0, "Status": "Paid", "Closer": "S", "Level": "L2", "Phone": NaN, "Source": NaN, "Unnamed: 9": NaN, "More details": NaN, "Unnamed: 11": 2.0, "PAID IN JULY AND AUG ": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": 12000.0, "Unnamed: 15": "Paid", "Unnamed: 16": "M", "Unnamed: 17": "<PERSON><PERSON><PERSON><PERSON>", "Unnamed: 18": "‪+91 98453 69005‬", "Unnamed: 19": "GPAY", "Unnamed: 20": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "ajak<PERSON><PERSON><PERSON><PERSON>@gmail.com", "Unnamed: 2": NaN, "Amount": 11249.0, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 78381 52566‬", "Source": NaN, "Unnamed: 9": NaN, "More details": NaN, "Unnamed: 11": 3.0, "PAID IN JULY AND AUG ": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": 12000.0, "Unnamed: 15": "Paid", "Unnamed: 16": "M", "Unnamed: 17": "<PERSON><PERSON><PERSON><PERSON>", "Unnamed: 18": "‪+91 98453 69005‬", "Unnamed: 19": "GPAY", "Unnamed: 20": NaN}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249.0, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 77022 19444‬", "Source": NaN, "Unnamed: 9": NaN, "More details": NaN, "Unnamed: 11": 4.0, "PAID IN JULY AND AUG ": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": 11249.0, "Unnamed: 15": "Paid", "Unnamed: 16": "M", "Unnamed: 17": "<PERSON><PERSON><PERSON><PERSON>", "Unnamed: 18": "‪+91 96860 55801‬", "Unnamed: 19": "STRIPE", "Unnamed: 20": NaN}, {"Name": "AJITH", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249.0, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 97318 06688‬", "Source": NaN, "Unnamed: 9": NaN, "More details": NaN, "Unnamed: 11": 5.0, "PAID IN JULY AND AUG ": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": 11249.0, "Unnamed: 15": "Paid", "Unnamed: 16": "M", "Unnamed: 17": "<PERSON><PERSON><PERSON><PERSON>", "Unnamed: 18": "‪+91 99447 08693‬", "Unnamed: 19": "GPAY", "Unnamed: 20": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249.0, "Status": "paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 6363 200 579‬", "Source": NaN, "Unnamed: 9": NaN, "More details": NaN, "Unnamed: 11": 6.0, "PAID IN JULY AND AUG ": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": 10500.0, "Unnamed: 15": "Paid", "Unnamed: 16": "M", "Unnamed: 17": "<PERSON><PERSON><PERSON><PERSON>", "Unnamed: 18": "‪+91 97890 48374‬", "Unnamed: 19": "GPAY", "Unnamed: 20": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 12749.0, "Status": "Paid", "Closer": NaN, "Level": "L1", "Phone": NaN, "Source": NaN, "Unnamed: 9": NaN, "More details": NaN, "Unnamed: 11": 7.0, "PAID IN JULY AND AUG ": "<EMAIL> ", "Unnamed: 13": NaN, "Unnamed: 14": 10000.0, "Unnamed: 15": "PAID", "Unnamed: 16": "M", "Unnamed: 17": "<PERSON><PERSON><PERSON><PERSON>", "Unnamed: 18": "‪+91 83485 75432‬", "Unnamed: 19": "GPAY", "Unnamed: 20": NaN}, {"Name": "<PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249.0, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+971 50 613 2100‬", "Source": NaN, "Unnamed: 9": NaN, "More details": NaN, "Unnamed: 11": 8.0, "PAID IN JULY AND AUG ": "<EMAIL> ", "Unnamed: 13": NaN, "Unnamed: 14": 11249.0, "Unnamed: 15": "PAID", "Unnamed: 16": "M", "Unnamed: 17": "<PERSON><PERSON><PERSON><PERSON>", "Unnamed: 18": "‪+91 76739 83232‬", "Unnamed: 19": "Stripe", "Unnamed: 20": NaN}, {"Name": "Shubam", "Email ": "<EMAIL> ", "Unnamed: 2": NaN, "Amount": 12000.0, "Status": "Paid", "Closer": "M/San", "Level": "L2", "Phone": "‪+91 72781 85335‬", "Source": "Stripe", "Unnamed: 9": NaN, "More details": NaN, "Unnamed: 11": 9.0, "PAID IN JULY AND AUG ": "<PERSON><PERSON><PERSON><PERSON>@gmail.com", "Unnamed: 13": NaN, "Unnamed: 14": 10500.0, "Unnamed: 15": "PAID", "Unnamed: 16": "M", "Unnamed: 17": "<PERSON><PERSON><PERSON><PERSON>", "Unnamed: 18": "‪+91 99878 27127‬", "Unnamed: 19": "GPAY", "Unnamed: 20": NaN}]}, "MAP -19": {"rows": 62, "columns": 12, "column_names": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "Source", "DATE OF PAY", "Unnamed: 10", "Unnamed: 11"], "data_types": {"Name": "object", "Email ": "object", "Unnamed: 2": "float64", "Amount": "float64", "Status": "object", "Closer": "object", "Level": "object", "Phone": "object", "Source": "object", "DATE OF PAY": "object", "Unnamed: 10": "object", "Unnamed: 11": "object"}, "preview": [{"Name": "<PERSON><PERSON> G.S<PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 14999.0, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 9847825202, "Source": NaN, "DATE OF PAY": "OLD BATCH ", "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Name": "Sai<PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11999.0, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 9620147420, "Source": NaN, "DATE OF PAY": "OLD BATCH ", "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Name": "<PERSON><PERSON> ", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10000.0, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 9844093744, "Source": NaN, "DATE OF PAY": "OLD BATCH ", "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Name": "<PERSON>", "Email ": "<PERSON>.<EMAIL>", "Unnamed: 2": NaN, "Amount": 9500.0, "Status": "Paid", "Closer": "S", "Level": "L1", "Phone": 7358909078, "Source": NaN, "DATE OF PAY": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249.0, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "99678 71021", "Source": NaN, "DATE OF PAY": "OLD BATCH ", "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Name": "<PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249.0, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 80990 35353‬", "Source": NaN, "DATE OF PAY": "OLD BATCH ", "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10300.0, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 80505 56839‬", "Source": NaN, "DATE OF PAY": "OLD BATCH ", "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<PERSON><PERSON><PERSON><PERSON><PERSON><EMAIL> ", "Unnamed: 2": NaN, "Amount": 11249.0, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 81305 94167‬", "Source": NaN, "DATE OF PAY": "OLD BATCH ", "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Name": "<PERSON><PERSON><PERSON> ", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10500.0, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 96635 99918‬", "Source": NaN, "DATE OF PAY": "OLD BATCH ", "Unnamed: 10": NaN, "Unnamed: 11": NaN}, {"Name": "prashant ", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10000.0, "Status": "Paid", "Closer": "<PERSON><PERSON>", "Level": "L1", "Phone": 9900013537, "Source": NaN, "DATE OF PAY": "OLD BATCH ", "Unnamed: 10": NaN, "Unnamed: 11": NaN}]}, "MAP - Prac 3": {"rows": 30, "columns": 15, "column_names": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9", "Unnamed: 10", "Previous ones", "Unnamed: 12", "Unnamed: 13", "Unnamed: 14"], "data_types": {"Name": "object", "Email ": "object", "Unnamed: 2": "float64", "Amount": "int64", "Status": "object", "Closer": "object", "Level": "object", "Phone": "object", "DATE OF PAY": "object", "Unnamed: 9": "object", "Unnamed: 10": "float64", "Previous ones": "object", "Unnamed: 12": "object", "Unnamed: 13": "object", "Unnamed: 14": "object"}, "preview": [{"Name": "Chetan <PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 12000, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 70213 51141‬", "DATE OF PAY": "23d Sept", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Previous ones": "VSS K <PERSON> ", "Unnamed: 12": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "Unnamed: 13": NaN, "Unnamed: 14": 9916683099}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 98480 02555‬", "DATE OF PAY": "24th Sept", "Unnamed: 9": "DEC BATCH", "Unnamed: 10": NaN, "Previous ones": "<PERSON><PERSON>", "Unnamed: 12": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": 9027184519}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 94900 05588‬", "DATE OF PAY": "25th Sept", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Previous ones": "<PERSON><PERSON><PERSON>", "Unnamed: 12": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": "‪+91 88843 71110‬"}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 12000, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 96770 22350‬", "DATE OF PAY": "25th Sept", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Previous ones": "<PERSON><PERSON><PERSON>", "Unnamed: 12": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": "‪+91 77602 18333‬"}, {"Name": "<PERSON><PERSON><PERSON><PERSON> Sastry <PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+60 14‑989 9615‬", "DATE OF PAY": "26th Sept", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Previous ones": NaN, "Unnamed: 12": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "Sai Kishore Komanduri ", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 90004 84166‬", "DATE OF PAY": "26th Sept", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Previous ones": "<PERSON>ni. <EMAIL>", "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": "‪+91 89761 02865‬"}, {"Name": "<PERSON><PERSON> murugan <PERSON>", "Email ": "<EMAIL> ", "Unnamed: 2": NaN, "Amount": 12000, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 97154 49095‬", "DATE OF PAY": "9th Oct", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Previous ones": "<PERSON><PERSON><PERSON><PERSON>", "Unnamed: 12": "<EMAIL> ", "Unnamed: 13": NaN, "Unnamed: 14": "‪+91 99457 40095‬"}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10300, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 73063 29704‬", "DATE OF PAY": "10th Oct", "Unnamed: 9": "DEC BATCH", "Unnamed: 10": NaN, "Previous ones": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L2", "Phone": "‪+91 83691 21905‬", "DATE OF PAY": "14th Oct", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Previous ones": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 12000, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 98453 67892‬", "DATE OF PAY": "15th Oct", "Unnamed: 9": "DEC BATCH", "Unnamed: 10": NaN, "Previous ones": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN}]}, "MAP - L1 NOV": {"rows": 26, "columns": 15, "column_names": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12", "Unnamed: 13", "Unnamed: 14"], "data_types": {"Name": "object", "Email ": "object", "Unnamed: 2": "float64", "Amount": "int64", "Status": "object", "Closer": "object", "Level": "object", "Phone": "object", "DATE OF PAY": "object", "Unnamed: 9": "object", "Unnamed: 10": "float64", "Unnamed: 11": "object", "Unnamed: 12": "object", "Unnamed: 13": "float64", "Unnamed: 14": "object"}, "preview": [{"Name": "<PERSON><PERSON><PERSON> ", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 98730 19743‬", "DATE OF PAY": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "<PERSON><PERSON>sh <PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10500, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 97175 83903‬", "DATE OF PAY": "30 Sept", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 90490 99888‬", "DATE OF PAY": "23d Sept", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "<EMAIL>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 99401 63736‬", "DATE OF PAY": "14th Oct", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 88829 99051‬", "DATE OF PAY": "23rd Oct", "Unnamed: 9": "2000 Advance 9249 Balance", "Unnamed: 10": NaN, "Unnamed: 11": "<PERSON><PERSON>", "Unnamed: 12": "GIVE HIM NEW DATES", "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10500, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 98946 29670‬", "DATE OF PAY": "23rd Oct", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Email ": "t.vivekan<PERSON>@gmail.com", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 96633 11829‬", "DATE OF PAY": "23rd Oct", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "<PERSON><PERSON><PERSON><PERSON> Sastry <PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+60 14‑989 9615‬", "DATE OF PAY": "26th Sept", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "OldBatch", "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10300, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 73063 29704‬", "DATE OF PAY": "10th Oct", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 99008 76429‬", "DATE OF PAY": "28th Oct", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>", "Unnamed: 13": NaN, "Unnamed: 14": "‪+91 98851 00030‬"}]}, "Y Pro": {"rows": 999, "columns": 10, "column_names": ["Name", "Email ", "<PERSON><PERSON><PERSON>", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9"], "data_types": {"Name": "object", "Email ": "object", "Gardian": "object", "Amount": "float64", "Status": "object", "Closer": "object", "Level": "object", "Phone": "object", "DATE OF PAY": "object", "Unnamed: 9": "object"}, "preview": [{"Name": "<PERSON><PERSON>", "Email ": "saheb<PERSON><PERSON>@gmail.com", "Gardian": NaN, "Amount": 13500.0, "Status": "Paid", "Closer": "M", "Level": "YPRO", "Phone": "‪+91 96201 48763‬", "DATE OF PAY": "12th Nov", "Unnamed: 9": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Gardian": NaN, "Amount": 9500.0, "Status": "Paid", "Closer": "M", "Level": "YPRO", "Phone": "‪+91 98303 58655‬", "DATE OF PAY": "13th Nov", "Unnamed: 9": "MARCH BATCH"}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Gardian": NaN, "Amount": 13500.0, "Status": "Paid", "Closer": "M", "Level": "YPRO", "Phone": "‪+91 96546 47766‬", "DATE OF PAY": "2024-11-26 00:00:00", "Unnamed: 9": NaN}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Gardian": NaN, "Amount": 11249.0, "Status": "Paid", "Closer": "M", "Level": "YPRO", "Phone": "‪+91 80179 90740‬", "DATE OF PAY": "25th Oct", "Unnamed: 9": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Gardian": NaN, "Amount": 10300.0, "Status": "Paid", "Closer": "M", "Level": "YPRO", "Phone": "‪+91 88976 62685‬", "DATE OF PAY": "30th Nov", "Unnamed: 9": "2000 Advance"}, {"Name": "Preetish K. Dash", "Email ": "<EMAIL>", "Gardian": NaN, "Amount": 10500.0, "Status": "Paid", "Closer": "M", "Level": "L0", "Phone": "‪+91 97765 35859‬", "DATE OF PAY": "Dec 2nd", "Unnamed: 9": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "s<PERSON><PERSON><PERSON><PERSON>@gmail.com", "Gardian": NaN, "Amount": 10700.0, "Status": "Paid", "Closer": "M", "Level": "L0", "Phone": "‪+91 97765 35859‬", "DATE OF PAY": "Dec 2nd", "Unnamed: 9": NaN}, {"Name": " <PERSON><PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Gardian": "<EMAIL>", "Amount": 10700.0, "Status": "Paid", "Closer": "M", "Level": "L0", "Phone": "‪+91 99454 43505‬", "DATE OF PAY": "Dec 11th", "Unnamed: 9": NaN}, {"Name": "Trinadh B", "Email ": "<EMAIL>", "Gardian": NaN, "Amount": 13500.0, "Status": "Paid", "Closer": "M", "Level": "L0", "Phone": "‪+91 94921 09119‬", "DATE OF PAY": "14th Nov", "Unnamed: 9": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Gardian": NaN, "Amount": NaN, "Status": NaN, "Closer": NaN, "Level": NaN, "Phone": 4084725631, "DATE OF PAY": NaN, "Unnamed: 9": NaN}]}, "Agentic Jan L1": {"rows": 40, "columns": 9, "column_names": ["Name", "Email ", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 8"], "data_types": {"Name": "object", "Email ": "object", "Amount": "int64", "Status": "object", "Closer": "object", "Level": "object", "Phone": "object", "DATE OF PAY": "object", "Unnamed: 8": "object"}, "preview": [{"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 12000, "Status": "Paid", "Closer": "S", "Level": "Agents L1", "Phone": NaN, "DATE OF PAY": "2024-12-10 00:00:00", "Unnamed: 8": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 12749, "Status": "Paid", "Closer": "S", "Level": "Agents L1", "Phone": NaN, "DATE OF PAY": "2024-12-11 00:00:00", "Unnamed: 8": NaN}, {"Name": "A <PERSON> S <PERSON>", "Email ": "<EMAIL>", "Amount": 12749, "Status": "Paid", "Closer": "S", "Level": "Agents L1", "Phone": NaN, "DATE OF PAY": "2024-12-11 00:00:00", "Unnamed: 8": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 12749, "Status": "Paid", "Closer": "S", "Level": "Agents L1", "Phone": 9885001554, "DATE OF PAY": "2024-12-12 00:00:00", "Unnamed: 8": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 12500, "Status": "Paid", "Closer": "S", "Level": "Agents L1", "Phone": 9985290108, "DATE OF PAY": "2025-01-02 00:00:00", "Unnamed: 8": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 12749, "Status": "Paid", "Closer": "S", "Level": "Agents L1", "Phone": 9899759127, "DATE OF PAY": "2024-12-24 00:00:00", "Unnamed: 8": NaN}, {"Name": "Venkatasubramanian S ", "Email ": "<EMAIL>", "Amount": 12500, "Status": "Paid", "Closer": "S", "Level": "Agents L1", "Phone": 9176615180, "DATE OF PAY": "2024-12-24 00:00:00", "Unnamed: 8": NaN}, {"Name": "Sai Kishore Komanduri", "Email ": "<EMAIL>", "Amount": 12749, "Status": "Paid", "Closer": "S", "Level": "Agents L1", "Phone": 9000484166, "DATE OF PAY": "2025-01-06 00:00:00", "Unnamed: 8": NaN}, {"Name": "<PERSON> ", "Email ": "<EMAIL>", "Amount": 12749, "Status": "Paid", "Closer": "S", "Level": "Agents L1", "Phone": 9972215775, "DATE OF PAY": "2025-01-06 00:00:00", "Unnamed: 8": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 12749, "Status": "Paid", "Closer": "S", "Level": "Agents L1", "Phone": 8008900948, "DATE OF PAY": "2024-12-24 00:00:00", "Unnamed: 8": NaN}]}, "MAP - L1 Feb": {"rows": 59, "columns": 13, "column_names": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "data_types": {"Name": "object", "Email ": "object", "Unnamed: 2": "object", "Amount": "object", "Status": "object", "Closer": "object", "Level": "object", "Phone": "object", "DATE OF PAY": "object", "Unnamed: 9": "object", "Unnamed: 10": "float64", "Unnamed: 11": "object", "Unnamed: 12": "object"}, "preview": [{"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 88829 99051‬", "DATE OF PAY": "23rd Oct", "Unnamed: 9": "2000 Advance 9249 Balance", "Unnamed: 10": NaN, "Unnamed: 11": "<PERSON><PERSON>", "Unnamed: 12": "GIVE HIM NEW DATES"}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10000, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 87902 84202‬", "DATE OF PAY": "6th Nov", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10500, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 98946 29670‬", "DATE OF PAY": "23rd Oct", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Name": "Smitha K R S", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 97311 84713‬", "DATE OF PAY": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10500, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 95027 34436‬", "DATE OF PAY": "Dec 2nd ", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 86185 82316‬", "DATE OF PAY": "Dec 9th", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 98868 81055‬", "DATE OF PAY": "Dec 9th", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}, {"Name": "<PERSON> ", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": 10300, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 99804 54216‬", "DATE OF PAY": "Nov 7th", "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": NaN, "Status": NaN, "Closer": NaN, "Level": "L1", "Phone": "‪+91 98999 41920‬", "DATE OF PAY": NaN, "Unnamed: 9": "OLD BATCH", "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Unnamed: 2": NaN, "Amount": NaN, "Status": "Paid", "Closer": "M", "Level": "L1", "Phone": "‪+91 80950 36637‬", "DATE OF PAY": NaN, "Unnamed: 9": "OLD BATCH", "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}]}, "Agentic Feb": {"rows": 44, "columns": 13, "column_names": ["Name", "Email ", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "data_types": {"Name": "object", "Email ": "object", "Amount": "float64", "Status": "object", "Closer": "object", "Level": "object", "Phone": "object", "DATE OF PAY": "object", "Unnamed: 8": "float64", "Unnamed: 9": "object", "Unnamed: 10": "float64", "Unnamed: 11": "float64", "Unnamed: 12": "object"}, "preview": [{"Name": "<PERSON><PERSON>", "Email ": "<PERSON><PERSON><PERSON>@brishti.in", "Amount": 13875.0, "Status": "Paid", "Closer": "M", "Level": "Agentic AI", "Phone": "‪+91 98300 15441‬", "DATE OF PAY": "Jan 22nd", "Unnamed: 8": NaN, "Unnamed: 9": "Name:<PERSON><PERSON><PERSON>", "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<PERSON><PERSON><PERSON>@brishti.in"}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": " <EMAIL>", "Amount": NaN, "Status": "Paid", "Closer": "M", "Level": "Agentic AI", "Phone": "‪+91 98807 16541‬", "DATE OF PAY": "OLD", "Unnamed: 8": NaN, "Unnamed: 9": "Email: <EMAIL>", "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": " <EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Email ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "Amount": 15725.0, "Status": "Paid", "Closer": "M", "Level": "Agentic AI", "Phone": "‪+91 73024 93499‬", "DATE OF PAY": "27th Jan", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"Name": "Gokul <PERSON>uchengo<PERSON> Vajravel", "Email ": " <EMAIL>", "Amount": 13875.0, "Status": "Paid", "Closer": "M", "Level": "Agentic AI", "Phone": "99002 16325", "DATE OF PAY": "OLD", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": " <EMAIL>"}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 12000.0, "Status": "Paid", "Closer": "M", "Level": "Agentic AI", "Phone": "‪+91 97418 90631‬", "DATE OF PAY": "OLD", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>hat<PERSON>", "Email ": "<EMAIL>", "Amount": 12000.0, "Status": "Paid", "Closer": "M", "Level": "Agentic AI", "Phone": "‪+91 99710 10646‬", "DATE OF PAY": "OLD", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}, {"Name": "~<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": NaN, "Status": "Paid", "Closer": "M", "Level": "Agentic AI", "Phone": "‪+91 99852 90108‬", "DATE OF PAY": NaN, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": NaN}, {"Name": " <PERSON><PERSON><PERSON><PERSON>", "Email ": "<EMAIL> ", "Amount": 10000.0, "Status": "Paid", "Closer": "M", "Level": "Agentic AI", "Phone": "‪+91 99457 40095‬", "DATE OF PAY": "11th Feb", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL> "}, {"Name": " <PERSON><PERSON>", "Email ": " <EMAIL>", "Amount": NaN, "Status": "Paid", "Closer": "M", "Level": "Agentic AI", "Phone": "‪+91 98946 29670‬", "DATE OF PAY": "OLD", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": " <EMAIL>"}, {"Name": "Nakul <PERSON>", "Email ": " <EMAIL>", "Amount": NaN, "Status": "Paid", "Closer": "M", "Level": "Agentic AI", "Phone": "‪+91 94200 04055‬", "DATE OF PAY": "OLD", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": " <EMAIL>"}]}, "MAP -L1 Mar": {"rows": 39, "columns": 13, "column_names": ["Name", "Email ", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "data_types": {"Name": "object", "Email ": "object", "Amount": "object", "Status": "object", "Closer": "object", "Level": "object", "Phone": "object", "DATE OF PAY": "object", "Unnamed: 8": "float64", "Unnamed: 9": "object", "Unnamed: 10": "float64", "Unnamed: 11": "float64", "Unnamed: 12": "object"}, "preview": [{"Name": " <PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "Level 1", "Phone": "‪+91 88671 82868‬", "DATE OF PAY": "18th Feb", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "Level 1", "Phone": "‪+91 90490 99888‬", "DATE OF PAY": "4th feb", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}, {"Name": "Purna Akshaya S R", "Email ": "purna<PERSON><PERSON><EMAIL>", "Amount": 10300, "Status": "Paid", "Closer": "M", "Level": "Level 1", "Phone": "‪+91 96001 74001‬", "DATE OF PAY": "4th feb", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "purna<PERSON><PERSON><EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "Level 1", "Phone": "‪+91 99674 45255‬", "DATE OF PAY": "5th Feb", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}, {"Name": "<PERSON>", "Email ": "<EMAIL>", "Amount": 10500, "Status": "Paid", "Closer": "M", "Level": "Level 1", "Phone": "‪+91 98682 48145‬", "DATE OF PAY": "5th Feb", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}, {"Name": "<PERSON><PERSON><PERSON>", "Email ": " <EMAIL>", "Amount": 10500, "Status": "Paid", "Closer": "M", "Level": "Level 1", "Phone": "‪+63 915 975 8082‬", "DATE OF PAY": "6th Feb", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": " <EMAIL>"}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "Level 1", "Phone": 919768002221, "DATE OF PAY": "6th Feb", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}, {"Name": "Vinay <PERSON>gade", "Email ": " <EMAIL> ", "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "Level 1", "Phone": "‪+91 98901 72993‬", "DATE OF PAY": "7th feb", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": " <EMAIL> "}, {"Name": "<PERSON>", "Email ": "sjk<PERSON><PERSON>@gmail.com", "Amount": 11249, "Status": "Paid", "Closer": "M", "Level": "Level 1", "Phone": "‪+91 99529 62944‬", "DATE OF PAY": "7th feb", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "sjk<PERSON><PERSON>@gmail.com"}, {"Name": "<PERSON><PERSON>", "Email ": "<EMAIL>", "Amount": 10500, "Status": "Paid", "Closer": "M", "Level": "Level 1", "Phone": "‪+91 77948 41440‬", "DATE OF PAY": "7th feb", "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN, "Unnamed: 11": NaN, "Unnamed: 12": "<EMAIL>"}]}, "Vibe Coding": {"error": "sequence item 2: expected str instance, int found"}, "Agentic - May": {"error": "sequence item 2: expected str instance, int found"}, "Essentails Next": {"error": "sequence item 2: expected str instance, int found"}, "Vibe Next": {"error": "sequence item 2: expected str instance, int found"}}, "total_sheets": 14, "mapping_suggestions": {"potential_leads": [{"sheet": "MAP -18", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "Source", "DATE OF PAY", "More details", "Unnamed: 11", "Unnamed: 12", "PAID IN JULY MONTH", "Unnamed: 14", "Unnamed: 15", "Unnamed: 16", "Unnamed: 17", "Unnamed: 18", "Unnamed: 19"], "confidence": "high"}, {"sheet": "MAP - Prac 2", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "Source", "Unnamed: 9", "More details", "Unnamed: 11", "PAID IN JULY AND AUG ", "Unnamed: 13", "Unnamed: 14", "Unnamed: 15", "Unnamed: 16", "Unnamed: 17", "Unnamed: 18", "Unnamed: 19", "Unnamed: 20"], "confidence": "high"}, {"sheet": "MAP -19", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "Source", "DATE OF PAY", "Unnamed: 10", "Unnamed: 11"], "confidence": "high"}, {"sheet": "MAP - Prac 3", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9", "Unnamed: 10", "Previous ones", "Unnamed: 12", "Unnamed: 13", "Unnamed: 14"], "confidence": "high"}, {"sheet": "MAP - L1 NOV", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12", "Unnamed: 13", "Unnamed: 14"], "confidence": "high"}, {"sheet": "Y Pro", "columns": ["Name", "Email ", "<PERSON><PERSON><PERSON>", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9"], "confidence": "high"}, {"sheet": "Agentic Jan L1", "columns": ["Name", "Email ", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 8"], "confidence": "high"}, {"sheet": "MAP - L1 Feb", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "confidence": "high"}, {"sheet": "Agentic Feb", "columns": ["Name", "Email ", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "confidence": "high"}, {"sheet": "MAP -L1 Mar", "columns": ["Name", "Email ", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "confidence": "high"}], "potential_workshops": [], "potential_payments": [{"sheet": "MAP -18", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "Source", "DATE OF PAY", "More details", "Unnamed: 11", "Unnamed: 12", "PAID IN JULY MONTH", "Unnamed: 14", "Unnamed: 15", "Unnamed: 16", "Unnamed: 17", "Unnamed: 18", "Unnamed: 19"], "confidence": "high"}, {"sheet": "MAP - Prac 2", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "Source", "Unnamed: 9", "More details", "Unnamed: 11", "PAID IN JULY AND AUG ", "Unnamed: 13", "Unnamed: 14", "Unnamed: 15", "Unnamed: 16", "Unnamed: 17", "Unnamed: 18", "Unnamed: 19", "Unnamed: 20"], "confidence": "high"}, {"sheet": "MAP -19", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "Source", "DATE OF PAY", "Unnamed: 10", "Unnamed: 11"], "confidence": "high"}, {"sheet": "MAP - Prac 3", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9", "Unnamed: 10", "Previous ones", "Unnamed: 12", "Unnamed: 13", "Unnamed: 14"], "confidence": "high"}, {"sheet": "MAP - L1 NOV", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12", "Unnamed: 13", "Unnamed: 14"], "confidence": "high"}, {"sheet": "Y Pro", "columns": ["Name", "Email ", "<PERSON><PERSON><PERSON>", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9"], "confidence": "high"}, {"sheet": "Agentic Jan L1", "columns": ["Name", "Email ", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 8"], "confidence": "high"}, {"sheet": "MAP - L1 Feb", "columns": ["Name", "Email ", "Unnamed: 2", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "confidence": "high"}, {"sheet": "Agentic Feb", "columns": ["Name", "Email ", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "confidence": "high"}, {"sheet": "MAP -L1 Mar", "columns": ["Name", "Email ", "Amount", "Status", "Closer", "Level", "Phone", "DATE OF PAY", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12"], "confidence": "high"}], "workshop_enrollments": []}}}