================================================================================
LEGACY CSV ANALYSIS REPORT
================================================================================
Generated: 2025-08-07 21:17:03
Source CSV: /Users/<USER>/Code/modernaipro/mai-administrative/legacy/old_crm/all_leads.csv
Target DB: /Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/database/leads.db

1. FILE STRUCTURE ANALYSIS
------------------------------
Total records in CSV: 2,204
Columns: Unnamed: 0, name, phone_number, email

2. DATA QUALITY ASSESSMENT
------------------------------
Email Quality:
  • Total emails: 2,187
  • Valid format: 2,186
  • Invalid format: 1
  • Unique emails: 2,076
  • Duplicates: 111
  • Completion rate: 99.2%

Phone Quality:
  • Total phones: 2,184
  • Completion rate: 99.1%
  • With country code: 1,636

Name Quality:
  • Total names: 1,832
  • Completion rate: 83.1%
  • Multi-word names: 1,433

3. DATABASE OVERLAP ANALYSIS
------------------------------
CSV emails: 2,072
Existing DB emails: 4,010
Overlapping emails: 121
New emails: 1,951
Overlap percentage: 5.8%
New percentage: 94.2%

Sample new emails:
  • <EMAIL>
  • <EMAIL>
  • <EMAIL>
  • <EMAIL>
  • <EMAIL>

4. NEW LEADS QUALITY ASSESSMENT
------------------------------
Potential new leads: 2,048

Email validity:
  • Valid emails: 2,047
  • Validity rate: 100.0%

Name completeness:
  • Complete names: 1,728
  • Completion rate: 84.4%

5. MIGRATION RECOMMENDATION
------------------------------
Should migrate: YES

Reasoning:
  • Moderate overlap with 1951 new leads

Migration Strategy:
  • Approach: Selective migration with quality tagging
  • Quality tags to apply:
    - email_quality: low
    - response_expectation: low
    - data_vintage: legacy
    - lead_origin: old_crm

Expected Outcome:
  • New leads added: 1,951
  • Database growth: 48.7%
  • Quality level: Low (legacy data)

================================================================================
END OF REPORT
================================================================================