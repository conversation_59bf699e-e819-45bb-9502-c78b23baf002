================================================================================
STRIPE PAYMENT DATA AUDIT & ENRICHMENT REPORT
================================================================================
Generated: 2025-08-07 21:55:36
Database: /Users/<USER>/Code/modernaipro/mai-administrative/new/dashboard/database/leads.db

EXECUTIVE SUMMARY
----------------------------------------
• Total Stripe records found: 9203
• Customers created: 0
• Customers updated: 0
• Duplicates resolved: 969
• Metadata enrichments: 32
• Issues identified: 1669

STRIPE RECORDS SUMMARY
----------------------------------------
• Leads with Stripe data: 8469
• Payment records: 32
• Customers with Stripe IDs: 702
• Customer emails linked to payments: 32

V2 SCHEMA COMPLIANCE
----------------------------------------
• Customer-Lead relationships: 1122
• Payment-Customer relationships: 32
• Customer-Email relationships: 1122

SAMPLE PROCESSING ACTIONS
----------------------------------------
Customer 669 (Lead 3257):
  Email: <EMAIL>
  • lead_status: Customer -> Enrolled

Customer 670 (Lead 3258):
  Email: <EMAIL>
  • lead_status: Customer -> Enrolled

Customer 671 (Lead 3259):
  Email: <EMAIL>
  • lead_status: Customer -> Enrolled

Customer 672 (Lead 3260):
  Email: <EMAIL>
  • lead_status: Customer -> Enrolled

Customer 673 (Lead 3263):
  Email: <EMAIL>
  • lead_status: Customer -> Enrolled

ISSUES IDENTIFIED (SAMPLE)
----------------------------------------
• status_misalignment: 700 instances
• duplicate_email_group: 969 instances

STRIPE REVENUE ANALYSIS
----------------------------------------
• Total Stripe customers: 1122
• Total revenue (USD): $7,219.00
• Average customer value: $6.43
• Total completed payments: 32

RECOMMENDATIONS
----------------------------------------
• Monitor orphaned records and resolve data integrity issues
• Implement automated customer status updates for new payments
• Consider setting up triggers for real-time metadata enrichment
• Regular duplicate detection and resolution processes
• Ensure consistent email validation across all entry points

================================================================================