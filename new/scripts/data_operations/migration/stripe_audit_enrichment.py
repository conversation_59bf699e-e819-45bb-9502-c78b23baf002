#!/usr/bin/env python3
"""
Stripe Payment Data Audit and Enrichment Script
==================================================

Purpose: Comprehensive audit and enrichment of Stripe payment data in V2 database
- Identify all Stripe-related records
- Ensure proper customer status marking
- Enrich metadata and payment information
- Resolve duplicates and maintain data integrity
- Generate detailed audit report

Database: /Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db
Author: Modern AI Pro Data Migration System
Date: 2025-01-07
"""

import sqlite3
import json
import re
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StripeAuditEnrichment:
    def __init__(self, db_path: str):
        """Initialize the Stripe audit and enrichment system."""
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        
        # Audit results
        self.audit_results = {
            'stripe_records_found': 0,
            'customers_created': 0,
            'customers_updated': 0,
            'duplicates_resolved': 0,
            'payments_reconciled': 0,
            'metadata_enriched': 0,
            'issues_found': [],
            'processing_summary': []
        }
        
        # Email patterns for better matching
        self.email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        
    def connect_db(self):
        """Connect to the database."""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.cursor = self.conn.cursor()
            logger.info(f"Connected to database: {self.db_path}")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
            
    def close_db(self):
        """Close database connection."""
        if self.conn:
            self.conn.close()
            logger.info("Database connection closed")
    
    def identify_stripe_records(self) -> Dict:
        """Identify all records with Stripe-related data sources."""
        logger.info("🔍 Identifying Stripe records in database...")
        
        stripe_patterns = [
            '%stripe%',
            '%payment%',
            'stripe_',
            'pi_',  # Stripe payment intent ID pattern
            'ch_',  # Stripe charge ID pattern
            'in_',  # Stripe invoice ID pattern
            'cus_'  # Stripe customer ID pattern
        ]
        
        stripe_records = {
            'leads': [],
            'customers': [],
            'payments': [],
            'customer_emails': []
        }
        
        # Check leads table for Stripe references
        for pattern in stripe_patterns:
            # Check data_source, lead_source, and raw_field_data
            query = """
            SELECT id, full_name, email, phone, lead_source, data_source, 
                   status, workshop_type, workshop_price_usd, created_time,
                   stripe_payment_id, payment_status, payment_amount
            FROM leads 
            WHERE lead_source LIKE ? 
               OR data_source LIKE ?
               OR stripe_payment_id IS NOT NULL
               OR payment_status LIKE '%paid%'
               OR payment_status LIKE '%completed%'
               OR email IN (
                   SELECT customer_email FROM payments WHERE customer_email IS NOT NULL
               )
            """
            self.cursor.execute(query, (pattern, pattern))
            results = self.cursor.fetchall()
            
            for record in results:
                if record not in stripe_records['leads']:
                    stripe_records['leads'].append(record)
        
        # Check payments table
        self.cursor.execute("""
        SELECT id, customer_id, lead_id, amount, currency, 
               stripe_payment_intent_id, stripe_charge_id,
               customer_email, status, workshop_type, payment_date,
               customer_name, receipt_url
        FROM payments
        """)
        stripe_records['payments'] = self.cursor.fetchall()
        
        # Check customers table for Stripe customer IDs
        self.cursor.execute("""
        SELECT id, lead_id, customer_email, stripe_customer_id, 
               payment_email, total_spent, total_spent_usd,
               customer_segment, subscription_status, created_at
        FROM customers
        WHERE stripe_customer_id IS NOT NULL OR payment_method = 'stripe'
        """)
        stripe_records['customers'] = self.cursor.fetchall()
        
        # Check customer_emails for payment emails
        self.cursor.execute("""
        SELECT ce.id, ce.customer_id, ce.email, ce.email_type, ce.is_primary
        FROM customer_emails ce
        JOIN payments p ON ce.email = p.customer_email
        """)
        stripe_records['customer_emails'] = self.cursor.fetchall()
        
        logger.info(f"Found {len(stripe_records['leads'])} Stripe-related leads")
        logger.info(f"Found {len(stripe_records['payments'])} payment records")
        logger.info(f"Found {len(stripe_records['customers'])} customers with Stripe IDs")
        logger.info(f"Found {len(stripe_records['customer_emails'])} payment email matches")
        
        self.audit_results['stripe_records_found'] = (
            len(stripe_records['leads']) + 
            len(stripe_records['payments']) + 
            len(stripe_records['customers'])
        )
        
        return stripe_records
    
    def verify_customer_status(self, stripe_records: Dict) -> List[Dict]:
        """Verify that Stripe records are properly marked as customers."""
        logger.info("👤 Verifying customer status for Stripe records...")
        
        status_issues = []
        
        # Check leads that have payments but aren't marked properly
        payment_emails = {record[7] for record in stripe_records['payments'] if record[7]}  # customer_email
        
        for lead in stripe_records['leads']:
            lead_id, full_name, email, phone, lead_source, data_source, status, workshop_type, price, created_time, stripe_payment_id, payment_status, payment_amount = lead
            
            # Check if this lead has payments
            has_payments = any(record[2] == lead_id for record in stripe_records['payments']) or stripe_payment_id is not None or (payment_amount and payment_amount > 0)
            
            # Check if lead exists as customer
            is_customer = any(record[1] == lead_id for record in stripe_records['customers'])  # lead_id in customers
            
            if has_payments and not is_customer:
                status_issues.append({
                    'type': 'missing_customer_record',
                    'lead_id': lead_id,
                    'full_name': full_name,
                    'email': email,
                    'has_payments': has_payments,
                    'is_customer': is_customer,
                    'recommendation': 'Create customer record'
                })
            
            # Check status alignment
            if has_payments and status not in ['Enrolled', 'Workshop Complete', 'Alumni Network']:
                status_issues.append({
                    'type': 'status_misalignment',
                    'lead_id': lead_id,
                    'full_name': full_name,
                    'email': email,
                    'current_status': status,
                    'recommended_status': 'Enrolled',
                    'reason': 'Has payment records but status not updated'
                })
        
        logger.info(f"Found {len(status_issues)} customer status issues")
        self.audit_results['issues_found'].extend(status_issues)
        
        return status_issues
    
    def detect_duplicates(self, stripe_records: Dict) -> List[Dict]:
        """Detect duplicate Stripe customers by email and other identifiers."""
        logger.info("🔍 Detecting duplicate Stripe customers...")
        
        duplicates = []
        email_groups = defaultdict(list)
        
        # Group leads by email
        for lead in stripe_records['leads']:
            if lead[2]:  # email field
                email = lead[2].lower().strip()
                if self.email_pattern.match(email):
                    email_groups[email].append({
                        'type': 'lead',
                        'id': lead[0],
                        'full_name': lead[1],
                        'email': email,
                        'phone': lead[3],
                        'lead_source': lead[4],
                        'status': lead[6],
                        'created_time': lead[9]
                    })
        
        # Group customers by email
        for customer in stripe_records['customers']:
            if customer[2]:  # customer_email
                email = customer[2].lower().strip()
                if self.email_pattern.match(email):
                    email_groups[email].append({
                        'type': 'customer',
                        'id': customer[0],
                        'lead_id': customer[1],
                        'email': email,
                        'stripe_customer_id': customer[3],
                        'payment_email': customer[4],
                        'total_spent_usd': customer[6],
                        'created_at': customer[9]
                    })
        
        # Check payment emails
        for payment in stripe_records['payments']:
            if payment[7]:  # customer_email
                email = payment[7].lower().strip()
                if self.email_pattern.match(email):
                    email_groups[email].append({
                        'type': 'payment',
                        'id': payment[0],
                        'customer_id': payment[1],
                        'lead_id': payment[2],
                        'email': email,
                        'amount': payment[3],
                        'created_at': payment[10]
                    })
        
        # Identify duplicates (same email with multiple records)
        for email, records in email_groups.items():
            if len(records) > 1:
                # Check if they're actually different entities
                unique_entities = set()
                for record in records:
                    if record['type'] == 'lead':
                        unique_entities.add(f"lead_{record['id']}")
                    elif record['type'] == 'customer':
                        unique_entities.add(f"customer_{record['id']}")
                    elif record['type'] == 'payment':
                        unique_entities.add(f"payment_customer_{record['customer_id']}")
                
                if len(unique_entities) > 1:
                    duplicates.append({
                        'email': email,
                        'records': records,
                        'entities_count': len(unique_entities),
                        'recommendation': 'Merge or consolidate records'
                    })
        
        logger.info(f"Found {len(duplicates)} potential duplicate email groups")
        self.audit_results['issues_found'].extend([
            {'type': 'duplicate_email_group', 'data': dup} for dup in duplicates
        ])
        
        return duplicates
    
    def enrich_customer_metadata(self, stripe_records: Dict) -> int:
        """Enrich customer metadata and ensure proper field population."""
        logger.info("📈 Enriching customer metadata...")
        
        enriched_count = 0
        
        # Process each customer record
        for customer in stripe_records['customers']:
            customer_id, lead_id, customer_email, stripe_customer_id, payment_email, total_spent, total_spent_usd, customer_segment, subscription_status, created_at = customer
            
            updates = {}
            enrichment_performed = False
            
            # Get lead information
            self.cursor.execute("""
            SELECT full_name, email, phone, lead_source, status, 
                   workshop_type, workshop_price_usd, created_time
            FROM leads WHERE id = ?
            """, (lead_id,))
            lead_info = self.cursor.fetchone()
            
            if lead_info:
                lead_name, lead_email, lead_phone, lead_source, lead_status, workshop_type, workshop_price, lead_created = lead_info
                
                # Ensure lead_source is set properly for Stripe customers
                if not lead_source or lead_source == 'meta_ads':
                    updates['lead_source'] = 'stripe_payment'
                    self.cursor.execute("UPDATE leads SET lead_source = ? WHERE id = ?", 
                                      ('stripe_payment', lead_id))
                    enrichment_performed = True
                
                # Update lead status if it's a paying customer
                if lead_status not in ['Enrolled', 'Workshop Complete', 'Alumni Network'] and total_spent_usd and total_spent_usd > 0:
                    new_status = 'Enrolled'
                    self.cursor.execute("UPDATE leads SET status = ? WHERE id = ?", 
                                      (new_status, lead_id))
                    updates['lead_status'] = f"{lead_status} -> {new_status}"
                    enrichment_performed = True
            
            # Get payment information for this customer
            self.cursor.execute("""
            SELECT COUNT(*), SUM(amount), MAX(payment_date), GROUP_CONCAT(DISTINCT workshop_type)
            FROM payments 
            WHERE customer_id = ? AND status = 'succeeded'
            """, (customer_id,))
            payment_info = self.cursor.fetchone()
            
            if payment_info and payment_info[0] > 0:
                payment_count, total_paid, last_payment, product_types = payment_info
                
                # Update customer segment based on payment history
                new_segment = customer_segment
                if payment_count >= 3:
                    new_segment = 'loyal'
                elif payment_count >= 2:
                    new_segment = 'repeat'
                elif payment_count >= 1:
                    new_segment = 'new'
                
                if new_segment != customer_segment:
                    self.cursor.execute("UPDATE customers SET customer_segment = ? WHERE id = ?", 
                                      (new_segment, customer_id))
                    updates['customer_segment'] = f"{customer_segment} -> {new_segment}"
                    enrichment_performed = True
                
                # Update total spent if it doesn't match
                if abs(float(total_spent_usd or 0) - float(total_paid or 0)) > 0.01:
                    self.cursor.execute("""
                    UPDATE customers 
                    SET total_spent_usd = ?, last_purchase_date = DATE(?), updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                    """, (total_paid, last_payment, customer_id))
                    updates['total_spent_usd'] = f"{total_spent_usd} -> {total_paid}"
                    enrichment_performed = True
            
            # Ensure customer has proper email entries
            self.cursor.execute("SELECT COUNT(*) FROM customer_emails WHERE customer_id = ?", (customer_id,))
            email_count = self.cursor.fetchone()[0]
            
            if email_count == 0 and customer_email:
                self.cursor.execute("""
                INSERT INTO customer_emails (customer_id, email, email_type, is_primary, is_verified)
                VALUES (?, ?, 'personal', TRUE, FALSE)
                """, (customer_id, customer_email))
                updates['customer_emails'] = 'Added primary email'
                enrichment_performed = True
            
            if enrichment_performed:
                enriched_count += 1
                self.audit_results['processing_summary'].append({
                    'customer_id': customer_id,
                    'lead_id': lead_id,
                    'customer_email': customer_email,
                    'updates': updates
                })
        
        self.conn.commit()
        logger.info(f"Enriched metadata for {enriched_count} customers")
        self.audit_results['metadata_enriched'] = enriched_count
        
        return enriched_count
    
    def create_missing_customer_records(self, status_issues: List[Dict]) -> int:
        """Create customer records for leads with payments but no customer record."""
        logger.info("👥 Creating missing customer records...")
        
        created_count = 0
        
        for issue in status_issues:
            if issue['type'] == 'missing_customer_record':
                lead_id = issue['lead_id']
                
                # Get full lead information
                self.cursor.execute("""
                SELECT full_name, email, phone, lead_source, created_time
                FROM leads WHERE id = ?
                """, (lead_id,))
                lead_info = self.cursor.fetchone()
                
                if lead_info:
                    full_name, email, phone, lead_source, created_time = lead_info
                    
                    # Create customer record
                    self.cursor.execute("""
                    INSERT INTO customers (
                        lead_id, customer_email, customer_phone, 
                        payment_method, customer_segment, created_at
                    ) VALUES (?, ?, ?, 'stripe', 'new', ?)
                    """, (lead_id, email, phone, created_time))
                    
                    customer_id = self.cursor.lastrowid
                    
                    # Add customer email record
                    if email and self.email_pattern.match(email):
                        self.cursor.execute("""
                        INSERT INTO customer_emails (customer_id, email, email_type, is_primary)
                        VALUES (?, ?, 'personal', TRUE)
                        """, (customer_id, email))
                    
                    # Update any existing payments to link to this customer
                    self.cursor.execute("""
                    UPDATE payments 
                    SET customer_id = ? 
                    WHERE lead_id = ? AND customer_id IS NULL
                    """, (customer_id, lead_id))
                    
                    created_count += 1
                    logger.info(f"Created customer record {customer_id} for lead {lead_id} ({full_name})")
        
        self.conn.commit()
        logger.info(f"Created {created_count} missing customer records")
        self.audit_results['customers_created'] = created_count
        
        return created_count
    
    def resolve_duplicate_issues(self, duplicates: List[Dict]) -> int:
        """Resolve duplicate customer issues by merging or updating records."""
        logger.info("🔧 Resolving duplicate issues...")
        
        resolved_count = 0
        
        for duplicate_group in duplicates:
            email = duplicate_group['email']
            records = duplicate_group['records']
            
            # Find the primary customer record (prefer existing customer over lead)
            primary_customer = None
            leads_to_merge = []
            payments_to_update = []
            
            for record in records:
                if record['type'] == 'customer':
                    if primary_customer is None or record['total_spent_usd'] > primary_customer.get('total_spent_usd', 0):
                        primary_customer = record
                elif record['type'] == 'lead':
                    leads_to_merge.append(record)
                elif record['type'] == 'payment':
                    payments_to_update.append(record)
            
            # If no existing customer, create one from the best lead
            if primary_customer is None and leads_to_merge:
                best_lead = max(leads_to_merge, key=lambda x: x.get('created_time', ''))
                
                # Create customer from lead
                self.cursor.execute("""
                INSERT INTO customers (
                    lead_id, customer_email, payment_method, 
                    customer_segment, created_at
                ) VALUES (?, ?, 'stripe', 'new', ?)
                """, (best_lead['id'], email, best_lead['created_time']))
                
                customer_id = self.cursor.lastrowid
                primary_customer = {'id': customer_id, 'type': 'customer'}
                
                # Add email record
                self.cursor.execute("""
                INSERT INTO customer_emails (customer_id, email, email_type, is_primary)
                VALUES (?, ?, 'personal', TRUE)
                """, (customer_id, email))
                
                leads_to_merge.remove(best_lead)
            
            if primary_customer:
                customer_id = primary_customer['id']
                
                # Update all payments to point to primary customer
                for payment in payments_to_update:
                    if payment['customer_id'] != customer_id:
                        self.cursor.execute("""
                        UPDATE payments SET customer_id = ? WHERE id = ?
                        """, (customer_id, payment['id']))
                
                # Link remaining leads to this customer
                for lead in leads_to_merge:
                    self.cursor.execute("""
                    UPDATE leads SET customer_id = ? WHERE id = ?
                    """, (customer_id, lead['id']))
                
                resolved_count += 1
                logger.info(f"Resolved duplicate group for email {email} -> customer {customer_id}")
        
        self.conn.commit()
        logger.info(f"Resolved {resolved_count} duplicate issues")
        self.audit_results['duplicates_resolved'] = resolved_count
        
        return resolved_count
    
    def validate_v2_schema_compliance(self) -> Dict:
        """Validate V2 schema compliance and relationships."""
        logger.info("✅ Validating V2 schema compliance...")
        
        validation_results = {
            'customer_lead_relationships': 0,
            'payment_customer_relationships': 0,
            'customer_email_relationships': 0,
            'orphaned_records': [],
            'schema_issues': []
        }
        
        # Check customer-lead relationships
        self.cursor.execute("""
        SELECT COUNT(*) FROM customers c
        JOIN leads l ON c.lead_id = l.id
        """)
        validation_results['customer_lead_relationships'] = self.cursor.fetchone()[0]
        
        # Check payment-customer relationships
        self.cursor.execute("""
        SELECT COUNT(*) FROM payments p
        JOIN customers c ON p.customer_id = c.id
        """)
        validation_results['payment_customer_relationships'] = self.cursor.fetchone()[0]
        
        # Check customer-email relationships
        self.cursor.execute("""
        SELECT COUNT(*) FROM customer_emails ce
        JOIN customers c ON ce.customer_id = c.id
        """)
        validation_results['customer_email_relationships'] = self.cursor.fetchone()[0]
        
        # Find orphaned records
        # Customers without leads
        self.cursor.execute("""
        SELECT c.id, c.customer_email FROM customers c
        LEFT JOIN leads l ON c.lead_id = l.id
        WHERE l.id IS NULL
        """)
        orphaned_customers = self.cursor.fetchall()
        
        # Payments without customers
        self.cursor.execute("""
        SELECT p.id, p.customer_email FROM payments p
        LEFT JOIN customers c ON p.customer_id = c.id
        WHERE c.id IS NULL
        """)
        orphaned_payments = self.cursor.fetchall()
        
        validation_results['orphaned_records'] = {
            'customers_without_leads': orphaned_customers,
            'payments_without_customers': orphaned_payments
        }
        
        logger.info(f"V2 Schema Validation: {validation_results['customer_lead_relationships']} customer-lead relationships")
        logger.info(f"Found {len(orphaned_customers)} orphaned customers, {len(orphaned_payments)} orphaned payments")
        
        return validation_results
    
    def generate_audit_report(self, stripe_records: Dict, validation_results: Dict) -> str:
        """Generate comprehensive audit report."""
        logger.info("📊 Generating comprehensive audit report...")
        
        report = []
        report.append("=" * 80)
        report.append("STRIPE PAYMENT DATA AUDIT & ENRICHMENT REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Database: {self.db_path}")
        report.append("")
        
        # Executive Summary
        report.append("EXECUTIVE SUMMARY")
        report.append("-" * 40)
        report.append(f"• Total Stripe records found: {self.audit_results['stripe_records_found']}")
        report.append(f"• Customers created: {self.audit_results['customers_created']}")
        report.append(f"• Customers updated: {self.audit_results['customers_updated']}")
        report.append(f"• Duplicates resolved: {self.audit_results['duplicates_resolved']}")
        report.append(f"• Metadata enrichments: {self.audit_results['metadata_enriched']}")
        report.append(f"• Issues identified: {len(self.audit_results['issues_found'])}")
        report.append("")
        
        # Stripe Records Summary
        report.append("STRIPE RECORDS SUMMARY")
        report.append("-" * 40)
        report.append(f"• Leads with Stripe data: {len(stripe_records['leads'])}")
        report.append(f"• Payment records: {len(stripe_records['payments'])}")
        report.append(f"• Customers with Stripe IDs: {len(stripe_records['customers'])}")
        report.append(f"• Customer emails linked to payments: {len(stripe_records['customer_emails'])}")
        report.append("")
        
        # V2 Schema Compliance
        report.append("V2 SCHEMA COMPLIANCE")
        report.append("-" * 40)
        report.append(f"• Customer-Lead relationships: {validation_results['customer_lead_relationships']}")
        report.append(f"• Payment-Customer relationships: {validation_results['payment_customer_relationships']}")
        report.append(f"• Customer-Email relationships: {validation_results['customer_email_relationships']}")
        report.append("")
        
        # Orphaned Records
        orphaned = validation_results['orphaned_records']
        if orphaned['customers_without_leads'] or orphaned['payments_without_customers']:
            report.append("ORPHANED RECORDS (ATTENTION REQUIRED)")
            report.append("-" * 40)
            if orphaned['customers_without_leads']:
                report.append(f"• Customers without leads: {len(orphaned['customers_without_leads'])}")
                for customer in orphaned['customers_without_leads'][:5]:  # Show first 5
                    report.append(f"  - Customer ID {customer[0]}: {customer[1]}")
            if orphaned['payments_without_customers']:
                report.append(f"• Payments without customers: {len(orphaned['payments_without_customers'])}")
                for payment in orphaned['payments_without_customers'][:5]:  # Show first 5
                    report.append(f"  - Payment ID {payment[0]}: {payment[1]}")
            report.append("")
        
        # Processing Summary (sample)
        if self.audit_results['processing_summary']:
            report.append("SAMPLE PROCESSING ACTIONS")
            report.append("-" * 40)
            for i, action in enumerate(self.audit_results['processing_summary'][:5]):
                report.append(f"Customer {action['customer_id']} (Lead {action['lead_id']}):")
                report.append(f"  Email: {action['customer_email']}")
                for key, value in action['updates'].items():
                    report.append(f"  • {key}: {value}")
                report.append("")
        
        # Issues Found (sample)
        if self.audit_results['issues_found']:
            report.append("ISSUES IDENTIFIED (SAMPLE)")
            report.append("-" * 40)
            issue_types = defaultdict(int)
            for issue in self.audit_results['issues_found']:
                issue_types[issue['type']] += 1
            
            for issue_type, count in issue_types.items():
                report.append(f"• {issue_type}: {count} instances")
            report.append("")
        
        # Revenue Analysis
        self.cursor.execute("""
        SELECT 
            COUNT(DISTINCT c.id) as total_customers,
            SUM(c.total_spent_usd) as total_revenue,
            AVG(c.total_spent_usd) as avg_customer_value,
            COUNT(DISTINCT p.id) as total_payments
        FROM customers c
        LEFT JOIN payments p ON c.id = p.customer_id AND p.status = 'completed'
        WHERE c.stripe_customer_id IS NOT NULL OR c.payment_method = 'stripe'
        """)
        revenue_data = self.cursor.fetchone()
        
        if revenue_data:
            report.append("STRIPE REVENUE ANALYSIS")
            report.append("-" * 40)
            report.append(f"• Total Stripe customers: {revenue_data[0] or 0}")
            report.append(f"• Total revenue (USD): ${revenue_data[1] or 0:,.2f}")
            report.append(f"• Average customer value: ${revenue_data[2] or 0:,.2f}")
            report.append(f"• Total completed payments: {revenue_data[3] or 0}")
            report.append("")
        
        # Recommendations
        report.append("RECOMMENDATIONS")
        report.append("-" * 40)
        report.append("• Monitor orphaned records and resolve data integrity issues")
        report.append("• Implement automated customer status updates for new payments")
        report.append("• Consider setting up triggers for real-time metadata enrichment")
        report.append("• Regular duplicate detection and resolution processes")
        report.append("• Ensure consistent email validation across all entry points")
        report.append("")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def run_full_audit(self) -> str:
        """Run the complete Stripe audit and enrichment process."""
        logger.info("🚀 Starting comprehensive Stripe audit and enrichment...")
        
        try:
            self.connect_db()
            
            # Step 1: Identify Stripe records
            stripe_records = self.identify_stripe_records()
            
            # Step 2: Verify customer status
            status_issues = self.verify_customer_status(stripe_records)
            
            # Step 3: Detect duplicates
            duplicates = self.detect_duplicates(stripe_records)
            
            # Step 4: Create missing customer records
            self.create_missing_customer_records(status_issues)
            
            # Step 5: Resolve duplicates
            self.resolve_duplicate_issues(duplicates)
            
            # Step 6: Enrich metadata
            self.enrich_customer_metadata(stripe_records)
            
            # Step 7: Validate V2 compliance
            validation_results = self.validate_v2_schema_compliance()
            
            # Step 8: Generate report
            report = self.generate_audit_report(stripe_records, validation_results)
            
            # Save report
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/data_migration/reports/stripe_audit_report_{timestamp}.txt"
            
            with open(report_filename, 'w') as f:
                f.write(report)
            
            logger.info(f"✅ Audit complete! Report saved to: {report_filename}")
            return report_filename
            
        except Exception as e:
            logger.error(f"Audit failed: {e}")
            raise
        finally:
            self.close_db()


def main():
    """Main execution function."""
    db_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"
    
    print("🔍 Modern AI Pro Stripe Payment Data Audit & Enrichment")
    print("=" * 60)
    
    auditor = StripeAuditEnrichment(db_path)
    report_file = auditor.run_full_audit()
    
    print(f"\n✅ Audit completed successfully!")
    print(f"📊 Report saved to: {report_file}")
    print("\nKey metrics:")
    print(f"• Stripe records processed: {auditor.audit_results['stripe_records_found']}")
    print(f"• Customers created: {auditor.audit_results['customers_created']}")
    print(f"• Metadata enrichments: {auditor.audit_results['metadata_enriched']}")
    print(f"• Duplicates resolved: {auditor.audit_results['duplicates_resolved']}")
    
    return report_file


if __name__ == "__main__":
    main()