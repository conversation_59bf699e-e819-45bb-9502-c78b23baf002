#!/usr/bin/env python3
"""
Comprehensive Stripe Customer Enrichment Script
Transform 668 bare Stripe payment records into rich customer profiles with course history
"""

import sqlite3
import pandas as pd
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple

class StripeCustomerEnricher:
    def __init__(self):
        self.db_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db'
        self.crm_excel_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/leads_data/US Lead CRM.xlsx'
        
        # Workshop code mapping
        self.workshop_mapping = {
            'L1': {
                'name': 'AI Essentials',
                'description': 'Foundation course',
                'price_range': [230, 320],
                'level': 'beginner'
            },
            'A1': {
                'name': 'Agentic AI Specialization', 
                'description': 'Advanced course',
                'price_range': [272, 272],
                'level': 'advanced'
            },
            'L2': {
                'name': 'AI Practitioner Advanced',
                'description': 'Advanced implementation',
                'price_range': [320, 320],
                'level': 'advanced'
            },
            'V1': {
                'name': 'Vibe Coding',
                'description': 'Programming course',
                'price_range': [200, 400],
                'level': 'intermediate'
            },
            'YAI': {
                'name': 'Young AI Program',
                'description': 'Discontinued',
                'price_range': [0, 0],
                'level': 'youth'
            },
            'VC': {
                'name': 'Vibe Coding',
                'description': 'Programming course (alternate code)',
                'price_range': [200, 400],
                'level': 'intermediate'
            }
        }
        
        self.conn = None
        self.crm_data = None
        self.enrichment_stats = {
            'total_stripe_customers': 0,
            'crm_matches_found': 0,
            'profiles_enriched': 0,
            'course_histories_created': 0,
            'phone_numbers_added': 0,
            'segments_updated': 0,
            'errors': []
        }
    
    def connect_db(self):
        """Connect to the database"""
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row  # Enable column access by name
    
    def load_crm_data(self):
        """Load and prepare CRM data"""
        print("Loading CRM data...")
        try:
            self.crm_data = pd.read_excel(self.crm_excel_path, sheet_name='New Leads')
            print(f"Loaded {len(self.crm_data)} CRM records")
            
            # Clean email addresses for matching
            self.crm_data['email_clean'] = self.crm_data['Email'].str.lower().str.strip()
            
            # Extract workshop codes from various fields
            self.crm_data['workshop_codes'] = self.crm_data.apply(self._extract_workshop_codes, axis=1)
            
            return True
        except Exception as e:
            print(f"Error loading CRM data: {e}")
            self.enrichment_stats['errors'].append(f"CRM load error: {e}")
            return False
    
    def _extract_workshop_codes(self, row) -> List[str]:
        """Extract workshop codes from CRM row"""
        codes = []
        
        # Check Purchase field
        if pd.notna(row.get('Purchase')):
            purchase_val = str(row['Purchase']).upper()
            for code in self.workshop_mapping.keys():
                if code in purchase_val:
                    codes.append(code)
        
        # Check Contact Code field
        if pd.notna(row.get('Contact Code')):
            contact_code = str(row['Contact Code']).upper()
            for code in self.workshop_mapping.keys():
                if code in contact_code:
                    codes.append(code)
        
        # Check Notes field for workshop mentions
        if pd.notna(row.get('Notes')):
            notes = str(row['Notes']).upper()
            for code in self.workshop_mapping.keys():
                if code in notes:
                    codes.append(code)
        
        return list(set(codes))  # Remove duplicates
    
    def get_stripe_customers(self) -> List[sqlite3.Row]:
        """Get all Stripe customers that need enrichment"""
        query = """
        SELECT * FROM customers 
        WHERE stripe_customer_id IS NOT NULL 
        AND stripe_customer_id != ''
        ORDER BY customer_email
        """
        return self.conn.execute(query).fetchall()
    
    def find_crm_match(self, customer_email: str) -> Optional[pd.Series]:
        """Find matching CRM record by email"""
        if not customer_email or self.crm_data is None:
            return None
        
        email_clean = customer_email.lower().strip()
        matches = self.crm_data[self.crm_data['email_clean'] == email_clean]
        
        if len(matches) > 0:
            return matches.iloc[0]  # Return first match
        
        return None
    
    def get_course_history_from_leads(self, customer_email: str) -> List[Dict]:
        """Get course history from leads table"""
        query = """
        SELECT workshop_type, payment_date, payment_amount, payment_status, 
               enrolled_date, workshop_completed_date
        FROM leads 
        WHERE email = ? AND payment_status = 'paid'
        ORDER BY payment_date DESC
        """
        
        lead_records = self.conn.execute(query, (customer_email,)).fetchall()
        course_history = []
        
        for record in lead_records:
            if record['workshop_type'] and record['workshop_type'] != 'TBD':
                course_info = self._map_workshop_type_to_course(record['workshop_type'])
                if course_info:
                    course_history.append({
                        'course_code': course_info['code'],
                        'course_name': course_info['name'],
                        'date_taken': record['payment_date'] or record['enrolled_date'],
                        'amount_paid': float(record['payment_amount']) if record['payment_amount'] else 0,
                        'payment_status': record['payment_status'] or 'completed'
                    })
        
        return course_history
    
    def _map_workshop_type_to_course(self, workshop_type: str) -> Optional[Dict]:
        """Map workshop type to course information"""
        workshop_upper = workshop_type.upper()
        
        # Direct mapping
        for code, info in self.workshop_mapping.items():
            if code in workshop_upper:
                return {'code': code, 'name': info['name']}
        
        # Check for specific patterns
        if 'AI' in workshop_upper and 'ESSENTIAL' in workshop_upper:
            return {'code': 'L1', 'name': self.workshop_mapping['L1']['name']}
        elif 'VIBE' in workshop_upper or 'VC' in workshop_upper:
            return {'code': 'V1', 'name': self.workshop_mapping['V1']['name']}
        elif 'AGENT' in workshop_upper:
            return {'code': 'A1', 'name': self.workshop_mapping['A1']['name']}
        
        return None
    
    def create_course_history_json(self, crm_match: pd.Series, lead_courses: List[Dict]) -> Dict:
        """Create comprehensive course history JSON"""
        all_courses = []
        
        # Add courses from CRM data
        if crm_match is not None and crm_match['workshop_codes']:
            for code in crm_match['workshop_codes']:
                course_info = self.workshop_mapping.get(code, {})
                all_courses.append({
                    'course_code': code,
                    'course_name': course_info.get('name', f'Workshop {code}'),
                    'date_taken': crm_match.get('Date'),
                    'amount_paid': self._estimate_amount_from_crm(crm_match, code),
                    'payment_status': 'completed' if crm_match.get('Status') == 'Bought' else 'enrolled',
                    'source': 'crm'
                })
        
        # Add courses from leads table
        for course in lead_courses:
            course['source'] = 'database'
            all_courses.append(course)
        
        # Remove duplicates and calculate totals
        unique_courses = []
        seen_courses = set()
        total_spent = 0
        
        for course in all_courses:
            course_key = f"{course['course_code']}_{course.get('date_taken', 'unknown')}"
            if course_key not in seen_courses:
                unique_courses.append(course)
                seen_courses.add(course_key)
                total_spent += course.get('amount_paid', 0)
        
        # Determine customer segment and next recommended course
        customer_segment = self._determine_customer_segment(unique_courses)
        next_course = self._recommend_next_course(unique_courses)
        
        return {
            'courses_taken': unique_courses,
            'total_courses': len(unique_courses),
            'total_spent': total_spent,
            'customer_segment': customer_segment,
            'next_recommended_course': next_course,
            'last_updated': datetime.now().isoformat(),
            'enrichment_source': 'crm_and_database'
        }
    
    def _estimate_amount_from_crm(self, crm_match: pd.Series, course_code: str) -> float:
        """Estimate payment amount from CRM data"""
        # Try to extract amount from Purchase field
        purchase_val = str(crm_match.get('Purchase', ''))
        
        # Look for numeric values in purchase field
        amounts = re.findall(r'\d+', purchase_val)
        if amounts:
            return float(amounts[0])
        
        # Use course mapping as fallback
        course_info = self.workshop_mapping.get(course_code, {})
        price_range = course_info.get('price_range', [0, 0])
        return float(price_range[0]) if price_range[0] > 0 else 0.0
    
    def _determine_customer_segment(self, courses: List[Dict]) -> str:
        """Determine customer segment based on course history"""
        total_spent = sum(course.get('amount_paid', 0) for course in courses)
        
        if len(courses) == 0:
            return 'new'
        elif len(courses) == 1:
            return 'new'
        elif len(courses) >= 2 and total_spent >= 500:
            return 'loyal'  # Multiple courses and high spend
        elif len(courses) >= 2:
            return 'repeat'
        else:
            return 'new'
    
    def _recommend_next_course(self, courses: List[Dict]) -> str:
        """Recommend next course based on completed courses"""
        completed_codes = [c['course_code'] for c in courses]
        
        # Course progression logic
        if 'L1' in completed_codes and 'A1' not in completed_codes:
            return 'A1'  # AI Essentials -> Agentic AI
        elif 'A1' in completed_codes and 'L2' not in completed_codes:
            return 'L2'  # Agentic AI -> AI Practitioner Advanced
        elif 'L1' not in completed_codes:
            return 'L1'  # Start with fundamentals
        elif 'V1' in completed_codes and 'L1' not in completed_codes:
            return 'L1'  # Vibe Coding graduates to AI Essentials
        else:
            return 'L2'  # Default to advanced course
    
    def enrich_customer(self, customer: sqlite3.Row) -> bool:
        """Enrich a single customer record"""
        try:
            customer_email = customer['customer_email']
            if not customer_email:
                return False
            
            # Find CRM match
            crm_match = self.find_crm_match(customer_email)
            
            # Get course history from leads table
            lead_courses = self.get_course_history_from_leads(customer_email)
            
            # Create course history JSON
            course_history = self.create_course_history_json(crm_match, lead_courses)
            
            # Prepare update data
            updates = {}
            
            # Add phone number if available
            if crm_match is not None and pd.notna(crm_match.get('Phone')):
                phone = str(crm_match['Phone']).strip()
                if phone and phone != 'nan':
                    updates['customer_phone'] = phone
                    self.enrichment_stats['phone_numbers_added'] += 1
            
            # Update customer segment
            updates['customer_segment'] = course_history['customer_segment']
            
            # Update workshop counts
            updates['workshops_completed'] = course_history['total_courses']
            updates['workshops_enrolled'] = course_history['total_courses']
            
            # Update total spent (if we have course history)
            if course_history['total_spent'] > 0:
                updates['total_spent'] = course_history['total_spent']
                updates['total_spent_usd'] = course_history['total_spent']
            
            # Store course history in a notes field or create JSON field
            course_history_json = json.dumps(course_history, indent=2, default=str)
            
            # Update the customer record
            if updates:
                update_fields = []
                update_values = []
                
                for field, value in updates.items():
                    update_fields.append(f"{field} = ?")
                    update_values.append(value)
                
                # Add updated timestamp
                update_fields.append("updated_at = ?")
                update_values.append(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                update_values.append(customer['id'])
                
                query = f"""
                UPDATE customers 
                SET {', '.join(update_fields)}
                WHERE id = ?
                """
                
                self.conn.execute(query, update_values)
                
                # Store course history in a separate note or update mechanism
                # For now, we'll print it for verification
                print(f"Enriched customer {customer_email}: {len(course_history['courses_taken'])} courses")
                
                if crm_match is not None:
                    self.enrichment_stats['crm_matches_found'] += 1
                
                self.enrichment_stats['profiles_enriched'] += 1
                
                if course_history['total_courses'] > 0:
                    self.enrichment_stats['course_histories_created'] += 1
                
                return True
            
            return False
            
        except Exception as e:
            error_msg = f"Error enriching customer {customer['customer_email'] if 'customer_email' in customer.keys() else 'unknown'}: {e}"
            print(error_msg)
            self.enrichment_stats['errors'].append(error_msg)
            return False
    
    def run_enrichment(self):
        """Run the complete enrichment process"""
        print("=== STARTING STRIPE CUSTOMER ENRICHMENT ===")
        
        # Connect to database
        self.connect_db()
        
        # Load CRM data
        if not self.load_crm_data():
            print("Failed to load CRM data. Exiting.")
            return
        
        # Get Stripe customers
        stripe_customers = self.get_stripe_customers()
        self.enrichment_stats['total_stripe_customers'] = len(stripe_customers)
        
        print(f"Found {len(stripe_customers)} Stripe customers to enrich")
        
        # Process each customer
        enriched_count = 0
        for i, customer in enumerate(stripe_customers, 1):
            if i % 50 == 0:  # Progress update every 50 customers
                print(f"Processed {i}/{len(stripe_customers)} customers...")
            
            if self.enrich_customer(customer):
                enriched_count += 1
        
        # Commit all changes
        self.conn.commit()
        self.conn.close()
        
        # Print final statistics
        self.print_enrichment_report()
    
    def print_enrichment_report(self):
        """Print comprehensive enrichment report"""
        print("\n=== CUSTOMER ENRICHMENT REPORT ===")
        print(f"Total Stripe customers processed: {self.enrichment_stats['total_stripe_customers']}")
        print(f"CRM matches found: {self.enrichment_stats['crm_matches_found']}")
        print(f"Customer profiles enriched: {self.enrichment_stats['profiles_enriched']}")
        print(f"Course histories created: {self.enrichment_stats['course_histories_created']}")
        print(f"Phone numbers added: {self.enrichment_stats['phone_numbers_added']}")
        
        # Success rate
        if self.enrichment_stats['total_stripe_customers'] > 0:
            success_rate = (self.enrichment_stats['profiles_enriched'] / self.enrichment_stats['total_stripe_customers']) * 100
            print(f"Enrichment success rate: {success_rate:.1f}%")
        
        if self.enrichment_stats['errors']:
            print(f"\nErrors encountered: {len(self.enrichment_stats['errors'])}")
            for error in self.enrichment_stats['errors'][:5]:  # Show first 5 errors
                print(f"  - {error}")
            if len(self.enrichment_stats['errors']) > 5:
                print(f"  ... and {len(self.enrichment_stats['errors']) - 5} more errors")

if __name__ == "__main__":
    enricher = StripeCustomerEnricher()
    enricher.run_enrichment()