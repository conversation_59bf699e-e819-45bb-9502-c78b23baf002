#!/usr/bin/env python3
"""
Stripe Customer Enrichment Analysis
Analyze current Stripe customer data and CRM data for enrichment strategy
"""

import sqlite3
import pandas as pd
import json
from datetime import datetime

def analyze_current_data():
    """Analyze current Stripe customers and CRM data"""
    
    # Connect to database
    conn = sqlite3.connect('/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db')
    
    print("=== CUSTOMER ANALYSIS ===")
    
    # Total customers
    total_customers = conn.execute('SELECT COUNT(*) FROM customers').fetchone()[0]
    print(f'Total customers: {total_customers}')
    
    # Customers with Stripe IDs
    stripe_customers = conn.execute("SELECT COUNT(*) FROM customers WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != ''").fetchone()[0]
    print(f'Customers with Stripe IDs: {stripe_customers}')
    
    # Sample Stripe customer records
    print("\n=== SAMPLE STRIPE CUSTOMER RECORDS ===")
    sample_customers = conn.execute('''
        SELECT customer_email, customer_phone, stripe_customer_id, total_spent, 
               subscription_status, customer_segment, workshops_completed
        FROM customers 
        WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != '' 
        LIMIT 10
    ''').fetchall()
    
    for customer in sample_customers:
        print(customer)
    
    # Customers missing phone data
    no_phone_count = conn.execute("SELECT COUNT(*) FROM customers WHERE customer_phone IS NULL OR customer_phone = ''").fetchone()[0]
    print(f"\nCustomers without phone: {no_phone_count}")
    
    # Workshop types in leads table
    print("\n=== WORKSHOP TYPE PATTERNS IN LEADS ===")
    workshop_types = conn.execute('''
        SELECT workshop_type, COUNT(*) as count 
        FROM leads 
        WHERE workshop_type IS NOT NULL 
        GROUP BY workshop_type 
        ORDER BY count DESC 
        LIMIT 15
    ''').fetchall()
    
    for wt in workshop_types:
        print(f'{wt[0]}: {wt[1]}')
    
    conn.close()
    
    # Analyze CRM data
    print("\n=== CRM DATA ANALYSIS ===")
    try:
        df = pd.read_excel('/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/leads_data/US Lead CRM.xlsx', sheet_name='New Leads')
        
        print(f"CRM records: {len(df)}")
        print(f"CRM columns: {df.columns.tolist()}")
        
        # Check for email matches with Stripe customers
        conn = sqlite3.connect('/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db')
        
        stripe_emails = [row[0] for row in conn.execute("SELECT customer_email FROM customers WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != ''").fetchall()]
        
        # Find matches
        crm_emails = df['Email'].dropna().str.lower()
        stripe_emails_lower = [email.lower() for email in stripe_emails if email]
        
        matches = crm_emails[crm_emails.isin(stripe_emails_lower)]
        print(f"\nEmail matches between CRM and Stripe: {len(matches)}")
        
        # Analyze CRM purchase data
        print("\nCRM Purchase patterns:")
        purchase_patterns = df['Purchase'].value_counts().head(10)
        print(purchase_patterns)
        
        # Status distribution
        print("\nCRM Status distribution:")
        status_dist = df['Status'].value_counts()
        print(status_dist)
        
        conn.close()
        
    except Exception as e:
        print(f"Error analyzing CRM data: {e}")

def create_workshop_mapping():
    """Create workshop code mapping"""
    
    workshop_mapping = {
        'L1': {
            'name': 'AI Essentials',
            'description': 'Foundation course',
            'price_range': '230-320',
            'level': 'beginner'
        },
        'A1': {
            'name': 'Agentic AI Specialization', 
            'description': 'Advanced course',
            'price_range': '272',
            'level': 'advanced'
        },
        'L2': {
            'name': 'AI Practitioner Advanced',
            'description': 'Advanced implementation',
            'price_range': '320',
            'level': 'advanced'
        },
        'V1': {
            'name': 'Vibe Coding',
            'description': 'Programming course',
            'price_range': 'varies',
            'level': 'intermediate'
        },
        'YAI': {
            'name': 'Young AI Program',
            'description': 'Discontinued',
            'price_range': 'N/A',
            'level': 'youth'
        }
    }
    
    print("\n=== WORKSHOP CODE MAPPING ===")
    for code, info in workshop_mapping.items():
        print(f"{code}: {info['name']} - {info['description']} (${info['price_range']})")
    
    return workshop_mapping

if __name__ == "__main__":
    analyze_current_data()
    create_workshop_mapping()