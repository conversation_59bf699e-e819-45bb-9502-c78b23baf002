#!/usr/bin/env python3
"""
Stripe Name Enrichment Script
Enrich 669 Stripe records with unknown names using Lead Sheet data
"""

import pandas as pd
import sqlite3
import numpy as np
from datetime import datetime
import sys
import os

def load_lead_sheet_data():
    """Load and clean Lead Sheet data"""
    file_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/leads_data/US Leads.xlsx'
    
    print("Loading Lead Sheet data...")
    lead_sheet = pd.read_excel(file_path, sheet_name='Lead Sheet')
    
    print(f"Lead Sheet Analysis:")
    print(f"Total rows: {len(lead_sheet)}")
    print(f"Columns: {list(lead_sheet.columns)}")
    
    # Clean emails
    lead_sheet['Email_cleaned'] = lead_sheet['Email'].astype(str).str.lower().str.strip()
    lead_sheet['Email_cleaned'] = lead_sheet['Email_cleaned'].replace('nan', np.nan)
    
    # Filter valid emails and names
    valid_data = lead_sheet[
        lead_sheet['Email_cleaned'].notna() & 
        (lead_sheet['Email_cleaned'] != '') &
        lead_sheet['Name'].notna() &
        (lead_sheet['Name'] != '')
    ].copy()
    
    print(f"Valid records with both email and name: {len(valid_data)}")
    
    # Check for duplicates
    duplicates = valid_data['Email_cleaned'].duplicated().sum()
    print(f"Duplicate emails: {duplicates}")
    
    # Remove duplicates, keeping first occurrence
    valid_data = valid_data.drop_duplicates(subset=['Email_cleaned'], keep='first')
    print(f"Records after removing duplicates: {len(valid_data)}")
    
    return valid_data

def get_unknown_stripe_records():
    """Get all Stripe records with unknown names from database"""
    db_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db'
    conn = sqlite3.connect(db_path)
    
    query = '''
    SELECT id, email, full_name, stripe_payment_id, payment_amount, phone, data_source
    FROM leads 
    WHERE full_name LIKE 'unknown%'
    ORDER BY id
    '''
    
    unknown_records = pd.read_sql_query(query, conn)
    conn.close()
    
    print(f"\nUnknown Stripe Records Analysis:")
    print(f"Total records: {len(unknown_records)}")
    print(f"Sample records:")
    print(unknown_records[['email', 'full_name']].head(10))
    
    return unknown_records

def match_and_enrich(unknown_records, lead_sheet_data):
    """Match unknown records with Lead Sheet data by email"""
    print(f"\nMatching Process:")
    
    # Clean unknown record emails
    unknown_records['email_cleaned'] = unknown_records['email'].str.lower().str.strip()
    
    # Create lookup dictionary from lead sheet
    lead_lookup = lead_sheet_data.set_index('Email_cleaned').to_dict('index')
    
    matches = []
    no_matches = []
    
    for idx, row in unknown_records.iterrows():
        email = row['email_cleaned']
        if email in lead_lookup:
            lead_data = lead_lookup[email]
            match_info = {
                'id': row['id'],
                'email': row['email'],
                'old_name': row['full_name'],
                'new_name': lead_data['Name'],
                'phone': lead_data.get('Phone', None),
                'python_exp': lead_data.get('Python Exp', None),
                'designation': lead_data.get('Designation', None),
                'notes': lead_data.get('Notes', None),
                'stripe_payment_id': row['stripe_payment_id'],
                'payment_amount': row['payment_amount']
            }
            matches.append(match_info)
        else:
            no_matches.append({
                'id': row['id'],
                'email': row['email'],
                'old_name': row['full_name']
            })
    
    print(f"Matches found: {len(matches)}")
    print(f"No matches: {len(no_matches)}")
    print(f"Success rate: {len(matches)/len(unknown_records)*100:.1f}%")
    
    return matches, no_matches

def update_database_records(matches):
    """Update database with enriched data"""
    if not matches:
        print("No matches to update")
        return
    
    db_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db'
    conn = sqlite3.connect(db_path)
    
    print(f"\nUpdating {len(matches)} records in database...")
    
    updates_made = 0
    for match in matches:
        try:
            # Update full_name and phone if available
            update_query = '''
            UPDATE leads 
            SET full_name = ?, 
                phone = ?,
                updated_at = datetime('now')
            WHERE id = ?
            '''
            
            conn.execute(update_query, (
                match['new_name'],
                match['phone'],
                match['id']
            ))
            updates_made += 1
            
        except Exception as e:
            print(f"Error updating record {match['id']}: {e}")
    
    conn.commit()
    conn.close()
    
    print(f"Successfully updated {updates_made} records")
    
    return updates_made

def generate_report(matches, no_matches, updates_made):
    """Generate enrichment report"""
    report = f"""
STRIPE NAME ENRICHMENT REPORT
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

EXECUTIVE SUMMARY:
================
• Target Records: 669 Stripe customers with unknown names
• Matches Found: {len(matches)} ({len(matches)/669*100:.1f}%)
• Database Updates: {updates_made}
• Remaining Unknown: {669 - len(matches)} ({(669-len(matches))/669*100:.1f}%)

ENRICHMENT DETAILS:
==================
Successfully matched and enriched {len(matches)} customer records using Lead Sheet data.
Each customer now has their real name instead of 'unknown_pi_[payment_id]' format.

SAMPLE ENRICHED RECORDS:
"""
    
    # Add sample enriched records
    for i, match in enumerate(matches[:10]):
        report += f"""
{i+1:2d}. Email: {match['email']}
    Old Name: {match['old_name']}
    New Name: {match['new_name']}
    Payment: ${match['payment_amount']}
"""
    
    if no_matches:
        report += f"""

UNMATCHED RECORDS ({len(no_matches)}):
"""
        for i, no_match in enumerate(no_matches[:10]):
            report += f"""
{i+1:2d}. {no_match['email']} - {no_match['old_name']}
"""
        if len(no_matches) > 10:
            report += f"    ... and {len(no_matches) - 10} more unmatched records\n"
    
    report += f"""

BUSINESS IMPACT:
===============
• Customer Experience: {len(matches)} paying customers can now be properly identified
• Personalization: Enable personalized communication with enriched customer data  
• Analytics: Improved customer segmentation and business intelligence
• Data Quality: Reduced unknown names from 669 to {669 - len(matches)}

NEXT STEPS:
==========
• Review {669 - len(matches)} remaining unknown records for alternative matching strategies
• Consider additional data sources for unmatched records
• Implement automated enrichment for future Stripe imports
"""
    
    print(report)
    
    # Save report to file
    report_path = f'/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/data_migration/reports/stripe_name_enrichment_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    with open(report_path, 'w') as f:
        f.write(report)
    
    print(f"Report saved to: {report_path}")
    return report_path

def main():
    """Main enrichment process"""
    print("STRIPE NAME ENRICHMENT PROCESS")
    print("=" * 50)
    
    # Phase 1: Load Lead Sheet data
    lead_sheet_data = load_lead_sheet_data()
    
    # Phase 2: Get unknown Stripe records
    unknown_records = get_unknown_stripe_records()
    
    # Phase 3: Match and enrich
    matches, no_matches = match_and_enrich(unknown_records, lead_sheet_data)
    
    # Phase 4: Update database
    updates_made = update_database_records(matches)
    
    # Phase 5: Generate report
    report_path = generate_report(matches, no_matches, updates_made)
    
    print(f"\nENRICHMENT COMPLETE!")
    print(f"Report saved to: {report_path}")

if __name__ == "__main__":
    main()