#!/usr/bin/env python3
"""
Stripe Customer Enrichment Validation Report
Generate comprehensive analysis of enrichment results
"""

import sqlite3
import pandas as pd
import json
from datetime import datetime
from collections import Counter

class EnrichmentValidator:
    def __init__(self):
        self.db_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db'
        self.conn = None
        
    def connect_db(self):
        """Connect to the database"""
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row
    
    def generate_enrichment_summary(self):
        """Generate comprehensive enrichment summary"""
        print("=== STRIPE CUSTOMER ENRICHMENT VALIDATION REPORT ===")
        print(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
        
        # Basic statistics
        total_customers = self.conn.execute('SELECT COUNT(*) FROM customers').fetchone()[0]
        stripe_customers = self.conn.execute(
            "SELECT COUNT(*) FROM customers WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != ''"
        ).fetchone()[0]
        
        print(f"\n📊 BASIC STATISTICS")
        print(f"Total customers in database: {total_customers:,}")
        print(f"Stripe customers identified: {stripe_customers:,}")
        
        # Enrichment statistics
        customers_with_phone = self.conn.execute(
            "SELECT COUNT(*) FROM customers WHERE customer_phone IS NOT NULL AND customer_phone != ''"
        ).fetchone()[0]
        
        customers_with_workshops = self.conn.execute(
            "SELECT COUNT(*) FROM customers WHERE workshops_completed > 0"
        ).fetchone()[0]
        
        customers_with_revenue = self.conn.execute(
            "SELECT COUNT(*) FROM customers WHERE total_spent > 0"
        ).fetchone()[0]
        
        print(f"\n🔍 ENRICHMENT RESULTS")
        print(f"Customers with phone numbers: {customers_with_phone:,} ({customers_with_phone/total_customers*100:.1f}%)")
        print(f"Customers with workshop completion: {customers_with_workshops:,} ({customers_with_workshops/total_customers*100:.1f}%)")
        print(f"Customers with revenue data: {customers_with_revenue:,} ({customers_with_revenue/total_customers*100:.1f}%)")
        
        # Customer segment analysis
        print(f"\n🎯 CUSTOMER SEGMENTATION ANALYSIS")
        segment_counts = self.conn.execute('''
            SELECT customer_segment, COUNT(*) as count
            FROM customers 
            WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != ''
            GROUP BY customer_segment
            ORDER BY count DESC
        ''').fetchall()
        
        for segment in segment_counts:
            percentage = (segment['count'] / stripe_customers) * 100
            print(f"{segment['customer_segment'].capitalize()} customers: {segment['count']:,} ({percentage:.1f}%)")
        
        # Revenue analysis
        print(f"\n💰 REVENUE ANALYSIS")
        revenue_stats = self.conn.execute('''
            SELECT 
                COUNT(*) as customers_with_revenue,
                SUM(total_spent) as total_revenue,
                AVG(total_spent) as avg_revenue_per_customer,
                MIN(total_spent) as min_spent,
                MAX(total_spent) as max_spent
            FROM customers 
            WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != ''
            AND total_spent > 0
        ''').fetchone()
        
        if revenue_stats['customers_with_revenue'] > 0:
            print(f"Customers with revenue: {revenue_stats['customers_with_revenue']:,}")
            print(f"Total tracked revenue: ${revenue_stats['total_revenue']:,.2f}")
            print(f"Average revenue per customer: ${revenue_stats['avg_revenue_per_customer']:,.2f}")
            print(f"Revenue range: ${revenue_stats['min_spent']:,.2f} - ${revenue_stats['max_spent']:,.2f}")
        else:
            print("No revenue data found in enriched records")
        
        # Workshop completion analysis
        print(f"\n🎓 WORKSHOP COMPLETION ANALYSIS")
        workshop_stats = self.conn.execute('''
            SELECT 
                workshops_completed,
                COUNT(*) as customer_count
            FROM customers 
            WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != ''
            GROUP BY workshops_completed
            ORDER BY workshops_completed DESC
        ''').fetchall()
        
        for stat in workshop_stats:
            if stat['workshops_completed'] > 0:
                percentage = (stat['customer_count'] / stripe_customers) * 100
                print(f"{stat['workshops_completed']} workshop(s): {stat['customer_count']:,} customers ({percentage:.1f}%)")
        
        # Top performing customers (by revenue)
        print(f"\n🌟 TOP PERFORMING CUSTOMERS")
        top_customers = self.conn.execute('''
            SELECT customer_email, total_spent, workshops_completed, customer_segment
            FROM customers 
            WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != ''
            AND total_spent > 0
            ORDER BY total_spent DESC
            LIMIT 10
        ''').fetchall()
        
        if top_customers:
            print("Top 10 customers by revenue:")
            for i, customer in enumerate(top_customers, 1):
                print(f"{i:2d}. {customer['customer_email'][:30]:<30} ${customer['total_spent']:>8,.2f} ({customer['workshops_completed']} courses, {customer['customer_segment']})")
        else:
            print("No customers with revenue data found")
        
        # Course completion patterns
        print(f"\n📚 COURSE COMPLETION PATTERNS")
        course_patterns = self.conn.execute('''
            SELECT 
                workshop_type,
                COUNT(*) as enrollments,
                COUNT(DISTINCT email) as unique_customers,
                AVG(payment_amount) as avg_payment
            FROM leads
            WHERE payment_status = 'paid'
            AND workshop_type IS NOT NULL
            AND workshop_type != 'TBD'
            GROUP BY workshop_type
            ORDER BY enrollments DESC
            LIMIT 10
        ''').fetchall()
        
        if course_patterns:
            print("Most popular courses:")
            for course in course_patterns:
                avg_payment = course['avg_payment'] or 0
                print(f"• {course['workshop_type']}: {course['enrollments']} enrollments, {course['unique_customers']} customers (avg: ${avg_payment:.0f})")
        
        # Data quality assessment
        print(f"\n🔍 DATA QUALITY ASSESSMENT")
        
        # Missing phone numbers
        missing_phones = self.conn.execute(
            "SELECT COUNT(*) FROM customers WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != '' AND (customer_phone IS NULL OR customer_phone = '')"
        ).fetchone()[0]
        phone_coverage = ((stripe_customers - missing_phones) / stripe_customers) * 100
        print(f"Phone number coverage: {phone_coverage:.1f}% ({stripe_customers - missing_phones:,}/{stripe_customers:,})")
        
        # Email validation
        invalid_emails = self.conn.execute(
            "SELECT COUNT(*) FROM customers WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != '' AND (customer_email NOT LIKE '%@%' OR customer_email IS NULL)"
        ).fetchone()[0]
        email_quality = ((stripe_customers - invalid_emails) / stripe_customers) * 100
        print(f"Email quality: {email_quality:.1f}% ({stripe_customers - invalid_emails:,}/{stripe_customers:,} valid)")
        
        # Duplicate detection
        duplicates = self.conn.execute('''
            SELECT customer_email, COUNT(*) as count
            FROM customers 
            WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != ''
            GROUP BY customer_email
            HAVING COUNT(*) > 1
        ''').fetchall()
        
        if duplicates:
            total_duplicates = sum(dup['count'] - 1 for dup in duplicates)
            print(f"Potential duplicates found: {len(duplicates)} email addresses with {total_duplicates} duplicate records")
        else:
            print("No email duplicates detected")
    
    def generate_campaign_intelligence(self):
        """Generate campaign intelligence recommendations"""
        print(f"\n\n🚀 CAMPAIGN INTELLIGENCE & RECOMMENDATIONS")
        print("=" * 70)
        
        # Segment-based recommendations
        segments = self.conn.execute('''
            SELECT 
                customer_segment,
                COUNT(*) as count,
                AVG(total_spent) as avg_spent,
                AVG(workshops_completed) as avg_workshops
            FROM customers
            WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != ''
            GROUP BY customer_segment
            ORDER BY count DESC
        ''').fetchall()
        
        print("📈 SEGMENTATION STRATEGY:")
        for segment in segments:
            count = segment['count']
            avg_spent = segment['avg_spent'] or 0
            avg_workshops = segment['avg_workshops'] or 0
            
            print(f"\n• {segment['customer_segment'].upper()} SEGMENT ({count:,} customers)")
            print(f"  Average spend: ${avg_spent:.2f}")
            print(f"  Average workshops: {avg_workshops:.1f}")
            
            # Recommendations based on segment
            if segment['customer_segment'] == 'new':
                print("  🎯 Recommendations: Welcome campaigns, L1 (AI Essentials) upsells")
            elif segment['customer_segment'] == 'repeat':
                print("  🎯 Recommendations: Advanced course promotions (A1, L2), loyalty programs")
            elif segment['customer_segment'] == 'loyal':
                print("  🎯 Recommendations: VIP treatment, beta program access, referral incentives")
        
        # Course progression opportunities
        print(f"\n📚 COURSE PROGRESSION OPPORTUNITIES:")
        
        # Customers who took L1 but not A1
        l1_to_a1_opportunity = self.conn.execute('''
            SELECT COUNT(DISTINCT email) as count
            FROM leads 
            WHERE workshop_type LIKE '%L1%' 
            AND payment_status = 'paid'
            AND email NOT IN (
                SELECT DISTINCT email FROM leads 
                WHERE workshop_type LIKE '%A1%' 
                AND payment_status = 'paid'
            )
        ''').fetchone()[0]
        
        if l1_to_a1_opportunity > 0:
            print(f"• {l1_to_a1_opportunity} customers completed L1 but not A1 → Target for Agentic AI Specialization")
        
        # Vibe Coding graduates
        vc_graduates = self.conn.execute('''
            SELECT COUNT(DISTINCT email) as count
            FROM leads 
            WHERE (workshop_type LIKE '%VC%' OR workshop_type LIKE '%Vibe%')
            AND payment_status = 'paid'
            AND email NOT IN (
                SELECT DISTINCT email FROM leads 
                WHERE workshop_type LIKE '%L1%' 
                AND payment_status = 'paid'
            )
        ''').fetchone()[0]
        
        if vc_graduates > 0:
            print(f"• {vc_graduates} Vibe Coding graduates → Target for AI Essentials transition")
        
        # High-value customer identification
        high_value = self.conn.execute('''
            SELECT COUNT(*) as count
            FROM customers
            WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != ''
            AND (total_spent >= 500 OR workshops_completed >= 2)
        ''').fetchone()[0]
        
        if high_value > 0:
            print(f"• {high_value} high-value customers → Candidates for VIP program and advanced courses")
    
    def export_enriched_data_sample(self, limit=20):
        """Export sample of enriched data for verification"""
        print(f"\n\n📋 SAMPLE ENRICHED CUSTOMER DATA")
        print("=" * 70)
        
        sample_customers = self.conn.execute(f'''
            SELECT 
                customer_email,
                customer_phone,
                customer_segment,
                workshops_completed,
                total_spent,
                subscription_status,
                updated_at
            FROM customers
            WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != ''
            AND (customer_phone IS NOT NULL OR workshops_completed > 0 OR total_spent > 0)
            ORDER BY total_spent DESC, workshops_completed DESC
            LIMIT {limit}
        ''').fetchall()
        
        if sample_customers:
            print(f"Showing top {len(sample_customers)} enriched customer records:")
            print(f"{'Email':<35} {'Phone':<15} {'Segment':<8} {'Courses':<7} {'Spent':<8} {'Updated'}")
            print("-" * 90)
            
            for customer in sample_customers:
                email = customer['customer_email'][:34]
                phone = (customer['customer_phone'] or 'N/A')[:14]
                segment = customer['customer_segment'][:7]
                courses = customer['workshops_completed'] or 0
                spent = customer['total_spent'] or 0
                updated = customer['updated_at'][:10] if customer['updated_at'] else 'N/A'
                
                print(f"{email:<35} {phone:<15} {segment:<8} {courses:<7} ${spent:<7.0f} {updated}")
        else:
            print("No enriched customer data found")
    
    def run_validation_report(self):
        """Run complete validation report"""
        self.connect_db()
        
        try:
            self.generate_enrichment_summary()
            self.generate_campaign_intelligence()
            self.export_enriched_data_sample()
            
        finally:
            if self.conn:
                self.conn.close()
        
        print(f"\n\n✅ ENRICHMENT VALIDATION COMPLETE")
        print(f"Report generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)

if __name__ == "__main__":
    validator = EnrichmentValidator()
    validator.run_validation_report()