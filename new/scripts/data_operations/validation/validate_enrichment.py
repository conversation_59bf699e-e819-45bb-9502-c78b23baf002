#!/usr/bin/env python3
"""
Validate the Stripe name enrichment results
"""

import sqlite3
import pandas as pd

def validate_enrichment():
    """Validate database changes after enrichment"""
    db_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db'
    conn = sqlite3.connect(db_path)
    
    print('VALIDATION RESULTS:')
    print('=' * 50)
    
    # Count remaining unknown names
    unknown_query = """
    SELECT COUNT(*) as remaining_unknown 
    FROM leads 
    WHERE full_name LIKE 'unknown%'
    """
    result = pd.read_sql_query(unknown_query, conn)
    remaining_unknown = result["remaining_unknown"].iloc[0]
    print(f'Remaining unknown names: {remaining_unknown}')
    
    # Sample enriched records
    enriched_query = """
    SELECT id, email, full_name, stripe_payment_id, payment_amount, phone, updated_at
    FROM leads 
    WHERE data_source = 'stripe' 
      AND full_name NOT LIKE 'unknown%'
      AND phone IS NOT NULL
    ORDER BY updated_at DESC
    LIMIT 10
    """
    
    enriched_records = pd.read_sql_query(enriched_query, conn)
    print(f'\nSample enriched Stripe records:')
    for _, row in enriched_records.iterrows():
        print(f'  {row["email"]} -> {row["full_name"]} (Payment: ${row["payment_amount"]})')
    
    # Verify total counts
    total_query = 'SELECT COUNT(*) as total FROM leads'
    stripe_query = 'SELECT COUNT(*) as stripe_total FROM leads WHERE data_source = "stripe"'
    
    total_result = pd.read_sql_query(total_query, conn)
    stripe_result = pd.read_sql_query(stripe_query, conn)
    
    print(f'\nDatabase Integrity Check:')
    print(f'Total leads: {total_result["total"].iloc[0]}')
    print(f'Stripe leads: {stripe_result["stripe_total"].iloc[0]}')
    
    # Calculate improvement metrics
    original_unknown = 669
    enriched_count = original_unknown - remaining_unknown
    improvement_rate = (enriched_count / original_unknown) * 100
    
    print(f'\nIMPROVEMENT METRICS:')
    print(f'Original unknown names: {original_unknown}')
    print(f'Successfully enriched: {enriched_count}')  
    print(f'Remaining unknown: {remaining_unknown}')
    print(f'Success rate: {improvement_rate:.1f}%')
    print(f'Data quality improvement: {(enriched_count/11174)*100:.2f}% of total database')
    
    # Show before/after comparison
    print(f'\nBEFORE/AFTER COMPARISON:')
    print(f'Unknown names before: 669 (6.0% of database)')
    print(f'Unknown names after: {remaining_unknown} ({remaining_unknown/11174*100:.2f}% of database)')
    print(f'Quality improvement: {((669-remaining_unknown)/11174)*100:.2f}% of database now properly named')
    
    conn.close()
    
    return {
        'original_unknown': original_unknown,
        'remaining_unknown': remaining_unknown,
        'enriched_count': enriched_count,
        'success_rate': improvement_rate
    }

if __name__ == "__main__":
    validate_enrichment()