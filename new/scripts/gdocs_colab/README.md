# Google Docs & Colab Content Processing

This folder contains scripts and temporary files used for processing Modern AI Pro course content from Google Docs and Colab notebooks.

## Scripts

### `colab_extractor.py`
- **Purpose**: Automated extraction of Colab notebooks from course structure documents
- **Usage**: Downloads and analyzes Colab notebooks, generates metadata JSON files
- **Output**: Organized notebook files and metadata for course organization

## Temporary Course Files

This folder may contain temporary notebook files and course documents downloaded during the content organization process. These files are typically processed and moved to the appropriate course folders in `/assets/courses/`.

## Process Flow

1. Course structure documents (Google Docs) are downloaded
2. Colab links are extracted using `colab_extractor.py`
3. Individual notebooks are downloaded and analyzed
4. Metadata is generated and course content is organized
5. Final organized content is moved to `/assets/courses/`

## Cleanup

Files in this folder are temporary and can be cleaned up after processing is complete. The organized course content should be found in:
- `/assets/courses/organized_content/downloaded_notebooks/`
- `/assets/courses/organized_content/metadata/`