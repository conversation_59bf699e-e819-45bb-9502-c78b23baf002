{"cells": [{"cell_type": "markdown", "metadata": {"id": "8fszY_BDSReS"}, "source": ["# ModernAI Pro: From Youtube to Audio summaries\n", "\n", "---\n", "\n", "\n", "\n", "\n", "\n", "In this notebook let's use Whisper and ChatGPT to summarize a Youtube video"]}, {"cell_type": "markdown", "metadata": {"id": "bYXuyb_Vbqpi"}, "source": ["## 1.Downloading Youtube videos\n", "Note: The legality of this depends on the geography"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 9988, "status": "ok", "timestamp": 1752376877585, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "Or7eNgoubpAl", "outputId": "6f9e41b3-e571-4951-a2b1-2e34bc2a3113"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[?25l   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/760.2 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K   \u001b[91m━━━━━━━━━━━━━━━\u001b[0m\u001b[90m╺\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m286.7/760.2 kB\u001b[0m \u001b[31m8.3 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m760.2/760.2 kB\u001b[0m \u001b[31m13.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip install -q -U pytubefix\n", "from pytubefix import YouTube"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"executionInfo": {"elapsed": 681, "status": "error", "timestamp": 1752376878267, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "y-_GpdxJcyfv", "colab": {"base_uri": "https://localhost:8080/", "height": 339}, "outputId": "58568c44-4f4a-48b6-ceac-efbfb7a7facb"}, "outputs": [{"output_type": "error", "ename": "BotDetection", "evalue": "zAjJYkUnTEs This request was detected as a bot. Use `use_po_token=True` or switch to WEB client to view. See more details at https://github.com/JuanBindez/pytubefix/pull/209", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mBotDetection\u001b[0m                              Traceback (most recent call last)", "\u001b[0;32m/tmp/ipython-input-2-3774088663.py\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[0;31m#url = \"https://www.youtube.com/watch?v=4Prc1UfuokY\" #thisissparta\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m \u001b[0myt\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mYouTube\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0murl\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 8\u001b[0;31m \u001b[0maudio\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0myt\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mstreams\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfilter\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0monly_audio\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfirst\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdownload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pytubefix/__main__.py\u001b[0m in \u001b[0;36mstreams\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    748\u001b[0m         \u001b[0;34m:\u001b[0m\u001b[0mrtype\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0;34m:\u001b[0m\u001b[0;32mclass\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;31m`\u001b[0m\u001b[0mStreamQuery\u001b[0m \u001b[0;34m<\u001b[0m\u001b[0mStreamQuery\u001b[0m\u001b[0;34m>\u001b[0m\u001b[0;31m`\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    749\u001b[0m         \"\"\"\n\u001b[0;32m--> 750\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcheck_availability\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    751\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mStreamQuery\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfmt_streams\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    752\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pytubefix/__main__.py\u001b[0m in \u001b[0;36mcheck_availability\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    404\u001b[0m                         \u001b[0;34m'Sign in to confirm you’re not a bot'\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    405\u001b[0m                 ):\n\u001b[0;32m--> 406\u001b[0;31m                     \u001b[0;32mraise\u001b[0m \u001b[0mexceptions\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mBotDetection\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mvideo_id\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvideo_id\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    407\u001b[0m                 \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    408\u001b[0m                     \u001b[0;32mraise\u001b[0m \u001b[0mexceptions\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mLoginRequired\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mvideo_id\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvideo_id\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mreason\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mreason\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mBotDetection\u001b[0m: zAjJYkUnTEs This request was detected as a bot. Use `use_po_token=True` or switch to WEB client to view. See more details at https://github.com/JuanBindez/pytubefix/pull/209"]}], "source": ["url = \"https://www.youtube.com/watch?v=zAjJYkUnTEs\" #short\n", "#url = \"https://www.youtube.com/watch?v=suQYoqqomX8\"\n", "#url = \"https://www.youtube.com/watch?v=VqspygBClOg\"\n", "#url = \"https://www.youtube.com/watch?v=T-D1OfcDW1M\"\n", "#url = \"https://www.youtube.com/watch?v=3x4zM7O8pZI\" #bb\n", "#url = \"https://www.youtube.com/watch?v=4Prc1UfuokY\" #thisissparta\n", "yt = YouTube(url)\n", "audio = yt.streams.filter(only_audio=True).first().download()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "48ri70rSegEu", "executionInfo": {"status": "aborted", "timestamp": 1752376878287, "user_tz": -330, "elapsed": 1, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["#Test if the Audio plays before we use whisper\n", "from IPython.display import Audio\n", "Audio(audio, rate = 22050)"]}, {"cell_type": "code", "source": [], "metadata": {"id": "g-Fj3GhmdqpJ", "executionInfo": {"status": "aborted", "timestamp": 1752376878288, "user_tz": -330, "elapsed": 1, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "uNZVt2SehmM-"}, "source": ["## 2. Trancribe Audio with Whisper"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 10905, "status": "aborted", "timestamp": 1752376878288, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}, "user_tz": -330}, "id": "SB1yKSHtQDIL"}, "outputs": [], "source": ["#First we will setup whisper\n", "! pip install git+https://github.com/openai/whisper.git -q\n", "import whisper\n", "model = whisper.load_model(\"tiny\") #for higher quality go for medium or large"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "sUbGor9qh_ng", "executionInfo": {"status": "aborted", "timestamp": 1752376878289, "user_tz": -330, "elapsed": 10905, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["transcript = model.transcribe(audio,fp16=False)['text']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LyqvDUX2Tmsz", "executionInfo": {"status": "aborted", "timestamp": 1752376878289, "user_tz": -330, "elapsed": 10904, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["transcript"]}, {"cell_type": "markdown", "metadata": {"id": "aTTxMXSzkTj_"}, "source": ["## 3. Clean the sentences and summarize with LLMs"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CzKVkJv6H-sx", "executionInfo": {"status": "aborted", "timestamp": 1752376878289, "user_tz": -330, "elapsed": 10903, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["!pip -q install langchain-groq"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hMBJnT5gaTlT", "executionInfo": {"status": "aborted", "timestamp": 1752376878290, "user_tz": -330, "elapsed": 10903, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["from google.colab import userdata\n", "import os\n", "os.environ[\"GROQ_API_KEY\"] = userdata.get(\"GROQ_API_KEY\")\n", "from langchain_groq import ChatGroq\n", "llm = ChatGroq(model_name=\"llama3-70b-8192\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "EPEc9-5Bp98h", "executionInfo": {"status": "aborted", "timestamp": 1752376878290, "user_tz": -330, "elapsed": 10902, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["response = llm.invoke(\"Why is <PERSON> white saying this?: \"+transcript).content\n", "response"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ebbe2WKlqay8", "executionInfo": {"status": "aborted", "timestamp": 1752376878290, "user_tz": -330, "elapsed": 10901, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["def summarize_yt(url, llm):\n", "  yt = YouTube(url)\n", "  audio = yt.streams.filter(only_audio=True).first().download()\n", "  print(\"Audio downloaded\")\n", "\n", "  transcript = model.transcribe(audio,fp16=False)['text']\n", "  print(transcript)\n", "\n", "  result = llm.invoke(\"Guess the movie and provide the context:\"+transcript).content\n", "\n", "  return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "HxnStJ_PrDib", "executionInfo": {"status": "aborted", "timestamp": 1752376878291, "user_tz": -330, "elapsed": 10901, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["text = summarize_yt(\"https://www.youtube.com/watch?v=J-lDwmvpav0\", llm)\n", "text"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "b9iX3oDUJfPl", "executionInfo": {"status": "aborted", "timestamp": 1752376878291, "user_tz": -330, "elapsed": 10900, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["text=summarize_yt(\"https://www.youtube.com/watch?v=yhNWsX9PjeE\",llm)\n", "text"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kWvcNLC_J7d0", "executionInfo": {"status": "aborted", "timestamp": 1752376878304, "user_tz": -330, "elapsed": 10912, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "14398970767757720756"}}}, "outputs": [], "source": ["text=summarize_yt(\"https://www.youtube.com/watch?v=omzW1NPIPvs\",llm)\n", "text"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": [{"file_id": "1jxheMoYc1TZ4K3GSBiK5YKjnsOpnh2Qi", "timestamp": 1687038112421}]}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}