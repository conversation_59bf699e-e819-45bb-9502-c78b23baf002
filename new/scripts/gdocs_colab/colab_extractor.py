#!/usr/bin/env python3
"""
Colab Notebook Content Extractor for Modern AI Pro Courses

This script:
1. Extracts all Colab links from course notebooks
2. Downloads the notebooks using gdown
3. Analyzes content and generates structured JSON metadata
4. Organizes content for easy notebook creation

Author: Claude Code Assistant
Created: August 2025
"""

import json
import re
import os
import sys
from pathlib import Path
import subprocess
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse, parse_qs

# Add the venv to path if needed
SCRIPT_DIR = Path(__file__).parent
VENV_PATH = SCRIPT_DIR / "venv" / "bin" / "activate"

class ColabExtractor:
    def __init__(self, courses_dir: Path, output_dir: Path):
        self.courses_dir = Path(courses_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        self.notebooks_dir = self.output_dir / "downloaded_notebooks"
        self.metadata_dir = self.output_dir / "metadata"
        self.notebooks_dir.mkdir(exist_ok=True)
        self.metadata_dir.mkdir(exist_ok=True)

    def extract_colab_links(self, notebook_path: Path) -> List[Dict[str, Any]]:
        """Extract all Colab links from a notebook with context."""
        with open(notebook_path, 'r', encoding='utf-8') as f:
            notebook = json.load(f)
        
        colab_links = []
        
        for i, cell in enumerate(notebook.get('cells', [])):
            if cell.get('cell_type') == 'markdown':
                source = ''.join(cell.get('source', []))
                
                # Find Colab links with titles
                colab_pattern = r'\[([^\]]+)\]\(https://colab\.research\.google\.com/drive/([a-zA-Z0-9_-]+)[^)]*\)'
                matches = re.findall(colab_pattern, source)
                
                for title, colab_id in matches:
                    colab_url = f"https://colab.research.google.com/drive/{colab_id}"
                    
                    # Extract context from the cell
                    context = self.extract_context(source, title)
                    module_info = self.extract_module_info(source)
                    
                    colab_links.append({
                        'title': title.strip(),
                        'colab_id': colab_id,
                        'colab_url': colab_url,
                        'cell_index': i,
                        'context': context,
                        'module': module_info.get('module_number'),
                        'module_title': module_info.get('module_title'),
                        'schedule': module_info.get('schedule'),
                        'concepts': self.extract_concepts(source)
                    })
        
        return colab_links

    def extract_context(self, source: str, title: str) -> str:
        """Extract surrounding context for a Colab link."""
        lines = source.split('\n')
        context_lines = []
        
        # Find lines around the link
        for i, line in enumerate(lines):
            if title in line:
                # Get surrounding lines
                start = max(0, i - 2)
                end = min(len(lines), i + 3)
                context_lines = lines[start:end]
                break
        
        return '\n'.join(context_lines).strip()

    def extract_module_info(self, source: str) -> Dict[str, Any]:
        """Extract module information from the cell content."""
        module_match = re.search(r'###\s*\*\*Module\s*(\d+)\*\*:\s*([^#\n]+)', source)
        schedule_match = re.search(r'(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)[^,\n]*', source)
        
        return {
            'module_number': int(module_match.group(1)) if module_match else None,
            'module_title': module_match.group(2).strip() if module_match else None,
            'schedule': schedule_match.group(0).strip() if schedule_match else None
        }

    def extract_concepts(self, source: str) -> List[str]:
        """Extract key concepts mentioned in the context."""
        concepts = []
        
        # Look for technical terms and concepts
        concept_patterns = [
            r'\b(LLM|AI|ML|Deep Learning|Neural Network|Transformer|GPT|BERT)\b',
            r'\b(langchain|huggingface|groq|openai|anthropic)\b',
            r'\b(memory|agents|tools|RAG|embedding|vector)\b',
            r'\b(deployment|evaluation|fine-tuning|RLHF)\b'
        ]
        
        for pattern in concept_patterns:
            matches = re.findall(pattern, source, re.IGNORECASE)
            concepts.extend([match.lower() for match in matches])
        
        return list(set(concepts))  # Remove duplicates

    def download_notebook(self, colab_id: str, title: str) -> Optional[Path]:
        """Download a Colab notebook using gdown."""
        try:
            # Clean filename
            safe_title = re.sub(r'[^a-zA-Z0-9_\-\s]', '', title)
            safe_title = re.sub(r'\s+', '_', safe_title.strip())
            filename = f"{colab_id}_{safe_title}.ipynb"
            output_path = self.notebooks_dir / filename
            
            # Use gdown to download
            cmd = f"source {VENV_PATH} && gdown --id {colab_id} -O '{output_path}'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0 and output_path.exists():
                return output_path
            else:
                print(f"Failed to download {colab_id}: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"Error downloading {colab_id}: {e}")
            return None

    def analyze_notebook_content(self, notebook_path: Path) -> Dict[str, Any]:
        """Analyze downloaded notebook content."""
        try:
            with open(notebook_path, 'r', encoding='utf-8') as f:
                notebook = json.load(f)
            
            analysis = {
                'cell_count': len(notebook.get('cells', [])),
                'code_cells': 0,
                'markdown_cells': 0,
                'imports': [],
                'functions_defined': [],
                'key_concepts': [],
                'estimated_time': '30 minutes',  # Default estimate
                'difficulty': 'intermediate'     # Default difficulty
            }
            
            for cell in notebook.get('cells', []):
                cell_type = cell.get('cell_type')
                
                if cell_type == 'code':
                    analysis['code_cells'] += 1
                    source = ''.join(cell.get('source', []))
                    
                    # Extract imports
                    import_matches = re.findall(r'(?:from|import)\s+([a-zA-Z_][a-zA-Z0-9_.]*)', source)
                    analysis['imports'].extend(import_matches)
                    
                    # Extract function definitions
                    func_matches = re.findall(r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)', source)
                    analysis['functions_defined'].extend(func_matches)
                
                elif cell_type == 'markdown':
                    analysis['markdown_cells'] += 1
            
            # Remove duplicates
            analysis['imports'] = list(set(analysis['imports']))
            analysis['functions_defined'] = list(set(analysis['functions_defined']))
            
            # Estimate difficulty based on complexity
            if analysis['code_cells'] > 10 or 'torch' in analysis['imports'] or 'tensorflow' in analysis['imports']:
                analysis['difficulty'] = 'advanced'
            elif analysis['code_cells'] < 5:
                analysis['difficulty'] = 'beginner'
            
            return analysis
            
        except Exception as e:
            print(f"Error analyzing {notebook_path}: {e}")
            return {}

    def process_course(self, notebook_path: Path) -> Dict[str, Any]:
        """Process a complete course notebook."""
        print(f"Processing: {notebook_path.name}")
        
        # Extract all Colab links
        colab_links = self.extract_colab_links(notebook_path)
        print(f"Found {len(colab_links)} Colab notebooks")
        
        # Group by modules
        modules = {}
        for link in colab_links:
            module_num = link.get('module') or 0
            if module_num not in modules:
                modules[module_num] = {
                    'module_id': module_num,
                    'title': link.get('module_title', f'Module {module_num}'),
                    'schedule': link.get('schedule'),
                    'notebooks': []
                }
            
            # Download and analyze notebook
            print(f"Downloading: {link['title']}")
            downloaded_path = self.download_notebook(link['colab_id'], link['title'])
            
            notebook_info = {
                'title': link['title'],
                'colab_id': link['colab_id'],
                'colab_url': link['colab_url'],
                'context': link['context'],
                'concepts': link['concepts'],
                'type': 'practical',
                'downloaded_path': str(downloaded_path) if downloaded_path else None
            }
            
            # Add analysis if download succeeded
            if downloaded_path:
                analysis = self.analyze_notebook_content(downloaded_path)
                notebook_info.update(analysis)
            
            modules[module_num]['notebooks'].append(notebook_info)
        
        # Create course metadata
        course_metadata = {
            'course_metadata': {
                'title': notebook_path.stem.replace('_', ' '),
                'source_file': str(notebook_path),
                'total_modules': len(modules),
                'total_notebooks': len(colab_links),
                'processed_date': '2025-08-07'
            },
            'modules': list(modules.values()),
            'setup_requirements': {
                'required': ['Ollama', 'VSCode', 'Git'],
                'optional': ['Miniconda'],
                'api_keys': ['Groq', 'HuggingFace', 'Tavily', 'Polygon', 'Kaggle', 'Langfuse']
            }
        }
        
        return course_metadata

    def save_metadata(self, metadata: Dict[str, Any], course_name: str):
        """Save processed metadata to JSON file."""
        output_file = self.metadata_dir / f"{course_name}_metadata.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"Metadata saved to: {output_file}")


def main():
    # Set up paths
    script_dir = Path(__file__).parent
    courses_dir = script_dir.parent / "assets" / "courses"
    output_dir = courses_dir / "organized_content"
    
    extractor = ColabExtractor(courses_dir, output_dir)
    
    # Process the Practitioner notebook
    practitioner_notebook = courses_dir / "Modern_AI_Pro_Practitioner_(India)_Aug_2025.ipynb"
    
    if not practitioner_notebook.exists():
        print(f"Notebook not found: {practitioner_notebook}")
        return
    
    # Process and save
    metadata = extractor.process_course(practitioner_notebook)
    extractor.save_metadata(metadata, "practitioner_aug_2025")
    
    print("\n=== Processing Complete ===")
    print(f"Downloaded notebooks: {extractor.notebooks_dir}")
    print(f"Metadata files: {extractor.metadata_dir}")


if __name__ == "__main__":
    main()