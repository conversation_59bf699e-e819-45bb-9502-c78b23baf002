#!/usr/bin/env python3
"""
Facebook Account Manager - Unified Script
PURPOSE: All-in-one Facebook account discovery, health monitoring, and database logging
USAGE: 
  python fb_account_manager.py discover          # Discover accessible accounts
  python fb_account_manager.py health-check      # Daily health monitoring with DB save
  python fb_account_manager.py permissions       # Check token permissions
  python fb_account_manager.py status            # Quick status check
NEEDED: Yes - Single script replaces fb_discover_accounts.py, fb_account_health_check.py, fb_simple_health_check.py
"""

import os
import sys
import json
import sqlite3
import logging
from datetime import datetime, timedelta
from facebook_business.api import FacebookAdsApi
from facebook_business.adobjects.adaccount import AdAccount
from facebook_business.adobjects.user import User
from facebook_business.adobjects.campaign import Campaign
from facebook_business.adobjects.adset import AdSet
from facebook_business.adobjects.ad import Ad
from dotenv import load_dotenv

# Import local config file
from fb_accounts_config import get_active_accounts

# Add scripts root to path for config import
scripts_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, scripts_root)

from config import settings, api_config, setup_logging

# Configuration from centralized config
access_token = api_config.facebook_extended_token
app_secret = api_config.facebook_app_secret
app_id = api_config.facebook_app_id

# Database path from centralized config
DB_PATH = settings.database_path

# Setup logging using centralized config
LOG_PATH = os.path.join(settings.logs_dir, 'fb_ads.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    handlers=[
        logging.FileHandler(LOG_PATH),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('fb_account_manager')

class FacebookAccountManager:
    def __init__(self):
        try:
            FacebookAdsApi.init(app_id=app_id, app_secret=app_secret, access_token=access_token)
            self.api_initialized = True
            logger.info("Facebook API initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize Facebook API: {e}")
            logger.error(f"Failed to initialize Facebook API: {e}")
            self.api_initialized = False
        
        self.results = {}
    
    def check_token_permissions(self):
        """Check what permissions the current token has"""
        print("🔍 Checking token permissions...")
        
        if not self.api_initialized:
            return {
                'granted_permissions': [],
                'essential_status': {},
                'all_essential_granted': False,
                'error': 'API not initialized'
            }
        
        try:
            # Get token info
            me = User(fbid='me')
            permissions = me.get_permissions()
            
            granted_permissions = [perm['permission'] for perm in permissions if perm['status'] == 'granted']
            
            print(f"✅ Granted permissions:")
            for perm in sorted(granted_permissions):
                print(f"  - {perm}")
            
            # Essential permissions for lead management
            essential_permissions = [
                'ads_read',
                'leads_retrieval', 
                'ads_management',
                'business_management'
            ]
            
            permission_status = {}
            for perm in essential_permissions:
                status = perm in granted_permissions
                permission_status[perm] = status
                print(f"{'✅' if status else '❌'} {perm}")
            
            return {
                'granted_permissions': granted_permissions,
                'essential_status': permission_status,
                'all_essential_granted': all(permission_status.values())
            }
            
        except Exception as e:
            print(f"❌ Error checking permissions: {e}")
            return {
                'granted_permissions': [],
                'essential_status': {},
                'all_essential_granted': False,
                'error': str(e)
            }
    
    def discover_accounts_fallback(self):
        """Fallback method using config file directly (API-independent)"""
        print("🔍 Using config-based fallback method...")
        
        try:
            # Get accounts from config file
            active_accounts = get_active_accounts()
            accessible_accounts = []
            
            print("\n=== CHECKING CONFIGURED ACCOUNTS (FALLBACK) ===")
            
            for account_name, account_info in active_accounts.items():
                account_id = account_info['id']
                print(f"✅ {account_name} (ID: {account_id}) - Configured as active")
                
                accessible_accounts.append({
                    'id': account_id,
                    'name': account_name,
                    'status': 'Active (Config)',
                    'accessible': True,
                    'config_name': account_name,
                    'description': account_info.get('description', '')
                })
            
            print(f"\n✅ Found {len(accessible_accounts)} configured accounts")
            
            return {
                'accessible_accounts': accessible_accounts,
                'total_accessible': len(accessible_accounts),
                'permissions': {'fallback_method': True, 'note': 'Using config file - API credentials not available'},
                'timestamp': datetime.now().isoformat(),
                'method': 'config_fallback'
            }
                
        except Exception as e:
            print(f"❌ Config fallback method failed: {e}")
            return {'accessible_accounts': [], 'total_accessible': 0}

    def discover_accounts(self):
        """Discover all accessible Facebook ad accounts"""
        print("🔍 Discovering Facebook ad accounts...")
        
        # Try API method first, fallback to subprocess if needed
        if not self.api_initialized:
            print("⚠️ API not initialized, using fallback method...")
            return self.discover_accounts_fallback()
        
        # Check permissions first
        permission_results = self.check_token_permissions()
        
        # If permissions check fails, use fallback
        if 'error' in permission_results:
            print("⚠️ Permission check failed, using fallback method...")
            return self.discover_accounts_fallback()
        
        print("\n" + "="*60)
        print("=== DISCOVERING FACEBOOK AD ACCOUNTS ===")
        
        # Try to discover accounts via business manager
        discovered_accounts = []
        try:
            me = User(fbid='me')
            ad_accounts = me.get_ad_accounts(fields=['id', 'name', 'account_status'])
            
            for account in ad_accounts:
                discovered_accounts.append({
                    'id': account['id'].replace('act_', ''),
                    'name': account.get('name', 'Unknown'),
                    'status': account.get('account_status', 'Unknown'),
                    'accessible': True
                })
                
        except Exception as e:
            print(f"❌ Error discovering accounts: {e}")
            print("⚠️ Using fallback method...")
            return self.discover_accounts_fallback()
        
        print(f"Discovered {len(discovered_accounts)} accounts automatically")
        
        print("\n" + "="*60)
        print("=== CHECKING TARGET ACCOUNTS FROM CONFIG ===")
        
        # Check configured accounts
        active_accounts = get_active_accounts()
        accessible_accounts = []
        
        for account_name, account_info in active_accounts.items():
            account_id = account_info['id']
            print(f"\nChecking {account_name} (ID: {account_id})...")
            
            try:
                account = AdAccount(f"act_{account_id}")
                account_data = account.api_get(fields=['id', 'name', 'account_status'])
                
                accessible_accounts.append({
                    'id': account_id,
                    'name': account_data.get('name', account_name),
                    'status': account_data.get('account_status', 'Unknown'),
                    'accessible': True,
                    'config_name': account_name
                })
                
                print(f"  ✅ Accessible: {account_data.get('name', account_name)}")
                print(f"     Status: {account_data.get('account_status', 'Unknown')}")
                
            except Exception as e:
                print(f"  ❌ Not accessible: {e}")
                accessible_accounts.append({
                    'id': account_id,
                    'name': account_name,
                    'status': 'Inaccessible',
                    'accessible': False,
                    'config_name': account_name,
                    'error': str(e)
                })
        
        # Summary
        accessible_count = len([acc for acc in accessible_accounts if acc['accessible']])
        
        print(f"\n=== SUMMARY ===")
        print(f"Total accessible accounts: {accessible_count}")
        print(f"Target accounts checked: {len(accessible_accounts)}")
        
        if accessible_count > 0:
            print(f"\n✅ You can pull leads from these accounts:")
            for account in accessible_accounts:
                if account['accessible']:
                    print(f"  - {account['name']} (act_{account['id']})")
        
        return {
            'accessible_accounts': accessible_accounts,
            'total_accessible': accessible_count,
            'permissions': permission_results,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_recent_lead_count(self, account_id, days=7):
        """Get lead count from last N days"""
        if not self.api_initialized:
            return 0
            
        try:
            account = AdAccount(f"act_{account_id}")
            
            # Get date range
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            # Get campaigns
            campaigns = account.get_campaigns(fields=['id'], params={'limit': 50})
            
            total_leads = 0
            for campaign in campaigns:
                try:
                    # Get adsets
                    adsets = Campaign(campaign['id']).get_ad_sets(fields=['id'], params={'limit': 50})
                    
                    for adset in adsets:
                        # Get ads
                        ads = AdSet(adset['id']).get_ads(fields=['id'], params={'limit': 50})
                        
                        for ad in ads:
                            # Get leads for this ad
                            leads = Ad(ad['id']).get_leads(
                                fields=['id'],
                                params={
                                    'filtering': [
                                        {'field': 'time_created', 'operator': 'GREATER_THAN', 'value': int(start_time.timestamp())},
                                        {'field': 'time_created', 'operator': 'LESS_THAN', 'value': int(end_time.timestamp())}
                                    ],
                                    'limit': 1000
                                }
                            )
                            total_leads += len(list(leads))
                            
                except Exception as e:
                    # Skip campaigns/adsets/ads that can't be accessed
                    continue
                    
            return total_leads
            
        except Exception as e:
            print(f"⚠️ Could not get lead count for {account_id}: {e}")
            return 0
    
    def health_check(self, include_lead_count=False):
        """Run comprehensive health check"""
        print("🏥 Starting Facebook Account Health Check...")
        print("=" * 60)
        logger.info("Starting health check")
        
        # Get account discovery results
        discovery_results = self.discover_accounts()
        accessible_count = len([acc for acc in discovery_results['accessible_accounts'] if acc['accessible']])
        logger.info(f"Health check: {accessible_count} accounts accessible")
        
        # Enhanced health check with lead counts if requested
        if include_lead_count:
            print(f"\n🔍 Checking recent lead volume (last 7 days)...")
            total_leads = 0
            for account in discovery_results['accessible_accounts']:
                if account['accessible']:
                    print(f"  Checking {account['name']}...")
                    lead_count = self.get_recent_lead_count(account['id'])
                    account['recent_leads_7d'] = lead_count
                    total_leads += lead_count
                    print(f"    {lead_count} leads in last 7 days")
            logger.info(f"Health check: {total_leads} total leads across all accounts (7d)")
        
        # Store results
        self.results = discovery_results
        
        return discovery_results
    
    def save_to_database(self):
        """Save health check results to database"""
        if not self.results:
            print("❌ No health results to save")
            return False
            
        try:
            # Connect to database
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # Create health table if needed
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS facebook_account_health (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    check_date DATE NOT NULL,
                    account_id TEXT NOT NULL,
                    account_name TEXT NOT NULL,
                    accessible BOOLEAN NOT NULL,
                    account_status TEXT,
                    recent_leads_7d INTEGER DEFAULT 0,
                    error_message TEXT,
                    permissions_status TEXT,
                    last_checked DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(check_date, account_id)
                )
            """)
            
            # Save results
            check_date = datetime.now().date()
            permissions_json = json.dumps(self.results.get('permissions', {}))
            
            for account in self.results['accessible_accounts']:
                # Get campaign and ads count if API is available
                campaign_count = 0
                ads_count = 0
                if self.api_initialized and account['accessible']:
                    campaign_count = self.get_active_campaigns_count(account['id'])
                    ads_count = self.get_active_ads_count(account['id'])
                
                cursor.execute("""
                    INSERT OR REPLACE INTO facebook_account_health 
                    (check_date, account_id, account_name, accessible, account_status, 
                     recent_leads_7d, error_message, permissions_status, active_campaigns, active_ads)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    check_date,
                    account['id'],
                    account['name'],
                    account['accessible'],
                    account.get('status', 'Unknown'),
                    account.get('recent_leads_7d', 0),
                    account.get('error'),
                    permissions_json,
                    campaign_count,
                    ads_count
                ))
            
            conn.commit()
            conn.close()
            
            accessible_count = len([a for a in self.results['accessible_accounts'] if a['accessible']])
            print(f"✅ Health check saved: {accessible_count} accessible accounts")
            logger.info(f"Health check saved to database: {accessible_count} accessible accounts")
            return True
            
        except Exception as e:
            print(f"❌ Database error: {e}")
            logger.error(f"Database save failed: {e}")
            return False
    
    def get_active_campaigns_count(self, account_id):
        """Get count of active campaigns for an account"""
        if not self.api_initialized:
            return 0
            
        try:
            account = AdAccount(f"act_{account_id}")
            campaigns = account.get_campaigns(fields=['id', 'status'])
            active_campaigns = [c for c in campaigns if c.get('status') == 'ACTIVE']
            return len(active_campaigns)
        except Exception as e:
            print(f"⚠️ Could not get campaign count for {account_id}: {e}")
            return 0
    
    def list_campaigns_detailed(self, account_name, account_id):
        """List active campaigns with detailed information"""
        if not self.api_initialized:
            print(f"⚠️ API not initialized, cannot fetch campaigns for {account_name}")
            return []
            
        print(f"\n{'='*60}")
        print(f"🏢 {account_name.upper()} - ACTIVE CAMPAIGNS (ID: {account_id})")
        print(f"{'='*60}")
        
        try:
            account = AdAccount(f"act_{account_id}")
            
            # Get campaigns with detailed fields
            campaigns = account.get_campaigns(fields=[
                'id', 'name', 'status', 'effective_status', 'created_time', 
                'updated_time', 'objective', 'spend_cap', 'daily_budget', 
                'lifetime_budget', 'start_time', 'stop_time'
            ])
            
            # Filter to only ACTIVE campaigns
            active_campaigns = [c for c in campaigns if c.get('status') == 'ACTIVE']
            
            print(f"🟢 Active Campaigns: {len(active_campaigns)}\n")
            
            if not active_campaigns:
                print("❌ No ACTIVE campaigns found in this account")
                return []
            
            # Display active campaigns
            for i, campaign in enumerate(active_campaigns, 1):
                status = campaign.get('status', 'Unknown')
                effective_status = campaign.get('effective_status', 'Unknown')
                
                print(f"{i:2}. ✅ {campaign.get('name', 'Unnamed')}")
                print(f"    ID: {campaign['id']}")
                print(f"    Status: {status} / {effective_status}")
                print(f"    Objective: {campaign.get('objective', 'N/A')}")
                
                if campaign.get('daily_budget') and campaign['daily_budget'] != 'N/A':
                    budget = int(campaign['daily_budget']) / 100  # Convert cents to dollars
                    print(f"    Daily Budget: ${budget:.2f}")
                elif campaign.get('lifetime_budget') and campaign['lifetime_budget'] != 'N/A':
                    budget = int(campaign['lifetime_budget']) / 100
                    print(f"    Lifetime Budget: ${budget:.2f}")
                
                created = campaign.get('created_time', 'N/A')
                print(f"    Created: {created[:10] if created != 'N/A' else 'N/A'}")
                print()
            
            return active_campaigns
            
        except Exception as e:
            print(f"❌ Error accessing campaigns for {account_name}: {e}")
            return []
    
    def get_active_ads_count(self, account_id, campaign_id=None):
        """Get count of active ads from active campaigns only (matches ads_overview logic)"""
        if not self.api_initialized:
            return 0
            
        try:
            account = AdAccount(f"act_{account_id}")
            
            if campaign_id:
                # Get ads for specific campaign
                campaign = Campaign(campaign_id)
                ads = campaign.get_ads(fields=['id', 'status'])
                active_ads = [ad for ad in ads if ad.get('status') == 'ACTIVE']
                return len(active_ads)
            else:
                # Get active ads from ACTIVE campaigns only (not all ads in account)
                campaigns = account.get_campaigns(fields=['id', 'status'])
                active_campaigns = [c for c in campaigns if c.get('status') == 'ACTIVE']
                
                total_active_ads = 0
                for campaign in active_campaigns:
                    try:
                        # Get ACTIVE adsets for this campaign
                        adsets = Campaign(campaign['id']).get_ad_sets(fields=['id', 'status'])
                        active_adsets = [a for a in adsets if a.get('status') == 'ACTIVE']
                        
                        for adset in active_adsets:
                            # Get ACTIVE ads for this adset
                            ads = AdSet(adset['id']).get_ads(fields=['id', 'status'])
                            active_ads = [a for a in ads if a.get('status') == 'ACTIVE']
                            total_active_ads += len(active_ads)
                    except Exception:
                        continue  # Skip campaigns that can't be accessed
                
                return total_active_ads
            
        except Exception as e:
            print(f"⚠️ Could not get ad count for {account_id}: {e}")
            return 0
    
    def list_active_ads_detailed(self, account_name, account_id):
        """List only ACTIVE ads from ACTIVE campaigns to avoid rate limits"""
        if not self.api_initialized:
            print(f"⚠️ API not initialized, cannot fetch ads for {account_name}")
            return []
            
        print(f"\n{'='*60}")
        print(f"📱 {account_name.upper()} - ACTIVE ADS ONLY (ID: {account_id})")
        print(f"{'='*60}")
        
        try:
            account = AdAccount(f"act_{account_id}")
            
            # First get only ACTIVE campaigns to reduce API calls
            campaigns = account.get_campaigns(fields=['id', 'name', 'status'])
            active_campaigns = [c for c in campaigns if c.get('status') == 'ACTIVE']
            
            if not active_campaigns:
                print("❌ No ACTIVE campaigns found")
                return []
            
            total_active_ads = 0
            active_ads_list = []
            
            for campaign in active_campaigns:
                print(f"\n🏢 Campaign: {campaign['name']} (ID: {campaign['id']})")
                
                try:
                    # Get ACTIVE adsets for this campaign
                    adsets = Campaign(campaign['id']).get_ad_sets(fields=['id', 'name', 'status'])
                    active_adsets = [a for a in adsets if a.get('status') == 'ACTIVE']
                    
                    campaign_ads = 0
                    for adset in active_adsets:
                        # Get ACTIVE ads for this adset
                        ads = AdSet(adset['id']).get_ads(fields=['id', 'name', 'status', 'effective_status'])
                        active_ads = [a for a in ads if a.get('status') == 'ACTIVE']
                        
                        for ad in active_ads:
                            campaign_ads += 1
                            total_active_ads += 1
                            active_ads_list.append({
                                'campaign_name': campaign['name'],
                                'campaign_id': campaign['id'],
                                'adset_name': adset['name'],
                                'adset_id': adset['id'],
                                'ad_name': ad['name'],
                                'ad_id': ad['id'],
                                'status': ad.get('status'),
                                'effective_status': ad.get('effective_status')
                            })
                            print(f"  ✅ {ad['name']} (ID: {ad['id']}) - {ad.get('effective_status', 'ACTIVE')}")
                    
                    print(f"  💡 {campaign_ads} active ads in this campaign")
                    
                except Exception as e:
                    print(f"  ❌ Error loading ads for campaign: {e}")
                    continue
            
            print(f"\n📊 TOTAL: {total_active_ads} active ads across {len(active_campaigns)} campaigns")
            return active_ads_list
            
        except Exception as e:
            print(f"❌ Error accessing ads for {account_name}: {e}")
            return []
    
    def ads_overview(self):
        """Show active ads overview across all accounts"""
        print("🚀 FACEBOOK ACTIVE ADS - OVERVIEW")
        print("=" * 80)
        
        # Get account discovery results first
        if not self.results:
            self.discover_accounts()
        
        active_accounts = get_active_accounts()
        all_ads = {}
        
        # Get ads for each accessible account using config
        for account_name, account_info in active_accounts.items():
            account_id = account_info['id']
            ads = self.list_active_ads_detailed(account_name, account_id)
            all_ads[account_name] = ads
        
        # Show summary
        print(f"\n\n{'='*80}")
        print("📊 ACTIVE ADS SUMMARY")
        print(f"{'='*80}")
        
        total_active = 0
        for account_name, ads in all_ads.items():
            count = len(ads)
            total_active += count
            print(f"{account_name}: {count} ACTIVE ads")
        
        print(f"\nTotal ACTIVE ads across all accounts: {total_active}")
        
        return all_ads

    def campaigns_overview(self):
        """Show campaigns overview across all accounts"""
        print("🚀 FACEBOOK ACTIVE CAMPAIGNS - OVERVIEW")
        print("=" * 80)
        
        # Get account discovery results first
        if not self.results:
            self.discover_accounts()
        
        active_accounts = get_active_accounts()
        all_campaigns = {}
        
        # Get campaigns for each accessible account using config
        for account_name, account_info in active_accounts.items():
            account_id = account_info['id']
            campaigns = self.list_campaigns_detailed(account_name, account_id)
            all_campaigns[account_name] = campaigns
        
        # Show summary
        print(f"\n\n{'='*80}")
        print("📊 ACTIVE CAMPAIGNS SUMMARY")
        print(f"{'='*80}")
        
        total_active = 0
        for account_name, campaigns in all_campaigns.items():
            count = len(campaigns)
            total_active += count
            print(f"{account_name}: {count} ACTIVE campaigns")
        
        print(f"\nTotal ACTIVE campaigns across all accounts: {total_active}")
        
        return all_campaigns

    def format_currency(self, amount, currency_code='USD'):
        """Format currency based on Facebook account currency"""
        currency_symbols = {
            'USD': '$',
            'INR': '₹',
            'EUR': '€',
            'GBP': '£',
            'CAD': 'C$',
            'AUD': 'A$'
        }
        symbol = currency_symbols.get(currency_code, currency_code)
        return f'{symbol}{amount:.0f}'

    def get_time_range(self, days):
        """Get time range for metrics"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        return {
            'since': start_date.strftime('%Y-%m-%d'),
            'until': end_date.strftime('%Y-%m-%d')
        }

    def fetch_campaign_metrics_for_account(self, account_id, account_name, time_range):
        """Fetch campaign performance metrics for ACTIVE campaigns only"""
        if not self.api_initialized:
            return None
            
        try:
            account = AdAccount(f"act_{account_id}")
            
            # Get account details including currency
            account_details = account.api_get(fields=['id', 'name', 'currency', 'account_status', 'timezone_name'])
            account_currency = account_details.get('currency', 'USD')
            
            # Get only ACTIVE campaigns
            all_campaigns = account.get_campaigns(fields=['id', 'name', 'status'])
            active_campaigns = [c for c in all_campaigns if c.get('status') == 'ACTIVE']
            
            total_impressions = 0
            total_clicks = 0
            total_spent = 0
            total_leads = 0
            campaign_details = []

            for campaign in active_campaigns:
                campaign_id = campaign['id']
                campaign_name = campaign.get('name', 'Unnamed Campaign')
                
                fields = [
                    'campaign_name',
                    'clicks',
                    'impressions',
                    'reach',
                    'spend',
                    'actions'
                ]
                params = {'time_range': time_range}
                
                try:
                    insights = Campaign(campaign_id).get_insights(fields=fields, params=params)
                    for insight in insights:
                        impressions = int(insight.get('impressions', 0))
                        clicks = int(insight.get('clicks', 0))
                        spend = float(insight.get('spend', 0))
                        actions = insight.get('actions', [])
                        
                        leads = 0
                        for action in actions:
                            if action['action_type'] == 'lead':
                                leads = int(action['value'])
                                break
                        
                        total_impressions += impressions
                        total_clicks += clicks
                        total_spent += spend
                        total_leads += leads
                        
                        cost_per_lead = spend / leads if leads > 0 else 0
                        campaign_details.append({
                            'campaign_id': campaign_id,
                            'campaign_name': campaign_name,
                            'impressions': impressions,
                            'clicks': clicks,
                            'spend': spend,
                            'leads': leads,
                            'cost_per_lead': cost_per_lead
                        })
                        
                except Exception as e:
                    print(f"⚠️ Error fetching insights for campaign {campaign_name}: {e}")
                    continue

            cost_per_lead = total_spent / total_leads if total_leads > 0 else 0
            
            return {
                'account_id': account_id,
                'account_name': account_name,
                'account_currency': account_currency,
                'num_campaigns': len(active_campaigns),
                'total_impressions': total_impressions,
                'total_clicks': total_clicks,
                'total_leads': total_leads,
                'total_spent': total_spent,
                'cost_per_lead': cost_per_lead,
                'campaign_details': campaign_details
            }
            
        except Exception as e:
            print(f"❌ Error fetching campaign metrics for {account_name}: {e}")
            return None

    def fetch_all_campaign_metrics(self, days=7):
        """Fetch campaign metrics for all active accounts"""
        if not self.api_initialized:
            print("⚠️ API not initialized, cannot fetch campaign metrics")
            return {}
            
        active_accounts = get_active_accounts()
        time_range = self.get_time_range(days)
        all_metrics = {}
        
        print(f"🔍 Fetching {days}-day campaign metrics for ACTIVE campaigns...")
        
        for account_name, account_info in active_accounts.items():
            account_id = account_info['id']
            metrics = self.fetch_campaign_metrics_for_account(account_id, account_name, time_range)
            if metrics:
                all_metrics[account_name] = metrics
                print(f"✅ {account_name}: {metrics['num_campaigns']} campaigns, {metrics['total_leads']} leads, {self.format_currency(metrics['total_spent'], metrics['account_currency'])} spent")
        
        return all_metrics

    def save_campaign_metrics_to_database(self, metrics_data, days=7):
        """Save campaign performance metrics to database"""
        if not metrics_data:
            return False
            
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # Create campaign metrics table if needed
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS facebook_campaign_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    check_date DATE NOT NULL,
                    account_id TEXT NOT NULL,
                    account_name TEXT NOT NULL,
                    account_currency TEXT NOT NULL,
                    time_period_days INTEGER NOT NULL,
                    num_active_campaigns INTEGER NOT NULL,
                    total_impressions INTEGER NOT NULL,
                    total_clicks INTEGER NOT NULL,
                    total_leads INTEGER NOT NULL,
                    total_spent REAL NOT NULL,
                    cost_per_lead REAL NOT NULL,
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(check_date, account_id, time_period_days)
                )
            """)
            
            # Create individual campaign performance table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS facebook_campaign_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    check_date DATE NOT NULL,
                    account_id TEXT NOT NULL,
                    account_name TEXT NOT NULL,
                    campaign_id TEXT NOT NULL,
                    campaign_name TEXT NOT NULL,
                    time_period_days INTEGER NOT NULL,
                    impressions INTEGER NOT NULL,
                    clicks INTEGER NOT NULL,
                    leads INTEGER NOT NULL,
                    spend REAL NOT NULL,
                    cost_per_lead REAL NOT NULL,
                    ctr REAL NOT NULL,
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(check_date, campaign_id, time_period_days)
                )
            """)
            
            check_date = datetime.now().date()
            
            # Clear existing campaign performance data for today
            cursor.execute("DELETE FROM facebook_campaign_performance WHERE check_date = ? AND time_period_days = ?", 
                         (check_date, days))
            
            for account_name, metrics in metrics_data.items():
                # Save account-level metrics
                cursor.execute("""
                    INSERT OR REPLACE INTO facebook_campaign_metrics 
                    (check_date, account_id, account_name, account_currency, time_period_days,
                     num_active_campaigns, total_impressions, total_clicks, total_leads, 
                     total_spent, cost_per_lead)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    check_date,
                    metrics['account_id'],
                    metrics['account_name'],
                    metrics['account_currency'],
                    days,
                    metrics['num_campaigns'],
                    metrics['total_impressions'],
                    metrics['total_clicks'],
                    metrics['total_leads'],
                    metrics['total_spent'],
                    metrics['cost_per_lead']
                ))
                
                # Save individual campaign performance data
                campaign_details = metrics.get('campaign_details', [])
                for campaign in campaign_details:
                    if campaign['leads'] > 0:  # Only save campaigns with leads
                        ctr = (campaign['clicks'] / max(1, campaign['impressions'])) * 100
                        
                        cursor.execute("""
                            INSERT OR REPLACE INTO facebook_campaign_performance 
                            (check_date, account_id, account_name, campaign_id, campaign_name,
                             time_period_days, impressions, clicks, leads, spend, cost_per_lead, ctr)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            check_date,
                            metrics['account_id'],
                            metrics['account_name'],
                            campaign['campaign_id'],
                            campaign['campaign_name'],
                            days,
                            campaign['impressions'],
                            campaign['clicks'],
                            campaign['leads'],
                            campaign['spend'],
                            campaign['cost_per_lead'],
                            ctr
                        ))
            
            conn.commit()
            conn.close()
            
            # Count individual campaigns saved
            total_campaigns = sum(len([c for c in metrics.get('campaign_details', []) if c['leads'] > 0]) 
                                for metrics in metrics_data.values())
            
            print(f"✅ Campaign metrics saved for {len(metrics_data)} accounts and {total_campaigns} individual campaigns")
            return True
            
        except Exception as e:
            print(f"❌ Error saving campaign metrics: {e}")
            return False

    def campaign_stats_overview(self, days=7):
        """Show campaign performance overview across all accounts"""
        print(f"🚀 FACEBOOK CAMPAIGN PERFORMANCE - {days} DAYS")
        print("=" * 80)
        
        metrics = self.fetch_all_campaign_metrics(days)
        
        if not metrics:
            print("❌ No campaign metrics available")
            return {}
        
        # Save to database
        if self.save_campaign_metrics_to_database(metrics, days):
            print("✅ Metrics saved to database")
        
        # Show summary
        total_campaigns = sum(m['num_campaigns'] for m in metrics.values())
        total_impressions = sum(m['total_impressions'] for m in metrics.values())
        total_clicks = sum(m['total_clicks'] for m in metrics.values())
        total_leads = sum(m['total_leads'] for m in metrics.values())
        total_spent = sum(m['total_spent'] for m in metrics.values())
        avg_cost_per_lead = total_spent / total_leads if total_leads > 0 else 0
        
        print(f"\n📊 {days}-DAY SUMMARY:")
        print(f"Total Active Campaigns: {total_campaigns}")
        print(f"Total Impressions: {total_impressions:,}")
        print(f"Total Clicks: {total_clicks:,}")
        print(f"Total Leads: {total_leads:,}")
        print(f"Total Spent: Mixed currencies ({total_spent:.0f})")
        print(f"Average Cost per Lead: {avg_cost_per_lead:.0f}")
        
        print(f"\n📋 ACCOUNT BREAKDOWN:")
        for account_name, data in metrics.items():
            print(f"\n✅ {account_name} ({data['account_currency']}):")
            print(f"   Active Campaigns: {data['num_campaigns']}")
            print(f"   Leads Generated: {data['total_leads']}")
            print(f"   Amount Spent: {self.format_currency(data['total_spent'], data['account_currency'])}")
            print(f"   Cost per Lead: {self.format_currency(data['cost_per_lead'], data['account_currency'])}")
        
        return metrics

    def generate_status_summary(self):
        """Generate status summary for monitoring"""
        if not self.results:
            return "No health check data available"
            
        accessible_accounts = [a for a in self.results['accessible_accounts'] if a['accessible']]
        inaccessible_accounts = [a for a in self.results['accessible_accounts'] if not a['accessible']]
        
        total_accounts = len(self.results['accessible_accounts'])
        accessible_count = len(accessible_accounts)
        
        # Determine overall health status
        if accessible_count == 0:
            status = "🔴 CRITICAL - No accounts accessible"
        elif accessible_count == total_accounts:
            status = "🟢 HEALTHY - All accounts accessible"
        else:
            status = f"🟡 WARNING - {accessible_count}/{total_accounts} accounts accessible"
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Build account lists with campaign counts if available
        accessible_list = []
        for acc in accessible_accounts:
            base_info = f"  ✅ {acc['name']} (ID: {acc['id']})"
            # Add campaign count if we have API access
            if self.api_initialized:
                campaign_count = self.get_active_campaigns_count(acc['id'])
                base_info += f" - {campaign_count} active campaigns"
            accessible_list.append(base_info)
        
        accessible_list_str = '\n'.join(accessible_list)
        
        inaccessible_list = ''
        if inaccessible_accounts:
            inaccessible_list = '\n\n❌ Inaccessible Accounts:\n' + \
                              '\n'.join([f"  ❌ {acc['name']} (ID: {acc['id']}) - {acc.get('error', 'Unknown error')}" 
                                       for acc in inaccessible_accounts])
        
        # Permission status
        permissions = self.results.get('permissions', {})
        perm_status = "✅ Complete" if permissions.get('all_essential_granted', False) else "❌ Missing permissions"
        
        # Lead volume summary
        total_leads = sum([acc.get('recent_leads_7d', 0) for acc in accessible_accounts])
        lead_summary = f"• {total_leads} leads in last 7 days" if total_leads > 0 else "• Lead count not checked"
        
        return f"""Facebook Account Health Check - {timestamp}

{status}

📊 Summary:
• {accessible_count}/{total_accounts} accounts accessible
{lead_summary}
• Permissions: {perm_status}

📋 Accessible Accounts:
{accessible_list_str}{inaccessible_list}
"""

    def find_lead_ads(self):
        """Find all lead ads across all accounts and test for accessibility"""
        print("🎯 SEARCHING FOR FACEBOOK LEAD ADS ACROSS ALL ACCOUNTS")
        print("=" * 80)
        
        if not self.api_initialized:
            print("❌ API not initialized")
            return []
            
        active_accounts = get_active_accounts()
        all_lead_ads = []
        
        for account_name, account_info in active_accounts.items():
            account_id = account_info['id']
            print(f"\n📊 {account_name} (ID: {account_id})")
            
            try:
                account = AdAccount(f"act_{account_id}")
                
                # Get all campaigns (including paused ones for historical lead ads)
                campaigns = account.get_campaigns(fields=['id', 'name', 'status', 'objective'])
                
                lead_campaigns = []
                for campaign in campaigns:
                    objective = campaign.get('objective', '')
                    if objective in ['LEAD_GENERATION', 'OUTCOME_LEADS']:
                        lead_campaigns.append(campaign)
                
                print(f"  📋 Found {len(lead_campaigns)} Lead Generation/Outcome campaigns")
                
                if lead_campaigns:
                    account_lead_ads = []
                    
                    for campaign in lead_campaigns[:5]:  # Limit to first 5 to avoid rate limits
                        objective = campaign.get('objective', 'UNKNOWN')
                        print(f"    🏢 {campaign['name']} [{campaign['status']}] - {objective}")
                        
                        try:
                            # Get adsets
                            adsets = Campaign(campaign['id']).get_ad_sets(fields=['id', 'name', 'status'])
                            
                            for adset in list(adsets)[:3]:  # First 3 adsets per campaign
                                # Get ads
                                ads = AdSet(adset['id']).get_ads(fields=['id', 'name', 'status'])
                                
                                for ad in list(ads)[:3]:  # First 3 ads per adset
                                    ad_id = ad['id']
                                    ad_name = ad['name']
                                    ad_status = ad.get('status', 'UNKNOWN')
                                    
                                    # Test if we can access leads from this ad
                                    try:
                                        leads = Ad(ad_id).get_leads(fields=['id'])
                                        lead_count = len(list(leads))
                                        
                                        lead_ad_info = {
                                            'account_name': account_name,
                                            'account_id': account_id,
                                            'campaign_name': campaign['name'],
                                            'campaign_id': campaign['id'],
                                            'campaign_status': campaign['status'],
                                            'adset_name': adset['name'],
                                            'adset_id': adset['id'],
                                            'ad_name': ad_name,
                                            'ad_id': ad_id,
                                            'ad_status': ad_status,
                                            'lead_count': lead_count,
                                            'accessible': True
                                        }
                                        
                                        if lead_count > 0:
                                            print(f"      🎉 {ad_name} (ID: {ad_id}) - {lead_count} leads [{ad_status}]")
                                            
                                            # Get sample lead to verify questionnaire access
                                            try:
                                                sample_leads = Ad(ad_id).get_leads(fields=['id', 'created_time', 'field_data'])
                                                sample_lead = list(sample_leads)[0]
                                                field_count = len(sample_lead.get('field_data', []))
                                                lead_ad_info['sample_fields'] = field_count
                                                print(f"        📝 Sample questionnaire: {field_count} fields")
                                            except:
                                                lead_ad_info['sample_fields'] = 0
                                        else:
                                            print(f"      📭 {ad_name} (ID: {ad_id}) - 0 leads [{ad_status}]")
                                        
                                        account_lead_ads.append(lead_ad_info)
                                        all_lead_ads.append(lead_ad_info)
                                        
                                    except Exception as e:
                                        print(f"      ❌ {ad_name} (ID: {ad_id}) - Cannot access leads: {str(e)[:50]}...")
                                        account_lead_ads.append({
                                            'account_name': account_name,
                                            'account_id': account_id,
                                            'campaign_name': campaign['name'],
                                            'ad_name': ad_name,
                                            'ad_id': ad_id,
                                            'ad_status': ad_status,
                                            'accessible': False,
                                            'error': str(e)[:100]
                                        })
                        
                        except Exception as e:
                            print(f"    ❌ Error processing campaign: {str(e)[:50]}...")
                    
                    print(f"  ✅ Found {len([ad for ad in account_lead_ads if ad.get('accessible')])} accessible lead ads")
                else:
                    print("  ❌ No Lead Generation or Outcome Lead campaigns found")
                    
            except Exception as e:
                print(f"❌ Error accessing account {account_name}: {e}")
        
        # Summary
        print(f"\n{'='*80}")
        print("📊 LEAD ADS SUMMARY")
        print(f"{'='*80}")
        
        accessible_ads = [ad for ad in all_lead_ads if ad.get('accessible')]
        ads_with_leads = [ad for ad in accessible_ads if ad.get('lead_count', 0) > 0]
        
        print(f"Total Lead Ads found: {len(all_lead_ads)}")
        print(f"Accessible Lead Ads: {len(accessible_ads)}")
        print(f"Lead Ads with leads: {len(ads_with_leads)}")
        
        if ads_with_leads:
            print(f"\n🎯 WORKING LEAD ADS (with leads):")
            for ad in ads_with_leads:
                print(f"  • {ad['ad_name']} (ID: {ad['ad_id']}) - {ad['lead_count']} leads")
                print(f"    Account: {ad['account_name']} | Campaign: {ad['campaign_name']}")
        
        return all_lead_ads

    def save_lead_ads_to_database(self, lead_ads_data, check_date=None):
        """Save lead ads data to database"""
        if not check_date:
            check_date = datetime.now().strftime('%Y-%m-%d')
        
        if not lead_ads_data:
            print("⚠️ No lead ads data to save")
            return False
            
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # Clear existing data for today to avoid duplicates
            cursor.execute("DELETE FROM facebook_lead_ads WHERE check_date = ?", (check_date,))
            
            # Insert new lead ads data
            for ad_data in lead_ads_data:
                if not ad_data.get('accessible'):
                    continue  # Skip inaccessible ads
                    
                cursor.execute("""
                    INSERT OR REPLACE INTO facebook_lead_ads 
                    (check_date, account_id, account_name, campaign_id, campaign_name, 
                     campaign_status, campaign_objective, adset_id, adset_name, 
                     ad_id, ad_name, ad_status, total_leads, questionnaire_fields, 
                     accessible, last_updated)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (
                    check_date,
                    ad_data['account_id'],
                    ad_data['account_name'],
                    ad_data['campaign_id'],
                    ad_data['campaign_name'],
                    ad_data['campaign_status'],
                    ad_data.get('campaign_objective', 'UNKNOWN'),
                    ad_data['adset_id'],
                    ad_data['adset_name'],
                    ad_data['ad_id'],
                    ad_data['ad_name'],
                    ad_data['ad_status'],
                    ad_data.get('lead_count', 0),
                    ad_data.get('sample_fields', 0),
                    ad_data.get('accessible', True)
                ))
            
            conn.commit()
            conn.close()
            
            accessible_count = len([ad for ad in lead_ads_data if ad.get('accessible')])
            print(f"✅ Saved {accessible_count} lead ads to database")
            return True
            
        except Exception as e:
            print(f"❌ Error saving lead ads to database: {e}")
            return False

    def lead_ads_with_database_save(self):
        """Find lead ads and save to database - for use in health-check command"""
        print("🎯 FINDING AND STORING LEAD ADS")
        print("=" * 50)
        
        # Get lead ads data but filter for active campaigns only
        lead_ads = self.find_lead_ads_active_only()
        
        # Save to database
        if self.save_lead_ads_to_database(lead_ads):
            working_ads = len([ad for ad in lead_ads if ad.get('accessible') and ad.get('lead_count', 0) > 0])
            total_leads = sum(ad.get('lead_count', 0) for ad in lead_ads if ad.get('accessible'))
            print(f"📊 Summary: {working_ads} working lead ads with {total_leads} total leads")
        
        return lead_ads

    def find_lead_ads_active_only(self):
        """Find lead ads from ACTIVE campaigns only (optimized for health-check)"""
        if not self.api_initialized:
            return []
            
        active_accounts = get_active_accounts()
        all_lead_ads = []
        
        for account_name, account_info in active_accounts.items():
            account_id = account_info['id']
            
            try:
                account = AdAccount(f"act_{account_id}")
                
                # Get ACTIVE campaigns only 
                campaigns = account.get_campaigns(fields=['id', 'name', 'status', 'objective'])
                active_lead_campaigns = [c for c in campaigns 
                                       if c.get('status') == 'ACTIVE' 
                                       and c.get('objective') in ['LEAD_GENERATION', 'OUTCOME_LEADS']]
                
                if not active_lead_campaigns:
                    continue
                    
                print(f"  📊 {account_name}: {len(active_lead_campaigns)} active lead campaigns")
                
                for campaign in active_lead_campaigns:
                    try:
                        # Get ACTIVE adsets
                        adsets = Campaign(campaign['id']).get_ad_sets(fields=['id', 'name', 'status'])
                        active_adsets = [a for a in adsets if a.get('status') == 'ACTIVE']
                        
                        for adset in active_adsets:
                            # Get ACTIVE ads
                            ads = AdSet(adset['id']).get_ads(fields=['id', 'name', 'status'])
                            active_ads = [a for a in ads if a.get('status') == 'ACTIVE']
                            
                            for ad in active_ads:
                                try:
                                    # Get lead count
                                    leads = Ad(ad['id']).get_leads(fields=['id'])
                                    lead_count = len(list(leads))
                                    
                                    # Get sample fields count if there are leads
                                    sample_fields = 0
                                    if lead_count > 0:
                                        try:
                                            sample_leads = Ad(ad['id']).get_leads(fields=['field_data'])
                                            sample_lead = list(sample_leads)[0]
                                            sample_fields = len(sample_lead.get('field_data', []))
                                        except:
                                            pass
                                    
                                    lead_ad_info = {
                                        'account_name': account_name,
                                        'account_id': account_id,
                                        'campaign_name': campaign['name'],
                                        'campaign_id': campaign['id'],
                                        'campaign_status': campaign['status'],
                                        'campaign_objective': campaign.get('objective', 'UNKNOWN'),
                                        'adset_name': adset['name'],
                                        'adset_id': adset['id'],
                                        'ad_name': ad['name'],
                                        'ad_id': ad['id'],
                                        'ad_status': ad.get('status'),
                                        'lead_count': lead_count,
                                        'sample_fields': sample_fields,
                                        'accessible': True
                                    }
                                    
                                    all_lead_ads.append(lead_ad_info)
                                    
                                    if lead_count > 0:
                                        print(f"    ✅ {ad['name']} (ID: {ad['id']}) - {lead_count} leads")
                                    
                                except Exception as e:
                                    print(f"    ❌ {ad['name']} - Cannot access: {str(e)[:50]}...")
                                    
                    except Exception as e:
                        print(f"  ❌ Error processing campaign {campaign['name']}: {str(e)[:50]}...")
                        
            except Exception as e:
                print(f"❌ Error accessing {account_name}: {e}")
        
        return all_lead_ads

def main():
    """Main function with command-line interface"""
    if len(sys.argv) < 2:
        print("""Facebook Account Manager - Unified Tool

Usage:
  python fb_account_manager.py discover          # Discover and list accessible accounts
  python fb_account_manager.py health-check      # Full health check with database save
  python fb_account_manager.py permissions       # Check token permissions only
  python fb_account_manager.py status            # Quick status check
  python fb_account_manager.py leads-count       # Health check with lead volume
  python fb_account_manager.py campaigns         # List active campaigns across all accounts
  python fb_account_manager.py ads               # List active ads across all accounts
  python fb_account_manager.py find-leads        # Find all lead ads and test accessibility
  python fb_account_manager.py stats [days]      # Campaign performance stats (default: 7 days)

Examples:
  python fb_account_manager.py discover          # For initial setup
  python fb_account_manager.py health-check      # For daily cron jobs
  python fb_account_manager.py status            # For quick monitoring
  python fb_account_manager.py campaigns         # For campaign overview
  python fb_account_manager.py ads               # For active ads overview
  python fb_account_manager.py find-leads        # For finding accessible lead ads
  python fb_account_manager.py stats 7           # 7-day campaign performance
  python fb_account_manager.py stats 30          # 30-day campaign performance
""")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    manager = FacebookAccountManager()
    
    # Execute based on command
    if command == 'discover':
        print("Facebook Ad Account Discovery Tool\n")
        results = manager.discover_accounts()
        
        # Exit with status code based on results
        if results['total_accessible'] == 0:
            sys.exit(2)  # Critical - no accounts
        else:
            sys.exit(0)  # Success
            
    elif command == 'health-check':
        print("Facebook Account Health Check\n")
        results = manager.health_check()
        
        # Save to database
        if manager.save_to_database():
            print("✅ Health check results saved to database")
        
        # Also fetch and save campaign stats as part of health check
        print("\n🔄 Fetching 1-day (today) campaign performance metrics...")
        campaign_metrics_1d = manager.campaign_stats_overview(days=1)
        
        print("\n🔄 Fetching 7-day campaign performance metrics...")
        campaign_metrics_7d = manager.campaign_stats_overview(days=7)
        
        # Fetch 30-day metrics too for dashboard trend analysis
        print("\n🔄 Fetching 30-day campaign performance metrics...")
        campaign_metrics_30d = manager.campaign_stats_overview(days=30)
        
        # Also collect and store lead ads data
        print("\n🎯 Collecting lead ads data...")
        lead_ads_data = manager.lead_ads_with_database_save()
        
        # Generate and print summary
        summary = manager.generate_status_summary()
        print("\n" + "="*60)
        print(summary)
        
        # Exit with appropriate status code
        if results['total_accessible'] == 0:
            sys.exit(2)  # Critical
        elif results['total_accessible'] < len(results['accessible_accounts']):
            sys.exit(1)  # Warning
        else:
            sys.exit(0)  # Success
            
    elif command == 'leads-count':
        print("Facebook Account Health Check with Lead Volume\n")
        results = manager.health_check(include_lead_count=True)
        
        # Save to database
        if manager.save_to_database():
            print("✅ Results saved to database")
        
        # Generate and print summary
        summary = manager.generate_status_summary()
        print("\n" + "="*60)
        print(summary)
        
        # Exit with appropriate status code
        if results['total_accessible'] == 0:
            sys.exit(2)  # Critical
        else:
            sys.exit(0)  # Success
            
    elif command == 'permissions':
        print("Facebook Token Permissions Check\n")
        permissions = manager.check_token_permissions()
        
        if permissions.get('all_essential_granted', False):
            print("\n✅ All essential permissions granted")
            sys.exit(0)
        else:
            print("\n❌ Missing essential permissions")
            missing = [k for k, v in permissions.get('essential_status', {}).items() if not v]
            print(f"Missing: {', '.join(missing)}")
            sys.exit(1)
            
    elif command == 'status':
        print("Facebook Accounts Quick Status\n")
        results = manager.health_check()
        summary = manager.generate_status_summary()
        print(summary)
        
        # Exit with status code
        if results['total_accessible'] == 0:
            sys.exit(2)
        elif results['total_accessible'] < len(results['accessible_accounts']):
            sys.exit(1)
        else:
            sys.exit(0)
            
    elif command == 'campaigns':
        print("Facebook Active Campaigns Overview\n")
        campaigns = manager.campaigns_overview()
        
        # Exit with status code based on whether we found campaigns
        total_campaigns = sum(len(clist) for clist in campaigns.values())
        if total_campaigns == 0:
            sys.exit(2)  # No campaigns found
        else:
            sys.exit(0)  # Success
            
    elif command == 'ads':
        print("Facebook Active Ads Overview\n")
        ads = manager.ads_overview()
        
        # Exit with status code based on whether we found ads
        total_ads = sum(len(alist) for alist in ads.values())
        if total_ads == 0:
            sys.exit(2)  # No ads found
        else:
            sys.exit(0)  # Success
            
    elif command == 'find-leads':
        print("Facebook Lead Ads Discovery\n")
        lead_ads = manager.find_lead_ads()
        
        # Exit with appropriate code
        working_ads = len([ad for ad in lead_ads if ad.get('accessible') and ad.get('lead_count', 0) > 0])
        if working_ads == 0:
            sys.exit(2)  # Critical - no working lead ads found
        else:
            sys.exit(0)  # Success
            
    elif command == 'stats':
        # Get days parameter (default: 7)
        days = 7
        if len(sys.argv) > 2:
            try:
                days = int(sys.argv[2])
                if days < 1 or days > 90:
                    print("⚠️ Days must be between 1 and 90. Using default: 7")
                    days = 7
            except ValueError:
                print("⚠️ Invalid days parameter. Using default: 7")
                days = 7
        
        print(f"Facebook Campaign Performance Stats ({days} days)\n")
        metrics = manager.campaign_stats_overview(days)
        
        # Exit with status code based on whether we found metrics
        if not metrics:
            sys.exit(2)  # No metrics found
        else:
            sys.exit(0)  # Success
            
    else:
        print(f"❌ Unknown command: {command}")
        print("Available commands: discover, health-check, permissions, status, leads-count, campaigns, ads, find-leads, stats")
        sys.exit(1)

if __name__ == "__main__":
    main()