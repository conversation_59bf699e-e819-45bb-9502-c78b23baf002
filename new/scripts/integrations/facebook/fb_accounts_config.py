#!/usr/bin/env python3
"""
Facebook Ad Accounts Configuration
Central place to manage all your ad accounts and their settings
"""

# All your ad accounts (updated from discovery results)
AD_ACCOUNTS = {
    "Invento Robotics": {
        "id": "****************",
        "description": "Main Invento Robotics account",
        "active": True,
        "expected_forms": ["AI Bootcamp", "Lead Gen Form"]
    },
    "Invento_US": {
        "id": "****************",
        "description": "US market account",
        "active": True,
        "expected_forms": ["Lead Gen Form - US"]
    },
    "Young AI Pro": {
        "id": "***************",
        "description": "Young AI Pro account",
        "active": True,
        "expected_forms": ["AI Workshop", "Lead Gen Form"]
    }
}

# Currency exchange rates (USD as base currency)
EXCHANGE_RATES = {
    'USD': 1.0,  # Base currency
    'INR': 83.25,  # Current USD to INR rate (update as needed)
    'EUR': 0.85,   # USD to EUR
    'GBP': 0.73,   # USD to GBP
    'CAD': 1.25,   # USD to CAD
    'AUD': 1.35    # USD to AUD
}

# Default currency for calculations
DEFAULT_CURRENCY = 'USD'

# Common field mappings for lead forms
FIELD_MAPPINGS = {
    # Standard fields
    'full_name': ['full_name', 'name', 'Name'],
    'first_name': ['first_name', 'firstName', 'First Name'],
    'last_name': ['last_name', 'lastName', 'Last Name'],
    'email': ['email', 'email_address', 'Email', 'Email address'],
    'phone': ['phone_number', 'phone', 'Phone', 'Phone number (recommended)'],
    'city': ['city', 'City', 'which_city_will_you_be_attending_this_from?'],
    
    # AI/Bootcamp specific fields
    'ai_experience': [
        'what_is_your_level_of_familiarity_with_artificial_intelligence_and_deep_learning?',
        'ai_experience',
        'experience_level'
    ],
    'expectations': [
        'what_are_your_expectations_from_this_bootcamp?',
        'which_of_these_goals_fit_your_needs_the_best?',
        'expectations',
        'course_expectations'
    ],
    'python_experience': [
        'do_you_know_python_language?',
        'what_is_your_programming_language_proficiency?',
        'python_exp',
        'python_knowledge'
    ],
    'other_languages': [
        'do_you_know_any_other_programming_language?',
        'other_lang',
        'programming_languages'
    ],
    'job_title': [
        'job_title',
        'current_title',
        'title'
    ]
}

def get_active_accounts():
    """Get list of active ad accounts"""
    return {name: info for name, info in AD_ACCOUNTS.items() if info['active']}

def get_account_by_id(account_id):
    """Find account info by ID"""
    for name, info in AD_ACCOUNTS.items():
        if info['id'] == account_id:
            return name, info
    return None, None

def get_field_value(field_data, field_category):
    """Extract field value using flexible field mapping"""
    possible_names = FIELD_MAPPINGS.get(field_category, [field_category])
    
    for field in field_data:
        field_name = field.get('name', '')
        if field_name in possible_names:
            values = field.get('values', [])
            return values[0] if values else 'N/A'
    return 'N/A'

def get_full_name(field_data):
    """Extract full name from either full_name field or first_name/last_name combination"""
    # First try to get full_name directly
    full_name = get_field_value(field_data, 'full_name')
    if full_name != 'N/A':
        return full_name
    
    # If no full_name, try to construct from first_name + last_name
    first_name = get_field_value(field_data, 'first_name')
    last_name = get_field_value(field_data, 'last_name')
    
    # Build full name from parts
    name_parts = []
    if first_name != 'N/A':
        name_parts.append(first_name)
    if last_name != 'N/A':
        name_parts.append(last_name)
    
    if name_parts:
        return ' '.join(name_parts)
    
    return 'N/A'

def convert_currency(amount, from_currency, to_currency=DEFAULT_CURRENCY):
    """Convert amount from one currency to another"""
    try:
        if from_currency == to_currency:
            return float(amount)
        
        from_rate = EXCHANGE_RATES.get(from_currency.upper(), 1.0)
        to_rate = EXCHANGE_RATES.get(to_currency.upper(), 1.0)
        
        # Convert to USD first, then to target currency
        usd_amount = float(amount) / from_rate
        converted_amount = usd_amount * to_rate
        
        return converted_amount
    except (ValueError, TypeError, ZeroDivisionError):
        return 0.0

def get_exchange_rate(from_currency, to_currency=DEFAULT_CURRENCY):
    """Get exchange rate between two currencies"""
    try:
        from_rate = EXCHANGE_RATES.get(from_currency.upper(), 1.0)
        to_rate = EXCHANGE_RATES.get(to_currency.upper(), 1.0)
        return to_rate / from_rate
    except ZeroDivisionError:
        return 1.0

def normalize_currency_amounts(amounts_with_currencies):
    """Normalize a list of (amount, currency) tuples to USD for calculations"""
    normalized_amounts = []
    for amount, currency in amounts_with_currencies:
        normalized_amount = convert_currency(amount, currency, DEFAULT_CURRENCY)
        normalized_amounts.append(normalized_amount)
    return normalized_amounts

if __name__ == "__main__":
    print("=== FACEBOOK AD ACCOUNTS CONFIGURATION ===\n")
    
    active_accounts = get_active_accounts()
    print(f"Active accounts: {len(active_accounts)}")
    
    for name, info in active_accounts.items():
        print(f"\n{name}:")
        print(f"  ID: {info['id']}")
        print(f"  Description: {info['description']}")
        print(f"  Expected forms: {', '.join(info['expected_forms'])}")
    
    print(f"\nTotal field mappings: {len(FIELD_MAPPINGS)}")
    print("Field categories:", list(FIELD_MAPPINGS.keys()))
    
    # Test currency conversion
    print(f"\n=== CURRENCY CONVERSION TEST ===")
    print(f"Current USD-INR rate: {EXCHANGE_RATES['INR']}")
    print(f"Convert ₹100 to USD: ${convert_currency(100, 'INR', 'USD'):.2f}")
    print(f"Convert $10 to INR: ₹{convert_currency(10, 'USD', 'INR'):.2f}")
    
    # Test average calculation with mixed currencies
    mixed_costs = [(100, 'USD'), (8325, 'INR'), (50, 'USD')]  # $100, ₹8325 (~$100), $50
    normalized = normalize_currency_amounts(mixed_costs)
    avg_usd = sum(normalized) / len(normalized)
    print(f"Mixed currency average: ${avg_usd:.2f} USD")