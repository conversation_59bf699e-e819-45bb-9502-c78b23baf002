#!/usr/bin/env python3
"""
PURPOSE: Convert short-lived Facebook token to long-lived token (60 days)
USAGE: Exchanges FB_ACCESS_TOKEN for extended token to prevent frequent expiration
NEEDED: Yes - Essential for maintaining API access without manual token refresh
"""

import requests
from dotenv import load_dotenv
import os

load_dotenv('../config/.env')

# Get the necessary credentials from the .env file
app_id = os.getenv("FB_APP_ID")
app_secret = os.getenv("FB_APP_SECRET")
short_lived_token = os.getenv("FB_ACCESS_TOKEN")

# Define the URL for exchanging the token
url = f"https://graph.facebook.com/v16.0/oauth/access_token"
params = {
    'grant_type': 'fb_exchange_token',
    'client_id': app_id,
    'client_secret': app_secret,
    'fb_exchange_token': short_lived_token
}

# Make the request
response = requests.get(url, params=params)

# Check if the request was successful
if response.status_code == 200:
    data = response.json()
    long_lived_token = data['access_token']
    print(f"Long-Lived Access Token: {long_lived_token}")
else:
    print(f"Error: {response.status_code}")
    print(response.json())
