#!/usr/bin/env python3
"""
PURPOSE: Optimized Facebook account health check (every 4 hours)
USAGE: Minimal logging, essential metrics only, production-ready
NEEDED: Yes - Core production health monitoring for lead automation
"""

import sys
import os
import sqlite3
import json
from datetime import datetime, timedelta, timezone

# Add scripts root to path for config import
scripts_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, scripts_root)

# Add current directory to path for local imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import settings, api_config
from facebook_business.api import FacebookAdsApi
from facebook_business.adobjects.adaccount import AdAccount
from facebook_business.adobjects.user import User
from fb_accounts_config import get_active_accounts

class OptimizedHealthChecker:
    def __init__(self):
        """Initialize Facebook API and paths"""
        # Facebook API credentials
        self.access_token = api_config.facebook_extended_token
        self.app_secret = api_config.facebook_app_secret
        self.app_id = api_config.facebook_app_id
        
        # Initialize Facebook API
        try:
            FacebookAdsApi.init(self.app_id, self.app_secret, self.access_token)
            self.api_initialized = True
        except Exception as e:
            self.api_initialized = False
        
        # Database and log paths
        self.db_path = settings.database_path
        self.health_log = os.path.join(settings.logs_dir, 'fb_campaign_health.log')

    def check_account_accessibility(self):
        """Check if configured accounts are accessible"""
        if not self.api_initialized:
            return {'accessible': 0, 'total': 0, 'accounts': []}
        
        active_accounts = get_active_accounts()
        accessible_accounts = []
        accessible_count = 0
        
        for account_name, account_info in active_accounts.items():
            account_id = account_info['id']
            
            try:
                account = AdAccount(f"act_{account_id}")
                account_data = account.api_get(fields=['id', 'name', 'account_status'])
                accessible_count += 1
                accessible_accounts.append({
                    'name': account_name,
                    'id': account_id,
                    'status': account_data.get('account_status', 'Unknown'),
                    'accessible': True
                })
            except Exception as e:
                accessible_accounts.append({
                    'name': account_name,
                    'id': account_id,
                    'accessible': False,
                    'error': str(e)[:50]
                })
        
        return {
            'accessible': accessible_count,
            'total': len(active_accounts),
            'accounts': accessible_accounts
        }

    def get_active_ads_count(self):
        """Get total count of active ads from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) FROM facebook_lead_ads 
                WHERE campaign_status = 'ACTIVE' 
                AND ad_status = 'ACTIVE'
            """)
            
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else 0
            
        except Exception as e:
            return 0

    def get_7day_lead_summary(self):
        """Get 7-day Facebook lead summary from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Count ONLY Facebook leads from last 7 days
            cursor.execute("""
                SELECT COUNT(*), 
                       AVG(CASE WHEN lead_score > 0 THEN lead_score ELSE NULL END) as avg_score
                FROM leads 
                WHERE datetime(substr(created_time, 1, 19)) >= datetime('now', '-7 days')
                AND data_source = 'facebook'
            """)
            
            result = cursor.fetchone()
            lead_count = result[0] if result else 0
            avg_score = result[1] if result and result[1] else 0
            
            # Get accurate cost data from fixed campaign metrics (USD converted)
            cursor.execute("""
                SELECT SUM(total_spent), SUM(total_leads)
                FROM facebook_campaign_metrics 
                WHERE check_date >= date('now', '-7 days')
                AND time_period_days = 7
            """)
            
            cost_result = cursor.fetchone()
            total_spent = cost_result[0] if cost_result and cost_result[0] else 0
            total_leads_metrics = cost_result[1] if cost_result and cost_result[1] else 0
            
            # Calculate cost per lead (fallback to $3.55 based on 133 leads/$472.79)
            if total_leads_metrics > 0 and total_spent > 0:
                cost_per_lead = total_spent / total_leads_metrics
            else:
                cost_per_lead = 3.55  # Based on corrected data: $472.79/133 leads
            
            conn.close()
            
            return {
                'leads_7d': lead_count,
                'avg_score': round(avg_score, 1) if avg_score else 0,
                'avg_cost_per_lead': round(cost_per_lead, 2)
            }
            
        except Exception as e:
            return {'leads_7d': 0, 'avg_score': 0, 'avg_cost_per_lead': 3.55}

    def check_api_status(self):
        """Quick API status check"""
        if not self.api_initialized:
            return 'FAILED'
        
        try:
            # Simple API call to verify connectivity
            me = User(fbid='me')
            permissions = me.get_permissions()
            return 'OK'
        except Exception as e:
            return 'ERROR'

    def get_critical_alerts(self):
        """Check for critical issues requiring immediate attention"""
        alerts = []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check for zero leads in last 6 hours
            cursor.execute("""
                SELECT COUNT(*) FROM leads 
                WHERE datetime(substr(created_time, 1, 19)) >= datetime('now', '-6 hours')
            """)
            result = cursor.fetchone()
            recent_leads = result[0] if result else 0
            
            if recent_leads == 0:
                alerts.append("No leads in 6+ hours")
            
            # Check for accounts with zero active ads
            cursor.execute("""
                SELECT COUNT(*) FROM facebook_lead_ads 
                WHERE campaign_status = 'ACTIVE' AND ad_status = 'ACTIVE'
            """)
            result = cursor.fetchone()
            active_ads = result[0] if result else 0
            
            if active_ads == 0:
                alerts.append("No active ads")
            
            conn.close()
            
        except Exception as e:
            alerts.append("Database check failed")
        
        return alerts

    def cleanup_inactive_ads(self):
        """Remove ads from active list if they had no leads in past 24 hours"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get currently active ads
            cursor.execute("""
                SELECT ad_id, ad_name, campaign_name 
                FROM facebook_lead_ads 
                WHERE campaign_status = 'ACTIVE' AND ad_status = 'ACTIVE'
            """)
            active_ads = cursor.fetchall()
            
            deactivated_count = 0
            
            for ad_id, ad_name, campaign_name in active_ads:
                # Check if this ad had any leads in past 24 hours
                cursor.execute("""
                    SELECT COUNT(*) FROM leads 
                    WHERE fb_ad_id = ? 
                    AND datetime(substr(created_time, 1, 19)) >= datetime('now', '-24 hours')
                """, (ad_id,))
                
                result = cursor.fetchone()
                recent_leads = result[0] if result else 0
                
                if recent_leads == 0:
                    # No leads in 24 hours - mark as inactive
                    cursor.execute("""
                        UPDATE facebook_lead_ads 
                        SET campaign_status = 'PAUSED', ad_status = 'PAUSED'
                        WHERE ad_id = ?
                    """, (ad_id,))
                    deactivated_count += 1
            
            conn.commit()
            conn.close()
            
            return deactivated_count
            
        except Exception as e:
            return 0

    def run_health_check(self):
        """Run optimized health check with minimal logging"""
        try:
            # First, cleanup ads with no recent leads
            deactivated_ads = self.cleanup_inactive_ads()
            
            # Check account accessibility
            account_status = self.check_account_accessibility()
            
            # Get active ads count (after cleanup)
            active_ads_count = self.get_active_ads_count()
            
            # Check API status
            api_status = self.check_api_status()
            
            # Get 7-day lead summary
            lead_summary = self.get_7day_lead_summary()
            
            # Check for critical alerts
            alerts = self.get_critical_alerts()
            
            # Add cleanup info to alerts if ads were deactivated
            if deactivated_ads > 0:
                alerts.append(f"Deactivated {deactivated_ads} idle ads")
            
            # Generate minimal log entries (3 lines max) - use UTC
            timestamp_str = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
            
            log_lines = [
                f"{timestamp_str}, {account_status['accessible']} accounts accessible, {active_ads_count} active ads, API: {api_status}",
                f"{timestamp_str}, Total leads 7d: {lead_summary['leads_7d']}, Cost/lead: ${lead_summary['avg_cost_per_lead']:.2f}",
            ]
            
            if alerts:
                log_lines.append(f"{timestamp_str}, Alerts: {', '.join(alerts)}")
            else:
                log_lines.append(f"{timestamp_str}, Alerts: None")
            
            # Write to log file
            with open(self.health_log, 'a') as f:
                for line in log_lines:
                    f.write(line + '\n')
            
            return {
                'status': 'success',
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'accounts_accessible': account_status['accessible'],
                'total_accounts': account_status['total'],
                'active_ads': active_ads_count,
                'api_status': api_status,
                'leads_7d': lead_summary['leads_7d'],
                'cost_per_lead': lead_summary['avg_cost_per_lead'],
                'alerts': alerts
            }
            
        except Exception as e:
            # Log error
            timestamp_str = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
            error_line = f"{timestamp_str}, HEALTH_ERROR, {str(e)}"
            
            with open(self.health_log, 'a') as f:
                f.write(error_line + '\n')
            
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

    def save_health_metrics_to_database(self, health_data):
        """Save health check results to database for dashboard"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create health metrics table if needed
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS facebook_health_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    check_date DATE NOT NULL,
                    check_time TIME NOT NULL,
                    accounts_accessible INTEGER NOT NULL,
                    total_accounts INTEGER NOT NULL,
                    active_ads INTEGER NOT NULL,
                    api_status TEXT NOT NULL,
                    leads_7d INTEGER NOT NULL,
                    cost_per_lead REAL NOT NULL,
                    alerts_count INTEGER NOT NULL,
                    alerts_text TEXT,
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(check_date, check_time)
                )
            """)
            
            check_date = datetime.now().date()
            check_time = datetime.now().time().strftime('%H:%M:%S')
            
            cursor.execute("""
                INSERT OR REPLACE INTO facebook_health_metrics 
                (check_date, check_time, accounts_accessible, total_accounts, 
                 active_ads, api_status, leads_7d, cost_per_lead, 
                 alerts_count, alerts_text)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                check_date,
                check_time,
                health_data['accounts_accessible'],
                health_data['total_accounts'],
                health_data['active_ads'],
                health_data['api_status'],
                health_data['leads_7d'],
                health_data['cost_per_lead'],
                len(health_data['alerts']),
                ', '.join(health_data['alerts']) if health_data['alerts'] else None
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            return False

def main():
    """Main function"""
    try:
        checker = OptimizedHealthChecker()
        result = checker.run_health_check()
        
        # Save to database for dashboard
        if result['status'] == 'success':
            checker.save_health_metrics_to_database(result)
        
        # Exit with appropriate code for cron monitoring
        if result['status'] == 'success':
            # Check for critical conditions
            if result['accounts_accessible'] == 0:
                sys.exit(2)  # Critical - no accounts accessible
            elif result['alerts']:
                sys.exit(1)  # Warning - alerts present
            else:
                sys.exit(0)  # All good
        else:
            sys.exit(2)  # Critical error
            
    except Exception as e:
        # Critical error - write to log
        timestamp_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        error_line = f"{timestamp_str}, CRITICAL_HEALTH, {str(e)}"
        try:
            logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), "new", "data", "logs")
            with open(os.path.join(logs_dir, 'fb_campaign_health.log'), 'a') as f:
                f.write(error_line + '\n')
        except:
            pass
        sys.exit(2)

if __name__ == "__main__":
    main()