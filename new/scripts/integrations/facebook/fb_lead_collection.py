#!/usr/bin/env python3
"""
PURPOSE: Optimized Facebook lead collection with timestamp recovery (every 5 minutes)
USAGE: Minimal logging, timestamp-based recovery, 2x faster than rate-limited version
NEEDED: Yes - Core production lead collection for 400+ leads/day target
"""

import sys
import os
import sqlite3
import json
import time
from datetime import datetime, timedelta, timezone
import re

# Add scripts root to path for config import
scripts_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, scripts_root)

# Add current directory to path for local imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import settings, api_config
from facebook_business.api import FacebookAdsApi
from facebook_business.adobjects.ad import Ad
from fb_accounts_config import get_active_accounts, get_field_value, get_full_name
from lead_assignment_logic import get_lead_assignment

class OptimizedLeadCollector:
    def __init__(self):
        """Initialize Facebook API and paths"""
        # Facebook API credentials
        self.access_token = api_config.facebook_extended_token
        self.app_secret = api_config.facebook_app_secret
        self.app_id = api_config.facebook_app_id
        
        # Initialize Facebook API
        FacebookAdsApi.init(self.app_id, self.app_secret, self.access_token)
        
        # Database and log paths
        self.db_path = settings.database_path
        self.leads_log = os.path.join(settings.logs_dir, 'fb_leads.log')
        
    def get_last_successful_timestamp(self):
        """Get last successful collection timestamp from log or database (UTC)"""
        # PRODUCTION FIX: Use proper timestamp recovery
        
        # Try database first - get latest processed lead
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT MAX(created_time) FROM leads 
                WHERE data_source = 'facebook' 
                AND created_time IS NOT NULL
            """)
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0]:
                # Facebook timestamps are already UTC
                fb_time = result[0].replace('Z', '').replace('+0000', '')
                last_db_time = datetime.fromisoformat(fb_time).replace(tzinfo=timezone.utc)
                # Go back 6 hours from last DB lead to catch any missed leads
                return last_db_time - timedelta(hours=6)
        except:
            pass
        
        # Fallback: last 48 hours UTC (wider net to catch all recent leads)
        return datetime.now(timezone.utc) - timedelta(hours=48)
        
        # Original logic (commented out for debugging)
        # Try log file first (faster)
        # if os.path.exists(self.leads_log):
        #     try:
        #         with open(self.leads_log, 'r') as f:
        #             lines = f.readlines()
        #             if lines:
        #                 last_line = lines[-1].strip()
        #                 # Parse format: "2025-08-14 10:05:01, TOTAL, 3 leads"
        #                 timestamp_match = re.match(r'^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', last_line)
        #                 if timestamp_match:
        #                     # Convert local log time to UTC
        #                     local_time = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
        #                     return local_time.replace(tzinfo=timezone.utc)
        #     except:
        #         pass

    def get_active_ads(self):
        """Get all active ads from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT DISTINCT account_name, campaign_id, campaign_name, ad_id, ad_name
                FROM facebook_lead_ads 
                WHERE campaign_status = 'ACTIVE' 
                AND ad_status = 'ACTIVE'
                ORDER BY account_name, campaign_name
            """)
            
            results = cursor.fetchall()
            conn.close()
            
            return [{'account_name': r[0], 'campaign_id': r[1], 'campaign_name': r[2], 
                    'ad_id': r[3], 'ad_name': r[4]} for r in results]
            
        except Exception as e:
            return []

    def save_lead_to_database(self, lead_data):
        """Save lead to database (returns True if new lead)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if lead exists
            cursor.execute("SELECT id FROM leads WHERE facebook_lead_id = ?", (lead_data['facebook_lead_id'],))
            existing = cursor.fetchone()
            if existing:
                print(f"DEBUG: Lead {lead_data['facebook_lead_id']} already exists in DB")
                conn.close()
                return False
            
            # Get assignment
            assigned_to, assignee_name, assignment_reason = get_lead_assignment(lead_data['facebook_lead_id'])
            
            # Create notes with raw form data and assignment info
            raw_form_data = json.dumps(lead_data.get('raw_field_data', []))
            notes_content = f"Raw fields: {raw_form_data} | Auto-assigned: {assignee_name} ({assignment_reason})"
            
            # Insert new lead with minimal processing - store raw form data in notes
            cursor.execute("""
                INSERT INTO leads (
                    facebook_lead_id, full_name, email, phone, campaign_name, 
                    account_name, campaign_id, account_id, fb_ad_id, lead_source, 
                    created_time, data_source, notes, status, assigned_to
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                lead_data['facebook_lead_id'],
                lead_data['full_name'],
                lead_data['email'],
                lead_data['phone'],
                lead_data['campaign_name'],
                lead_data['account_name'],
                lead_data['campaign_id'],
                lead_data.get('account_id', ''),
                lead_data['ad_id'],
                'fb_ads',
                lead_data['created_time'],
                'facebook',
                notes_content,
                'New',
                assigned_to
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"DEBUG: Error saving lead {lead_data.get('facebook_lead_id', 'unknown')}: {e}")
            if 'conn' in locals():
                conn.close()
            return False

    def collect_leads_since_timestamp(self, since_timestamp):
        """Collect leads from all active ads since timestamp"""
        active_ads = self.get_active_ads()
        print(f"DEBUG: Found {len(active_ads)} active ads")
        for ad in active_ads:
            print(f"  - {ad['ad_name']} (ID: {ad['ad_id']}) in {ad['account_name']}")
        
        if not active_ads:
            print("DEBUG: No active ads found!")
            return 0
        
        total_new_leads = 0
        active_accounts = get_active_accounts()
        print(f"DEBUG: Active accounts: {list(active_accounts.keys())}")
        
        # Group ads by account for efficient processing
        ads_by_account = {}
        for ad in active_ads:
            account_name = ad['account_name']
            if account_name not in ads_by_account:
                ads_by_account[account_name] = []
            ads_by_account[account_name].append(ad)
        
        for account_name, ads in ads_by_account.items():
            if account_name not in active_accounts:
                continue
                
            account_id = active_accounts[account_name]['id']
            account_leads = 0
            
            for ad in ads:
                print(f"DEBUG: Querying ad {ad['ad_id']} ({ad['ad_name']})")
                try:
                    # Query leads since timestamp
                    leads = Ad(ad['ad_id']).get_leads(
                        fields=['id', 'created_time', 'field_data'],
                        params={'limit': 50}  # Higher limit for 5-min intervals
                    )
                    
                    print(f"DEBUG: Facebook API returned {len(leads)} leads")
                    leads_processed = 0
                    leads_too_old = 0
                    
                    for lead in leads:
                        # Facebook timestamps are UTC (e.g., "2025-08-14T00:56:16+0000")
                        fb_time = lead['created_time'].replace('Z', '').replace('+0000', '')
                        lead_created = datetime.fromisoformat(fb_time).replace(tzinfo=timezone.utc)
                        
                        # Only process leads newer than our timestamp (both UTC)
                        if lead_created <= since_timestamp:
                            leads_too_old += 1
                            if leads_too_old <= 3:  # Show first 3 examples
                                print(f"DEBUG: Lead {lead['id']} too old: {lead_created} <= {since_timestamp}")
                            continue
                        
                        leads_processed += 1
                        print(f"DEBUG: Processing lead {lead['id']} created at {lead_created}")
                        
                        # Extract only essential fields, store raw data as JSON
                        lead_data = {
                            'facebook_lead_id': lead['id'],
                            'created_time': lead['created_time'],
                            'campaign_name': ad['campaign_name'],
                            'campaign_id': ad['campaign_id'],
                            'account_name': account_name,
                            'account_id': account_id,
                            'ad_id': ad['ad_id'],
                            'full_name': get_full_name(lead['field_data']),
                            'email': get_field_value(lead['field_data'], 'email'),
                            'phone': get_field_value(lead['field_data'], 'phone'),
                            'raw_field_data': lead['field_data']  # Store complete form data as-is
                        }
                        
                        # Save to database
                        if self.save_lead_to_database(lead_data):
                            account_leads += 1
                            total_new_leads += 1
                            print(f"DEBUG: Saved lead {lead['id']} to database")
                        else:
                            print(f"DEBUG: Failed to save lead {lead['id']} (likely duplicate)")
                    
                    print(f"DEBUG: Ad {ad['ad_id']} summary: {leads_processed} processed, {leads_too_old} too old")
                    
                except Exception as e:
                    # Log error but continue with next ad
                    print(f"DEBUG: Error querying ad {ad['ad_id']}: {e}")
                    pass
                
                # Small delay between ads
                time.sleep(1)
            
            # Log account results
            if account_leads > 0:
                timestamp_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                log_line = f"{timestamp_str}, {account_name}, {account_leads} leads\n"
                with open(self.leads_log, 'a') as f:
                    f.write(log_line)
        
        return total_new_leads

    def run_collection(self):
        """Main collection function with timestamp recovery"""
        try:
            # Get last successful timestamp
            since_timestamp = self.get_last_successful_timestamp()
            
            # Log what timestamp we're using (keep for production monitoring)
            
            # Collect new leads
            new_leads = self.collect_leads_since_timestamp(since_timestamp)
            
            # Log summary (single line) - use UTC for consistency
            timestamp_str = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
            if new_leads > 0:
                log_line = f"{timestamp_str}, TOTAL, {new_leads} leads\n"
            else:
                log_line = f"{timestamp_str}, TOTAL, 0 leads (since {since_timestamp.strftime('%m-%d %H:%M UTC')})\n"
            
            with open(self.leads_log, 'a') as f:
                f.write(log_line)
            
            return {
                'status': 'success' if new_leads > 0 else 'no_new_leads',
                'leads_count': new_leads,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'since_timestamp': since_timestamp.isoformat()
            }
            
        except Exception as e:
            # Log error
            timestamp_str = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
            error_line = f"{timestamp_str}, ERROR, {str(e)}\n"
            with open(self.leads_log, 'a') as f:
                f.write(error_line)
            
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

def main():
    """Main function"""
    try:
        collector = OptimizedLeadCollector()
        result = collector.run_collection()
        
        # Exit with appropriate code for cron monitoring
        if result['status'] in ['success', 'no_new_leads']:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        # Critical error - write to log
        timestamp_str = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
        error_line = f"{timestamp_str}, CRITICAL, {str(e)}\n"
        try:
            logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), "new", "data", "logs")
            with open(os.path.join(logs_dir, 'fb_leads.log'), 'a') as f:
                f.write(error_line)
        except:
            pass
        sys.exit(2)

if __name__ == "__main__":
    main()