#!/usr/bin/env python3
"""
Lead Assignment Logic for Facebook Leads
Dynamic assignment based on YAML configuration
"""

import hashlib
import yaml
from pathlib import Path

def load_assignment_config():
    """Load lead assignment configuration from YAML file"""
    config_path = Path(__file__).parent.parent.parent.parent / "config" / "lead_assignment.yaml"
    
    try:
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"Warning: Could not load assignment config: {e}")
        # Fallback to default configuration
        return {
            'manish_percentage': 100,
            'mahalakshmi_percentage': 0,
            'manish_id': 3,
            'mahalakshmi_id': 9
        }

def get_lead_assignment(lead_id):
    """
    Determine lead assignment based on configuration
    
    Args:
        lead_id (str): Unique lead identifier (Facebook lead ID)
        
    Returns:
        tuple: (user_id, assignee_name, assignment_reason)
    """
    config = load_assignment_config()
    
    manish_id = config['manish_id']
    mahalakshmi_id = config['mahalakshmi_id']
    manish_percentage = config['manish_percentage']
    mahalakshmi_percentage = config['mahalakshmi_percentage']
    
    # Use deterministic hash-based assignment for consistency
    hash_value = int(hashlib.md5(str(lead_id).encode()).hexdigest(), 16)
    assignment_value = hash_value % 100
    
    if assignment_value < manish_percentage:
        return manish_id, "Manish", f"{manish_percentage}% assignment ratio"
    else:
        return mahalakshmi_id, "Mahalakshmi", f"{mahalakshmi_percentage}% assignment ratio"

def is_indian_phone(phone):
    """
    Check if phone number is Indian
    Used for existing leads assignment (not Facebook leads)
    """
    if not phone or phone == 'N/A' or phone == '':
        return False
    
    # Clean the phone number
    clean_phone = phone.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
    
    # Check for Indian numbers
    return (clean_phone.startswith('+91') or 
            clean_phone.startswith('91') or
            (len(clean_phone) == 10 and clean_phone[0] in '6789'))

def test_distribution():
    """
    Test the distribution logic with sample lead IDs
    """
    print("🧪 Testing Lead Assignment Distribution")
    print("=" * 50)
    
    # Test with 1000 sample lead IDs
    manish_count = 0
    mahalakshmi_count = 0
    
    for i in range(1000):
        lead_id = f"test_lead_{i}"
        user_id, name, reason = get_lead_assignment(lead_id)
        
        if user_id == MANISH_ID:
            manish_count += 1
        else:
            mahalakshmi_count += 1
    
    manish_percentage = (manish_count / 1000) * 100
    mahalakshmi_percentage = (mahalakshmi_count / 1000) * 100
    
    print(f"📊 Results from 1000 test leads:")
    print(f"Manish: {manish_count} leads ({manish_percentage:.1f}%)")
    print(f"Mahalakshmi: {mahalakshmi_count} leads ({mahalakshmi_percentage:.1f}%)")
    print(f"Target: 20% Manish, 80% Mahalakshmi")
    
    # Check if within acceptable range (±3%)
    if 17 <= manish_percentage <= 23 and 77 <= mahalakshmi_percentage <= 83:
        print("✅ Distribution is within acceptable range!")
    else:
        print("⚠️ Distribution is outside expected range")
    
    # Show sample assignments
    print(f"\n🔍 Sample assignments:")
    for i in range(10):
        lead_id = f"sample_{i}"
        user_id, name, reason = get_lead_assignment(lead_id)
        print(f"  Lead {lead_id} → {name}")

if __name__ == "__main__":
    test_distribution()