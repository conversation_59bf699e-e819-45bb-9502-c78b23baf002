#!/usr/bin/env python3
"""
PURPOSE: Monitor Facebook lead collection cron jobs and rate limiting
USAGE: Check status, performance, and health of automated lead collection
NEEDED: Yes - Essential for monitoring production lead collection
"""

import os
import json
import sqlite3
from datetime import datetime, timedelta
from glob import glob

class CronJobMonitor:
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.logs_dir = os.path.join(self.script_dir, 'logs')
        self.state_file = os.path.join(self.script_dir, 'rate_limit_state.json')
        self.db_path = '/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db'

    def check_recent_activity(self):
        """Check recent lead collection activity"""
        print("📊 RECENT ACTIVITY SUMMARY")
        print("=" * 40)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Leads in last hour
            cursor.execute("""
                SELECT COUNT(*) FROM leads 
                WHERE lead_source = 'fb_ads' 
                AND datetime(created_time) >= datetime('now', '-1 hour')
            """)
            last_hour = cursor.fetchone()[0]
            
            # Leads in last 24 hours
            cursor.execute("""
                SELECT COUNT(*) FROM leads 
                WHERE lead_source = 'fb_ads' 
                AND datetime(created_time) >= datetime('now', '-1 day')
            """)
            last_day = cursor.fetchone()[0]
            
            # Leads by account (last 24 hours)
            cursor.execute("""
                SELECT account_name, COUNT(*) as count
                FROM leads 
                WHERE lead_source = 'fb_ads' 
                AND datetime(created_time) >= datetime('now', '-1 day')
                GROUP BY account_name
                ORDER BY count DESC
            """)
            by_account = cursor.fetchall()
            
            conn.close()
            
            print(f"🕐 Last hour:     {last_hour} leads")
            print(f"📅 Last 24 hours: {last_day} leads")
            print()
            
            if by_account:
                print("📈 By account (last 24h):")
                for account, count in by_account:
                    print(f"  {account}: {count} leads")
            else:
                print("  No leads found in last 24 hours")
                
        except Exception as e:
            print(f"❌ Error checking database: {e}")
        
        print()

    def check_rate_limiting_status(self):
        """Check rate limiting rotation status"""
        print("🔄 RATE LIMITING STATUS")
        print("=" * 40)
        
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r') as f:
                    state = json.load(f)
                
                print(f"📍 Current account index: {state.get('account_index', 0)}")
                print(f"🔢 Total runs: {state.get('total_runs', 0)}")
                print(f"📝 Accounts in rotation: {len(state.get('accounts_list', []))}")
                
                if state.get('accounts_list'):
                    print("   Accounts:")
                    for i, account in enumerate(state['accounts_list']):
                        marker = " 👈 NEXT" if i == state.get('account_index', 0) else ""
                        print(f"     {i}: {account}{marker}")
                
                if state.get('last_run'):
                    last_run = datetime.fromisoformat(state['last_run'])
                    minutes_ago = (datetime.now() - last_run).total_seconds() / 60
                    print(f"⏰ Last run: {minutes_ago:.1f} minutes ago")
                    
                    if minutes_ago > 5:
                        print("   ⚠️  Warning: No recent activity (cron job may be down)")
                else:
                    print("⚠️  No previous runs recorded")
                    
            except Exception as e:
                print(f"❌ Error reading state file: {e}")
        else:
            print("⚠️  No state file found (rate-limited script hasn't run yet)")
        
        print()

    def check_log_files(self):
        """Check recent log files for errors"""
        print("📋 LOG FILE STATUS")
        print("=" * 40)
        
        if not os.path.exists(self.logs_dir):
            print("⚠️  Logs directory doesn't exist")
            return
        
        today = datetime.now().strftime('%Y%m%d')
        
        # Check rate-limited leads log
        rate_log = os.path.join(self.logs_dir, f'rate_limited_leads_{today}.log')
        if os.path.exists(rate_log):
            size = os.path.getsize(rate_log)
            print(f"✅ Rate-limited leads log: {size} bytes")
            
            # Check for recent errors
            try:
                with open(rate_log, 'r') as f:
                    lines = f.readlines()[-20:]  # Last 20 lines
                    error_lines = [line for line in lines if '❌' in line or 'ERROR' in line.upper()]
                    if error_lines:
                        print(f"   ⚠️  {len(error_lines)} recent errors found")
                        for error in error_lines[-3:]:  # Show last 3 errors
                            print(f"     {error.strip()}")
                    else:
                        print("   ✅ No recent errors")
            except Exception as e:
                print(f"   ❌ Error reading log: {e}")
        else:
            print(f"⚠️  No rate-limited leads log for today")
        
        # Check daily health log
        health_log = os.path.join(self.logs_dir, f'daily_health_{today}.log')
        if os.path.exists(health_log):
            size = os.path.getsize(health_log)
            print(f"✅ Daily health log: {size} bytes")
        else:
            print(f"ℹ️  No daily health log for today (runs at 6 AM)")
        
        print()

    def estimate_api_usage(self):
        """Estimate Facebook API usage"""
        print("📡 API USAGE ESTIMATE")
        print("=" * 40)
        
        try:
            with open(self.state_file, 'r') as f:
                state = json.load(f)
            
            total_runs = state.get('total_runs', 0)
            accounts_per_run = 2  # Max accounts per run
            api_calls_per_account = 2  # Optimized: direct lead calls only (no campaign/ad discovery)
            
            estimated_calls = total_runs * accounts_per_run * api_calls_per_account
            
            print(f"🏃 Total runs: {total_runs}")
            print(f"📞 Estimated API calls: {estimated_calls}")
            print(f"⚡ Calls per minute: ~{accounts_per_run * api_calls_per_account}")
            print(f"📊 Daily estimate: ~{accounts_per_run * api_calls_per_account * 60 * 24:,} calls")
            
            # Facebook rate limits (approximate)
            print()
            print("📋 Facebook Rate Limits (approximate):")
            print("   - Ad Account calls: 25,000/day")
            print("   - Marketing API: 200 calls/hour per user")
            print("   - Our usage: Very conservative, well within limits")
            
        except Exception as e:
            print(f"❌ Error estimating usage: {e}")
        
        print()

    def show_recommendations(self):
        """Show optimization recommendations"""
        print("💡 RECOMMENDATIONS")
        print("=" * 40)
        
        recommendations = []
        
        # Check if cron jobs are running
        if not os.path.exists(self.state_file):
            recommendations.append("🚀 Run setup_cron_jobs.sh to start automated collection")
        
        # Check for recent activity
        try:
            with open(self.state_file, 'r') as f:
                state = json.load(f)
            
            if state.get('last_run'):
                last_run = datetime.fromisoformat(state['last_run'])
                minutes_ago = (datetime.now() - last_run).total_seconds() / 60
                
                if minutes_ago > 5:
                    recommendations.append("⚠️  Check if cron jobs are running (no recent activity)")
                elif minutes_ago < 1:
                    recommendations.append("✅ System is actively collecting leads")
        except:
            pass
        
        # Check log files for errors
        today = datetime.now().strftime('%Y%m%d')
        rate_log = os.path.join(self.logs_dir, f'rate_limited_leads_{today}.log')
        if os.path.exists(rate_log):
            try:
                with open(rate_log, 'r') as f:
                    content = f.read()
                    if '❌' in content or 'ERROR' in content.upper():
                        recommendations.append("🔍 Check logs for errors and resolve issues")
            except:
                pass
        
        if not recommendations:
            recommendations.append("✅ System appears to be running smoothly")
        
        for rec in recommendations:
            print(f"  {rec}")
        
        print()

def main():
    """Main monitoring function"""
    print(f"🔍 Facebook Lead Collection Monitor")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    monitor = CronJobMonitor()
    
    monitor.check_recent_activity()
    monitor.check_rate_limiting_status()
    monitor.check_log_files()
    monitor.estimate_api_usage()
    monitor.show_recommendations()
    
    print("🎯 Use this script regularly to monitor lead collection health")

if __name__ == "__main__":
    main()