#!/bin/bash

# Facebook Lead Management Cron Jobs Setup
# This script sets up automated polling for Facebook leads and account health

echo "🚀 Setting up Facebook Lead Management Cron Jobs..."

# Get the absolute path to the scripts directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_PATH="/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/venv"

# Create logs directory if it doesn't exist
mkdir -p "$SCRIPT_DIR/logs"

echo "📂 Script directory: $SCRIPT_DIR"
echo "🐍 Virtual environment: $VENV_PATH"

# Function to add cron job if it doesn't exist
add_cron_job() {
    local cron_line="$1"
    local description="$2"
    
    # Check if cron job already exists
    if crontab -l 2>/dev/null | grep -F "$cron_line" > /dev/null; then
        echo "✅ $description already exists in crontab"
    else
        # Add the cron job
        (crontab -l 2>/dev/null; echo "$cron_line") | crontab -
        echo "➕ Added: $description"
    fi
}

# Create wrapper scripts for better logging
echo "📝 Creating wrapper scripts..."

# 1. Rate-limited leads puller (every minute)
cat > "$SCRIPT_DIR/cron_rate_limited_leads.sh" << EOF
#!/bin/bash
cd "$SCRIPT_DIR"
export PATH="$VENV_PATH/bin:\$PATH"
python3 fb_rate_limited_leads.py >> logs/rate_limited_leads_\$(date +%Y%m%d).log 2>&1
EOF

# 2. Daily account health check
cat > "$SCRIPT_DIR/cron_daily_health.sh" << EOF
#!/bin/bash
cd "$SCRIPT_DIR"
export PATH="$VENV_PATH/bin:\$PATH"
python3 fb_account_manager.py health-check >> logs/daily_health_\$(date +%Y%m%d).log 2>&1
EOF

# Make scripts executable
chmod +x "$SCRIPT_DIR/cron_rate_limited_leads.sh"
chmod +x "$SCRIPT_DIR/cron_daily_health.sh"

echo "✅ Wrapper scripts created and made executable"

# Add cron jobs
echo "⏰ Setting up cron jobs..."

# Every 10 minutes: Rate-limited lead collection (conservative rate limiting)
add_cron_job "*/10 * * * * $SCRIPT_DIR/cron_rate_limited_leads.sh" "Rate-limited leads collection (every 10 minutes)"

# Every 4 hours: Account health check to update active ads database
add_cron_job "0 2,6,10,14,18,22 * * * $SCRIPT_DIR/cron_daily_health.sh" "Account health check every 4 hours"

# Log cleanup: Keep only last 7 days of logs (daily at 7 AM)
add_cron_job "0 7 * * * find $SCRIPT_DIR/logs -name '*.log' -mtime +7 -delete" "Log cleanup (keep 7 days)"

echo ""
echo "✅ Cron jobs setup complete!"
echo ""
echo "📋 Current schedule:"
echo "  Every 10 minutes: Rate-limited lead collection (1-2 accounts per run)"
echo "  Every 4 hours:    Account health check (2,6,10,14,18,22)"
echo "  Daily 7 AM:       Log cleanup (keep 7 days)"
echo ""
echo "📁 Logs will be saved to: $SCRIPT_DIR/logs/"
echo ""
echo "🔍 To view current cron jobs:"
echo "  crontab -l"
echo ""
echo "📊 To monitor logs:"
echo "  tail -f $SCRIPT_DIR/logs/rate_limited_leads_\$(date +%Y%m%d).log"
echo "  tail -f $SCRIPT_DIR/logs/daily_health_\$(date +%Y%m%d).log"
echo ""
echo "⚠️  Note: Make sure the Python virtual environment is activated and all dependencies are installed."
echo ""

# Test the scripts
echo "🧪 Testing scripts..."

echo "Testing rate-limited leads script:"
if "$SCRIPT_DIR/cron_rate_limited_leads.sh"; then
    echo "✅ Rate-limited leads script test passed"
else
    echo "❌ Rate-limited leads script test failed"
fi

echo "Testing daily health script:"
if timeout 30s "$SCRIPT_DIR/cron_daily_health.sh"; then
    echo "✅ Daily health script test passed"
else
    echo "⚠️  Daily health script test timed out (this is normal if it takes a while)"
fi

echo ""
echo "🎯 Setup complete! Lead collection will start automatically."
echo "   Check logs in a few minutes to verify everything is working."