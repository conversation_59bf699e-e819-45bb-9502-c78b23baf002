#!/usr/bin/env python3
"""
Generic LLM Email Generator
===========================
Generic wrapper for Azure OpenAI LLM calls for email generation
"""

import os
from typing import Dict, Optional, Tuple
from dotenv import load_dotenv

# Load environment variables
load_dotenv("/Users/<USER>/Code/modernaipro/mai-administrative/new/config/.env")

try:
    from openai import AzureOpenAI
    print("✅ OpenAI library imported successfully")
except ImportError:
    print("❌ OpenAI library not found. Install with: pip install openai")
    exit(1)


class EmailGenerator:
    """Generic LLM wrapper for email generation"""
    
    def __init__(self):
        """Initialize the LLM client"""
        self.client = AzureOpenAI(
            api_version="2024-10-21",
            azure_endpoint=os.getenv('AZURE_ENDPOINT'),
            api_key=os.getenv('AZURE_API_KEY'),
        )
        self.deployment = os.getenv('AZURE_DEPLOYMENT', 'gpt-5-mini')
    
    def generate_email(
        self, 
        system_prompt: str,
        user_prompt: str
    ) -> Tuple[str, bool, str]:
        """
        Generic LLM email generation
        
        Args:
            system_prompt: System prompt for the LLM
            user_prompt: User prompt containing lead data and instructions
            
        Returns:
            Tuple of (email_content, success_flag, model_used)
        """
        try:
            # Generate email using LLM
            # IMPORTANT: Do NOT add max_tokens, max_completion_tokens, temperature, 
            # top_p, or other parameters - GPT-5 doesn't support them and will return empty results
            response = self.client.chat.completions.create(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                model=self.deployment
            )
            
            email_content = response.choices[0].message.content
            model_used = response.model or self.deployment
            
            return email_content, True, model_used
            
        except Exception as e:
            print(f"❌ Email generation failed: {e}")
            if "unsupported" in str(e).lower() or "invalid" in str(e).lower():
                print("💡 Note: GPT-5/o1 models don't support deprecated parameters")
            return f"Error generating email: {e}", False, "error"


if __name__ == "__main__":
    print("📧 Generic Email Generator - Use from enhanced_email_composer.py")