#!/usr/bin/env python3
"""
GPT-5 Chat Test
==============
Simple test to verify GPT-5 API connectivity and basic chat functionality
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv("../../config/.env")

try:
    from openai import AzureOpenAI
    print("✅ OpenAI library imported successfully")
except ImportError:
    print("❌ OpenAI library not found. Install with: pip install openai")
    exit(1)

def test_gpt5_chat():
    """Test basic GPT-5 chat functionality"""
    
    # Get configuration from .env
    endpoint = os.getenv('AZURE_ENDPOINT')
    model_name = os.getenv('AZURE_DEPLOYMENT', 'gpt-5-mini')
    deployment = os.getenv('AZURE_DEPLOYMENT', 'gpt-5-mini')
    subscription_key = os.getenv('AZURE_API_KEY')
    api_version = "2024-10-21"  # Latest stable API version
    
    print(f"🔧 Configuration:")
    print(f"   Endpoint: {endpoint}")
    print(f"   Model: {model_name}")
    print(f"   Deployment: {deployment}")
    print(f"   API Version: {api_version}")
    print(f"   API Key: {'*' * (len(subscription_key) - 4) + subscription_key[-4:] if subscription_key else 'None'}")
    
    if not all([endpoint, subscription_key]):
        print("❌ Missing required environment variables")
        return
    
    try:
        # Initialize Azure OpenAI client
        client = AzureOpenAI(
            api_version=api_version,
            azure_endpoint=endpoint,
            api_key=subscription_key,
        )
        
        print("\n🚀 Testing GPT-5 basic chat...")
        
        # Simple chat test
        response = client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful assistant specialized in CRM and sales.",
                },
                {
                    "role": "user",
                    "content": "Hello! Can you help me reach to a guy with sales issues.",
                }
            ],
            model=deployment
        )
        
        print("✅ GPT-5 Response:")
        print("-" * 50)
        content = response.choices[0].message.content
        print(f"Content: '{content}'" if content else "No content returned")
        print(f"Finish reason: {response.choices[0].finish_reason}")
        print("-" * 50)
        print(f"📊 Usage: {response.usage}")
        
        # Check for reasoning tokens (new feature in GPT-5)
        if hasattr(response.usage, 'completion_tokens_details') and hasattr(response.usage.completion_tokens_details, 'reasoning_tokens'):
            print(f"🧠 Reasoning tokens: {response.usage.completion_tokens_details.reasoning_tokens}")
        
    except Exception as e:
        print(f"❌ GPT-5 test failed: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Provide specific guidance for common parameter errors
        if "unsupported" in str(e).lower() or "invalid" in str(e).lower():
            print("\n💡 Note: GPT-5/o1 models don't support temperature, top_p, presence_penalty, frequency_penalty, or max_tokens")
            print("   Use max_completion_tokens, reasoning_effort, and verbosity instead")


def test_gpt5_vision():
    """Test GPT-5 vision capabilities with a simple image"""
    
    # Get configuration from .env
    endpoint = os.getenv('AZURE_ENDPOINT')
    deployment = os.getenv('AZURE_DEPLOYMENT', 'gpt-5')
    subscription_key = os.getenv('AZURE_API_KEY')
    api_version = "2024-10-21"  # Latest stable API version
    
    # Test image path
    test_image = "/Users/<USER>/Code/amc8/assets/AMC prep/2024-AMC8-Problems_pages/page_02.png"
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        return
    
    try:
        import base64
        
        # Encode image
        with open(test_image, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')
        
        client = AzureOpenAI(
            api_version=api_version,
            azure_endpoint=endpoint,
            api_key=subscription_key,
        )
        
        print("\n🔍 Testing GPT-5 vision capabilities...")
        
        response = client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert at analyzing mathematical competition problems.",
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "How many problems are on this page? Just give me a simple count."},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
                    ]
                }
            ],
            max_completion_tokens=1000,  # Required parameter for GPT-5/o1 models
            reasoning_effort="medium",  # Controls reasoning depth (minimal/low/medium/high)
            model=deployment
        )
        
        print("✅ GPT-5 Vision Response:")
        print("-" * 50)
        content = response.choices[0].message.content
        print(f"Content: '{content}'" if content else "No content returned")
        print(f"Finish reason: {response.choices[0].finish_reason}")
        print("-" * 50)
        print(f"📊 Usage: {response.usage}")
        
        # Check for reasoning tokens
        if hasattr(response.usage, 'completion_tokens_details') and hasattr(response.usage.completion_tokens_details, 'reasoning_tokens'):
            print(f"🧠 Reasoning tokens: {response.usage.completion_tokens_details.reasoning_tokens}")
        
    except Exception as e:
        print(f"❌ GPT-5 vision test failed: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Provide specific guidance for common parameter errors
        if "unsupported" in str(e).lower() or "invalid" in str(e).lower():
            print("\n💡 Note: GPT-5/o1 models don't support temperature, top_p, presence_penalty, frequency_penalty, or max_tokens")
            print("   Use max_completion_tokens, reasoning_effort, and verbosity instead")


if __name__ == "__main__":
    print("🧪 GPT-5 API Testing")
    print("=" * 40)
    
    # Test basic chat
    test_gpt5_chat()
    
    # Test vision capabilities
    test_gpt5_vision()
    
    print("\n🎯 Testing complete!")