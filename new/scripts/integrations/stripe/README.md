# Stripe Webhook Setup Guide

## Overview
This Python-based Stripe webhook system handles payment events and automatically updates lead status in your SQLite database.

## Setup Instructions

### 1. Environment Variables
Add these to your `.env` file:
```
STRIPE_SECRET_KEY=sk_test_... (or sk_live_... for production)
STRIPE_WEBHOOK_SECRET=whsec_... (from Stripe Workbench)
```

### 2. Install Dependencies
```bash
cd /Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/stripe
pip install flask stripe python-dotenv
```

### 3. Run the Webhook Server

#### For Development (with Stripe CLI):
```bash
# Terminal 1: Start the webhook server
python stripe_webhook_server.py 5001

# Terminal 2: Forward Stripe events to local server
stripe listen --forward-to localhost:5001/webhook/stripe
```

#### For Production:
```bash
# Run with a process manager like PM2 or systemd
pm2 start stripe_webhook_server.py --interpreter python3 -- 5001
```

### 4. Configure Nginx (for production)
Add to your nginx configuration:
```nginx
location /webhook/stripe {
    proxy_pass http://localhost:5001/webhook/stripe;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
    # Important: Don't modify the request body
    proxy_request_buffering off;
}
```

### 5. Update Stripe Workbench
In Stripe Dashboard > Workbench > Webhooks:
- Endpoint URL: `https://yourdomain.com/webhook/stripe`
- Events to listen for:
  - `payment_intent.succeeded`
  - `payment_intent.payment_failed`
  - `charge.refunded`

## How It Works

1. **Payment Success Flow**:
   - Stripe sends `payment_intent.succeeded` event
   - Webhook updates lead status to "Enrolled"
   - Triggers email/SMS/WhatsApp notifications
   - Logs interaction in database

2. **Payment Link Creation**:
   - SDRs use `create_payment_link.py` to generate custom links
   - Links include metadata (lead_id, workshop_type)
   - Supports discount codes

3. **Database Updates**:
   - Updates `leads` table status
   - Records workshop type and payment amount
   - Adds entry to `lead_interactions` table

## Integration with Dashboard

Your Next.js dashboard will automatically reflect the status changes since it reads from the same SQLite database. No modifications needed to the dashboard code.

## Testing

### Test Payment Success:
```bash
# With Stripe CLI
stripe trigger payment_intent.succeeded \
  --add payment_intent:metadata.lead_id=123 \
  --add payment_intent:metadata.workshop_type="AI Essentials" \
  --add payment_intent:receipt_email="<EMAIL>"
```

### Health Check:
```bash
curl http://localhost:5001/health
```

## Monitoring

Check logs at:
```
/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/logs/stripe_webhook.log
```

## Post-Payment Actions

The webhook automatically triggers:
1. Enrollment confirmation email (via `mail/send_enrollment_confirmation.py`)
2. WhatsApp message (via `send_whatsapp.py`)
3. Certificate generation (after workshop completion)

You'll need to create these scripts based on your existing email/WhatsApp patterns.

## Security Notes

1. **Always verify webhook signatures** - The code already does this
2. **Use HTTPS in production** - Required by Stripe
3. **Keep webhook secret secure** - Never commit to git
4. **Use full database paths** - SQLite needs absolute paths for webhooks
