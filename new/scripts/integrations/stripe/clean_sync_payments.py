#!/usr/bin/env python3
"""
Clean Stripe Payment Sync - Only Successful Payment Intents
PURPOSE: Sync only the 666 successful payment intents from Stripe (matching dashboard)
USAGE: python clean_sync_payments.py
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime
import stripe
from dotenv import load_dotenv

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv('/Users/<USER>/Code/modernaipro/mai-administrative/new/config/.env')

# Stripe configuration
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), '../../data/database/leads.db')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clean_and_resync():
    """Clear database and sync only successful payment intents"""
    
    print("🧹 CLEAN STRIPE PAYMENT SYNC")
    print("="*50)
    print("This will:")
    print("- Clear all existing payment data")
    print("- Sync only successful payment intents (modern Stripe API)")
    print("- Target: 666 successful payments (matching your dashboard)")
    
    confirm = input("\nProceed? (yes/no): ")
    if confirm.lower() != 'yes':
        print("Cancelled.")
        return
    
    # Step 1: Clear existing data
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        print("\n🗑️  Clearing existing payment data...")
        cursor.execute("DELETE FROM leads")
        cursor.execute("DELETE FROM lead_interactions")
        conn.commit()
        print("✅ Existing data cleared")
        
    except Exception as e:
        logger.error(f"Error clearing data: {str(e)}")
        return
    finally:
        conn.close()
    
    # Step 2: Sync only successful payment intents
    print("\n📡 Syncing successful payment intents from Stripe...")
    
    successful_payments = 0
    has_more = True
    starting_after = None
    
    while has_more:
        try:
            payment_intents = stripe.PaymentIntent.list(
                limit=100,
                starting_after=starting_after
            )
            
            for pi in payment_intents.data:
                if pi.status == 'succeeded':
                    process_successful_payment_intent(pi)
                    successful_payments += 1
                    
                    # Progress indicator
                    if successful_payments % 50 == 0:
                        print(f"  Processed {successful_payments} successful payments...")
            
            has_more = payment_intents.has_more
            if has_more:
                starting_after = payment_intents.data[-1].id
                
        except Exception as e:
            logger.error(f"Error fetching payment intents: {str(e)}")
            break
    
    print(f"\n✅ Sync completed: {successful_payments} successful payments processed")
    
    # Step 3: Generate final report
    generate_final_report()

def process_successful_payment_intent(payment_intent):
    """Process a single successful payment intent"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        # Extract customer info
        email = payment_intent.receipt_email
        
        # Try to get email from charges if available
        if not email:
            try:
                # Expand charges to get billing details
                charges = stripe.PaymentIntent.retrieve(
                    payment_intent.id,
                    expand=['charges.data.billing_details']
                )
                if charges.charges.data:
                    email = charges.charges.data[0].billing_details.email
            except:
                pass
        
        # Use placeholder if no email found
        if not email:
            email = f"unknown_{payment_intent.id}@stripe.com"
        
        # Extract metadata
        workshop_type = payment_intent.metadata.get('workshop_type') if payment_intent.metadata else None
        
        # Payment details
        amount = payment_intent.amount / 100
        currency = payment_intent.currency.upper()
        payment_date = datetime.fromtimestamp(payment_intent.created)
        
        # Convert to USD for reporting
        if currency == 'INR':
            amount_usd = amount / 83.0
        else:
            amount_usd = amount
        
        # Insert into database
        cursor.execute("""
            INSERT INTO leads (
                lead_id, email, full_name, status, workshop_type,
                workshop_price_usd, workshop_currency, created_time,
                account_name, campaign_name, account_id, campaign_id,
                payment_status, stripe_payment_id, payment_date, 
                payment_amount, enrolled_date, lead_source, data_source
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            f"stripe_{payment_intent.id}", 
            email, 
            email.split('@')[0] if '@' in email else 'Unknown',
            'Enrolled', 
            workshop_type, 
            amount_usd, 
            currency, 
            payment_date,
            'Direct Purchase', 
            'Direct Purchase', 
            'direct', 
            'direct',
            'Paid', 
            payment_intent.id, 
            payment_date, 
            amount, 
            payment_date,
            'direct', 
            'stripe'
        ))
        
        # Log the payment interaction
        lead_id = cursor.lastrowid
        cursor.execute("""
            INSERT INTO lead_interactions 
            (lead_id, interaction_type, user_email, subject, message, outcome, interaction_date)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            lead_id,
            'enrollment',
            'stripe_sync',
            'Payment Intent Sync',
            f"Payment {payment_intent.id}: {amount} {currency} for {workshop_type or 'Workshop'}",
            'Payment recorded',
            payment_date
        ))
        
        conn.commit()
        
    except Exception as e:
        logger.error(f"Error processing payment {payment_intent.id}: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

def generate_final_report():
    """Generate final summary report"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        # Get summary statistics
        cursor.execute("""
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT CASE WHEN email NOT LIKE '<EMAIL>' THEN email END) as unique_real_emails,
                COUNT(CASE WHEN workshop_currency = 'USD' THEN 1 END) as usd_payments,
                COUNT(CASE WHEN workshop_currency = 'INR' THEN 1 END) as inr_payments,
                SUM(CASE WHEN workshop_currency = 'USD' THEN payment_amount END) as usd_total,
                SUM(CASE WHEN workshop_currency = 'INR' THEN payment_amount END) as inr_total,
                SUM(workshop_price_usd) as total_usd_equivalent
            FROM leads
        """)
        
        stats = cursor.fetchone()
        
        print(f"\n📊 FINAL SYNC REPORT")
        print("="*50)
        print(f"Total Successful Payments: {stats[0]:,}")
        print(f"Unique Customers (real emails): {stats[1]:,}")
        print(f"USD Payments: {stats[2]:,} (${stats[4]:,.2f})" if stats[4] else f"USD Payments: {stats[2]:,}")
        print(f"INR Payments: {stats[3]:,} (₹{stats[5]:,.2f})" if stats[5] else f"INR Payments: {stats[3]:,}")
        print(f"Total Revenue (USD equivalent): ${stats[6]:,.2f}")
        
        # Compare with target
        target = 666
        actual = stats[0]
        
        if actual == target:
            print(f"\n🎯 PERFECT MATCH: {actual} payments = Stripe dashboard target!")
        elif abs(actual - target) <= 5:
            print(f"\n✅ VERY CLOSE: {actual} payments vs {target} target (diff: {actual - target:+d})")
        else:
            print(f"\n⚠️  DIFFERENCE: {actual} payments vs {target} target (diff: {actual - target:+d})")
        
        # Show top customers
        print(f"\n💰 TOP CUSTOMERS BY PAYMENT AMOUNT:")
        cursor.execute("""
            SELECT email, payment_amount, workshop_currency, stripe_payment_id
            FROM leads 
            WHERE email NOT LIKE '<EMAIL>'
            AND payment_amount IS NOT NULL
            ORDER BY payment_amount DESC
            LIMIT 5
        """)
        
        top_customers = cursor.fetchall()
        for email, amount, currency, stripe_id in top_customers:
            if currency == 'USD':
                print(f"  {email}: ${amount:.2f} USD ({stripe_id[:15]}...)")
            else:
                print(f"  {email}: ₹{amount:.2f} {currency} ({stripe_id[:15]}...)")
        
    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
    finally:
        conn.close()

def main():
    clean_and_resync()

if __name__ == "__main__":
    main()