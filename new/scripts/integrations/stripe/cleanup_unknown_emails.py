#!/usr/bin/env python3
"""
Stripe Unknown Email Cleanup
PURPOSE: Fix unknown email records using Stripe API and CSV enrichment
USAGE: python cleanup_unknown_emails.py
"""

import os
import sys
import csv
import sqlite3
import logging
import re
from datetime import datetime
import stripe
from dotenv import load_dotenv
import argparse

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv('/Users/<USER>/Code/modernaipro/mai-administrative/new/config/.env')

# Stripe configuration
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), '../../data/database/leads.db')
CSV_PATH = '/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/leads_data/unified_customers (1).csv'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UnknownEmailCleaner:
    def __init__(self):
        self.db_path = DB_PATH
        self.csv_path = CSV_PATH
        self.csv_data = {}  # Will hold CSV customer data for enrichment
        
    def cleanup_unknown_emails(self):
        """Main cleanup process"""
        logger.info("🧹 Starting cleanup of unknown email records...")
        
        # First load CSV data for enrichment
        self.load_csv_data()
        
        # Get all unknown email records
        unknown_records = self.get_unknown_email_records()
        logger.info(f"Found {len(unknown_records)} records with unknown emails")
        
        fixed_count = 0
        merged_count = 0
        
        for record in unknown_records:
            try:
                result = self.fix_unknown_email_record(record)
                if result == 'fixed':
                    fixed_count += 1
                elif result == 'merged':
                    merged_count += 1
                    
            except Exception as e:
                logger.error(f"Error processing record {record['id']}: {str(e)}")
        
        logger.info(f"✅ Cleanup complete!")
        logger.info(f"🔧 Records fixed: {fixed_count}")
        logger.info(f"🔄 Records merged: {merged_count}")
        
        # Generate cleanup report
        self.generate_cleanup_report()
    
    def load_csv_data(self):
        """Load CSV data for customer enrichment"""
        logger.info("📊 Loading CSV data for enrichment...")
        
        with open(self.csv_path, 'r', encoding='utf-8') as file:
            csv_reader = csv.DictReader(file)
            
            for row in csv_reader:
                email = row['Email'].strip()
                if email and '@' in email:
                    self.csv_data[email] = {
                        'customer_id': row['id'].strip() if row['id'] else None,
                        'name': row['Name'].strip(),
                        'created_date': row['Created (UTC)'].strip(),
                        'total_spend': float(row['Total Spend']) if row['Total Spend'] else 0,
                        'payment_count': int(row['Payment Count']) if row['Payment Count'] else 0
                    }
        
        logger.info(f"Loaded {len(self.csv_data)} customer records from CSV")
    
    def get_unknown_email_records(self):
        """Get all records with unknown email patterns"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Get leads with unknown emails that have Stripe payment IDs
            cursor.execute("""
                SELECT id, lead_id, email, full_name, stripe_payment_id, payment_amount, status
                FROM leads 
                WHERE email LIKE 'unknown_%' AND stripe_payment_id IS NOT NULL
                ORDER BY id
            """)
            
            records = []
            for row in cursor.fetchall():
                records.append({
                    'id': row[0],
                    'lead_id': row[1], 
                    'email': row[2],
                    'full_name': row[3],
                    'stripe_payment_id': row[4],
                    'payment_amount': row[5],
                    'status': row[6]
                })
            
            return records
            
        finally:
            conn.close()
    
    def fix_unknown_email_record(self, record):
        """Fix a single unknown email record"""
        stripe_payment_id = record['stripe_payment_id']
        
        # Extract payment intent ID from various formats
        payment_intent_id = self.extract_payment_intent_id(stripe_payment_id)
        
        if not payment_intent_id:
            logger.warning(f"Could not extract payment intent ID from: {stripe_payment_id}")
            return 'skipped'
        
        # Try to get real customer data from Stripe API
        real_email, real_name, customer_id = self.get_real_customer_data(payment_intent_id)
        
        if real_email:
            # We found the real customer data!
            return self.merge_or_update_customer(record, real_email, real_name, customer_id)
        else:
            logger.warning(f"Could not find real customer data for payment: {payment_intent_id}")
            return 'skipped'
    
    def extract_payment_intent_id(self, stripe_payment_id):
        """Extract clean payment intent ID from various formats"""
        if not stripe_payment_id:
            return None
            
        # Handle different formats:
        # pi_3RtXmHL5XudS4oeH1LJcwmS4 (clean)
        # <EMAIL> (wrapped)
        
        # Remove unknown_ prefix and @stripe.com suffix
        clean_id = stripe_payment_id.replace('unknown_', '').replace('@stripe.com', '')
        
        # Check if it looks like a valid Stripe ID
        if re.match(r'^(pi_|ch_)[a-zA-Z0-9]{24,}$', clean_id):
            return clean_id
        
        return None
    
    def get_real_customer_data(self, payment_intent_id):
        """Get real customer data from Stripe API"""
        try:
            # Try as payment intent first
            if payment_intent_id.startswith('pi_'):
                payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
                
                # Get email from payment intent
                email = payment_intent.receipt_email
                
                # Try to get email and name from customer
                if payment_intent.customer:
                    customer = stripe.Customer.retrieve(payment_intent.customer)
                    email = email or customer.email
                    name = customer.name
                    customer_id = customer.id
                else:
                    name = None
                    customer_id = None
                
                # Try to get email from charges
                if not email and hasattr(payment_intent, 'charges') and payment_intent.charges.data:
                    charge = payment_intent.charges.data[0]
                    email = charge.billing_details.email if charge.billing_details else None
                    if not name and charge.billing_details:
                        name = charge.billing_details.name
                
                return email, name, customer_id
                
            # Try as charge
            elif payment_intent_id.startswith('ch_'):
                charge = stripe.Charge.retrieve(payment_intent_id)
                
                email = charge.billing_details.email if charge.billing_details else None
                email = email or charge.receipt_email
                
                name = charge.billing_details.name if charge.billing_details else None
                
                # Get customer info if available
                customer_id = None
                if charge.customer:
                    customer = stripe.Customer.retrieve(charge.customer)
                    email = email or customer.email
                    name = name or customer.name
                    customer_id = customer.id
                
                return email, name, customer_id
                
        except stripe.error.InvalidRequestError:
            logger.warning(f"Payment intent/charge not found: {payment_intent_id}")
        except Exception as e:
            logger.error(f"Error retrieving payment data for {payment_intent_id}: {str(e)}")
        
        return None, None, None
    
    def merge_or_update_customer(self, record, real_email, real_name, customer_id):
        """Merge or update customer with real data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Check if a lead with this real email already exists
            cursor.execute("SELECT id FROM leads WHERE email = ? AND id != ?", (real_email, record['id']))
            existing_lead = cursor.fetchone()
            
            if existing_lead:
                # Merge: Move payments to existing lead and delete duplicate
                existing_lead_id = existing_lead[0]
                
                # Update payments to point to existing lead
                cursor.execute("""
                    UPDATE payments 
                    SET lead_id = ?, customer_email = ?
                    WHERE lead_id = ?
                """, (existing_lead_id, real_email, record['id']))
                
                # Update customers table
                cursor.execute("""
                    UPDATE customers 
                    SET customer_email = ?
                    WHERE lead_id = ?
                """, (real_email, record['id']))
                
                # Delete the duplicate lead
                cursor.execute("DELETE FROM leads WHERE id = ?", (record['id'],))
                
                logger.info(f"✅ Merged unknown lead {record['id']} into existing lead {existing_lead_id} ({real_email})")
                
                # Enrich with CSV data if available
                self.enrich_with_csv_data(cursor, existing_lead_id, real_email)
                
                conn.commit()
                return 'merged'
                
            else:
                # Update: Fix the email and enrich
                cursor.execute("""
                    UPDATE leads 
                    SET email = ?, 
                        full_name = CASE 
                            WHEN (full_name IS NULL OR full_name = '' OR full_name LIKE 'unknown_%') THEN ?
                            ELSE full_name 
                        END,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (real_email, real_name or self.format_name_from_email(real_email), record['id']))
                
                # Update payments table
                cursor.execute("""
                    UPDATE payments 
                    SET customer_email = ?, 
                        customer_name = ?
                    WHERE lead_id = ?
                """, (real_email, real_name or self.format_name_from_email(real_email), record['id']))
                
                # Update customers table
                cursor.execute("""
                    UPDATE customers 
                    SET customer_email = ?,
                        stripe_customer_id = COALESCE(stripe_customer_id, ?)
                    WHERE lead_id = ?
                """, (real_email, customer_id, record['id']))
                
                logger.info(f"✅ Fixed unknown email for lead {record['id']}: {record['email']} → {real_email}")
                
                # Enrich with CSV data if available
                self.enrich_with_csv_data(cursor, record['id'], real_email)
                
                conn.commit()
                return 'fixed'
                
        except Exception as e:
            logger.error(f"Database error: {str(e)}")
            conn.rollback()
            return 'error'
        finally:
            conn.close()
    
    def enrich_with_csv_data(self, cursor, lead_id, email):
        """Enrich record with CSV data if available"""
        if email in self.csv_data:
            csv_record = self.csv_data[email]
            
            # Update with richer data from CSV
            cursor.execute("""
                UPDATE leads 
                SET full_name = CASE 
                        WHEN (full_name IS NULL OR full_name = '' OR full_name LIKE 'unknown_%') THEN ?
                        ELSE full_name 
                    END
                WHERE id = ?
            """, (csv_record['name'], lead_id))
            
            logger.info(f"📊 Enriched lead {lead_id} with CSV data: {csv_record['name']}")
    
    def format_name_from_email(self, email):
        """Create a reasonable name from email address"""
        if not email:
            return "Unknown Customer"
        
        name_part = email.split('@')[0]
        name_parts = name_part.replace('.', ' ').replace('_', ' ').replace('-', ' ').split()
        return ' '.join(word.capitalize() for word in name_parts)
    
    def generate_cleanup_report(self):
        """Generate final cleanup report"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Check remaining unknown emails
            cursor.execute("SELECT COUNT(*) FROM leads WHERE email LIKE 'unknown%'")
            remaining_unknown_leads = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM payments WHERE customer_email LIKE 'unknown%'")
            remaining_unknown_payments = cursor.fetchone()[0]
            
            # Get total counts
            cursor.execute("SELECT COUNT(*) FROM leads WHERE payment_status = 'Paid'")
            total_leads = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*), SUM(amount) FROM payments WHERE customer_email NOT LIKE 'unknown%'")
            clean_payment_stats = cursor.fetchone()
            
            cursor.execute("SELECT COUNT(DISTINCT customer_email) FROM payments WHERE customer_email NOT LIKE 'unknown%'")
            unique_customers = cursor.fetchone()[0]
            
            # Print report
            print("\n" + "="*80)
            print("🧹 EMAIL CLEANUP REPORT")
            print("="*80)
            print(f"📊 Total Leads: {total_leads}")
            print(f"💳 Clean Payment Records: {clean_payment_stats[0] or 0}")
            print(f"👥 Unique Customers: {unique_customers}")
            print(f"💰 Revenue (Clean): ${clean_payment_stats[1]:,.2f}" if clean_payment_stats[1] else "💰 Revenue: $0.00")
            print(f"⚠️  Remaining Unknown Leads: {remaining_unknown_leads}")
            print(f"⚠️  Remaining Unknown Payments: {remaining_unknown_payments}")
            print("="*80 + "\n")
            
        finally:
            conn.close()

def main():
    parser = argparse.ArgumentParser(description='Cleanup unknown email records using Stripe API')
    args = parser.parse_args()
    
    cleaner = UnknownEmailCleaner()
    
    confirm = input("This will cleanup unknown email records using Stripe API and CSV data. Continue? (yes/no): ")
    if confirm.lower() != 'yes':
        print("Cleanup cancelled.")
        return
    
    cleaner.cleanup_unknown_emails()

if __name__ == "__main__":
    main()