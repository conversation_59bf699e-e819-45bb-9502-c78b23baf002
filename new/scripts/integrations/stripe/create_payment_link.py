#!/usr/bin/env python3
"""
Stripe Payment Link Generator
PURPOSE: Create custom payment links with metadata for tracking
USAGE: python create_payment_link.py
"""

import os
import stripe
from dotenv import load_dotenv

# Load environment variables
load_dotenv('/Users/<USER>/Code/modernaipro/mai-administrative/new/config/.env')

# Stripe configuration
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

class PaymentLinkGenerator:
    def __init__(self):
        self.workshop_prices = {
            'AI Essentials': {
                'usd': 32000,  # $320 in cents
                'inr': 1500000  # ₹15,000 in paise
            },
            'AI Practitioner': {
                'usd': 32000,
                'inr': 1500000
            },
            'Agentic AI': {
                'usd': 32000,
                'inr': 1500000
            },
            'Vibe Coding': {
                'usd': 32000,
                'inr': 1500000
            },
            'AI for PMs': {
                'usd': 32000,
                'inr': 1500000
            },
            'AI for UX': {
                'usd': 32000,
                'inr': 1500000
            }
        }
        
        # Create or get existing products
        self.products = self._ensure_products_exist()
        
    def _ensure_products_exist(self):
        """Ensure Stripe products exist for each workshop"""
        products = {}
        
        for workshop_type in self.workshop_prices.keys():
            # Search for existing product
            existing = stripe.Product.list(
                active=True,
                limit=100
            )
            
            found = None
            for product in existing.data:
                if product.name == workshop_type:
                    found = product
                    break
            
            if found:
                products[workshop_type] = found
            else:
                # Create new product
                product = stripe.Product.create(
                    name=workshop_type,
                    description=f"Modern AI Pro - {workshop_type} Workshop"
                )
                products[workshop_type] = product
                
                # Create prices for USD and INR
                stripe.Price.create(
                    product=product.id,
                    unit_amount=self.workshop_prices[workshop_type]['usd'],
                    currency='usd',
                    nickname=f"{workshop_type} - USD"
                )
                
                stripe.Price.create(
                    product=product.id,
                    unit_amount=self.workshop_prices[workshop_type]['inr'],
                    currency='inr',
                    nickname=f"{workshop_type} - INR"
                )
        
        return products
    
    def create_payment_link(self, lead_id, email, workshop_type, currency='usd', discount_percent=None):
        """Create a custom payment link for a lead"""
        
        # Get the appropriate price
        prices = stripe.Price.list(
            product=self.products[workshop_type].id,
            currency=currency.lower(),
            limit=1
        )
        
        if not prices.data:
            raise ValueError(f"No price found for {workshop_type} in {currency}")
        
        price_id = prices.data[0].id
        
        # Create line items
        line_items = [{
            'price': price_id,
            'quantity': 1
        }]
        
        # Create payment link
        payment_link_params = {
            'line_items': line_items,
            'metadata': {
                'lead_id': str(lead_id),
                'workshop_type': workshop_type,
                'email': email
            },
            'after_completion': {
                'type': 'redirect',
                'redirect': {
                    'url': 'https://modernaipro.com/welcome?enrollment=success'
                }
            },
            'customer_creation': 'always'
        }
        
        # Add discount if provided
        if discount_percent:
            coupon = stripe.Coupon.create(
                percent_off=discount_percent,
                duration='once',
                name=f'{discount_percent}% off - {email}'
            )
            payment_link_params['discounts'] = [{'coupon': coupon.id}]
        
        payment_link = stripe.PaymentLink.create(**payment_link_params)
        
        return payment_link.url
    
    def create_subscription_link(self, lead_id, email, plan='monthly'):
        """Create payment link for future subscription model"""
        # This is for your November 2025 subscription launch
        # Placeholder for now
        pass

# Example usage
if __name__ == "__main__":
    generator = PaymentLinkGenerator()
    
    # Example: Create a payment link
    link = generator.create_payment_link(
        lead_id=123,
        email="<EMAIL>",
        workshop_type="AI Essentials",
        currency="usd",
        discount_percent=15  # 15% referral discount
    )
    
    print(f"Payment link created: {link}")
