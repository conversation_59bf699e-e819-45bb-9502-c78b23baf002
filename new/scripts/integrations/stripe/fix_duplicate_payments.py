#!/usr/bin/env python3
"""
Fix Duplicate Payments Script
PURPOSE: Remove duplicate transactions and keep only the 666 successful payments from Stripe
USAGE: python fix_duplicate_payments.py
"""

import os
import sqlite3
import logging
from datetime import datetime

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), '../../data/database/leads.db')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_duplicate_payments():
    """Remove duplicate payments and keep only legitimate transactions"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        print("🔧 Analyzing duplicate payments...")
        
        # Step 1: Find duplicate Stripe payment IDs
        cursor.execute("""
            SELECT stripe_payment_id, COUNT(*) as count
            FROM leads 
            WHERE stripe_payment_id IS NOT NULL
            GROUP BY stripe_payment_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"Found {len(duplicates)} Stripe IDs with duplicates")
            
            # Remove duplicates, keeping only the first occurrence
            for stripe_id, count in duplicates:
                cursor.execute("""
                    DELETE FROM leads 
                    WHERE stripe_payment_id = ? 
                    AND id NOT IN (
                        SELECT MIN(id) 
                        FROM leads 
                        WHERE stripe_payment_id = ?
                    )
                """, (stripe_id, stripe_id))
                
                removed = cursor.rowcount
                print(f"  Removed {removed} duplicate records for {stripe_id}")
        
        # Step 2: Remove records that look like payment intent/charge duplicates
        # These would have similar amounts and timestamps but different stripe IDs
        cursor.execute("""
            WITH potential_duplicates AS (
                SELECT 
                    id,
                    email,
                    payment_amount,
                    payment_date,
                    stripe_payment_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY 
                            CASE WHEN email LIKE 'unknown_%' THEN 'unknown' ELSE email END,
                            payment_amount,
                            DATE(payment_date)
                        ORDER BY 
                            CASE WHEN stripe_payment_id LIKE 'pi_%' THEN 1 ELSE 2 END,
                            id
                    ) as rn
                FROM leads
                WHERE payment_status = 'Paid'
                AND payment_amount IS NOT NULL
            )
            DELETE FROM leads 
            WHERE id IN (
                SELECT id FROM potential_duplicates WHERE rn > 1
            )
        """)
        
        removed_similar = cursor.rowcount
        print(f"Removed {removed_similar} potential duplicate transactions")
        
        # Step 3: Get final counts
        cursor.execute("SELECT COUNT(*) FROM leads WHERE payment_status = 'Paid'")
        final_count = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT workshop_currency, COUNT(*), SUM(payment_amount)
            FROM leads 
            WHERE payment_status = 'Paid' 
            GROUP BY workshop_currency
        """)
        
        currency_breakdown = cursor.fetchall()
        
        conn.commit()
        
        print(f"\n✅ Cleanup completed!")
        print(f"Final paid record count: {final_count}")
        print("Currency breakdown:")
        for currency, count, total in currency_breakdown:
            if currency == 'USD':
                print(f"  {currency}: {count} payments, ${total:,.2f}")
            elif currency == 'INR':
                print(f"  {currency}: {count} payments, ₹{total:,.2f}")
            else:
                print(f"  {currency}: {count} payments, {total}")
        
        target_count = 666  # Based on Stripe dashboard
        if abs(final_count - target_count) <= 10:
            print(f"🎯 SUCCESS: Count ({final_count}) is very close to Stripe target ({target_count})")
        else:
            print(f"⚠️  Note: Count ({final_count}) differs from Stripe target ({target_count}) by {abs(final_count - target_count)}")
        
    except Exception as e:
        logger.error(f"Error fixing duplicates: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

def main():
    print("🔧 FIXING DUPLICATE STRIPE PAYMENTS")
    print("="*50)
    fix_duplicate_payments()

if __name__ == "__main__":
    main()