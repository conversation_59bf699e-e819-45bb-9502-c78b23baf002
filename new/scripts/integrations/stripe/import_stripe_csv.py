#!/usr/bin/env python3
"""
Stripe CSV Data Importer
PURPOSE: Import complete Stripe customer and payment data from CSV export
USAGE: python import_stripe_csv.py
"""

import os
import sys
import csv
import sqlite3
import logging
from datetime import datetime
import argparse

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), '../../data/database/leads.db')
CSV_PATH = '/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/leads_data/unified_customers (1).csv'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StripeCSVImporter:
    def __init__(self):
        self.db_path = DB_PATH
        self.csv_path = CSV_PATH
        
    def import_csv_data(self):
        """Import all Stripe customer and payment data from CSV"""
        logger.info("🚀 Starting Stripe CSV import...")
        
        customers_processed = 0
        payments_created = 0
        repeat_customers = 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            with open(self.csv_path, 'r', encoding='utf-8') as file:
                csv_reader = csv.DictReader(file)
                
                for row in csv_reader:
                    try:
                        # Parse CSV row
                        customer_id = row['id'].strip() if row['id'] else None
                        email = row['Email'].strip()
                        name = row['Name'].strip()
                        created_date = row['Created (UTC)'].strip()
                        total_spend = float(row['Total Spend']) if row['Total Spend'] else 0
                        payment_count = int(row['Payment Count']) if row['Payment Count'] else 0
                        
                        # Skip empty emails
                        if not email or not self.is_valid_email(email):
                            continue
                        
                        # Process this customer
                        self.process_customer_from_csv(
                            cursor, customer_id, email, name, created_date, 
                            total_spend, payment_count
                        )
                        
                        customers_processed += 1
                        payments_created += payment_count
                        
                        if payment_count > 1:
                            repeat_customers += 1
                        
                        if customers_processed % 50 == 0:
                            logger.info(f"Processed {customers_processed} customers...")
                        
                    except Exception as e:
                        logger.error(f"Error processing row {row}: {str(e)}")
                        continue
            
            conn.commit()
            
            # Generate final report
            logger.info(f"✅ Import complete!")
            logger.info(f"📊 Customers processed: {customers_processed}")
            logger.info(f"💳 Total payments: {payments_created}")
            logger.info(f"🔄 Repeat customers: {repeat_customers}")
            
            self.generate_final_report()
            
        except Exception as e:
            logger.error(f"Import failed: {str(e)}")
            conn.rollback()
        finally:
            conn.close()
    
    def is_valid_email(self, email):
        """Check if email is valid"""
        return email and '@' in email and '.' in email.split('@')[1]
    
    def process_customer_from_csv(self, cursor, customer_id, email, name, created_date, total_spend, payment_count):
        """Process a single customer from CSV data"""
        
        # Parse created date
        try:
            if created_date:
                created_dt = datetime.strptime(created_date, '%Y-%m-%d %H:%M')
            else:
                created_dt = datetime.now()
        except:
            created_dt = datetime.now()
        
        # Ensure lead exists
        lead_id = self.ensure_lead_exists(cursor, customer_id, email, name, created_dt)
        
        # Create individual payment records (since CSV shows aggregated data)
        self.create_payment_records(cursor, lead_id, email, name, payment_count, total_spend)
        
        # Update customer record
        self.update_customer_record(cursor, lead_id, customer_id, email, name, total_spend, payment_count, created_dt)
    
    def ensure_lead_exists(self, cursor, customer_id, email, name, created_dt):
        """Ensure lead record exists for this customer"""
        # Check if lead already exists
        cursor.execute("SELECT id FROM leads WHERE email = ?", (email,))
        result = cursor.fetchone()
        
        if result:
            # Update existing lead with customer info if better data available
            cursor.execute("""
                UPDATE leads 
                SET full_name = CASE 
                        WHEN (full_name IS NULL OR full_name = '' OR full_name LIKE 'unknown_%') THEN ?
                        ELSE full_name 
                    END,
                    status = CASE 
                        WHEN status = 'New' THEN 'Enrolled'
                        ELSE status
                    END,
                    payment_status = 'Paid',
                    updated_at = CURRENT_TIMESTAMP
                WHERE email = ?
            """, (name, email))
            return result[0]
        else:
            # Create new lead
            lead_id_str = customer_id if customer_id else f"csv_{email.replace('@', '_').replace('.', '_')}"
            
            cursor.execute("""
                INSERT INTO leads (
                    lead_id, email, full_name, status, created_time,
                    account_name, campaign_name, account_id, campaign_id,
                    lead_source, data_source, payment_status, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                lead_id_str, email, name, 'Enrolled', created_dt,
                'Stripe Customer', 'Direct Customer', 'stripe', 'stripe',
                'direct', 'stripe_csv_import', 'Paid', datetime.now()
            ))
            return cursor.lastrowid
    
    def create_payment_records(self, cursor, lead_id, email, name, payment_count, total_spend):
        """Create individual payment records from aggregated data"""
        # Check if payments already exist for this customer
        cursor.execute("SELECT COUNT(*) FROM payments WHERE customer_email = ?", (email,))
        existing_payments = cursor.fetchone()[0]
        
        if existing_payments >= payment_count:
            return  # Already have enough payment records
        
        # Calculate average payment amount
        if payment_count > 0 and total_spend > 0:
            avg_amount = total_spend / payment_count
        else:
            return
        
        # Create payment records (simplified - we don't have individual transaction details from CSV)
        payments_to_create = payment_count - existing_payments
        
        for i in range(payments_to_create):
            # Use different payment IDs to avoid conflicts
            payment_id = f"csv_{email.replace('@', '_').replace('.', '_')}_{i+1+existing_payments}"
            
            cursor.execute("""
                INSERT OR IGNORE INTO payments (
                    lead_id, stripe_payment_intent_id, amount, currency, status,
                    workshop_type, payment_date, customer_email, customer_name
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                lead_id, payment_id, avg_amount, 'USD', 'succeeded',
                'Unknown', datetime.now(), email, name
            ))
    
    def update_customer_record(self, cursor, lead_id, customer_id, email, name, total_spend, payment_count, created_dt):
        """Update or create customer record"""
        # Check if customer already exists
        cursor.execute("SELECT id FROM customers WHERE customer_email = ?", (email,))
        result = cursor.fetchone()
        
        customer_segment = 'repeat' if payment_count > 1 else 'new'
        
        if result:
            # Update existing customer
            cursor.execute("""
                UPDATE customers 
                SET total_spent_usd = ?,
                    workshops_enrolled = ?,
                    customer_segment = ?,
                    stripe_customer_id = COALESCE(stripe_customer_id, ?),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (total_spend, payment_count, customer_segment, customer_id, result[0]))
        else:
            # Create new customer
            cursor.execute("""
                INSERT INTO customers (
                    lead_id, customer_email, stripe_customer_id, total_spent_usd,
                    workshops_enrolled, customer_segment,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                lead_id, email, customer_id, total_spend,
                payment_count, customer_segment,
                created_dt, datetime.now()
            ))
    
    def generate_final_report(self):
        """Generate final summary report"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Get comprehensive stats
            cursor.execute("SELECT COUNT(*) FROM leads WHERE payment_status = 'Paid'")
            total_leads = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*), SUM(amount) FROM payments")
            payment_stats = cursor.fetchone()
            
            cursor.execute("SELECT customer_segment, COUNT(*) FROM customers GROUP BY customer_segment")
            segment_stats = cursor.fetchall()
            
            # Get top repeat customers
            cursor.execute("""
                SELECT customer_email, customer_name, COUNT(*) as payments, SUM(amount) as total
                FROM payments 
                WHERE customer_email NOT LIKE 'unknown_%'
                GROUP BY customer_email 
                HAVING COUNT(*) > 1 
                ORDER BY total DESC 
                LIMIT 10
            """)
            repeat_customers = cursor.fetchall()
            
            # Print comprehensive report
            print("\n" + "="*80)
            print("🎉 STRIPE CSV IMPORT COMPLETE")
            print("="*80)
            print(f"📊 Total Leads with Payments: {total_leads}")
            print(f"💳 Total Payment Records: {payment_stats[0] or 0}")
            print(f"💰 Total Revenue: ${payment_stats[1]:,.2f}" if payment_stats[1] else "💰 Total Revenue: $0.00")
            
            print("\n🎯 Customer Segmentation:")
            print("-" * 40)
            for segment, count in segment_stats:
                print(f"  {segment.title()}: {count} customers")
            
            if repeat_customers:
                print(f"\n🔄 Top Repeat Customers:")
                print("-" * 60)
                for email, name, payment_count, total_spent in repeat_customers:
                    name_display = name or "No name"
                    print(f"  {email} ({name_display})")
                    print(f"    {payment_count} payments • ${total_spent:.2f} total")
            
            print("="*80 + "\n")
            
        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
        finally:
            conn.close()

def main():
    parser = argparse.ArgumentParser(description='Import Stripe CSV data to database')
    args = parser.parse_args()
    
    importer = StripeCSVImporter()
    
    confirm = input(f"This will import Stripe CSV data from:\n{CSV_PATH}\n\nContinue? (yes/no): ")
    if confirm.lower() != 'yes':
        print("Import cancelled.")
        return
    
    importer.import_csv_data()

if __name__ == "__main__":
    main()