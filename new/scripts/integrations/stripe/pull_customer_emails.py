#!/usr/bin/env python3
"""
Pull Customer Emails from Stripe
PURPOSE: Retrieve actual customer emails for payment records
USAGE: python pull_customer_emails.py [--limit N] [--dry-run]
"""

import os
import sys
import sqlite3
import time
import logging
import argparse
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import stripe
    from dotenv import load_dotenv
except ImportError:
    print("❌ Required libraries not found. Please install: pip install stripe python-dotenv")
    sys.exit(1)

# Load environment variables
load_dotenv('/Users/<USER>/Code/modernaipro/mai-administrative/new/config/.env')

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), '../../data/database/leads.db')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Code/modernaipro/mai-administrative/new/data/logs/email_pull.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EmailPuller:
    def __init__(self, rate_limit_delay=0.5):
        """Initialize with rate limiting delay (seconds between requests)"""
        self.rate_limit_delay = rate_limit_delay
        self.requests_made = 0
        self.emails_found = 0
        self.emails_updated = 0
        
        # Load Stripe API key
        stripe_key = os.getenv('STRIPE_SECRET_KEY')
        if not stripe_key:
            raise ValueError("STRIPE_SECRET_KEY environment variable not set")
        
        stripe.api_key = stripe_key
        logger.info("Initialized Stripe API client")

    def get_payment_intents_needing_emails(self, limit=10):
        """Get payment intents that need email updates"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get records with placeholder emails
        query = """
        SELECT id, stripe_payment_id, full_name, email
        FROM leads 
        WHERE stripe_payment_id IS NOT NULL 
        AND (email LIKE '<EMAIL>' OR email IS NULL OR email = '')
        ORDER BY payment_date DESC
        LIMIT ?
        """
        
        cursor.execute(query, (limit,))
        records = cursor.fetchall()
        conn.close()
        
        logger.info(f"Found {len(records)} records needing email updates")
        return records

    def get_email_from_payment_intent(self, payment_intent_id):
        """Retrieve email from payment intent and associated data"""
        try:
            # Add rate limiting delay
            time.sleep(self.rate_limit_delay)
            self.requests_made += 1
            
            # Retrieve the payment intent
            pi = stripe.PaymentIntent.retrieve(payment_intent_id)
            logger.debug(f"Retrieved PaymentIntent: {payment_intent_id}")
            
            email = None
            source = None
            
            # Method 1: Check receipt_email in PaymentIntent
            if hasattr(pi, 'receipt_email') and pi.receipt_email:
                email = pi.receipt_email
                source = "payment_intent_receipt"
                logger.debug(f"Found email in PaymentIntent receipt_email: {email}")
            
            # Method 2: Check customer email if customer exists
            elif hasattr(pi, 'customer') and pi.customer:
                time.sleep(self.rate_limit_delay)  # Additional rate limit for customer lookup
                self.requests_made += 1
                
                customer = stripe.Customer.retrieve(pi.customer)
                if hasattr(customer, 'email') and customer.email:
                    email = customer.email
                    source = "customer_object"
                    logger.debug(f"Found email in Customer object: {email}")
            
            # Method 3: Check charges for receipt_email
            if not email and hasattr(pi, 'charges') and pi.charges.data:
                for charge in pi.charges.data:
                    if hasattr(charge, 'receipt_email') and charge.receipt_email:
                        email = charge.receipt_email
                        source = "charge_receipt"
                        logger.debug(f"Found email in Charge receipt_email: {email}")
                        break
            
            # Method 4: Check billing details in payment method
            if not email and hasattr(pi, 'payment_method'):
                time.sleep(self.rate_limit_delay)
                self.requests_made += 1
                
                try:
                    pm = stripe.PaymentMethod.retrieve(pi.payment_method)
                    if (hasattr(pm, 'billing_details') and 
                        pm.billing_details and 
                        hasattr(pm.billing_details, 'email') and 
                        pm.billing_details.email):
                        email = pm.billing_details.email
                        source = "payment_method_billing"
                        logger.debug(f"Found email in PaymentMethod billing: {email}")
                except Exception as e:
                    logger.debug(f"Could not retrieve PaymentMethod: {e}")
            
            if email:
                self.emails_found += 1
                logger.info(f"✅ Found email for {payment_intent_id}: {email} (source: {source})")
                return email, source
            else:
                logger.info(f"❌ No email found for {payment_intent_id}")
                return None, None
                
        except stripe.error.RateLimitError as e:
            logger.warning(f"Rate limit hit for {payment_intent_id}. Waiting 60 seconds...")
            time.sleep(60)
            return self.get_email_from_payment_intent(payment_intent_id)  # Retry
            
        except Exception as e:
            logger.error(f"Error retrieving email for {payment_intent_id}: {e}")
            return None, None

    def update_email_in_database(self, record_id, email, source, dry_run=False):
        """Update email in database"""
        if dry_run:
            logger.info(f"[DRY RUN] Would update record {record_id} with email: {email}")
            return
            
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                UPDATE leads 
                SET email = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (email, record_id))
            
            conn.commit()
            self.emails_updated += 1
            logger.info(f"✅ Updated database record {record_id} with email: {email}")
            
        except Exception as e:
            logger.error(f"Error updating database for record {record_id}: {e}")
        finally:
            conn.close()

    def pull_emails(self, limit=10, dry_run=False):
        """Main method to pull emails for payment records"""
        logger.info(f"🚀 Starting email pull process (limit: {limit}, dry_run: {dry_run})")
        
        # Get records needing email updates
        records = self.get_payment_intents_needing_emails(limit)
        
        if not records:
            logger.info("✅ No records need email updates")
            return
        
        start_time = time.time()
        
        for record_id, payment_intent_id, current_name, current_email in records:
            logger.info(f"\n--- Processing record {record_id}: {payment_intent_id} ---")
            logger.info(f"Current: {current_name} <{current_email}>")
            
            # Get email from Stripe
            email, source = self.get_email_from_payment_intent(payment_intent_id)
            
            if email:
                # Update database
                self.update_email_in_database(record_id, email, source, dry_run)
                
                # Also update the name if it's a placeholder
                if current_name and current_name.startswith('unknown_'):
                    logger.info(f"Note: Name is still placeholder: {current_name}")
            else:
                logger.info(f"⚠️  No email found for record {record_id}")
        
        # Summary
        elapsed_time = time.time() - start_time
        logger.info(f"\n📊 EMAIL PULL SUMMARY")
        logger.info(f"="*50)
        logger.info(f"Records processed: {len(records)}")
        logger.info(f"API requests made: {self.requests_made}")
        logger.info(f"Emails found: {self.emails_found}")
        logger.info(f"Database updates: {self.emails_updated}")
        logger.info(f"Success rate: {(self.emails_found/len(records)*100):.1f}%")
        logger.info(f"Time elapsed: {elapsed_time:.1f} seconds")
        logger.info(f"Avg time per record: {(elapsed_time/len(records)):.1f} seconds")


def main():
    parser = argparse.ArgumentParser(description='Pull customer emails from Stripe')
    parser.add_argument('--limit', type=int, default=10, 
                       help='Number of records to process (default: 10)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be done without making changes')
    parser.add_argument('--rate-limit', type=float, default=0.5,
                       help='Delay between API requests in seconds (default: 0.5)')
    
    args = parser.parse_args()
    
    try:
        puller = EmailPuller(rate_limit_delay=args.rate_limit)
        puller.pull_emails(limit=args.limit, dry_run=args.dry_run)
        
    except KeyboardInterrupt:
        logger.info("\n⏹️  Process interrupted by user")
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()