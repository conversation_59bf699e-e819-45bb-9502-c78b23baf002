#!/usr/bin/env python3
"""
Stripe Webhook Server - Handles payment events and updates database
PURPOSE: Receive Stripe webhook events and update lead status
USAGE: python stripe_webhook_server.py [port]
"""

import os
import sys
import json
import sqlite3
import logging
from datetime import datetime
from flask import Flask, request, jsonify
import stripe

# Add scripts root to path for config import
scripts_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, scripts_root)

from config import settings, api_config, setup_logging

# Stripe configuration
stripe.api_key = api_config.stripe_secret_key

# For local testing with Stripe CLI, use the secret from the CLI output
# For production, use the secret from your .env file
WEBHOOK_SECRET = api_config.stripe_webhook_secret

# If running locally with Stripe CLI and no webhook secret in env, 
# you can temporarily hardcode the one shown in CLI output
if not WEBHOOK_SECRET and len(sys.argv) > 2 and sys.argv[2] == "--local":
    # When you run 'stripe listen', it shows: "Your webhook signing secret is whsec_..."
    # Copy that value here for local testing
    WEBHOOK_SECRET = "whsec_PASTE_YOUR_CLI_SECRET_HERE"
    print("Using local CLI webhook secret for testing")

# Database path from centralized config
DB_PATH = settings.database_path

# Flask app
app = Flask(__name__)

# Configure logging using centralized system
logger = setup_logging('stripe_webhook')

class PaymentProcessor:
    def __init__(self):
        self.db_path = DB_PATH
        
    def update_lead_status(self, email, payment_intent):
        """Update lead status to Enrolled when payment succeeds"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Extract metadata
            lead_id = payment_intent.metadata.get('lead_id')
            workshop_type = payment_intent.metadata.get('workshop_type')
            amount = payment_intent.amount / 100  # Convert from cents
            currency = payment_intent.currency.upper()
            
            # Update lead status
            if lead_id:
                # Update by lead_id if provided
                cursor.execute("""
                    UPDATE leads 
                    SET status = 'Enrolled',
                        workshop_type = ?,
                        workshop_price_usd = ?,
                        workshop_currency = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (workshop_type, amount, currency, lead_id))
            else:
                # Fallback to email
                cursor.execute("""
                    UPDATE leads 
                    SET status = 'Enrolled',
                        workshop_type = ?,
                        workshop_price_usd = ?,
                        workshop_currency = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE email = ? AND status NOT IN ('Enrolled', 'Workshop Complete')
                    ORDER BY created_time DESC
                    LIMIT 1
                """, (workshop_type, amount, currency, email))
            
            # Log the interaction
            if lead_id or email:
                cursor.execute("""
                    INSERT INTO lead_interactions 
                    (lead_id, interaction_type, user_email, subject, message, outcome)
                    VALUES (
                        (SELECT id FROM leads WHERE email = ? OR id = ? LIMIT 1),
                        'enrollment',
                        'stripe_webhook',
                        'Payment Successful',
                        ?,
                        'Enrolled in workshop'
                    )
                """, (email, lead_id, f"Payment of {amount} {currency} for {workshop_type}"))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Updated lead status for {email} - Enrolled in {workshop_type}")
            
            # Trigger post-payment actions
            self.trigger_post_payment_actions(email, lead_id, workshop_type, amount)
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating lead status: {str(e)}")
            return False
    
    def handle_payment_failure(self, email, payment_intent):
        """Log payment failure"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            lead_id = payment_intent.metadata.get('lead_id')
            
            # Log the failed payment attempt
            cursor.execute("""
                INSERT INTO lead_interactions 
                (lead_id, interaction_type, user_email, subject, message, outcome)
                VALUES (
                    (SELECT id FROM leads WHERE email = ? OR id = ? LIMIT 1),
                    'note_added',
                    'stripe_webhook',
                    'Payment Failed',
                    ?,
                    'Payment attempt failed'
                )
            """, (email, lead_id, f"Payment failed: {payment_intent.last_payment_error.message if payment_intent.last_payment_error else 'Unknown error'}"))
            
            conn.commit()
            conn.close()
            
            logger.warning(f"Payment failed for {email}")
            return True
            
        except Exception as e:
            logger.error(f"Error logging payment failure: {str(e)}")
            return False
    
    def handle_refund(self, charge):
        """Update lead status when refund is processed"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Find the lead by payment intent
            payment_intent_id = charge.payment_intent
            
            cursor.execute("""
                UPDATE leads 
                SET status = 'Lost',
                    updated_at = CURRENT_TIMESTAMP
                WHERE id IN (
                    SELECT lead_id FROM lead_interactions 
                    WHERE message LIKE ?
                )
            """, (f"%{payment_intent_id}%",))
            
            # Log the refund
            cursor.execute("""
                INSERT INTO lead_interactions 
                (lead_id, interaction_type, user_email, subject, message, outcome)
                VALUES (
                    (SELECT lead_id FROM lead_interactions WHERE message LIKE ? LIMIT 1),
                    'note_added',
                    'stripe_webhook',
                    'Refund Processed',
                    ?,
                    'Workshop enrollment refunded'
                )
            """, (f"%{payment_intent_id}%", f"Refund of {charge.amount_refunded/100} {charge.currency.upper()}"))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Processed refund for payment intent: {payment_intent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing refund: {str(e)}")
            return False
    
    def trigger_post_payment_actions(self, email, lead_id, workshop_type, amount):
        """Trigger other scripts for post-payment actions"""
        try:
            # Call your email sending script
            import subprocess
            
            data = {
                'email': email,
                'lead_id': lead_id,
                'workshop_type': workshop_type,
                'amount': amount
            }
            
            # Example: Call your mail sending script
            mail_script = os.path.join(os.path.dirname(__file__), '../mail/send_enrollment_confirmation.py')
            if os.path.exists(mail_script):
                subprocess.Popen([
                    sys.executable, 
                    mail_script, 
                    json.dumps(data)
                ])
                logger.info(f"Triggered enrollment email for {email}")
            
            # Example: Call WhatsApp script
            whatsapp_script = os.path.join(os.path.dirname(__file__), '../send_whatsapp.py')
            if os.path.exists(whatsapp_script):
                subprocess.Popen([
                    sys.executable,
                    whatsapp_script,
                    json.dumps({
                        'phone': self.get_phone_by_email(email),
                        'message': f"Welcome to {workshop_type}! Your payment has been confirmed.",
                        'template': 'enrollment_confirmation'
                    })
                ])
            
        except Exception as e:
            logger.error(f"Error triggering post-payment actions: {str(e)}")
    
    def get_phone_by_email(self, email):
        """Get phone number from database by email"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT phone_number FROM leads WHERE email = ? LIMIT 1", (email,))
            result = cursor.fetchone()
            conn.close()
            return result[0] if result else None
        except:
            return None

# Initialize processor
processor = PaymentProcessor()

@app.route('/webhook/stripe', methods=['POST'])
def stripe_webhook():
    """Handle Stripe webhook events"""
    payload = request.data
    sig_header = request.headers.get('Stripe-Signature')
    
    # For local testing, you can skip signature verification if no secret is set
    if not WEBHOOK_SECRET:
        logger.warning("No webhook secret configured - skipping signature verification (UNSAFE for production!)")
        try:
            event = json.loads(payload)
        except json.JSONDecodeError:
            return jsonify({'error': 'Invalid payload'}), 400
    else:
        try:
            # Verify webhook signature
            event = stripe.Webhook.construct_event(
                payload, sig_header, WEBHOOK_SECRET
            )
        except ValueError as e:
            logger.error(f"Invalid payload: {str(e)}")
            return jsonify({'error': 'Invalid payload'}), 400
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Invalid signature: {str(e)}")
            return jsonify({'error': 'Invalid signature'}), 400
    
    # Log the event
    logger.info(f"Received event: {event['type']} - ID: {event['id']}")
    
    # Handle different event types
    try:
        if event['type'] == 'payment_intent.succeeded':
            payment_intent = event['data']['object']
            email = payment_intent.receipt_email or payment_intent.charges.data[0].billing_details.email
            processor.update_lead_status(email, payment_intent)
            
        elif event['type'] == 'payment_intent.payment_failed':
            payment_intent = event['data']['object']
            email = payment_intent.receipt_email or payment_intent.charges.data[0].billing_details.email
            processor.handle_payment_failure(email, payment_intent)
            
        elif event['type'] == 'charge.refunded':
            charge = event['data']['object']
            processor.handle_refund(charge)
            
        else:
            logger.info(f"Unhandled event type: {event['type']}")
    
    except Exception as e:
        logger.error(f"Error processing event: {str(e)}")
        return jsonify({'error': 'Event processing failed'}), 500
    
    return jsonify({'received': True}), 200

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'stripe_webhook',
        'timestamp': datetime.now().isoformat()
    }), 200

if __name__ == '__main__':
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 5001
    logger.info(f"Starting Stripe webhook server on port {port}")
    app.run(host='0.0.0.0', port=port, debug=False)
