#!/usr/bin/env python3
"""
Enhanced Stripe Customer & Payment Sync
PURPOSE: Sync all Stripe customers with their full payment history - no fake emails!
USAGE: python sync_stripe_customers_enhanced.py [--days N] [--all]
"""

import os
import sys
import json
import sqlite3
import logging
from datetime import datetime, timedelta
import stripe
import argparse

# Add scripts root to path for config import
scripts_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, scripts_root)

from config import settings, api_config, setup_logging

# Stripe configuration
stripe.api_key = api_config.stripe_secret_key

# Database path from centralized config
DB_PATH = settings.database_path

# Configure logging using centralized system
logger = setup_logging('stripe_enhanced_sync')

class EnhancedStripeSync:
    def __init__(self):
        self.db_path = DB_PATH
        self.workshop_keywords = [
            'AI Essentials',
            'AI Practitioner', 
            'Agentic AI',
            'Vibe Coding',
            'AI for PMs',
            'AI for UX'
        ]
        
    def sync_all_customers(self, days_back=None, sync_all=False):
        """Sync all Stripe customers and their payment history"""
        logger.info(f"🚀 Starting enhanced Stripe customer sync - {'All time' if sync_all else f'Last {days_back} days'}")
        
        # Set date filter
        created_filter = {}
        if not sync_all and days_back:
            start_date = datetime.now() - timedelta(days=days_back)
            created_filter = {'gte': int(start_date.timestamp())}
        
        customers_processed = 0
        total_payments = 0
        has_more = True
        starting_after = None
        
        while has_more:
            # Fetch Stripe customers
            customers = stripe.Customer.list(
                limit=100,
                created=created_filter,
                starting_after=starting_after
            )
            
            for customer in customers.data:
                try:
                    # ONLY process customers with real emails - no fake ones!
                    if not customer.email or not self.is_valid_email(customer.email):
                        logger.debug(f"Skipping customer {customer.id} - no valid email")
                        continue
                    
                    # Process this customer and all their payments
                    payment_count = self.process_customer_and_payments(customer)
                    customers_processed += 1
                    total_payments += payment_count
                    
                    logger.info(f"✅ Processed customer: {customer.email} ({customer.name or 'No name'}) - {payment_count} payments")
                    
                except Exception as e:
                    logger.error(f"Error processing customer {customer.id}: {str(e)}")
            
            has_more = customers.has_more
            if has_more:
                starting_after = customers.data[-1].id
        
        logger.info(f"🎉 Sync complete: {customers_processed} customers, {total_payments} total payments")
        self.generate_summary_report()
    
    def is_valid_email(self, email):
        """Check if email is valid (not fake)"""
        if not email:
            return False
        # Skip fake emails created by old sync
        if email.startswith('unknown_') and email.endswith('@stripe.com'):
            return False
        # Basic email validation
        return '@' in email and '.' in email.split('@')[1]
    
    def process_customer_and_payments(self, customer):
        """Process a single customer and all their payment history"""
        email = customer.email
        customer_name = customer.name or self.format_name_from_email(email)
        payments_processed = 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Find or create lead record
            lead_id = self.ensure_lead_exists(cursor, customer, email, customer_name)
            
            # Get all payment intents for this customer
            payment_intents = stripe.PaymentIntent.list(
                customer=customer.id,
                limit=100
            )
            
            for payment_intent in payment_intents.data:
                if payment_intent.status == 'succeeded':
                    self.process_payment_intent(cursor, lead_id, customer, payment_intent)
                    payments_processed += 1
            
            # Also get charges (for older Stripe setups)
            charges = stripe.Charge.list(
                customer=customer.id,
                limit=100
            )
            
            for charge in charges.data:
                if charge.paid and not charge.refunded:
                    # Check if this charge is already covered by a payment intent
                    if not self.charge_already_processed(cursor, charge.id):
                        self.process_charge(cursor, lead_id, customer, charge)
                        payments_processed += 1
            
            # Update customer record
            self.update_customer_record(cursor, lead_id, customer, email, customer_name, payments_processed)
            
            conn.commit()
            return payments_processed
            
        except Exception as e:
            logger.error(f"Error processing customer {customer.id}: {str(e)}")
            conn.rollback()
            return 0
        finally:
            conn.close()
    
    def format_name_from_email(self, email):
        """Create a reasonable name from email address"""
        if not email:
            return "Unknown Customer"
        
        name_part = email.split('@')[0]
        # Remove common separators and capitalize
        name_parts = name_part.replace('.', ' ').replace('_', ' ').replace('-', ' ').split()
        return ' '.join(word.capitalize() for word in name_parts)
    
    def ensure_lead_exists(self, cursor, customer, email, customer_name):
        """Ensure lead record exists for this customer"""
        # Try to find existing lead
        cursor.execute("SELECT id FROM leads WHERE email = ?", (email,))
        result = cursor.fetchone()
        
        if result:
            # Update existing lead with customer info
            cursor.execute("""
                UPDATE leads 
                SET full_name = COALESCE(NULLIF(full_name, ''), ?),
                    updated_at = CURRENT_TIMESTAMP
                WHERE email = ?
            """, (customer_name, email))
            return result[0]
        else:
            # Create new lead
            cursor.execute("""
                INSERT INTO leads (
                    lead_id, email, full_name, status, created_time,
                    account_name, campaign_name, account_id, campaign_id,
                    lead_source, data_source, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                f"stripe_customer_{customer.id}", email, customer_name,
                'New', datetime.fromtimestamp(customer.created),
                'Stripe Customer', 'Direct Customer', 'stripe', 'stripe',
                'direct', 'stripe_customer_sync', datetime.now()
            ))
            return cursor.lastrowid
    
    def process_payment_intent(self, cursor, lead_id, customer, payment_intent):
        """Process a payment intent"""
        amount = payment_intent.amount / 100
        currency = payment_intent.currency.upper()
        payment_date = datetime.fromtimestamp(payment_intent.created)
        
        # Convert to USD
        amount_usd = amount / 83.0 if currency == 'INR' else amount
        
        # Detect workshop type
        workshop_type = self.detect_workshop_type(payment_intent)
        
        # Update lead with payment info
        self.update_lead_with_payment(cursor, lead_id, payment_intent.id, amount, amount_usd, currency, payment_date, workshop_type)
        
        # Add payment record
        self.add_payment_record(cursor, lead_id, customer.email, customer.name, payment_intent.id, amount, amount_usd, currency, payment_date, workshop_type)
    
    def process_charge(self, cursor, lead_id, customer, charge):
        """Process a charge"""
        amount = charge.amount / 100
        currency = charge.currency.upper()
        payment_date = datetime.fromtimestamp(charge.created)
        
        # Convert to USD
        amount_usd = amount / 83.0 if currency == 'INR' else amount
        
        # Detect workshop type
        workshop_type = self.detect_workshop_type_from_charge(charge)
        
        # Update lead with payment info
        self.update_lead_with_payment(cursor, lead_id, charge.id, amount, amount_usd, currency, payment_date, workshop_type)
        
        # Add payment record
        self.add_payment_record(cursor, lead_id, customer.email, customer.name, charge.id, amount, amount_usd, currency, payment_date, workshop_type)
    
    def charge_already_processed(self, cursor, charge_id):
        """Check if charge is already processed as part of payment intent"""
        cursor.execute("SELECT id FROM payments WHERE stripe_payment_intent_id = ?", (charge_id,))
        return cursor.fetchone() is not None
    
    def detect_workshop_type(self, payment_intent):
        """Detect workshop type from payment intent"""
        description = payment_intent.description or ""
        metadata = payment_intent.metadata or {}
        
        # Check metadata first
        if metadata.get('workshop_type'):
            return metadata['workshop_type']
        
        # Check description
        for workshop in self.workshop_keywords:
            if workshop.lower() in description.lower():
                return workshop
        
        return 'Unknown'
    
    def detect_workshop_type_from_charge(self, charge):
        """Detect workshop type from charge"""
        description = charge.description or ""
        metadata = charge.metadata or {}
        
        # Check metadata first
        if metadata.get('workshop_type'):
            return metadata['workshop_type']
        
        # Check description
        for workshop in self.workshop_keywords:
            if workshop.lower() in description.lower():
                return workshop
        
        return 'Unknown'
    
    def update_lead_with_payment(self, cursor, lead_id, stripe_payment_id, amount, amount_usd, currency, payment_date, workshop_type):
        """Update lead record with payment information"""
        cursor.execute("""
            UPDATE leads 
            SET status = CASE 
                    WHEN status IN ('Workshop Complete', 'Alumni Network') THEN status
                    ELSE 'Enrolled'
                END,
                workshop_type = COALESCE(NULLIF(workshop_type, ''), ?),
                workshop_price_usd = COALESCE(workshop_price_usd, ?),
                workshop_currency = COALESCE(workshop_currency, ?),
                payment_status = 'Paid',
                stripe_payment_id = COALESCE(stripe_payment_id, ?),
                payment_date = COALESCE(payment_date, ?),
                payment_amount = COALESCE(payment_amount, ?),
                enrolled_date = COALESCE(enrolled_date, ?),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (workshop_type, amount_usd, currency, stripe_payment_id, payment_date, amount, payment_date, lead_id))
    
    def add_payment_record(self, cursor, lead_id, email, customer_name, stripe_payment_id, amount, amount_usd, currency, payment_date, workshop_type):
        """Add payment to payments table"""
        # Check if payment already exists
        cursor.execute("SELECT id FROM payments WHERE stripe_payment_intent_id = ?", (stripe_payment_id,))
        
        if not cursor.fetchone():
            cursor.execute("""
                INSERT INTO payments (
                    lead_id, stripe_payment_intent_id, amount, currency, status,
                    workshop_type, payment_date, customer_email, customer_name
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                lead_id, stripe_payment_id, amount_usd, currency, 'succeeded',
                workshop_type, payment_date, email, customer_name
            ))
    
    def update_customer_record(self, cursor, lead_id, customer, email, customer_name, payment_count):
        """Update or create customer record"""
        # Check if customer exists
        cursor.execute("SELECT id, total_spent_usd, workshops_enrolled FROM customers WHERE customer_email = ?", (email,))
        result = cursor.fetchone()
        
        # Calculate total spent from payments
        cursor.execute("SELECT SUM(amount) FROM payments WHERE customer_email = ?", (email,))
        total_spent = cursor.fetchone()[0] or 0
        
        if result:
            # Update existing customer
            customer_segment = 'repeat' if payment_count > 1 else 'new'
            cursor.execute("""
                UPDATE customers 
                SET total_spent_usd = ?,
                    workshops_enrolled = ?,
                    customer_segment = ?,
                    stripe_customer_id = ?,
                    last_purchase_date = (
                        SELECT MAX(payment_date) FROM payments WHERE customer_email = ?
                    ),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (total_spent, payment_count, customer_segment, customer.id, email, result[0]))
        else:
            # Create new customer
            customer_segment = 'repeat' if payment_count > 1 else 'new'
            cursor.execute("""
                INSERT INTO customers (
                    lead_id, customer_email, stripe_customer_id, total_spent_usd,
                    workshops_enrolled, customer_segment,
                    last_purchase_date, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                lead_id, email, customer.id, total_spent,
                payment_count, customer_segment,
                datetime.now(), datetime.now(), datetime.now()
            ))
    
    def generate_summary_report(self):
        """Generate summary report of sync results"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Get total counts
            cursor.execute("SELECT COUNT(*) FROM leads WHERE stripe_payment_id IS NOT NULL")
            total_leads = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*), SUM(amount) FROM payments")
            payment_stats = cursor.fetchone()
            
            cursor.execute("SELECT customer_segment, COUNT(*) FROM customers GROUP BY customer_segment")
            segment_stats = cursor.fetchall()
            
            # Find repeat customers
            cursor.execute("""
                SELECT customer_email, customer_name, COUNT(*) as payment_count, SUM(amount) as total_spent 
                FROM payments 
                WHERE customer_email NOT LIKE '<EMAIL>'
                GROUP BY customer_email 
                HAVING COUNT(*) > 1 
                ORDER BY payment_count DESC, total_spent DESC 
                LIMIT 10
            """)
            repeat_customers = cursor.fetchall()
            
            # Print comprehensive report
            print("\n" + "="*80)
            print("🎉 ENHANCED STRIPE SYNC SUMMARY")
            print("="*80)
            print(f"📊 Total Leads with Payments: {total_leads}")
            print(f"💳 Total Payment Records: {payment_stats[0] or 0}")
            print(f"💰 Total Revenue: ${payment_stats[1]:,.2f}" if payment_stats[1] else "💰 Total Revenue: $0.00")
            
            print("\n🎯 Customer Segmentation:")
            print("-" * 40)
            for segment, count in segment_stats:
                print(f"  {segment.title()}: {count} customers")
            
            if repeat_customers:
                print(f"\n🔄 Top Repeat Customers:")
                print("-" * 60)
                for email, name, payment_count, total_spent in repeat_customers:
                    name_display = name or "No name"
                    print(f"  {email} ({name_display})")
                    print(f"    {payment_count} payments • ${total_spent:.2f} total")
            else:
                print("\n🔄 No repeat customers found with valid emails")
            
            print("="*80 + "\n")
            
        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
        finally:
            conn.close()

def main():
    parser = argparse.ArgumentParser(description='Enhanced Stripe customer and payment sync')
    parser.add_argument('--days', type=int, default=30, help='Number of days to sync (default: 30)')
    parser.add_argument('--all', action='store_true', help='Sync all historical customers')
    
    args = parser.parse_args()
    
    syncer = EnhancedStripeSync()
    
    if args.all:
        confirm = input("This will sync ALL historical customers and payments. Continue? (yes/no): ")
        if confirm.lower() != 'yes':
            print("Sync cancelled.")
            return
    
    syncer.sync_all_customers(days_back=args.days, sync_all=args.all)

if __name__ == "__main__":
    main()