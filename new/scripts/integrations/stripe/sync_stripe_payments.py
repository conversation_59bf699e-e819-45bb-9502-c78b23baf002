#!/usr/bin/env python3
"""
Stripe Historical Payment Sync
PURPOSE: Pull all historical payments from Stripe and update database
USAGE: python sync_stripe_payments.py [--days N] [--all]
"""

import os
import sys
import json
import sqlite3
import logging
from datetime import datetime, timedelta
import stripe
import argparse

# Add scripts root to path for config import
scripts_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, scripts_root)

from config import settings, api_config, setup_logging

# Stripe configuration
stripe.api_key = api_config.stripe_secret_key

# Database path from centralized config
DB_PATH = settings.database_path

# Configure logging using centralized system
logger = setup_logging('stripe_sync')

class StripePaymentSync:
    def __init__(self):
        self.db_path = DB_PATH
        self.workshop_keywords = [
            'AI Essentials',
            'AI Practitioner',
            'Agentic AI',
            'Vibe Coding',
            'AI for PMs',
            'AI for UX'
        ]
        self.customer_cache = {}  # Cache Stripe customer data to avoid repeated API calls
        
    def sync_payments(self, days_back=None, sync_all=False):
        """Sync payments from Stripe to local database"""
        logger.info(f"Starting Stripe payment sync - {'All time' if sync_all else f'Last {days_back} days'}")
        
        # Set date filter
        created_filter = {}
        if not sync_all and days_back:
            start_date = datetime.now() - timedelta(days=days_back)
            created_filter = {'gte': int(start_date.timestamp())}
        
        # Fetch successful payments
        payments_processed = 0
        has_more = True
        starting_after = None
        
        while has_more:
            # Fetch payment intents
            payment_intents = stripe.PaymentIntent.list(
                limit=100,
                created=created_filter,
                starting_after=starting_after
            )
            
            for payment_intent in payment_intents.data:
                if payment_intent.status == 'succeeded':
                    self.process_payment(payment_intent)
                    payments_processed += 1
            
            has_more = payment_intents.has_more
            if has_more:
                starting_after = payment_intents.data[-1].id
        
        logger.info(f"Processed {payments_processed} successful payments")
        
        # Also sync charges for older payments (only if no payment intents found)
        # Skip charges if we already have payment intents to avoid duplicates
        if payments_processed == 0:
            logger.info("No payment intents found, syncing charges...")
            self.sync_charges(days_back, sync_all)
        else:
            logger.info("Skipping charge sync to avoid duplicates with payment intents")
        
        # Sync all Stripe customers to ensure leads table completeness
        self.sync_all_stripe_customers()
        
        # Generate summary report
        self.generate_summary_report()
    
    def sync_charges(self, days_back=None, sync_all=False):
        """Sync charges (for older Stripe implementations)"""
        logger.info("Syncing charges...")
        
        created_filter = {}
        if not sync_all and days_back:
            start_date = datetime.now() - timedelta(days=days_back)
            created_filter = {'gte': int(start_date.timestamp())}
        
        charges_processed = 0
        has_more = True
        starting_after = None
        
        while has_more:
            charges = stripe.Charge.list(
                limit=100,
                created=created_filter,
                starting_after=starting_after
            )
            
            for charge in charges.data:
                if charge.paid and not charge.refunded:
                    self.process_charge(charge)
                    charges_processed += 1
            
            has_more = charges.has_more
            if has_more:
                starting_after = charges.data[-1].id
        
        logger.info(f"Processed {charges_processed} successful charges")
    
    def sync_all_stripe_customers(self):
        """Sync all Stripe customers to ensure leads table completeness"""
        logger.info("🔄 Syncing all Stripe customers to leads table...")
        
        customers_processed = 0
        customers_missing_from_leads = 0
        has_more = True
        starting_after = None
        
        while has_more:
            # Fetch Stripe customers
            customers = stripe.Customer.list(
                limit=100,
                starting_after=starting_after
            )
            
            for customer in customers.data:
                try:
                    # Skip customers without email
                    if not customer.email:
                        continue
                    
                    # Check if customer exists in leads table
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    cursor.execute("SELECT id FROM leads WHERE email = ?", (customer.email,))
                    lead_exists = cursor.fetchone()
                    
                    if not lead_exists:
                        # Customer missing from leads - create entry
                        customer_name = customer.name or customer.email.split('@')[0].replace('.', ' ').replace('_', ' ').title()
                        
                        cursor.execute("""
                            INSERT INTO leads (
                                lead_id, email, full_name, status, created_time,
                                account_name, campaign_name, account_id, campaign_id,
                                lead_source, data_source, updated_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            f"stripe_customer_{customer.id}", customer.email, customer_name,
                            'New', datetime.fromtimestamp(customer.created),
                            'Stripe Customer', 'Direct Customer', 'stripe', 'stripe',
                            'direct', 'stripe_customer_sync', datetime.now()
                        ))
                        
                        conn.commit()
                        customers_missing_from_leads += 1
                        logger.info(f"Added missing customer to leads: {customer.email} ({customer_name})")
                    
                    conn.close()
                    customers_processed += 1
                    
                except Exception as e:
                    logger.error(f"Error processing customer {customer.id}: {str(e)}")
            
            has_more = customers.has_more
            if has_more:
                starting_after = customers.data[-1].id
        
        logger.info(f"✅ Processed {customers_processed} Stripe customers")
        logger.info(f"📝 Added {customers_missing_from_leads} missing customers to leads table")
    
    def get_customer_name_from_stripe(self, payment_intent):
        """Get customer name from Stripe API"""
        try:
            # First try to get name from payment intent customer
            if payment_intent.customer:
                if payment_intent.customer not in self.customer_cache:
                    customer = stripe.Customer.retrieve(payment_intent.customer)
                    self.customer_cache[payment_intent.customer] = customer
                else:
                    customer = self.customer_cache[payment_intent.customer]
                
                if customer.name:
                    return customer.name
            
            # Try to get name from charges billing details
            if hasattr(payment_intent, 'charges') and payment_intent.charges and payment_intent.charges.data:
                charge = payment_intent.charges.data[0]
                if charge.billing_details and charge.billing_details.name:
                    return charge.billing_details.name
            
            # Try to get from receipt email (extract name part)
            if payment_intent.receipt_email:
                name_part = payment_intent.receipt_email.split('@')[0]
                # Convert email username to readable name
                name_parts = name_part.replace('.', ' ').replace('_', ' ').split()
                return ' '.join(word.capitalize() for word in name_parts)
            
            return None
            
        except Exception as e:
            logger.warning(f"Could not retrieve customer name for payment {payment_intent.id}: {str(e)}")
            return None
    
    def process_payment(self, payment_intent):
        """Process a single payment intent"""
        try:
            # Extract customer info with enhanced email detection
            email = payment_intent.receipt_email
            
            # Try to get email from charges if available
            if not email:
                try:
                    if hasattr(payment_intent, 'charges') and payment_intent.charges and payment_intent.charges.data:
                        email = payment_intent.charges.data[0].billing_details.email
                except:
                    pass
            
            # Try to get email from customer object
            if not email and payment_intent.customer:
                try:
                    if payment_intent.customer not in self.customer_cache:
                        customer = stripe.Customer.retrieve(payment_intent.customer)
                        self.customer_cache[payment_intent.customer] = customer
                    else:
                        customer = self.customer_cache[payment_intent.customer]
                    
                    if customer.email:
                        email = customer.email
                except Exception as e:
                    logger.warning(f"Could not retrieve customer email for {payment_intent.customer}: {str(e)}")
            
            if not email:
                logger.warning(f"No email found for payment {payment_intent.id}")
                # Still process payment without email for counting purposes
                email = f"unknown_{payment_intent.id}@stripe.com"
            
            # Get customer name from Stripe API
            customer_name = self.get_customer_name_from_stripe(payment_intent)
            if not customer_name:
                # Fallback to email username
                customer_name = email.split('@')[0].replace('.', ' ').replace('_', ' ').title()
            
            # Extract metadata
            lead_id = payment_intent.metadata.get('lead_id')
            workshop_type = payment_intent.metadata.get('workshop_type')
            
            # Try to detect workshop type from description if not in metadata
            if not workshop_type and payment_intent.description:
                for workshop in self.workshop_keywords:
                    if workshop.lower() in payment_intent.description.lower():
                        workshop_type = workshop
                        break
            
            # Payment details
            amount = payment_intent.amount / 100
            currency = payment_intent.currency.upper()
            payment_date = datetime.fromtimestamp(payment_intent.created)
            
            # Convert to USD for consistent reporting (approximate rates)
            if currency == 'INR':
                amount_usd = amount / 83.0  # Approximate INR to USD conversion
            else:
                amount_usd = amount
            
            # Update all database tables
            self.update_all_tables(
                email=email,
                customer_name=customer_name,
                lead_id=lead_id,
                workshop_type=workshop_type,
                amount=amount,
                amount_usd=amount_usd,
                currency=currency,
                payment_date=payment_date,
                stripe_payment_id=payment_intent.id,
                stripe_customer_id=payment_intent.customer
            )
            
        except Exception as e:
            logger.error(f"Error processing payment {payment_intent.id}: {str(e)}")
    
    def process_charge(self, charge):
        """Process a single charge (legacy)"""
        try:
            email = charge.billing_details.email or charge.receipt_email
            if not email:
                logger.warning(f"No email found for charge {charge.id}")
                # Still process charge without email for counting purposes
                email = f"unknown_{charge.id}@stripe.com"
            
            # Get customer name from charge
            customer_name = None
            if charge.billing_details and charge.billing_details.name:
                customer_name = charge.billing_details.name
            else:
                # Fallback to email username
                customer_name = email.split('@')[0].replace('.', ' ').replace('_', ' ').title()
            
            # Extract metadata
            lead_id = charge.metadata.get('lead_id') if charge.metadata else None
            workshop_type = charge.metadata.get('workshop_type') if charge.metadata else None
            
            # Try to detect from description
            if not workshop_type and charge.description:
                for workshop in self.workshop_keywords:
                    if workshop.lower() in charge.description.lower():
                        workshop_type = workshop
                        break
            
            amount = charge.amount / 100
            currency = charge.currency.upper()
            payment_date = datetime.fromtimestamp(charge.created)
            
            # Convert to USD for consistent reporting (approximate rates)
            if currency == 'INR':
                amount_usd = amount / 83.0  # Approximate INR to USD conversion
            else:
                amount_usd = amount
            
            # Use comprehensive sync for charges too
            self.update_all_tables(
                email=email,
                customer_name=customer_name,
                lead_id=lead_id,
                workshop_type=workshop_type,
                amount=amount,
                amount_usd=amount_usd,
                currency=currency,
                payment_date=payment_date,
                stripe_payment_id=charge.id,
                stripe_customer_id=charge.customer if hasattr(charge, 'customer') else None
            )
            
        except Exception as e:
            logger.error(f"Error processing charge {charge.id}: {str(e)}")
    
    def update_all_tables(self, email, customer_name, lead_id, workshop_type, amount, amount_usd, currency, payment_date, stripe_payment_id, stripe_customer_id=None):
        """Update leads, payments, and customers tables with comprehensive data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # First, find or create/update the lead
            existing_lead_id = self.update_lead_record(cursor, email, customer_name, lead_id, workshop_type, amount_usd, currency, payment_date, stripe_payment_id, amount)
            
            # Then, ensure payment is recorded in payments table
            self.update_payment_record(cursor, existing_lead_id, email, customer_name, workshop_type, amount, amount_usd, currency, payment_date, stripe_payment_id)
            
            # Finally, update or create customer record
            self.update_customer_record(cursor, existing_lead_id, email, customer_name, stripe_customer_id, amount_usd, payment_date)
            
            conn.commit()
            logger.info(f"Updated all tables for payment {stripe_payment_id} (lead: {existing_lead_id})")
            
        except Exception as e:
            logger.error(f"Database error for payment {stripe_payment_id}: {str(e)}")
            conn.rollback()
        finally:
            conn.close()
    
    def update_lead_record(self, cursor, email, customer_name, lead_id, workshop_type, amount_usd, currency, payment_date, stripe_payment_id, amount):
        """Update or create lead record"""
        # First, try to find the lead
        if lead_id:
            cursor.execute("SELECT id, status FROM leads WHERE id = ?", (lead_id,))
        else:
            cursor.execute("""
                SELECT id, status FROM leads 
                WHERE email = ? 
                ORDER BY created_time DESC 
                LIMIT 1
            """, (email,))
        
        lead_result = cursor.fetchone()
        
        if lead_result:
            existing_lead_id, current_status = lead_result
            
            # Only update if not already marked as completed
            if current_status not in ['Workshop Complete', 'Alumni Network']:
                cursor.execute("""
                    UPDATE leads 
                    SET status = CASE 
                            WHEN status IN ('Workshop Complete', 'Alumni Network') THEN status
                            ELSE 'Enrolled'
                        END,
                        full_name = COALESCE(NULLIF(full_name, ''), ?),
                        workshop_type = COALESCE(workshop_type, ?),
                        workshop_price_usd = ?,
                        workshop_currency = ?,
                        payment_status = 'Paid',
                        stripe_payment_id = ?,
                        payment_date = ?,
                        payment_amount = ?,
                        enrolled_date = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (customer_name, workshop_type, amount_usd, currency, stripe_payment_id, payment_date, amount, payment_date, existing_lead_id))
        else:
            # Create a new lead record if not found
            cursor.execute("""
                INSERT INTO leads (
                    lead_id, email, full_name, status, workshop_type,
                    workshop_price_usd, workshop_currency, created_time,
                    account_name, campaign_name, account_id, campaign_id,
                    payment_status, stripe_payment_id, payment_date, 
                    payment_amount, enrolled_date, lead_source, data_source
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                f"stripe_{stripe_payment_id}", email, customer_name,
                'Enrolled', workshop_type, amount_usd, currency, payment_date,
                'Direct Purchase', 'Direct Purchase', 'direct', 'direct',
                'Paid', stripe_payment_id, payment_date, amount, payment_date,
                'direct', 'stripe'
            ))
            
            existing_lead_id = cursor.lastrowid
        
        return existing_lead_id
    
    def update_payment_record(self, cursor, lead_id, email, customer_name, workshop_type, amount, amount_usd, currency, payment_date, stripe_payment_id):
        """Update or create payment record"""
        # Check if payment already exists
        cursor.execute("SELECT id FROM payments WHERE stripe_payment_intent_id = ?", (stripe_payment_id,))
        
        if not cursor.fetchone():
            # Insert new payment record (handle NULL workshop_type)
            cursor.execute("""
                INSERT INTO payments (
                    lead_id, stripe_payment_intent_id, amount, currency, status,
                    workshop_type, payment_date, customer_email, customer_name
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                lead_id, stripe_payment_id, amount_usd, currency, 'succeeded',
                workshop_type or 'Unknown', payment_date, email, customer_name
            ))
            logger.info(f"Added payment record: {stripe_payment_id} for ${amount_usd}")
    
    def update_customer_record(self, cursor, lead_id, email, customer_name, stripe_customer_id, amount_usd, payment_date):
        """Update or create customer record"""
        # Check if customer already exists
        cursor.execute("SELECT id, total_spent_usd, workshops_enrolled FROM customers WHERE customer_email = ?", (email,))
        customer_result = cursor.fetchone()
        
        if customer_result:
            customer_id, current_spent, current_workshops = customer_result
            
            # Update existing customer - mark as repeat if they have previous purchases
            new_segment = 'repeat' if current_workshops >= 1 else 'new'
            logger.info(f"Updating customer {customer_name}: previous spent ${current_spent}, workshops {current_workshops} -> segment: {new_segment}")
            
            cursor.execute("""
                UPDATE customers 
                SET total_spent_usd = total_spent_usd + ?,
                    workshops_enrolled = workshops_enrolled + 1,
                    last_purchase_date = ?,
                    customer_segment = ?,
                    stripe_customer_id = COALESCE(stripe_customer_id, ?),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (amount_usd, payment_date, new_segment, stripe_customer_id, customer_id))
        else:
            # Create new customer record
            logger.info(f"Creating new customer: {customer_name} ({email}) - ${amount_usd}")
            cursor.execute("""
                INSERT INTO customers (
                    lead_id, customer_email, stripe_customer_id, total_spent_usd,
                    workshops_enrolled, last_purchase_date, customer_segment,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                lead_id, email, stripe_customer_id, amount_usd,
                1, payment_date, 'new',
                datetime.now(), datetime.now()
            ))
    
    def update_lead_payment(self, email, lead_id, workshop_type, amount, amount_usd, currency, payment_date, stripe_payment_id):
        """Update lead record with payment information"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # First, try to find the lead
            if lead_id:
                cursor.execute("SELECT id, status FROM leads WHERE id = ?", (lead_id,))
            else:
                cursor.execute("""
                    SELECT id, status FROM leads 
                    WHERE email = ? 
                    ORDER BY created_time DESC 
                    LIMIT 1
                """, (email,))
            
            lead_result = cursor.fetchone()
            
            if lead_result:
                existing_lead_id, current_status = lead_result
                
                # Only update if not already marked as completed
                if current_status not in ['Workshop Complete', 'Alumni Network']:
                    cursor.execute("""
                        UPDATE leads 
                        SET status = CASE 
                                WHEN status IN ('Workshop Complete', 'Alumni Network') THEN status
                                ELSE 'Enrolled'
                            END,
                            workshop_type = COALESCE(workshop_type, ?),
                            workshop_price_usd = ?,
                            workshop_currency = ?,
                            payment_status = 'Paid',
                            stripe_payment_id = ?,
                            payment_date = ?,
                            payment_amount = ?,
                            enrolled_date = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (workshop_type, amount_usd, currency, stripe_payment_id, payment_date, amount, payment_date, existing_lead_id))
                    
                    logger.info(f"Updated lead {existing_lead_id} with payment {stripe_payment_id}")
            else:
                # Create a new lead record if not found
                cursor.execute("""
                    INSERT INTO leads (
                        lead_id, email, full_name, status, workshop_type,
                        workshop_price_usd, workshop_currency, created_time,
                        account_name, campaign_name, account_id, campaign_id,
                        payment_status, stripe_payment_id, payment_date, 
                        payment_amount, enrolled_date, lead_source, data_source
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    f"stripe_{stripe_payment_id}", email, email.split('@')[0],
                    'Enrolled', workshop_type, amount_usd, currency, payment_date,
                    'Direct Purchase', 'Direct Purchase', 'direct', 'direct',
                    'Paid', stripe_payment_id, payment_date, amount, payment_date,
                    'direct', 'stripe'
                ))
                
                existing_lead_id = cursor.lastrowid
                logger.info(f"Created new lead {existing_lead_id} from Stripe payment")
            
            # Check if payment already logged
            cursor.execute("""
                SELECT id FROM lead_interactions 
                WHERE message LIKE ? 
                LIMIT 1
            """, (f"%{stripe_payment_id}%",))
            
            if not cursor.fetchone():
                # Log the payment interaction
                cursor.execute("""
                    INSERT INTO lead_interactions 
                    (lead_id, interaction_type, user_email, subject, message, outcome, interaction_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    existing_lead_id,
                    'enrollment',
                    'stripe_sync',
                    'Historical Payment Sync',
                    f"Payment {stripe_payment_id}: {amount} {currency} for {workshop_type or 'Workshop'}",
                    'Payment recorded',
                    payment_date
                ))
            
            conn.commit()
            
        except Exception as e:
            logger.error(f"Database error for payment {stripe_payment_id}: {str(e)}")
            conn.rollback()
        finally:
            conn.close()
    
    def generate_summary_report(self):
        """Generate a summary report of the sync"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Get summary statistics
            cursor.execute("""
                SELECT 
                    COUNT(DISTINCT id) as total_enrolled,
                    COUNT(DISTINCT CASE WHEN workshop_type IS NOT NULL THEN id END) as with_workshop_type,
                    SUM(workshop_price_usd) as total_revenue_usd,
                    COUNT(DISTINCT workshop_type) as unique_workshops,
                    COUNT(DISTINCT CASE WHEN workshop_currency = 'USD' THEN id END) as usd_payments,
                    COUNT(DISTINCT CASE WHEN workshop_currency = 'INR' THEN id END) as inr_payments,
                    SUM(CASE WHEN workshop_currency = 'USD' THEN workshop_price_usd ELSE 0 END) as usd_revenue,
                    SUM(CASE WHEN workshop_currency = 'INR' THEN payment_amount ELSE 0 END) as inr_revenue_original
                FROM leads 
                WHERE status IN ('Enrolled', 'Workshop Complete', 'Alumni Network')
            """)
            
            stats = cursor.fetchone()
            
            # Get breakdown by workshop
            cursor.execute("""
                SELECT 
                    workshop_type,
                    COUNT(*) as count,
                    SUM(workshop_price_usd) as revenue
                FROM leads 
                WHERE status IN ('Enrolled', 'Workshop Complete', 'Alumni Network')
                  AND workshop_type IS NOT NULL
                GROUP BY workshop_type
                ORDER BY count DESC
            """)
            
            workshop_breakdown = cursor.fetchall()
            
            # Print report
            print("\n" + "="*60)
            print("STRIPE PAYMENT SYNC SUMMARY")
            print("="*60)
            print(f"Total Enrolled Leads: {stats[0]}")
            print(f"Leads with Workshop Type: {stats[1]}")
            print(f"USD Payments: {stats[4]}")
            print(f"INR Payments: {stats[5]}")
            print(f"USD Revenue: ${stats[6]:,.2f}" if stats[6] else "USD Revenue: $0.00")
            print(f"INR Revenue: ₹{stats[7]:,.2f}" if stats[7] else "INR Revenue: ₹0.00")
            print(f"Total Revenue (USD equivalent): ${stats[2]:,.2f}" if stats[2] else "Total Revenue: N/A")
            print(f"Unique Workshop Types: {stats[3]}")
            print("\nWorkshop Breakdown:")
            print("-"*40)
            
            for workshop, count, revenue in workshop_breakdown:
                print(f"{workshop}: {count} enrollments, ${revenue:,.2f}" if revenue else f"{workshop}: {count} enrollments")
            
            print("="*60 + "\n")
            
            # Log the summary
            logger.info(f"Sync complete - {stats[0]} total enrolled leads")
            
        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
        finally:
            conn.close()

def main():
    parser = argparse.ArgumentParser(description='Sync Stripe payments to local database')
    parser.add_argument('--days', type=int, default=30, help='Number of days to sync (default: 30)')
    parser.add_argument('--all', action='store_true', help='Sync all historical payments')
    parser.add_argument('--dry-run', action='store_true', help='Preview without making changes')
    
    args = parser.parse_args()
    
    if args.dry_run:
        logger.info("DRY RUN MODE - No changes will be made")
    
    syncer = StripePaymentSync()
    
    if args.all:
        confirm = input("This will sync ALL historical payments. Continue? (yes/no): ")
        if confirm.lower() != 'yes':
            print("Sync cancelled.")
            return
    
    syncer.sync_payments(days_back=args.days, sync_all=args.all)

if __name__ == "__main__":
    main()
