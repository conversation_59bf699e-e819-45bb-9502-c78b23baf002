#!/usr/bin/env python3
"""
Stripe Data Validation Script
PURPOSE: Compare our database against Stripe dashboard to ensure data accuracy
USAGE: python validate_stripe_data.py
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime, timedelta
from collections import defaultdict
import stripe

# Add scripts root to path for config import
scripts_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, scripts_root)

from config import settings, api_config, setup_logging

# Stripe configuration
stripe.api_key = api_config.stripe_secret_key

# Database path from centralized config
DB_PATH = settings.database_path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StripeDataValidator:
    def __init__(self):
        self.db_path = DB_PATH
        self.stripe_stats = {
            'total_payment_intents': 0,
            'successful_payment_intents': 0,
            'total_charges': 0,
            'successful_charges': 0,
            'unique_customers': set(),
            'currencies': defaultdict(int),
            'amounts_by_currency': defaultdict(float),
            'payment_methods': defaultdict(int),
            'date_range': {'earliest': None, 'latest': None}
        }
        self.db_stats = {
            'total_records': 0,
            'paid_records': 0,
            'unique_emails': set(),
            'currencies': defaultdict(int),
            'amounts_by_currency': defaultdict(float),
            'duplicate_stripe_ids': [],
            'missing_stripe_ids': []
        }
    
    def validate_all_data(self):
        """Run comprehensive validation against Stripe"""
        print("🔍 Starting comprehensive Stripe data validation...")
        print("="*60)
        
        # Step 1: Fetch all data from Stripe
        print("📡 Fetching data from Stripe API...")
        self.fetch_stripe_payment_intents()
        self.fetch_stripe_charges()
        
        # Step 2: Analyze our database
        print("🗄️  Analyzing local database...")
        self.analyze_database()
        
        # Step 3: Compare and validate
        print("⚖️  Comparing data sources...")
        self.compare_data()
        
        # Step 4: Generate comprehensive report
        print("📊 Generating validation report...")
        self.generate_validation_report()
        
        # Step 5: Check for specific issues
        print("🔧 Checking for data quality issues...")
        self.check_data_quality()
    
    def fetch_stripe_payment_intents(self):
        """Fetch all payment intents from Stripe"""
        logger.info("Fetching payment intents from Stripe...")
        
        has_more = True
        starting_after = None
        
        while has_more:
            try:
                payment_intents = stripe.PaymentIntent.list(
                    limit=100,
                    starting_after=starting_after
                )
                
                for pi in payment_intents.data:
                    self.stripe_stats['total_payment_intents'] += 1
                    
                    if pi.status == 'succeeded':
                        self.stripe_stats['successful_payment_intents'] += 1
                        
                        # Track customer info
                        email = pi.receipt_email
                        if email:
                            self.stripe_stats['unique_customers'].add(email)
                        
                        # Track currency and amounts
                        currency = pi.currency.upper()
                        amount = pi.amount / 100
                        self.stripe_stats['currencies'][currency] += 1
                        self.stripe_stats['amounts_by_currency'][currency] += amount
                        
                        # Track date range
                        created_date = datetime.fromtimestamp(pi.created)
                        if not self.stripe_stats['date_range']['earliest'] or created_date < self.stripe_stats['date_range']['earliest']:
                            self.stripe_stats['date_range']['earliest'] = created_date
                        if not self.stripe_stats['date_range']['latest'] or created_date > self.stripe_stats['date_range']['latest']:
                            self.stripe_stats['date_range']['latest'] = created_date
                
                has_more = payment_intents.has_more
                if has_more:
                    starting_after = payment_intents.data[-1].id
                    
            except Exception as e:
                logger.error(f"Error fetching payment intents: {str(e)}")
                break
        
        logger.info(f"Fetched {self.stripe_stats['total_payment_intents']} payment intents ({self.stripe_stats['successful_payment_intents']} successful)")
    
    def fetch_stripe_charges(self):
        """Fetch all charges from Stripe"""
        logger.info("Fetching charges from Stripe...")
        
        has_more = True
        starting_after = None
        
        while has_more:
            try:
                charges = stripe.Charge.list(
                    limit=100,
                    starting_after=starting_after
                )
                
                for charge in charges.data:
                    self.stripe_stats['total_charges'] += 1
                    
                    if charge.paid and not charge.refunded:
                        self.stripe_stats['successful_charges'] += 1
                        
                        # Track customer info
                        email = charge.billing_details.email or charge.receipt_email
                        if email:
                            self.stripe_stats['unique_customers'].add(email)
                        
                        # Track payment methods
                        if charge.payment_method_details:
                            payment_method = charge.payment_method_details.type
                            self.stripe_stats['payment_methods'][payment_method] += 1
                
                has_more = charges.has_more
                if has_more:
                    starting_after = charges.data[-1].id
                    
            except Exception as e:
                logger.error(f"Error fetching charges: {str(e)}")
                break
        
        logger.info(f"Fetched {self.stripe_stats['total_charges']} charges ({self.stripe_stats['successful_charges']} successful)")
    
    def analyze_database(self):
        """Analyze our local database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Count total records
            cursor.execute("SELECT COUNT(*) FROM leads")
            self.db_stats['total_records'] = cursor.fetchone()[0]
            
            # Count paid records
            cursor.execute("SELECT COUNT(*) FROM leads WHERE payment_status = 'Paid'")
            self.db_stats['paid_records'] = cursor.fetchone()[0]
            
            # Get unique emails (excluding unknown ones)
            cursor.execute("SELECT DISTINCT email FROM leads WHERE email NOT LIKE '<EMAIL>'")
            real_emails = cursor.fetchall()
            self.db_stats['unique_emails'] = set([email[0] for email in real_emails])
            
            # Currency breakdown
            cursor.execute("""
                SELECT workshop_currency, COUNT(*), SUM(payment_amount) 
                FROM leads 
                WHERE payment_status = 'Paid' AND workshop_currency IS NOT NULL
                GROUP BY workshop_currency
            """)
            
            for currency, count, total_amount in cursor.fetchall():
                self.db_stats['currencies'][currency] = count
                self.db_stats['amounts_by_currency'][currency] = total_amount or 0
            
            # Check for duplicate Stripe IDs
            cursor.execute("""
                SELECT stripe_payment_id, COUNT(*) 
                FROM leads 
                WHERE stripe_payment_id IS NOT NULL 
                GROUP BY stripe_payment_id 
                HAVING COUNT(*) > 1
            """)
            
            duplicates = cursor.fetchall()
            self.db_stats['duplicate_stripe_ids'] = [(stripe_id, count) for stripe_id, count in duplicates]
            
        except Exception as e:
            logger.error(f"Error analyzing database: {str(e)}")
        finally:
            conn.close()
    
    def compare_data(self):
        """Compare Stripe data with our database"""
        self.discrepancies = {
            'payment_count_diff': 0,
            'currency_differences': {},
            'amount_differences': {},
            'missing_customers': set(),
            'extra_customers': set()
        }
        
        # Compare payment counts
        stripe_successful_total = self.stripe_stats['successful_payment_intents'] + self.stripe_stats['successful_charges']
        self.discrepancies['payment_count_diff'] = self.db_stats['paid_records'] - stripe_successful_total
        
        # Compare currencies
        all_currencies = set(self.stripe_stats['currencies'].keys()) | set(self.db_stats['currencies'].keys())
        for currency in all_currencies:
            stripe_count = self.stripe_stats['currencies'].get(currency, 0)
            db_count = self.db_stats['currencies'].get(currency, 0)
            if stripe_count != db_count:
                self.discrepancies['currency_differences'][currency] = {
                    'stripe': stripe_count,
                    'database': db_count,
                    'difference': db_count - stripe_count
                }
        
        # Compare customer lists
        stripe_customers = self.stripe_stats['unique_customers']
        db_customers = self.db_stats['unique_emails']
        
        self.discrepancies['missing_customers'] = stripe_customers - db_customers
        self.discrepancies['extra_customers'] = db_customers - stripe_customers
    
    def check_data_quality(self):
        """Check for data quality issues"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        issues = []
        
        try:
            # Check for missing essential fields
            cursor.execute("SELECT COUNT(*) FROM leads WHERE email IS NULL OR email = ''")
            missing_emails = cursor.fetchone()[0]
            if missing_emails > 0:
                issues.append(f"❌ {missing_emails} records missing email addresses")
            
            # Check for missing payment amounts
            cursor.execute("SELECT COUNT(*) FROM leads WHERE payment_status = 'Paid' AND (payment_amount IS NULL OR payment_amount = 0)")
            missing_amounts = cursor.fetchone()[0]
            if missing_amounts > 0:
                issues.append(f"❌ {missing_amounts} paid records missing payment amounts")
            
            # Check for inconsistent currencies
            cursor.execute("""
                SELECT COUNT(*) FROM leads 
                WHERE payment_status = 'Paid' 
                AND (workshop_currency IS NULL OR workshop_currency = '')
            """)
            missing_currencies = cursor.fetchone()[0]
            if missing_currencies > 0:
                issues.append(f"❌ {missing_currencies} paid records missing currency information")
            
            # Check for very old or future dates
            cursor.execute("""
                SELECT COUNT(*) FROM leads 
                WHERE created_time < '2020-01-01' OR created_time > datetime('now', '+1 day')
            """)
            invalid_dates = cursor.fetchone()[0]
            if invalid_dates > 0:
                issues.append(f"❌ {invalid_dates} records have invalid dates")
            
            # Check for duplicate stripe payment IDs
            if self.db_stats['duplicate_stripe_ids']:
                issues.append(f"❌ {len(self.db_stats['duplicate_stripe_ids'])} duplicate Stripe payment IDs found")
            
            if not issues:
                issues.append("✅ No major data quality issues detected")
            
            self.data_quality_issues = issues
            
        except Exception as e:
            logger.error(f"Error checking data quality: {str(e)}")
            self.data_quality_issues = [f"❌ Error checking data quality: {str(e)}"]
        finally:
            conn.close()
    
    def generate_validation_report(self):
        """Generate comprehensive validation report"""
        print("\n" + "="*80)
        print("🔍 STRIPE DATA VALIDATION REPORT")
        print("="*80)
        
        # Stripe API Statistics
        print("\n📡 STRIPE API DATA:")
        print("-" * 40)
        print(f"Total Payment Intents: {self.stripe_stats['total_payment_intents']:,}")
        print(f"Successful Payment Intents: {self.stripe_stats['successful_payment_intents']:,}")
        print(f"Total Charges: {self.stripe_stats['total_charges']:,}")
        print(f"Successful Charges: {self.stripe_stats['successful_charges']:,}")
        print(f"Unique Customers (with emails): {len(self.stripe_stats['unique_customers']):,}")
        
        if self.stripe_stats['date_range']['earliest'] and self.stripe_stats['date_range']['latest']:
            print(f"Date Range: {self.stripe_stats['date_range']['earliest'].strftime('%Y-%m-%d')} to {self.stripe_stats['date_range']['latest'].strftime('%Y-%m-%d')}")
        
        print("\nStripe Currency Breakdown:")
        for currency, count in self.stripe_stats['currencies'].items():
            amount = self.stripe_stats['amounts_by_currency'][currency]
            if currency == 'USD':
                print(f"  {currency}: {count:,} payments, ${amount:,.2f}")
            elif currency == 'INR':
                print(f"  {currency}: {count:,} payments, ₹{amount:,.2f}")
            else:
                print(f"  {currency}: {count:,} payments, {amount:,.2f}")
        
        # Database Statistics
        print("\n🗄️  LOCAL DATABASE DATA:")
        print("-" * 40)
        print(f"Total Records: {self.db_stats['total_records']:,}")
        print(f"Paid Records: {self.db_stats['paid_records']:,}")
        print(f"Unique Real Emails: {len(self.db_stats['unique_emails']):,}")
        
        print("\nDatabase Currency Breakdown:")
        for currency, count in self.db_stats['currencies'].items():
            amount = self.db_stats['amounts_by_currency'][currency]
            if currency == 'USD':
                print(f"  {currency}: {count:,} payments, ${amount:,.2f}")
            elif currency == 'INR':
                print(f"  {currency}: {count:,} payments, ₹{amount:,.2f}")
            else:
                print(f"  {currency}: {count:,} payments, {amount:,.2f}")
        
        # Comparison Results
        print("\n⚖️  COMPARISON RESULTS:")
        print("-" * 40)
        
        stripe_total_successful = self.stripe_stats['successful_payment_intents'] + self.stripe_stats['successful_charges']
        
        if self.discrepancies['payment_count_diff'] == 0:
            print("✅ Payment counts match perfectly!")
        else:
            print(f"❌ Payment count discrepancy: {self.discrepancies['payment_count_diff']:+d}")
            print(f"   (Database: {self.db_stats['paid_records']:,} vs Stripe: {stripe_total_successful:,})")
        
        if self.discrepancies['currency_differences']:
            print("\n❌ Currency count differences:")
            for currency, diff in self.discrepancies['currency_differences'].items():
                print(f"   {currency}: Database {diff['database']:,} vs Stripe {diff['stripe']:,} (diff: {diff['difference']:+d})")
        else:
            print("✅ Currency counts match!")
        
        if self.discrepancies['missing_customers']:
            print(f"\n❌ {len(self.discrepancies['missing_customers'])} customers in Stripe but not in database:")
            for email in list(self.discrepancies['missing_customers'])[:5]:
                print(f"   - {email}")
            if len(self.discrepancies['missing_customers']) > 5:
                print(f"   ... and {len(self.discrepancies['missing_customers']) - 5} more")
        
        if self.discrepancies['extra_customers']:
            print(f"\n❌ {len(self.discrepancies['extra_customers'])} customers in database but not found in Stripe:")
            for email in list(self.discrepancies['extra_customers'])[:5]:
                print(f"   - {email}")
            if len(self.discrepancies['extra_customers']) > 5:
                print(f"   ... and {len(self.discrepancies['extra_customers']) - 5} more")
        
        # Data Quality Issues
        print("\n🔧 DATA QUALITY ASSESSMENT:")
        print("-" * 40)
        for issue in self.data_quality_issues:
            print(issue)
        
        # Summary
        print("\n📋 SUMMARY:")
        print("-" * 40)
        
        total_issues = (
            abs(self.discrepancies['payment_count_diff']) + 
            len(self.discrepancies['currency_differences']) +
            len(self.discrepancies['missing_customers']) +
            len(self.discrepancies['extra_customers']) +
            len([issue for issue in self.data_quality_issues if issue.startswith('❌')])
        )
        
        if total_issues == 0:
            print("🎉 EXCELLENT: All data matches perfectly with Stripe!")
        elif total_issues <= 5:
            print("✅ GOOD: Minor discrepancies found, but data is mostly accurate")
        elif total_issues <= 15:
            print("⚠️  MODERATE: Several discrepancies found, review recommended")
        else:
            print("❌ SIGNIFICANT: Major discrepancies found, immediate review required")
        
        print(f"Total issues detected: {total_issues}")
        print("="*80)
    
    def generate_detailed_discrepancy_report(self):
        """Generate detailed report of specific discrepancies for investigation"""
        if not any([
            self.discrepancies['payment_count_diff'],
            self.discrepancies['currency_differences'],
            self.discrepancies['missing_customers'],
            self.discrepancies['extra_customers']
        ]):
            return
        
        print("\n🔍 DETAILED DISCREPANCY INVESTIGATION:")
        print("="*60)
        
        # Sample transactions for investigation
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Show some unknown email records
            cursor.execute("""
                SELECT stripe_payment_id, workshop_currency, payment_amount, created_time
                FROM leads 
                WHERE email LIKE '<EMAIL>'
                ORDER BY payment_amount DESC
                LIMIT 10
            """)
            
            unknown_records = cursor.fetchall()
            if unknown_records:
                print("\n📝 Sample records with unknown emails:")
                print("Payment ID | Currency | Amount | Date")
                print("-" * 50)
                for record in unknown_records:
                    stripe_id, currency, amount, date = record
                    print(f"{stripe_id[:20]}... | {currency or 'N/A'} | {amount or 'N/A'} | {date}")
        
        except Exception as e:
            logger.error(f"Error generating detailed report: {str(e)}")
        finally:
            conn.close()

def main():
    validator = StripeDataValidator()
    validator.validate_all_data()
    validator.generate_detailed_discrepancy_report()
    
    print(f"\n💡 RECOMMENDATIONS:")
    print("- If payment counts are higher in database than Stripe, check for duplicates")
    print("- If customers are missing, verify Stripe webhook configuration")
    print("- If amounts don't match, check currency conversion logic")
    print("- Review logs for any API errors during sync")

if __name__ == "__main__":
    main()