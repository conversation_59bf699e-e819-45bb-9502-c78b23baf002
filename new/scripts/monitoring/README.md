# Monitoring and Automation Scripts

This directory contains cron jobs, health checks, and system monitoring scripts for the Modern AI Pro infrastructure.

## Cron Job Scripts

### `cron_health_check.sh`
- **Purpose**: System health monitoring
- **Schedule**: Regular intervals (typically hourly)
- **Features**: Database connectivity, service status, resource utilization
- **Output**: Health status reports and alerts

### `cron_lead_collection.sh`
- **Purpose**: Automated lead collection from various sources
- **Schedule**: Daily or as configured
- **Features**: Facebook Lead Ads collection, data validation, duplicate checking
- **Output**: New leads processed and imported

### `cron_weekly_report.sh`
- **Purpose**: Generate weekly summary reports
- **Schedule**: Weekly (typically Sunday nights)
- **Features**: Lead metrics, conversion rates, system performance
- **Output**: Executive summary reports

## Configuration

### `setup_cron_jobs.sh`
- **Purpose**: Install and configure all cron jobs
- **Usage**: Run once to set up automated scheduling
- **Features**: Crontab management, log rotation, error handling

### `crontab_entries.txt`
- **Purpose**: Cron job schedule definitions
- **Usage**: Reference file for cron timing configurations
- **Format**: Standard crontab format with documentation

## Monitoring Features

1. **Database Health**: Connection testing, query performance monitoring
2. **Service Status**: API endpoint availability, response time monitoring
3. **Resource Utilization**: Disk space, memory usage, CPU monitoring
4. **Lead Processing**: Import success rates, data quality metrics
5. **Error Tracking**: Automated error detection and alerting

## Log Management

All monitoring scripts write to the `../logs/` directory:
- Health check logs with timestamps
- Lead collection status and metrics
- Error reports and system alerts
- Weekly report generation logs

## Alerting

Monitoring scripts can be configured to send alerts via:
- Email notifications
- Slack/Discord webhooks
- SMS alerts for critical issues
- Dashboard status updates

## Maintenance

1. **Regular Review**: Check monitoring logs weekly
2. **Threshold Updates**: Adjust alerting thresholds as needed
3. **Script Updates**: Update monitoring logic as system evolves
4. **Log Rotation**: Ensure log files don't consume excessive disk space

## Setup Instructions

1. Make scripts executable:
   ```bash
   chmod +x *.sh
   ```

2. Configure cron jobs:
   ```bash
   ./setup_cron_jobs.sh
   ```

3. Verify installation:
   ```bash
   crontab -l
   ```

4. Monitor logs:
   ```bash
   tail -f ../logs/health_check_*.log
   ```

## Dependencies

- Bash shell environment
- Database connectivity tools
- System monitoring utilities (ps, df, free, etc.)
- Email/notification services configuration