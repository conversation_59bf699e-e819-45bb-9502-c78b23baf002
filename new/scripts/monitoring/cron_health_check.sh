#!/bin/bash

# Facebook Account Health Check Cron Job
# Runs daily to monitor account accessibility and permissions

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/logs/health_check_$(date +%Y%m%d).log"

echo "===============================================" >> "$LOG_FILE"
echo "Health Check Started: $(date)" >> "$LOG_FILE"

# Activate virtual environment and run health check
cd "$SCRIPT_DIR/facebook"
source ../venv/bin/activate

# Run account health check (using unified script)
python3 fb_account_manager.py health-check >> "$LOG_FILE" 2>&1

EXIT_CODE=$?
echo "Health Check Completed: $(date) (Exit Code: $EXIT_CODE)" >> "$LOG_FILE"

# Send alert if critical issues found
if [ $EXIT_CODE -eq 2 ]; then
    echo "🚨 CRITICAL: Facebook account health check failed!" >> "$LOG_FILE"
    # Could add email/Slack notification here
fi

# Rotate logs (keep last 30 days for health checks)
find "$SCRIPT_DIR/logs" -name "health_check_*.log" -mtime +30 -delete

exit $EXIT_CODE
