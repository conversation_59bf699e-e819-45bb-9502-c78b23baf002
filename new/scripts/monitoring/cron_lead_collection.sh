#!/bin/bash

# Facebook Lead Collection Cron Job
# Runs every 5 minutes to collect new leads (400/day target)

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/logs/lead_collection_$(date +%Y%m%d).log"

echo "===============================================" >> "$LOG_FILE"
echo "Lead Collection Started: $(date)" >> "$LOG_FILE"

# Activate virtual environment and run lead collection
cd "$SCRIPT_DIR/facebook"
source ../venv/bin/activate

# Run lead collection with 6-hour lookback (overlap for safety)
python3 fb_leads_scheduler.py 6 >> "$LOG_FILE" 2>&1

EXIT_CODE=$?
echo "Lead Collection Completed: $(date) (Exit Code: $EXIT_CODE)" >> "$LOG_FILE"

# Rotate logs (keep last 7 days)
find "$SCRIPT_DIR/logs" -name "lead_collection_*.log" -mtime +7 -delete

exit $EXIT_CODE
