#!/bin/bash

# Facebook Lead Management Cron Job Setup
# Sets up automated jobs for lead collection and account health monitoring
# Based on Aug 30 PRD requirements for 400 leads/day scale

echo "🚀 Setting up Facebook Lead Management Cron Jobs"
echo "=================================================="

# Get the absolute path to the scripts directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FACEBOOK_DIR="$SCRIPT_DIR/facebook"
VENV_PATH="$SCRIPT_DIR/venv/bin/activate"

echo "📁 Script directory: $SCRIPT_DIR"
echo "🐍 Virtual environment: $VENV_PATH"

# Check if virtual environment exists
if [ ! -f "$VENV_PATH" ]; then
    echo "❌ Virtual environment not found at $VENV_PATH"
    echo "Please set up the virtual environment first"
    exit 1
fi

# Create log directory
LOG_DIR="$SCRIPT_DIR/logs"
mkdir -p "$LOG_DIR"
echo "📝 Log directory: $LOG_DIR"

# Create cron job scripts with proper error handling and logging

# 1. Lead Collection Script (every 5 minutes during business hours)
cat > "$SCRIPT_DIR/cron_lead_collection.sh" << 'EOF'
#!/bin/bash

# Facebook Lead Collection Cron Job
# Runs every 5 minutes to collect new leads (400/day target)

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/logs/lead_collection_$(date +%Y%m%d).log"

echo "===============================================" >> "$LOG_FILE"
echo "Lead Collection Started: $(date)" >> "$LOG_FILE"

# Activate virtual environment and run lead collection
cd "$SCRIPT_DIR/facebook"
source ../venv/bin/activate

# Run lead collection with 6-hour lookback (overlap for safety)
python3 fb_leads_scheduler.py 6 >> "$LOG_FILE" 2>&1

EXIT_CODE=$?
echo "Lead Collection Completed: $(date) (Exit Code: $EXIT_CODE)" >> "$LOG_FILE"

# Rotate logs (keep last 7 days)
find "$SCRIPT_DIR/logs" -name "lead_collection_*.log" -mtime +7 -delete

exit $EXIT_CODE
EOF

# 2. Account Health Check Script (daily at 6 AM)
cat > "$SCRIPT_DIR/cron_health_check.sh" << 'EOF'
#!/bin/bash

# Facebook Account Health Check Cron Job
# Runs daily to monitor account accessibility and permissions

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/logs/health_check_$(date +%Y%m%d).log"

echo "===============================================" >> "$LOG_FILE"
echo "Health Check Started: $(date)" >> "$LOG_FILE"

# Activate virtual environment and run health check
cd "$SCRIPT_DIR/facebook"
source ../venv/bin/activate

# Run account health check
python3 fb_account_health_check.py >> "$LOG_FILE" 2>&1

EXIT_CODE=$?
echo "Health Check Completed: $(date) (Exit Code: $EXIT_CODE)" >> "$LOG_FILE"

# Send alert if critical issues found
if [ $EXIT_CODE -eq 2 ]; then
    echo "🚨 CRITICAL: Facebook account health check failed!" >> "$LOG_FILE"
    # Could add email/Slack notification here
fi

# Rotate logs (keep last 30 days for health checks)
find "$SCRIPT_DIR/logs" -name "health_check_*.log" -mtime +30 -delete

exit $EXIT_CODE
EOF

# 3. Weekly Summary Report (Sundays at 8 AM)
cat > "$SCRIPT_DIR/cron_weekly_report.sh" << 'EOF'
#!/bin/bash

# Weekly Lead Performance Report
# Generates summary of lead collection and conversion metrics

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/logs/weekly_report_$(date +%Y%m%d).log"

echo "===============================================" >> "$LOG_FILE"
echo "Weekly Report Started: $(date)" >> "$LOG_FILE"

# Activate virtual environment
cd "$SCRIPT_DIR/facebook"
source ../venv/bin/activate

# Generate weekly metrics summary
echo "📊 Weekly Lead Collection Summary" >> "$LOG_FILE"
echo "Week ending: $(date)" >> "$LOG_FILE"
echo "=================================" >> "$LOG_FILE"

# Count leads from last 7 days (would query database in production)
echo "This would generate weekly performance metrics" >> "$LOG_FILE"

echo "Weekly Report Completed: $(date)" >> "$LOG_FILE"
EOF

# Make scripts executable
chmod +x "$SCRIPT_DIR/cron_lead_collection.sh"
chmod +x "$SCRIPT_DIR/cron_health_check.sh"
chmod +x "$SCRIPT_DIR/cron_weekly_report.sh"

echo "✅ Cron job scripts created successfully"

# Generate crontab entries
CRON_ENTRIES="$SCRIPT_DIR/crontab_entries.txt"

cat > "$CRON_ENTRIES" << EOF
# Facebook Lead Management Cron Jobs
# Generated on $(date)

# Lead collection every 5 minutes during business hours (9 AM - 9 PM)
# Targeting 400 leads/day with continuous monitoring
*/5 9-21 * * * $SCRIPT_DIR/cron_lead_collection.sh

# Account health monitoring daily at 6 AM
0 6 * * * $SCRIPT_DIR/cron_health_check.sh

# Weekly performance report on Sundays at 8 AM  
0 8 * * 0 $SCRIPT_DIR/cron_weekly_report.sh

# Cleanup old logs monthly (1st of month at 2 AM)
0 2 1 * * find $LOG_DIR -name "*.log" -mtime +30 -delete
EOF

echo "📋 Cron job entries created at: $CRON_ENTRIES"
echo ""
echo "🔧 To install these cron jobs, run:"
echo "   crontab -l > /tmp/current_cron"
echo "   cat $CRON_ENTRIES >> /tmp/current_cron"
echo "   crontab /tmp/current_cron"
echo ""
echo "📊 Cron job schedule:"
echo "   • Lead collection: Every 5 minutes (9 AM - 9 PM)"
echo "   • Health checks: Daily at 6 AM"
echo "   • Weekly reports: Sundays at 8 AM"
echo ""
echo "📝 Logs will be stored in: $LOG_DIR"
echo ""
echo "⚠️  Next steps:"
echo "   1. Test the scripts manually first"
echo "   2. Install cron jobs using commands above"
echo "   3. Monitor logs for the first few days"
echo "   4. Set up alerts for critical failures (exit code 2)"

# Test script permissions and paths
echo ""
echo "🧪 Testing script setup..."

if [ -f "$FACEBOOK_DIR/fb_leads_scheduler.py" ]; then
    echo "✅ Lead scheduler script found"
else
    echo "❌ Lead scheduler script missing"
fi

if [ -f "$FACEBOOK_DIR/fb_account_health_check.py" ]; then
    echo "✅ Health check script found"
else
    echo "❌ Health check script missing"
fi

if [ -f "$VENV_PATH" ]; then
    echo "✅ Virtual environment found"
else
    echo "❌ Virtual environment missing"
fi

echo ""
echo "🎉 Facebook lead management cron job setup complete!"
echo "Ready for 400 leads/day automated collection and monitoring"