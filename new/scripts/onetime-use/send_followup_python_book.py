#!/usr/bin/env python3
"""
Follow-up Sales Email <PERSON>t for Mahalakshmi - Python Book Gift
Sends follow-up emails with Python book attachment to prospects from Google Sheets
Uses service account with domain-wide delegation
"""

import os
import sys
import base64
import time
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from google.oauth2 import service_account
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import gspread

# Add the config directory to the path for shared resources
script_dir = os.path.dirname(os.path.abspath(__file__))
config_dir = os.path.join(script_dir, '..', '..', 'config')
sys.path.append(config_dir)

# Service account file path
SERVICE_ACCOUNT_FILE = os.path.join(config_dir, 'modernaipro-b601d7749092.json')

# Google Sheets configuration  
SHEET_URL = 'https://docs.google.com/spreadsheets/d/1Bh4OHSHwMo7ukK0Fa8kvCOZgpAtba_WTru3SgeK65Wg/edit?gid=*********#gid=*********'

# PDF attachment path
PDF_ATTACHMENT_PATH = '/Users/<USER>/Code/modernaipro/mai-administrative/new/assets/Python for the Executive Mind.pdf'

def generate_followup_email_content(recipient_name):
    """Generate the follow-up email content with Python book theme"""
    
    # Personalize greeting using recipient name
    greeting = f"Hey {recipient_name}," if recipient_name else "Hey,"
    
    return f"""<html><body>
<p>{greeting}</p>

<p>Following up on my previous email about our AI Bootcamp with <strong>Dr. Balaji Viswanathan</strong>...</p>

<p><strong>Here's the reality:</strong> While everyone talks about AI replacing jobs, the executives who understand Python are the ones <em>creating</em> the AI-powered roles.</p>

<div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;">
<h3 style="color: #007bff; margin-top: 0;">The 10-20-100 Formula</h3>
<ul style="margin: 0;">
<li><strong>10 hours:</strong> Master Python fundamentals for business</li>
<li><strong>20 hours:</strong> Complete our Modern AI Pro program</li>
<li><strong>100x:</strong> Your career potential return on investment</li>
</ul>
</div>

<p>Most programming courses teach you to think like a software engineer. But as a business leader, you need to think like an <strong>executive who happens to code</strong>.</p>

<p>That's why I'm gifting you <strong>"Python for the Executive Mind"</strong> - a 73-page guide I wrote specifically for professionals like you who need to:</p>

<ul>
<li>Understand AI capabilities without the technical jargon</li>
<li>Automate business processes intelligently</li>
<li>Make data-driven decisions with confidence</li>
<li>Lead AI initiatives in your organization</li>
</ul>

<p>This isn't another "Hello World" tutorial. It's your strategic advantage in the AI-driven business landscape.</p>

<p>The book is attached to this email - no strings attached, no opt-ins required. Just my way of helping fellow business leaders prepare for what's coming.</p>

<p>After you've had a chance to review it, I'd love to discuss how our AI Bootcamp can take you from Python basics to AI mastery.</p>

<p><strong>Ready to join the executives who are shaping the AI future instead of fearing it?</strong></p>

<p>Best,<br><br>
Mahalakshmi<br><br>
Head of Sales, Modern AI</p>

<p style="font-size: 12px; color: #666; margin-top: 30px;">
P.S. The most successful executives I know didn't wait for AI to mature. They learned Python early and positioned themselves as AI leaders. Your competitive advantage starts with page 1 of the attached guide.
</p>
</body></html>"""

def get_email_list_from_sheets():
    """Fetch email list from Google Sheets using gspread (same as original script)"""
    SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
    
    try:
        # Authenticate using gspread (same approach as original script)
        credentials = Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES)
        gc = gspread.authorize(credentials)
        
        # Open sheet by URL and get the specific worksheet
        sheet = gc.open_by_url(SHEET_URL)
        worksheet = sheet.worksheet("Email")  # Get worksheet by name
        
        # Get all records as list of dictionaries (automatically skips header row)
        records = worksheet.get_all_records()
        
        # Extract emails from the 'Email' column and names from first column
        emails = []
        names = []
        
        for record in records:
            if 'Email' in record and record['Email']:
                email = str(record['Email']).strip()
                # Use first column as recipient name (typically 'Name' column)
                name = str(record.get('Name', '')).strip()
                
                if email and '@' in email:  # Basic email validation
                    emails.append(email)
                    names.append(name)
        
        print(f"📧 Found {len(emails)} valid email addresses from worksheet")
        
        # Return all email/name pairs
        if emails:
            return list(zip(emails, names))
        else:
            return []
        
    except Exception as error:
        print(f"❌ Error fetching emails from Google Sheets: {error}")
        print(f"Make sure the sheet is shared with: <EMAIL>")
        return []

def create_email_message_with_attachment(sender, to, subject, message_text, attachment_path):
    """Create email message with PDF attachment"""
    message = MIMEMultipart()
    message['to'] = to
    message['from'] = sender
    message['subject'] = subject
    message['reply-to'] = '<EMAIL>'  # Ensure replies go to Mahalakshmi

    # Add HTML content
    msg = MIMEText(message_text, 'html')
    message.attach(msg)
    
    # Add PDF attachment
    if os.path.exists(attachment_path):
        filename = os.path.basename(attachment_path)
        
        with open(attachment_path, 'rb') as attachment:
            part = MIMEBase('application', 'pdf')
            part.set_payload(attachment.read())
            
        encoders.encode_base64(part)
        part.add_header(
            'Content-Disposition',
            f'attachment; filename= {filename}'
        )
        
        message.attach(part)
        print(f"✅ PDF attachment added: {filename}")
    else:
        print(f"❌ Warning: PDF file not found at {attachment_path}")

    raw = base64.urlsafe_b64encode(message.as_bytes()).decode()
    return {'raw': raw}

def send_message(service, user_id, message):
    """Send email message via Gmail API"""
    try:
        message = service.users().messages().send(
            userId=user_id, body=message).execute()
        print(f"✅ Message sent successfully - ID: {message['id']}")
        return message
    except HttpError as error:
        print(f"❌ Error sending email: {error}")
        return None

def send_followup_email(recipient_email, recipient_name=""):
    """Send follow-up email with Python book to a specific recipient"""
    SCOPES = ['https://www.googleapis.com/auth/gmail.send']
    
    try:
        credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES)
        
        # Use domain-wide delegation to send as Mahalakshmi
        delegated_credentials = credentials.with_subject('<EMAIL>')
        service = build('gmail', 'v1', credentials=delegated_credentials)

        subject = "🎁 Your Free Python Executive Guide (10-20-100 Formula Inside)"
        message_text = generate_followup_email_content(recipient_name)

        email_message = create_email_message_with_attachment(
            '<EMAIL>', 
            recipient_email, 
            subject, 
            message_text,
            PDF_ATTACHMENT_PATH
        )

        result = send_message(service, 'me', email_message)
        return result is not None
        
    except Exception as error:
        print(f"❌ Error sending email to {recipient_email}: {error}")
        return False

def main():
    """Main function to send follow-up emails with Python book to all prospects"""
    print("🚀 Starting Python Book Follow-up Email Campaign")
    print("=" * 50)
    
    # Check if service account file exists
    if not os.path.exists(SERVICE_ACCOUNT_FILE):
        print(f"❌ Service account file not found: {SERVICE_ACCOUNT_FILE}")
        return
    
    # Check if PDF file exists
    if not os.path.exists(PDF_ATTACHMENT_PATH):
        print(f"❌ PDF file not found: {PDF_ATTACHMENT_PATH}")
        return
    
    print(f"📎 PDF attachment ready: {os.path.basename(PDF_ATTACHMENT_PATH)}")
    
    # Get email list from Google Sheets
    print("📥 Fetching email list from Google Sheets...")
    contact_list = get_email_list_from_sheets()
    
    if not contact_list:
        print("❌ No emails found. Exiting.")
        return
    
    print(f"📋 Found {len(contact_list)} contact(s)")
    
    # Print contacts for verification
    print("\n📋 Contact(s) from the spreadsheet:")
    for i, (email, name) in enumerate(contact_list, 1):
        print(f"   {i}. {name} <{email}>")
    
    # Ask if user wants to send to first email only (test mode)
    print(f"\n🧪 Would you like to test with just the first email first?")
    test_response = input("Type 'TEST' to send to first email only, or 'ALL' to send to all: ").strip().upper()
    
    if test_response == 'TEST':
        contact_list = contact_list[:1]  # Only send to first contact
        print(f"🧪 TEST MODE: Sending to first contact only")
    elif test_response == 'ALL':
        print(f"📧 FULL CAMPAIGN: Sending to all {len(contact_list)} contacts")
    else:
        print("❌ Invalid response. Exiting.")
        return
    
    # Ask for final confirmation
    print(f"\n⚠️  PYTHON BOOK FOLLOW-UP CAMPAIGN: Ready to send to {len(contact_list)} recipient(s)")
    print("Subject: 🎁 Your Free Python Executive Guide (10-20-100 Formula Inside)")
    print("From: <EMAIL>")
    print("Attachment: Python for the Executive Mind.pdf (1.6 MB)")
    
    response = input(f"\n🤔 Are you sure you want to send {len(contact_list)} emails? Type 'SEND' to confirm: ").strip()
    if response != 'SEND':
        print("❌ Email sending cancelled.")
        return
    
    # Send emails with delay between each
    success_count = 0
    total_count = len(contact_list)
    
    for i, (email, name) in enumerate(contact_list, 1):
        print(f"\n📤 Sending email {i}/{total_count} to: {name} <{email}>")
        
        success = send_followup_email(email, name)
        if success:
            success_count += 1
        
        # Add delay between emails to avoid rate limiting
        if i < total_count:
            print("⏱️  Waiting 2 seconds before next email...")
            time.sleep(2)
    
    print("\n" + "=" * 50)
    print(f"📊 Email Campaign Summary:")
    print(f"   Total emails: {total_count}")
    print(f"   Successful: {success_count}")
    print(f"   Failed: {total_count - success_count}")
    print(f"   Success rate: {(success_count/total_count)*100:.1f}%")
    print(f"   PDF attachment: {os.path.basename(PDF_ATTACHMENT_PATH)}")
    print("🎉 Campaign completed!")

if __name__ == "__main__":
    main()