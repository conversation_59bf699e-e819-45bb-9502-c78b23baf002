#!/usr/bin/env python3
"""
Sales Email Script for <PERSON>hala<PERSON>hmi
Sends AI Bootcamp sales emails to prospects from Google Sheets
Uses service account with domain-wide delegation
"""

import os
import sys
import base64
import time
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from google.oauth2 import service_account
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import csv

# Add the config directory to the path for shared resources
script_dir = os.path.dirname(os.path.abspath(__file__))
# Go up to /scripts/core/email-automation -> /scripts -> /new -> /config
project_root = os.path.join(script_dir, '..', '..', '..')
config_dir = os.path.join(project_root, 'config')
sys.path.append(config_dir)

# Service account file path
SERVICE_ACCOUNT_FILE = os.path.join(config_dir, 'modernaipro-b601d7749092.json')

# CSV file configuration  
CSV_FILE_PATH = os.path.join(project_root, 'data', 'imports', 'US Lead CRM - Email Mrktg.csv')

def generate_sales_email_content(recipient_name):
    """Generate urgent sales email content for Aug 15th class"""
    
    first_name = recipient_name.split()[0] if recipient_name else "there"
    
    return f"""<html><body>
<p>Hi {first_name},</p>

<p><strong>4 days left, 4 spots remaining</strong></p>

<p>Our AI Bootcamp with Dr. Balaji starts <strong>August 15th</strong> - this is your final chance to join.</p>

<p><strong>What you'll build:</strong> 5 interactive mini-projects covering real AI applications (LLMs, chatbots, RAG systems). Build a strong grasp of both AI foundations and enterprise implementation - combining AI strategy with hands-on development.</p>

<p><strong>When:</strong> Aug 15-17 (Fri evening + weekend)<br>
<strong>Spots left:</strong> Only 4<br>
<strong>Registration closes:</strong> Tomorrow</p>

<p>Ready to secure your spot?</p>

<p>Best,<br>
Mahalakshmi<br>
Modern AI</p>
</body></html>"""

def create_email_message(sender, to, subject, message_text):
    """Create email message without attachments"""
    message = MIMEMultipart()
    message['to'] = to
    message['from'] = sender
    message['subject'] = subject
    message['reply-to'] = '<EMAIL>'  # Ensure replies go to Mahalakshmi

    msg = MIMEText(message_text, 'html')
    message.attach(msg)

    raw = base64.urlsafe_b64encode(message.as_bytes()).decode()
    return {'raw': raw}

def send_message(service, user_id, message):
    """Send email message via Gmail API"""
    try:
        message = service.users().messages().send(
            userId=user_id, body=message).execute()
        print(f"✅ Message sent successfully - ID: {message['id']}")
        return message
    except HttpError as error:
        print(f"❌ Error sending email: {error}")
        return None

def debug_sheets_access():
    """Debug function to check what sheets and worksheets are accessible"""
    SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
    
    try:
        credentials = Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES)
        gc = gspread.authorize(credentials)
        
        print("🔍 Debugging Google Sheets access...")
        
        # Try to open the sheet
        sheet = gc.open_by_url(SHEET_URL)
        print(f"✅ Successfully opened sheet: '{sheet.title}'")
        
        # List all worksheets
        worksheets = sheet.worksheets()
        print(f"📋 Found {len(worksheets)} worksheets:")
        for i, ws in enumerate(worksheets):
            print(f"   {i}: '{ws.title}' (id: {ws.id}) - {ws.row_count} rows, {ws.col_count} cols")
        
        # Try to get data from the "Email" worksheet specifically
        try:
            worksheet = sheet.worksheet("Email")
            print(f"\n🔍 Examining 'Email' worksheet: '{worksheet.title}'")
            
            # Get first few rows to see the structure
            try:
                values = worksheet.get_all_values()
                if values:
                    print(f"📊 First few rows of data:")
                    for i, row in enumerate(values[:5]):  # Show first 5 rows
                        print(f"   Row {i+1}: {row}")
                    
                    # Show column headers (first row)
                    if len(values) > 0:
                        headers = values[0]
                        print(f"📝 Column headers: {headers}")
                        
                        # Look for email-like columns
                        email_columns = []
                        for j, header in enumerate(headers):
                            if 'email' in header.lower() or 'mail' in header.lower():
                                email_columns.append((j, header))
                        
                        if email_columns:
                            print(f"📧 Potential email columns found: {email_columns}")
                        else:
                            print("❌ No obvious email columns found")
                else:
                    print("❌ No data found in worksheet")
                    
            except Exception as e:
                print(f"❌ Error reading worksheet data: {e}")
        
        except Exception as e:
            print(f"❌ Could not access 'Email' worksheet: {e}")
            print("Available worksheets:")
            for i, ws in enumerate(worksheets):
                print(f"   {i}: '{ws.title}'")
        
        return True
        
    except Exception as error:
        print(f"❌ Error accessing Google Sheets: {error}")
        print(f"Make sure the sheet is shared with: <EMAIL>")
        return False

def get_email_list_from_csv():
    """Fetch email list from CSV file"""
    import csv
    
    try:
        if not os.path.exists(CSV_FILE_PATH):
            print(f"❌ CSV file not found: {CSV_FILE_PATH}")
            return []
        
        emails = []
        names = []
        
        with open(CSV_FILE_PATH, 'r', newline='', encoding='utf-8') as csvfile:
            # Auto-detect the CSV format
            sample = csvfile.read(1024)
            csvfile.seek(0)
            sniffer = csv.Sniffer()
            
            # Check if CSV has headers by looking at first line
            has_header = sniffer.has_header(sample)
            
            reader = csv.reader(csvfile)
            
            for i, row in enumerate(reader):
                # Skip empty rows
                if not row or len(row) < 3:
                    continue
                
                # Parse format: Date,Name,Email,Phone
                try:
                    date = row[0].strip()
                    name = row[1].strip()
                    email = row[2].strip()
                    phone = row[3].strip() if len(row) > 3 else ""
                    
                    # Basic email validation
                    if email and '@' in email.lower() and '.' in email.lower():
                        emails.append(email.lower())
                        names.append(name)
                        
                except (IndexError, AttributeError):
                    print(f"⚠️ Skipping malformed row {i+1}: {row}")
                    continue
        
        print(f"📧 Found {len(emails)} valid email addresses from CSV file")
        
        # Return all email/name pairs
        if emails:
            return list(zip(emails, names))
        else:
            return []
        
    except Exception as error:
        print(f"❌ Error reading CSV file: {error}")
        return []

def send_sales_email(recipient_email, recipient_name=""):
    """Send sales email to a specific recipient"""
    SCOPES = ['https://www.googleapis.com/auth/gmail.send']
    
    try:
        credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES)
        
        # Use domain-wide delegation to send as Mahalakshmi
        delegated_credentials = credentials.with_subject('<EMAIL>')
        service = build('gmail', 'v1', credentials=delegated_credentials)

        subject = "🔥 Last 4 spots - AI Bootcamp starts Aug 15th"
        message_text = generate_sales_email_content(recipient_name)

        email_message = create_email_message(
            '<EMAIL>', 
            recipient_email, 
            subject, 
            message_text
        )

        result = send_message(service, 'me', email_message)
        return result is not None
        
    except Exception as error:
        print(f"❌ Error sending email to {recipient_email}: {error}")
        return False

def main():
    """Main function to send sales emails to all prospects"""
    print("🚀 Starting AI Bootcamp Sales Email Campaign")
    print("=" * 50)
    
    # Check for test mode and dry-run arguments
    import sys
    test_mode = "--test" in sys.argv or "-t" in sys.argv
    dry_run = "--dry-run" in sys.argv or "-d" in sys.argv
    
    # Debug path information
    print(f"🔍 Script directory: {script_dir}")
    print(f"🔍 Project root: {os.path.abspath(project_root)}")
    print(f"🔍 Config directory: {os.path.abspath(config_dir)}")
    print(f"🔍 Service account file path: {os.path.abspath(SERVICE_ACCOUNT_FILE)}")
    print(f"🔍 CSV file path: {os.path.abspath(CSV_FILE_PATH)}")
    
    # Check if service account file exists
    if not os.path.exists(SERVICE_ACCOUNT_FILE):
        print(f"❌ Service account file not found: {SERVICE_ACCOUNT_FILE}")
        print(f"🔍 Looking for file at: {os.path.abspath(SERVICE_ACCOUNT_FILE)}")
        return
    
    # Get email list from CSV file
    print("📥 Fetching email list from CSV file...")
    contact_list = get_email_list_from_csv()
    
    if not contact_list:
        print("❌ No emails found. Exiting.")
        return
    
    # Apply test mode filtering
    if test_mode:
        contact_list = contact_list[:1]  # Only first contact
        print(f"🧪 TEST MODE: Using only the first contact")
    
    print(f"📋 Found {len(contact_list)} contact(s)")
    
    # Print contacts for verification
    print("\n📋 Contact(s) from the CSV file:")
    for i, (email, name) in enumerate(contact_list, 1):
        print(f"   {i}. {name} <{email}>")
    
    # Ask if user wants to proceed with sending
    campaign_type = "TEST EMAIL" if test_mode else "BULK EMAIL CAMPAIGN"
    print(f"\n⚠️  {campaign_type}: Ready to send to {len(contact_list)} recipient(s)")
    print("Subject: 🔥 Last 4 spots - AI Bootcamp starts Aug 15th")
    print("From: <EMAIL>")
    
    # Skip confirmation in dry-run mode
    if dry_run:
        print(f"\n🧪 DRY RUN MODE: Simulating email send to {len(contact_list)} recipient(s)")
        print("✅ Emails would be sent with the following content:")
        
        # Show sample email content
        sample_email = generate_sales_email_content(contact_list[0][1] if contact_list else "Sample Name")
        print(f"\n📧 Sample Email Content:")
        print(f"Subject: 🔥 Last 4 spots - AI Bootcamp starts Aug 15th")
        print(f"Body: {sample_email[:200]}...")
        print(f"\n✅ All {len(contact_list)} emails would be sent successfully!")
        return
    
    confirm_text = "TEST" if test_mode else "SEND"
    response = input(f"\n🤔 Are you sure you want to send {len(contact_list)} emails? Type '{confirm_text}' to confirm: ").strip()
    if response != confirm_text:
        print("❌ Email sending cancelled.")
        return
    
    # Send emails with delay between each
    success_count = 0
    total_count = len(contact_list)
    
    for i, (email, name) in enumerate(contact_list, 1):
        print(f"\n📤 Sending email {i}/{total_count} to: {name} <{email}>")
        
        success = send_sales_email(email, name)
        if success:
            success_count += 1
        
        # Add delay between emails to avoid rate limiting
        if i < total_count:
            print("⏱️  Waiting 2 seconds before next email...")
            time.sleep(2)
    
    print("\n" + "=" * 50)
    print(f"📊 Email Campaign Summary:")
    print(f"   Total emails: {total_count}")
    print(f"   Successful: {success_count}")
    print(f"   Failed: {total_count - success_count}")
    print(f"   Success rate: {(success_count/total_count)*100:.1f}%")
    print("🎉 Campaign completed!")

if __name__ == "__main__":
    main()