#!/usr/bin/env python3
"""
TEMPORARY SCRIPT - Mark leads older than 24 hours as contacted
<PERSON> this once to clean up the lead queue for testing
"""

import sqlite3
import sys
import os

# Get database path
script_dir = os.path.dirname(os.path.abspath(__file__))
db_path = os.path.join(script_dir, '..', 'data', 'database', 'leads.db')

try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Mark old leads as contacted
    cursor.execute("""
        UPDATE leads 
        SET status = 'Contacted'
        WHERE data_source = 'facebook' 
        AND created_time < datetime('now', '-24 hours')
        AND status = 'New'
    """)
    
    updated_count = cursor.rowcount
    conn.commit()
    conn.close()
    
    print(f"✅ Marked {updated_count} old leads as 'Contacted'")
    
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)