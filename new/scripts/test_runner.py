#!/usr/bin/env python3
"""
Test Runner - Validates all test files can be imported and run
"""
import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that all test files can be imported"""
    print("🧪 Testing Test File Imports")
    print("=" * 50)
    
    test_files = [
        "tests.test_email_library",
        "tests.test_lead_email_sender", 
        "tests.test_email_campaigns",
        "tests.test_llm_generation"
    ]
    
    results = {}
    
    for test_module in test_files:
        try:
            __import__(test_module)
            print(f"✅ {test_module}: Import successful")
            results[test_module] = "SUCCESS"
        except Exception as e:
            print(f"❌ {test_module}: Import failed - {e}")
            results[test_module] = f"FAILED: {e}"
    
    print("\n" + "=" * 50)
    print("📊 Import Test Summary")
    print("=" * 50)
    
    success_count = sum(1 for r in results.values() if r == "SUCCESS")
    total_count = len(results)
    
    for module, result in results.items():
        status = "✅" if result == "SUCCESS" else "❌"
        print(f"{status} {module}: {result}")
    
    print(f"\n🏁 Results: {success_count}/{total_count} tests imported successfully")
    
    if success_count == total_count:
        print("🎉 All test files import correctly!")
        return True
    else:
        print("⚠️ Some test files have import issues")
        return False

def test_core_imports():
    """Test that core modules can be imported"""
    print("\n🔧 Testing Core Module Imports")
    print("=" * 50)
    
    core_modules = [
        "core.email_library",
        "core.lead_selector",
        "core.email_campaign_manager", 
        "core.enhanced_email_composer"
    ]
    
    results = {}
    
    for module in core_modules:
        try:
            __import__(module)
            print(f"✅ {module}: Import successful")
            results[module] = "SUCCESS"
        except Exception as e:
            print(f"❌ {module}: Import failed - {e}")
            results[module] = f"FAILED: {e}"
    
    success_count = sum(1 for r in results.values() if r == "SUCCESS")
    total_count = len(results)
    
    print(f"\n🏁 Core Module Results: {success_count}/{total_count} imported successfully")
    return success_count == total_count

def run_safe_test_methods():
    """Run safe test methods that don't require external APIs"""
    print("\n🛡️ Running Safe Test Methods (No External APIs)")
    print("=" * 50)
    
    try:
        # Test lead email sender safe methods
        from tests.test_lead_email_sender import TestLeadEmailSender
        
        # Create test instance
        tester = TestLeadEmailSender()
        
        # Test assignment mapping
        assignment_map = {3: "<EMAIL>", 9: "<EMAIL>"}
        sender = tester.get_sender_email_from_assignment(9, assignment_map)
        
        if sender == "<EMAIL>":
            print("✅ Assignment mapping test: PASSED")
        else:
            print(f"❌ Assignment mapping test: FAILED (got {sender})")
            
    except Exception as e:
        print(f"❌ Safe test methods failed: {e}")
        return False
    
    return True

def main():
    """Main test runner"""
    print("🚀 Test Suite Validation")
    print("=" * 60)
    
    # Test imports
    imports_ok = test_imports()
    core_imports_ok = test_core_imports()
    safe_tests_ok = run_safe_test_methods()
    
    print("\n" + "=" * 60)
    print("🏆 Final Results")
    print("=" * 60)
    
    print(f"📦 Test Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"🔧 Core Imports: {'✅ PASS' if core_imports_ok else '❌ FAIL'}")
    print(f"🛡️ Safe Tests: {'✅ PASS' if safe_tests_ok else '❌ FAIL'}")
    
    overall_success = imports_ok and core_imports_ok and safe_tests_ok
    
    if overall_success:
        print("\n🎉 ALL TESTS VALIDATED SUCCESSFULLY!")
        print("📝 Test files are properly structured and importable")
        print("💡 Run individual tests with their CLI interfaces:")
        print("   python3 tests/test_lead_email_sender.py")
        print("   python3 tests/test_email_campaigns.py") 
        print("   python3 tests/test_llm_generation.py")
    else:
        print("\n⚠️ Some tests failed validation")
        print("🔍 Check the errors above for details")
    
    print("\n🔗 For pytest (if installed):")
    print("   pytest tests/ -v")
    print("   pytest tests/ -v -m 'not external_api'  # Skip external API tests")

if __name__ == "__main__":
    main()