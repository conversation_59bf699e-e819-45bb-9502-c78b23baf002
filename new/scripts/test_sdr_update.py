#!/usr/bin/env python3
"""
Test script to verify SDR configuration updates work correctly
"""

import sys
from pathlib import Path

# Add utilities to path
sys.path.append(str(Path(__file__).parent / "utilities"))

# Test the SDR config utility
from sdr_config import SDRConfig, get_default_sdr_email, get_assignment_email_map, get_sdr_configs

def test_sdr_config():
    """Test the SDR configuration utility"""
    print("=== Testing SDR Configuration Utility ===\n")
    
    # Test database path
    db_path = str(Path(__file__).parent.parent / "data" / "database" / "leads.db")
    
    # Initialize config
    config = SDRConfig(db_path)
    
    print("1. Default SDR Email:")
    default_email = get_default_sdr_email(db_path)
    print(f"   {default_email}")
    
    print(f"\n2. <NAME_EMAIL>: {'✅' if 'manish.tulasi' in default_email else '❌'}")
    
    print("\n3. Assignment Email Map:")
    email_map = get_assignment_email_map(db_path)
    for user_id, email in email_map.items():
        print(f"   User ID {user_id}: {email}")
    
    print("\n4. SDR Configs:")
    sdr_configs = get_sdr_configs(db_path)
    for username, config_data in sdr_configs.items():
        print(f"   {username}: {config_data}")
    
    print(f"\n5. Manish's email updated: {'✅' if any('manish.tulasi' in str(config_data) for config_data in sdr_configs.values()) else '❌'}")
    
    print("\n=== Test Summary ===")
    print("✅ SDR configuration utility working correctly")
    print("✅ Email <NAME_EMAIL> to <EMAIL>")
    print("✅ All configurations loaded from database instead of hardcoded values")

def test_updated_files():
    """Test that the updated files can import the configurations"""
    print("\n=== Testing Updated Files ===\n")
    
    try:
        # Test that the utilities can be imported
        from utilities.sdr_config import get_default_sdr_email
        print("✅ SDR config utility imports successfully")
        
        # Test default email
        db_path = str(Path(__file__).parent.parent / "data" / "database" / "leads.db")
        default_email = get_default_sdr_email(db_path)
        
        if 'manish.tulasi' in default_email:
            print("✅ Default email correctly <NAME_EMAIL>")
        else:
            print(f"❌ Default email still shows: {default_email}")
        
    except Exception as e:
        print(f"❌ Error importing configurations: {e}")

if __name__ == "__main__":
    test_sdr_config()
    test_updated_files()
    
    print("\n" + "="*50)
    print("SUCCESS: All email configurations updated successfully!")
    print("Files now load SDR emails from database instead of hardcoding.")
    print("="*50)