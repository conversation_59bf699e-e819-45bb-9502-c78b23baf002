# Modern AI Pro - Lead Flow Testing Suite

🧪 **Comprehensive testing framework for critical lead flow functionalities**

## Overview

This interactive testing suite allows you to verify all critical components of the Modern AI Pro lead flow system in one place. Test everything from Facebook lead pulling to GPT-5 email composition with an intuitive command-line interface.

## Features

### 🎯 Core Functionality Tests
1. **📱 Facebook Lead Pulling** - Pull latest leads from Facebook Ad accounts
2. **💳 Stripe Data Integration** - Fetch recent payment and customer data  
3. **🗄️ Database Operations** - Query lead data with full content details
4. **✉️ Email Sending** - Send test emails via Gmail API
5. **🤖 GPT-5 Email Composition** - Generate personalized emails using AI
6. **🔄 Full Integration Testing** - Run all tests in sequence

### 📊 Advanced Features
- Real-time performance metrics (duration tracking)
- Detailed error reporting and debugging
- Email quality analysis and personalization scoring
- Configuration validation for all services
- Interactive menu-driven interface

## Quick Start

### 1. Install Dependencies
```bash
# Core dependencies
pip install facebook-business stripe openai python-dotenv pyyaml

# Google API dependencies (for email)
pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client
```

### 2. Configuration Setup
Ensure your `.env` file in `/config/` contains:
```bash
# Facebook
FB_EXTENDED_TOKEN=your_facebook_token
FB_APP_SECRET=your_app_secret
FB_APP_ID=your_app_id

# Stripe
STRIPE_SECRET_KEY=your_stripe_secret_key

# Azure OpenAI (GPT-5)
AZURE_ENDPOINT=your_azure_endpoint
AZURE_API_KEY=your_azure_api_key
AZURE_DEPLOYMENT=gpt-5

# Email (Service Account JSON file should be in /config/)
```

### 3. Run the Interactive Tester
```bash
cd /path/to/scripts/tests
python interactive_tester.py
```

## Usage Guide

### Interactive Menu Options

```
🚀 MODERN AI PRO - LEAD FLOW TESTING SUITE
============================================================
1. 📱 Pull Latest Facebook Lead
2. 💳 Get Latest Stripe Data  
3. 🗄️ Get Latest Lead from Database
4. ✉️ Send Test Email (to <EMAIL>)
5. 🤖 Compose Email with GPT-5
6. 🔄 Run All Tests (Full Integration)
7. 📊 View Test Reports
0. ❌ Exit
============================================================
```

### Individual Module Testing

Each module can also be tested independently:

```bash
# Test Facebook integration
cd modules && python facebook_tester.py

# Test Stripe integration  
cd modules && python stripe_tester.py

# Test database operations
cd modules && python database_tester.py

# Test email sending
cd modules && python email_tester.py

# Test GPT-5 email composition
cd modules && python gpt5_tester.py
```

## Module Details

### 📱 Facebook Tester (`modules/facebook_tester.py`)
- **Purpose**: Validates Facebook Ads API connectivity and lead retrieval
- **Tests**: API connection, account access, latest lead pulling
- **Dependencies**: facebook-business SDK
- **Key Features**: 
  - Multi-account support
  - Lead data formatting and assignment logic
  - Rate limit awareness

### 💳 Stripe Tester (`modules/stripe_tester.py`)
- **Purpose**: Validates Stripe API connectivity and payment data retrieval  
- **Tests**: API connection, payment retrieval, customer data access
- **Dependencies**: stripe SDK
- **Key Features**:
  - Payment intent analysis
  - Workshop type identification
  - Customer information enrichment

### 🗄️ Database Tester (`modules/database_tester.py`)
- **Purpose**: Validates database connectivity and data integrity
- **Tests**: Connection status, lead retrieval, interaction history
- **Dependencies**: sqlite3 (built-in)
- **Key Features**:
  - Full lead data with relationships
  - Interaction history tracking
  - Database statistics and schema info

### ✉️ Email Tester (`modules/email_tester.py`)
- **Purpose**: Validates email sending functionality via Gmail API
- **Tests**: Service account auth, email composition, delivery
- **Dependencies**: google-api-python-client, google-auth
- **Key Features**:
  - HTML email templates
  - Service account authentication
  - Delivery confirmation

### 🤖 GPT5 Tester (`modules/gpt5_tester.py`)
- **Purpose**: Validates AI-powered email generation
- **Tests**: Azure OpenAI connection, email generation, quality analysis
- **Dependencies**: openai (Azure), pyyaml
- **Key Features**:
  - Personalization scoring
  - Email quality analysis
  - Template parsing and formatting

## Test Results & Reporting

### Success Indicators
- ✅ **Green checkmarks** - Tests passed successfully
- 📊 **Performance metrics** - Response times and data counts
- 📋 **Detailed output** - Lead info, payment details, email content

### Error Handling  
- ❌ **Red X marks** - Tests failed with error details
- 🔧 **Configuration guidance** - Missing credentials or setup issues
- 📝 **Debugging info** - Stack traces and troubleshooting hints

### Sample Output
```
🔍 FACEBOOK LEAD TESTING
----------------------------------------
✅ Facebook Lead Pull: SUCCESS
📋 Latest Lead Details:
   Name: John Smith
   Email: <EMAIL>
   Source: Facebook - Invento Robotics
   Created: 2025-08-12 14:30:22
```

## Troubleshooting

### Common Issues

1. **Facebook API Errors**
   - Verify FB_EXTENDED_TOKEN is valid and not expired
   - Check ad account permissions and active campaigns
   - Ensure lead generation campaigns exist

2. **Stripe API Errors**  
   - Verify STRIPE_SECRET_KEY is correct
   - Check account permissions for payments access
   - Ensure test/live mode consistency

3. **Database Errors**
   - Verify database file exists at expected path
   - Check file permissions for read access
   - Ensure database schema is current

4. **Email Errors**
   - Verify service account JSON file location
   - Check Gmail API permissions and scopes
   - Ensure sender email has proper delegation

5. **GPT-5 Errors**
   - Verify Azure OpenAI credentials and endpoint
   - Check deployment name and model availability  
   - Ensure sufficient API quota

### Debug Mode
Set `DEBUG=True` in individual modules for verbose logging.

## Integration with Lead Flow

This testing suite validates the same components used by the production lead flow system:

- **Facebook Integration** → Lead collection automation
- **Database Operations** → Lead storage and retrieval
- **Email Generation** → AI-powered outreach
- **Email Sending** → Automated delivery
- **Stripe Integration** → Payment processing and customer data

## Contributing

### Adding New Tests
1. Create new tester module in `modules/`
2. Follow the established pattern:
   - `__init__()` - Initialize connections
   - `test_*()` methods - Individual test functions  
   - Error handling with duration tracking
   - Standalone test function for direct execution

2. Update `interactive_tester.py` to include new menu option

### Test Guidelines
- Always include timing information (`duration`)
- Provide detailed error messages for troubleshooting
- Return structured data for programmatic access
- Include success/failure status in all responses

---

**🎯 Ready to test your lead flow?** Run `python interactive_tester.py` and verify every component is working perfectly!