#!/usr/bin/env python3
"""
Database Testing Module
Tests database connectivity and data retrieval functionality
"""
import sys
import os
import time
import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List


class DatabaseTester:
    """Database testing functionality"""
    
    def __init__(self):
        """Initialize database connection"""
        self.logger = self._setup_logging()
        
        # Database path - use the unified leads.db
        self.db_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))),
            'dashboard', 'database', 'leads.db'
        )
        
        # Check if database exists
        if os.path.exists(self.db_path):
            self.db_initialized = True
            self.logger.info(f"Database found at: {self.db_path}")
        else:
            self.db_initialized = False
            self.logger.error(f"Database not found at: {self.db_path}")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for database tester"""
        logger = logging.getLogger('database_tester')
        if not logger.handlers:
            logger.setLevel(logging.INFO)
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def get_latest_lead(self) -> Dict[str, Any]:
        """
        Get the latest lead from the database with all its contents
        
        Returns:
            Dict with success status, lead data, and timing info
        """
        start_time = time.time()
        
        try:
            if not self.db_initialized:
                return {
                    'success': False,
                    'error': f'Database not found at: {self.db_path}',
                    'duration': time.time() - start_time
                }
            
            self.logger.info("🔍 Fetching latest lead from database...")
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                # Get the latest lead with all details
                query = """
                SELECT 
                    l.*,
                    u.first_name as assigned_first_name,
                    u.last_name as assigned_last_name,
                    u.email as assigned_email
                FROM leads l
                LEFT JOIN users u ON l.assigned_to = u.id
                ORDER BY l.created_time DESC, l.id DESC
                LIMIT 1
                """
                
                cursor = conn.execute(query)
                lead_row = cursor.fetchone()
                
                if not lead_row:
                    return {
                        'success': False,
                        'error': 'No leads found in database',
                        'duration': time.time() - start_time
                    }
                
                # Convert row to dictionary
                lead_data = dict(lead_row)
                
                # Get interaction history for this lead
                interaction_history = self._get_lead_interactions(conn, lead_data['id'])
                lead_data['interactions'] = interaction_history
                
                # Get additional metadata
                lead_data['total_interactions'] = len(interaction_history)
                lead_data['last_interaction'] = interaction_history[0]['interaction_date'] if interaction_history else None
                
                # Format assigned user info
                if lead_data['assigned_to']:
                    lead_data['assigned_user_name'] = f"{lead_data['assigned_first_name']} {lead_data['assigned_last_name']}"
                else:
                    lead_data['assigned_user_name'] = "Unassigned"
                
                self.logger.info(f"✅ Retrieved lead: {lead_data['full_name']} (ID: {lead_data['id']})")
                
                return {
                    'success': True,
                    'data': lead_data,
                    'duration': time.time() - start_time,
                    'message': f"Successfully retrieved latest lead: {lead_data['full_name']}"
                }
                
        except Exception as e:
            self.logger.error(f"Error fetching latest lead: {e}")
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def _get_lead_interactions(self, conn: sqlite3.Connection, lead_id: int) -> List[Dict[str, Any]]:
        """
        Get all interactions for a specific lead
        
        Args:
            conn: Database connection
            lead_id: Lead ID
            
        Returns:
            List of interaction dictionaries
        """
        try:
            query = """
            SELECT 
                id,
                interaction_type,
                user_email,
                subject,
                message,
                outcome,
                interaction_date,
                response_received,
                follow_up_required,
                follow_up_date
            FROM lead_interactions 
            WHERE lead_id = ?
            ORDER BY interaction_date DESC
            """
            
            cursor = conn.execute(query, (lead_id,))
            interactions = []
            
            for row in cursor.fetchall():
                interaction = dict(row)
                interactions.append(interaction)
            
            return interactions
            
        except Exception as e:
            self.logger.warning(f"Error fetching interactions for lead {lead_id}: {e}")
            return []
    
    def test_database_connection(self) -> Dict[str, Any]:
        """
        Test database connection and get basic stats
        
        Returns:
            Dict with connection test results and database statistics
        """
        start_time = time.time()
        
        try:
            if not self.db_initialized:
                return {
                    'success': False,
                    'error': f'Database not found at: {self.db_path}',
                    'duration': time.time() - start_time
                }
            
            with sqlite3.connect(self.db_path) as conn:
                # Get database statistics
                stats = {}
                
                # Count leads
                cursor = conn.execute("SELECT COUNT(*) FROM leads")
                stats['total_leads'] = cursor.fetchone()[0]
                
                # Count users
                cursor = conn.execute("SELECT COUNT(*) FROM users")
                stats['total_users'] = cursor.fetchone()[0]
                
                # Count interactions
                cursor = conn.execute("SELECT COUNT(*) FROM lead_interactions")
                stats['total_interactions'] = cursor.fetchone()[0]
                
                # Get leads by status
                cursor = conn.execute("""
                    SELECT status, COUNT(*) as count 
                    FROM leads 
                    GROUP BY status 
                    ORDER BY count DESC
                """)
                stats['leads_by_status'] = dict(cursor.fetchall())
                
                # Get recent activity
                cursor = conn.execute("""
                    SELECT COUNT(*) 
                    FROM leads 
                    WHERE created_time >= datetime('now', '-24 hours')
                """)
                stats['leads_last_24h'] = cursor.fetchone()[0]
                
                # Get database file size
                db_size = os.path.getsize(self.db_path)
                stats['db_size_mb'] = round(db_size / (1024 * 1024), 2)
                
                return {
                    'success': True,
                    'data': stats,
                    'duration': time.time() - start_time,
                    'message': 'Database connection successful'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def get_leads_summary(self, limit: int = 10) -> Dict[str, Any]:
        """
        Get summary of recent leads
        
        Args:
            limit: Number of recent leads to retrieve
            
        Returns:
            Dict with leads summary information
        """
        start_time = time.time()
        
        try:
            if not self.db_initialized:
                return {
                    'success': False,
                    'error': f'Database not found at: {self.db_path}',
                    'duration': time.time() - start_time
                }
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                # Get recent leads with assigned user info
                query = """
                SELECT 
                    l.id,
                    l.full_name,
                    l.email,
                    l.status,
                    l.priority,
                    l.lead_source,
                    l.created_time,
                    u.first_name as assigned_first_name,
                    u.last_name as assigned_last_name
                FROM leads l
                LEFT JOIN users u ON l.assigned_to = u.id
                ORDER BY l.created_time DESC
                LIMIT ?
                """
                
                cursor = conn.execute(query, (limit,))
                leads_summary = []
                
                for row in cursor.fetchall():
                    lead = dict(row)
                    if lead['assigned_first_name']:
                        lead['assigned_name'] = f"{lead['assigned_first_name']} {lead['assigned_last_name']}"
                    else:
                        lead['assigned_name'] = "Unassigned"
                    
                    leads_summary.append(lead)
                
                return {
                    'success': True,
                    'data': {
                        'leads': leads_summary,
                        'total_leads': len(leads_summary)
                    },
                    'duration': time.time() - start_time,
                    'message': f"Retrieved {len(leads_summary)} recent leads"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def get_lead_by_id(self, lead_id: int) -> Dict[str, Any]:
        """
        Get a specific lead by ID with all details
        
        Args:
            lead_id: The ID of the lead to retrieve
            
        Returns:
            Dict with lead data
        """
        start_time = time.time()
        
        try:
            if not self.db_initialized:
                return {
                    'success': False,
                    'error': f'Database not found at: {self.db_path}',
                    'duration': time.time() - start_time
                }
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                # Get lead with assigned user info
                query = """
                SELECT 
                    l.*,
                    u.first_name as assigned_first_name,
                    u.last_name as assigned_last_name,
                    u.email as assigned_email
                FROM leads l
                LEFT JOIN users u ON l.assigned_to = u.id
                WHERE l.id = ?
                """
                
                cursor = conn.execute(query, (lead_id,))
                lead_row = cursor.fetchone()
                
                if not lead_row:
                    return {
                        'success': False,
                        'error': f'Lead with ID {lead_id} not found',
                        'duration': time.time() - start_time
                    }
                
                # Convert row to dictionary
                lead_data = dict(lead_row)
                
                # Get interaction history
                interaction_history = self._get_lead_interactions(conn, lead_data['id'])
                lead_data['interactions'] = interaction_history
                lead_data['total_interactions'] = len(interaction_history)
                
                # Format assigned user info
                if lead_data['assigned_to']:
                    lead_data['assigned_user_name'] = f"{lead_data['assigned_first_name']} {lead_data['assigned_last_name']}"
                else:
                    lead_data['assigned_user_name'] = "Unassigned"
                
                return {
                    'success': True,
                    'data': lead_data,
                    'duration': time.time() - start_time,
                    'message': f"Successfully retrieved lead: {lead_data['full_name']}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def get_database_schema(self) -> Dict[str, Any]:
        """
        Get database schema information
        
        Returns:
            Dict with database schema information
        """
        start_time = time.time()
        
        try:
            if not self.db_initialized:
                return {
                    'success': False,
                    'error': f'Database not found at: {self.db_path}',
                    'duration': time.time() - start_time
                }
            
            with sqlite3.connect(self.db_path) as conn:
                # Get all tables
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                schema_info = {}
                
                for table_name in tables:
                    # Get table info
                    cursor = conn.execute(f"PRAGMA table_info({table_name})")
                    columns = []
                    
                    for row in cursor.fetchall():
                        columns.append({
                            'name': row[1],
                            'type': row[2],
                            'not_null': bool(row[3]),
                            'default_value': row[4],
                            'primary_key': bool(row[5])
                        })
                    
                    # Get row count
                    cursor = conn.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = cursor.fetchone()[0]
                    
                    schema_info[table_name] = {
                        'columns': columns,
                        'row_count': row_count
                    }
                
                return {
                    'success': True,
                    'data': {
                        'tables': tables,
                        'schema': schema_info
                    },
                    'duration': time.time() - start_time,
                    'message': f"Retrieved schema for {len(tables)} tables"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }


# Test function for standalone usage
def test_database_integration():
    """Test function for standalone usage"""
    print("🧪 Testing Database Integration...")
    
    tester = DatabaseTester()
    
    # Test database connection
    print("\n1. Testing Database Connection...")
    connection_result = tester.test_database_connection()
    if connection_result['success']:
        print("✅ Database Connection: SUCCESS")
        stats = connection_result['data']
        print(f"   Total Leads: {stats['total_leads']}")
        print(f"   Total Users: {stats['total_users']}")
        print(f"   Total Interactions: {stats['total_interactions']}")
        print(f"   Database Size: {stats['db_size_mb']} MB")
    else:
        print(f"❌ Database Connection: FAILED - {connection_result['error']}")
    
    # Test getting latest lead
    print("\n2. Testing Latest Lead Retrieval...")
    lead_result = tester.get_latest_lead()
    if lead_result['success']:
        print("✅ Lead Retrieval: SUCCESS")
        lead = lead_result['data']
        print(f"   Name: {lead['full_name']}")
        print(f"   Email: {lead['email']}")
        print(f"   Status: {lead['status']}")
        print(f"   Assigned to: {lead['assigned_user_name']}")
        print(f"   Interactions: {lead['total_interactions']}")
    else:
        print(f"❌ Lead Retrieval: FAILED - {lead_result['error']}")
    
    # Test leads summary
    print("\n3. Testing Leads Summary...")
    summary_result = tester.get_leads_summary(5)
    if summary_result['success']:
        print("✅ Leads Summary: SUCCESS")
        print(f"   Retrieved: {summary_result['data']['total_leads']} leads")
    else:
        print(f"❌ Leads Summary: FAILED - {summary_result['error']}")


if __name__ == "__main__":
    test_database_integration()