#!/usr/bin/env python3
"""
Facebook Lead Testing Module
Tests Facebook lead pulling functionality
"""
import sys
import os
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv

# Add Facebook scripts directory to path
facebook_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'facebook')
sys.path.append(facebook_dir)

try:
    from facebook_business.api import FacebookAdsApi
    from facebook_business.adobjects.adaccount import AdAccount
    from facebook_business.adobjects.campaign import Campaign
    from facebook_business.adobjects.ad import Ad
    from fb_accounts_config import get_active_accounts, get_field_value, get_full_name
    from lead_assignment_logic import get_lead_assignment
except ImportError as e:
    print(f"⚠️ Facebook Business SDK not available: {e}")
    print("   Install with: pip install facebook-business")


class FacebookTester:
    """Facebook lead testing functionality"""
    
    def __init__(self):
        """Initialize Facebook API connection"""
        self.logger = self._setup_logging()
        
        # Load environment variables
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'config', '.env')
        load_dotenv(config_path)
        
        # Facebook API credentials
        self.access_token = os.getenv("FB_EXTENDED_TOKEN")
        self.app_secret = os.getenv("FB_APP_SECRET")
        self.app_id = os.getenv("FB_APP_ID")
        
        # Check if credentials are available
        if not all([self.access_token, self.app_secret, self.app_id]):
            self.logger.warning("Facebook credentials not fully configured")
            self.api_initialized = False
        else:
            try:
                # Initialize Facebook API
                FacebookAdsApi.init(self.app_id, self.app_secret, self.access_token)
                self.api_initialized = True
                self.logger.info("Facebook API initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize Facebook API: {e}")
                self.api_initialized = False
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for Facebook tester"""
        logger = logging.getLogger('facebook_tester')
        if not logger.handlers:
            logger.setLevel(logging.INFO)
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def pull_latest_lead(self) -> Dict[str, Any]:
        """
        Pull the latest lead from Facebook
        
        Returns:
            Dict with success status, data, and timing info
        """
        start_time = time.time()
        
        try:
            if not self.api_initialized:
                return {
                    'success': False,
                    'error': 'Facebook API not initialized. Check credentials.',
                    'duration': time.time() - start_time
                }
            
            self.logger.info("🔍 Fetching latest Facebook lead...")
            
            # Get active accounts
            active_accounts = get_active_accounts()
            
            if not active_accounts:
                return {
                    'success': False,
                    'error': 'No active Facebook ad accounts configured',
                    'duration': time.time() - start_time
                }
            
            latest_lead = None
            latest_timestamp = None
            
            # Check each active account for the most recent lead
            for account_name, account_info in active_accounts.items():
                self.logger.info(f"Checking account: {account_name}")
                
                try:
                    account_id = f"act_{account_info['id']}"
                    ad_account = AdAccount(account_id)
                    
                    # Get recent campaigns (last 7 days)
                    campaigns = ad_account.get_campaigns(
                        fields=['name', 'status', 'objective'],
                        params={
                            'effective_status': ['ACTIVE'],
                            'time_range': {
                                'since': (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d'),
                                'until': datetime.now().strftime('%Y-%m-%d')
                            }
                        }
                    )
                    
                    for campaign in campaigns:
                        if campaign.get('objective') != 'LEAD_GENERATION':
                            continue
                        
                        # Get ads for this campaign
                        ads = campaign.get_ads(
                            fields=['name', 'status'],
                            params={'effective_status': ['ACTIVE']}
                        )
                        
                        for ad in ads:
                            # Get leads for this ad (just the most recent)
                            leads = ad.get_leads(
                                fields=['id', 'created_time', 'field_data', 'ad_id', 'ad_name', 'campaign_name', 'form_name'],
                                params={
                                    'limit': 5,  # Just get a few recent leads
                                    'filtering': [{'field': 'time_created', 'operator': 'GREATER_THAN', 'value': int((datetime.now() - timedelta(hours=24)).timestamp())}]
                                }
                            )
                            
                            for lead in leads:
                                lead_timestamp = datetime.strptime(lead['created_time'], '%Y-%m-%dT%H:%M:%S%z')
                                
                                if latest_timestamp is None or lead_timestamp > latest_timestamp:
                                    latest_timestamp = lead_timestamp
                                    latest_lead = self._format_lead_data(lead, account_name)
                    
                except Exception as e:
                    self.logger.warning(f"Error checking account {account_name}: {e}")
                    continue
            
            if latest_lead:
                self.logger.info(f"✅ Found latest lead: {latest_lead['full_name']}")
                return {
                    'success': True,
                    'data': latest_lead,
                    'duration': time.time() - start_time,
                    'timestamp': latest_timestamp.isoformat(),
                    'message': f"Successfully retrieved latest lead from {latest_lead.get('account_name', 'Facebook')}"
                }
            else:
                return {
                    'success': False,
                    'error': 'No leads found in the last 24 hours',
                    'duration': time.time() - start_time
                }
                
        except Exception as e:
            self.logger.error(f"Error pulling Facebook lead: {e}")
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def _format_lead_data(self, lead_data: Dict, account_name: str) -> Dict[str, Any]:
        """
        Format lead data into standardized structure
        
        Args:
            lead_data: Raw lead data from Facebook API
            account_name: Name of the ad account
            
        Returns:
            Formatted lead dictionary
        """
        try:
            field_data = lead_data.get('field_data', [])
            
            # Extract standard fields
            full_name = get_full_name(field_data)
            email = get_field_value(field_data, 'email')
            phone = get_field_value(field_data, 'phone')
            city = get_field_value(field_data, 'city')
            
            # Extract AI-specific fields
            ai_experience = get_field_value(field_data, 'ai_experience')
            expectations = get_field_value(field_data, 'expectations')
            python_experience = get_field_value(field_data, 'python_experience')
            
            # Determine lead assignment
            assignment_info = get_lead_assignment(full_name, email, city)
            
            formatted_lead = {
                'id': lead_data.get('id'),
                'lead_id': lead_data.get('id'),  # Facebook lead ID
                'full_name': full_name,
                'email': email,
                'phone': phone,
                'city': city,
                'ai_experience': ai_experience,
                'expectations': expectations,
                'python_experience': python_experience,
                'lead_source': 'Facebook',
                'lead_source_detail': f"Facebook - {account_name}",
                'campaign_name': lead_data.get('campaign_name', 'Unknown'),
                'ad_name': lead_data.get('ad_name', 'Unknown'),
                'form_name': lead_data.get('form_name', 'Unknown'),
                'account_name': account_name,
                'priority': assignment_info.get('priority', 'Medium'),
                'assigned_to': assignment_info.get('assigned_user_id'),
                'workshop_type': assignment_info.get('workshop_type', 'General'),
                'status': 'New',
                'created_time': lead_data.get('created_time'),
                'updated_at': datetime.now().isoformat(),
                'notes': f"Raw fields: {field_data}"
            }
            
            return formatted_lead
            
        except Exception as e:
            self.logger.error(f"Error formatting lead data: {e}")
            return {
                'id': lead_data.get('id', 'unknown'),
                'full_name': 'Unknown',
                'email': '<EMAIL>',
                'error': f"Formatting error: {e}"
            }
    
    def test_api_connection(self) -> Dict[str, Any]:
        """
        Test Facebook API connection
        
        Returns:
            Dict with connection test results
        """
        start_time = time.time()
        
        try:
            if not self.api_initialized:
                return {
                    'success': False,
                    'error': 'Facebook API not initialized',
                    'duration': time.time() - start_time
                }
            
            # Try to get account info
            active_accounts = get_active_accounts()
            
            if not active_accounts:
                return {
                    'success': False,
                    'error': 'No active accounts configured',
                    'duration': time.time() - start_time
                }
            
            # Test connection by getting basic account info
            account_name = list(active_accounts.keys())[0]
            account_info = active_accounts[account_name]
            account_id = f"act_{account_info['id']}"
            
            ad_account = AdAccount(account_id)
            account_details = ad_account.api_get(fields=['name', 'account_status', 'currency'])
            
            return {
                'success': True,
                'data': {
                    'account_name': account_details.get('name', account_name),
                    'account_status': account_details.get('account_status'),
                    'currency': account_details.get('currency'),
                    'total_accounts': len(active_accounts)
                },
                'duration': time.time() - start_time,
                'message': 'Facebook API connection successful'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def get_account_summary(self) -> Dict[str, Any]:
        """
        Get summary of all configured Facebook accounts
        
        Returns:
            Dict with account summary information
        """
        try:
            active_accounts = get_active_accounts()
            
            summary = {
                'total_accounts': len(active_accounts),
                'accounts': []
            }
            
            for name, info in active_accounts.items():
                summary['accounts'].append({
                    'name': name,
                    'id': info['id'],
                    'description': info.get('description', 'No description'),
                    'expected_forms': info.get('expected_forms', [])
                })
            
            return {
                'success': True,
                'data': summary,
                'message': f"Retrieved summary for {len(active_accounts)} active accounts"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


# Test function for standalone usage
def test_facebook_integration():
    """Test function for standalone usage"""
    print("🧪 Testing Facebook Integration...")
    
    tester = FacebookTester()
    
    # Test API connection
    print("\n1. Testing API Connection...")
    connection_result = tester.test_api_connection()
    if connection_result['success']:
        print("✅ API Connection: SUCCESS")
        print(f"   Account: {connection_result['data']['account_name']}")
        print(f"   Status: {connection_result['data']['account_status']}")
    else:
        print(f"❌ API Connection: FAILED - {connection_result['error']}")
    
    # Test pulling latest lead
    print("\n2. Testing Lead Pull...")
    lead_result = tester.pull_latest_lead()
    if lead_result['success']:
        print("✅ Lead Pull: SUCCESS")
        lead = lead_result['data']
        print(f"   Name: {lead['full_name']}")
        print(f"   Email: {lead['email']}")
        print(f"   Source: {lead['lead_source_detail']}")
    else:
        print(f"❌ Lead Pull: FAILED - {lead_result['error']}")


if __name__ == "__main__":
    test_facebook_integration()