#!/usr/bin/env python3
"""
Interactive Lead Flow Testing Suite
Comprehensive testing tool for critical lead flow functionalities
"""
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'lead_flow'))

from new.scripts.tests.facebook_tester import FacebookTester
from new.scripts.tests.stripe_tester import StripeTester  
from new.scripts.tests.database_tester import DatabaseTester
from modules.email_tester import EmailTester
from modules.gpt5_tester import GPT5Tester


class InteractiveLeadFlowTester:
    """Main interactive testing interface"""
    
    def __init__(self):
        """Initialize all test modules"""
        print("🔧 Initializing Lead Flow Testing Suite...")
        
        try:
            self.facebook_tester = FacebookTester()
            self.stripe_tester = StripeTester()
            self.database_tester = DatabaseTester()
            self.email_tester = EmailTester()
            self.gpt5_tester = GPT5Tester()
            print("✅ All test modules loaded successfully")
        except Exception as e:
            print(f"❌ Error initializing test modules: {e}")
            sys.exit(1)
    
    def show_main_menu(self):
        """Display main menu options"""
        print("\n" + "="*60)
        print("🚀 MODERN AI PRO - LEAD FLOW TESTING SUITE")
        print("="*60)
        print("1. 📱 Pull Latest Facebook Lead")
        print("2. 💳 Get Latest Stripe Data")
        print("3. 🗄️  Get Latest Lead from Database")
        print("4. ✉️  Send Test Email (to <EMAIL>)")
        print("5. 🤖 Compose Email with GPT-5")
        print("6. 🔄 Run All Tests (Full Integration)")
        print("7. 📊 View Test Reports")
        print("0. ❌ Exit")
        print("="*60)
    
    def handle_facebook_test(self):
        """Handle Facebook lead testing"""
        print("\n🔍 FACEBOOK LEAD TESTING")
        print("-" * 40)
        
        try:
            result = self.facebook_tester.pull_latest_lead()
            self._display_result("Facebook Lead Pull", result)
            
            if result['success'] and result.get('data'):
                print("\n📋 Latest Lead Details:")
                lead = result['data']
                print(f"   Name: {lead.get('full_name', 'N/A')}")
                print(f"   Email: {lead.get('email', 'N/A')}")
                print(f"   Source: {lead.get('lead_source', 'N/A')}")
                print(f"   Created: {lead.get('created_time', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Facebook test failed: {e}")
    
    def handle_stripe_test(self):
        """Handle Stripe data testing"""
        print("\n🔍 STRIPE DATA TESTING")
        print("-" * 40)
        
        try:
            result = self.stripe_tester.get_latest_data()
            self._display_result("Stripe Data Pull", result)
            
            if result['success'] and result.get('data'):
                print("\n💳 Latest Payment Data:")
                payment = result['data']
                print(f"   Customer: {payment.get('customer_email', 'N/A')}")
                print(f"   Amount: ${payment.get('amount', 0)/100:.2f}")
                print(f"   Status: {payment.get('status', 'N/A')}")
                print(f"   Date: {payment.get('created', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Stripe test failed: {e}")
    
    def handle_database_test(self):
        """Handle database testing"""
        print("\n🔍 DATABASE TESTING")
        print("-" * 40)
        
        try:
            result = self.database_tester.get_latest_lead()
            self._display_result("Database Lead Query", result)
            
            if result['success'] and result.get('data'):
                print("\n🗄️ Latest Lead from Database:")
                lead = result['data']
                print(f"   ID: {lead.get('id', 'N/A')}")
                print(f"   Name: {lead.get('full_name', 'N/A')}")
                print(f"   Email: {lead.get('email', 'N/A')}")
                print(f"   Status: {lead.get('status', 'N/A')}")
                print(f"   Assigned To: {lead.get('assigned_to', 'Unassigned')}")
                print(f"   Created: {lead.get('created_time', 'N/A')}")
                print(f"   Notes: {lead.get('notes', 'N/A')[:100]}...")
                
        except Exception as e:
            print(f"❌ Database test failed: {e}")
    
    def handle_email_test(self):
        """Handle email testing"""
        print("\n🔍 EMAIL TESTING")
        print("-" * 40)
        
        try:
            test_email = "<EMAIL>"
            print(f"📧 Sending test email to: {test_email}")
            
            result = self.email_tester.send_test_email(test_email)
            self._display_result("Test Email Send", result)
            
            if result['success']:
                print(f"✅ Test email sent successfully!")
                print(f"   Subject: {result.get('subject', 'N/A')}")
                print(f"   Message ID: {result.get('message_id', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Email test failed: {e}")
    
    def handle_gpt5_test(self):
        """Handle GPT-5 email composition testing"""
        print("\n🔍 GPT-5 EMAIL COMPOSITION TESTING")
        print("-" * 40)
        
        try:
            # First get a lead to compose email for
            lead_result = self.database_tester.get_latest_lead()
            
            if not lead_result['success'] or not lead_result.get('data'):
                print("❌ No lead found to compose email for")
                return
            
            lead = lead_result['data']
            print(f"🎯 Composing email for: {lead.get('full_name', 'Unknown')}")
            
            result = self.gpt5_tester.compose_email_for_lead(lead)
            self._display_result("GPT-5 Email Composition", result)
            
            if result['success'] and result.get('data'):
                email_content = result['data']
                print("\n🤖 Generated Email Content:")
                print(f"   Subject: {email_content.get('subject', 'N/A')}")
                print(f"   Body Preview: {email_content.get('body', '')[:200]}...")
                print(f"   Generation Method: {email_content.get('generated_by', 'N/A')}")
                
        except Exception as e:
            print(f"❌ GPT-5 test failed: {e}")
    
    def handle_full_integration_test(self):
        """Run all tests in sequence"""
        print("\n🔄 RUNNING FULL INTEGRATION TEST")
        print("=" * 50)
        
        tests = [
            ("Facebook Lead Pull", self.handle_facebook_test),
            ("Stripe Data Pull", self.handle_stripe_test),
            ("Database Query", self.handle_database_test),
            ("Email Send", self.handle_email_test),
            ("GPT-5 Composition", self.handle_gpt5_test)
        ]
        
        results = {}
        start_time = datetime.now()
        
        for test_name, test_func in tests:
            print(f"\n▶️ Running {test_name}...")
            try:
                test_func()
                results[test_name] = "✅ PASSED"
            except Exception as e:
                results[test_name] = f"❌ FAILED: {e}"
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n📊 INTEGRATION TEST SUMMARY")
        print("=" * 50)
        for test_name, result in results.items():
            print(f"{test_name:.<30} {result}")
        print(f"Total Duration: {duration.total_seconds():.2f} seconds")
    
    def handle_view_reports(self):
        """Show test reports and logs"""
        print("\n📊 TEST REPORTS")
        print("-" * 40)
        
        # Show recent test logs
        print("🔍 Recent Test Activity:")
        # TODO: Implement log viewing functionality
        print("   (Log viewing feature coming soon)")
    
    def _display_result(self, test_name: str, result: Dict[str, Any]):
        """Display formatted test result"""
        status = "✅ SUCCESS" if result.get('success') else "❌ FAILED"
        print(f"\n{test_name}: {status}")
        
        if not result.get('success'):
            error_msg = result.get('error', 'Unknown error')
            print(f"   Error: {error_msg}")
        
        if result.get('duration'):
            print(f"   Duration: {result['duration']:.2f} seconds")
    
    def run(self):
        """Main interactive loop"""
        print("🎯 Welcome to Modern AI Pro Lead Flow Testing Suite!")
        print(f"Session started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        while True:
            try:
                self.show_main_menu()
                choice = input("\nSelect an option (0-7): ").strip()
                
                if choice == '0':
                    print("\n👋 Thank you for using Lead Flow Testing Suite!")
                    break
                elif choice == '1':
                    self.handle_facebook_test()
                elif choice == '2':
                    self.handle_stripe_test()
                elif choice == '3':
                    self.handle_database_test()
                elif choice == '4':
                    self.handle_email_test()
                elif choice == '5':
                    self.handle_gpt5_test()
                elif choice == '6':
                    self.handle_full_integration_test()
                elif choice == '7':
                    self.handle_view_reports()
                else:
                    print("❌ Invalid option. Please try again.")
                
                input("\nPress Enter to continue...")
                
            except KeyboardInterrupt:
                print("\n\n👋 Testing session interrupted. Goodbye!")
                break
            except Exception as e:
                print(f"\n💥 Unexpected error: {e}")
                input("Press Enter to continue...")


def main():
    """Entry point"""
    try:
        tester = InteractiveLeadFlowTester()
        tester.run()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()