#!/usr/bin/env python3
"""
Stripe Data Testing Module
Tests Stripe API connectivity and data retrieval functionality
"""
import sys
import os
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv

try:
    import stripe
    STRIPE_AVAILABLE = True
except ImportError:
    STRIPE_AVAILABLE = False
    print("⚠️ Stripe library not available. Install with: pip install stripe")


class StripeTester:
    """Stripe data testing functionality"""
    
    def __init__(self):
        """Initialize Stripe API connection"""
        self.logger = self._setup_logging()
        
        # Load environment variables
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'config', '.env')
        load_dotenv(config_path)
        
        # Stripe configuration
        self.stripe_secret_key = os.getenv("STRIPE_SECRET_KEY")
        
        # Check if Stripe is available and configured
        if not STRIPE_AVAILABLE:
            self.api_initialized = False
            self.logger.warning("Stripe library not available")
        elif not self.stripe_secret_key:
            self.api_initialized = False
            self.logger.warning("Stripe secret key not configured")
        else:
            try:
                stripe.api_key = self.stripe_secret_key
                # Test API connection with a simple call
                stripe.Account.retrieve()
                self.api_initialized = True
                self.logger.info("Stripe API initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize Stripe API: {e}")
                self.api_initialized = False
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for Stripe tester"""
        logger = logging.getLogger('stripe_tester')
        if not logger.handlers:
            logger.setLevel(logging.INFO)
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def get_latest_data(self) -> Dict[str, Any]:
        """
        Get the latest payment data from Stripe
        
        Returns:
            Dict with success status, payment data, and timing info
        """
        start_time = time.time()
        
        try:
            if not self.api_initialized:
                return {
                    'success': False,
                    'error': 'Stripe API not initialized. Check credentials.',
                    'duration': time.time() - start_time
                }
            
            self.logger.info("🔍 Fetching latest Stripe payment data...")
            
            # Get the most recent successful payment
            payment_intents = stripe.PaymentIntent.list(
                limit=10,  # Get recent payments
                status='succeeded'
            )
            
            if not payment_intents.data:
                return {
                    'success': False,
                    'error': 'No successful payments found',
                    'duration': time.time() - start_time
                }
            
            # Get the latest payment
            latest_payment = payment_intents.data[0]
            
            # Get customer details if available
            customer_info = None
            if latest_payment.customer:
                try:
                    customer = stripe.Customer.retrieve(latest_payment.customer)
                    customer_info = {
                        'id': customer.id,
                        'email': customer.email,
                        'name': customer.name,
                        'created': datetime.fromtimestamp(customer.created).isoformat()
                    }
                except Exception as e:
                    self.logger.warning(f"Could not retrieve customer info: {e}")
            
            # Format payment data
            formatted_payment = self._format_payment_data(latest_payment, customer_info)
            
            self.logger.info(f"✅ Retrieved latest payment: {formatted_payment.get('amount_display', 'Unknown amount')}")
            
            return {
                'success': True,
                'data': formatted_payment,
                'duration': time.time() - start_time,
                'message': f"Successfully retrieved latest payment from Stripe"
            }
            
        except Exception as e:
            self.logger.error(f"Error fetching Stripe data: {e}")
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def _format_payment_data(self, payment_intent: Any, customer_info: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Format payment data into standardized structure
        
        Args:
            payment_intent: Stripe PaymentIntent object
            customer_info: Optional customer information
            
        Returns:
            Formatted payment dictionary
        """
        try:
            # Extract basic payment info
            amount = payment_intent.amount
            currency = payment_intent.currency.upper()
            amount_display = f"{currency} {amount/100:.2f}"
            
            # Extract metadata for workshop type identification
            metadata = payment_intent.metadata or {}
            description = payment_intent.description or ""
            
            # Determine workshop type from description or metadata
            workshop_type = self._identify_workshop_type(description, metadata)
            
            formatted_payment = {
                'id': payment_intent.id,
                'amount': amount,
                'currency': currency,
                'amount_display': amount_display,
                'status': payment_intent.status,
                'description': description,
                'workshop_type': workshop_type,
                'created': datetime.fromtimestamp(payment_intent.created).isoformat(),
                'metadata': dict(metadata),
                'payment_method_types': payment_intent.payment_method_types,
                'receipt_email': payment_intent.receipt_email,
                'customer_info': customer_info,
                'customer_email': customer_info['email'] if customer_info else payment_intent.receipt_email,
                'customer_name': customer_info['name'] if customer_info else 'Unknown'
            }
            
            return formatted_payment
            
        except Exception as e:
            self.logger.error(f"Error formatting payment data: {e}")
            return {
                'id': payment_intent.id if hasattr(payment_intent, 'id') else 'unknown',
                'amount': 0,
                'currency': 'USD',
                'status': 'unknown',
                'error': f"Formatting error: {e}"
            }
    
    def _identify_workshop_type(self, description: str, metadata: Dict) -> str:
        """
        Identify workshop type from payment description or metadata
        
        Args:
            description: Payment description
            metadata: Payment metadata
            
        Returns:
            Workshop type string
        """
        workshop_keywords = {
            'AI Essentials': ['essentials', 'basic', 'intro'],
            'AI Practitioner': ['practitioner', 'advanced', 'pro'],
            'Agentic AI': ['agentic', 'agent', 'autonomous'],
            'Vibe Coding': ['vibe', 'coding', 'development'],
            'AI for PMs': ['pm', 'product manager', 'management'],
            'AI for UX': ['ux', 'design', 'user experience']
        }
        
        # Check description and metadata for keywords
        search_text = f"{description} {' '.join(metadata.values())}".lower()
        
        for workshop_type, keywords in workshop_keywords.items():
            if any(keyword in search_text for keyword in keywords):
                return workshop_type
        
        return "General Workshop"
    
    def test_api_connection(self) -> Dict[str, Any]:
        """
        Test Stripe API connection
        
        Returns:
            Dict with connection test results
        """
        start_time = time.time()
        
        try:
            if not self.api_initialized:
                return {
                    'success': False,
                    'error': 'Stripe API not initialized',
                    'duration': time.time() - start_time
                }
            
            # Get account information
            account = stripe.Account.retrieve()
            
            return {
                'success': True,
                'data': {
                    'account_id': account.id,
                    'business_profile': account.business_profile.name if account.business_profile else 'N/A',
                    'country': account.country,
                    'default_currency': account.default_currency,
                    'email': account.email,
                    'charges_enabled': account.charges_enabled,
                    'payouts_enabled': account.payouts_enabled
                },
                'duration': time.time() - start_time,
                'message': 'Stripe API connection successful'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def get_recent_payments_summary(self, limit: int = 5) -> Dict[str, Any]:
        """
        Get summary of recent payments
        
        Args:
            limit: Number of recent payments to retrieve
            
        Returns:
            Dict with payment summary information
        """
        start_time = time.time()
        
        try:
            if not self.api_initialized:
                return {
                    'success': False,
                    'error': 'Stripe API not initialized',
                    'duration': time.time() - start_time
                }
            
            # Get recent successful payments
            payment_intents = stripe.PaymentIntent.list(
                limit=limit,
                status='succeeded'
            )
            
            payments_summary = []
            total_amount = 0
            
            for payment in payment_intents.data:
                amount = payment.amount / 100  # Convert from cents
                total_amount += amount
                
                payments_summary.append({
                    'id': payment.id,
                    'amount': amount,
                    'currency': payment.currency.upper(),
                    'description': payment.description or 'No description',
                    'created': datetime.fromtimestamp(payment.created).strftime('%Y-%m-%d %H:%M:%S'),
                    'customer_email': payment.receipt_email or 'N/A'
                })
            
            return {
                'success': True,
                'data': {
                    'payments': payments_summary,
                    'total_payments': len(payments_summary),
                    'total_amount': total_amount,
                    'currency': payment_intents.data[0].currency.upper() if payment_intents.data else 'USD'
                },
                'duration': time.time() - start_time,
                'message': f"Retrieved {len(payments_summary)} recent payments"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def get_customers_summary(self, limit: int = 5) -> Dict[str, Any]:
        """
        Get summary of recent customers
        
        Args:
            limit: Number of recent customers to retrieve
            
        Returns:
            Dict with customer summary information
        """
        start_time = time.time()
        
        try:
            if not self.api_initialized:
                return {
                    'success': False,
                    'error': 'Stripe API not initialized',
                    'duration': time.time() - start_time
                }
            
            # Get recent customers
            customers = stripe.Customer.list(limit=limit)
            
            customers_summary = []
            
            for customer in customers.data:
                customers_summary.append({
                    'id': customer.id,
                    'email': customer.email or 'N/A',
                    'name': customer.name or 'N/A',
                    'created': datetime.fromtimestamp(customer.created).strftime('%Y-%m-%d %H:%M:%S'),
                    'description': customer.description or 'No description'
                })
            
            return {
                'success': True,
                'data': {
                    'customers': customers_summary,
                    'total_customers': len(customers_summary)
                },
                'duration': time.time() - start_time,
                'message': f"Retrieved {len(customers_summary)} recent customers"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }


# Test function for standalone usage
def test_stripe_integration():
    """Test function for standalone usage"""
    print("🧪 Testing Stripe Integration...")
    
    tester = StripeTester()
    
    # Test API connection
    print("\n1. Testing API Connection...")
    connection_result = tester.test_api_connection()
    if connection_result['success']:
        print("✅ API Connection: SUCCESS")
        print(f"   Account: {connection_result['data']['business_profile']}")
        print(f"   Country: {connection_result['data']['country']}")
        print(f"   Currency: {connection_result['data']['default_currency']}")
    else:
        print(f"❌ API Connection: FAILED - {connection_result['error']}")
    
    # Test getting latest payment
    print("\n2. Testing Latest Payment Retrieval...")
    payment_result = tester.get_latest_data()
    if payment_result['success']:
        print("✅ Payment Retrieval: SUCCESS")
        payment = payment_result['data']
        print(f"   Amount: {payment['amount_display']}")
        print(f"   Customer: {payment['customer_email']}")
        print(f"   Status: {payment['status']}")
    else:
        print(f"❌ Payment Retrieval: FAILED - {payment_result['error']}")
    
    # Test recent payments summary
    print("\n3. Testing Recent Payments Summary...")
    summary_result = tester.get_recent_payments_summary()
    if summary_result['success']:
        print("✅ Payment Summary: SUCCESS")
        print(f"   Total Payments: {summary_result['data']['total_payments']}")
        print(f"   Total Amount: {summary_result['data']['total_amount']} {summary_result['data']['currency']}")
    else:
        print(f"❌ Payment Summary: FAILED - {summary_result['error']}")


if __name__ == "__main__":
    test_stripe_integration()