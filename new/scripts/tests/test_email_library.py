#!/usr/bin/env python3
"""
Email Library Tests (pytest compatible)
Tests email functionality using the Modern AI Pro Email Library
Can run with external APIs using real test credentials
"""

import os
import sys
from pathlib import Path

# Try to import pytest, but don't require it for CLI usage
try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False
    # Create dummy pytest decorators for CLI usage
    class pytest:
        @staticmethod
        def fixture(func=None):
            return func if func else lambda f: f

# Add the core directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "core"))

# Import the email library
from email_library import (
    ModernAIEmailer, 
    EmailConfig, 
    EmailMessage, 
    EmailAttachment,
    EmailTemplates,
    create_quick_email
)

# Test configuration
TEST_EMAIL = "<EMAIL>"  # Safe test email
TEST_NAME = "Dr<PERSON> <PERSON><PERSON>"

class TestEmailLibrary:
    """Email library test suite (pytest compatible)"""
    
    @pytest.fixture
    def mahalakshmi_emailer(self):
        """Fixture for <PERSON><PERSON><PERSON><PERSON><PERSON>'s email configuration"""
        config = EmailConfig(sender_email='<EMAIL>')
        return ModernAIEmailer(config)
    
    @pytest.fixture
    def manish_emailer(self):
        """Fixture for Manish's email configuration"""
        config = EmailConfig(sender_email='<EMAIL>')
        return ModernAIEmailer(config)
    
    def test_simple_email(self, mahalakshmi_emailer):
        """Test sending a simple email using the library"""
        print("\n🧪 Testing Simple Email")
        
        # Create email using template
        body = EmailTemplates.test_email(TEST_NAME)
        
        email = EmailMessage(
            to=TEST_EMAIL,
            subject="🧪 Email System Test - Modern AI Pro Library",
            body=body,
            reply_to='<EMAIL>'
        )
        
        result = mahalakshmi_emailer.send_email(email, apply_rate_limit=False)
        assert result, "Simple email should send successfully"
    
    def test_email_with_attachment(self, mahalakshmi_emailer):
        """Test sending an email with attachment"""
        print("\n📎 Testing Email with Attachment")
        
        # Use the email_library.py file as test attachment
        script_dir = Path(__file__).parent.parent / "core"
        test_file = script_dir / "email_library.py"
        
        if not test_file.exists():
            pytest.skip("Test attachment file not found")
        
        body = f"""<html><body>
<p>Hi {TEST_NAME.split()[0]},</p>

<p>This is a test email with an attachment from the Modern AI Pro email library.</p>

<p><strong>Attachment Test:</strong></p>
<ul>
    <li>File: email_library.py</li>
    <li>Purpose: Testing attachment functionality</li>
    <li>Library: Modern AI Pro Email Library ✅</li>
</ul>

<p>If you received this email with the attachment, the library is working correctly!</p>

<p>Best regards,<br>
Modern AI Pro Email Library Test</p>
</body></html>"""
        
        email = EmailMessage(
            to=TEST_EMAIL,
            subject="📎 Attachment Test - Modern AI Pro Library",
            body=body,
            reply_to='<EMAIL>'
        )
        
        # Add attachment
        attachment = EmailAttachment(str(test_file), 'email_library_test.py', 'text/x-python')
        email.add_attachment(attachment)
        
        print(f"📎 Adding attachment: {attachment.filename} ({attachment.size_mb():.2f} MB)")
        
        result = mahalakshmi_emailer.send_email(email, apply_rate_limit=False)
        assert result, "Email with attachment should send successfully"
    
    def test_bulk_emails(self, mahalakshmi_emailer):
        """Test bulk email functionality"""
        print("\n📧 Testing Bulk Email")
        
        recipients = [
            (TEST_EMAIL, TEST_NAME),
            (TEST_EMAIL, f"{TEST_NAME} (Copy)")
        ]
        
        body_template = EmailTemplates.sales_email("{name}")
        
        results = mahalakshmi_emailer.send_bulk_emails(
            recipients=recipients,
            subject="🚀 Bulk Email Test - Modern AI Pro",
            body_template=body_template,
            rate_limit_seconds=1.0
        )
        
        print(f"📊 Bulk email results: {results}")
        assert results["success"] > 0, "At least one bulk email should succeed"
    
    def test_manish_simple_email(self, manish_emailer):
        """Test sending simple email from Manish"""
        print("\n👨‍💼 Testing Manish Email (Simple)")
        
        body = f"""<html><body>
<p>Hi {TEST_NAME.split()[0]},</p>

<p>This is a test email from <strong>Manish</strong> using the Modern AI Pro email library.</p>

<p><strong>Manish Test Details:</strong></p>
<ul>
    <li>Sender: <EMAIL> ✅</li>
    <li>Library: Modern AI Pro Email Library</li>
    <li>Test Type: Simple email (no attachment)</li>
    <li>Authentication: Service account delegation</li>
</ul>

<p>If you received this email, Manish's email configuration is working correctly!</p>

<p>Best regards,<br>
Manish<br>
Modern AI Pro Team</p>
</body></html>"""
        
        email = EmailMessage(
            to=TEST_EMAIL,
            subject="👨‍💼 Manish Test Email - Simple",
            body=body,
            reply_to='<EMAIL>'
        )
        
        result = manish_emailer.send_email(email, apply_rate_limit=False)
        assert result, "Manish simple email should send successfully"
    
    def test_manish_email_with_attachment(self, manish_emailer):
        """Test sending email from Manish with attachment"""
        print("\n👨‍💼 Testing Manish Email (With Attachment)")
        
        # Use the email_library.py file as test attachment
        script_dir = Path(__file__).parent.parent / "core"
        test_file = script_dir / "email_library.py"
        
        if not test_file.exists():
            pytest.skip("Test attachment file not found")
        
        body = f"""<html><body>
<p>Hi {TEST_NAME.split()[0]},</p>

<p>This is a test email with attachment from <strong>Manish</strong> using the Modern AI Pro email library.</p>

<p><strong>Manish Attachment Test:</strong></p>
<ul>
    <li>Sender: <EMAIL> ✅</li>
    <li>File: email_library.py</li>
    <li>Purpose: Testing Manish attachment functionality</li>
    <li>Authentication: Service account delegation</li>
</ul>

<p>If you received this email with the attachment, Manish's configuration is working correctly!</p>

<p>Best regards,<br>
Manish<br>
Modern AI Pro Team</p>
</body></html>"""
        
        email = EmailMessage(
            to=TEST_EMAIL,
            subject="👨‍💼 Manish Attachment Test",
            body=body,
            reply_to='<EMAIL>'
        )
        
        # Add attachment
        attachment = EmailAttachment(str(test_file), 'manish_test_attachment.py', 'text/x-python')
        email.add_attachment(attachment)
        
        print(f"📎 Adding attachment: {attachment.filename} ({attachment.size_mb():.2f} MB)")
        
        result = manish_emailer.send_email(email, apply_rate_limit=False)
        assert result, "Manish email with attachment should send successfully"
    
    def test_email_templates(self):
        """Test email templates functionality"""
        print("\n📝 Testing Email Templates")
        
        # Test all template methods
        test_template = EmailTemplates.test_email("Test User")
        assert "Test User" in test_template, "Test template should include user name"
        
        sales_template = EmailTemplates.sales_email("Sales User")
        assert "Sales User" in sales_template, "Sales template should include user name"
        
        followup_template = EmailTemplates.followup_with_gift("Followup User")
        assert "Followup User" in followup_template, "Followup template should include user name"
        
        print("✅ All templates working correctly")
    
    def test_email_validation(self, mahalakshmi_emailer):
        """Test email validation"""
        print("\n🔍 Testing Email Validation")
        
        # Test config validation
        result = mahalakshmi_emailer.validate_config()
        assert result, "Email configuration should be valid"
        
        print("✅ Email validation successful")

# CLI compatibility for direct testing with external APIs
def main():
    """CLI entry point for direct testing with external APIs"""
    print("🧪 Modern AI Pro Email Library Test Suite")
    print("=" * 50)
    print("⚠️  This will send REAL emails to the test address!")
    print(f"📧 Test Email: {TEST_EMAIL}")
    print(f"👤 Test Name: {TEST_NAME}")
    
    # Parse command line arguments for specific tests
    import sys
    
    test_simple_only = "--simple" in sys.argv
    test_attachment_only = "--attachment" in sys.argv
    test_bulk_only = "--bulk" in sys.argv
    test_manish_only = "--manish" in sys.argv
    test_manish_simple = "--manish-simple" in sys.argv
    test_manish_attach = "--manish-attach" in sys.argv
    
    if "--yes" not in sys.argv and "-y" not in sys.argv:
        any_specific_test = any([test_simple_only, test_attachment_only, test_bulk_only, 
                                test_manish_only, test_manish_simple, test_manish_attach])
        test_type = "specific test" if any_specific_test else "all tests"
        response = input(f"\n🤔 Run {test_type} with REAL external APIs? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Test cancelled.")
            return
    
    # Create test instances
    mahalakshmi_config = EmailConfig(sender_email='<EMAIL>')
    mahalakshmi_emailer = ModernAIEmailer(mahalakshmi_config)
    
    manish_config = EmailConfig(sender_email='<EMAIL>')
    manish_emailer = ModernAIEmailer(manish_config)
    
    # Create test class instance
    test_instance = TestEmailLibrary()
    
    # Run tests
    results = []
    
    try:
        if test_simple_only or not any([test_attachment_only, test_bulk_only, test_manish_only, test_manish_simple, test_manish_attach]):
            try:
                test_instance.test_simple_email(mahalakshmi_emailer)
                results.append(("Simple Email", True))
            except Exception as e:
                print(f"❌ Simple Email failed: {e}")
                results.append(("Simple Email", False))
        
        if test_attachment_only or not any([test_simple_only, test_bulk_only, test_manish_only, test_manish_simple, test_manish_attach]):
            try:
                test_instance.test_email_with_attachment(mahalakshmi_emailer)
                results.append(("Email with Attachment", True))
            except Exception as e:
                print(f"❌ Email with Attachment failed: {e}")
                results.append(("Email with Attachment", False))
        
        if test_bulk_only or not any([test_simple_only, test_attachment_only, test_manish_only, test_manish_simple, test_manish_attach]):
            try:
                test_instance.test_bulk_emails(mahalakshmi_emailer)
                results.append(("Bulk Email", True))
            except Exception as e:
                print(f"❌ Bulk Email failed: {e}")
                results.append(("Bulk Email", False))
        
        if test_manish_only or test_manish_simple or not any([test_simple_only, test_attachment_only, test_bulk_only, test_manish_attach]):
            try:
                test_instance.test_manish_simple_email(manish_emailer)
                results.append(("Manish Simple Email", True))
            except Exception as e:
                print(f"❌ Manish Simple Email failed: {e}")
                results.append(("Manish Simple Email", False))
        
        if test_manish_only or test_manish_attach or not any([test_simple_only, test_attachment_only, test_bulk_only, test_manish_simple]):
            try:
                test_instance.test_manish_email_with_attachment(manish_emailer)
                results.append(("Manish Email with Attachment", True))
            except Exception as e:
                print(f"❌ Manish Email with Attachment failed: {e}")
                results.append(("Manish Email with Attachment", False))
        
    except KeyboardInterrupt:
        print("\n⛔ Tests interrupted by user")
        return
    
    # Print results
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    overall_success = all(result[1] for result in results)
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    print("📨 Check the recipient's inbox to verify delivery.")
    print("\n💡 To run with pytest: pytest tests/test_email_library.py -v")
    print("💡 To run specific test: pytest tests/test_email_library.py::TestEmailLibrary::test_simple_email -v")

if __name__ == "__main__":
    main()