#!/usr/bin/env python3
"""
Test Lead Email Sender - ONLY for Lead ID 13390 (Dr. <PERSON><PERSON><PERSON>)
SAFETY: Only <NAME_EMAIL> - NEVER to real customers
Pytest compatible with external API testing capabilities
"""

import sys
import os
from pathlib import Path
from typing import Dict, Optional

# Try to import pytest, but don't require it for CLI usage
try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False
    # Create dummy pytest decorators for CLI usage
    class pytest:
        @staticmethod
        def fixture(func=None):
            return func if func else lambda f: f
        
        class mark:
            @staticmethod
            def external_api(func):
                return func

# Add core directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "core"))

from enhanced_email_composer import EnhancedEmailComposer
from lead_selector import LeadSelector

# Test configuration - SAFETY FIRST
TEST_LEAD_ID = 13390
ALLOWED_TEST_EMAIL = "<EMAIL>"
DATABASE_PATH = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"

class TestLeadEmailSender:
    """SAFE lead email testing - pytest compatible"""
    
    @pytest.fixture
    def lead_selector(self):
        """Fixture for Lead Selector"""
        return LeadSelector(DATABASE_PATH)
    
    @pytest.fixture
    def test_lead(self, lead_selector):
        """Fixture to get the test lead with safety checks"""
        lead = lead_selector.get_lead_by_id(TEST_LEAD_ID, include_interactions=True)
        
        # Safety checks
        assert lead is not None, f"Test lead {TEST_LEAD_ID} not found"
        assert 'error' not in lead, f"Error getting test lead: {lead.get('error')}"
        assert lead.get('email') == ALLOWED_TEST_EMAIL, f"SAFETY ABORT: Lead email {lead.get('email')} is not the allowed test email {ALLOWED_TEST_EMAIL}"
        
        return lead
    
    @pytest.fixture
    def assignment_email_map(self):
        """Assignment to email mapping"""
        return {
            3: "<EMAIL>",
            9: "<EMAIL>",
            None: "<EMAIL>"  # Default fallback
        }
    
    def get_sender_email_from_assignment(self, assigned_to: int, assignment_map: Dict) -> str:
        """Get sender email based on lead assignment"""
        sender_email = assignment_map.get(assigned_to, "<EMAIL>")
        print(f"📧 Using sender: {sender_email} (assignment ID: {assigned_to})")
        return sender_email
    
    def test_lead_retrieval_with_enrichment(self, test_lead):
        """Test that test lead is retrieved with enriched data"""
        print(f"\n🔍 Testing Lead Retrieval with Enrichment")
        
        # Check basic lead data
        assert test_lead['id'] == TEST_LEAD_ID
        assert test_lead['email'] == ALLOWED_TEST_EMAIL
        
        # Check enriched data
        assert 'priority_score' in test_lead, "Lead should have priority score"
        assert 'detected_location' in test_lead, "Lead should have detected location"
        assert 'email_count' in test_lead, "Lead should have email count"
        assert 'status' in test_lead, "Lead should have status"
        
        print(f"✅ Test lead retrieved with enriched data:")
        print(f"   ID: {test_lead['id']}")
        print(f"   Name: {test_lead.get('full_name', 'Unknown')}")
        print(f"   Email: {test_lead.get('email', 'Unknown')}")
        print(f"   Assignment: {test_lead.get('assigned_to', 'Unassigned')}")
        print(f"   Priority Score: {test_lead.get('priority_score', 'N/A')}")
        print(f"   Location: {test_lead.get('detected_location', 'Unknown')}")
        print(f"   Email Count: {test_lead.get('email_count', 0)}")
        print(f"   Status: {test_lead.get('status', 'Unknown')}")
    
    def test_sender_assignment_mapping(self, test_lead, assignment_email_map):
        """Test correct sender assignment based on lead assignment"""
        print(f"\n📧 Testing Sender Assignment Mapping")
        
        assigned_to = test_lead.get('assigned_to')
        sender_email = self.get_sender_email_from_assignment(assigned_to, assignment_email_map)
        
        # Verify correct mapping
        if assigned_to == 3:
            assert sender_email == "<EMAIL>"
        elif assigned_to == 9:
            assert sender_email == "<EMAIL>"
        else:
            assert sender_email == "<EMAIL>"  # Default
        
        print(f"✅ Assignment mapping working correctly")
    
    def test_email_composition(self, test_lead, assignment_email_map):
        """Test email composition with correct sender"""
        print(f"\n🤖 Testing Email Composition")
        
        # Get correct sender
        sender_email = self.get_sender_email_from_assignment(
            test_lead.get('assigned_to'), assignment_email_map
        )
        
        # Create composer with correct sender
        composer = EnhancedEmailComposer(DATABASE_PATH, test_mode=True, sender_email=sender_email)
        
        # Compose email
        subject, body, generation_method = composer.compose_email(test_lead)
        
        # Verify composition
        assert subject, "Email should have a subject"
        assert body, "Email should have a body"
        assert generation_method, "Should know which generation method was used"
        
        print(f"✅ Email composed successfully:")
        print(f"   Generation Method: {generation_method}")
        print(f"   Subject: {subject}")
        print(f"   Body Length: {len(body)} characters")
        print(f"   Sender: {sender_email}")
    
    @pytest.mark.external_api
    def test_actual_email_sending(self, test_lead, assignment_email_map):
        """Test actual email sending with external APIs (requires --external-api flag)"""
        print(f"\n📧 Testing ACTUAL Email Sending to Test Address")
        print(f"⚠️  WARNING: This will send a REAL email to {ALLOWED_TEST_EMAIL}")
        
        # Get correct sender
        sender_email = self.get_sender_email_from_assignment(
            test_lead.get('assigned_to'), assignment_email_map
        )
        
        # Create composer in PRODUCTION mode for actual sending
        composer = EnhancedEmailComposer(DATABASE_PATH, test_mode=False, sender_email=sender_email)
        
        # Compose email
        subject, body, generation_method = composer.compose_email(test_lead)
        
        # Send email
        success = composer.send_email(test_lead, subject, body)
        
        assert success, "Email should send successfully"
        
        print(f"✅ REAL EMAIL SENT SUCCESSFULLY!")
        print(f"   To: {test_lead['email']}")
        print(f"   From: {sender_email}")
        print(f"   Subject: {subject}")
        print(f"   Generation Method: {generation_method}")
    
    def test_safety_checks(self, lead_selector):
        """Test safety checks prevent sending to wrong emails"""
        print(f"\n🛡️ Testing Safety Checks")
        
        # Test with different lead ID (should be safe or fail gracefully)
        fake_lead = {'id': 99999, 'email': '<EMAIL>', 'assigned_to': 3}
        
        # This should NOT send to fake email
        try:
            composer = EnhancedEmailComposer(DATABASE_PATH, test_mode=True)
            subject, body, method = composer.compose_email(fake_lead)
            
            # Should compose but not send to non-test email
            assert subject, "Should compose email even for fake lead"
            print(f"✅ Safety check: Composed email for fake lead without sending")
        except Exception as e:
            print(f"✅ Safety check: Fake lead handled gracefully: {e}")
    
    def test_interaction_logging(self, test_lead, assignment_email_map):
        """Test that interactions are logged properly"""
        print(f"\n📝 Testing Interaction Logging")
        
        sender_email = self.get_sender_email_from_assignment(
            test_lead.get('assigned_to'), assignment_email_map
        )
        
        composer = EnhancedEmailComposer(DATABASE_PATH, test_mode=True, sender_email=sender_email)
        
        # Compose and log email (test mode)
        subject, body, method = composer.compose_email(test_lead)
        success = composer._log_email_composition(test_lead, subject, body, method)
        
        assert success, "Email composition should be logged successfully"
        print(f"✅ Interaction logged successfully")

# CLI compatibility for external API testing
def main():
    """CLI entry point for direct testing with external APIs"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Lead Email Sender (SAFE - Dr. Balaji only)')
    parser.add_argument('--lead-id', type=int, default=TEST_LEAD_ID, help='Lead ID to test (default: 13390)')
    parser.add_argument('--send', action='store_true', help='Actually send email (REAL API calls)')
    parser.add_argument('--yes', action='store_true', help='Skip confirmation prompts')
    
    args = parser.parse_args()
    
    print("🧪 Test Lead Email Sender")
    print("=" * 50)
    print(f"🎯 Target lead ID: {args.lead_id}")
    print(f"🎯 SAFETY: Only allowed email: {ALLOWED_TEST_EMAIL}")
    print("=" * 50)
    
    # Safety check for lead ID
    if args.lead_id != TEST_LEAD_ID:
        print(f"⚠️ WARNING: Testing with non-default lead ID {args.lead_id}")
        if not args.yes:
            confirm = input("Continue? (y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ Test cancelled")
                return
    
    # Initialize components
    selector = LeadSelector(DATABASE_PATH)
    
    # Get test lead
    lead = selector.get_lead_by_id(args.lead_id, include_interactions=True)
    
    if not lead or 'error' in lead:
        print(f"❌ Cannot get lead {args.lead_id}: {lead.get('error') if lead else 'Not found'}")
        return
    
    # SAFETY CHECK
    if lead.get('email') != ALLOWED_TEST_EMAIL:
        print(f"❌ SAFETY ABORT: Lead {args.lead_id} email is not the allowed test email!")
        print(f"   Expected: {ALLOWED_TEST_EMAIL}")
        print(f"   Found: {lead.get('email')}")
        return
    
    # Assignment mapping
    assignment_map = {
        3: "<EMAIL>",
        9: "<EMAIL>",
        None: "<EMAIL>"
    }
    
    sender_email = assignment_map.get(lead.get('assigned_to'), "<EMAIL>")
    
    print(f"✅ Test lead confirmed:")
    print(f"   ID: {lead['id']}")
    print(f"   Name: {lead.get('full_name', 'Unknown')}")
    print(f"   Email: {lead.get('email', 'Unknown')}")
    print(f"   Assigned To: {lead.get('assigned_to', 'Unassigned')}")
    print(f"   Sender: {sender_email}")
    
    # Create composer
    test_mode = not args.send
    composer = EnhancedEmailComposer(DATABASE_PATH, test_mode=test_mode, sender_email=sender_email)
    
    # Compose email
    try:
        subject, body, generation_method = composer.compose_email(lead)
        print(f"✅ Email composed using: {generation_method}")
        print(f"📧 Subject: {subject}")
    except Exception as e:
        print(f"❌ Email composition failed: {e}")
        return
    
    # Send or log
    if args.send:
        if not args.yes:
            confirm = input(f"\n🚨 Send REAL email to {lead['email']}? Type 'SEND' to confirm: ").strip()
            if confirm != 'SEND':
                print("❌ Email sending cancelled")
                return
        
        print(f"📧 SENDING REAL EMAIL...")
        success = composer.send_email(lead, subject, body)
        
        if success:
            print(f"✅ REAL EMAIL SENT SUCCESSFULLY!")
        else:
            print(f"❌ Email sending failed!")
    else:
        print(f"🧪 TEST MODE: Email composition logged (not sent)")
        composer._log_email_composition(lead, subject, body, generation_method)
    
    print(f"\n💡 To run with pytest:")
    print(f"   pytest tests/test_lead_email_sender.py -v")
    print(f"   pytest tests/test_lead_email_sender.py -v -m 'not external_api'  # Skip real API tests")
    print(f"   pytest tests/test_lead_email_sender.py -v -m 'external_api'      # Only real API tests")

if __name__ == "__main__":
    main()