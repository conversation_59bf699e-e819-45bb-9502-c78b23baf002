#!/usr/bin/env python3
"""
Test LLM Email Generation with pytest compatibility
Tests the LLM-based email generation workflow with external API capabilities
"""
import sys
import os
import sqlite3
from pathlib import Path

# Try to import pytest, but don't require it for CLI usage
try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False
    # Create dummy pytest decorators for CLI usage
    class pytest:
        @staticmethod
        def fixture(func=None):
            return func if func else lambda f: f
        
        class mark:
            @staticmethod
            def external_api(func):
                return func

# Ensure we're using the venv python path
venv_path = "/Users/<USER>/Code/modernaipro/mai-administrative/new/scripts/venv/lib/python3.13/site-packages"
if venv_path not in sys.path:
    sys.path.insert(0, venv_path)

# Add core directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "core"))

# Test configuration
TEST_LEAD_ID = 13378  # Ali Naqvi - good test data
DATABASE_PATH = "/Users/<USER>/Code/modernaipro/mai-administrative/new/data/database/leads.db"

class TestLLMGeneration:
    """Test LLM email generation - pytest compatible"""
    
    @pytest.fixture
    def email_generator(self):
        """Fixture for EmailGenerator"""
        try:
            from llm.email_generator import EmailGenerator
            return EmailGenerator()
        except ImportError as e:
            pytest.skip(f"EmailGenerator not available: {e}")
    
    @pytest.fixture
    def test_leads(self):
        """Fixture to get test leads with good data"""
        query = """
        SELECT id, full_name, email, current_title, company, 
               ai_experience_level, learning_goals, workshop_type,
               assigned_to
        FROM leads 
        WHERE full_name IS NOT NULL 
        AND email IS NOT NULL
        AND current_title IS NOT NULL
        AND company IS NOT NULL
        ORDER BY id DESC
        LIMIT 5
        """
        
        try:
            with sqlite3.connect(DATABASE_PATH) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query)
                leads = [dict(row) for row in cursor.fetchall()]
                return leads
        except Exception as e:
            pytest.skip(f"Cannot access database: {e}")
    
    def test_email_generator_import(self):
        """Test that EmailGenerator can be imported"""
        print("🔧 Testing EmailGenerator import...")
        
        try:
            from llm.email_generator import EmailGenerator
            print("✅ EmailGenerator imported successfully!")
            assert True
        except ImportError as e:
            print(f"❌ Import failed: {e}")
            assert False, f"Cannot import EmailGenerator: {e}"
    
    def test_email_generator_initialization(self, email_generator):
        """Test EmailGenerator initialization"""
        print("🔧 Testing EmailGenerator initialization...")
        
        assert email_generator is not None, "EmailGenerator should initialize"
        print("✅ EmailGenerator initialized successfully!")
    
    def test_database_connection(self, test_leads):
        """Test database connection and lead retrieval"""
        print("🔍 Testing database connection and lead data...")
        
        assert len(test_leads) > 0, "Should find test leads with good data"
        
        lead = test_leads[0]
        assert lead['id'], "Lead should have ID"
        assert lead['full_name'], "Lead should have name"
        assert lead['email'], "Lead should have email"
        
        print(f"✅ Found {len(test_leads)} test leads with good data")
        print(f"   Best lead: {lead['full_name']} ({lead['email']})")
    
    @pytest.mark.external_api
    def test_llm_email_generation_specific_lead(self, email_generator):
        """Test LLM email generation with specific test lead"""
        print(f"📧 Testing LLM email generation for lead {TEST_LEAD_ID}...")
        
        try:
            email_content, success = email_generator.generate_email_by_db_id(TEST_LEAD_ID)
            
            if success:
                assert email_content, "Generated email should have content"
                assert len(email_content) > 50, "Email should be substantial"
                
                print("✅ Email generated successfully!")
                print("=" * 60)
                print(email_content[:200] + "..." if len(email_content) > 200 else email_content)
                print("=" * 60)
                
                return True
            else:
                print(f"⚠️ Email generation failed for lead {TEST_LEAD_ID}: {email_content}")
                # Don't fail the test - this could be expected for some leads
                return False
                
        except Exception as e:
            print(f"💥 Error during email generation: {e}")
            assert False, f"Email generation should not raise exceptions: {e}"
    
    @pytest.mark.external_api
    def test_llm_email_generation_best_lead(self, email_generator, test_leads):
        """Test LLM email generation with best available lead"""
        print("📧 Testing LLM email generation with best available lead...")
        
        best_lead = test_leads[0]
        lead_id = best_lead['id']
        
        print(f"🎯 Testing with lead: {best_lead['full_name']} (ID: {lead_id})")
        
        try:
            email_content, success = email_generator.generate_email_by_db_id(lead_id)
            
            assert success, f"Email generation should succeed for lead with good data: {email_content}"
            assert email_content, "Generated email should have content"
            assert len(email_content) > 50, "Email should be substantial"
            
            print("✅ Email generated successfully!")
            print("=" * 60)
            print(email_content[:300] + "..." if len(email_content) > 300 else email_content)
            print("=" * 60)
            
        except Exception as e:
            print(f"💥 Error during email generation: {e}")
            assert False, f"Email generation should not raise exceptions: {e}"
    
    def test_error_handling(self, email_generator):
        """Test error handling with invalid lead ID"""
        print("🛡️ Testing error handling with invalid lead ID...")
        
        try:
            email_content, success = email_generator.generate_email_by_db_id(99999)
            
            # Should handle gracefully - either fail with success=False or handle the error
            if not success:
                print(f"✅ Invalid lead handled gracefully: {email_content}")
            else:
                print("⚠️ Generated email for invalid lead (unexpected but not necessarily wrong)")
            
            # Test passes as long as no exception is raised
            assert True
            
        except Exception as e:
            print(f"⚠️ Exception raised for invalid lead: {e}")
            # This might be expected behavior, so don't fail the test
            assert True

# CLI compatibility for external API testing
def find_good_test_lead():
    """Find a lead with good data for testing"""
    
    query = """
    SELECT id, full_name, email, current_title, company, 
           ai_experience_level, learning_goals, workshop_type,
           assigned_to
    FROM leads 
    WHERE full_name IS NOT NULL 
    AND email IS NOT NULL
    AND current_title IS NOT NULL
    AND company IS NOT NULL
    ORDER BY id DESC
    LIMIT 5
    """
    
    try:
        with sqlite3.connect(DATABASE_PATH) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(query)
            
            leads = [dict(row) for row in cursor.fetchall()]
            
            if leads:
                print("📋 Found potential test leads:")
                for i, lead in enumerate(leads, 1):
                    print(f"   {i}. ID {lead['id']}: {lead['full_name']} ({lead['email']})")
                    print(f"      Title: {lead['current_title']}")
                    print(f"      Company: {lead['company']}")
                    print(f"      Assigned: {lead['assigned_to']}")
                    print()
                
                return leads[0]['id']  # Return the first good lead
            else:
                print("❌ No suitable test leads found")
                return None
                
    except Exception as e:
        print(f"❌ Database error: {e}")
        return None

def test_llm_email_generation_cli(lead_db_id: int):
    """Test LLM email generation with a specific lead (CLI version)"""
    
    print(f"🧪 Testing LLM Email Generation for Lead ID: {lead_db_id}")
    print("=" * 60)
    
    try:
        # Import and initialize the email generator
        print("🔧 Initializing EmailGenerator...")
        from llm.email_generator import EmailGenerator
        generator = EmailGenerator()
        print("✅ EmailGenerator initialized successfully!")
        
        # Generate email
        print(f"\n📧 Generating email for lead {lead_db_id}...")
        email_content, success = generator.generate_email_by_db_id(lead_db_id)
        
        if success:
            print("✅ Email generated successfully!")
            print("=" * 60)
            print(email_content)
            print("=" * 60)
            
            return True
        else:
            print(f"❌ Email generation failed: {email_content}")
            return False
            
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        print("💡 Make sure the LLM module is available and venv is activated")
        return False
    except Exception as e:
        print(f"💥 Error during email generation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function for CLI usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test LLM Email Generation')
    parser.add_argument('--lead-id', type=int, default=TEST_LEAD_ID, help=f'Lead ID to test (default: {TEST_LEAD_ID})')
    parser.add_argument('--find-lead', action='store_true', help='Find a good test lead automatically')
    
    args = parser.parse_args()
    
    print("🧪 Test LLM Email Generation")
    print("=" * 50)
    
    if args.find_lead:
        print("🔍 Finding a good test lead...")
        lead_id = find_good_test_lead()
        if not lead_id:
            print("❌ No good test leads found")
            return
    else:
        lead_id = args.lead_id
    
    print(f"🎯 Testing with lead ID: {lead_id}")
    
    success = test_llm_email_generation_cli(lead_id)
    
    if not success and not args.find_lead:
        print("\n🔍 Let's try to find a lead with better data...")
        better_lead_id = find_good_test_lead()
        
        if better_lead_id and better_lead_id != lead_id:
            print(f"\n🎯 Trying with better lead ID: {better_lead_id}")
            success = test_llm_email_generation_cli(better_lead_id)
    
    if success:
        print("\n🎉 LLM Email Generation Test PASSED!")
    else:
        print("\n💥 LLM Email Generation Test FAILED!")
    
    print(f"\n💡 To run with pytest:")
    print(f"   pytest tests/workflow/test_llm_generation.py -v")
    print(f"   pytest tests/workflow/test_llm_generation.py -v -m 'not external_api'  # Skip LLM API tests")
    print(f"   pytest tests/workflow/test_llm_generation.py -v -m 'external_api'      # Only LLM API tests")

if __name__ == "__main__":
    main()