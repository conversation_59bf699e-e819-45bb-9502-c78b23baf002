# 🚨 EMERGENCY PASSWORD RESET - PRODUCTION USAGE

## CRITICAL - Run This Immediately on Production Server

Due to exposed admin credentials in the git repository, all admin and sales passwords need to be changed immediately.

### 🏃‍♂️ QUICK START (Copy & Paste for Production)

```bash
# 1. Navigate to the utilities directory
cd /path/to/mai-administrative/new/scripts/utilities

# 2. Activate the Python virtual environment
source ../venv/bin/activate

# 3. Install bcrypt if not already installed
pip install bcrypt

# 4. Run the emergency password reset
python emergency_password_reset.py
```

### 📋 What This Script Does

✅ **Finds all users** with roles: `admin`, `sales`, `sales_rep`  
✅ **Generates secure 16-character passwords** (uppercase, lowercase, numbers, symbols)  
✅ **Updates database** with bcrypt hashed passwords (cost factor 12)  
✅ **Creates secure credential files** with 0600 permissions  
✅ **Displays passwords** on screen for immediate use  
✅ **Logs all changes** for audit trail  

### 📊 Expected Users to be Reset

Based on your database, these users will likely be affected:
- <PERSON><PERSON><PERSON> (`bala<PERSON>`) - admin  
- <PERSON><PERSON> (`manish`) - sales  
- <PERSON><PERSON><PERSON><PERSON><PERSON> (`mahalakshmi`) - sales
- Yash Voladoddi (`yash`) - admin

### 🔐 Sample Output

```
✅ PASSWORD RESET COMPLETED SUCCESSFULLY!
📊 Users updated: 4

📋 QUICK REFERENCE (for immediate use):
1. Balaji Viswanathan (admin)
   🔑 balaji / t1v2BBdf:AhYvU;3
2. Manish Tulasi (sales)  
   🔑 manish / J^PrzZNhoA^3N=p5
3. Mahalakshmi M (sales)
   🔑 mahalakshmi / hH@cQ?KCps!3R5tL
4. Yash Voladoddi (admin)
   🔑 yash / >lY!(KvucQ^OWb>0
```

### 📁 Output Files Location

The script creates files in: `mai-administrative/secure_credentials/`

- `emergency_reset_TIMESTAMP.json` - Machine readable
- `NEW_PASSWORDS_TIMESTAMP.txt` - Human readable 
- `password_reset_audit_TIMESTAMP.json` - Audit log

### ⚡ IMMEDIATE ACTIONS AFTER RUNNING

1. **📝 COPY THE NEW PASSWORDS** from the terminal output
2. **🧪 TEST ONE LOGIN** to verify it works: http://localhost:3000/login
3. **📧 NOTIFY ALL USERS** of the password change  
4. **🗑️ DELETE CREDENTIAL FILES** after distributing passwords:
   ```bash
   rm -rf ../../../secure_credentials/
   ```

### 🔒 SECURITY NOTES

- ⚠️  **The compromised password was**: `AdminMitra2025!Secure#99`
- ⚠️  **Anyone with git access** may have seen this password
- ✅ **New passwords are cryptographically secure** using Python's `secrets` module
- ✅ **Database uses bcrypt** with cost factor 12
- ✅ **Credential files have restricted permissions** (0600)

### 🐛 Troubleshooting

**"Database file not found":**
```bash
# Check if database exists
ls -la ../../data/database/leads.db
# If not, adjust the DB_PATH in the script
```

**"ModuleNotFoundError: No module named 'bcrypt'":**
```bash
# Install bcrypt in the virtual environment
source ../venv/bin/activate
pip install bcrypt
```

**"Permission denied" for output files:**
```bash
# Create directory manually
mkdir -p ../../../secure_credentials
chmod 755 ../../../secure_credentials
```

### 🧪 Testing the Script

Before running on production, you can test the password generation:

```bash
python emergency_password_reset.py --test
```

### 📞 Emergency Manual Reset

If the script fails, you can manually reset passwords:

```bash
# Connect to SQLite database
sqlite3 ../../data/database/leads.db

# Check current users
.headers on
SELECT id, username, email, role FROM users WHERE role IN ('admin', 'sales', 'sales_rep');

# Generate bcrypt hash (use online tool or Python)
# UPDATE users SET password_hash = 'NEW_BCRYPT_HASH' WHERE username = 'balaji';
```

### 🎯 Why This Emergency Reset is Needed

**Security Incident Details:**
- **Date**: August 14, 2025
- **Issue**: Hardcoded admin credentials found in `create_admin_account.js` 
- **Exposed**: Username `balaji`, email `<EMAIL>`, password `AdminMitra2025!Secure#99`
- **Risk**: Full admin access exposed in public git repository
- **Git Commit**: `03528a2` by `yashvoladoddi37`

---

**⏰ TIME SENSITIVE**: Run this script immediately to secure your system!