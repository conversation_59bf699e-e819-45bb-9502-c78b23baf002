# Utility Scripts

This directory contains general utility scripts and helper functions used across the Modern AI Pro system.

## Database Utilities

### `database_init.py`
- **Purpose**: Database initialization and setup
- **Usage**: Run when setting up new database instances
- **Features**: Table creation, schema setup, initial data seeding

## Data Processing Utilities

### `excel_reader.py`
- **Purpose**: Excel file reading and processing
- **Usage**: Parse various Excel formats for lead imports
- **Features**: Multiple sheet handling, data validation, format conversion

### `sheet20_parser.py`
- **Purpose**: Specialized parser for Sheet20 data format
- **Usage**: Process specific sheet format used in legacy systems
- **Features**: Custom parsing logic, error handling

### `parse_l1_april.py`
- **Purpose**: Parse L1 April-specific data format
- **Usage**: Process historical data from April L1 records
- **Features**: Date-specific parsing, data transformation

## Integration Utilities

### `establish_workshop_connections.py`
- **Purpose**: Connect workshop attendees with lead records
- **Usage**: Link workshop participation data to CRM
- **Features**: Attendee matching, workshop analytics

### `workshop_parser_template.py`
- **Purpose**: Template for parsing workshop data
- **Usage**: Base template for new workshop data formats
- **Features**: Extensible parsing framework

### `send_whatsapp.py`
- **Purpose**: WhatsApp message automation
- **Usage**: Send notifications and updates via WhatsApp
- **Features**: Bulk messaging, template support, rate limiting

## Planning and Migration Utilities

### `legacy_migration_plan.py`
- **Purpose**: Legacy data migration planning
- **Usage**: Plan and validate migration strategies
- **Features**: Migration roadmap, data mapping, validation rules

## Usage Guidelines

1. **Error Handling**: All utilities include comprehensive error handling
2. **Logging**: Execution logs are written to the `logs/` directory
3. **Configuration**: Check for required configuration files or environment variables
4. **Testing**: Test utilities with sample data before production use

## Dependencies

- Python 3.8+
- Various libraries as specified in main `requirements.txt`
- Database connectivity libraries
- Excel processing libraries (openpyxl, pandas)
- Communication APIs (WhatsApp, etc.)

## Maintenance

These utilities are maintained and updated as system requirements evolve. Check individual file headers for last modification dates and version information.