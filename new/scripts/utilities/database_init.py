#!/usr/bin/env python3
"""
Database Initialization Script
PURPOSE: Create and initialize database tables for Python scripts
USAGE: python database_init.py
"""

import os
import sqlite3
import logging
from datetime import datetime

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), '../data/database/leads.db')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseInitializer:
    def __init__(self):
        self.db_path = DB_PATH
        
    def create_database(self):
        """Create database and all required tables"""
        logger.info(f"Initializing database at {self.db_path}")
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Create leads table
            self.create_leads_table(cursor)
            
            # Create lead interactions table
            self.create_lead_interactions_table(cursor)
            
            # Create users table
            self.create_users_table(cursor)
            
            # Create payments table
            self.create_payments_table(cursor)
            
            # Create workshops table
            self.create_workshops_table(cursor)
            
            # Create certificate templates table
            self.create_certificate_templates_table(cursor)
            
            # Create indexes
            self.create_indexes(cursor)
            
            # Insert initial data
            self.insert_initial_data(cursor)
            
            conn.commit()
            logger.info("Database initialization completed successfully")
            
        except Exception as e:
            logger.error(f"Database initialization failed: {str(e)}")
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def create_leads_table(self, cursor):
        """Create the main leads table"""
        logger.info("Creating leads table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leads (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                
                -- Lead Identification
                lead_id TEXT UNIQUE,
                facebook_lead_id TEXT,
                
                -- Contact Information
                full_name TEXT NOT NULL,
                email TEXT NOT NULL,
                phone TEXT,
                whatsapp_available BOOLEAN DEFAULT FALSE,
                
                -- Professional Information
                current_title TEXT,
                company TEXT,
                company_size TEXT,
                location TEXT,
                industry TEXT,
                
                -- Workshop Information
                workshop_type TEXT,
                workshop_price_usd DECIMAL(10,2),
                workshop_currency TEXT DEFAULT 'USD',
                enrolled_date DATETIME,
                workshop_completed_date DATETIME,
                
                -- Lead Source & Attribution
                lead_source TEXT DEFAULT 'fb_ads',
                account_name TEXT,
                campaign_name TEXT,
                account_id TEXT,
                campaign_id TEXT,
                utm_source TEXT,
                utm_medium TEXT,
                utm_campaign TEXT,
                
                -- Lead Management
                status TEXT DEFAULT 'New',
                priority TEXT DEFAULT 'Medium',
                assigned_to INTEGER,
                lead_score INTEGER DEFAULT 0,
                
                -- AI Experience Assessment
                ai_experience_level TEXT,
                programming_experience TEXT,
                learning_goals TEXT,
                
                -- Communication Tracking
                email_sent BOOLEAN DEFAULT FALSE,
                email_opened BOOLEAN DEFAULT FALSE,
                sms_sent BOOLEAN DEFAULT FALSE,
                whatsapp_sent BOOLEAN DEFAULT FALSE,
                first_response_time DATETIME,
                last_contact_date DATETIME,
                
                -- Payment Tracking
                payment_status TEXT DEFAULT 'Unpaid',
                stripe_payment_id TEXT,
                payment_date DATETIME,
                payment_amount DECIMAL(10,2),
                
                -- Certificate Management
                certificate_type TEXT,
                certificate_generated BOOLEAN DEFAULT FALSE,
                certificate_sent BOOLEAN DEFAULT FALSE,
                certificate_sent_at DATETIME,
                certificate_path TEXT,
                
                -- Referral & Alumni Tracking
                referred_by INTEGER,
                referral_code TEXT,
                is_alumni BOOLEAN DEFAULT FALSE,
                kapi_eligible BOOLEAN DEFAULT FALSE,
                
                -- System Fields
                created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                data_source TEXT DEFAULT 'facebook',
                
                -- Notes
                notes TEXT,
                internal_notes TEXT
            )
        ''')
    
    def create_lead_interactions_table(self, cursor):
        """Create lead interactions/communication log table"""
        logger.info("Creating lead_interactions table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lead_interactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lead_id INTEGER,
                interaction_type TEXT NOT NULL,
                user_email TEXT,
                subject TEXT,
                message TEXT,
                outcome TEXT,
                interaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                
                -- Status change tracking
                old_status TEXT,
                new_status TEXT,
                
                -- Communication metrics
                response_received BOOLEAN DEFAULT FALSE,
                duration_minutes INTEGER,
                
                -- Follow-up tracking
                follow_up_required BOOLEAN DEFAULT FALSE,
                follow_up_date DATETIME,
                follow_up_notes TEXT,
                
                FOREIGN KEY (lead_id) REFERENCES leads(id)
            )
        ''')
    
    def create_users_table(self, cursor):
        """Create users table for SDRs and admin"""
        logger.info("Creating users table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                role TEXT DEFAULT 'sales_rep',
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME,
                
                -- Performance tracking
                leads_assigned INTEGER DEFAULT 0,
                leads_converted INTEGER DEFAULT 0,
                total_revenue_generated DECIMAL(10,2) DEFAULT 0
            )
        ''')
    
    def create_payments_table(self, cursor):
        """Create payments table for Stripe integration"""
        logger.info("Creating payments table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lead_id INTEGER,
                stripe_payment_intent_id TEXT UNIQUE,
                stripe_charge_id TEXT,
                amount DECIMAL(10,2) NOT NULL,
                currency TEXT DEFAULT 'USD',
                status TEXT DEFAULT 'pending',
                workshop_type TEXT NOT NULL,
                cohort_date DATE,
                discount_code TEXT,
                created_by_sdr INTEGER,
                payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                refunded_date DATETIME,
                refund_amount DECIMAL(10,2),
                
                -- Payment link tracking
                payment_link_id TEXT,
                payment_link_url TEXT,
                link_clicked_at DATETIME,
                
                -- Metadata
                customer_email TEXT,
                customer_name TEXT,
                receipt_url TEXT,
                
                FOREIGN KEY (lead_id) REFERENCES leads(id),
                FOREIGN KEY (created_by_sdr) REFERENCES users(id)
            )
        ''')
    
    def create_workshops_table(self, cursor):
        """Create workshops table"""
        logger.info("Creating workshops table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS workshops (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                description TEXT,
                start_date DATETIME NOT NULL,
                end_date DATETIME NOT NULL,
                instructor TEXT,
                max_participants INTEGER DEFAULT 50,
                current_participants INTEGER DEFAULT 0,
                price_usd DECIMAL(10,2),
                price_inr DECIMAL(10,2),
                timezone TEXT DEFAULT 'Asia/Kolkata',
                meeting_link TEXT,
                materials_link TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
    
    def create_certificate_templates_table(self, cursor):
        """Create certificate templates table"""
        logger.info("Creating certificate_templates table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS certificate_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                workshop_type TEXT NOT NULL,
                template_path TEXT NOT NULL,
                background_image TEXT,
                font_config TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
    
    def create_indexes(self, cursor):
        """Create database indexes for performance"""
        logger.info("Creating database indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_leads_email ON leads(email)",
            "CREATE INDEX IF NOT EXISTS idx_leads_status ON leads(status)",
            "CREATE INDEX IF NOT EXISTS idx_leads_created_time ON leads(created_time)",
            "CREATE INDEX IF NOT EXISTS idx_leads_lead_id ON leads(lead_id)",
            "CREATE INDEX IF NOT EXISTS idx_leads_facebook_lead_id ON leads(facebook_lead_id)",
            "CREATE INDEX IF NOT EXISTS idx_leads_stripe_payment_id ON leads(stripe_payment_id)",
            "CREATE INDEX IF NOT EXISTS idx_lead_interactions_lead_id ON lead_interactions(lead_id)",
            "CREATE INDEX IF NOT EXISTS idx_lead_interactions_type ON lead_interactions(interaction_type)",
            "CREATE INDEX IF NOT EXISTS idx_payments_stripe_payment_intent_id ON payments(stripe_payment_intent_id)",
            "CREATE INDEX IF NOT EXISTS idx_payments_lead_id ON payments(lead_id)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def insert_initial_data(self, cursor):
        """Insert initial data like default users and workshop types"""
        logger.info("Inserting initial data...")
        
        # Insert default admin user
        cursor.execute('''
            INSERT OR IGNORE INTO users 
            (username, email, first_name, last_name, role, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('admin', '<EMAIL>', 'Admin', 'User', 'admin', True))
        
        # Insert sample SDRs
        sdrs = [
            ('sanyu', '<EMAIL>', 'Sanyu', 'Sales'),
            ('manish', '<EMAIL>', 'Manish', 'Kumar'),
            ('shivam', '<EMAIL>', 'Shivam', 'Singh')
        ]
        
        for username, email, first_name, last_name in sdrs:
            cursor.execute('''
                INSERT OR IGNORE INTO users 
                (username, email, first_name, last_name, role, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, email, first_name, last_name, 'sales_rep', True))
        
        # Insert certificate templates
        templates = [
            ('AI Essentials Certificate', 'AI Essentials', '/templates/ai-essentials.html'),
            ('AI Practitioner Certificate', 'AI Practitioner', '/templates/ai-practitioner.html'),
            ('Agentic AI Certificate', 'Agentic AI', '/templates/agentic-ai.html'),
            ('Vibe Coding Certificate', 'Vibe Coding', '/templates/vibe-coding.html'),
            ('AI for PMs Certificate', 'AI for PMs', '/templates/ai-for-pms.html'),
            ('AI for UX Certificate', 'AI for UX', '/templates/ai-for-ux.html')
        ]
        
        for name, workshop_type, template_path in templates:
            cursor.execute('''
                INSERT OR IGNORE INTO certificate_templates 
                (name, workshop_type, template_path, is_active)
                VALUES (?, ?, ?, ?)
            ''', (name, workshop_type, template_path, True))
    
    def check_database_health(self):
        """Check if database is properly initialized"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Check if main tables exist
            tables = ['leads', 'lead_interactions', 'users', 'payments', 'workshops', 'certificate_templates']
            
            for table in tables:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if not cursor.fetchone():
                    logger.error(f"Table {table} does not exist")
                    return False
            
            # Check data counts
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM certificate_templates")
            template_count = cursor.fetchone()[0]
            
            logger.info(f"Database health check passed - {user_count} users, {template_count} certificate templates")
            return True
            
        except Exception as e:
            logger.error(f"Database health check failed: {str(e)}")
            return False
        finally:
            conn.close()

def main():
    initializer = DatabaseInitializer()
    
    print("🚀 Initializing Modern AI Pro database...")
    print(f"📍 Database location: {DB_PATH}")
    
    try:
        initializer.create_database()
        
        if initializer.check_database_health():
            print("✅ Database initialization completed successfully!")
            print("📊 You can now run the Stripe payment sync script")
        else:
            print("❌ Database initialization completed but health check failed")
            
    except Exception as e:
        print(f"❌ Database initialization failed: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())