#!/usr/bin/env python3

"""
🚨 EMERGENCY PASSWORD RESET SCRIPT

This script resets passwords for all admin and sales users due to security incident.
Use this immediately after discovering credential exposure.

Usage: python emergency_password_reset.py

SECURITY NOTES:
- Generates cryptographically secure random passwords
- Updates all admin and sales role users
- Outputs new credentials to secure location
- Logs all password changes for audit
"""

import os
import sys
import sqlite3
import secrets
import string
import hashlib
import json
from datetime import datetime
from pathlib import Path
import bcrypt

# Configuration
SCRIPT_DIR = Path(__file__).parent
DB_PATH = SCRIPT_DIR / "../../data/database/leads.db"
OUTPUT_DIR = SCRIPT_DIR.parent.parent.parent / "secure_credentials"
TIMESTAMP = datetime.now().isoformat().replace(':', '-').replace('.', '-')

# Password configuration
PASSWORD_LENGTH = 16
PASSWORD_CHARS = {
    'uppercase': string.ascii_uppercase,
    'lowercase': string.ascii_lowercase,
    'numbers': string.digits,
    'symbols': '!@#$%^&*()_+-=[]{}|;:,.<>?'
}


def generate_secure_password(length=PASSWORD_LENGTH):
    """Generate a cryptographically secure password"""
    all_chars = ''.join(PASSWORD_CHARS.values())
    
    # Ensure at least one character from each category
    password = [
        secrets.choice(PASSWORD_CHARS['uppercase']),
        secrets.choice(PASSWORD_CHARS['lowercase']),
        secrets.choice(PASSWORD_CHARS['numbers']),
        secrets.choice(PASSWORD_CHARS['symbols'])
    ]
    
    # Fill the rest with random characters
    for _ in range(4, length):
        password.append(secrets.choice(all_chars))
    
    # Shuffle the password to randomize position of required characters
    secrets.SystemRandom().shuffle(password)
    return ''.join(password)


def hash_password(password):
    """Hash password using bcrypt"""
    salt = bcrypt.gensalt(rounds=12)
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')


def create_secure_output_dir():
    """Create secure output directory"""
    OUTPUT_DIR.mkdir(mode=0o700, exist_ok=True, parents=True)


def get_target_users(cursor):
    """Get all admin and sales users"""
    cursor.execute("""
        SELECT id, username, email, first_name, last_name, role, last_login
        FROM users 
        WHERE role IN ('admin', 'sales', 'sales_rep') 
        AND is_active = 1
        ORDER BY role, username
    """)
    return cursor.fetchall()


def reset_passwords():
    """Main password reset function"""
    print("🚨 EMERGENCY PASSWORD RESET INITIATED")
    print("=" * 50)
    print(f"⏰ Timestamp: {datetime.now().isoformat()}")
    print(f"📍 Database: {DB_PATH}")
    print()

    if not DB_PATH.exists():
        print(f"❌ Database file not found: {DB_PATH}")
        print("Please check the database path and try again.")
        sys.exit(1)

    reset_results = []
    audit_log = []

    try:
        # Connect to database
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        print("✅ Connected to database")

        # Get all admin and sales users
        target_users = get_target_users(cursor)

        print(f"📋 Found {len(target_users)} users requiring password reset:")
        for user in target_users:
            print(f"   • {user['first_name']} {user['last_name']} ({user['username']}) - {user['role']}")
        print()

        if len(target_users) == 0:
            print("⚠️  No admin or sales users found. Exiting.")
            return

        # Confirm before proceeding
        print("🔄 Starting password reset process...")
        print()

        # Process each user
        for user in target_users:
            try:
                # Generate new secure password
                new_password = generate_secure_password()
                password_hash = hash_password(new_password)

                # Update database
                cursor.execute("""
                    UPDATE users 
                    SET password_hash = ? 
                    WHERE id = ?
                """, (password_hash, user['id']))

                if cursor.rowcount == 1:
                    reset_data = {
                        'id': user['id'],
                        'username': user['username'],
                        'email': user['email'],
                        'firstName': user['first_name'],
                        'lastName': user['last_name'],
                        'role': user['role'],
                        'newPassword': new_password,
                        'resetAt': datetime.now().isoformat(),
                        'previousLogin': user['last_login']
                    }

                    reset_results.append(reset_data)

                    # Log for audit (without password)
                    audit_log.append({
                        'userId': user['id'],
                        'username': user['username'],
                        'email': user['email'],
                        'role': user['role'],
                        'action': 'password_reset',
                        'timestamp': datetime.now().isoformat(),
                        'reason': 'emergency_security_incident'
                    })

                    print(f"✅ Reset password for: {user['first_name']} {user['last_name']} ({user['username']})")
                else:
                    print(f"❌ Failed to reset password for: {user['username']}")

            except Exception as e:
                print(f"❌ Error resetting password for {user['username']}: {e}")

        # Commit all changes
        conn.commit()

        # Create secure output directory
        create_secure_output_dir()

        # Write credentials to secure file
        credentials_file = OUTPUT_DIR / f"emergency_reset_{TIMESTAMP}.json"
        credentials_data = {
            'resetReason': 'Emergency password reset due to credential exposure in git repository',
            'resetTimestamp': datetime.now().isoformat(),
            'affectedUsers': len(reset_results),
            'credentials': [
                {
                    'name': f"{user['firstName']} {user['lastName']}",
                    'username': user['username'],
                    'email': user['email'],
                    'role': user['role'],
                    'newPassword': user['newPassword'],
                    'loginUrl': 'http://localhost:3000/login'
                }
                for user in reset_results
            ]
        }

        with open(credentials_file, 'w') as f:
            json.dump(credentials_data, f, indent=2)
        os.chmod(credentials_file, 0o600)

        # Write audit log
        audit_file = OUTPUT_DIR / f"password_reset_audit_{TIMESTAMP}.json"
        with open(audit_file, 'w') as f:
            json.dump(audit_log, f, indent=2)
        os.chmod(audit_file, 0o600)

        # Write human-readable summary
        summary_file = OUTPUT_DIR / f"NEW_PASSWORDS_{TIMESTAMP}.txt"
        summary_content = f"""🚨 EMERGENCY PASSWORD RESET SUMMARY
{'=' * 50}
Reset Date: {datetime.now().isoformat()}
Reason: Credential exposure in git repository
Users Updated: {len(reset_results)}

NEW CREDENTIALS:
{'=' * 50}

"""

        for i, user in enumerate(reset_results, 1):
            summary_content += f"""{i}. {user['firstName']} {user['lastName']}
   Username: {user['username']}
   Email: {user['email']}
   Role: {user['role']}
   New Password: {user['newPassword']}
   Login URL: http://localhost:3000/login

"""

        summary_content += f"""SECURITY NOTES:
{'=' * 20}
• All passwords are cryptographically secure (16 characters)
• Passwords contain uppercase, lowercase, numbers, and symbols
• Users should change passwords on first login
• This file should be deleted after password distribution
• Inform users to update any stored credentials

"""

        with open(summary_file, 'w') as f:
            f.write(summary_content)
        os.chmod(summary_file, 0o600)

        print()
        print("✅ PASSWORD RESET COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print(f"📊 Users updated: {len(reset_results)}")
        print(f"📁 Credentials file: {credentials_file}")
        print(f"📋 Summary file: {summary_file}")
        print(f"📝 Audit log: {audit_file}")
        print()
        print("🔒 NEXT STEPS:")
        print("1. Securely distribute new passwords to users")
        print("2. Instruct users to change passwords on first login")
        print("3. Delete credential files after distribution")
        print("4. Update any automated systems using old credentials")
        print("5. Monitor for any unauthorized access attempts")
        print()
        print("⚠️  IMPORTANT: Keep credential files secure and delete after use!")

        # Display quick summary of new credentials
        print()
        print("📋 QUICK REFERENCE (for immediate use):")
        print("=" * 50)
        for i, user in enumerate(reset_results, 1):
            print(f"{i}. {user['firstName']} {user['lastName']} ({user['role']})")
            print(f"   🔑 {user['username']} / {user['newPassword']}")

    except Exception as e:
        print(f"❌ CRITICAL ERROR: {e}")
        sys.exit(1)
    finally:
        if 'conn' in locals():
            conn.close()


def test_password_generation():
    """Test password generation functionality"""
    print("🔐 Testing Password Generation")
    print("=" * 40)

    for i in range(1, 6):
        password = generate_secure_password()
        
        # Validate password criteria
        has_uppercase = any(c in string.ascii_uppercase for c in password)
        has_lowercase = any(c in string.ascii_lowercase for c in password)
        has_numbers = any(c in string.digits for c in password)
        has_symbols = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password)
        is_correct_length = len(password) == 16
        
        is_valid = all([has_uppercase, has_lowercase, has_numbers, has_symbols, is_correct_length])
        
        print(f"{i}. {password}")
        print(f"   ✅ Length: {len(password)} {'✅' if is_correct_length else '❌'}")
        print(f"   ✅ Uppercase: {'✅' if has_uppercase else '❌'} Lowercase: {'✅' if has_lowercase else '❌'}")
        print(f"   ✅ Numbers: {'✅' if has_numbers else '❌'} Symbols: {'✅' if has_symbols else '❌'}")
        print(f"   {'✅ VALID' if is_valid else '❌ INVALID'}")
        print()

    print("✅ Password generation test completed")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_password_generation()
    else:
        reset_passwords()