#!/usr/bin/env python3
"""
Establish Workshop Connections Script
- Creates workshop records in workshops table
- Creates payment records for paid leads
- Links leads to workshops properly
- Identifies customers vs leads
"""

import sqlite3
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class WorkshopConnectionManager:
    def __init__(self):
        self.db_path = project_root / "dashboard" / "database" / "leads.db"
        
        # Workshop mapping based on the Excel analysis
        self.workshop_mappings = {
            # L1 - Essentials workshops
            'L1 April 5&6': {
                'name': 'AI Essentials - April 2025',
                'type': 'L1',
                'description': 'Foundation course covering core AI concepts for enterprise applications',
                'start_date': '2025-04-05 10:00:00',
                'end_date': '2025-04-06 17:00:00',
                'price_usd': 320.00
            },
            'L1 Feb1&2': {
                'name': 'AI Essentials - February 2025',
                'type': 'L1',
                'description': 'Foundation course covering core AI concepts for enterprise applications',
                'start_date': '2025-02-01 10:00:00',
                'end_date': '2025-02-02 17:00:00',
                'price_usd': 230.00
            },
            'L1 Dec 14&15': {
                'name': 'AI Essentials - December 2024',
                'type': 'L1',
                'description': 'Foundation course covering core AI concepts for enterprise applications',
                'start_date': '2024-12-14 10:00:00',
                'end_date': '2024-12-15 17:00:00',
                'price_usd': 230.00
            },
            'L1 Nov9&10': {
                'name': 'AI Essentials - November 2024',
                'type': 'L1',
                'description': 'Foundation course covering core AI concepts for enterprise applications',
                'start_date': '2024-11-09 10:00:00',
                'end_date': '2024-11-10 17:00:00',
                'price_usd': 230.00
            },
            'L1 Sep28&29': {
                'name': 'AI Essentials - September 2024 (Late)',
                'type': 'L1',
                'description': 'Foundation course covering core AI concepts for enterprise applications',
                'start_date': '2024-09-28 10:00:00',
                'end_date': '2024-09-29 17:00:00',
                'price_usd': 230.00
            },
            'L1 Sep7&8': {
                'name': 'AI Essentials - September 2024 (Early)',
                'type': 'L1',
                'description': 'Foundation course covering core AI concepts for enterprise applications',
                'start_date': '2024-09-07 10:00:00',
                'end_date': '2024-09-08 17:00:00',
                'price_usd': 230.00
            },
            
            # A1 - Agentic AI workshops
            'A1 May 3&4': {
                'name': 'Agentic AI Specialization - May 2025',
                'type': 'A1',
                'description': 'Advanced course focusing on autonomous AI agents and enterprise deployment',
                'start_date': '2025-05-03 10:00:00',
                'end_date': '2025-05-04 17:00:00',
                'price_usd': 272.00
            },
            'A1 March 1&2': {
                'name': 'Agentic AI Specialization - March 2025',
                'type': 'A1',
                'description': 'Advanced course focusing on autonomous AI agents and enterprise deployment',
                'start_date': '2025-03-01 10:00:00',
                'end_date': '2025-03-02 17:00:00',
                'price_usd': 272.00
            },
            'A1 Jan18&19': {
                'name': 'Agentic AI Specialization - January 2025',
                'type': 'A1',
                'description': 'Advanced course focusing on autonomous AI agents and enterprise deployment',
                'start_date': '2025-01-18 10:00:00',
                'end_date': '2025-01-19 17:00:00',
                'price_usd': 272.00
            },
            
            # L2 - Practitioner workshops
            'L2 Nov16&17': {
                'name': 'AI Practitioner Advanced - November 2024',
                'type': 'L2',
                'description': 'Advanced course focused on AI deployment and practical implementations',
                'start_date': '2024-11-16 10:00:00',
                'end_date': '2024-11-17 17:00:00',
                'price_usd': 320.00
            },
            
            # Young AI (Discontinued)
            'Young AI': {
                'name': 'Young AI Program (Discontinued)',
                'type': 'YAI',
                'description': 'Special program for young learners - now discontinued',
                'start_date': '2024-11-12 10:00:00',
                'end_date': '2024-12-20 17:00:00',
                'price_usd': 272.00
            }
        }
        
        print(f"🔗 Workshop Connection Manager initialized")
        print(f"🗄️  Database: {self.db_path}")
        print(f"🎓 Workshop mappings: {len(self.workshop_mappings)} workshops")
    
    def create_workshop_records(self):
        """Create workshop records in the workshops table"""
        print(f"\n🏗️  Creating workshop records...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        created_count = 0
        
        for sheet_name, workshop_info in self.workshop_mappings.items():
            try:
                # Check if workshop already exists
                cursor.execute("""
                    SELECT id FROM workshops 
                    WHERE name = ? AND type = ? AND start_date = ?
                """, (workshop_info['name'], workshop_info['type'], workshop_info['start_date']))
                
                if cursor.fetchone():
                    print(f"   ⏭️  Workshop exists: {workshop_info['name']}")
                    continue
                
                # Insert workshop
                insert_sql = """
                    INSERT INTO workshops (
                        name, type, description, start_date, end_date, 
                        price_usd, timezone, is_active, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                cursor.execute(insert_sql, (
                    workshop_info['name'],
                    workshop_info['type'],
                    workshop_info['description'],
                    workshop_info['start_date'],
                    workshop_info['end_date'],
                    workshop_info['price_usd'],
                    'America/New_York',  # US workshops
                    True,
                    datetime.now().isoformat()
                ))
                
                created_count += 1
                print(f"   ✅ Created: {workshop_info['name']} ({workshop_info['type']})")
                
            except Exception as e:
                print(f"   ❌ Error creating workshop {sheet_name}: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 Created {created_count} workshop records")
        return created_count
    
    def link_leads_to_workshops(self):
        """Link leads to workshops and create payment records"""
        print(f"\n🔗 Linking leads to workshops...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get all leads that came from Excel import with workshop_type
        cursor.execute("""
            SELECT id, full_name, email, workshop_type, payment_status, 
                   payment_amount, payment_date, enrolled_date, data_source, notes
            FROM leads 
            WHERE data_source LIKE '%US Leads.xlsx%' 
            AND workshop_type IS NOT NULL
        """)
        
        leads = cursor.fetchall()
        print(f"📊 Found {len(leads)} leads from Excel import")
        
        payment_created = 0
        workshop_linked = 0
        customers_identified = 0
        
        for lead in leads:
            lead_id, name, email, workshop_type, payment_status, payment_amount, payment_date, enrolled_date, data_source, notes = lead
            
            try:
                # Extract workshop sheet name from data_source
                sheet_name = None
                for sheet in self.workshop_mappings.keys():
                    if sheet in data_source:
                        sheet_name = sheet
                        break
                
                if not sheet_name:
                    print(f"   ⚠️  No workshop sheet found for {name}")
                    continue
                
                # Get workshop ID by matching the sheet name directly
                workshop_info = self.workshop_mappings.get(sheet_name)
                if not workshop_info:
                    print(f"   ⚠️  No workshop mapping for sheet {sheet_name}")
                    continue
                
                cursor.execute("""
                    SELECT id FROM workshops 
                    WHERE name = ? AND type = ?
                """, (workshop_info['name'], workshop_info['type']))
                
                workshop_row = cursor.fetchone()
                if not workshop_row:
                    print(f"   ⚠️  No workshop found for {name} ({sheet_name})")
                    continue
                
                workshop_id = workshop_row[0]
                
                # Create payment record if paid
                if payment_status == 'Paid' and payment_amount and payment_amount > 0:
                    # Check if payment record exists
                    cursor.execute("""
                        SELECT id FROM payments WHERE lead_id = ? AND workshop_type = ?
                    """, (lead_id, workshop_type))
                    
                    if not cursor.fetchone():
                        # Create payment record
                        payment_sql = """
                            INSERT INTO payments (
                                lead_id, amount, currency, status, workshop_type,
                                payment_date, customer_email, customer_name
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        
                        cursor.execute(payment_sql, (
                            lead_id,
                            payment_amount,
                            'USD',
                            'completed',
                            workshop_type,
                            payment_date or enrolled_date or datetime.now().isoformat(),
                            email,
                            name
                        ))
                        
                        payment_created += 1
                        customers_identified += 1
                        print(f"   💳 Payment created: {name} - ${payment_amount}")
                
                # Update lead with workshop price if missing
                if not payment_amount and sheet_name in self.workshop_mappings:
                    workshop_price = self.workshop_mappings[sheet_name]['price_usd']
                    cursor.execute("""
                        UPDATE leads SET workshop_price_usd = ? WHERE id = ?
                    """, (workshop_price, lead_id))
                
                workshop_linked += 1
                
            except Exception as e:
                print(f"   ❌ Error processing {name}: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 Workshop linking complete!")
        print(f"   🔗 Leads linked: {workshop_linked}")
        print(f"   💳 Payments created: {payment_created}")
        print(f"   👥 Customers identified: {customers_identified}")
        
        return {
            'linked': workshop_linked,
            'payments': payment_created,
            'customers': customers_identified
        }
    
    def update_customer_status(self):
        """Update leads to mark them as customers if they have payments"""
        print(f"\n👥 Updating customer status...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Update leads with payments to be customers
        cursor.execute("""
            UPDATE leads 
            SET is_alumni = TRUE,
                status = 'Customer'
            WHERE id IN (
                SELECT DISTINCT lead_id 
                FROM payments 
                WHERE status = 'completed'
            )
            AND is_alumni = FALSE
        """)
        
        customers_updated = cursor.rowcount
        
        # Update leads without payments but with payment_status = 'Paid'
        cursor.execute("""
            UPDATE leads 
            SET is_alumni = TRUE,
                status = 'Customer'
            WHERE payment_status = 'Paid' 
            AND is_alumni = FALSE
        """)
        
        additional_customers = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        total_customers = customers_updated + additional_customers
        print(f"   ✅ Updated {total_customers} leads to customer status")
        print(f"      📊 From payments table: {customers_updated}")
        print(f"      📊 From payment_status: {additional_customers}")
        
        return total_customers
    
    def generate_workshop_summary(self):
        """Generate a summary of workshops and their enrollments"""
        print(f"\n📊 Generating workshop summary...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get workshop enrollment stats
        cursor.execute("""
            SELECT 
                w.name,
                w.type,
                w.start_date,
                w.price_usd,
                COUNT(l.id) as total_enrolled,
                COUNT(CASE WHEN l.payment_status = 'Paid' THEN 1 END) as paid_students,
                COUNT(p.id) as payments_recorded,
                SUM(CASE WHEN l.payment_status = 'Paid' THEN l.payment_amount ELSE 0 END) as revenue_from_leads,
                SUM(p.amount) as revenue_from_payments
            FROM workshops w
            LEFT JOIN leads l ON l.workshop_type = w.type 
                AND l.data_source LIKE '%US Leads.xlsx%'
            LEFT JOIN payments p ON p.lead_id = l.id
            GROUP BY w.id, w.name, w.type, w.start_date, w.price_usd
            ORDER BY w.start_date DESC
        """)
        
        workshops = cursor.fetchall()
        
        print(f"\n🎓 WORKSHOP ENROLLMENT SUMMARY")
        print("=" * 80)
        
        total_revenue = 0
        total_students = 0
        
        for workshop in workshops:
            name, type_, start_date, price, enrolled, paid, payments, revenue_leads, revenue_payments = workshop
            
            revenue = revenue_payments or revenue_leads or 0
            total_revenue += revenue
            total_students += paid
            
            print(f"\n🎯 {name}")
            print(f"   📅 Date: {start_date}")
            print(f"   💰 Price: ${price}")
            print(f"   👥 Enrolled: {enrolled} | Paid: {paid} | Payments: {payments}")
            print(f"   💵 Revenue: ${revenue:.2f}")
        
        print(f"\n🏆 TOTALS:")
        print(f"   👥 Total Customers: {total_students}")
        print(f"   💵 Total Revenue: ${total_revenue:.2f}")
        
        conn.close()
        
        return {
            'total_customers': total_students,
            'total_revenue': total_revenue,
            'workshops': len(workshops)
        }

def main():
    print("=" * 60)
    print("🔗 WORKSHOP CONNECTION ESTABLISHMENT")
    print("=" * 60)
    
    manager = WorkshopConnectionManager()
    
    # Step 1: Create workshop records
    created_workshops = manager.create_workshop_records()
    
    # Step 2: Link leads to workshops and create payment records
    linking_results = manager.link_leads_to_workshops()
    
    # Step 3: Update customer status
    customers_updated = manager.update_customer_status()
    
    # Step 4: Generate summary
    summary = manager.generate_workshop_summary()
    
    print(f"\n✨ WORKSHOP CONNECTION COMPLETE!")
    print(f"🏗️  Workshops created: {created_workshops}")
    print(f"🔗 Leads linked: {linking_results['linked']}")
    print(f"💳 Payments created: {linking_results['payments']}")
    print(f"👥 Customers identified: {customers_updated}")
    print(f"💵 Total revenue tracked: ${summary['total_revenue']:.2f}")

if __name__ == "__main__":
    main()