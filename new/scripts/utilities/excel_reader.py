#!/usr/bin/env python3
"""
Excel Data Migration Script for Workshop Data
Reads Excel files from assets directory and processes workshop/student data
"""

import pandas as pd
import sqlite3
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class ExcelDataMigrator:
    def __init__(self):
        self.assets_dir = project_root / "assets"
        self.db_path = project_root / "dashboard" / "database" / "leads.db"
        
        print(f"🚀 Excel Data Migrator initialized")
        print(f"📁 Assets directory: {self.assets_dir}")
        print(f"🗄️  Database path: {self.db_path}")
        
    def list_excel_files(self):
        """List all Excel files in the assets directory"""
        excel_files = []
        for file_path in self.assets_dir.glob("*.xlsx"):
            size_mb = file_path.stat().st_size / (1024 * 1024)
            excel_files.append({
                'name': file_path.name,
                'path': str(file_path),
                'size_mb': round(size_mb, 2)
            })
        
        print(f"\n📊 Found {len(excel_files)} Excel files:")
        for file_info in excel_files:
            print(f"  • {file_info['name']} ({file_info['size_mb']} MB)")
        
        return excel_files
    
    def analyze_excel_file(self, file_path, max_preview_rows=10):
        """Analyze an Excel file structure and preview data"""
        try:
            print(f"\n🔍 Analyzing: {Path(file_path).name}")
            print("=" * 50)
            
            # Read Excel file
            xl_file = pd.ExcelFile(file_path)
            
            analysis = {
                'file_name': Path(file_path).name,
                'sheets': {},
                'total_sheets': len(xl_file.sheet_names)
            }
            
            print(f"📋 Sheets found: {xl_file.sheet_names}")
            
            for sheet_name in xl_file.sheet_names:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    
                    sheet_info = {
                        'rows': len(df),
                        'columns': len(df.columns),
                        'column_names': list(df.columns),
                        'data_types': df.dtypes.to_dict(),
                        'preview': df.head(max_preview_rows).to_dict('records') if len(df) > 0 else []
                    }
                    
                    analysis['sheets'][sheet_name] = sheet_info
                    
                    print(f"\n📊 Sheet: '{sheet_name}'")
                    print(f"   Rows: {sheet_info['rows']}")
                    print(f"   Columns: {sheet_info['columns']}")
                    print(f"   Column Names: {', '.join(sheet_info['column_names'][:10])}{'...' if len(sheet_info['column_names']) > 10 else ''}")
                    
                    # Show preview of first few rows
                    if len(df) > 0:
                        print(f"\n   📝 Preview (first {min(max_preview_rows, len(df))} rows):")
                        print(df.head(max_preview_rows).to_string(index=False, max_cols=8))
                    
                except Exception as e:
                    print(f"   ❌ Error reading sheet '{sheet_name}': {e}")
                    analysis['sheets'][sheet_name] = {'error': str(e)}
            
            return analysis
            
        except Exception as e:
            print(f"❌ Error analyzing file {file_path}: {e}")
            return None
    
    def suggest_data_mapping(self, analysis):
        """Suggest how to map Excel data to database tables"""
        suggestions = {
            'potential_leads': [],
            'potential_workshops': [],
            'potential_payments': [],
            'workshop_enrollments': []
        }
        
        print(f"\n💡 Data Mapping Suggestions for {analysis['file_name']}:")
        print("=" * 60)
        
        for sheet_name, sheet_info in analysis['sheets'].items():
            if 'error' in sheet_info:
                continue
                
            columns = [col.lower() for col in sheet_info['column_names']]
            
            # Check for leads data
            lead_indicators = ['name', 'email', 'phone', 'lead', 'contact', 'student', 'participant']
            if any(indicator in ' '.join(columns) for indicator in lead_indicators):
                suggestions['potential_leads'].append({
                    'sheet': sheet_name,
                    'columns': sheet_info['column_names'],
                    'confidence': 'high' if 'email' in ' '.join(columns) else 'medium'
                })
                print(f"📧 LEADS DATA - Sheet: '{sheet_name}' (Found contact/email fields)")
            
            # Check for workshop data
            workshop_indicators = ['workshop', 'course', 'class', 'training', 'cohort', 'batch']
            if any(indicator in ' '.join(columns) for indicator in workshop_indicators):
                suggestions['potential_workshops'].append({
                    'sheet': sheet_name,
                    'columns': sheet_info['column_names'],
                    'confidence': 'high' if any(ind in ' '.join(columns) for ind in ['date', 'time', 'schedule']) else 'medium'
                })
                print(f"🎓 WORKSHOP DATA - Sheet: '{sheet_name}' (Found workshop/training fields)")
            
            # Check for payment data
            payment_indicators = ['payment', 'amount', 'price', 'fee', 'cost', 'paid', 'revenue', 'stripe']
            if any(indicator in ' '.join(columns) for indicator in payment_indicators):
                suggestions['potential_payments'].append({
                    'sheet': sheet_name,
                    'columns': sheet_info['column_names'],
                    'confidence': 'high' if any(ind in ' '.join(columns) for ind in ['amount', 'payment', 'stripe']) else 'medium'
                })
                print(f"💳 PAYMENT DATA - Sheet: '{sheet_name}' (Found payment/financial fields)")
            
            # Check for enrollment data
            enrollment_indicators = ['enroll', 'register', 'signup', 'join', 'attendance', 'completion']
            if any(indicator in ' '.join(columns) for indicator in enrollment_indicators):
                suggestions['workshop_enrollments'].append({
                    'sheet': sheet_name,
                    'columns': sheet_info['column_names'],
                    'confidence': 'medium'
                })
                print(f"📝 ENROLLMENT DATA - Sheet: '{sheet_name}' (Found enrollment fields)")
        
        return suggestions
    
    def migrate_leads_data(self, file_path, sheet_name, column_mapping):
        """Migrate leads data from Excel to database"""
        try:
            print(f"\n🔄 Migrating leads from {Path(file_path).name} - Sheet: {sheet_name}")
            
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # Connect to database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            migrated_count = 0
            skipped_count = 0
            
            for _, row in df.iterrows():
                try:
                    # Extract data using column mapping
                    lead_data = {}
                    for db_field, excel_col in column_mapping.items():
                        if excel_col in df.columns:
                            lead_data[db_field] = row[excel_col] if pd.notna(row[excel_col]) else None
                    
                    # Skip if no email
                    if not lead_data.get('email'):
                        skipped_count += 1
                        continue
                    
                    # Check if lead already exists
                    cursor.execute("SELECT id FROM leads WHERE email = ?", (lead_data['email'],))
                    if cursor.fetchone():
                        skipped_count += 1
                        continue
                    
                    # Insert lead
                    insert_sql = """
                        INSERT INTO leads (
                            full_name, email, phone, workshop_type, payment_status,
                            lead_source, data_source, created_time, notes
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    cursor.execute(insert_sql, (
                        lead_data.get('full_name', ''),
                        lead_data['email'],
                        lead_data.get('phone', ''),
                        lead_data.get('workshop_type', ''),
                        lead_data.get('payment_status', 'Unknown'),
                        'excel_import',
                        f"Imported from {Path(file_path).name}",
                        datetime.now().isoformat(),
                        f"Migrated from Excel: {sheet_name}"
                    ))
                    
                    migrated_count += 1
                    
                except Exception as e:
                    print(f"   ⚠️  Error processing row: {e}")
                    skipped_count += 1
            
            conn.commit()
            conn.close()
            
            print(f"   ✅ Migrated: {migrated_count} leads")
            print(f"   ⏭️  Skipped: {skipped_count} leads")
            
            return {'migrated': migrated_count, 'skipped': skipped_count}
            
        except Exception as e:
            print(f"❌ Error migrating leads: {e}")
            return None
    
    def export_analysis_report(self, analyses, output_file=None):
        """Export analysis report to JSON file"""
        if not output_file:
            output_file = project_root / "scripts" / "data_migration" / f"excel_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(output_file, 'w') as f:
                json.dump(analyses, f, indent=2, default=str)
            
            print(f"\n📄 Analysis report saved: {output_file}")
            return str(output_file)
            
        except Exception as e:
            print(f"❌ Error saving report: {e}")
            return None

def main():
    """Main function to analyze Excel files"""
    print("=" * 60)
    print("📊 EXCEL DATA MIGRATION TOOL")
    print("=" * 60)
    
    migrator = ExcelDataMigrator()
    
    # List all Excel files
    excel_files = migrator.list_excel_files()
    
    if not excel_files:
        print("\n❌ No Excel files found in assets directory")
        return
    
    # Analyze each file
    all_analyses = {}
    
    for file_info in excel_files:
        analysis = migrator.analyze_excel_file(file_info['path'])
        if analysis:
            all_analyses[file_info['name']] = analysis
            
            # Get mapping suggestions
            suggestions = migrator.suggest_data_mapping(analysis)
            analysis['mapping_suggestions'] = suggestions
    
    # Export report
    migrator.export_analysis_report(all_analyses)
    
    print(f"\n🎉 Analysis complete!")
    print(f"📁 Found {len(excel_files)} Excel files")
    print(f"📊 Analyzed {len(all_analyses)} files successfully")
    print(f"\n💡 Next steps:")
    print(f"   1. Review the analysis above")
    print(f"   2. Identify which sheets contain workshop/student data")
    print(f"   3. Use the mapping suggestions to plan data migration")

if __name__ == "__main__":
    main()