#!/usr/bin/env python3
"""
Legacy CSV Migration Plan Generator
Creates specific migration strategy based on analysis
"""

import pandas as pd
import sqlite3
from datetime import datetime

def generate_migration_plan():
    """Generate specific migration plan with SQL and tagging strategy"""
    
    print("LEGACY CSV MIGRATION PLAN")
    print("=" * 50)
    print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n1. EXECUTIVE SUMMARY:")
    print("   - Source: 2 legacy CSV files (master_list.csv, leads_export.csv)")
    print("   - Current DB: 7,895 leads (recent HubSpot migration)")
    print("   - Migration Value: 3,274 new leads (48% database growth)")
    print("   - Recommendation: MIGRATE leads_export.csv ONLY")
    print("   - Expected ROI: 163.7x (excellent)")
    
    print("\n2. FILE ANALYSIS RESULTS:")
    print("   MASTER_LIST.CSV:")
    print("   ├── Records: 3,402")  
    print("   ├── New leads: 9 (0.3%)")
    print("   ├── Quality issues: Invalid email formats detected")
    print("   └── Recommendation: SKIP - not worth migration effort")
    
    print("\n   LEADS_EXPORT.CSV:")
    print("   ├── Records: 6,425")
    print("   ├── New leads: 3,274 (52.2%)")
    print("   ├── Lead sources: Facebook (98.5%), LinkedIn (1.5%)")
    print("   ├── Data quality: Good (99.5% phone, 100% names)")
    print("   └── Recommendation: MIGRATE - excellent value")
    
    print("\n3. MIGRATION STRATEGY:")
    print("   Step 1: Prepare leads_export.csv")
    print("   ├── Clean email addresses (lowercase, trim)")
    print("   ├── Split 'name' field into first_name, last_name")
    print("   ├── Standardize phone numbers (remove +, format)")
    print("   └── Apply quality tags")
    
    print("\n   Step 2: Database Insertion")
    print("   ├── Filter out existing emails (47.8% overlap)")
    print("   ├── Insert 3,274 new leads")
    print("   ├── Tag with vintage and quality markers")
    print("   └── Update lead statistics")
    
    print("\n4. TAGGING STRATEGY:")
    print("   Quality Tags:")
    print("   ├── email_quality: 'medium' (legacy but clean)")
    print("   ├── response_expectation: 'low' (older leads)")
    print("   ├── data_vintage: 'legacy_facebook'")  
    print("   ├── lead_origin: 'facebook_export_legacy'")
    print("   └── priority: 'Low'")
    
    print("\n   Source Attribution:")
    print("   ├── lead_source: 'Facebook' (6,327), 'LinkedIn' (97)")
    print("   ├── data_source: 'leads_export_legacy'")
    print("   ├── import_date: current timestamp")
    print("   └── notes: 'Migrated from legacy CSV export'")
    
    print("\n5. IMPLEMENTATION COMMANDS:")
    print("\n   # Create migration script for leads_export.csv:")
    print('   python workshop_parser_template.py "leads_export" "Legacy_Facebook" "Historical Import"')
    
    print("\n   # Recommended SQL for tagging:")
    print("   UPDATE leads SET ")
    print("     email_quality = 'medium',")
    print("     response_expectation = 'low',") 
    print("     data_vintage = 'legacy_facebook',")
    print("     priority = 'Low'")
    print("   WHERE data_source = 'leads_export_legacy';")
    
    print("\n6. EXPECTED OUTCOMES:")
    print("   Database Growth:")
    print("   ├── Before: 7,895 leads")
    print("   ├── After: 11,169 leads (+3,274)")
    print("   └── Growth: 41.5%")
    
    print("\n   Lead Composition:")
    print("   ├── Recent HubSpot: 7,895 (70.7%)")
    print("   ├── Legacy Facebook: 3,274 (29.3%)")
    print("   └── Total: 11,169 leads")
    
    print("\n   Quality Distribution:")
    print("   ├── High quality (recent): 70.7%")
    print("   ├── Medium quality (legacy): 29.3%")
    print("   └── Campaign potential: Segment by vintage")
    
    print("\n7. RISK ASSESSMENT:")
    print("   Low Risk:")
    print("   ├── No duplicate emails will be inserted")
    print("   ├── Existing data integrity preserved")
    print("   ├── Clear tagging for future segmentation")
    print("   └── Reversible via data_source filtering")
    
    print("\n8. POST-MIGRATION VALIDATION:")
    print("   Verification Queries:")
    print("   ├── SELECT COUNT(*) FROM leads WHERE data_source = 'leads_export_legacy';")
    print("   ├── SELECT lead_source, COUNT(*) FROM leads WHERE data_vintage = 'legacy_facebook' GROUP BY lead_source;")
    print("   └── SELECT COUNT(DISTINCT email) FROM leads;")
    
    print("\n9. NEXT STEPS:")
    print("   1. Review and approve migration plan")
    print("   2. Run leads_export.csv migration script")
    print("   3. Validate data integrity post-migration")
    print("   4. Archive master_list.csv (not migrated)")
    print("   5. Update database statistics and documentation")
    
    print("\n" + "=" * 50)
    print("END OF MIGRATION PLAN")
    print("Ready for implementation approval")

if __name__ == "__main__":
    generate_migration_plan()