#!/usr/bin/env python3
"""
Temporary script to parse L1 April 5&6 workshop data from US Leads.xlsx
This script handles the specific format and quirks of this sheet
"""

import pandas as pd
import sqlite3
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class L1AprilParser:
    def __init__(self):
        self.assets_dir = project_root / "assets"
        self.db_path = project_root / "dashboard" / "database" / "leads.db"
        self.file_path = self.assets_dir / "US Leads.xlsx"
        self.sheet_name = "L1 April 5&6"
        
        print(f"🎯 L1 April 5&6 Parser initialized")
        print(f"📁 File: {self.file_path}")
        print(f"🗄️  Database: {self.db_path}")
    
    def analyze_sheet_structure(self):
        """Analyze the specific structure of L1 April 5&6 sheet"""
        print(f"\n🔍 Analyzing {self.sheet_name} structure...")
        
        df = pd.read_excel(self.file_path, sheet_name=self.sheet_name)
        
        print(f"📊 Total rows: {len(df)}")
        print(f"📊 Columns: {list(df.columns)}")
        print(f"\n🔍 First few rows (raw data):")
        print(df.head(10).to_string())
        
        # Check for empty rows and data patterns
        print(f"\n📋 Data quality analysis:")
        print(f"   • Empty rows: {df.isnull().all(axis=1).sum()}")
        print(f"   • Rows with email: {df['Email'].notna().sum()}")
        print(f"   • Rows with names: {df['Name'].notna().sum()}")
        print(f"   • Rows with payment dates: {df['Payment Date'].notna().sum()}")
        print(f"   • Unique payment methods: {df['Payment Method'].unique()}")
        
        return df
    
    def clean_and_parse_data(self, df):
        """Clean and parse the data for database insertion"""
        print(f"\n🧹 Cleaning data...")
        
        # Remove completely empty rows
        df = df.dropna(how='all')
        
        # Remove header row (usually first row is empty/headers)
        df = df[df['Name'].notna() & (df['Name'] != 'Name')]
        
        print(f"📊 After cleaning: {len(df)} valid rows")
        
        parsed_records = []
        
        for idx, row in df.iterrows():
            try:
                # Extract basic info
                name = str(row['Name']).strip() if pd.notna(row['Name']) else None
                email = str(row['Email']).strip().lower() if pd.notna(row['Email']) else None
                phone = str(row['Phone']).strip() if pd.notna(row['Phone']) else None
                
                # Skip if no name or email
                if not name or not email or email == 'nan':
                    print(f"   ⏭️  Skipping row {idx}: Missing name/email")
                    continue
                
                # Parse payment info
                payment_amount = None
                if pd.notna(row['Amount']):
                    try:
                        payment_amount = float(row['Amount'])
                    except:
                        payment_amount = None
                
                payment_method = str(row['Payment Method']).strip() if pd.notna(row['Payment Method']) else None
                
                # Parse dates
                lead_date = None
                payment_date = None
                enrollment_date = None
                
                if pd.notna(row['Lead Date']):
                    try:
                        lead_date = pd.to_datetime(row['Lead Date']).isoformat()
                    except:
                        pass
                
                if pd.notna(row['Payment Date']):
                    try:
                        payment_date = pd.to_datetime(row['Payment Date']).isoformat()
                        enrollment_date = payment_date  # Assume enrollment when payment made
                    except:
                        pass
                
                # Determine payment status
                payment_status = 'Unknown'
                if payment_amount and payment_amount > 0:
                    if payment_date:
                        payment_status = 'Paid'
                    else:
                        payment_status = 'Pending'
                
                # Extract notes from unnamed columns
                notes_parts = []
                for col in ['Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9']:
                    if col in row and pd.notna(row[col]):
                        notes_parts.append(str(row[col]).strip())
                
                notes = ' | '.join(notes_parts) if notes_parts else None
                
                record = {
                    'full_name': name,
                    'email': email,
                    'phone': phone,
                    'workshop_type': 'L1',  # Essentials
                    'payment_status': payment_status,
                    'payment_amount': payment_amount,
                    'lead_source': 'excel_import',
                    'data_source': f'US Leads.xlsx - {self.sheet_name}',
                    'created_time': lead_date or datetime.now().isoformat(),
                    'enrolled_date': enrollment_date,
                    'payment_date': payment_date,
                    'notes': f"L1 April 5&6 2025 workshop. Payment via {payment_method}. {notes}" if notes else f"L1 April 5&6 2025 workshop. Payment via {payment_method}." if payment_method else "L1 April 5&6 2025 workshop",
                    'location': 'US'
                }
                
                parsed_records.append(record)
                print(f"   ✅ Parsed: {name} ({email}) - ${payment_amount or 0}")
                
            except Exception as e:
                print(f"   ❌ Error parsing row {idx}: {e}")
                continue
        
        print(f"\n📋 Successfully parsed {len(parsed_records)} records")
        return parsed_records
    
    def insert_to_database(self, records):
        """Insert parsed records into the database"""
        print(f"\n💾 Inserting {len(records)} records to database...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        updated_count = 0
        skipped_count = 0
        
        for record in records:
            try:
                # Check if lead already exists by email
                cursor.execute("SELECT id, workshop_type, payment_status FROM leads WHERE email = ?", (record['email'],))
                existing = cursor.fetchone()
                
                if existing:
                    existing_id, existing_workshop, existing_status = existing
                    
                    # Update if this has better/more recent data
                    if record['payment_status'] == 'Paid' and existing_status != 'Paid':
                        update_sql = """
                            UPDATE leads SET 
                                workshop_type = ?, payment_status = ?, payment_amount = ?,
                                enrolled_date = ?, payment_date = ?,
                                notes = ?, location = ?
                            WHERE email = ?
                        """
                        cursor.execute(update_sql, (
                            record['workshop_type'], record['payment_status'], record['payment_amount'],
                            record['enrolled_date'], record['payment_date'],
                            record['notes'], record['location'], record['email']
                        ))
                        updated_count += 1
                        print(f"   🔄 Updated: {record['full_name']} (better payment info)")
                    else:
                        skipped_count += 1
                        print(f"   ⏭️  Skipped: {record['full_name']} (already exists)")
                else:
                    # Insert new lead
                    insert_sql = """
                        INSERT INTO leads (
                            full_name, email, phone, workshop_type, payment_status, payment_amount,
                            lead_source, data_source, created_time, enrolled_date,
                            payment_date, notes, location
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    cursor.execute(insert_sql, (
                        record['full_name'], record['email'], record['phone'], record['workshop_type'],
                        record['payment_status'], record['payment_amount'],
                        record['lead_source'], record['data_source'], record['created_time'],
                        record['enrolled_date'], record['payment_date'], record['notes'], record['location']
                    ))
                    inserted_count += 1
                    print(f"   ✅ Inserted: {record['full_name']}")
                    
            except Exception as e:
                print(f"   ❌ Error inserting {record['full_name']}: {e}")
                skipped_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 Database operation complete!")
        print(f"   ✅ Inserted: {inserted_count} new leads")
        print(f"   🔄 Updated: {updated_count} existing leads")
        print(f"   ⏭️  Skipped: {skipped_count} leads")
        
        return {
            'inserted': inserted_count,
            'updated': updated_count,
            'skipped': skipped_count
        }

def main():
    print("=" * 60)
    print("🎓 L1 APRIL 5&6 WORKSHOP DATA PARSER")
    print("=" * 60)
    
    parser = L1AprilParser()
    
    # Step 1: Analyze sheet structure
    df = parser.analyze_sheet_structure()
    
    # Step 2: Clean and parse data
    records = parser.clean_and_parse_data(df)
    
    if not records:
        print("❌ No valid records found to insert")
        return
    
    # Step 3: Preview some records
    print(f"\n👀 Preview of parsed records:")
    for i, record in enumerate(records[:3]):
        print(f"   {i+1}. {record['full_name']} ({record['email']}) - {record['payment_status']} ${record['payment_amount'] or 0}")
    
    # Step 4: Insert to database
    print(f"\n🚀 Proceeding to insert {len(records)} records to database...")
    result = parser.insert_to_database(records)
    print(f"\n✨ Success! Check the dashboard to verify the data.")

if __name__ == "__main__":
    main()