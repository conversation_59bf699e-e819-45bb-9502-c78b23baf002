#!/usr/bin/env python3
"""
SDR Configuration Utility
Loads SDR (Sales Development Representative) configurations from the database
instead of hardcoding them in various files.
"""

import sqlite3
from pathlib import Path
from typing import Dict, Optional

class SDRConfig:
    """Utility class to load SDR configurations from database"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            # Default database path
            self.db_path = str(Path(__file__).parent.parent.parent / "data" / "database" / "leads.db")
        else:
            self.db_path = db_path
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict]:
        """Get user information by ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT id, username, email, first_name, last_name, role 
                    FROM users 
                    WHERE id = ? AND is_active = 1
                """, (user_id,))
                
                row = cursor.fetchone()
                if row:
                    return dict(row)
                return None
                
        except Exception as e:
            print(f"Error fetching user by ID {user_id}: {e}")
            return None
    
    def get_user_by_email(self, email: str) -> Optional[Dict]:
        """Get user information by email"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT id, username, email, first_name, last_name, role 
                    FROM users 
                    WHERE email = ? AND is_active = 1
                """, (email,))
                
                row = cursor.fetchone()
                if row:
                    return dict(row)
                return None
                
        except Exception as e:
            print(f"Error fetching user by email {email}: {e}")
            return None
    
    def get_all_sales_users(self) -> Dict[int, Dict]:
        """Get all active sales users"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT id, username, email, first_name, last_name, role 
                    FROM users 
                    WHERE role IN ('sales', 'sales_rep', 'admin') AND is_active = 1
                    ORDER BY id
                """)
                
                users = {}
                for row in cursor.fetchall():
                    user_data = dict(row)
                    users[user_data['id']] = user_data
                
                return users
                
        except Exception as e:
            print(f"Error fetching sales users: {e}")
            return {}
    
    def get_assignment_email_map(self) -> Dict[int, str]:
        """Get mapping of user IDs to email addresses for lead assignment"""
        try:
            users = self.get_all_sales_users()
            email_map = {}
            
            for user_id, user_data in users.items():
                email_map[user_id] = user_data['email']
            
            # Add default fallback (first sales user or manish.tulasi)
            if users:
                default_email = None
                # Try to find manish.tulasi first
                for user_data in users.values():
                    if 'manish' in user_data['email'].lower():
                        default_email = user_data['email']
                        break
                
                # If not found, use first sales user
                if not default_email:
                    default_email = list(users.values())[0]['email']
                
                email_map[None] = default_email
            
            return email_map
            
        except Exception as e:
            print(f"Error getting assignment email map: {e}")
            return {}
    
    def get_sdr_configs(self) -> Dict[str, Dict]:
        """Get SDR configurations for email templates"""
        try:
            users = self.get_all_sales_users()
            sdr_configs = {}
            
            for user_data in users.values():
                username = user_data['username']
                sdr_configs[username] = {
                    'email': user_data['email'],
                    'name': user_data['first_name'],
                    'title': self._get_user_title(user_data['role'], user_data['first_name'])
                }
            
            return sdr_configs
            
        except Exception as e:
            print(f"Error getting SDR configs: {e}")
            return {}
    
    def _get_user_title(self, role: str, first_name: str) -> str:
        """Get appropriate title based on role and name"""
        role_titles = {
            'admin': 'CEO',
            'sales': 'Business Development Manager',
            'sales_rep': 'Sales Representative'
        }
        
        # Special cases
        if first_name and first_name.lower() == 'mahalakshmi':
            return 'Head of Sales'
        
        return role_titles.get(role, 'Sales Representative')

# Convenience functions for backward compatibility
def get_default_sdr_email(db_path: str = None) -> str:
    """Get the default SDR email address"""
    config = SDRConfig(db_path)
    email_map = config.get_assignment_email_map()
    return email_map.get(None, "<EMAIL>")

def get_assignment_email_map(db_path: str = None) -> Dict[int, str]:
    """Get assignment to email mapping"""
    config = SDRConfig(db_path)
    return config.get_assignment_email_map()

def get_sdr_configs(db_path: str = None) -> Dict[str, Dict]:
    """Get all SDR configurations"""
    config = SDRConfig(db_path)
    return config.get_sdr_configs()

if __name__ == "__main__":
    # Test the utility
    config = SDRConfig()
    
    print("=== All Sales Users ===")
    users = config.get_all_sales_users()
    for user_id, user_data in users.items():
        print(f"ID: {user_id}, Name: {user_data['first_name']} {user_data['last_name']}, Email: {user_data['email']}")
    
    print("\n=== Assignment Email Map ===")
    email_map = config.get_assignment_email_map()
    for user_id, email in email_map.items():
        print(f"User ID {user_id}: {email}")
    
    print("\n=== SDR Configs ===")
    sdr_configs = config.get_sdr_configs()
    for username, config_data in sdr_configs.items():
        print(f"{username}: {config_data}")