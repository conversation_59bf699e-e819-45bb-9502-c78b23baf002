import gspread
import requests
import re
import os
import time
from datetime import datetime
from oauth2client.service_account import ServiceAccountCredentials
from dotenv import load_dotenv
import logging
import builtins
from legacy.phone_validation import is_valid_phone_number, clean_phone_number

load_dotenv()

# --- Logging Setup ---
# Logs will be written both to the console (stdout) and to a file named
# `send_whatsapp.log` in the same directory.  DEBUG level will capture
# all detailed traces, including the original `print` statements.
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s | %(levelname)s | %(message)s",
    handlers=[
        logging.FileHandler("/Users/<USER>/Code/modernaipro/mai-administrative/new/data/logs/send_whatsapp.log", mode="a", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- Redirect built-in print to logging for unified output ---
_orig_print = builtins.print

def _print_to_log(*args, **kwargs):
    """Intercepts all `print()` calls and duplicates them to the logger."""
    message = " ".join(str(a) for a in args)
    logger.debug(message)
    _orig_print(*args, **kwargs)

# Monkey-patch the global print function so every call is also logged.
builtins.print = _print_to_log

# --- Configuration ---
API_KEY = os.getenv('BREVO_API_KEY')
TEMPLATE_ID = int(os.getenv('BREVO_TEMPLATE_ID', 7)) # Default to 7 if not set
SENDER_NUMBER = os.getenv('BREVO_SENDER_NUMBER')
GOOGLE_SHEET_NAME = os.getenv('GOOGLE_SHEET_NAME')

# --- Google Sheets Setup ---
SCOPE = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
CREDS_FILE = 'credentials.json'

# --- Column Mapping (1-based index) ---
# Previous Mappings (commented out for reference)
# PHONE_COL = 8; FIRST_NAME_COL = 5; JOB_TITLE_COL = 2; STATUS_COL = 9; STATUS_DETAILS_COL = 10; LAST_UPDATED_COL = 11; LAST_ROW_CELL_REF = 'L1'

# New column mappings based on updated spreadsheet structure
FIRST_NAME_COL = 6      # Column F (full_name)
PHONE_COL = 7           # Column G (phone_number)
STATUS_COL = 8          # Column H (status)
MESSAGE_ID_COL = 9      # Column I (message_id)
STATUS_TIMESTAMP_COL = 10 # Column J (status_timestamp)
ERROR_REASON_COL = 11   # Column K (error_reason)
LAST_ROW_CELL_REF = 'N2' # Column B (last_processed_row)

# Email is now in column 5
EMAIL_COL = 5

# We'll use the campaign_name to determine if we should send a message
CAMPAIGN_NAME_COL = 1   # Column A (campaign_name)

# List of sheet names to process
SHEET_NAMES_TO_PROCESS = ["Without Price"]

import sys

# Add WhatsApp-Selenium to path
current_dir = os.path.dirname(os.path.abspath(__file__))
whatsapp_selenium_dir = os.path.join(current_dir, 'WhatsApp-Selenium')
sys.path.append(whatsapp_selenium_dir)

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service as ChromeService
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.keys import Keys
except ImportError:
    print("❌ Error: Could not import required Selenium libraries.")
    Whatsapp = None

# Define chrome path as a constant
CHROME_BINARY_PATH = "/usr/bin/chromium-browser"
SELENIUM_USER_DATA_DIR = os.path.join(current_dir, "selenium_user_data")

def send_whatsapp_selenium(phone, message):
    """Sends a WhatsApp message using a direct Selenium implementation with webdriver-manager."""

    print(f"ℹ️ Attempting to send message to {phone} using Selenium fallback...")
    try:
        options = webdriver.ChromeOptions()
        options.binary_location = CHROME_BINARY_PATH
        options.add_argument(f"user-data-dir={SELENIUM_USER_DATA_DIR}")
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36")

        service = ChromeService(ChromeDriverManager().install())
        browser = webdriver.Chrome(service=service, options=options)

        print("Navigating to WhatsApp Web...")
        browser.get("https://web.whatsapp.com")
        
        # Wait for login to complete using the persistent session
        WebDriverWait(browser, 60).until(
            EC.presence_of_element_located((By.XPATH, "//div[@id='side']"))
        )
        print("Login successful.")

        # Open chat directly
        chat_url = f"https://web.whatsapp.com/send?phone={phone}&text={message.replace(' ', '%20')}"
        browser.get(chat_url)

        # Wait for the send button to be clickable and click it
        send_button_xpath = "//button[@aria-label='Send']"
        WebDriverWait(browser, 30).until(
            EC.element_to_be_clickable((By.XPATH, send_button_xpath))
        )
        browser.find_element(By.XPATH, send_button_xpath).click()
        
        print(f"✅ Successfully sent message to {phone} via Selenium.")
        time.sleep(5) # Wait a moment for message to be sent
        browser.quit()
        return "SELENIUM_SENT"
    except Exception as e:
        print(f"❌ Failed to send message via Selenium to {phone}: {e}")
        if 'browser' in locals() and browser:
            browser.quit()
        return "FALLBACK_FAILED"

def get_google_sheet_client():
    """Authenticates with Google Sheets API and returns a client object."""
    try:
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDS_FILE, SCOPE)
        client = gspread.authorize(creds)
        return client
    except FileNotFoundError:
        print(f"❌ Error: The credentials file '{CREDS_FILE}' was not found.")
        print("Please follow the setup instructions in README.md to create it.")
        return None
    except Exception as e:
        print(f"❌ An error occurred during Google Sheets authentication: {e}")
        return None

def send_whatsapp_message(first_name, phone):
    """Sends a WhatsApp message using the Brevo API with Selenium fallback."""
    url = "https://api.brevo.com/v3/whatsapp/sendMessage"
    payload = {
        "templateId": TEMPLATE_ID,
        "senderNumber": SENDER_NUMBER,
        "contactNumbers": [phone],  # Updated to array as per API docs
        "params": {
            "FIRSTNAME": first_name
        }
    }
    headers = {
        "api-key": API_KEY,
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        response.raise_for_status()  # Raises an HTTPError for bad responses (4xx or 5xx)
        response_data = response.json()
        message_id = response_data.get("messageId")
        print(f"✅ Successfully sent message to {phone}. Message ID: {message_id}")
        return ("SENT", message_id)
    except (requests.exceptions.HTTPError, requests.exceptions.RequestException, Exception) as err:
        error_message = str(err)
        response_text = ""
        # Safely access response text
        if hasattr(err, 'response') and err.response is not None:
            response_text = err.response.text
            try:
                # Try to get more specific error from Brevo JSON response
                error_details = err.response.json()
                error_message = error_details.get('message', error_message)
            except ValueError:
                # Keep original error message if response is not JSON
                pass

        print(f"❌ Brevo API failed for {phone}. Error: {err}. Response: {response_text}")
        print("🔁 Initiating Selenium fallback...")
        
        # Construct the message text for Selenium. This should ideally match the Brevo template.
        # Since we no longer use job_title, the generic fallback message is simplified.
        message_text = f"Hello {first_name}, this is a follow-up message. Please let us know if you are interested."
        
        # The fallback will be handled in process_leads if needed.
        return ("FAILED", error_message)

def process_leads():
    """
    Main function to process leads from Google Sheets.
    - Reads the last processed row from cell K1.
    - Fetches new rows.
    - Sends WhatsApp messages assuming phone numbers are pre-validated.
    - Updates the status and last processed row in the sheet.
    """
    print("DEBUG: process_leads() called")

    # --- Environment Variable Check ---
    if not all([API_KEY, TEMPLATE_ID, SENDER_NUMBER, GOOGLE_SHEET_NAME]):
        print("❌ Error: One or more environment variables are missing.")
        print("Please check your .env file for BREVO_API_KEY, BREVO_TEMPLATE_ID, BREVO_SENDER_NUMBER, GOOGLE_SHEET_NAME.")
        return
    print("DEBUG: Environment variables check passed.")

    # --- Google Sheet Connection ---
    client = get_google_sheet_client()
    if not client:
        return
    print(f"DEBUG: Google Sheet client: {client}")

    for sheet_name in SHEET_NAMES_TO_PROCESS:
        try:
            print(f"DEBUG: Attempting to open sheet: '{sheet_name}'...")
            worksheet = client.open(GOOGLE_SHEET_NAME).worksheet(sheet_name)
            print(f"DEBUG: Successfully opened sheet: '{sheet_name}'")
        except gspread.exceptions.SpreadsheetNotFound:
            print(f"❌ Error: Spreadsheet '{GOOGLE_SHEET_NAME}' not found.")
            print("Please ensure the name is correct and the service account has access.")
            return
        except Exception as e:
            print(f"❌ An error occurred opening the sheet: {e}")
            return

        # --- Read Last Processed Row ---
        try:
            last_processed_row_val = worksheet.acell(LAST_ROW_CELL_REF).value
            last_processed_row = int(last_processed_row_val) if last_processed_row_val and last_processed_row_val.isdigit() else 6
            # Ensure we never process rows before row 6
            last_processed_row = max(last_processed_row, 6)
            print(f"✅ Last processed row from {LAST_ROW_CELL_REF} is {last_processed_row_val}, starting from row {last_processed_row}")
        except Exception as e:
            print(f"⚠️ Warning: Could not read last processed row from {LAST_ROW_CELL_REF}. Defaulting to row 6. Error: {e}")
            last_processed_row = 6

        # --- Fetch and Process Data ---
        try:
            print(f"✅ Fetching data from sheet '{sheet_name}' starting at row {last_processed_row}...")
            # Fetch from the starting row to the last column we care about (K for error_reason)
            # and all the way to the last row in the sheet.
            data = worksheet.get(f'A{last_processed_row}:K{worksheet.row_count}')
            if not data:
                print(f"ℹ️ No new rows to process for sheet '{sheet_name}'.")
                continue
            print(f"✅ Successfully fetched {len(data)} new rows to process.")
        except Exception as e:
            print(f"❌ An error occurred fetching data from the sheet: {e}")
            return

        new_last_processed_row_for_sheet = last_processed_row
        updates = [] # To hold all cell updates for batching

        for i, row in enumerate(data, start=last_processed_row):
            # Only require enough columns for phone and first name to process the row
            # STATUS_COL check will happen later only if there are enough columns
            if len(row) < max(PHONE_COL, FIRST_NAME_COL):
                print(f"Skipping row {i} in sheet '{sheet_name}': not enough columns for essential data. Expected at least {max(PHONE_COL, FIRST_NAME_COL)}, got {len(row)}.") 
                if i >= new_last_processed_row_for_sheet:
                    new_last_processed_row_for_sheet = i + 1
                continue

            phone = row[PHONE_COL - 1] if PHONE_COL <= len(row) else ""
            first_name = row[FIRST_NAME_COL - 1] if FIRST_NAME_COL <= len(row) else ""
            email = row[EMAIL_COL - 1] if EMAIL_COL <= len(row) else ""
            campaign_name = row[CAMPAIGN_NAME_COL - 1] if CAMPAIGN_NAME_COL <= len(row) else ""
            
            # Only check status if the row has enough columns
            status = row[STATUS_COL - 1] if STATUS_COL <= len(row) else ""
            
            # Check if we should resend because there was an error in status column
            resend_due_to_error = False
            if status and len(status) >= 5 and status[:5].lower() == 'error':
                resend_due_to_error = True
                print(f"Will resend to row {i} because status is '{status}'.") 
            
            # Check if we have a valid row with phone number and campaign name
            # Skip empty rows or rows without valid phone numbers in column G
            if not phone or not campaign_name:
                print(f"Skipping row {i} in sheet '{sheet_name}': Phone is empty ('{phone}') or campaign_name is empty ('{campaign_name}').")
                if i >= new_last_processed_row_for_sheet:
                    new_last_processed_row_for_sheet = i + 1
                continue
                
            # Skip rows that have already been sent successfully
            if (STATUS_COL <= len(row) and status == 'SENT') and not resend_due_to_error:
                print(f"Skipping row {i} in sheet '{sheet_name}': Status is already 'SENT' ('{status}') and no error.")
                if i >= new_last_processed_row_for_sheet:
                    new_last_processed_row_for_sheet = i + 1
                continue

            # Check if the phone number is valid
            if not is_valid_phone_number(phone):
                print(f"Skipping row {i} in sheet '{sheet_name}': Phone number '{phone}' is not valid.")
                updates.append(gspread.Cell(i, STATUS_COL, "INVALID_PHONE"))
                updates.append(gspread.Cell(i, ERROR_REASON_COL, "Phone number is not valid"))
                if i >= new_last_processed_row_for_sheet:
                    new_last_processed_row_for_sheet = i + 1
                continue
                
            # Clean and standardize the phone number (adds country code if needed)
            cleaned_phone = clean_phone_number(phone)
            print(f"Processing row {i} in sheet '{sheet_name}': Using phone '{cleaned_phone}' for original '{phone}'.")

            # Send message and update status
            if phone and status.upper() != 'SENT': # Process only if not already sent
                print(f"Processing row {i}: Name - {first_name}, Phone - {phone}")
                status_result, data = send_whatsapp_message(first_name, cleaned_phone)

                # Get current timestamp for status update
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # Prepare cell updates for batch operation
                updates.append(gspread.Cell(i, STATUS_COL, status_result))
                updates.append(gspread.Cell(i, STATUS_TIMESTAMP_COL, current_time))
                
                # If sent successfully, data is the messageId
                if status_result == "SENT":
                    updates.append(gspread.Cell(i, MESSAGE_ID_COL, data))
                else:
                    # If failed, update error_reason column
                    updates.append(gspread.Cell(i, ERROR_REASON_COL, data))
                
                print(f"Queued update for row {i}: Status='{status_result}', Time='{current_time}')")
            else:
                print(f"Skipping row {i}: Phone is missing or status is already 'SENT'.")

            # Pause to avoid hitting API rate limits
            time.sleep(1) 

        # --- Batch Update Google Sheet ---
        if updates:
            try:
                print(f"Sending {len(updates)} cell updates to the sheet...")
                worksheet.update_cells(updates)
                print("✅ Batch update successful.")
            except Exception as e:
                print(f"❌ An error occurred during batch update: {e}")

        # --- Update Last Processed Row in K1 ---
        if new_last_processed_row_for_sheet > last_processed_row:
            try:
                worksheet.update_acell(LAST_ROW_CELL_REF, new_last_processed_row_for_sheet)
                print(f"✅ Successfully updated {LAST_ROW_CELL_REF} to {new_last_processed_row_for_sheet} for sheet '{sheet_name}'.")
            except Exception as e:
                print(f"❌ Failed to update {LAST_ROW_CELL_REF} for sheet '{sheet_name}'. Error: {e}")
        else:
            print(f"DEBUG: No new rows effectively processed for sheet '{sheet_name}' to warrant K1 update. K1 value ({last_processed_row}), new_last_processed_row_for_sheet ({new_last_processed_row_for_sheet}).")

        # After processing leads, fetch and update event statuses
        update_event_statuses(worksheet)

    print("\n All sheets processed.")

def update_activity(days=30):
    """
    Fetches WhatsApp message activity from the Brevo API using the event-report endpoint
    and updates the status column with the event status (sent, delivered, read, error).
    For error events, it includes the reason in the error_reason column.
    Also updates the status_timestamp column with the date of the status update.
    
    Args:
        days (int): Number of days of data to fetch (default 30, max 90)
    """
    print("\n--- Starting WhatsApp Activity Update Process ---")
    
    # --- Environment Variable Check ---
    if not API_KEY:
        print("❌ Error: BREVO_API_KEY environment variable is missing.")
        print("Please check your .env file.")
        return
    
    # --- Google Sheet Connection ---
    client = get_google_sheet_client()
    if not client:
        return
    
    for sheet_name in SHEET_NAMES_TO_PROCESS:
        try:
            print(f"DEBUG: Attempting to open sheet: '{sheet_name}'...")
            worksheet = client.open(GOOGLE_SHEET_NAME).worksheet(sheet_name)
            print(f"DEBUG: Successfully opened sheet: '{sheet_name}'")
            
            # Get all the data from the sheet
            print(f"Fetching all data from sheet '{sheet_name}'...")
            all_sheet_data = worksheet.get_all_values()
            
            # Create a map of {contactNumber: row_number}
            # Assumes header row is present, so sheet data is 1-indexed for rows
            contact_number_to_row = {}
            for i, row in enumerate(all_sheet_data):
                if i == 0:  # Skip header row
                    continue
                    
                if len(row) >= PHONE_COL:
                    phone = row[PHONE_COL - 1]
                    # Only process rows with valid phone numbers
                    if phone and is_valid_phone_number(phone):
                        # Clean and standardize the phone number (adds country code if needed)
                        cleaned_phone = clean_phone_number(phone)
                        contact_number_to_row[cleaned_phone] = i + 1  # +1 because sheet is 1-indexed
            
            print(f"Found {len(contact_number_to_row)} contact numbers in the sheet.")
            
            # Fetch event reports from Brevo API
            headers = {
                'accept': 'application/json',
                'api-key': API_KEY
            }
            
            # Set up query parameters for date range (max 90 days)
            params = {}
            days = min(days, 90)  # Ensure we don't exceed API limit of 90 days
            if days > 0:
                params['days'] = days
                
            url = "https://api.sendinblue.com/v3/whatsapp/statistics/events"
            
            try:
                print(f"Fetching event reports from Brevo API for the last {days} days...")
                response = requests.get(url, headers=headers, params=params, timeout=60)
                response.raise_for_status()
                events_data = response.json().get('events', [])
                print(f"✅ Successfully fetched {len(events_data)} events.")

                if not events_data:
                    print("No events to process.")
                    continue
                
                # Process events and determine the latest status for each contact number
                # Create a dictionary to store events with their timestamps for each contact
                contact_events = {}
                
                print(f"Processing {len(events_data)} events to find latest status for each contact...")
                
                for event in events_data:
                    contact_number = event.get('contactNumber')
                    # Remove any '+' prefix if present
                    if contact_number and contact_number.startswith('+'):
                        contact_number = contact_number[1:]
                    
                    # Skip if we don't have this contact in our sheet
                    if contact_number not in contact_number_to_row:
                        continue
                    
                    event_type = event.get('event')
                    event_date = event.get('date')
                    
                    # Parse the date string to a datetime object for comparison
                    try:
                        # Assuming format is ISO 8601 (e.g., '2025-07-10T10:30:45Z')
                        event_datetime = datetime.strptime(event_date, '%Y-%m-%dT%H:%M:%SZ')
                    except (ValueError, TypeError):
                        # If parsing fails, use a very old date to ensure this event is not prioritized
                        print(f"Warning: Could not parse date '{event_date}' for contact {contact_number}")
                        event_datetime = datetime.min
                    
                    # Format the status value based on event type
                    if event_type == 'error':
                        status_value = f"ERROR: {event.get('reason', 'Unknown reason')}"
                    else:
                        # Capitalize the event type for better readability
                        status_value = event_type.upper()
                    
                    # Store this event with its parsed datetime
                    if contact_number not in contact_events:
                        contact_events[contact_number] = []
                    
                    contact_events[contact_number].append({
                        'status': status_value,
                        'datetime': event_datetime,
                        'type': event_type,
                        'original_date': event_date
                    })
                
                # Now determine the latest status for each contact
                latest_statuses = {}
                for contact_number, events in contact_events.items():
                    # Sort events by datetime (newest first)
                    sorted_events = sorted(events, key=lambda x: x['datetime'], reverse=True)
                    
                    # Check if any event is a 'reply' which is the most ideal state
                    reply_events = [e for e in sorted_events if e['type'] == 'reply']
                    if reply_events:
                        # Use the most recent 'reply' event
                        latest_event = reply_events[0]
                        latest_statuses[contact_number] = (latest_event['status'], latest_event['original_date'])
                        print(f"Found REPLY event for {contact_number} from {latest_event['original_date']}")
                        continue
                    
                    # If no 'reply' events, check for 'read' events which are next best
                    read_events = [e for e in sorted_events if e['type'] == 'read']
                    if read_events:
                        # Use the most recent 'read' event
                        latest_event = read_events[0]
                        latest_statuses[contact_number] = (latest_event['status'], latest_event['original_date'])
                        continue
                    
                    # Otherwise, just use the most recent event of any type
                    if sorted_events:
                        latest_event = sorted_events[0]  # Most recent event (already sorted)
                        latest_statuses[contact_number] = (latest_event['status'], latest_event['original_date'])
                
                print(f"Determined latest status for {len(latest_statuses)} contacts.")
                
                # Prepare batch updates for the sheet
                updates = []
                for contact_number, (status, date) in latest_statuses.items():
                    if contact_number in contact_number_to_row:
                        row_num = contact_number_to_row[contact_number]
                        # Update status column
                        updates.append(gspread.Cell(row_num, STATUS_COL, status))
                        
                        # Update status timestamp column
                        formatted_date = date  # Default to original format
                        try:
                            # Convert to a more readable format
                            dt = datetime.strptime(date, '%Y-%m-%dT%H:%M:%SZ')
                            formatted_date = dt.strftime('%Y-%m-%d %H:%M:%S')
                        except (ValueError, TypeError):
                            # Keep original format if parsing fails
                            pass
                        
                        updates.append(gspread.Cell(row_num, STATUS_TIMESTAMP_COL, formatted_date))
                        
                        # If status contains ERROR, update error_reason column
                        if 'ERROR' in status:
                            error_reason = status.replace('ERROR: ', '')
                            updates.append(gspread.Cell(row_num, ERROR_REASON_COL, error_reason))
                
                # Perform the batch update
                if updates:
                    print(f"Sending {len(updates)} activity status updates to the sheet...")
                    worksheet.update_cells(updates)
                    print("✅ Activity status batch update successful.")
                else:
                    print("No matching contact numbers found to update.")
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ Error fetching event reports from Brevo API: {e}")
            except Exception as e:
                print(f"❌ An unexpected error occurred during activity update: {e}")
                
        except Exception as e:
            print(f"❌ An error occurred opening or processing sheet '{sheet_name}': {e}")
    
    print("\n All sheets processed for activity updates.")

def update_event_statuses(worksheet):
    """
    Fetches the latest WhatsApp message events from the Brevo API and updates
    the Google Sheet with the most recent status for each message using the new column structure.
    Updates the status, status_timestamp, and error_reason columns.
    """
    print("\n--- Starting Event Status Update Process ---")
    headers = {
        'accept': 'application/json',
        'api-key': API_KEY
    }
    url = "https://api.brevo.com/v3/whatsapp/statistics/events"

    try:
        print("Fetching event reports from Brevo API...")
        response = requests.get(url, headers=headers, timeout=60)
        response.raise_for_status()
        events_data = response.json().get('events', [])
        print(f"✅ Successfully fetched {len(events_data)} events.")

        if not events_data:
            print("No new events to process.")
            return

        # 1. Get all message IDs from the sheet to create a map
        print("Fetching message IDs from the sheet to create a mapping...")
        all_sheet_data = worksheet.get_all_values()
        # Create a map of {messageId: row_number}
        # Assumes header row is present, so sheet data is 1-indexed for rows
        message_id_to_row = {
            row[MESSAGE_ID_COL - 1]: i + 1 
            for i, row in enumerate(all_sheet_data) 
            if len(row) >= MESSAGE_ID_COL and row[MESSAGE_ID_COL - 1]
        }
        print(f"Found {len(message_id_to_row)} message IDs in the sheet.")

        # 2. Process events and determine the latest status for each message ID
        # The API returns events newest first, so the first event we see for a 
        # messageId is its latest status.
        latest_statuses = {}
        for event in events_data:
            msg_id = event.get('messageId')
            if msg_id in message_id_to_row and msg_id not in latest_statuses:
                latest_statuses[msg_id] = event.get('event')
        
        print(f"Determined latest status for {len(latest_statuses)} messages.")

        # 3. Prepare batch updates for the sheet
        updates = []
        for msg_id, status in latest_statuses.items():
            row_num = message_id_to_row[msg_id]
            # Update status column
            status_value = status.upper()  # Capitalize for better readability
            updates.append(gspread.Cell(row_num, STATUS_COL, status_value))
            
            # Update status timestamp column with current time
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            updates.append(gspread.Cell(row_num, STATUS_TIMESTAMP_COL, current_time))
            
            # If status is error, update error_reason column
            if status.lower() == 'error':
                updates.append(gspread.Cell(row_num, ERROR_REASON_COL, "Error detected in event report"))

        # 4. Perform the batch update
        if updates:
            print(f"Sending {len(updates)} event status updates to the sheet...")
            worksheet.update_cells(updates)
            print("✅ Event status batch update successful.")
        else:
            print("No matching message IDs found to update.")

    except requests.exceptions.RequestException as e:
        print(f"❌ Error fetching event reports from Brevo API: {e}")
    except Exception as e:
        print(f"❌ An unexpected error occurred during event update: {e}")

def diagnose_all_messages():
    """
    Comprehensive diagnostic function to analyze all message statuses.
    Provides detailed breakdown and insights for the entire WhatsApp messaging pipeline.
    """
    print("🔍 Starting comprehensive diagnostic analysis for all message statuses...")
    
    try:
        # Connect to Google Sheets
        gc = gspread.service_account(filename=CREDS_FILE)
        spreadsheet = gc.open(GOOGLE_SHEET_NAME)
        
        # Get all worksheets
        worksheets = spreadsheet.worksheets()
        
        # Initialize comprehensive tracking
        total_messages = 0
        status_breakdown = {
            'Not Sent': [],
            'ERROR': [],
            'Delivered': [],
            'Read': [],
            'REPLY': [],
            'Soft Bounce': [],
            'INVALID_PHONE': [],
            'Other': []
        }
        
        for worksheet in worksheets:
            sheet_name = worksheet.title
            print(f"\n📋 Analyzing sheet: '{sheet_name}'")
            
            try:
                # Get all data from the sheet
                all_data = worksheet.get_all_values()
                if not all_data or len(all_data) <= 1:
                    print(f"   ℹ️ No data found in sheet '{sheet_name}'")
                    continue
                
                # Skip header row
                data_rows = all_data[1:]
                sheet_messages = 0
                
                for row_idx, row in enumerate(data_rows, start=2):  # Start from row 2 (after header)
                    # Skip rows without essential data
                    if len(row) < max(PHONE_COL, FIRST_NAME_COL):
                        continue
                    
                    phone = row[PHONE_COL - 1] if PHONE_COL <= len(row) else ""
                    first_name = row[FIRST_NAME_COL - 1] if FIRST_NAME_COL <= len(row) else ""
                    campaign_name = row[CAMPAIGN_NAME_COL - 1] if CAMPAIGN_NAME_COL <= len(row) else ""
                    status = row[STATUS_COL - 1] if STATUS_COL <= len(row) else ""
                    error_reason = row[ERROR_REASON_COL - 1] if ERROR_REASON_COL <= len(row) and len(row) > ERROR_REASON_COL - 1 else ""
                    message_id = row[MESSAGE_ID_COL - 1] if MESSAGE_ID_COL <= len(row) and len(row) > MESSAGE_ID_COL - 1 else ""
                    
                    # Skip completely empty rows
                    if not phone and not first_name:
                        continue
                    
                    total_messages += 1
                    sheet_messages += 1
                    
                    # Categorize and analyze each status
                    message_data = {
                        'sheet': sheet_name,
                        'row': row_idx,
                        'phone': phone,
                        'name': first_name,
                        'campaign': campaign_name,
                        'status': status,
                        'error_reason': error_reason,
                        'message_id': message_id,
                        'analysis': {}
                    }
                    
                    # Analyze based on status
                    if not status or status.strip() == "":
                        # Not Sent Analysis
                        if not phone or phone.strip() == "":
                            message_data['analysis'] = {
                                'issue': 'Missing phone number',
                                'severity': 'High',
                                'action': 'Add phone number to process this lead'
                            }
                        elif not campaign_name or campaign_name.strip() == "":
                            message_data['analysis'] = {
                                'issue': 'Missing campaign name',
                                'severity': 'High', 
                                'action': 'Add campaign name to enable message sending'
                            }
                        elif not is_valid_phone_number(phone):
                            message_data['analysis'] = {
                                'issue': 'Invalid phone format',
                                'severity': 'High',
                                'action': f'Fix phone format: {phone}'
                            }
                        else:
                            message_data['analysis'] = {
                                'issue': 'Ready to process',
                                'severity': 'Low',
                                'action': 'Run process command to send message'
                            }
                        status_breakdown['Not Sent'].append(message_data)
                        
                    elif status.upper().startswith('ERROR'):
                        # Error Analysis
                        if 'not found' in error_reason.lower():
                            message_data['analysis'] = {
                                'issue': 'Template or configuration error',
                                'severity': 'High',
                                'action': 'Check Brevo template and API configuration'
                            }
                        elif 'invalid' in error_reason.lower():
                            message_data['analysis'] = {
                                'issue': 'Invalid phone number',
                                'severity': 'Medium',
                                'action': 'Verify and correct phone number format'
                            }
                        else:
                            message_data['analysis'] = {
                                'issue': 'API or system error',
                                'severity': 'Medium',
                                'action': 'Retry sending or check system logs'
                            }
                        status_breakdown['ERROR'].append(message_data)
                        
                    elif status.upper() in ['DELIVERED', 'SENT']:
                        # Delivered Analysis
                        if message_id:
                            message_data['analysis'] = {
                                'issue': 'Successfully delivered',
                                'severity': 'None',
                                'action': 'Monitor for read receipts and replies'
                            }
                        else:
                            message_data['analysis'] = {
                                'issue': 'Delivered but no message ID',
                                'severity': 'Low',
                                'action': 'Message sent but tracking may be incomplete'
                            }
                        status_breakdown['Delivered'].append(message_data)
                        
                    elif status.upper() == 'READ':
                        # Read Analysis
                        message_data['analysis'] = {
                            'issue': 'Message opened by recipient',
                            'severity': 'None',
                            'action': 'High engagement - monitor for replies'
                        }
                        status_breakdown['Read'].append(message_data)
                        
                    elif status.upper() == 'REPLY':
                        # Reply Analysis
                        message_data['analysis'] = {
                            'issue': 'Customer replied - HOT LEAD!',
                            'severity': 'None',
                            'action': 'URGENT: Follow up with sales team immediately'
                        }
                        status_breakdown['REPLY'].append(message_data)
                        
                    elif 'SOFT BOUNCE' in status.upper():
                        # Soft Bounce Analysis
                        message_data['analysis'] = {
                            'issue': 'Temporary delivery issue',
                            'severity': 'Medium',
                            'action': 'Will retry automatically or manually retry later'
                        }
                        status_breakdown['Soft Bounce'].append(message_data)
                        
                    elif 'INVALID_PHONE' in status.upper():
                        # Invalid Phone Analysis
                        message_data['analysis'] = {
                            'issue': 'Phone number validation failed',
                            'severity': 'High',
                            'action': f'Correct phone format: {phone}'
                        }
                        status_breakdown['INVALID_PHONE'].append(message_data)
                        
                    else:
                        # Other statuses
                        message_data['analysis'] = {
                            'issue': f'Unknown status: {status}',
                            'severity': 'Medium',
                            'action': 'Review status and determine appropriate action'
                        }
                        status_breakdown['Other'].append(message_data)
                
                print(f"   📊 Analyzed {sheet_messages} messages")
                    
            except Exception as e:
                print(f"   ❌ Error analyzing sheet '{sheet_name}': {e}")
                continue
        
        # Generate comprehensive report
        print(f"\n" + "="*80)
        print(f"🔍 COMPREHENSIVE DIAGNOSTIC REPORT - ALL MESSAGE STATUSES")
        print(f"="*80)
        print(f"📊 Total messages analyzed: {total_messages}")
        
        if total_messages == 0:
            print("❌ No messages found to analyze!")
            return
        
        # Status overview
        print(f"\n📈 STATUS OVERVIEW:")
        for status_name, messages in status_breakdown.items():
            if messages:
                count = len(messages)
                percentage = round((count / total_messages) * 100, 1)
                
                # Determine status health
                if status_name in ['Read', 'REPLY']:
                    health = "🟢 EXCELLENT"
                elif status_name == 'Delivered':
                    health = "🔵 GOOD"
                elif status_name in ['Not Sent', 'ERROR', 'INVALID_PHONE']:
                    health = "🔴 NEEDS ATTENTION"
                elif status_name == 'Soft Bounce':
                    health = "🟡 MONITOR"
                else:
                    health = "⚪ REVIEW"
                
                print(f"   • {status_name}: {count} messages ({percentage}%) - {health}")
        
        # Detailed analysis for each status
        for status_name, messages in status_breakdown.items():
            if not messages:
                continue
                
            print(f"\n" + "="*60)
            print(f"📋 {status_name.upper()} ANALYSIS ({len(messages)} messages)")
            print(f"="*60)
            
            # Group by issue type
            issue_groups = {}
            for msg in messages:
                issue = msg['analysis']['issue']
                if issue not in issue_groups:
                    issue_groups[issue] = []
                issue_groups[issue].append(msg)
            
            # Show issue breakdown
            print(f"📊 Issue Breakdown:")
            for issue, msgs in sorted(issue_groups.items(), key=lambda x: len(x[1]), reverse=True):
                count = len(msgs)
                percentage = round((count / len(messages)) * 100, 1)
                severity = msgs[0]['analysis']['severity']
                severity_icon = "🔴" if severity == "High" else "🟡" if severity == "Medium" else "🟢" if severity == "Low" else "⚪"
                print(f"   {severity_icon} {issue}: {count} messages ({percentage}%)")
            
            # Show sample messages (first 5 per issue)
            print(f"\n📝 Sample Messages:")
            for issue, msgs in list(issue_groups.items())[:3]:  # Show top 3 issues
                print(f"\n   🔸 {issue} ({len(msgs)} messages):")
                for msg in msgs[:3]:  # Show first 3 messages per issue
                    print(f"      • Row {msg['row']} ({msg['sheet']}): {msg['name']} - {msg['phone']}")
                    print(f"        Action: {msg['analysis']['action']}")
                if len(msgs) > 3:
                    print(f"        ... and {len(msgs) - 3} more")
        
        # Priority actions
        print(f"\n" + "="*60)
        print(f"🚨 PRIORITY ACTIONS")
        print(f"="*60)
        
        # High priority issues
        high_priority = []
        medium_priority = []
        opportunities = []
        
        for status_name, messages in status_breakdown.items():
            for msg in messages:
                severity = msg['analysis']['severity']
                if severity == 'High':
                    high_priority.append(f"Row {msg['row']} ({msg['sheet']}): {msg['analysis']['action']}")
                elif severity == 'Medium':
                    medium_priority.append(f"Row {msg['row']} ({msg['sheet']}): {msg['analysis']['action']}")
                elif status_name == 'REPLY':
                    opportunities.append(f"Row {msg['row']} ({msg['sheet']}): {msg['name']} - {msg['analysis']['action']}")
        
        if high_priority:
            print(f"🔴 HIGH PRIORITY ({len(high_priority)} issues):")
            for action in high_priority[:10]:  # Show first 10
                print(f"   • {action}")
            if len(high_priority) > 10:
                print(f"   ... and {len(high_priority) - 10} more high priority issues")
        
        if opportunities:
            print(f"\n💰 HOT LEADS - IMMEDIATE FOLLOW-UP REQUIRED ({len(opportunities)} leads):")
            for opportunity in opportunities[:5]:  # Show first 5
                print(f"   • {opportunity}")
        
        if medium_priority:
            print(f"\n🟡 MEDIUM PRIORITY ({len(medium_priority)} issues):")
            for action in medium_priority[:5]:  # Show first 5
                print(f"   • {action}")
        
        # Conversion funnel analysis
        print(f"\n" + "="*60)
        print(f"📊 CONVERSION FUNNEL ANALYSIS")
        print(f"="*60)
        
        delivered_count = len(status_breakdown['Delivered'])
        read_count = len(status_breakdown['Read'])
        reply_count = len(status_breakdown['REPLY'])
        
        if delivered_count > 0:
            read_rate = round((read_count / delivered_count) * 100, 1)
            reply_rate = round((reply_count / delivered_count) * 100, 1)
            
            print(f"📈 Delivery → Read Rate: {read_rate}% ({read_count}/{delivered_count})")
            print(f"📈 Delivery → Reply Rate: {reply_rate}% ({reply_count}/{delivered_count})")
            
            # Benchmarking
            if read_rate >= 60:
                print(f"   🟢 Excellent read rate! Above industry average (40-60%)")
            elif read_rate >= 40:
                print(f"   🔵 Good read rate, within industry average")
            else:
                print(f"   🟡 Read rate below average - consider improving message content")
                
            if reply_rate >= 10:
                print(f"   🟢 Outstanding reply rate! Well above average (2-5%)")
            elif reply_rate >= 5:
                print(f"   🔵 Good reply rate, above average")
            elif reply_rate >= 2:
                print(f"   🟡 Average reply rate")
            else:
                print(f"   🔴 Low reply rate - review message strategy")
        
        # Next steps
        print(f"\n💡 RECOMMENDED NEXT STEPS:")
        print(f"   1. Fix high priority issues first")
        print(f"   2. Follow up immediately with REPLY leads")
        print(f"   3. Run: poetry run python3 send_whatsapp.py process")
        print(f"   4. Monitor conversion rates and optimize messaging")
        print(f"   5. Set up automated alerts for new replies")
        
    except Exception as e:
        print(f"❌ Error during comprehensive diagnostic analysis: {e}")
        import traceback
        traceback.print_exc()

def error_sb_retry():
    """
    Special function to resend messages to contacts that have ERROR or Soft Bounce in the status column.
    Updates the status_timestamp column but preserves the original error in error_reason column.
    """
    print("\n--- Starting Error Retry Process ---")
    
    # --- Environment Variable Check ---
    if not all([API_KEY, TEMPLATE_ID, SENDER_NUMBER, GOOGLE_SHEET_NAME]):
        print("❌ Error: One or more environment variables are missing.")
        print("Please check your .env file for BREVO_API_KEY, BREVO_TEMPLATE_ID, BREVO_SENDER_NUMBER, GOOGLE_SHEET_NAME.")
        return
    
    # --- Google Sheet Connection ---
    client = get_google_sheet_client()
    if not client:
        return
    
    for sheet_name in SHEET_NAMES_TO_PROCESS:
        try:
            print(f"DEBUG: Attempting to open sheet: '{sheet_name}'...")
            worksheet = client.open(GOOGLE_SHEET_NAME).worksheet(sheet_name)
            print(f"DEBUG: Successfully opened sheet: '{sheet_name}'")
            
            # Get all the data from the sheet including the status column
            print(f"Fetching all data from sheet '{sheet_name}'...")
            all_sheet_data = worksheet.get_all_values()
            
            # Skip header row
            if len(all_sheet_data) <= 1:
                print(f"No data found in sheet '{sheet_name}'.") 
                continue
            
            # Track rows with errors for retry
            retry_rows = []
            
            for i, row in enumerate(all_sheet_data[1:], start=2):  # Start from row 2 (1-indexed, after header)
                # Check if we have enough columns for the status column
                if len(row) >= STATUS_COL:
                    status = row[STATUS_COL - 1]
                    
                    # Check if status starts with 'ERROR' or contains 'Soft Bounce' (case-insensitive)
                    if status and (len(status) >= 5 and status[:5].upper() == 'ERROR') or ('soft-bounce' in status.lower()):
                        # Only proceed if we have a phone number
                        if len(row) >= PHONE_COL and row[PHONE_COL - 1]:
                            phone = row[PHONE_COL - 1]
                            first_name = row[FIRST_NAME_COL - 1] if len(row) >= FIRST_NAME_COL else ""
                            
                            # Add to retry list
                            retry_rows.append((i, phone, first_name, status))
            
            print(f"Found {len(retry_rows)} rows with ERROR or Soft Bounce status in sheet '{sheet_name}'")
            
            # Process retry rows
            for row_num, phone, first_name, error_status in retry_rows:
                # Skip rows without valid phone numbers
                if not phone or not is_valid_phone_number(phone):
                    print(f"Skipping row {row_num} in sheet '{sheet_name}': Phone number '{phone}' is not valid.")
                    # Update the status and error reason
                    worksheet.update_cell(row_num, STATUS_COL, "INVALID_PHONE")
                    worksheet.update_cell(row_num, ERROR_REASON_COL, "Phone number is not valid")
                    continue
                
                # Clean and standardize the phone number (adds country code if needed)
                cleaned_phone = clean_phone_number(phone)
                print(f"Processing row {row_num} in sheet '{sheet_name}': Using phone '{cleaned_phone}' for original '{phone}'.")
                
                print(f"Retrying message for row {row_num} in sheet '{sheet_name}': Name - {first_name}, Phone - {cleaned_phone}, Previous status - {error_status}")
                
                # Send message without updating status column
                try:
                    status, data = send_whatsapp_message(first_name, cleaned_phone)
                    if status == "SENT":
                        print(f"✅ Successfully resent message to {cleaned_phone} (Row {row_num}). Message ID: {data}")
                    else:
                        print(f"❌ Failed to resend message to {cleaned_phone} (Row {row_num}). Error: {data}")
                        
                        # Try Selenium fallback if API fails
                        if 'selenium' in sys.modules:
                            # Construct the message text for Selenium - match Brevo template
                            message_text = f"Hello {first_name}, this is a follow-up message. Please let us know if you are interested."
                            selenium_status = send_whatsapp_selenium(cleaned_phone, message_text)
                            print(f"Selenium fallback for {cleaned_phone} (Row {row_num}): {selenium_status}")
                            
                except Exception as e:
                    print(f"❌ Exception occurred while sending to {cleaned_phone} (Row {row_num}): {str(e)}")
                    
                # Pause to avoid hitting API rate limits
                time.sleep(1)
                
        except Exception as e:
            print(f"❌ An error occurred processing sheet '{sheet_name}': {e}")
            
    print("\n--- Error Retry Process Complete ---")


def generate_analytics(worksheet):
    """
    Generate comprehensive analytics starting from column P.
    Creates status tracking with counts, percentages, and visual formatting.
    """
    print("📊 Generating analytics...")
    
    try:
        # Get all data from the sheet
        all_data = worksheet.get_all_values()
        if len(all_data) <= 1:  # Only header row or empty
            print("ℹ️ No data to analyze.")
            return
        
        # Skip header row
        data_rows = all_data[1:]
        
        # Count statuses
        status_counts = {}
        total_messages = 0
        error_details = {}
        
        for row in data_rows:
            if len(row) > STATUS_COL - 1:  # Check if status column exists
                status = row[STATUS_COL - 1].strip() if row[STATUS_COL - 1] else ""
                
                # Only count rows that have been processed (non-empty status)
                if not status:
                    continue
                    
                total_messages += 1
                
                # Normalize status names for better grouping
                if status.upper().startswith('ERROR'):
                    status_key = 'ERROR'
                    # Extract error reason if available
                    if len(row) > ERROR_REASON_COL - 1 and row[ERROR_REASON_COL - 1]:
                        error_reason = row[ERROR_REASON_COL - 1]
                        if error_reason not in error_details:
                            error_details[error_reason] = 0
                        error_details[error_reason] += 1
                elif status.upper() in ['DELIVERED', 'SENT']:
                    status_key = 'Delivered'
                elif status.upper() == 'READ':
                    status_key = 'Read'
                elif status.upper() == 'REPLY':
                    status_key = 'REPLY'
                elif 'SOFT BOUNCE' in status.upper():
                    status_key = 'Soft Bounce'
                else:
                    status_key = status or 'Not Sent'
                
                if status_key not in status_counts:
                    status_counts[status_key] = 0
                status_counts[status_key] += 1
        
        if total_messages == 0:
            print("ℹ️ No messages found to analyze.")
            return
        
        # Define logical funnel order (message lifecycle progression)
        funnel_order = [
            'Not Sent',      # Messages that haven't been sent yet
            'ERROR',         # Messages that failed to send
            'SOFT-BOUNCE',   # Messages that soft bounced
            'INVALID_PHONE', # Invalid phone numbers
            'Delivered',     # Messages successfully delivered
            'Read',          # Messages that were read
            'REPLY'          # Messages that received replies
        ]
        
        # Sort statuses by logical funnel order, then by count for statuses not in the order
        def get_sort_key(status_item):
            status, count = status_item
            # Normalize status for comparison
            status_normalized = status.upper().replace(' ', '_').replace('-', '_')
            
            # Check for partial matches
            if status_normalized.startswith('ERROR'):
                order_index = funnel_order.index('ERROR')
            elif 'SOFT' in status_normalized and 'BOUNCE' in status_normalized:
                order_index = funnel_order.index('SOFT-BOUNCE')
            elif 'INVALID' in status_normalized and 'PHONE' in status_normalized:
                order_index = funnel_order.index('INVALID_PHONE')
            else:
                # Try exact match
                try:
                    order_index = funnel_order.index(status)
                except ValueError:
                    # If status not in funnel_order, put it at the end, sorted by count
                    order_index = len(funnel_order)
            
            return (order_index, -count)  # Negative count for descending within same order
        
        sorted_statuses = sorted(status_counts.items(), key=get_sort_key)
        
        # Prepare analytics data starting from column P (column 16)
        analytics_start_col = 16  # Column P
        analytics_data = []
        
        # Header
        analytics_data.append(['ANALYTICS', '', '', ''])  # Row with analytics header
        analytics_data.append(['Status', 'Count', '%', 'Visual'])  # Column headers
        
        # Add total row at the top
        analytics_data.append(['TOTAL MESSAGES', str(total_messages), '100%', ''])
        analytics_data.append(['', '', '', ''])  # Empty separator row
        
        # Add status rows
        for status, count in sorted_statuses:
            percentage = round((count / total_messages) * 100, 1)
            visual_bar = '█' * min(int(percentage / 5), 20)  # Scale bar to max 20 chars
            color_indicator = {
                'ERROR': '🔴',
                'Delivered': '🔵', 
                'Read': '🟢',
                'REPLY': '🟢',
                'Soft Bounce': '🟡',
                'Not Sent': '⚫'
            }.get(status, '⚪')
            
            analytics_data.append([
                f"{color_indicator} {status}",
                str(count),
                f"{percentage}%",
                visual_bar
            ])
        
        # Add error breakdown if errors exist
        if error_details:
            analytics_data.append(['', '', '', ''])
            analytics_data.append(['ERROR BREAKDOWN', '', '', ''])
            analytics_data.append(['Error Reason', 'Count', '%', ''])
            
            sorted_errors = sorted(error_details.items(), key=lambda x: x[1], reverse=True)
            total_errors = sum(error_details.values())
            
            for error_reason, count in sorted_errors:
                error_percentage = round((count / total_errors) * 100, 1)
                # Truncate long error messages
                display_error = error_reason[:30] + '...' if len(error_reason) > 30 else error_reason
                analytics_data.append([
                    f"🔴 {display_error}",
                    str(count),
                    f"{error_percentage}%",
                    ''
                ])
        
        # Add timestamp
        analytics_data.append(['', '', '', ''])
        analytics_data.append(['Last Updated', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '', ''])
        
        # Clear existing analytics area (columns P to S)
        try:
            # Clear a reasonable range for analytics
            clear_range = f'P1:S{len(analytics_data) + 10}'
            worksheet.batch_clear([clear_range])
        except Exception as e:
            print(f"⚠️ Warning: Could not clear existing analytics: {e}")
        
        # Write analytics data
        if analytics_data:
            start_cell = 'P1'
            end_col_letter = chr(ord('P') + len(analytics_data[0]) - 1)  # Calculate end column
            end_row = len(analytics_data)
            range_name = f'{start_cell}:{end_col_letter}{end_row}'
            
            worksheet.update(range_name, analytics_data)
            print(f"✅ Analytics updated in range {range_name}")
            
            # Apply formatting for better visualization
            try:
                # Format header row
                worksheet.format('P1:S1', {
                    'backgroundColor': {'red': 0.2, 'green': 0.2, 'blue': 0.8},
                    'textFormat': {'bold': True, 'foregroundColor': {'red': 1, 'green': 1, 'blue': 1}}
                })
                
                # Format column headers
                worksheet.format('P2:S2', {
                    'backgroundColor': {'red': 0.9, 'green': 0.9, 'blue': 0.9},
                    'textFormat': {'bold': True}
                })
                
                print("✅ Analytics formatting applied")
            except Exception as e:
                print(f"⚠️ Warning: Could not apply formatting: {e}")
        
        print(f"📊 Analytics generated successfully! Total messages analyzed: {total_messages}")
        
    except Exception as e:
        print(f"❌ Error generating analytics: {e}")
        import traceback
        traceback.print_exc()


def refresh_analytics():
    """
    Refresh analytics for all sheets in SHEET_NAMES_TO_PROCESS.
    """
    client = get_google_sheet_client()
    if not client:
        return
    
    try:
        for sheet_name in SHEET_NAMES_TO_PROCESS:
            try:
                worksheet = client.open(GOOGLE_SHEET_NAME).worksheet(sheet_name)
                print(f"\n📊 Generating analytics for sheet '{sheet_name}'...")
                generate_analytics(worksheet)
                print(f"✅ Analytics generated successfully for sheet '{sheet_name}'.")
            except Exception as e:
                print(f"❌ Error processing sheet '{sheet_name}': {e}")
                continue
                
        print("\n🎉 Analytics refresh completed for all sheets.")
    except Exception as e:
        print(f"❌ An unexpected error occurred during analytics refresh: {e}")


def error_sb_retry_from_row(start_row, sheet_name="Without Price"):
    """
    Retry sending messages to ERROR/SOFT BOUNCE candidates from a specific starting row onwards.
    Similar to LAST PROCESSED ROW functionality.
    
    Args:
        start_row (int): Starting row number to process ERROR/SOFT BOUNCE messages from
        sheet_name (str): Name of the sheet to process (default: "Without Price")
    """
    print(f"🔄 Retrying ERROR/SOFT BOUNCE messages from row {start_row} onwards in sheet '{sheet_name}'")
    
    # --- Environment Variable Check ---
    if not all([API_KEY, TEMPLATE_ID, SENDER_NUMBER, GOOGLE_SHEET_NAME]):
        print("❌ Error: One or more environment variables are missing.")
        print("Please check your .env file for BREVO_API_KEY, BREVO_TEMPLATE_ID, BREVO_SENDER_NUMBER, GOOGLE_SHEET_NAME.")
        return
    
    # --- Google Sheet Connection ---
    client = get_google_sheet_client()
    if not client:
        return
    
    try:
        worksheet = client.open(GOOGLE_SHEET_NAME).worksheet(sheet_name)
        print(f"✅ Successfully opened sheet: '{sheet_name}'")
    except gspread.exceptions.SpreadsheetNotFound:
        print(f"❌ Error: Spreadsheet '{GOOGLE_SHEET_NAME}' not found.")
        return
    except Exception as e:
        print(f"❌ An error occurred opening the sheet: {e}")
        return
    
    # --- Fetch All Data ---
    try:
        print(f"Fetching all data from sheet '{sheet_name}'...")
        all_sheet_data = worksheet.get_all_values()
        
        # Skip header row
        if len(all_sheet_data) <= 1:
            print(f"No data found in sheet '{sheet_name}'.")
            return
            
        header_row = all_sheet_data[0]
        data_rows = all_sheet_data[1:]  # Skip header
        
        # Track rows with errors for retry
        retry_rows = []
        
        # Process rows from start_row onwards
        for i, row in enumerate(data_rows, start=2):  # Start from row 2 (1-indexed)
            if i < start_row:
                continue
                
            # Check if we have enough columns for the status column
            if len(row) >= STATUS_COL:
                status = row[STATUS_COL - 1]
                
                # Check if status starts with 'ERROR' or contains 'Soft Bounce' (case-insensitive)
                if status and ((len(status) >= 5 and status[:5].upper() == 'ERROR') or ('soft-bounce' in status.lower())):
                    # Only proceed if we have a phone number
                    if len(row) >= PHONE_COL and row[PHONE_COL - 1]:
                        phone = row[PHONE_COL - 1]
                        first_name = row[FIRST_NAME_COL - 1] if len(row) >= FIRST_NAME_COL else ""
                        
                        # Add to retry list
                        retry_rows.append((i, phone, first_name, status))
        
        print(f"Found {len(retry_rows)} ERROR/SOFT BOUNCE rows from row {start_row} onwards in sheet '{sheet_name}'")
        
        if not retry_rows:
            print("No ERROR/SOFT BOUNCE messages found to retry.")
            return
        
        # Process retry rows
        updates = []
        successful_retries = 0
        failed_retries = 0
        
        for row_num, phone, first_name, error_status in retry_rows:
            # Skip rows without valid phone numbers
            if not phone or not is_valid_phone_number(phone):
                print(f"Skipping row {row_num} in sheet '{sheet_name}': Phone number '{phone}' is not valid.")
                # Update the status and error reason
                updates.append(gspread.Cell(row_num, STATUS_COL, "INVALID_PHONE"))
                updates.append(gspread.Cell(row_num, ERROR_REASON_COL, "Phone number is not valid"))
                failed_retries += 1
                continue
            
            # Clean and standardize the phone number (adds country code if needed)
            cleaned_phone = clean_phone_number(phone)
            print(f"Processing row {row_num} in sheet '{sheet_name}': Using phone '{cleaned_phone}' for original '{phone}'.")
            
            print(f"Retrying message for row {row_num} in sheet '{sheet_name}': Name - {first_name}, Phone - {cleaned_phone}, Previous status - {error_status}")
            
            # Send message without updating status column initially
            try:
                status, data = send_whatsapp_message(first_name, cleaned_phone)
                if status == "SENT":
                    print(f"✅ Successfully resent message to {cleaned_phone} (Row {row_num}). Message ID: {data}")
                    successful_retries += 1
                else:
                    print(f"❌ Failed to resend message to {cleaned_phone} (Row {row_num}). Error: {data}")
                    
                    # Try Selenium fallback if API fails
                    if 'selenium' in sys.modules:
                        # Construct the message text for Selenium - match Brevo template
                        message_text = f"Hello {first_name}, this is a follow-up message. Please let us know if you are interested."
                        selenium_status = send_whatsapp_selenium(cleaned_phone, message_text)
                        print(f"Selenium fallback for {cleaned_phone} (Row {row_num}): {selenium_status}")
                        
            except Exception as e:
                print(f"❌ Exception occurred while sending to {cleaned_phone} (Row {row_num}): {str(e)}")
                failed_retries += 1
            
            # Rate limiting
            time.sleep(1)
        
        # Update Google Sheet with results
        if updates:
            try:
                print(f"\n📝 Updating {len(updates)} cells in the sheet...")
                worksheet.update_cells(updates)
                print("✅ Sheet updated successfully.")
            except Exception as e:
                print(f"❌ Error updating sheet: {e}")
        
        # --- Summary ---
        print(f"\n📊 ERROR/SOFT BOUNCE Retry Summary (from row {start_row} onwards):")
        print(f"✅ Successful retries: {successful_retries}")
        print(f"❌ Failed retries: {failed_retries}")
        print(f"🎯 Total rows processed: {len(retry_rows)}")
        
    except Exception as e:
        print(f"❌ Error processing sheet '{sheet_name}': {e}")
    
    # --- Environment Variable Check ---
    if not all([API_KEY, TEMPLATE_ID, SENDER_NUMBER, GOOGLE_SHEET_NAME]):
        print("❌ Error: One or more environment variables are missing.")
        print("Please check your .env file for BREVO_API_KEY, BREVO_TEMPLATE_ID, BREVO_SENDER_NUMBER, GOOGLE_SHEET_NAME.")
        return
    
    # --- Google Sheet Connection ---
    client = get_google_sheet_client()
    if not client:
        return
    
    try:
        worksheet = client.open(GOOGLE_SHEET_NAME).worksheet(sheet_name)
        print(f"✅ Successfully opened sheet: '{sheet_name}'")
    except gspread.exceptions.SpreadsheetNotFound:
        print(f"❌ Error: Spreadsheet '{GOOGLE_SHEET_NAME}' not found.")
        return
    except Exception as e:
        print(f"❌ An error occurred opening the sheet: {e}")
        return
    
    # --- Fetch All Data ---
    try:
        print(f"Fetching all data from sheet '{sheet_name}'...")
        all_sheet_data = worksheet.get_all_values()
        
        # Skip header row
        if len(all_sheet_data) <= 1:
            print(f"No data found in sheet '{sheet_name}'.")
            return
            
        header_row = all_sheet_data[0]
        data_rows = all_sheet_data[1:]  # Skip header
        
        # Track rows with errors for retry
        retry_rows = []
        
        # Process rows from start_row onwards
        for i, row in enumerate(data_rows, start=2):  # Start from row 2 (1-indexed)
            if i < start_row:
                continue
                
            # Check if we have enough columns for the status column
            if len(row) >= STATUS_COL:
                status = row[STATUS_COL - 1]
                
                # Check if status starts with 'ERROR' or contains 'Soft Bounce' (case-insensitive)
                if status and ((len(status) >= 5 and status[:5].upper() == 'ERROR') or ('soft-bounce' in status.lower())):
                    # Only proceed if we have a phone number
                    if len(row) >= PHONE_COL and row[PHONE_COL - 1]:
                        phone = row[PHONE_COL - 1]
                        first_name = row[FIRST_NAME_COL - 1] if len(row) >= FIRST_NAME_COL else ""
                        
                        # Add to retry list
                        retry_rows.append((i, phone, first_name, status))
        
        print(f"Found {len(retry_rows)} ERROR/SOFT BOUNCE rows from row {start_row} onwards in sheet '{sheet_name}'")
        
        if not retry_rows:
            print("No ERROR/SOFT BOUNCE messages found to retry.")
            return
        
        # Process retry rows
        updates = []
        successful_retries = 0
        failed_retries = 0
        
        for row_num, phone, first_name, error_status in retry_rows:
            # Skip rows without valid phone numbers
            if not phone or not is_valid_phone_number(phone):
                print(f"Skipping row {row_num} in sheet '{sheet_name}': Phone number '{phone}' is not valid.")
                # Update the status and error reason
                updates.append(gspread.Cell(row_num, STATUS_COL, "INVALID_PHONE"))
                updates.append(gspread.Cell(row_num, ERROR_REASON_COL, "Phone number is not valid"))
                failed_retries += 1
                continue
            
            # Clean and standardize the phone number (adds country code if needed)
            cleaned_phone = clean_phone_number(phone)
            print(f"Processing row {row_num} in sheet '{sheet_name}': Using phone '{cleaned_phone}' for original '{phone}'.")
            
            print(f"Retrying message for row {row_num} in sheet '{sheet_name}': Name - {first_name}, Phone - {cleaned_phone}, Previous status - {error_status}")
            
            # Send message without updating status column initially
            try:
                status, data = send_whatsapp_message(first_name, cleaned_phone)
                if status == "SENT":
                    print(f"✅ Successfully resent message to {cleaned_phone} (Row {row_num}). Message ID: {data}")
                    successful_retries += 1
                else:
                    print(f"❌ Failed to resend message to {cleaned_phone} (Row {row_num}). Error: {data}")
                    
                    # Try Selenium fallback if API fails
                    if 'selenium' in sys.modules:
                        # Construct the message text for Selenium - match Brevo template
                        message_text = f"Hello {first_name}, this is a follow-up message. Please let us know if you are interested."
                        selenium_status = send_whatsapp_selenium(cleaned_phone, message_text)
                        print(f"Selenium fallback for {cleaned_phone} (Row {row_num}): {selenium_status}")
                        
            except Exception as e:
                print(f"❌ Exception occurred while sending to {cleaned_phone} (Row {row_num}): {str(e)}")
                failed_retries += 1
            
            # Rate limiting
            time.sleep(1)
        
        # Update Google Sheet with results
        if updates:
            try:
                print(f"\n📝 Updating {len(updates)} cells in the sheet...")
                worksheet.update_cells(updates)
                print("✅ Sheet updated successfully.")
            except Exception as e:
                print(f"❌ Error updating sheet: {e}")
        
        # --- Summary ---
        print(f"\n📊 ERROR/SOFT BOUNCE Retry Summary (from row {start_row} onwards):")
        print(f"✅ Successful retries: {successful_retries}")
        print(f"❌ Failed retries: {failed_retries}")
        print(f"🎯 Total rows processed: {len(retry_rows)}")
        
    except Exception as e:
        print(f"❌ Error processing sheet '{sheet_name}': {e}")
    
    # Update event statuses after sending
    if successful_retries > 0:
        print("\n🔄 Updating message statuses...")
        update_event_statuses(worksheet)

# ... (rest of the code remains the same)

if __name__ == "__main__":
    # Example of how to run the script from the command line:
    # `python3 send_whatsapp.py process` to run the main lead processing logic
    # `python3 send_whatsapp.py retry` to retry failed messages
    # `python3 send_whatsapp.py analytics` to generate analytics
    # `python3 send_whatsapp.py diagnose` to run comprehensive diagnostics
    # `python3 send_whatsapp.py send_rows 2,4,5` to send messages to specific rows
    # `python3 send_whatsapp.py retry_from_row 100` to retry ERROR/SOFT BOUNCE messages from row 100 onwards
    
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "process":
            process_leads()
        elif command == "update_activity":
            days = 30  # default
            if len(sys.argv) > 2:
                try:
                    days = int(sys.argv[2])
                    if days > 90:
                        print("⚠️ Warning: Maximum days allowed is 90. Setting to 90.")
                        days = 90
                except ValueError:
                    print("⚠️ Warning: Invalid days parameter. Using default of 30.")
            update_activity(days)
        elif command == "retry":
            error_sb_retry()
        elif command == "analytics":
            refresh_analytics()
        elif command == "send_rows":
            if len(sys.argv) < 3:
                print("❌ Error: Missing row numbers for 'send_rows' command.")
                print("Usage: python3 send_whatsapp.py send_rows <row_numbers> [sheet_name]")
            else:
                try:
                    row_numbers_str = sys.argv[2]
                    row_numbers = [int(r.strip()) for r in row_numbers_str.split(',')]
                    
                    sheet_name = "Without Price"  # default
                    if len(sys.argv) > 3:
                        sheet_name = sys.argv[3].strip("'\"")
                    
                    print(f"🎯 Targeting rows: {row_numbers} in sheet: '{sheet_name}'")
                    send_to_specific_rows(row_numbers, sheet_name)
                except ValueError as e:
                    print(f"❌ Error parsing row numbers: {e}")
                    print("Please provide row numbers as comma-separated integers (e.g., 2,4,5)")
                except Exception as e:
                    print(f"❌ Error executing send_rows command: {e}")
        elif command == "diagnose":
            diagnose_all_messages()
        elif command == "retry_from_row" and len(sys.argv) > 2:
            try:
                start_row = int(sys.argv[2])
                sheet_name = "Without Price"  # default
                if len(sys.argv) > 3:
                    sheet_name = sys.argv[3].strip("'\"")

                if start_row < 2:
                    print("❌ Error: Starting row must be at least 2 to skip the header.")
                else:
                    print(f"🎯 Retrying from row: {start_row} in sheet: '{sheet_name}'")
                    error_sb_retry_from_row(start_row, sheet_name)
            except ValueError:
                print("❌ Error: Invalid row number. Please provide an integer.")
        else:
            print("❌ Unknown command or missing arguments.")
            print("Available commands: process, update_activity, error_sb_retry, analytics, send_rows, diagnose, retry_from_row")
    else:
        print("ℹ️ No command provided. Available commands:")
        print("  - process: Process new leads and send WhatsApp messages")
        print("  - update_activity [days]: Update message statuses (default: 30 days, max: 90)")
        print("  - error_sb_retry: Retry sending messages that failed or soft bounced")
        print("  - analytics: Generate comprehensive analytics starting from column P")
        print("  - send_rows <row_numbers> [sheet_name]: Send messages to specific rows")
        print("  - diagnose: Diagnose all message statuses and provide detailed insights")
        print("\nExample usage:")
        print("  python3 send_whatsapp.py process")
        print("  python3 send_whatsapp.py update_activity 7")
        print("  python3 send_whatsapp.py error_sb_retry")
        print("  python3 send_whatsapp.py analytics")
        print("  python3 send_whatsapp.py send_rows 2,4,5")
        print("  python3 send_whatsapp.py send_rows 2,4,5 'Without Price'")
        print("  python3 send_whatsapp.py diagnose")
    # All command handling is now in the block abov