#!/usr/bin/env python3
"""
Sheet20 Parser - Specialized parser for headerless name/email data
Handles the 1,091 rows of name-email pairs in Sheet20
"""

import pandas as pd
import sqlite3
import re
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class Sheet20Parser:
    def __init__(self):
        self.assets_dir = project_root / "assets" / "leads_data"
        self.db_path = project_root / "dashboard" / "database" / "leads.db"
        self.file_path = self.assets_dir / "US Leads.xlsx"
        self.sheet_name = "Sheet20"
        
        print(f"🎯 Sheet20 Parser initialized")
        print(f"📁 File: {self.file_path}")
        print(f"🗄️  Database: {self.db_path}")
    
    def load_and_validate_data(self):
        """Load Sheet20 data and validate structure"""
        print(f"\n📊 Loading Sheet20 data...")
        
        # Read without headers since first row contains data
        df = pd.read_excel(self.file_path, sheet_name=self.sheet_name, header=None)
        
        print(f"📊 Loaded {len(df)} rows × {len(df.columns)} columns")
        
        # Validate expected structure (2 columns: names, emails)
        if len(df.columns) != 2:
            raise ValueError(f"Expected 2 columns, got {len(df.columns)}")
        
        # Rename columns for clarity
        df.columns = ['name', 'email']
        
        print(f"✅ Data structure validated")
        return df
    
    def clean_data(self, df):
        """Clean and validate the name/email data"""
        print(f"\n🧹 Cleaning data...")
        
        initial_count = len(df)
        
        # Remove rows where both name and email are null
        df = df.dropna(how='all')
        print(f"   • Removed {initial_count - len(df)} completely empty rows")
        
        # Clean names
        df['name'] = df['name'].astype(str).str.strip()
        df['name'] = df['name'].replace(['nan', 'NaN', 'None', ''], None)
        
        # Clean emails
        df['email'] = df['email'].astype(str).str.strip().str.lower()
        df['email'] = df['email'].replace(['nan', 'NaN', 'None', ''], None)
        
        # Validate email format
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        valid_emails = df['email'].str.match(email_pattern, na=False)
        
        print(f"   • Valid emails: {valid_emails.sum()} / {len(df)} ({valid_emails.sum()/len(df)*100:.1f}%)")
        
        # Filter to rows with valid emails (names can be missing but emails must be valid)
        df = df[valid_emails].copy()
        
        # Handle missing names
        missing_names = df['name'].isna().sum()
        if missing_names > 0:
            print(f"   • Warning: {missing_names} rows have missing names")
            df['name'] = df['name'].fillna('Unknown')
        
        print(f"   • Final dataset: {len(df)} leads with valid emails")
        return df
    
    def check_duplicates(self, df):
        """Check for duplicates within the dataset and against database"""
        print(f"\n🔍 Checking for duplicates...")
        
        # Internal duplicates
        internal_dupes = df.duplicated(subset=['email']).sum()
        if internal_dupes > 0:
            print(f"   • Internal duplicate emails: {internal_dupes}")
            df = df.drop_duplicates(subset=['email'], keep='first')
            print(f"   • Removed duplicates, {len(df)} unique leads remaining")
        
        # Database duplicates
        if not self.db_path.exists():
            print(f"   • Database not found, all leads will be new")
            return df, 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check existing leads
        emails_to_check = df['email'].tolist()
        placeholders = ','.join(['?' for _ in emails_to_check])
        cursor.execute(f"SELECT email FROM leads WHERE LOWER(email) IN ({placeholders})", emails_to_check)
        existing_emails = set(row[0].lower() for row in cursor.fetchall())
        
        existing_count = len([email for email in emails_to_check if email in existing_emails])
        new_count = len(df) - existing_count
        
        print(f"   • Existing in database: {existing_count}")
        print(f"   • New leads to import: {new_count}")
        
        # Filter out existing leads
        df_new = df[~df['email'].isin(existing_emails)].copy()
        
        conn.close()
        return df_new, existing_count
    
    def import_to_database(self, df, existing_count):
        """Import the cleaned leads to database"""
        if len(df) == 0:
            print(f"\n📊 No new leads to import")
            return 0
        
        print(f"\n💾 Importing {len(df)} new leads to database...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Prepare data for insertion
        current_time = datetime.now().isoformat()
        imported_count = 0
        
        for _, row in df.iterrows():
            try:
                cursor.execute("""
                    INSERT INTO leads (
                        full_name, email, workshop_type, payment_status,
                        lead_source, data_source, created_time, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row['name'],
                    row['email'],
                    'Sheet20',  # Workshop type
                    'Unknown',  # Payment status
                    'excel_import',  # Lead source
                    'Sheet20 - US Leads.xlsx',  # Data source
                    current_time,
                    'Imported from Sheet20 - bulk email list'
                ))
                imported_count += 1
                
            except Exception as e:
                print(f"   ⚠️  Error importing {row['email']}: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"✅ Successfully imported {imported_count} new leads")
        return imported_count
    
    def generate_report(self, total_processed, new_imports, existing_count):
        """Generate migration report"""
        print(f"\n" + "=" * 60)
        print(f"📊 SHEET20 MIGRATION REPORT")
        print(f"=" * 60)
        print(f"📋 Sheet: {self.sheet_name}")
        print(f"📁 Source: US Leads.xlsx")
        print(f"🕒 Processed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"")
        print(f"📊 SUMMARY:")
        print(f"   • Total records processed: {total_processed}")
        print(f"   • Already existed: {existing_count}")
        print(f"   • New leads imported: {new_imports}")
        print(f"   • Import success rate: {(new_imports/(total_processed-existing_count))*100:.1f}%" if (total_processed-existing_count) > 0 else "N/A")
        print(f"")
        print(f"🎯 DATABASE STATUS:")
        
        # Get updated database stats
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM leads")
        total_leads = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM leads WHERE data_source LIKE '%Sheet20%'")
        sheet20_leads = cursor.fetchone()[0]
        
        print(f"   • Total leads in database: {total_leads}")
        print(f"   • Sheet20 leads: {sheet20_leads}")
        
        conn.close()
        
        print(f"=" * 60)
    
    def run_migration(self):
        """Execute complete migration process"""
        try:
            # Load and validate data
            df = self.load_and_validate_data()
            
            # Clean data
            df_clean = self.clean_data(df)
            
            # Check duplicates
            df_new, existing_count = self.check_duplicates(df_clean)
            
            # Import to database
            imported_count = self.import_to_database(df_new, existing_count)
            
            # Generate report
            self.generate_report(len(df_clean), imported_count, existing_count)
            
            return {
                'total_processed': len(df_clean),
                'new_imports': imported_count,
                'existing_count': existing_count,
                'success': True
            }
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }

def main():
    """Main function to run Sheet20 migration"""
    print("=" * 60)
    print("📊 SHEET20 MIGRATION TOOL")
    print("=" * 60)
    
    parser = Sheet20Parser()
    result = parser.run_migration()
    
    if result['success']:
        print(f"\n🎉 Migration completed successfully!")
        print(f"📈 Imported {result['new_imports']} new leads")
    else:
        print(f"\n❌ Migration failed: {result['error']}")

if __name__ == "__main__":
    main()