#!/usr/bin/env python3
"""
Generic Workshop Data Parser Template
Flexible parser for different workshop sheet formats from US Leads.xlsx
"""

import pandas as pd
import sqlite3
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class WorkshopParser:
    def __init__(self, sheet_name, workshop_type, workshop_dates=None):
        self.assets_dir = project_root / "assets"
        self.db_path = project_root / "dashboard" / "database" / "leads.db"
        # Default to US Leads.xlsx, but allow override
        self.file_path = self.assets_dir / "US Leads.xlsx"
        self.sheet_name = sheet_name
        self.workshop_type = workshop_type
        self.workshop_dates = workshop_dates
        
        print(f"🎯 {sheet_name} Parser initialized")
        print(f"📁 File: {self.file_path}")
        print(f"🗄️  Database: {self.db_path}")
        print(f"🎓 Workshop Type: {workshop_type}")
    
    def set_file_path(self, file_path):
        """Set a custom file path for the Excel file"""
        self.file_path = Path(file_path)
        print(f"📁 Updated file path: {self.file_path}")
    
    def analyze_sheet_structure(self):
        """Analyze the specific structure of the workshop sheet"""
        print(f"\n🔍 Analyzing {self.sheet_name} structure...")
        
        df = pd.read_excel(self.file_path, sheet_name=self.sheet_name)
        
        print(f"📊 Total rows: {len(df)}")
        print(f"📊 Columns: {list(df.columns)}")
        print(f"\n🔍 First few rows (raw data):")
        print(df.head(10).to_string())
        
        # Check for data patterns
        print(f"\n📋 Data quality analysis:")
        print(f"   • Empty rows: {df.isnull().all(axis=1).sum()}")
        
        # Check for common column variations
        email_cols = [col for col in df.columns if 'email' in col.lower()]
        name_cols = [col for col in df.columns if 'name' in col.lower()]
        phone_cols = [col for col in df.columns if 'phone' in col.lower()]
        date_cols = [col for col in df.columns if 'date' in col.lower()]
        amount_cols = [col for col in df.columns if 'amount' in col.lower()]
        
        print(f"   • Email columns: {email_cols}")
        print(f"   • Name columns: {name_cols}")
        print(f"   • Phone columns: {phone_cols}")
        print(f"   • Date columns: {date_cols}")
        print(f"   • Amount columns: {amount_cols}")
        
        if email_cols:
            print(f"   • Rows with email: {df[email_cols[0]].notna().sum()}")
        if name_cols:
            print(f"   • Rows with names: {df[name_cols[0]].notna().sum()}")
        
        return df
    
    def detect_column_mapping(self, df):
        """Automatically detect column mappings based on column names"""
        mapping = {}
        
        # Map common column variations
        for col in df.columns:
            col_lower = col.lower()
            if 'first name' in col_lower:
                mapping['first_name'] = col
            elif 'last name' in col_lower:
                mapping['last_name'] = col
            elif 'name' in col_lower and 'first' not in col_lower and 'last' not in col_lower:
                mapping['name'] = col
            elif 'email' in col_lower:
                mapping['email'] = col
            elif 'phone' in col_lower:
                mapping['phone'] = col
            elif 'payment date' in col_lower or 'date' in col_lower:
                if 'payment_date' not in mapping:
                    mapping['payment_date'] = col
                elif 'lead_date' not in mapping:
                    mapping['lead_date'] = col
            elif 'amount' in col_lower:
                mapping['amount'] = col
            elif 'payment method' in col_lower or 'method' in col_lower:
                mapping['payment_method'] = col
        
        print(f"\n🗺️  Detected column mapping:")
        for key, value in mapping.items():
            print(f"   • {key}: {value}")
        
        return mapping
    
    def clean_and_parse_data(self, df, column_mapping):
        """Clean and parse the data for database insertion"""
        print(f"\n🧹 Cleaning data...")
        
        # Remove completely empty rows
        df = df.dropna(how='all')
        
        # Remove rows where name is empty or looks like a header
        name_col = column_mapping.get('name', 'Name')
        if name_col in df.columns:
            df = df[df[name_col].notna() & (df[name_col] != name_col)]
        
        print(f"📊 After cleaning: {len(df)} valid rows")
        
        parsed_records = []
        
        for idx, row in df.iterrows():
            try:
                # Extract basic info using mapping - handle both combined name and first/last name
                name = None
                if 'first_name' in column_mapping and 'last_name' in column_mapping:
                    first_name = str(row[column_mapping['first_name']]).strip() if pd.notna(row[column_mapping['first_name']]) else ""
                    last_name = str(row[column_mapping['last_name']]).strip() if pd.notna(row[column_mapping['last_name']]) else ""
                    name = f"{first_name} {last_name}".strip()
                elif 'name' in column_mapping:
                    name = str(row[column_mapping['name']]).strip() if pd.notna(row[column_mapping['name']]) else None
                
                email = str(row[column_mapping.get('email', 'Email')]).strip().lower() if pd.notna(row[column_mapping.get('email', 'Email')]) else None
                
                # Handle phone field safely - only if column exists
                phone = None
                if 'phone' in column_mapping and column_mapping['phone'] in df.columns:
                    phone = str(row[column_mapping['phone']]).strip() if pd.notna(row[column_mapping['phone']]) else None
                
                # Skip if no name or email
                if not name or not email or email == 'nan':
                    print(f"   ⏭️  Skipping row {idx}: Missing name/email")
                    continue
                
                # Parse payment info
                payment_amount = None
                amount_col = column_mapping.get('amount', 'Amount')
                if amount_col in df.columns and pd.notna(row[amount_col]):
                    try:
                        payment_amount = float(row[amount_col])
                    except:
                        payment_amount = None
                
                payment_method = None
                method_col = column_mapping.get('payment_method')
                if method_col and method_col in df.columns:
                    payment_method = str(row[method_col]).strip() if pd.notna(row[method_col]) else None
                
                # Parse dates
                lead_date = None
                payment_date = None
                enrollment_date = None
                
                lead_date_col = column_mapping.get('lead_date')
                if lead_date_col and lead_date_col in df.columns and pd.notna(row[lead_date_col]):
                    try:
                        lead_date = pd.to_datetime(row[lead_date_col]).isoformat()
                    except:
                        pass
                
                payment_date_col = column_mapping.get('payment_date')
                if payment_date_col and payment_date_col in df.columns and pd.notna(row[payment_date_col]):
                    try:
                        payment_date = pd.to_datetime(row[payment_date_col]).isoformat()
                        enrollment_date = payment_date  # Assume enrollment when payment made
                    except:
                        pass
                
                # Determine payment status
                payment_status = 'Unknown'
                if payment_amount and payment_amount > 0:
                    if payment_date:
                        payment_status = 'Paid'
                    else:
                        payment_status = 'Pending'
                
                # Extract notes from unnamed columns
                notes_parts = []
                for col in df.columns:
                    if 'unnamed' in col.lower() and pd.notna(row[col]):
                        notes_parts.append(str(row[col]).strip())
                
                notes = ' | '.join(notes_parts) if notes_parts else None
                
                workshop_note = f"{self.sheet_name} workshop"
                if self.workshop_dates:
                    workshop_note += f" ({self.workshop_dates})"
                if payment_method:
                    workshop_note += f". Payment via {payment_method}"
                if notes:
                    workshop_note += f". {notes}"
                
                record = {
                    'full_name': name,
                    'email': email,
                    'phone': phone,
                    'workshop_type': self.workshop_type,
                    'payment_status': payment_status,
                    'payment_amount': payment_amount,
                    'lead_source': 'excel_import',
                    'data_source': f'{Path(self.file_path).name} - {self.sheet_name}',
                    'created_time': lead_date or datetime.now().isoformat(),
                    'enrolled_date': enrollment_date,
                    'payment_date': payment_date,
                    'notes': workshop_note,
                    'location': 'US'
                }
                
                parsed_records.append(record)
                print(f"   ✅ Parsed: {name} ({email}) - ${payment_amount or 0}")
                
            except Exception as e:
                print(f"   ❌ Error parsing row {idx}: {e}")
                continue
        
        print(f"\n📋 Successfully parsed {len(parsed_records)} records")
        return parsed_records
    
    def insert_to_database(self, records):
        """Insert parsed records into the database"""
        print(f"\n💾 Inserting {len(records)} records to database...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        updated_count = 0
        skipped_count = 0
        
        for record in records:
            try:
                # Check if lead already exists by email
                cursor.execute("SELECT id, workshop_type, payment_status FROM leads WHERE email = ?", (record['email'],))
                existing = cursor.fetchone()
                
                if existing:
                    existing_id, existing_workshop, existing_status = existing
                    
                    # Update if this has better/more recent payment data
                    if record['payment_status'] == 'Paid' and existing_status != 'Paid':
                        update_sql = """
                            UPDATE leads SET 
                                workshop_type = ?, payment_status = ?, payment_amount = ?,
                                enrolled_date = ?, payment_date = ?, notes = ?, location = ?
                            WHERE email = ?
                        """
                        cursor.execute(update_sql, (
                            record['workshop_type'], record['payment_status'], record['payment_amount'],
                            record['enrolled_date'], record['payment_date'],
                            record['notes'], record['location'], record['email']
                        ))
                        updated_count += 1
                        print(f"   🔄 Updated: {record['full_name']} (better payment info)")
                    else:
                        skipped_count += 1
                        print(f"   ⏭️  Skipped: {record['full_name']} (already exists)")
                else:
                    # Insert new lead
                    insert_sql = """
                        INSERT INTO leads (
                            full_name, email, phone, workshop_type, payment_status, payment_amount,
                            lead_source, data_source, created_time, enrolled_date,
                            payment_date, notes, location
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    cursor.execute(insert_sql, (
                        record['full_name'], record['email'], record['phone'], record['workshop_type'],
                        record['payment_status'], record['payment_amount'],
                        record['lead_source'], record['data_source'], record['created_time'],
                        record['enrolled_date'], record['payment_date'], record['notes'], record['location']
                    ))
                    inserted_count += 1
                    print(f"   ✅ Inserted: {record['full_name']}")
                    
            except Exception as e:
                print(f"   ❌ Error processing {record['full_name']}: {e}")
                skipped_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 Database operation complete!")
        print(f"   ✅ Inserted: {inserted_count} new leads")
        print(f"   🔄 Updated: {updated_count} existing leads")
        print(f"   ⏭️  Skipped: {skipped_count} leads")
        
        return {
            'inserted': inserted_count,
            'updated': updated_count,
            'skipped': skipped_count
        }

def parse_workshop(sheet_name, workshop_type, workshop_dates=None):
    """Parse a specific workshop sheet"""
    print("=" * 60)
    print(f"🎓 {sheet_name.upper()} WORKSHOP DATA PARSER")
    print("=" * 60)
    
    parser = WorkshopParser(sheet_name, workshop_type, workshop_dates)
    
    # Step 1: Analyze sheet structure
    df = parser.analyze_sheet_structure()
    
    # Step 2: Detect column mapping
    column_mapping = parser.detect_column_mapping(df)
    
    # Step 3: Clean and parse data
    records = parser.clean_and_parse_data(df, column_mapping)
    
    if not records:
        print("❌ No valid records found to insert")
        return None
    
    # Step 4: Preview some records
    print(f"\n👀 Preview of parsed records:")
    for i, record in enumerate(records[:3]):
        print(f"   {i+1}. {record['full_name']} ({record['email']}) - {record['payment_status']} ${record['payment_amount'] or 0}")
    
    # Step 5: Insert to database
    print(f"\n🚀 Proceeding to insert {len(records)} records to database...")
    result = parser.insert_to_database(records)
    print(f"\n✨ Success! Check the dashboard to verify the data.")
    
    return result

if __name__ == "__main__":
    # Example usage - can be customized for each sheet
    if len(sys.argv) < 3:
        print("Usage: python workshop_parser_template.py <sheet_name> <workshop_type> [workshop_dates]")
        print("Example: python workshop_parser_template.py 'L1 Feb1&2' 'L1' 'February 1-2, 2025'")
        sys.exit(1)
    
    sheet_name = sys.argv[1]
    workshop_type = sys.argv[2]
    workshop_dates = sys.argv[3] if len(sys.argv) > 3 else None
    
    parse_workshop(sheet_name, workshop_type, workshop_dates)