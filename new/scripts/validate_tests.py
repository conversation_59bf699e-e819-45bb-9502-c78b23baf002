#!/usr/bin/env python3
"""Simple test structure validation without execution"""

import os
from pathlib import Path

def check_file_structure():
    """Check that all expected test files exist"""
    print("📁 Checking Test File Structure")
    print("=" * 50)
    
    base_dir = Path(__file__).parent
    
    expected_files = [
        "tests/test_email_library.py",
        "tests/test_lead_email_sender.py", 
        "tests/test_email_campaigns.py",
        "tests/test_llm_generation.py",
        "core/email_library.py",
        "core/lead_selector.py",
        "core/email_campaign_manager.py",
        "core/enhanced_email_composer.py",
        "core/workflow_integration.py"
    ]
    
    all_exist = True
    for file_path in expected_files:
        full_path = base_dir / file_path
        if full_path.exists():
            size = full_path.stat().st_size
            print(f"✅ {file_path} ({size:,} bytes)")
        else:
            print(f"❌ {file_path} - MISSING")
            all_exist = False
    
    return all_exist

def check_test_content():
    """Check that test files have expected content"""
    print("\n🔍 Checking Test File Content")
    print("=" * 50)
    
    base_dir = Path(__file__).parent
    
    test_checks = [
        ("tests/test_email_library.py", ["class TestEmailLibrary", "def test_", "pytest"]),
        ("tests/test_lead_email_sender.py", ["class TestLeadEmailSender", "TEST_LEAD_ID = 13390", "ALLOWED_TEST_EMAIL"]),
        ("tests/test_email_campaigns.py", ["def main()", "def test_", "EMAIL Campaign Manager"]),
        ("tests/test_llm_generation.py", ["class TestLLMGeneration", "EmailGenerator", "def main()"]),
    ]
    
    all_good = True
    for file_path, expected_content in test_checks:
        full_path = base_dir / file_path
        if not full_path.exists():
            print(f"❌ {file_path} - File missing")
            all_good = False
            continue
            
        try:
            with open(full_path, 'r') as f:
                content = f.read()
            
            missing_content = []
            for expected in expected_content:
                if expected not in content:
                    missing_content.append(expected)
            
            if missing_content:
                print(f"⚠️ {file_path} - Missing: {', '.join(missing_content)}")
                all_good = False
            else:
                print(f"✅ {file_path} - Content looks good")
                
        except Exception as e:
            print(f"❌ {file_path} - Error reading: {e}")
            all_good = False
    
    return all_good

def check_removed_redundant_files():
    """Check that redundant files were properly handled"""
    print("\n🗑️ Checking Redundant File Removal")
    print("=" * 50)
    
    base_dir = Path(__file__).parent
    
    removed_files = [
        "tests/modules/email_tester.py",
        "tests/modules/gpt5_tester.py"
    ]
    
    all_handled = True
    for file_path in removed_files:
        full_path = base_dir / file_path
        if full_path.exists():
            try:
                with open(full_path, 'r') as f:
                    content = f.read().strip()
                
                if "This file has been removed" in content:
                    print(f"✅ {file_path} - Properly marked as removed")
                elif len(content) < 100:  # Very short file, likely our removal marker
                    print(f"✅ {file_path} - Appears to be replaced with removal marker")
                else:
                    print(f"⚠️ {file_path} - Still contains original content")
                    all_handled = False
            except Exception as e:
                print(f"❌ {file_path} - Error reading: {e}")
                all_handled = False
        else:
            print(f"✅ {file_path} - Completely removed")
    
    return all_handled

def main():
    """Main validation function"""
    print("🧪 Test Suite Structure Validation")
    print("=" * 60)
    
    structure_ok = check_file_structure()
    content_ok = check_test_content() 
    removed_ok = check_removed_redundant_files()
    
    print("\n" + "=" * 60)
    print("🏆 Validation Summary")
    print("=" * 60)
    
    print(f"📁 File Structure: {'✅ PASS' if structure_ok else '❌ FAIL'}")
    print(f"🔍 Test Content: {'✅ PASS' if content_ok else '❌ FAIL'}")  
    print(f"🗑️ Redundancy Removal: {'✅ PASS' if removed_ok else '❌ FAIL'}")
    
    overall_success = structure_ok and content_ok and removed_ok
    
    if overall_success:
        print("\n🎉 ALL VALIDATIONS PASSED!")
        print("✅ Test files are properly organized")
        print("✅ Required content is present")
        print("✅ Redundant files handled correctly")
        print("\n💡 To run tests:")
        print("   python3 tests/test_lead_email_sender.py")
        print("   python3 tests/test_email_campaigns.py")
        print("   python3 tests/test_llm_generation.py")
    else:
        print("\n⚠️ Some validations failed - check above for details")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)