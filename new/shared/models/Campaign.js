// Campaign model for Facebook campaign tracking and attribution
// Manages campaign performance, spend, and lead attribution

// Campaign status
export const CampaignStatus = {
  ACTIVE: 'active',
  PAUSED: 'paused',
  ENDED: 'ended',
  DRAFT: 'draft',
  ARCHIVED: 'archived'
};

// Campaign objectives
export const CampaignObjective = {
  LEAD_GENERATION: 'lead_generation',
  CONVERSIONS: 'conversions',
  TRAFFIC: 'traffic',
  REACH: 'reach',
  BRAND_AWARENESS: 'brand_awareness'
};

// Campaign types
export const CampaignType = {
  PROSPECTING: 'prospecting',
  RETARGETING: 'retargeting',
  LOOKALIKE: 'lookalike',
  INTEREST_BASED: 'interest_based',
  BEHAVIORAL: 'behavioral'
};

// Campaign schema
export const CampaignSchema = {
  tableName: 'campaigns',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    
    // Facebook campaign data
    facebook_campaign_id: { type: 'VARCHAR(255)', unique: true, index: true },
    facebook_account_id: { type: 'VARCHAR(255)', index: true, notNull: true },
    
    // Campaign details
    name: { type: 'VARCHAR(255)', notNull: true },
    objective: { type: 'ENUM', values: Object.values(CampaignObjective), notNull: true },
    status: { type: 'ENUM', values: Object.values(CampaignStatus), default: CampaignStatus.ACTIVE },
    campaign_type: { type: 'ENUM', values: Object.values(CampaignType), nullable: true },
    
    // Targeting information
    target_audience: { type: 'VARCHAR(255)', nullable: true },
    age_range: { type: 'VARCHAR(50)', nullable: true }, // "25-45"
    gender: { type: 'VARCHAR(20)', nullable: true },
    locations: { type: 'JSON', nullable: true }, // Array of targeted locations
    interests: { type: 'JSON', nullable: true }, // Array of interest targeting
    behaviors: { type: 'JSON', nullable: true }, // Array of behavior targeting
    custom_audiences: { type: 'JSON', nullable: true }, // Custom audience IDs
    lookalike_audiences: { type: 'JSON', nullable: true }, // Lookalike audience IDs
    
    // Budget and bidding
    daily_budget: { type: 'DECIMAL(10,2)', nullable: true },
    lifetime_budget: { type: 'DECIMAL(10,2)', nullable: true },
    bid_strategy: { type: 'VARCHAR(100)', nullable: true },
    
    // Date range
    start_date: { type: 'DATETIME', notNull: true },
    end_date: { type: 'DATETIME', nullable: true },
    
    // Performance metrics (updated from Facebook API)
    impressions: { type: 'BIGINT', default: 0 },
    reach: { type: 'BIGINT', default: 0 },
    clicks: { type: 'INTEGER', default: 0 },
    ctr: { type: 'DECIMAL(5,4)', default: 0 }, // Click-through rate
    cpc: { type: 'DECIMAL(10,2)', default: 0 }, // Cost per click
    cpm: { type: 'DECIMAL(10,2)', default: 0 }, // Cost per mille
    
    // Lead metrics
    leads_generated: { type: 'INTEGER', default: 0 },
    cpl: { type: 'DECIMAL(10,2)', default: 0 }, // Cost per lead
    qualified_leads: { type: 'INTEGER', default: 0 },
    cost_per_qualified_lead: { type: 'DECIMAL(10,2)', default: 0 },
    
    // Conversion metrics
    demos_scheduled: { type: 'INTEGER', default: 0 },
    demos_completed: { type: 'INTEGER', default: 0 },
    enrollments: { type: 'INTEGER', default: 0 },
    conversions: { type: 'INTEGER', default: 0 },
    conversion_rate: { type: 'DECIMAL(5,4)', default: 0 },
    
    // Revenue metrics
    total_spend: { type: 'DECIMAL(10,2)', default: 0 },
    revenue_generated: { type: 'DECIMAL(10,2)', default: 0 },
    roas: { type: 'DECIMAL(8,4)', default: 0 }, // Return on ad spend
    coca: { type: 'DECIMAL(10,2)', default: 0 }, // Cost of customer acquisition
    
    // Quality metrics
    relevance_score: { type: 'DECIMAL(3,2)', nullable: true },
    quality_ranking: { type: 'VARCHAR(20)', nullable: true },
    engagement_rate_ranking: { type: 'VARCHAR(20)', nullable: true },
    conversion_rate_ranking: { type: 'VARCHAR(20)', nullable: true },
    
    // Attribution and tracking
    utm_source: { type: 'VARCHAR(100)', default: 'facebook' },
    utm_medium: { type: 'VARCHAR(100)', default: 'cpc' },
    utm_campaign: { type: 'VARCHAR(255)', nullable: true },
    
    // Geographic performance
    top_performing_location: { type: 'VARCHAR(100)', nullable: true },
    location_performance: { type: 'JSON', nullable: true }, // Performance by location
    
    // Timing insights
    best_performing_time: { type: 'VARCHAR(50)', nullable: true },
    day_of_week_performance: { type: 'JSON', nullable: true },
    
    // Creative performance
    top_performing_creative: { type: 'VARCHAR(255)', nullable: true },
    creative_performance: { type: 'JSON', nullable: true },
    
    // Optimization notes
    optimization_notes: { type: 'TEXT', nullable: true },
    last_optimized: { type: 'DATETIME', nullable: true },
    optimization_score: { type: 'INTEGER', default: 0 }, // 0-100 optimization score
    
    // System tracking
    created_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    updated_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' },
    last_sync: { type: 'DATETIME', nullable: true }, // Last API sync
    
    // Performance flags
    is_top_performer: { type: 'BOOLEAN', default: false },
    needs_attention: { type: 'BOOLEAN', default: false },
    is_paused_by_system: { type: 'BOOLEAN', default: false }
  },
  
  indexes: [
    { columns: ['facebook_campaign_id'], unique: true },
    { columns: ['facebook_account_id'] },
    { columns: ['status'] },
    { columns: ['start_date'] },
    { columns: ['total_spend'] },
    { columns: ['leads_generated'] },
    { columns: ['conversion_rate'] },
    { columns: ['coca'] },
    { columns: ['is_top_performer'] },
    { columns: ['needs_attention'] }
  ]
};

// Daily campaign performance tracking
export const CampaignDailyStatsSchema = {
  tableName: 'campaign_daily_stats',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    campaign_id: { type: 'INTEGER', foreignKey: 'campaigns.id', notNull: true },
    date: { type: 'DATE', notNull: true },
    
    // Daily metrics
    impressions: { type: 'INTEGER', default: 0 },
    reach: { type: 'INTEGER', default: 0 },
    clicks: { type: 'INTEGER', default: 0 },
    spend: { type: 'DECIMAL(10,2)', default: 0 },
    leads: { type: 'INTEGER', default: 0 },
    qualified_leads: { type: 'INTEGER', default: 0 },
    demos_scheduled: { type: 'INTEGER', default: 0 },
    conversions: { type: 'INTEGER', default: 0 },
    revenue: { type: 'DECIMAL(10,2)', default: 0 },
    
    // Calculated metrics
    ctr: { type: 'DECIMAL(5,4)', default: 0 },
    cpc: { type: 'DECIMAL(10,2)', default: 0 },
    cpl: { type: 'DECIMAL(10,2)', default: 0 },
    conversion_rate: { type: 'DECIMAL(5,4)', default: 0 },
    coca: { type: 'DECIMAL(10,2)', default: 0 },
    roas: { type: 'DECIMAL(8,4)', default: 0 }
  },
  
  indexes: [
    { columns: ['campaign_id', 'date'], unique: true },
    { columns: ['date'] },
    { columns: ['spend'] },
    { columns: ['leads'] }
  ]
};

// Campaign class with business logic
export default class Campaign {
  constructor(data = {}) {
    Object.assign(this, data);
  }
  
  // Calculate cost per qualified lead
  getCostPerQualifiedLead() {
    if (this.qualified_leads === 0) return 0;
    return Math.round(this.total_spend / this.qualified_leads);
  }
  
  // Calculate lead qualification rate
  getQualificationRate() {
    if (this.leads_generated === 0) return 0;
    return Math.round((this.qualified_leads / this.leads_generated) * 100);
  }
  
  // Calculate demo-to-enrollment rate
  getDemoConversionRate() {
    if (this.demos_completed === 0) return 0;
    return Math.round((this.enrollments / this.demos_completed) * 100);
  }
  
  // Calculate overall conversion rate (leads to enrollments)
  getOverallConversionRate() {
    if (this.leads_generated === 0) return 0;
    return Math.round((this.enrollments / this.leads_generated) * 100);
  }
  
  // Get performance grade based on key metrics
  getPerformanceGrade() {
    const cplTarget = 200; // ₹200 target CPL
    const conversionTarget = 6; // 6% target conversion rate
    const cocaTarget = 4000; // ₹4000 target COCA
    
    let score = 0;
    
    // CPL scoring (30% weight)
    if (this.cpl <= cplTarget * 0.8) score += 30;
    else if (this.cpl <= cplTarget) score += 20;
    else if (this.cpl <= cplTarget * 1.2) score += 10;
    
    // Conversion rate scoring (40% weight)
    if (this.conversion_rate >= conversionTarget * 1.2) score += 40;
    else if (this.conversion_rate >= conversionTarget) score += 30;
    else if (this.conversion_rate >= conversionTarget * 0.8) score += 20;
    else if (this.conversion_rate >= conversionTarget * 0.5) score += 10;
    
    // COCA scoring (30% weight)
    if (this.coca <= cocaTarget * 0.8) score += 30;
    else if (this.coca <= cocaTarget) score += 20;
    else if (this.coca <= cocaTarget * 1.2) score += 10;
    
    if (score >= 80) return 'A';
    if (score >= 65) return 'B';
    if (score >= 50) return 'C';
    if (score >= 35) return 'D';
    return 'F';
  }
  
  // Check if campaign needs attention
  needsAttention() {
    const highCPL = this.cpl > 300; // High cost per lead
    const lowConversion = this.conversion_rate < 3; // Low conversion rate
    const highCOCA = this.coca > 6000; // High customer acquisition cost
    const lowROAS = this.roas < 2; // Low return on ad spend
    
    return highCPL || lowConversion || highCOCA || lowROAS;
  }
  
  // Get optimization recommendations
  getOptimizationRecommendations() {
    const recommendations = [];
    
    if (this.cpl > 300) {
      recommendations.push('HIGH CPL: Consider pausing or optimizing targeting');
    }
    
    if (this.conversion_rate < 3) {
      recommendations.push('LOW CONVERSION: Review lead quality and sales process');
    }
    
    if (this.coca > 6000) {
      recommendations.push('HIGH COCA: Reduce spend or improve targeting');
    }
    
    if (this.roas < 2) {
      recommendations.push('LOW ROAS: Campaign not profitable, needs optimization');
    }
    
    if (this.ctr < 1) {
      recommendations.push('LOW CTR: Refresh creative or improve targeting');
    }
    
    if (this.relevance_score && this.relevance_score < 6) {
      recommendations.push('LOW RELEVANCE: Improve ad relevance to audience');
    }
    
    return recommendations;
  }
  
  // Calculate daily burn rate
  getDailyBurnRate() {
    if (!this.start_date) return 0;
    
    const startDate = new Date(this.start_date);
    const now = new Date();
    const daysRunning = Math.max(1, Math.ceil((now - startDate) / (1000 * 60 * 60 * 24)));
    
    return Math.round(this.total_spend / daysRunning);
  }
  
  // Get campaign health status
  getHealthStatus() {
    const grade = this.getPerformanceGrade();
    
    if (['A', 'B'].includes(grade)) return 'healthy';
    if (grade === 'C') return 'warning';
    return 'critical';
  }
  
  // Calculate projected monthly spend
  getProjectedMonthlySpend() {
    const dailyBurn = this.getDailyBurnRate();
    return dailyBurn * 30;
  }
  
  // Get top performing demographics
  getTopDemographics() {
    // This would be calculated from detailed performance data
    return {
      age: this.age_range || 'Unknown',
      location: this.top_performing_location || 'Unknown',
      time: this.best_performing_time || 'Unknown'
    };
  }
  
  // Check if campaign is profitable
  isProfitable() {
    return this.roas > 1 && this.revenue_generated > this.total_spend;
  }
  
  // Get status color for UI
  getStatusColor() {
    const colors = {
      [CampaignStatus.ACTIVE]: 'bg-green-100 text-green-800',
      [CampaignStatus.PAUSED]: 'bg-yellow-100 text-yellow-800',
      [CampaignStatus.ENDED]: 'bg-gray-100 text-gray-800',
      [CampaignStatus.DRAFT]: 'bg-blue-100 text-blue-800',
      [CampaignStatus.ARCHIVED]: 'bg-gray-100 text-gray-600'
    };
    return colors[this.status] || 'bg-gray-100 text-gray-800';
  }
  
  // Get performance color based on grade
  getPerformanceColor() {
    const grade = this.getPerformanceGrade();
    const colors = {
      'A': 'text-green-600',
      'B': 'text-blue-600', 
      'C': 'text-yellow-600',
      'D': 'text-orange-600',
      'F': 'text-red-600'
    };
    return colors[grade] || 'text-gray-600';
  }
}