// Certificate model for bootcamp completion certificates
// Manages certificate generation, delivery, and tracking

// Certificate types
export const CertificateType = {
  AI_ESSENTIALS: 'ai_essentials',
  AI_PRACTITIONER: 'ai_practitioner',
  ADVANCED_AI: 'advanced_ai',
  EXECUTIVE_AI: 'executive_ai',
  CUSTOM: 'custom'
};

// Certificate status
export const CertificateStatus = {
  PENDING: 'pending',
  GENERATED: 'generated',
  SENT: 'sent',
  DELIVERED: 'delivered',
  FAILED: 'failed',
  REVOKED: 'revoked'
};

// Certificate schema
export const CertificateSchema = {
  tableName: 'certificates',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    
    // Certificate identification
    certificate_id: { type: 'VARCHAR(100)', unique: true, index: true }, // Unique certificate ID
    certificate_number: { type: 'VARCHAR(50)', unique: true, index: true }, // Sequential number
    
    // Student information
    user_id: { type: 'INTEGER', foreignKey: 'users.id', notNull: true },
    student_name: { type: 'VARCHAR(255)', notNull: true },
    student_email: { type: 'VARCHAR(255)', notNull: true },
    
    // Program information
    workshop_id: { type: 'INTEGER', foreignKey: 'workshops.id', nullable: true },
    program_name: { type: 'VARCHAR(255)', notNull: true },
    certificate_type: { type: 'ENUM', values: Object.values(CertificateType), notNull: true },
    completion_date: { type: 'DATE', notNull: true },
    
    // Certificate details
    template_used: { type: 'VARCHAR(255)', nullable: true },
    grade_achieved: { type: 'VARCHAR(20)', nullable: true }, // A+, A, B+, B, C
    final_score: { type: 'DECIMAL(5,2)', nullable: true }, // Percentage score
    skills_demonstrated: { type: 'JSON', nullable: true }, // Array of skills
    
    // Instructor and validation
    instructor_name: { type: 'VARCHAR(255)', nullable: true },
    instructor_signature: { type: 'VARCHAR(500)', nullable: true }, // Signature image path
    validation_code: { type: 'VARCHAR(100)', unique: true, index: true },
    
    // Certificate generation
    status: { type: 'ENUM', values: Object.values(CertificateStatus), default: CertificateStatus.PENDING },
    generated_at: { type: 'DATETIME', nullable: true },
    file_path: { type: 'VARCHAR(500)', nullable: true }, // Path to generated PDF
    file_size: { type: 'INTEGER', nullable: true }, // File size in bytes
    
    // Email delivery
    email_sent_at: { type: 'DATETIME', nullable: true },
    email_delivered_at: { type: 'DATETIME', nullable: true },
    email_opened_at: { type: 'DATETIME', nullable: true },
    email_subject: { type: 'VARCHAR(255)', nullable: true },
    email_template: { type: 'VARCHAR(100)', nullable: true },
    
    // Social sharing
    social_share_enabled: { type: 'BOOLEAN', default: true },
    linkedin_shared: { type: 'BOOLEAN', default: false },
    linkedin_shared_at: { type: 'DATETIME', nullable: true },
    twitter_shared: { type: 'BOOLEAN', default: false },
    twitter_shared_at: { type: 'DATETIME', nullable: true },
    
    // Referral integration
    referral_code_embedded: { type: 'VARCHAR(50)', nullable: true },
    referral_discount_percentage: { type: 'DECIMAL(5,2)', default: 15.00 },
    
    // Verification and security
    verification_url: { type: 'VARCHAR(500)', nullable: true },
    blockchain_hash: { type: 'VARCHAR(128)', nullable: true }, // For blockchain verification
    is_verified: { type: 'BOOLEAN', default: true },
    
    // Analytics
    view_count: { type: 'INTEGER', default: 0 },
    download_count: { type: 'INTEGER', default: 0 },
    verification_count: { type: 'INTEGER', default: 0 },
    
    // System tracking
    created_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    updated_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' },
    
    // Error tracking
    generation_attempts: { type: 'INTEGER', default: 0 },
    last_error: { type: 'TEXT', nullable: true },
    retry_after: { type: 'DATETIME', nullable: true }
  },
  
  indexes: [
    { columns: ['certificate_id'], unique: true },
    { columns: ['certificate_number'], unique: true },
    { columns: ['validation_code'], unique: true },
    { columns: ['user_id'] },
    { columns: ['workshop_id'] },
    { columns: ['status'] },
    { columns: ['completion_date'] },
    { columns: ['generated_at'] },
    { columns: ['email_sent_at'] }
  ]
};

// Certificate templates configuration
export const CertificateTemplateSchema = {
  tableName: 'certificate_templates',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    name: { type: 'VARCHAR(255)', unique: true, notNull: true },
    certificate_type: { type: 'ENUM', values: Object.values(CertificateType), notNull: true },
    template_path: { type: 'VARCHAR(500)', notNull: true },
    background_image: { type: 'VARCHAR(500)', nullable: true },
    font_family: { type: 'VARCHAR(100)', default: 'Arial' },
    font_size_name: { type: 'INTEGER', default: 24 },
    font_size_program: { type: 'INTEGER', default: 18 },
    font_size_date: { type: 'INTEGER', default: 14 },
    
    // Layout configuration
    name_position_x: { type: 'INTEGER', default: 400 },
    name_position_y: { type: 'INTEGER', default: 300 },
    program_position_x: { type: 'INTEGER', default: 400 },
    program_position_y: { type: 'INTEGER', default: 250 },
    date_position_x: { type: 'INTEGER', default: 400 },
    date_position_y: { type: 'INTEGER', default: 200 },
    
    is_active: { type: 'BOOLEAN', default: true },
    created_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' }
  }
};

// Certificate class with business logic
export default class Certificate {
  constructor(data = {}) {
    Object.assign(this, data);
  }
  
  // Generate unique certificate ID
  static generateCertificateId() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `CERT_${timestamp}_${random}`.toUpperCase();
  }
  
  // Generate validation code
  static generateValidationCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 12; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  
  // Get formatted certificate number
  getFormattedCertificateNumber() {
    if (!this.certificate_number) return 'Pending';
    return `MAI-${this.certificate_number}`;
  }
  
  // Get verification URL
  getVerificationUrl() {
    if (!this.validation_code) return null;
    return `https://modernaipro.com/verify/${this.validation_code}`;
  }
  
  // Get LinkedIn sharing URL
  getLinkedInShareUrl() {
    const baseUrl = 'https://www.linkedin.com/sharing/share-offsite/';
    const verificationUrl = this.getVerificationUrl();
    const text = `I'm excited to share that I've completed the ${this.program_name} program!`;
    
    if (!verificationUrl) return null;
    
    return `${baseUrl}?url=${encodeURIComponent(verificationUrl)}&title=${encodeURIComponent(text)}`;
  }
  
  // Get Twitter sharing URL
  getTwitterShareUrl() {
    const baseUrl = 'https://twitter.com/intent/tweet';
    const verificationUrl = this.getVerificationUrl();
    const text = `Just completed the ${this.program_name} program! 🎓 #AI #MachineLearning #ModernAI`;
    
    if (!verificationUrl) return null;
    
    return `${baseUrl}?text=${encodeURIComponent(text)}&url=${encodeURIComponent(verificationUrl)}`;
  }
  
  // Check if certificate can be generated
  canGenerate() {
    return this.status === CertificateStatus.PENDING &&
           this.student_name &&
           this.program_name &&
           this.completion_date;
  }
  
  // Check if certificate can be sent
  canSend() {
    return this.status === CertificateStatus.GENERATED &&
           this.file_path &&
           this.student_email;
  }
  
  // Get days since completion
  getDaysSinceCompletion() {
    if (!this.completion_date) return null;
    
    const completionDate = new Date(this.completion_date);
    const now = new Date();
    return Math.ceil((now - completionDate) / (1000 * 60 * 60 * 24));
  }
  
  // Check if certificate is overdue (should be sent within 24 hours)
  isOverdue() {
    const daysSince = this.getDaysSinceCompletion();
    return daysSince !== null && 
           daysSince > 1 && 
           this.status !== CertificateStatus.SENT &&
           this.status !== CertificateStatus.DELIVERED;
  }
  
  // Get delivery status text
  getDeliveryStatusText() {
    switch (this.status) {
      case CertificateStatus.PENDING:
        return 'Certificate generation pending';
      case CertificateStatus.GENERATED:
        return 'Certificate ready to send';
      case CertificateStatus.SENT:
        return this.email_delivered_at ? 'Delivered' : 'Email sent';
      case CertificateStatus.DELIVERED:
        return this.email_opened_at ? 'Opened by recipient' : 'Delivered';
      case CertificateStatus.FAILED:
        return 'Delivery failed - requires attention';
      case CertificateStatus.REVOKED:
        return 'Certificate revoked';
      default:
        return 'Unknown status';
    }
  }
  
  // Get status color for UI
  getStatusColor() {
    const colors = {
      [CertificateStatus.PENDING]: 'bg-yellow-100 text-yellow-800',
      [CertificateStatus.GENERATED]: 'bg-blue-100 text-blue-800',
      [CertificateStatus.SENT]: 'bg-green-100 text-green-800',
      [CertificateStatus.DELIVERED]: 'bg-emerald-100 text-emerald-800',
      [CertificateStatus.FAILED]: 'bg-red-100 text-red-800',
      [CertificateStatus.REVOKED]: 'bg-gray-100 text-gray-800'
    };
    return colors[this.status] || 'bg-gray-100 text-gray-800';
  }
  
  // Calculate processing time (completion to delivery)
  getProcessingTimeHours() {
    if (!this.completion_date || !this.email_sent_at) return null;
    
    const completionDate = new Date(this.completion_date);
    const sentDate = new Date(this.email_sent_at);
    return Math.round((sentDate - completionDate) / (1000 * 60 * 60));
  }
  
  // Check if meets SLA (24 hour delivery target)
  meetsSLA() {
    const processingTime = this.getProcessingTimeHours();
    return processingTime !== null && processingTime <= 24;
  }
  
  // Get certificate type badge
  getTypeBadge() {
    const badges = {
      [CertificateType.AI_ESSENTIALS]: '🎯 AI Essentials',
      [CertificateType.AI_PRACTITIONER]: '⚡ AI Practitioner',
      [CertificateType.ADVANCED_AI]: '🚀 Advanced AI',
      [CertificateType.EXECUTIVE_AI]: '👑 Executive AI',
      [CertificateType.CUSTOM]: '🎨 Custom Program'
    };
    return badges[this.certificate_type] || '📜 Certificate';
  }
  
  // Get estimated referral value
  getEstimatedReferralValue() {
    if (!this.referral_code_embedded) return 0;
    
    // Assume 10% of certificate recipients make referrals
    // and average bootcamp price is ₹15,000
    const referralRate = 0.1;
    const avgBootcampPrice = 15000;
    const discountAmount = avgBootcampPrice * (this.referral_discount_percentage / 100);
    
    return Math.round(referralRate * discountAmount);
  }
  
  // Format grade for display
  getFormattedGrade() {
    if (!this.grade_achieved) return 'Completed';
    return `Grade: ${this.grade_achieved}`;
  }
  
  // Get skills as formatted list
  getFormattedSkills() {
    if (!this.skills_demonstrated || !Array.isArray(this.skills_demonstrated)) {
      return 'General AI skills';
    }
    return this.skills_demonstrated.join(', ');
  }
}