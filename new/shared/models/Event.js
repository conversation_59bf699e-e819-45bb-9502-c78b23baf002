// Event model for bootcamps, webinars, and workshops
// Extended from Workshop model with additional event management features

// Import from Workshop model
import { EventType, WorkshopStatus } from './Workshop.js';

// Event priority levels
export const EventPriority = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// Event marketing status
export const MarketingStatus = {
  NOT_STARTED: 'not_started',
  IN_PROGRESS: 'in_progress',
  PAUSED: 'paused',
  COMPLETED: 'completed'
};

// Registration status
export const RegistrationStatus = {
  OPEN: 'open',
  CLOSING_SOON: 'closing_soon',
  FULL: 'full',
  CLOSED: 'closed',
  WAITLIST: 'waitlist'
};

// Event schema (extends Workshop schema)
export const EventSchema = {
  tableName: 'events',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    
    // Basic event information
    title: { type: 'VARCHAR(255)', notNull: true },
    description: { type: 'TEXT', nullable: true },
    short_description: { type: 'VARCHAR(500)', nullable: true }, // For cards/listings
    event_type: { type: 'ENUM', values: Object.values(EventType), notNull: true },
    
    // Scheduling and duration
    start_datetime: { type: 'DATETIME', notNull: true },
    end_datetime: { type: 'DATETIME', notNull: true },
    timezone: { type: 'VARCHAR(50)', default: 'Asia/Kolkata' },
    duration_minutes: { type: 'INTEGER', nullable: true },
    
    // Location and delivery
    is_online: { type: 'BOOLEAN', default: true },
    venue_name: { type: 'VARCHAR(255)', nullable: true },
    venue_address: { type: 'TEXT', nullable: true },
    meeting_platform: { type: 'VARCHAR(100)', nullable: true }, // Zoom, Teams, etc.
    meeting_link: { type: 'VARCHAR(500)', nullable: true },
    meeting_passcode: { type: 'VARCHAR(50)', nullable: true },
    
    // Capacity and registration
    max_capacity: { type: 'INTEGER', nullable: true },
    min_capacity: { type: 'INTEGER', default: 5 },
    current_registrations: { type: 'INTEGER', default: 0 },
    waitlist_count: { type: 'INTEGER', default: 0 },
    registration_status: { type: 'ENUM', values: Object.values(RegistrationStatus), default: RegistrationStatus.OPEN },
    
    // Registration management
    registration_opens: { type: 'DATETIME', nullable: true },
    registration_closes: { type: 'DATETIME', nullable: true },
    early_bird_deadline: { type: 'DATETIME', nullable: true },
    
    // Pricing
    is_free: { type: 'BOOLEAN', default: false },
    price: { type: 'DECIMAL(10,2)', default: 0 },
    early_bird_price: { type: 'DECIMAL(10,2)', nullable: true },
    currency: { type: 'VARCHAR(3)', default: 'INR' },
    
    // Content and speakers
    speaker_name: { type: 'VARCHAR(255)', nullable: true },
    speaker_bio: { type: 'TEXT', nullable: true },
    speaker_image: { type: 'VARCHAR(500)', nullable: true },
    speaker_linkedin: { type: 'VARCHAR(500)', nullable: true },
    
    // Additional speakers for panels/multi-speaker events
    additional_speakers: { type: 'JSON', nullable: true }, // Array of speaker objects
    
    // Event materials
    agenda: { type: 'JSON', nullable: true }, // Array of agenda items
    materials_link: { type: 'VARCHAR(500)', nullable: true },
    slides_link: { type: 'VARCHAR(500)', nullable: true },
    recording_link: { type: 'VARCHAR(500)', nullable: true },
    resources: { type: 'JSON', nullable: true }, // Array of resource links
    
    // Prerequisites and target audience
    prerequisites: { type: 'TEXT', nullable: true },
    target_audience: { type: 'VARCHAR(500)', nullable: true },
    difficulty_level: { type: 'VARCHAR(50)', nullable: true }, // beginner, intermediate, advanced
    learning_objectives: { type: 'JSON', nullable: true }, // Array of objectives
    
    // Marketing and promotion
    marketing_status: { type: 'ENUM', values: Object.values(MarketingStatus), default: MarketingStatus.NOT_STARTED },
    promotional_image: { type: 'VARCHAR(500)', nullable: true },
    banner_image: { type: 'VARCHAR(500)', nullable: true },
    landing_page_url: { type: 'VARCHAR(500)', nullable: true },
    registration_url: { type: 'VARCHAR(500)', nullable: true },
    
    // Social media and campaigns
    facebook_event_id: { type: 'VARCHAR(255)', nullable: true },
    linkedin_event_id: { type: 'VARCHAR(255)', nullable: true },
    campaign_ids: { type: 'JSON', nullable: true }, // Array of associated campaign IDs
    hashtags: { type: 'JSON', nullable: true }, // Array of hashtags
    
    // Status and priority
    status: { type: 'ENUM', values: Object.values(WorkshopStatus), default: WorkshopStatus.DRAFT },
    priority: { type: 'ENUM', values: Object.values(EventPriority), default: EventPriority.MEDIUM },
    is_featured: { type: 'BOOLEAN', default: false },
    is_recurring: { type: 'BOOLEAN', default: false },
    
    // Analytics and performance
    page_views: { type: 'INTEGER', default: 0 },
    registration_conversion_rate: { type: 'DECIMAL(5,4)', default: 0 },
    attendance_rate: { type: 'DECIMAL(5,4)', default: 0 },
    satisfaction_score: { type: 'DECIMAL(3,2)', nullable: true }, // 1-5 rating
    nps_score: { type: 'DECIMAL(4,2)', nullable: true }, // Net Promoter Score
    
    // Follow-up and conversion
    follow_up_sequence: { type: 'VARCHAR(100)', nullable: true },
    lead_magnet: { type: 'VARCHAR(255)', nullable: true },
    upsell_opportunity: { type: 'VARCHAR(255)', nullable: true },
    
    // Certificate and completion
    issues_certificate: { type: 'BOOLEAN', default: false },
    certificate_template: { type: 'VARCHAR(255)', nullable: true },
    completion_criteria: { type: 'JSON', nullable: true }, // Attendance %, participation, etc.
    
    // Notifications and reminders
    reminder_sequence: { type: 'JSON', nullable: true }, // Array of reminder timings
    confirmation_email_sent: { type: 'BOOLEAN', default: false },
    reminder_24h_sent: { type: 'BOOLEAN', default: false },
    reminder_1h_sent: { type: 'BOOLEAN', default: false },
    
    // System tracking
    created_by: { type: 'INTEGER', foreignKey: 'users.id', nullable: true },
    created_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    updated_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' },
    published_at: { type: 'DATETIME', nullable: true },
    
    // Quality and approval
    needs_approval: { type: 'BOOLEAN', default: true },
    approved_by: { type: 'INTEGER', foreignKey: 'users.id', nullable: true },
    approved_at: { type: 'DATETIME', nullable: true },
    
    // External integrations
    calendar_event_id: { type: 'VARCHAR(255)', nullable: true },
    zoom_meeting_id: { type: 'VARCHAR(255)', nullable: true },
    eventbrite_id: { type: 'VARCHAR(255)', nullable: true }
  },
  
  indexes: [
    { columns: ['event_type'] },
    { columns: ['start_datetime'] },
    { columns: ['status'] },
    { columns: ['registration_status'] },
    { columns: ['is_featured'] },
    { columns: ['priority'] },
    { columns: ['created_by'] },
    { columns: ['marketing_status'] },
    { columns: ['max_capacity'] },
    { columns: ['current_registrations'] }
  ]
};

// Event registrations tracking
export const EventRegistrationSchema = {
  tableName: 'event_registrations',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    event_id: { type: 'INTEGER', foreignKey: 'events.id', notNull: true },
    user_id: { type: 'INTEGER', foreignKey: 'users.id', nullable: true }, // Null for guest registrations
    
    // Registration details
    first_name: { type: 'VARCHAR(255)', notNull: true },
    last_name: { type: 'VARCHAR(255)', nullable: true },
    email: { type: 'VARCHAR(255)', notNull: true },
    phone: { type: 'VARCHAR(20)', nullable: true },
    company: { type: 'VARCHAR(255)', nullable: true },
    title: { type: 'VARCHAR(255)', nullable: true },
    
    // Registration metadata
    registration_date: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    registration_source: { type: 'VARCHAR(100)', nullable: true }, // website, social, referral
    utm_source: { type: 'VARCHAR(100)', nullable: true },
    utm_campaign: { type: 'VARCHAR(255)', nullable: true },
    referrer_email: { type: 'VARCHAR(255)', nullable: true },
    
    // Payment and pricing
    amount_paid: { type: 'DECIMAL(10,2)', default: 0 },
    payment_status: { type: 'VARCHAR(50)', default: 'pending' },
    discount_applied: { type: 'DECIMAL(5,2)', default: 0 },
    discount_code: { type: 'VARCHAR(50)', nullable: true },
    
    // Attendance tracking
    registered_for_reminders: { type: 'BOOLEAN', default: true },
    attended: { type: 'BOOLEAN', default: false },
    attendance_duration_minutes: { type: 'INTEGER', default: 0 },
    joined_at: { type: 'DATETIME', nullable: true },
    left_at: { type: 'DATETIME', nullable: true },
    
    // Engagement tracking
    asked_questions: { type: 'INTEGER', default: 0 },
    participated_in_polls: { type: 'INTEGER', default: 0 },
    chat_messages: { type: 'INTEGER', default: 0 },
    engagement_score: { type: 'INTEGER', default: 0 }, // 0-100
    
    // Follow-up and conversion
    feedback_submitted: { type: 'BOOLEAN', default: false },
    satisfaction_rating: { type: 'INTEGER', nullable: true }, // 1-5
    nps_score: { type: 'INTEGER', nullable: true }, // 0-10
    follow_up_interest: { type: 'VARCHAR(255)', nullable: true },
    
    // Lead qualification
    is_qualified_lead: { type: 'BOOLEAN', default: false },
    lead_score: { type: 'INTEGER', default: 0 },
    sales_follow_up_required: { type: 'BOOLEAN', default: false },
    assigned_to_sales: { type: 'INTEGER', foreignKey: 'users.id', nullable: true },
    
    // Certificate and completion
    certificate_eligible: { type: 'BOOLEAN', default: false },
    certificate_issued: { type: 'BOOLEAN', default: false },
    certificate_id: { type: 'INTEGER', foreignKey: 'certificates.id', nullable: true },
    
    // Status tracking
    registration_status: { type: 'VARCHAR(50)', default: 'confirmed' }, // confirmed, cancelled, no_show
    cancellation_reason: { type: 'VARCHAR(255)', nullable: true },
    cancelled_at: { type: 'DATETIME', nullable: true }
  },
  
  indexes: [
    { columns: ['event_id'] },
    { columns: ['user_id'] },
    { columns: ['email'] },
    { columns: ['registration_date'] },
    { columns: ['attended'] },
    { columns: ['payment_status'] },
    { columns: ['is_qualified_lead'] },
    { columns: ['registration_status'] }
  ]
};

// Event class with business logic
export default class Event {
  constructor(data = {}) {
    Object.assign(this, data);
  }
  
  // Get capacity utilization percentage
  getCapacityUtilization() {
    if (!this.max_capacity) return 0;
    return Math.round((this.current_registrations / this.max_capacity) * 100);
  }
  
  // Get registration status based on capacity
  getCalculatedRegistrationStatus() {
    const utilization = this.getCapacityUtilization();
    
    if (utilization >= 100) return RegistrationStatus.FULL;
    if (utilization >= 90) return RegistrationStatus.CLOSING_SOON;
    if (this.registration_closes && new Date() > new Date(this.registration_closes)) {
      return RegistrationStatus.CLOSED;
    }
    return RegistrationStatus.OPEN;
  }
  
  // Check if early bird pricing is available
  isEarlyBirdAvailable() {
    if (!this.early_bird_deadline || !this.early_bird_price) return false;
    return new Date() <= new Date(this.early_bird_deadline);
  }
  
  // Get current price (early bird or regular)
  getCurrentPrice() {
    if (this.is_free) return 0;
    if (this.isEarlyBirdAvailable()) return this.early_bird_price || this.price;
    return this.price;
  }
  
  // Get savings from early bird pricing
  getEarlyBirdSavings() {
    if (!this.isEarlyBirdAvailable() || !this.early_bird_price) return 0;
    return this.price - this.early_bird_price;
  }
  
  // Get time until event starts
  getTimeUntilStart() {
    const startTime = new Date(this.start_datetime);
    const now = new Date();
    const diffMs = startTime - now;
    
    if (diffMs <= 0) return { text: 'Started', isPast: true };
    
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return { text: `${days} day${days > 1 ? 's' : ''}`, isPast: false };
    if (hours > 0) return { text: `${hours} hour${hours > 1 ? 's' : ''}`, isPast: false };
    return { text: `${minutes} minute${minutes > 1 ? 's' : ''}`, isPast: false };
  }
  
  // Format event date and time
  getFormattedDateTime() {
    const start = new Date(this.start_datetime);
    const end = new Date(this.end_datetime);
    
    const dateOptions = { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric',
      year: start.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
    };
    
    const timeOptions = { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    };
    
    const dateStr = start.toLocaleDateString('en-IN', dateOptions);
    const startTimeStr = start.toLocaleTimeString('en-IN', timeOptions);
    const endTimeStr = end.toLocaleTimeString('en-IN', timeOptions);
    
    return {
      date: dateStr,
      time: `${startTimeStr} - ${endTimeStr}`,
      timezone: this.timezone
    };
  }
  
  // Calculate expected revenue
  getExpectedRevenue() {
    const avgPrice = this.getCurrentPrice();
    const registrationRate = 0.7; // Assume 70% registration rate from page views
    const attendanceRate = 0.8; // Assume 80% attendance rate
    
    if (this.max_capacity) {
      return Math.round(this.max_capacity * registrationRate * attendanceRate * avgPrice);
    }
    
    return Math.round(this.current_registrations * attendanceRate * avgPrice);
  }
  
  // Get marketing urgency level
  getMarketingUrgency() {
    const timeUntil = this.getTimeUntilStart();
    const utilization = this.getCapacityUtilization();
    
    if (timeUntil.isPast) return 'past';
    if (utilization >= 90) return 'low'; // Almost full
    
    const daysUntil = Math.ceil((new Date(this.start_datetime) - new Date()) / (1000 * 60 * 60 * 24));
    
    if (daysUntil <= 3 && utilization < 50) return 'critical';
    if (daysUntil <= 7 && utilization < 70) return 'high';
    if (daysUntil <= 14 && utilization < 80) return 'medium';
    return 'low';
  }
  
  // Get status color for UI
  getStatusColor() {
    const colors = {
      [WorkshopStatus.DRAFT]: 'bg-gray-100 text-gray-800',
      [WorkshopStatus.OPEN]: 'bg-green-100 text-green-800',
      [WorkshopStatus.FILLING_FAST]: 'bg-orange-100 text-orange-800',
      [WorkshopStatus.ALMOST_FULL]: 'bg-red-100 text-red-800',
      [WorkshopStatus.FULL]: 'bg-red-100 text-red-800',
      [WorkshopStatus.IN_PROGRESS]: 'bg-blue-100 text-blue-800',
      [WorkshopStatus.COMPLETED]: 'bg-emerald-100 text-emerald-800',
      [WorkshopStatus.CANCELLED]: 'bg-gray-100 text-gray-600'
    };
    return colors[this.status] || 'bg-gray-100 text-gray-800';
  }
  
  // Get priority color for UI
  getPriorityColor() {
    const colors = {
      [EventPriority.LOW]: 'text-gray-600',
      [EventPriority.MEDIUM]: 'text-blue-600',
      [EventPriority.HIGH]: 'text-orange-600',
      [EventPriority.CRITICAL]: 'text-red-600'
    };
    return colors[this.priority] || 'text-gray-600';
  }
  
  // Check if event needs immediate attention
  needsAttention() {
    const urgency = this.getMarketingUrgency();
    const utilization = this.getCapacityUtilization();
    
    return urgency === 'critical' || 
           (urgency === 'high' && utilization < 30) ||
           this.priority === EventPriority.CRITICAL;
  }
  
  // Get recommended actions
  getRecommendedActions() {
    const actions = [];
    const urgency = this.getMarketingUrgency();
    const utilization = this.getCapacityUtilization();
    
    if (urgency === 'critical') {
      actions.push('URGENT: Increase marketing spend immediately');
      actions.push('Send promotional emails to entire database');
      actions.push('Create social media push campaign');
    }
    
    if (utilization < 30 && urgency === 'high') {
      actions.push('Consider targeted LinkedIn ads');
      actions.push('Reach out to past attendees for referrals');
    }
    
    if (utilization >= 90) {
      actions.push('Prepare waitlist management');
      actions.push('Consider increasing capacity if possible');
    }
    
    if (!this.confirmation_email_sent && this.current_registrations > 0) {
      actions.push('Send confirmation emails to registrants');
    }
    
    return actions;
  }
  
  // Calculate conversion rate from page views to registrations
  getConversionRate() {
    if (!this.page_views || this.page_views === 0) return 0;
    return Math.round((this.current_registrations / this.page_views) * 100 * 100) / 100; // 2 decimal places
  }
  
  // Get event type icon
  getTypeIcon() {
    const icons = {
      [EventType.BOOTCAMP]: '🎓',
      [EventType.WORKSHOP]: '🛠️',
      [EventType.WEBINAR]: '📹',
      [EventType.MASTERCLASS]: '👑'
    };
    return icons[this.event_type] || '📅';
  }
}