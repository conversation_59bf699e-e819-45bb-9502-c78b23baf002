// KapiLead model for enterprise pipeline management
// Tracks high-value enterprise deals and cross-sell opportunities

// Kapi deal stages
export const KapiStage = {
  DISCOVERY: 'discovery',
  QUALIFIED: 'qualified',
  DEMO_SCHEDULED: 'demo_scheduled',
  DEMO_COMPLETED: 'demo_completed',
  PROPOSAL_SENT: 'proposal_sent',
  NEGOTIATION: 'negotiation',
  LEGAL_REVIEW: 'legal_review',
  CLOSED_WON: 'closed_won',
  CLOSED_LOST: 'closed_lost'
};

// Kapi product interests
export const KapiProduct = {
  ENTERPRISE_AI_PLATFORM: 'enterprise_ai_platform',
  CUSTOM_LLM_TRAINING: 'custom_llm_training',
  AI_CONSULTING_PACKAGE: 'ai_consulting_package',
  AI_IMPLEMENTATION_FRAMEWORK: 'ai_implementation_framework',
  ENTERPRISE_AI_STRATEGY: 'enterprise_ai_strategy',
  TEAM_AI_BOOTCAMP: 'team_ai_bootcamp',
  EXECUTIVE_AI_PROGRAM: 'executive_ai_program'
};

// Lead sources for Ka<PERSON>
export const KapiLeadSource = {
  MODERN_AI_GRADUATE: 'modern_ai_graduate',
  DIRECT_INQUIRY: 'direct_inquiry',
  REFERRAL: 'referral',
  LINKEDIN_OUTREACH: 'linkedin_outreach',
  CONFERENCE: 'conference',
  WEBINAR: 'webinar',
  CONTENT_MARKETING: 'content_marketing',
  PARTNER_REFERRAL: 'partner_referral'
};

// Company sizes for Kapi targeting
export const CompanySize = {
  STARTUP: '10-50',
  SMALL: '51-100', 
  MEDIUM: '101-500',
  LARGE: '501-1000',
  ENTERPRISE: '1001-5000',
  MEGA_CORP: '5000+'
};

// Kapi lead schema
export const KapiLeadSchema = {
  tableName: 'kapi_leads',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    
    // Company information
    company_name: { type: 'VARCHAR(255)', notNull: true },
    company_size: { type: 'ENUM', values: Object.values(CompanySize), nullable: true },
    industry: { type: 'VARCHAR(100)', nullable: true },
    annual_revenue: { type: 'BIGINT', nullable: true }, // In INR
    employee_count: { type: 'INTEGER', nullable: true },
    headquarters_location: { type: 'VARCHAR(255)', nullable: true },
    website: { type: 'VARCHAR(500)', nullable: true },
    
    // Contact information
    contact_name: { type: 'VARCHAR(255)', notNull: true },
    title: { type: 'VARCHAR(255)', nullable: true },
    email: { type: 'VARCHAR(255)', index: true, notNull: true },
    phone: { type: 'VARCHAR(20)', nullable: true },
    linkedin_profile: { type: 'VARCHAR(500)', nullable: true },
    
    // Modern AI connection
    is_modern_ai_graduate: { type: 'BOOLEAN', default: false },
    bootcamp_completed_date: { type: 'DATE', nullable: true },
    original_lead_id: { type: 'INTEGER', foreignKey: 'leads.id', nullable: true }, // Reference to original lead
    graduation_cohort: { type: 'VARCHAR(100)', nullable: true },
    
    // Deal information
    kapi_interest: { type: 'ENUM', values: Object.values(KapiProduct), notNull: true },
    deal_size: { type: 'DECIMAL(12,2)', notNull: true }, // Deal value in INR
    currency: { type: 'VARCHAR(3)', default: 'INR' },
    contract_duration_months: { type: 'INTEGER', nullable: true },
    
    // Pipeline tracking
    stage: { type: 'ENUM', values: Object.values(KapiStage), default: KapiStage.DISCOVERY },
    probability: { type: 'INTEGER', default: 25 }, // 0-100% close probability
    expected_close_date: { type: 'DATE', notNull: true },
    actual_close_date: { type: 'DATE', nullable: true },
    
    // Lead source and attribution
    source: { type: 'ENUM', values: Object.values(KapiLeadSource), notNull: true },
    referrer_name: { type: 'VARCHAR(255)', nullable: true },
    campaign_source: { type: 'VARCHAR(255)', nullable: true },
    
    // Sales team assignment
    assigned_to: { type: 'INTEGER', foreignKey: 'users.id', notNull: true },
    assigned_date: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    sales_engineer: { type: 'INTEGER', foreignKey: 'users.id', nullable: true },
    
    // Communication tracking
    first_contact_date: { type: 'DATETIME', nullable: true },
    last_contact_date: { type: 'DATETIME', nullable: true },
    next_follow_up_date: { type: 'DATETIME', nullable: true },
    
    // Demo and proposal tracking
    demo_scheduled_date: { type: 'DATETIME', nullable: true },
    demo_completed_date: { type: 'DATETIME', nullable: true },
    demo_feedback: { type: 'TEXT', nullable: true },
    proposal_sent_date: { type: 'DATETIME', nullable: true },
    proposal_value: { type: 'DECIMAL(12,2)', nullable: true },
    
    // Decision makers and stakeholders
    decision_maker: { type: 'VARCHAR(255)', nullable: true },
    technical_contact: { type: 'VARCHAR(255)', nullable: true },
    procurement_contact: { type: 'VARCHAR(255)', nullable: true },
    stakeholders: { type: 'JSON', nullable: true }, // Array of stakeholder objects
    
    // Technical requirements
    technical_requirements: { type: 'TEXT', nullable: true },
    integration_complexity: { type: 'VARCHAR(50)', nullable: true }, // low, medium, high
    deployment_timeline: { type: 'VARCHAR(100)', nullable: true },
    budget_approved: { type: 'BOOLEAN', default: false },
    
    // Competition and alternatives
    competitors_considered: { type: 'JSON', nullable: true }, // Array of competitors
    competitive_advantage: { type: 'TEXT', nullable: true },
    
    // Cross-sell opportunities
    cross_sell_opportunity: { type: 'VARCHAR(500)', nullable: true },
    potential_additional_revenue: { type: 'DECIMAL(12,2)', default: 0 },
    team_training_interest: { type: 'BOOLEAN', default: false },
    executive_program_interest: { type: 'BOOLEAN', default: false },
    
    // Risk assessment
    risk_factors: { type: 'JSON', nullable: true }, // Array of risk factors
    risk_level: { type: 'VARCHAR(20)', default: 'medium' }, // low, medium, high
    
    // Lost deal tracking
    lost_reason: { type: 'VARCHAR(255)', nullable: true },
    lost_to_competitor: { type: 'VARCHAR(255)', nullable: true },
    lost_date: { type: 'DATETIME', nullable: true },
    
    // Notes and communication
    notes: { type: 'TEXT', nullable: true },
    internal_notes: { type: 'TEXT', nullable: true },
    communication_log: { type: 'JSON', nullable: true }, // Array of communication records
    
    // System tracking
    created_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    updated_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' },
    
    // Data quality and scoring
    lead_score: { type: 'INTEGER', default: 0 }, // 0-100 enterprise lead scoring
    engagement_score: { type: 'INTEGER', default: 0 }, // Engagement level tracking
    data_completeness: { type: 'INTEGER', default: 0 } // Data quality score
  },
  
  indexes: [
    { columns: ['company_name'] },
    { columns: ['email'] },
    { columns: ['stage'] },
    { columns: ['assigned_to'] },
    { columns: ['expected_close_date'] },
    { columns: ['deal_size'] },
    { columns: ['probability'] },
    { columns: ['source'] },
    { columns: ['is_modern_ai_graduate'] },
    { columns: ['created_at'] }
  ]
};

// Kapi deal activities tracking
export const KapiActivitySchema = {
  tableName: 'kapi_activities',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    kapi_lead_id: { type: 'INTEGER', foreignKey: 'kapi_leads.id', notNull: true },
    user_id: { type: 'INTEGER', foreignKey: 'users.id', notNull: true },
    
    // Activity details
    activity_type: { type: 'VARCHAR(100)', notNull: true }, // call, email, demo, proposal, meeting
    activity_date: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    subject: { type: 'VARCHAR(255)', nullable: true },
    description: { type: 'TEXT', nullable: true },
    outcome: { type: 'VARCHAR(255)', nullable: true },
    
    // Stage progression
    old_stage: { type: 'VARCHAR(50)', nullable: true },
    new_stage: { type: 'VARCHAR(50)', nullable: true },
    old_probability: { type: 'INTEGER', nullable: true },
    new_probability: { type: 'INTEGER', nullable: true },
    
    // Meeting details
    meeting_type: { type: 'VARCHAR(100)', nullable: true }, // discovery, demo, technical, commercial
    attendees: { type: 'JSON', nullable: true }, // Array of attendee objects
    duration_minutes: { type: 'INTEGER', nullable: true },
    
    // Next steps
    follow_up_required: { type: 'BOOLEAN', default: false },
    follow_up_date: { type: 'DATETIME', nullable: true },
    follow_up_notes: { type: 'TEXT', nullable: true }
  },
  
  indexes: [
    { columns: ['kapi_lead_id'] },
    { columns: ['activity_type'] },
    { columns: ['activity_date'] },
    { columns: ['user_id'] }
  ]
};

// Kapi class with business logic
export default class KapiLead {
  constructor(data = {}) {
    Object.assign(this, data);
  }
  
  // Get full contact name
  getContactFullName() {
    return this.contact_name || 'Unknown Contact';
  }
  
  // Calculate weighted pipeline value (deal_size * probability)
  getWeightedValue() {
    return Math.round(this.deal_size * (this.probability / 100));
  }
  
  // Get days until expected close
  getDaysUntilClose() {
    if (!this.expected_close_date) return null;
    
    const closeDate = new Date(this.expected_close_date);
    const now = new Date();
    const diffTime = closeDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  }
  
  // Check if deal is closing this month
  isClosingThisMonth() {
    const daysUntil = this.getDaysUntilClose();
    return daysUntil !== null && daysUntil <= 30 && daysUntil >= 0;
  }
  
  // Calculate days in current stage
  getDaysInStage() {
    // This would be calculated from stage change activities
    return this.days_in_stage || 0;
  }
  
  // Get stage velocity (average days per stage)
  getStageVelocity() {
    const stageOrder = Object.values(KapiStage);
    const currentStageIndex = stageOrder.indexOf(this.stage);
    
    if (currentStageIndex <= 0) return 0;
    
    const createdDate = new Date(this.created_at);
    const now = new Date();
    const totalDays = Math.ceil((now - createdDate) / (1000 * 60 * 60 * 24));
    
    return Math.round(totalDays / (currentStageIndex + 1));
  }
  
  // Check if deal needs attention (stalled)
  isStalled() {
    const daysInStage = this.getDaysInStage();
    const stageLimits = {
      [KapiStage.DISCOVERY]: 14,
      [KapiStage.QUALIFIED]: 7,
      [KapiStage.DEMO_SCHEDULED]: 3,
      [KapiStage.DEMO_COMPLETED]: 7,
      [KapiStage.PROPOSAL_SENT]: 14,
      [KapiStage.NEGOTIATION]: 21,
      [KapiStage.LEGAL_REVIEW]: 30
    };
    
    const limit = stageLimits[this.stage] || 30;
    return daysInStage > limit;
  }
  
  // Get next action based on stage
  getNextAction() {
    switch (this.stage) {
      case KapiStage.DISCOVERY:
        return 'Conduct discovery call to understand requirements';
      
      case KapiStage.QUALIFIED:
        return 'Schedule technical demo with stakeholders';
      
      case KapiStage.DEMO_SCHEDULED:
        return 'Prepare demo materials and confirm attendees';
      
      case KapiStage.DEMO_COMPLETED:
        return 'Follow up on demo feedback and next steps';
      
      case KapiStage.PROPOSAL_SENT:
        return 'Follow up on proposal review and questions';
      
      case KapiStage.NEGOTIATION:
        return 'Continue contract negotiations';
      
      case KapiStage.LEGAL_REVIEW:
        return 'Coordinate with legal teams for contract finalization';
      
      default:
        return 'Continue pipeline progression';
    }
  }
  
  // Calculate deal health score
  getDealHealthScore() {
    let score = 50; // Base score
    
    // Modern AI graduate bonus
    if (this.is_modern_ai_graduate) score += 20;
    
    // Engagement score
    score += (this.engagement_score || 0) * 0.3;
    
    // Stage progression (higher stages = higher score)
    const stageOrder = Object.values(KapiStage);
    const stageIndex = stageOrder.indexOf(this.stage);
    score += stageIndex * 5;
    
    // Reduce score if stalled
    if (this.isStalled()) score -= 25;
    
    // Budget approval bonus
    if (this.budget_approved) score += 15;
    
    // Risk level adjustment
    if (this.risk_level === 'low') score += 10;
    else if (this.risk_level === 'high') score -= 15;
    
    // Company size bonus (larger companies = higher score)
    const sizeBonus = {
      [CompanySize.ENTERPRISE]: 15,
      [CompanySize.MEGA_CORP]: 20,
      [CompanySize.LARGE]: 10,
      [CompanySize.MEDIUM]: 5
    };
    score += sizeBonus[this.company_size] || 0;
    
    return Math.max(0, Math.min(100, Math.round(score)));
  }
  
  // Get deal size category
  getDealSizeCategory() {
    if (this.deal_size >= 1000000) return 'mega'; // ₹10L+
    if (this.deal_size >= 500000) return 'large'; // ₹5L+
    if (this.deal_size >= 200000) return 'medium'; // ₹2L+
    return 'small';
  }
  
  // Calculate total opportunity value (including cross-sell)
  getTotalOpportunityValue() {
    return this.deal_size + (this.potential_additional_revenue || 0);
  }
  
  // Get stage color for UI
  getStageColor() {
    const colors = {
      [KapiStage.DISCOVERY]: 'bg-blue-100 text-blue-800',
      [KapiStage.QUALIFIED]: 'bg-yellow-100 text-yellow-800',
      [KapiStage.DEMO_SCHEDULED]: 'bg-purple-100 text-purple-800',
      [KapiStage.DEMO_COMPLETED]: 'bg-indigo-100 text-indigo-800',
      [KapiStage.PROPOSAL_SENT]: 'bg-orange-100 text-orange-800',
      [KapiStage.NEGOTIATION]: 'bg-green-100 text-green-800',
      [KapiStage.LEGAL_REVIEW]: 'bg-teal-100 text-teal-800',
      [KapiStage.CLOSED_WON]: 'bg-emerald-100 text-emerald-800',
      [KapiStage.CLOSED_LOST]: 'bg-red-100 text-red-800'
    };
    return colors[this.stage] || 'bg-gray-100 text-gray-800';
  }
  
  // Get priority level based on deal size and close date
  getPriority() {
    const daysUntil = this.getDaysUntilClose();
    const dealSize = this.deal_size;
    
    if (daysUntil !== null && daysUntil <= 7 && dealSize >= 500000) return 'urgent';
    if (daysUntil !== null && daysUntil <= 14 && dealSize >= 200000) return 'high';
    if (dealSize >= 1000000) return 'high';
    if (daysUntil !== null && daysUntil <= 30) return 'medium';
    return 'low';
  }
  
  // Format deal size for display
  getFormattedDealSize() {
    if (this.deal_size >= 10000000) {
      return `₹${Math.round(this.deal_size / 10000000)}Cr`;
    } else if (this.deal_size >= 100000) {
      return `₹${Math.round(this.deal_size / 100000)}L`;
    } else if (this.deal_size >= 1000) {
      return `₹${Math.round(this.deal_size / 1000)}K`;
    }
    return `₹${this.deal_size}`;
  }
  
  // Check if eligible for team training cross-sell
  isTeamTrainingEligible() {
    return this.company_size !== CompanySize.STARTUP && 
           ['closed_won', 'negotiation', 'legal_review'].includes(this.stage);
  }
}