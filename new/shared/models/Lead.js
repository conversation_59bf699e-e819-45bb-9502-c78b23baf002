// Lead model for Facebook lead management
// Core lead tracking and sales funnel management

// Lead sources
export const LeadSource = {
  FB_ADS: 'fb_ads',
  ORGANIC: 'organic',
  REFERRAL: 'referral',
  INBOUND: 'inbound',
  LINKEDIN_ADS: 'linkedin_ads',
  GOOGLE_ADS: 'google_ads',
  DIRECT: 'direct',
  WEBINAR: 'webinar',
  EVENT: 'event'
};

// Lead status/stages - matches the conversion funnel
export const LeadStatus = {
  NEW: 'new',                           // Fresh lead from Facebook
  AUTO_RESPONDED: 'auto_responded',     // Instant email + SMS sent
  CONTACTED: 'contacted',               // Sales person reached out
  QUALIFIED: 'qualified',               // Meets senior tech criteria
  DEMO_SCHEDULED: 'demo_scheduled',     // Demo call booked
  DEMO_COMPLETED: 'demo_completed',     // Demo call conducted
  ENROLLED: 'enrolled',                 // Paid and enrolled
  BOOTCAMP_COMPLETE: 'bootcamp_complete', // Graduated
  KAPI_OPPORTUNITY: 'kapi_opportunity', // Upsell tracking
  LOST: 'lost'                         // Not interested/unqualified
};

// Lead segments for prioritization
export const LeadSegment = {
  HOT: 'hot',       // High conversion probability (15-20%)
  WARM: 'warm',     // Medium conversion probability (8-12%)
  MEDIUM: 'medium'  // Lower conversion probability (3-5%)
};

// Lead priority levels
export const LeadPriority = {
  URGENT: 'urgent',     // Needs contact within 5 minutes
  HIGH: 'high',         // Contact within 1 hour
  MEDIUM: 'medium',     // Contact within 4 hours
  LOW: 'low'           // Contact within 24 hours
};

// Lead schema
export const LeadSchema = {
  tableName: 'leads',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    
    // Facebook lead data
    facebook_lead_id: { type: 'VARCHAR(255)', unique: true, index: true },
    facebook_ad_id: { type: 'VARCHAR(255)', index: true },
    facebook_campaign_id: { type: 'VARCHAR(255)', index: true },
    facebook_form_id: { type: 'VARCHAR(255)', nullable: true },
    
    // Contact information
    first_name: { type: 'VARCHAR(255)', notNull: true },
    last_name: { type: 'VARCHAR(255)', nullable: true },
    email: { type: 'VARCHAR(255)', index: true, notNull: true },
    phone: { type: 'VARCHAR(20)', index: true, nullable: true },
    
    // Professional information
    company: { type: 'VARCHAR(255)', nullable: true },
    title: { type: 'VARCHAR(255)', nullable: true },
    industry: { type: 'VARCHAR(100)', nullable: true },
    company_size: { type: 'VARCHAR(50)', nullable: true },
    experience_years: { type: 'INTEGER', nullable: true },
    
    // AI experience and interests
    ai_experience_level: { type: 'VARCHAR(50)', nullable: true }, // beginner, intermediate, advanced
    ai_interests: { type: 'JSON', nullable: true }, // Array of interests
    current_ai_usage: { type: 'TEXT', nullable: true },
    learning_goals: { type: 'TEXT', nullable: true },
    
    // Lead tracking
    source: { type: 'ENUM', values: Object.values(LeadSource), notNull: true },
    status: { type: 'ENUM', values: Object.values(LeadStatus), default: LeadStatus.NEW },
    segment: { type: 'ENUM', values: Object.values(LeadSegment), nullable: true },
    priority: { type: 'ENUM', values: Object.values(LeadPriority), default: LeadPriority.HIGH },
    
    // Scoring and qualification
    lead_score: { type: 'INTEGER', default: 0 }, // 0-100 scoring system
    qualification_score: { type: 'INTEGER', default: 0 }, // Technical qualification
    seniority_score: { type: 'INTEGER', default: 0 }, // Job title seniority
    company_score: { type: 'INTEGER', default: 0 }, // Company potential
    
    // Assignment and ownership
    assigned_to: { type: 'INTEGER', foreignKey: 'users.id', nullable: true },
    assigned_at: { type: 'DATETIME', nullable: true },
    
    // Contact tracking
    first_contact_date: { type: 'DATETIME', nullable: true },
    last_contact_date: { type: 'DATETIME', nullable: true },
    contact_attempts: { type: 'INTEGER', default: 0 },
    response_received: { type: 'BOOLEAN', default: false },
    
    // Auto-response tracking
    email_sent: { type: 'BOOLEAN', default: false },
    email_sent_at: { type: 'DATETIME', nullable: true },
    sms_sent: { type: 'BOOLEAN', default: false },
    sms_sent_at: { type: 'DATETIME', nullable: true },
    whatsapp_sent: { type: 'BOOLEAN', default: false },
    whatsapp_sent_at: { type: 'DATETIME', nullable: true },
    
    // Engagement tracking
    email_opened: { type: 'BOOLEAN', default: false },
    email_clicked: { type: 'BOOLEAN', default: false },
    website_visited: { type: 'BOOLEAN', default: false },
    demo_requested: { type: 'BOOLEAN', default: false },
    
    // Demo and enrollment
    demo_scheduled_date: { type: 'DATETIME', nullable: true },
    demo_completed_date: { type: 'DATETIME', nullable: true },
    demo_feedback: { type: 'TEXT', nullable: true },
    demo_rating: { type: 'INTEGER', nullable: true }, // 1-5 rating
    
    // Conversion tracking
    enrolled_date: { type: 'DATETIME', nullable: true },
    enrollment_amount: { type: 'DECIMAL(10,2)', nullable: true },
    bootcamp_completed_date: { type: 'DATETIME', nullable: true },
    certificate_sent: { type: 'BOOLEAN', default: false },
    
    // Attribution and campaign data
    utm_source: { type: 'VARCHAR(100)', nullable: true },
    utm_medium: { type: 'VARCHAR(100)', nullable: true },
    utm_campaign: { type: 'VARCHAR(255)', nullable: true },
    utm_content: { type: 'VARCHAR(255)', nullable: true },
    utm_term: { type: 'VARCHAR(255)', nullable: true },
    
    // Geographic data
    country: { type: 'VARCHAR(100)', nullable: true },
    state: { type: 'VARCHAR(100)', nullable: true },
    city: { type: 'VARCHAR(100)', nullable: true },
    timezone: { type: 'VARCHAR(50)', nullable: true },
    
    // Referral tracking
    referred_by: { type: 'INTEGER', foreignKey: 'users.id', nullable: true },
    referral_code: { type: 'VARCHAR(50)', nullable: true },
    
    // Loss tracking
    lost_reason: { type: 'VARCHAR(255)', nullable: true },
    lost_date: { type: 'DATETIME', nullable: true },
    lost_notes: { type: 'TEXT', nullable: true },
    
    // Notes and communication
    notes: { type: 'TEXT', nullable: true },
    internal_notes: { type: 'TEXT', nullable: true },
    tags: { type: 'JSON', nullable: true }, // Array of tags
    
    // System tracking
    created_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    updated_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' },
    
    // Data quality
    is_duplicate: { type: 'BOOLEAN', default: false },
    duplicate_of: { type: 'INTEGER', foreignKey: 'leads.id', nullable: true },
    data_quality_score: { type: 'INTEGER', default: 100 } // 0-100 data completeness
  },
  
  indexes: [
    { columns: ['facebook_lead_id'], unique: true },
    { columns: ['email'] },
    { columns: ['phone'] },
    { columns: ['status'] },
    { columns: ['source'] },
    { columns: ['segment'] },
    { columns: ['priority'] },
    { columns: ['assigned_to'] },
    { columns: ['created_at'] },
    { columns: ['first_contact_date'] },
    { columns: ['facebook_campaign_id'] },
    { columns: ['utm_campaign'] },
    { columns: ['lead_score'] }
  ]
};

// Lead communication/activity log
export const LeadActivitySchema = {
  tableName: 'lead_activities',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    lead_id: { type: 'INTEGER', foreignKey: 'leads.id', notNull: true },
    user_id: { type: 'INTEGER', foreignKey: 'users.id', nullable: true }, // Who performed the activity
    
    // Activity details
    activity_type: { type: 'VARCHAR(100)', notNull: true }, // call, email, sms, whatsapp, note, status_change
    activity_date: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    
    // Activity content
    subject: { type: 'VARCHAR(255)', nullable: true },
    content: { type: 'TEXT', nullable: true },
    outcome: { type: 'VARCHAR(255)', nullable: true },
    
    // Status changes
    old_status: { type: 'VARCHAR(50)', nullable: true },
    new_status: { type: 'VARCHAR(50)', nullable: true },
    
    // Communication tracking
    duration_minutes: { type: 'INTEGER', nullable: true }, // For calls
    response_received: { type: 'BOOLEAN', default: false },
    sentiment: { type: 'VARCHAR(20)', nullable: true }, // positive, neutral, negative
    
    // Next steps
    follow_up_date: { type: 'DATETIME', nullable: true },
    follow_up_notes: { type: 'TEXT', nullable: true }
  },
  
  indexes: [
    { columns: ['lead_id'] },
    { columns: ['activity_type'] },
    { columns: ['activity_date'] },
    { columns: ['user_id'] }
  ]
};

// Lead class with business logic
export default class Lead {
  constructor(data = {}) {
    Object.assign(this, data);
  }
  
  // Get full name
  getFullName() {
    return `${this.first_name || ''} ${this.last_name || ''}`.trim();
  }
  
  // Calculate lead age in hours
  getLeadAgeHours() {
    const created = new Date(this.created_at);
    const now = new Date();
    return Math.round((now - created) / (1000 * 60 * 60));
  }
  
  // Check if lead needs urgent attention (>5 minutes without contact)
  needsUrgentAttention() {
    if (this.first_contact_date) return false;
    return this.getLeadAgeHours() * 60 > 5; // 5 minutes
  }
  
  // Calculate response time in minutes
  getResponseTimeMinutes() {
    if (!this.first_contact_date) return null;
    const created = new Date(this.created_at);
    const contacted = new Date(this.first_contact_date);
    return Math.round((contacted - created) / (1000 * 60));
  }
  
  // Check if response time meets target (<5 minutes)
  meetsResponseTimeTarget() {
    const responseTime = this.getResponseTimeMinutes();
    return responseTime !== null && responseTime <= 5;
  }
  
  // Calculate days since last contact
  getDaysSinceLastContact() {
    if (!this.last_contact_date) return null;
    const lastContact = new Date(this.last_contact_date);
    const now = new Date();
    return Math.ceil((now - lastContact) / (1000 * 60 * 60 * 24));
  }
  
  // Check if lead is stale (no contact in 7+ days)
  isStale() {
    const daysSince = this.getDaysSinceLastContact();
    return daysSince !== null && daysSince >= 7;
  }
  
  // Determine lead segment based on scoring
  getCalculatedSegment() {
    if (this.lead_score >= 80) return LeadSegment.HOT;
    if (this.lead_score >= 50) return LeadSegment.WARM;
    return LeadSegment.MEDIUM;
  }
  
  // Check if lead is senior tech professional
  isSeniorTech() {
    const seniorTitles = ['CTO', 'VP', 'Director', 'Head', 'Senior', 'Lead', 'Principal', 'Architect'];
    return seniorTitles.some(title => 
      this.title?.toLowerCase().includes(title.toLowerCase())
    );
  }
  
  // Calculate conversion probability based on segment
  getConversionProbability() {
    switch (this.segment || this.getCalculatedSegment()) {
      case LeadSegment.HOT: return { min: 15, max: 20 };
      case LeadSegment.WARM: return { min: 8, max: 12 };
      case LeadSegment.MEDIUM: return { min: 3, max: 5 };
      default: return { min: 3, max: 5 };
    }
  }
  
  // Get expected revenue based on conversion probability
  getExpectedRevenue() {
    const probability = this.getConversionProbability();
    const avgProbability = (probability.min + probability.max) / 2 / 100;
    const avgBootcampPrice = 15000; // ₹15,000 average bootcamp price
    return Math.round(avgBootcampPrice * avgProbability);
  }
  
  // Check if eligible for Kapi upsell
  isKapiEligible() {
    return this.status === LeadStatus.BOOTCAMP_COMPLETE &&
           this.isSeniorTech() &&
           this.company &&
           ['1000+', '500-1000', '100-500'].includes(this.company_size);
  }
  
  // Get next action based on status and timing
  getNextAction() {
    const ageHours = this.getLeadAgeHours();
    const daysSinceContact = this.getDaysSinceLastContact();
    
    switch (this.status) {
      case LeadStatus.NEW:
        return ageHours > 0.1 ? 'Send auto-response (URGENT)' : 'Auto-response scheduled';
      
      case LeadStatus.AUTO_RESPONDED:
        return ageHours > 1 ? 'Make qualification call (HIGH PRIORITY)' : 'Schedule qualification call';
      
      case LeadStatus.CONTACTED:
        return 'Assess qualification and schedule demo';
      
      case LeadStatus.QUALIFIED:
        return 'Schedule demo call';
      
      case LeadStatus.DEMO_SCHEDULED:
        return 'Prepare for demo and send reminder';
      
      case LeadStatus.DEMO_COMPLETED:
        return 'Follow up on enrollment decision';
      
      case LeadStatus.ENROLLED:
        return 'Send bootcamp joining instructions';
      
      case LeadStatus.BOOTCAMP_COMPLETE:
        return this.isKapiEligible() ? 'Assess Kapi opportunity' : 'Request testimonial/referral';
      
      default:
        if (daysSinceContact && daysSinceContact >= 7) {
          return 'Re-engage (stale lead)';
        }
        return 'Continue nurturing';
    }
  }
  
  // Get priority level based on segment and timing
  getCalculatedPriority() {
    if (this.needsUrgentAttention()) return LeadPriority.URGENT;
    if (this.segment === LeadSegment.HOT || this.lead_score >= 80) return LeadPriority.HIGH;
    if (this.segment === LeadSegment.WARM || this.lead_score >= 50) return LeadPriority.MEDIUM;
    return LeadPriority.LOW;
  }
  
  // Get status color for UI
  getStatusColor() {
    const colors = {
      [LeadStatus.NEW]: 'bg-red-100 text-red-800',
      [LeadStatus.AUTO_RESPONDED]: 'bg-yellow-100 text-yellow-800',
      [LeadStatus.CONTACTED]: 'bg-blue-100 text-blue-800',
      [LeadStatus.QUALIFIED]: 'bg-purple-100 text-purple-800',
      [LeadStatus.DEMO_SCHEDULED]: 'bg-orange-100 text-orange-800',
      [LeadStatus.DEMO_COMPLETED]: 'bg-indigo-100 text-indigo-800',
      [LeadStatus.ENROLLED]: 'bg-green-100 text-green-800',
      [LeadStatus.BOOTCAMP_COMPLETE]: 'bg-emerald-100 text-emerald-800',
      [LeadStatus.KAPI_OPPORTUNITY]: 'bg-purple-100 text-purple-800',
      [LeadStatus.LOST]: 'bg-gray-100 text-gray-800'
    };
    return colors[this.status] || 'bg-gray-100 text-gray-800';
  }
}