// User model for lead management dashboard
// Adapted from legacy user.py with focus on lead management and sales team

// User roles for dashboard access control
export const UserRoles = {
  STUDENT: 'student',
  SALES_REP: 'sales_rep', 
  ADMIN: 'admin',
  EXECUTIVE: 'executive',
  BD: 'bd'
};

// Subscription tiers for revenue tracking
export const SubscriptionTier = {
  FREE: 'free',
  BASIC: 'basic',       // ₹2,500/month
  PREMIUM: 'premium',   // ₹15,000/3-month bootcamp
  KAPI: 'kapi'         // Enterprise deals
};

// User schema for SQLite/PostgreSQL
export const UserSchema = {
  tableName: 'users',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    username: { type: 'VARCHAR(255)', unique: true, index: true },
    email: { type: 'VARCHAR(255)', unique: true, index: true },
    first_name: { type: 'VARCHA<PERSON>(255)' },
    last_name: { type: 'VARCHAR(255)' },
    password_hash: { type: 'VARCHAR(255)' },
    email_verified: { type: 'BOOLEAN', default: false },
    password_reset_token: { type: 'VARCHAR(500)', nullable: true },
    welcome_code_used: { type: 'VARCHAR(50)', nullable: true },
    
    // Role and permissions
    role: { type: 'ENUM', values: Object.values(UserRoles), default: UserRoles.STUDENT },
    
    // Subscription and revenue tracking
    subscription_tier: { type: 'ENUM', values: Object.values(SubscriptionTier), default: SubscriptionTier.FREE },
    subscription_start_date: { type: 'DATETIME', nullable: true },
    subscription_end_date: { type: 'DATETIME', nullable: true },
    is_active: { type: 'BOOLEAN', default: true },
    
    // Contact information for lead management
    phone: { type: 'VARCHAR(20)', nullable: true },
    company: { type: 'VARCHAR(255)', nullable: true },
    title: { type: 'VARCHAR(255)', nullable: true },
    industry: { type: 'VARCHAR(100)', nullable: true },
    
    // Lead tracking fields
    lead_source: { type: 'VARCHAR(100)', nullable: true }, // FB ads, referral, organic, etc.
    utm_campaign: { type: 'VARCHAR(255)', nullable: true },
    utm_source: { type: 'VARCHAR(100)', nullable: true },
    utm_medium: { type: 'VARCHAR(100)', nullable: true },
    
    // Sales team assignment
    assigned_to: { type: 'INTEGER', nullable: true }, // Foreign key to sales rep user
    
    // Lead qualification
    ai_experience_level: { type: 'VARCHAR(50)', nullable: true }, // beginner, intermediate, advanced
    lead_score: { type: 'INTEGER', default: 0 }, // 0-100 lead scoring
    lead_segment: { type: 'VARCHAR(20)', nullable: true }, // hot, warm, medium
    
    // Referral system
    referred_by: { type: 'INTEGER', nullable: true }, // Foreign key to referrer user
    referral_code: { type: 'VARCHAR(50)', nullable: true },
    
    // Timestamps
    created_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    updated_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' },
    last_contact_date: { type: 'DATETIME', nullable: true },
    
    // Bootcamp completion tracking
    bootcamp_completed: { type: 'BOOLEAN', default: false },
    bootcamp_completion_date: { type: 'DATETIME', nullable: true },
    certificate_generated: { type: 'BOOLEAN', default: false },
    certificate_sent: { type: 'BOOLEAN', default: false }
  },
  
  indexes: [
    { columns: ['email'], unique: true },
    { columns: ['username'], unique: true },
    { columns: ['role'] },
    { columns: ['subscription_tier'] },
    { columns: ['lead_source'] },
    { columns: ['assigned_to'] },
    { columns: ['lead_segment'] },
    { columns: ['created_at'] }
  ]
};

// User class with business logic
export default class User {
  constructor(data = {}) {
    Object.assign(this, data);
  }
  
  // Check if user's subscription is active
  isSubscriptionActive() {
    if (this.subscription_tier === SubscriptionTier.FREE) {
      return true;
    }
    if (!this.subscription_end_date) {
      return false;
    }
    return new Date() <= new Date(this.subscription_end_date);
  }
  
  // Check if user has dashboard access
  hasDashboardAccess() {
    return [UserRoles.ADMIN, UserRoles.EXECUTIVE, UserRoles.BD, UserRoles.SALES_REP].includes(this.role);
  }
  
  // Check if user has executive-level access (full dashboard)
  hasExecutiveAccess() {
    return [UserRoles.ADMIN, UserRoles.EXECUTIVE].includes(this.role);
  }
  
  // Check if user is a sales representative (limited dashboard)
  isSalesRep() {
    return this.role === UserRoles.SALES_REP;
  }
  
  // Get user's full name
  getFullName() {
    return `${this.first_name || ''} ${this.last_name || ''}`.trim();
  }
  
  // Calculate days since last contact
  getDaysSinceLastContact() {
    if (!this.last_contact_date) return null;
    const lastContact = new Date(this.last_contact_date);
    const now = new Date();
    const diffTime = Math.abs(now - lastContact);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  
  // Check if lead is hot (high priority)
  isHotLead() {
    return this.lead_segment === 'hot' || this.lead_score >= 80;
  }
  
  // Get lead priority level
  getLeadPriority() {
    if (this.lead_score >= 80 || this.lead_segment === 'hot') return 'high';
    if (this.lead_score >= 50 || this.lead_segment === 'warm') return 'medium';
    return 'low';
  }
  
  // Calculate revenue contribution
  getRevenueContribution() {
    if (!this.isSubscriptionActive()) return 0;
    
    switch (this.subscription_tier) {
      case SubscriptionTier.BASIC: return 2500; // Monthly
      case SubscriptionTier.PREMIUM: return 15000; // 3-month bootcamp
      case SubscriptionTier.KAPI: return 500000; // Average enterprise deal
      default: return 0;
    }
  }
  
  // Check if eligible for Kapi upsell
  isKapiEligible() {
    return this.bootcamp_completed && 
           this.company && 
           ['CTO', 'VP', 'Director', 'Head', 'CEO', 'Founder'].some(title => 
             this.title?.toLowerCase().includes(title.toLowerCase())
           );
  }

  // Static methods for database operations
  static async findByUsername(username) {
    const db = require('../lib/db').getDb();
    const stmt = db.prepare('SELECT * FROM users WHERE username = ?');
    return stmt.get(username);
  }

  static async findByEmail(email) {
    const db = require('../lib/db').getDb();
    const stmt = db.prepare('SELECT * FROM users WHERE email = ?');
    return stmt.get(email);
  }

  static async findById(id) {
    const db = require('../lib/db').getDb();
    const stmt = db.prepare('SELECT * FROM users WHERE id = ?');
    return stmt.get(id);
  }

  static async create(userData) {
    const db = require('../lib/db').getDb();
    const stmt = db.prepare(`
      INSERT INTO users (
        username, email, password_hash, first_name, last_name, 
        role, subscription_tier, email_verified, welcome_code_used
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      userData.username,
      userData.email,
      userData.password_hash,
      userData.first_name,
      userData.last_name,
      userData.role || 'student',
      userData.subscription_tier || 'free',
      userData.email_verified || false,
      userData.welcome_code_used
    );
    
    return result.lastInsertRowid;
  }

  static async setPasswordResetToken(userId, token) {
    const db = require('../lib/db').getDb();
    const stmt = db.prepare('UPDATE users SET password_reset_token = ? WHERE id = ?');
    return stmt.run(token, userId);
  }

  static async clearPasswordResetToken(userId) {
    const db = require('../lib/db').getDb();
    const stmt = db.prepare('UPDATE users SET password_reset_token = NULL WHERE id = ?');
    return stmt.run(userId);
  }

  static async updatePassword(userId, passwordHash) {
    const db = require('../lib/db').getDb();
    const stmt = db.prepare('UPDATE users SET password_hash = ? WHERE id = ?');
    return stmt.run(passwordHash, userId);
  }

  static async verifyEmail(userId) {
    const db = require('../lib/db').getDb();
    const stmt = db.prepare('UPDATE users SET email_verified = true WHERE id = ?');
    return stmt.run(userId);
  }
}

// Payment history schema for revenue tracking
export const PaymentHistorySchema = {
  tableName: 'payment_history',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    user_id: { type: 'INTEGER', foreignKey: 'users.id', notNull: true },
    amount: { type: 'DECIMAL(10,2)', notNull: true },
    payment_date: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    subscription_tier: { type: 'ENUM', values: Object.values(SubscriptionTier), notNull: true },
    payment_method: { type: 'VARCHAR(100)', notNull: true },
    transaction_id: { type: 'VARCHAR(255)', unique: true, notNull: true },
    status: { type: 'VARCHAR(50)', notNull: true }, // succeeded, failed, refunded
    currency: { type: 'VARCHAR(3)', default: 'INR' },
    
    // Campaign attribution
    campaign_id: { type: 'INTEGER', nullable: true },
    lead_source: { type: 'VARCHAR(100)', nullable: true }
  }
};

// Referral schema for tracking referral program
export const ReferralSchema = {
  tableName: 'referrals',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    referrer_id: { type: 'INTEGER', foreignKey: 'users.id', notNull: true },
    referred_id: { type: 'INTEGER', foreignKey: 'users.id', notNull: true },
    referral_code: { type: 'VARCHAR(50)', index: true, notNull: true },
    created_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    status: { type: 'VARCHAR(50)', default: 'pending' }, // pending, completed, rewarded
    
    // Reward tracking
    referrer_reward_given: { type: 'BOOLEAN', default: false },
    referred_reward_given: { type: 'BOOLEAN', default: false },
    reward_type: { type: 'VARCHAR(100)', nullable: true }, // 15% discount, free_month, etc.
    reward_amount: { type: 'DECIMAL(10,2)', nullable: true }
  }
};