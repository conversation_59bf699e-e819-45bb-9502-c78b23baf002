// Workshop/Bootcamp model for event management
// Adapted from legacy workshop.py for AI bootcamp management

// Workshop/Event types
export const EventType = {
  BOOTCAMP: 'bootcamp',
  WORKSHOP: 'workshop', 
  WEBINAR: 'webinar',
  MASTERCLASS: 'masterclass'
};

// Workshop status
export const WorkshopStatus = {
  DRAFT: 'draft',
  OPEN: 'open',
  FILLING_FAST: 'filling_fast',
  ALMOST_FULL: 'almost_full',
  FULL: 'full',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

// Workshop schema
export const WorkshopSchema = {
  tableName: 'workshops',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    name: { type: 'VARCHAR(255)', notNull: true },
    description: { type: 'TEXT', nullable: true },
    event_type: { type: 'ENUM', values: Object.values(EventType), default: EventType.BOOTCAMP },
    
    // Scheduling
    start_date: { type: 'DATETIME', notNull: true },
    end_date: { type: 'DATETIME', notNull: true },
    duration_hours: { type: 'INTEGER', nullable: true }, // Duration in hours
    timezone: { type: 'VARCHAR(50)', default: 'Asia/Kolkata' },
    
    // Content and resources
    syllabus_link: { type: 'VARCHAR(500)', nullable: true },
    meeting_link: { type: 'VARCHAR(500)', nullable: true },
    recording_link: { type: 'VARCHAR(500)', nullable: true },
    materials_link: { type: 'VARCHAR(500)', nullable: true },
    
    // Location and logistics
    geography: { type: 'VARCHAR(100)', nullable: true }, // Bangalore, NCR, Online, etc.
    venue: { type: 'VARCHAR(255)', nullable: true },
    is_online: { type: 'BOOLEAN', default: true },
    
    // Pricing and capacity
    cost: { type: 'DECIMAL(10,2)', nullable: true },
    currency: { type: 'VARCHAR(3)', default: 'INR' },
    max_participants: { type: 'INTEGER', nullable: true },
    min_participants: { type: 'INTEGER', default: 5 },
    
    // Registration management
    registration_open: { type: 'BOOLEAN', default: true },
    registration_deadline: { type: 'DATETIME', nullable: true },
    early_bird_deadline: { type: 'DATETIME', nullable: true },
    early_bird_discount: { type: 'DECIMAL(5,2)', nullable: true }, // Percentage
    
    // Status and tracking
    status: { type: 'ENUM', values: Object.values(WorkshopStatus), default: WorkshopStatus.DRAFT },
    is_active: { type: 'BOOLEAN', default: true },
    
    // Instructor and content
    instructor_name: { type: 'VARCHAR(255)', nullable: true },
    instructor_bio: { type: 'TEXT', nullable: true },
    difficulty_level: { type: 'VARCHAR(50)', nullable: true }, // beginner, intermediate, advanced
    prerequisites: { type: 'TEXT', nullable: true },
    
    // Marketing and tracking
    utm_campaign: { type: 'VARCHAR(255)', nullable: true },
    landing_page_url: { type: 'VARCHAR(500)', nullable: true },
    promotional_image: { type: 'VARCHAR(500)', nullable: true },
    
    // Certificate management
    certificate_template: { type: 'VARCHAR(255)', nullable: true },
    certificates_generated: { type: 'BOOLEAN', default: false },
    
    // Timestamps
    created_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    updated_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' }
  },
  
  indexes: [
    { columns: ['start_date'] },
    { columns: ['status'] },
    { columns: ['event_type'] },
    { columns: ['geography'] },
    { columns: ['is_active'] }
  ]
};

// Workshop participants (many-to-many relationship)
export const WorkshopParticipantSchema = {
  tableName: 'workshop_participants',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    workshop_id: { type: 'INTEGER', foreignKey: 'workshops.id', notNull: true },
    user_id: { type: 'INTEGER', foreignKey: 'users.id', notNull: true },
    
    // Registration details
    registered_at: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    registration_source: { type: 'VARCHAR(100)', nullable: true }, // direct, referral, campaign
    
    // Payment tracking
    payment_status: { type: 'VARCHAR(50)', default: 'pending' }, // pending, paid, refunded, waived
    amount_paid: { type: 'DECIMAL(10,2)', default: 0 },
    discount_applied: { type: 'DECIMAL(5,2)', default: 0 },
    payment_date: { type: 'DATETIME', nullable: true },
    
    // Attendance tracking
    attendance_status: { type: 'VARCHAR(50)', default: 'registered' }, // registered, attended, no_show, cancelled
    check_in_time: { type: 'DATETIME', nullable: true },
    check_out_time: { type: 'DATETIME', nullable: true },
    
    // Completion and certification
    completed: { type: 'BOOLEAN', default: false },
    completion_date: { type: 'DATETIME', nullable: true },
    certificate_issued: { type: 'BOOLEAN', default: false },
    certificate_sent: { type: 'BOOLEAN', default: false },
    
    // Feedback and satisfaction
    feedback_rating: { type: 'INTEGER', nullable: true }, // 1-5 rating
    feedback_comment: { type: 'TEXT', nullable: true },
    nps_score: { type: 'INTEGER', nullable: true }, // Net Promoter Score 0-10
    
    // Follow-up tracking
    follow_up_sent: { type: 'BOOLEAN', default: false },
    kapi_opportunity: { type: 'BOOLEAN', default: false }
  },
  
  indexes: [
    { columns: ['workshop_id', 'user_id'], unique: true },
    { columns: ['payment_status'] },
    { columns: ['attendance_status'] },
    { columns: ['completed'] }
  ]
};

// Workshop payment history
export const WorkshopPaymentHistorySchema = {
  tableName: 'workshop_payment_history',
  columns: {
    id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
    workshop_id: { type: 'INTEGER', foreignKey: 'workshops.id', notNull: true },
    user_id: { type: 'INTEGER', foreignKey: 'users.id', notNull: true },
    amount: { type: 'DECIMAL(10,2)', notNull: true },
    payment_date: { type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    payment_method: { type: 'VARCHAR(100)', notNull: true },
    transaction_id: { type: 'VARCHAR(255)', unique: true, notNull: true },
    status: { type: 'VARCHAR(50)', notNull: true }, // succeeded, failed, refunded
    currency: { type: 'VARCHAR(3)', default: 'INR' },
    
    // Attribution
    lead_source: { type: 'VARCHAR(100)', nullable: true },
    campaign_id: { type: 'INTEGER', nullable: true }
  }
};

// Workshop class with business logic
export default class Workshop {
  constructor(data = {}) {
    Object.assign(this, data);
  }
  
  // Get current registration count
  getRegistrationCount() {
    // This would be calculated from workshop_participants table
    return this.registered_count || 0;
  }
  
  // Calculate capacity utilization
  getCapacityUtilization() {
    if (!this.max_participants) return 0;
    return Math.round((this.getRegistrationCount() / this.max_participants) * 100);
  }
  
  // Get workshop status based on capacity
  getCapacityStatus() {
    const utilization = this.getCapacityUtilization();
    
    if (utilization >= 95) return WorkshopStatus.FULL;
    if (utilization >= 90) return WorkshopStatus.ALMOST_FULL;
    if (utilization >= 70) return WorkshopStatus.FILLING_FAST;
    return WorkshopStatus.OPEN;
  }
  
  // Check if registration is open
  isRegistrationOpen() {
    if (!this.registration_open) return false;
    if (this.status === WorkshopStatus.FULL) return false;
    if (this.registration_deadline && new Date() > new Date(this.registration_deadline)) return false;
    return true;
  }
  
  // Check if early bird discount is available
  isEarlyBirdAvailable() {
    if (!this.early_bird_deadline || !this.early_bird_discount) return false;
    return new Date() <= new Date(this.early_bird_deadline);
  }
  
  // Calculate discounted price
  getDiscountedPrice() {
    if (!this.cost) return 0;
    if (!this.isEarlyBirdAvailable()) return this.cost;
    
    const discount = this.early_bird_discount / 100;
    return this.cost * (1 - discount);
  }
  
  // Get days until workshop starts
  getDaysUntilStart() {
    const startDate = new Date(this.start_date);
    const now = new Date();
    const diffTime = startDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 0;
    return diffDays;
  }
  
  // Get formatted date range
  getDateRange() {
    const start = new Date(this.start_date);
    const end = new Date(this.end_date);
    
    const options = { 
      month: 'short', 
      day: 'numeric',
      year: start.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
    };
    
    const startStr = start.toLocaleDateString('en-IN', options);
    const endStr = end.toLocaleDateString('en-IN', options);
    
    if (start.toDateString() === end.toDateString()) {
      return startStr;
    }
    
    return `${startStr} - ${endStr}`;
  }
  
  // Calculate total revenue
  getTotalRevenue() {
    // This would be calculated from workshop_payment_history table
    return this.total_revenue || 0;
  }
  
  // Get completion rate
  getCompletionRate() {
    const registered = this.getRegistrationCount();
    if (registered === 0) return 0;
    
    // This would be calculated from workshop_participants table
    const completed = this.completed_count || 0;
    return Math.round((completed / registered) * 100);
  }
  
  // Check if workshop is eligible for certificates
  canIssueCertificates() {
    return this.status === WorkshopStatus.COMPLETED && 
           this.certificate_template && 
           !this.certificates_generated;
  }
  
  // Get average NPS score
  getAverageNPS() {
    // This would be calculated from workshop_participants feedback
    return this.average_nps || null;
  }
  
  // Get follow-up opportunities
  getKapiOpportunities() {
    // Count of participants marked as kapi_opportunity
    return this.kapi_opportunity_count || 0;
  }
}