// Database initialization script
// Creates all tables and relationships for the lead management dashboard

import {
  UserSchema,
  PaymentHistorySchema,
  ReferralSchema,
  WorkshopSchema,
  WorkshopParticipantSchema,
  WorkshopPaymentHistorySchema,
  LeadSchema,
  LeadActivitySchema,
  CampaignSchema,
  CampaignDailyStatsSchema,
  KapiLeadSchema,
  KapiActivitySchema,
  CertificateSchema,
  CertificateTemplateSchema,
  EventSchema,
  EventRegistrationSchema
} from './index.js';

// SQL generation helper functions
class SQLGenerator {
  static getColumnDefinition(column) {
    let sql = `${column.type}`;
    
    if (column.primaryKey) sql += ' PRIMARY KEY';
    if (column.autoIncrement) sql += ' AUTOINCREMENT';
    if (column.unique) sql += ' UNIQUE';
    if (column.notNull) sql += ' NOT NULL';
    if (column.nullable === false) sql += ' NOT NULL';
    if (column.default !== undefined) {
      if (typeof column.default === 'string' && column.default !== 'CURRENT_TIMESTAMP') {
        sql += ` DEFAULT '${column.default}'`;
      } else {
        sql += ` DEFAULT ${column.default}`;
      }
    }
    
    return sql;
  }
  
  static generateCreateTableSQL(schema) {
    const { tableName, columns, indexes = [] } = schema;
    
    let sql = `CREATE TABLE IF NOT EXISTS ${tableName} (\n`;
    
    // Add columns
    const columnDefinitions = Object.entries(columns).map(([name, column]) => {
      return `  ${name} ${this.getColumnDefinition(column)}`;
    });
    
    sql += columnDefinitions.join(',\n');
    sql += '\n);\n\n';
    
    // Add indexes
    indexes.forEach(index => {
      const indexName = `idx_${tableName}_${index.columns.join('_')}`;
      const uniqueClause = index.unique ? 'UNIQUE ' : '';
      sql += `CREATE ${uniqueClause}INDEX IF NOT EXISTS ${indexName} ON ${tableName} (${index.columns.join(', ')});\n`;
    });
    
    return sql + '\n';
  }
  
  static generateForeignKeySQL(schema) {
    const { tableName, columns } = schema;
    let sql = '';
    
    Object.entries(columns).forEach(([columnName, column]) => {
      if (column.foreignKey) {
        const [refTable, refColumn] = column.foreignKey.split('.');
        sql += `ALTER TABLE ${tableName} ADD FOREIGN KEY (${columnName}) REFERENCES ${refTable}(${refColumn});\n`;
      }
    });
    
    return sql;
  }
}

// Database initialization class
export class DatabaseInitializer {
  constructor(database) {
    this.db = database;
  }
  
  // Initialize all tables in correct order (dependencies first)
  async initializeDatabase() {
    console.log('🚀 Initializing database schema...');
    
    try {
      // Create tables in dependency order
      await this.createCoreTables();
      await this.createWorkshopTables();
      await this.createLeadTables();
      await this.createCampaignTables();
      await this.createKapiTables();
      await this.createCertificateTables();
      await this.createEventTables();
      
      // Add foreign key constraints (if using a database that supports them)
      await this.addForeignKeyConstraints();
      
      // Insert initial data
      await this.insertInitialData();
      
      console.log('✅ Database initialization completed successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      return false;
    }
  }
  
  async createCoreTables() {
    console.log('📦 Creating core tables...');
    
    // Users table (foundation for all other tables)
    await this.db.exec(SQLGenerator.generateCreateTableSQL(UserSchema));
    
    // Payment and referral tables
    await this.db.exec(SQLGenerator.generateCreateTableSQL(PaymentHistorySchema));
    await this.db.exec(SQLGenerator.generateCreateTableSQL(ReferralSchema));
  }
  
  async createWorkshopTables() {
    console.log('🎓 Creating workshop tables...');
    
    await this.db.exec(SQLGenerator.generateCreateTableSQL(WorkshopSchema));
    await this.db.exec(SQLGenerator.generateCreateTableSQL(WorkshopParticipantSchema));
    await this.db.exec(SQLGenerator.generateCreateTableSQL(WorkshopPaymentHistorySchema));
  }
  
  async createLeadTables() {
    console.log('🎯 Creating lead management tables...');
    
    await this.db.exec(SQLGenerator.generateCreateTableSQL(LeadSchema));
    await this.db.exec(SQLGenerator.generateCreateTableSQL(LeadActivitySchema));
  }
  
  async createCampaignTables() {
    console.log('📊 Creating campaign tables...');
    
    await this.db.exec(SQLGenerator.generateCreateTableSQL(CampaignSchema));
    await this.db.exec(SQLGenerator.generateCreateTableSQL(CampaignDailyStatsSchema));
  }
  
  async createKapiTables() {
    console.log('🏢 Creating Kapi enterprise tables...');
    
    await this.db.exec(SQLGenerator.generateCreateTableSQL(KapiLeadSchema));
    await this.db.exec(SQLGenerator.generateCreateTableSQL(KapiActivitySchema));
  }
  
  async createCertificateTables() {
    console.log('🏆 Creating certificate tables...');
    
    await this.db.exec(SQLGenerator.generateCreateTableSQL(CertificateSchema));
    await this.db.exec(SQLGenerator.generateCreateTableSQL(CertificateTemplateSchema));
  }
  
  async createEventTables() {
    console.log('📅 Creating event tables...');
    
    await this.db.exec(SQLGenerator.generateCreateTableSQL(EventSchema));
    await this.db.exec(SQLGenerator.generateCreateTableSQL(EventRegistrationSchema));
  }
  
  async addForeignKeyConstraints() {
    console.log('🔗 Adding foreign key constraints...');
    
    const schemas = [
      PaymentHistorySchema,
      ReferralSchema,
      WorkshopParticipantSchema,
      WorkshopPaymentHistorySchema,
      LeadSchema,
      LeadActivitySchema,
      CampaignDailyStatsSchema,
      KapiLeadSchema,
      KapiActivitySchema,
      CertificateSchema,
      EventRegistrationSchema
    ];
    
    for (const schema of schemas) {
      const foreignKeySQL = SQLGenerator.generateForeignKeySQL(schema);
      if (foreignKeySQL) {
        try {
          await this.db.exec(foreignKeySQL);
        } catch (error) {
          // Some databases (like SQLite) don't support adding FK constraints after table creation
          console.warn(`⚠️ Could not add foreign keys for ${schema.tableName}:`, error.message);
        }
      }
    }
  }
  
  async insertInitialData() {
    console.log('🌱 Inserting initial data...');
    
    // Insert default certificate templates
    await this.insertCertificateTemplates();
    
    // Insert default admin user (if needed)
    await this.insertDefaultUsers();
    
    // Insert sample campaign data (for development)
    if (process.env.NODE_ENV === 'development') {
      await this.insertSampleData();
    }
  }
  
  async insertCertificateTemplates() {
    const templates = [
      {
        name: 'AI Essentials Certificate',
        certificate_type: 'ai_essentials',
        template_path: '/templates/ai-essentials.html',
        background_image: '/images/cert-bg-essentials.jpg'
      },
      {
        name: 'AI Practitioner Certificate',
        certificate_type: 'ai_practitioner',
        template_path: '/templates/ai-practitioner.html',
        background_image: '/images/cert-bg-practitioner.jpg'
      },
      {
        name: 'Advanced AI Certificate',
        certificate_type: 'advanced_ai',
        template_path: '/templates/advanced-ai.html',
        background_image: '/images/cert-bg-advanced.jpg'
      }
    ];
    
    for (const template of templates) {
      await this.db.run(`
        INSERT OR IGNORE INTO certificate_templates 
        (name, certificate_type, template_path, background_image)
        VALUES (?, ?, ?, ?)
      `, [template.name, template.certificate_type, template.template_path, template.background_image]);
    }
  }
  
  async insertDefaultUsers() {
    // Insert admin user if not exists
    await this.db.run(`
      INSERT OR IGNORE INTO users 
      (username, email, first_name, last_name, role, is_active)
      VALUES (?, ?, ?, ?, ?, ?)
    `, ['admin', '<EMAIL>', 'Admin', 'User', 'admin', true]);
    
    // Insert sample sales reps
    const salesReps = [
      { username: 'sanyu', email: '<EMAIL>', first_name: 'Sanyu', last_name: 'Sales' },
      { username: 'manish', email: '<EMAIL>', first_name: 'Manish', last_name: 'Kumar' },
      { username: 'shivam', email: '<EMAIL>', first_name: 'Shivam', last_name: 'Singh' }
    ];
    
    for (const rep of salesReps) {
      await this.db.run(`
        INSERT OR IGNORE INTO users 
        (username, email, first_name, last_name, role, is_active)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [rep.username, rep.email, rep.first_name, rep.last_name, 'sales_rep', true]);
    }
  }
  
  async insertSampleData() {
    console.log('🧪 Inserting sample data for development...');
    
    // Insert sample campaigns
    await this.db.run(`
      INSERT OR IGNORE INTO campaigns 
      (name, facebook_campaign_id, facebook_account_id, objective, status, total_spend, leads_generated, cpl, conversion_rate, coca)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'Senior_Dev_LLM_Integration', 
      'FB123456789', 
      'act_123456789', 
      'lead_generation', 
      'active', 
      25000, 
      125, 
      200, 
      8.5, 
      2950
    ]);
  }
  
  // Utility method to check if database is initialized
  async isDatabaseInitialized() {
    try {
      const result = await this.db.get(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='users'
      `);
      return !!result;
    } catch (error) {
      return false;
    }
  }
  
  // Drop all tables (use with caution!)
  async dropAllTables() {
    console.log('🗑️ Dropping all tables...');
    
    const tables = [
      'event_registrations',
      'events', 
      'certificate_templates',
      'certificates',
      'kapi_activities',
      'kapi_leads',
      'campaign_daily_stats',
      'campaigns',
      'lead_activities',
      'leads',
      'workshop_payment_history',
      'workshop_participants',
      'workshops',
      'referrals',
      'payment_history',
      'users'
    ];
    
    for (const table of tables) {
      await this.db.run(`DROP TABLE IF EXISTS ${table}`);
    }
    
    console.log('✅ All tables dropped');
  }
}

// Export SQL generation utilities for custom use
export { SQLGenerator };

// Default export for easy initialization
export default DatabaseInitializer;