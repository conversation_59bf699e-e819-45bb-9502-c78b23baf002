// Export all models for easy importing

// Core user and authentication
export { default as User, UserRoles, SubscriptionTier } from './User.js';

// Workshop and event management
export { default as Workshop, EventType, WorkshopStatus } from './Workshop.js';
export { default as Event, EventPriority, RegistrationStatus } from './Event.js';

// Lead management and sales pipeline
export { default as Lead, LeadSource, LeadStatus, LeadSegment } from './Lead.js';
export { default as Campaign, CampaignStatus, CampaignObjective } from './Campaign.js';

// Enterprise and high-value deals
export { default as KapiLead, KapiStage, KapiProduct, KapiLeadSource } from './KapiLead.js';

// Certificate management
export { default as Certificate, CertificateType, CertificateStatus } from './Certificate.js';

// Model schemas for database setup
export { 
  UserSchema, 
  PaymentHistorySchema, 
  ReferralSchema 
} from './User.js';

export { 
  WorkshopSchema, 
  WorkshopParticipantSchema, 
  WorkshopPaymentHistorySchema 
} from './Workshop.js';

export { 
  LeadSchema, 
  LeadActivitySchema 
} from './Lead.js';

export { 
  CampaignSchema, 
  CampaignDailyStatsSchema 
} from './Campaign.js';

export { 
  KapiLeadSchema, 
  KapiActivitySchema 
} from './KapiLead.js';

export { 
  CertificateSchema, 
  CertificateTemplateSchema 
} from './Certificate.js';

export { 
  EventSchema, 
  EventRegistrationSchema 
} from './Event.js';